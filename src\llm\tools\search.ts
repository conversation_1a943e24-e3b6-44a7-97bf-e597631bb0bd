import { useUiStore } from 'src/stores/ui';
import {
  DEFAULT_TAVILY_SETTINGS,
  blacklistKeywords,
  exclude_domains,
} from 'src/config/defaultSettings';
import { $t } from 'src/composables/useTrans';

interface TavilySearchOptions {
  query: string;
  search_depth?: 'basic' | 'advanced';
  include_answer?: boolean;
  include_raw_content?: boolean;
  max_results?: number;
  include_domains?: string[];
  exclude_domains?: string[];
  include_images?: boolean;
  include_image_descriptions?: boolean;
}

interface TavilySearchResult {
  title: string;
  url: string;
  content: string;
  raw_content?: string;
  score: number;
  published_date?: string;
}

interface TavilyResponse {
  answer?: string;
  query: string;
  response_time: number;
  images?: string[];
  results: TavilySearchResult[];
  follow_up_questions?: string[];
}

/**
 * 获取Tavily搜索配置
 */
async function getTavilySettings() {
  const uiStore = useUiStore();
  await uiStore.loadSettings();

  const defaultProvider = uiStore.defaultSearchEngineProvider;
  if (!defaultProvider || defaultProvider.key !== 'tavily') {
    throw new Error($t('src.llm.tools.search.noTavilyResourceProvider'));
  }

  const config = defaultProvider.config as {
    apiKey: string;
    baseUrl: string;
    searchDepth: 'basic' | 'advanced';
    includeAnswer: boolean;
    includeRawContent: boolean;
    maxResults: number;
    includeImages: boolean;
    includeImageDescriptions: boolean;
  };
  const defaultSettings = DEFAULT_TAVILY_SETTINGS;

  return {
    apiKey: config.apiKey || defaultSettings.apiKey,
    baseUrl: config.baseUrl || defaultSettings.baseUrl,
    searchDepth: config.searchDepth || defaultSettings.searchDepth,
    includeAnswer: config.includeAnswer ?? defaultSettings.includeAnswer,
    includeRawContent: config.includeRawContent ?? defaultSettings.includeRawContent,
    maxResults: config.maxResults || defaultSettings.maxResults,
    includeImages: config.includeImages ?? defaultSettings.includeImages,
    includeImageDescriptions:
      config.includeImageDescriptions ?? defaultSettings.includeImageDescriptions,
  };
}

/**
 * 执行Tavily搜索
 */
async function searchWeb(options: TavilySearchOptions): Promise<{
  success: boolean;
  message: string;
  tool_name: string;
  results?: TavilySearchResult[];
  answer?: string;
  images?: string[];
  follow_up_questions?: string[];
  query?: string;
  response_time?: number;
}> {
  try {
    console.log('🔍 [Tavily Search] 开始搜索:', options.query);

    const settings = await getTavilySettings();

    if (!settings.apiKey) {
      return {
        success: false,
        message: $t('src.llm.tools.search.tavilyApiKeyNotConfigured'),
        tool_name: 'search_web',
      };
    }

    const searchOptions: TavilySearchOptions = {
      query: options.query,
      search_depth: options.search_depth || settings.searchDepth,
      include_answer:
        options.include_answer !== undefined ? options.include_answer : settings.includeAnswer,
      include_raw_content:
        options.include_raw_content !== undefined
          ? options.include_raw_content
          : settings.includeRawContent,
      max_results: options.max_results || settings.maxResults,
      include_images:
        options.include_images !== undefined ? options.include_images : settings.includeImages,
      include_image_descriptions:
        options.include_image_descriptions !== undefined
          ? options.include_image_descriptions
          : settings.includeImageDescriptions,
    };

    // 如果用户指定了域名过滤，使用用户指定的
    if (options.include_domains) {
      searchOptions.include_domains = options.include_domains;
    }
    if (options.exclude_domains) {
      searchOptions.exclude_domains = [...options.exclude_domains, ...exclude_domains];
    } else {
      searchOptions.exclude_domains = exclude_domains;
    }

    console.log('🔍 [Tavily Search] 搜索参数:', searchOptions);

    const response = await fetch(settings.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${settings.apiKey}`,
      },
      body: JSON.stringify(searchOptions),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [Tavily Search] API请求失败:', response.status, errorText);
      return {
        success: false,
        message: $t('src.llm.tools.search.searchRequestFailed', {
          status: response.status,
          statusText: response.statusText,
        }),
        tool_name: 'search_web',
      };
    }

    const data: TavilyResponse = await response.json();
    console.log('✅ [Tavily Search] 搜索成功，结果数:', data.results?.length || 0);

    // 格式化结果
    // 关键字黑名单，过阿里云敏感词
    const formattedResults =
      data.results
        ?.filter(
          (result) =>
            result.title && blacklistKeywords.some((keyword) => !result.title.includes(keyword)),
        )
        ?.map((result) => ({
          title: result.title,
          url: result.url,
          content: result.content,
          raw_content: result.raw_content,
          score: result.score,
          published_date: result.published_date,
        })) || [];

    let message = $t('src.llm.tools.search.searchCompleted', {
      count: formattedResults.length,
    });
    if (data.answer) {
      message += $t('src.llm.tools.search.directAnswer', { answer: data.answer });
    }

    return {
      success: true,
      message,
      results: formattedResults,
      answer: data.answer,
      images: data.images,
      follow_up_questions: data.follow_up_questions,
      query: data.query,
      response_time: data.response_time,
      tool_name: 'search_web',
    };
  } catch (error) {
    console.error('❌ [Tavily Search] 搜索失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.search.searchFailed', {
        error: error instanceof Error ? error.message : String(error),
      }),
      tool_name: 'search_web',
    };
  }
}

// 导出工具函数映射
export const searchTools = {
  search_web: searchWeb,
};

// 导出工具映射表
export const searchToolMappings = {
  search_web: searchWeb,
} as const;

// 导出工具schema
export const tools = [
  {
    type: 'function' as const,
    function: {
      name: 'search_web',
      description:
        '使用Tavily搜索引擎在互联网上搜索信息。支持基础和高级搜索模式，可以获取网页内容、答案摘要、图片等。',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: '搜索查询字符串',
          },
          search_depth: {
            type: 'string',
            enum: ['basic', 'advanced'],
            description: '搜索深度，basic为基础搜索，advanced为高级搜索（更深入但较慢）',
          },
          include_answer: {
            type: 'boolean',
            description: '是否包含AI生成的答案摘要',
          },
          include_raw_content: {
            type: 'boolean',
            description: '是否包含网页的原始内容',
          },
          max_results: {
            type: 'number',
            description: '最大结果数量，1-20之间',
            minimum: 1,
            maximum: 20,
          },
          include_domains: {
            type: 'array',
            items: {
              type: 'string',
            },
            description: '只搜索指定域名的结果',
          },
          exclude_domains: {
            type: 'array',
            items: {
              type: 'string',
            },
            description: '排除指定域名的结果',
          },
          include_images: {
            type: 'boolean',
            description: '是否包含相关图片',
          },
          include_image_descriptions: {
            type: 'boolean',
            description: '是否包含图片描述',
          },
        },
        required: ['query'],
      },
    },
  },
];
