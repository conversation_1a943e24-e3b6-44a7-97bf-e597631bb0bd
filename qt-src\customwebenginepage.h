#ifndef CUSTOMWEBENGINEPAGE_H
#define CUSTOMWEBENGINEPAGE_H

#include <QWebEnginePage>
#include <QUrl>

class CustomWebEnginePage : public QWebEnginePage
{
    Q_OBJECT

public:
    explicit CustomWebEnginePage(QObject *parent = nullptr);

protected:
    bool acceptNavigationRequest(const QUrl &url, NavigationType type, bool isMainFrame) override;

private:
    bool isExternalUrl(const QUrl &url) const;
};

#endif // CUSTOMWEBENGINEPAGE_H
