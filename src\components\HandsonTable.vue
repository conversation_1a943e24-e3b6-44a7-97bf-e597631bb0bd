<template>
  <div class="hot-demo-container">
    <HotTable :data="tableData" :settings="hotSettings" @afterChange="handleChange" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { HotTable } from '@handsontable/vue3';
import { registerAllModules } from 'handsontable/registry';
import 'handsontable/dist/handsontable.full.min.css';

registerAllModules();

const tableData = ref([
  ['A1', 'B1', 'C1'],
  ['A2', 'B2', 'C2'],
  ['A3', 'B3', 'C3'],
]);

const hotSettings = ref({
  licenseKey: 'non-commercial-and-evaluation',
  height: 'auto',
  width: '100%',
  colHeaders: true,
  rowHeaders: true,
  contextMenu: true,
  manualColumnResize: true,
  manualRowResize: true,
  mergeCells: true,
  dropdownMenu: true,
  filters: true,
  comments: true,
  columnSorting: true,
  autoColumnSize: true,
  autoRowSize: true,
  stretchH: 'all',
});

const handleChange = () => {
  // 这里只做演示，不处理数据同步
};
</script>

<style scoped>
.hot-demo-container {
  width: 100%;
  max-width: 100%;
  margin: 32px auto;
  box-sizing: border-box;
  overflow: hidden;
}

.hot-demo-container :deep(.handsontable) {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box;
}

.hot-demo-container :deep(.ht_master),
.hot-demo-container :deep(.wtHolder),
.hot-demo-container :deep(.htCore),
.hot-demo-container :deep(table) {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box;
}
</style>
