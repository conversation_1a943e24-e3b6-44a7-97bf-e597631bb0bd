/**
 * 消息预处理服务
 * 统一处理提示词构建、工具配置、上下文处理等逻辑
 */

import type { Message, Conversation } from 'src/types/qwen';
import type { Document } from 'src/types/doc';
import type { KnowledgeBase } from 'src/env';
import type { ConversationRole, PromptContext } from 'src/services/promptService';
import { promptService } from 'src/services/promptService';
import { knowledgeService } from 'src/services/knowledgeService';
import { tools } from 'src/llm/tools/index';
import { getDocumentInfo } from 'src/llm/tools/editor';

/**
 * LLM调用参数接口 - 使用对象参数规范接口
 */
export interface LlmCallParams {
  // 基础参数
  messages: Message[];
  conversation: Conversation;

  // 模式和角色
  conversationMode: 'agent' | 'chat';
  conversationRole: ConversationRole;

  // 工具和文档
  enabledTools: string[];
  relatedDocuments: Document[];
  currentDocument: Document | null;

  // 控制参数
  abortController: AbortController | null;

  // 大模型供应商
  provider?: string;
}

/**
 * 简化的LLM供应商调用参数 - 预处理后的数据
 */
export interface SimplifiedLlmParams {
  // 预处理后的消息（包含系统提示词和用户输入）
  messages: Message[];

  // LLM Store 中的响应式消息数组引用（用于流式更新）
  messagesRef: { value: Message[] };

  // 对话信息
  conversation: Conversation;

  // 工具配置
  tools: Array<{
    type: string;
    function: {
      name: string;
      description: string;
      parameters: Record<string, unknown>;
    };
  }>;
  enableTools: boolean;

  // 控制参数
  abortController: AbortController | null;

  // 工具调用回调
  onToolCall?: (toolCall: {
    id: string;
    type: string;
    function: { name: string; arguments: string };
  }) => void;

  // 中断信号
  signal?: AbortSignal;
}

/**
 * 预处理结果接口
 */
export interface PreprocessedData {
  // 处理后的消息列表（包含系统提示词）
  processedMessages: Message[];

  // 工具配置
  toolsConfig: {
    enabled: boolean;
    tools: Array<{
      type: string;
      function: {
        name: string;
        description: string;
        parameters: Record<string, unknown>;
      };
    }>;
  };

  // 上下文信息
  contextInfo: {
    hasDocument: boolean;
    knowledgeContext?: PromptContext['knowledgeContext'];
    attachmentsDescription?: string;
    selectedTextDescription?: string;
  };
}

/**
 * 消息预处理服务类
 */
export class MessagePreprocessorService {
  /**
   * 预处理消息和上下文
   */
  async preprocessMessage(params: LlmCallParams): Promise<PreprocessedData> {
    console.log('[MessagePreprocessor] 开始预处理消息');

    const {
      messages,
      conversation,
      conversationMode,
      conversationRole,
      enabledTools,
      relatedDocuments,
      currentDocument,
      provider,
    } = params;

    // 1. 检查是否有活动文档
    const hasActiveDocument = !!currentDocument;
    let currentDocumentInfo = null;

    if (hasActiveDocument && currentDocument) {
      try {
        const docInfoResult = getDocumentInfo({ documentId: currentDocument.id });
        if (docInfoResult.success && docInfoResult.documentInfo) {
          currentDocumentInfo = docInfoResult.documentInfo;
          console.log('📄 [MessagePreprocessor] 获取当前文档信息成功');
        } else {
          console.warn('⚠️ [MessagePreprocessor] 获取当前文档信息失败:', docInfoResult.message);
        }
      } catch (error) {
        console.warn('⚠️ [MessagePreprocessor] 获取当前文档信息失败:', error);
      }
    }

    // 2. 获取当前选择的知识库（用于工具提示）
    const selectedKnowledgeBase = knowledgeService.getSelectedKnowledgeBase();
    console.log('🔍 [MessagePreprocessor] 获取到的知识库:', selectedKnowledgeBase?.name || '无');

    // 3. 构建Prompt上下文
    const promptContext: PromptContext = {
      role: conversationRole,
      hasDocument: hasActiveDocument,
      enableTools: enabledTools.length > 0,
      conversationMode,
      conversationPrompt: conversation.prompt || '',
      provider,
      relatedDocuments,
      customInstructions: undefined,
      knowledgeContext: selectedKnowledgeBase
        ? {
            knowledgeBase: selectedKnowledgeBase.name,
            searchQuery: '',
            results: [],
          }
        : undefined,
      currentDocument: currentDocumentInfo,
    };

    // 4. 添加附加内容上下文和提示词
    let attachmentsDescription: string | undefined;
    let selectedTextDescription: string | undefined;
    let customInstructions: string | undefined;

    try {
      const { llmStore } = await import('src/composeables/useStore');

      attachmentsDescription = llmStore.getAttachmentsPromptDescription();
      if (attachmentsDescription) {
        promptContext.attachmentsDescription = attachmentsDescription;
        console.log(
          '📎 [MessagePreprocessor] 添加附加内容到Prompt，内容长度:',
          attachmentsDescription.length,
        );
      }

      selectedTextDescription = llmStore.getSelectedTextPromptDescription();
      if (selectedTextDescription) {
        promptContext.selectedTextDescription = selectedTextDescription;
        console.log(
          '📝 [MessagePreprocessor] 添加选中文本到Prompt，内容长度:',
          selectedTextDescription.length,
        );
      }

      // 获取当前选中的提示词内容
      const { useUiStore } = await import('src/stores/ui');
      const uiStore = useUiStore();
      
      if (uiStore.perferences?.prompt?.chat?.selected) {
        const selectedPromptName = uiStore.perferences.prompt.chat.selected;
        const chatPrompts = uiStore.perferences.prompt.chat.list || [];
        const selectedPrompt = chatPrompts.find(p => p.name === selectedPromptName);
        
        if (selectedPrompt) {
          if (typeof selectedPrompt.prompt === 'string') {
            customInstructions = selectedPrompt.prompt;
          } else if (selectedPrompt.prompt?.responsibilities && selectedPrompt.prompt?.emphasize) {
            // 处理聊天模式的特殊格式
            const responsibilities = selectedPrompt.prompt.responsibilities.filter(r => r.trim()).join('\n');
            const emphasize = selectedPrompt.prompt.emphasize;
            customInstructions = `${responsibilities}\n\n${emphasize}`.trim();
          }
          promptContext.customInstructions = customInstructions;
        }
      }
    } catch (error) {
      console.warn('⚠️ [MessagePreprocessor] 获取附加内容描述失败:', error);
    }

    // 5. 生成系统Prompt
    const systemPrompt = await promptService.buildSystemPrompt(promptContext);

    // 6. 构建处理后的消息列表
    const nonSystemMessages = messages.filter((msg) => msg.role !== 'system');
    const processedMessages: Message[] = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      ...nonSystemMessages,
    ];

    // 7. 配置工具
    const toolsConfig = this.buildToolsConfig(enabledTools, true, selectedKnowledgeBase);

    // 9. 构建上下文信息
    const contextInfo = {
      hasDocument: hasActiveDocument,
      knowledgeContext: promptContext.knowledgeContext,
      attachmentsDescription,
      selectedTextDescription,
    };

    console.log('[MessagePreprocessor] 预处理完成');
    console.log('[MessagePreprocessor] 系统提示词长度:', systemPrompt.length);
    console.log('[MessagePreprocessor] 处理后消息数量:', processedMessages.length);
    console.log('[MessagePreprocessor] 启用工具数量:', toolsConfig.tools.length);

    return {
      processedMessages,
      toolsConfig,
      contextInfo,
    };
  }

  /**
   * 生成简化的LLM调用参数
   */
  async getSimplifiedParams(
    params: LlmCallParams,
    messagesRef: { value: Message[] },
    modelSupportsTools: boolean = true,
  ): Promise<SimplifiedLlmParams> {
    // 临时修改 preprocessMessage 调用，传递工具调用能力
    const preprocessedData = await this.preprocessMessageWithToolSupport(
      params,
      modelSupportsTools,
    );

    return {
      messages: preprocessedData.processedMessages,
      messagesRef: messagesRef,
      conversation: params.conversation,
      tools: preprocessedData.toolsConfig.tools,
      enableTools: preprocessedData.toolsConfig.enabled,
      abortController: params.abortController,
    };
  }

  /**
   * 带工具支持判断的消息预处理
   */
  private async preprocessMessageWithToolSupport(
    params: LlmCallParams,
    modelSupportsTools: boolean,
  ) {
    const { messages, conversationMode, enabledTools, relatedDocuments, currentDocument } = params;

    console.log('[MessagePreprocessor] 开始预处理消息');
    console.log('[MessagePreprocessor] 输入消息数量:', messages.length);
    console.log('[MessagePreprocessor] 启用工具:', enabledTools);
    console.log('[MessagePreprocessor] 模型支持工具调用:', modelSupportsTools);

    // 1. 获取当前文档信息
    let currentDocumentInfo = null;
    const hasActiveDocument = !!currentDocument;
    if (hasActiveDocument && currentDocument) {
      try {
        const docInfoResult = getDocumentInfo({ documentId: currentDocument.id });
        if (docInfoResult.success && docInfoResult.documentInfo) {
          currentDocumentInfo = docInfoResult.documentInfo;
          console.log('📄 [MessagePreprocessor] 获取当前文档信息成功');
        } else {
          console.warn('⚠️ [MessagePreprocessor] 获取当前文档信息失败:', docInfoResult.message);
        }
      } catch (error) {
        console.warn('⚠️ [MessagePreprocessor] 获取当前文档信息失败:', error);
      }
    }

    // 2. 获取当前选择的知识库（用于工具提示）
    const selectedKnowledgeBase = knowledgeService.getSelectedKnowledgeBase();
    console.log(
      '🔍 [MessagePreprocessor] 获取到的知识库 (WithToolSupport):',
      selectedKnowledgeBase?.name || '无',
    );

    // 3. 构建提示词上下文
    const { useUiStore } = await import('src/stores/ui');
    const uiStore = useUiStore();
    
    // 获取当前选中的提示词内容
    let customInstructions = '';
    if (uiStore.perferences?.prompt?.chat?.selected) {
      const selectedPromptName = uiStore.perferences.prompt.chat.selected;
      const chatPrompts = uiStore.perferences.prompt.chat.list || [];
      const selectedPrompt = chatPrompts.find(p => p.name === selectedPromptName);
      
      if (selectedPrompt) {
        if (typeof selectedPrompt.prompt === 'string') {
          customInstructions = selectedPrompt.prompt;
        } else if (selectedPrompt.prompt?.responsibilities && selectedPrompt.prompt?.emphasize) {
          // 处理聊天模式的特殊格式
          const responsibilities = selectedPrompt.prompt.responsibilities.filter(r => r.trim()).join('\n');
          const emphasize = selectedPrompt.prompt.emphasize;
          customInstructions = `${responsibilities}\n\n${emphasize}`.trim();
        }
      }
    }

    const promptContext: PromptContext = {
      role: 'inkcop', // 默认角色
      hasDocument: hasActiveDocument,
      conversationMode,
      enableTools: modelSupportsTools && enabledTools.length > 0, // 如果模型不支持工具，设为false
      relatedDocuments,
      currentDocument: currentDocumentInfo,
      customInstructions: customInstructions || undefined,
      knowledgeContext: selectedKnowledgeBase
        ? {
            knowledgeBase: selectedKnowledgeBase.name,
            searchQuery: '',
            results: [],
          }
        : undefined,
    };

    // 4. 处理附件描述（暂时为空）
    const attachmentsDescription = '';
    const selectedTextDescription = '';

    // 5. 生成系统Prompt
    const systemPrompt = await promptService.buildSystemPrompt(promptContext);

    // 6. 构建处理后的消息列表
    const nonSystemMessages = messages.filter((msg) => msg.role !== 'system');
    const processedMessages: Message[] = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      ...nonSystemMessages,
    ];

    // 7. 配置工具
    const toolsConfig = this.buildToolsConfig(
      enabledTools,
      modelSupportsTools,
      selectedKnowledgeBase,
    );

    // 8. 构建上下文信息
    const contextInfo = {
      hasDocument: hasActiveDocument,
      knowledgeContext: promptContext.knowledgeContext,
      attachmentsDescription,
      selectedTextDescription,
    };

    console.log('[MessagePreprocessor] 预处理完成');
    console.log('[MessagePreprocessor] 系统提示词长度:', systemPrompt.length);
    console.log('[MessagePreprocessor] 处理后消息数量:', processedMessages.length);
    console.log('[MessagePreprocessor] 启用工具数量:', toolsConfig.tools.length);

    return {
      processedMessages,
      toolsConfig,
      contextInfo,
    };
  }

  /**
   * 构建工具配置
   */
  private buildToolsConfig(
    enabledTools: string[],
    modelSupportsTools: boolean = true,
    selectedKnowledgeBase?: KnowledgeBase | null,
  ) {
    console.log('🔧 [MessagePreprocessor] buildToolsConfig 参数:');
    console.log('  - enabledTools:', enabledTools);
    console.log('  - modelSupportsTools:', modelSupportsTools);
    console.log('  - selectedKnowledgeBase:', selectedKnowledgeBase?.name || '无');

    // 如果模型不支持工具调用，直接返回空配置
    if (!modelSupportsTools || enabledTools.length === 0) {
      console.log(
        `🔧 [MessagePreprocessor] 工具调用被禁用 - 模型支持: ${modelSupportsTools}, 启用工具数: ${enabledTools.length}`,
      );
      return {
        enabled: false,
        tools: [],
      };
    }

    // 创建启用工具列表的副本，以便动态添加知识库工具
    const finalEnabledTools = [...enabledTools];

    // 如果用户选择了知识库，添加知识库工具
    if (selectedKnowledgeBase) {
      if (!finalEnabledTools.includes('search_knowledge')) {
        finalEnabledTools.push('search_knowledge');
        console.log('🔧 [MessagePreprocessor] 检测到已选择知识库，添加 search_knowledge 工具');
      } else {
        console.log('🔧 [MessagePreprocessor] search_knowledge 工具已存在于启用列表中');
      }
    } else {
      console.log('🔧 [MessagePreprocessor] 未选择知识库，不添加 search_knowledge 工具');
    }

    // 从所有工具中筛选出启用的工具
    console.log(
      '🔧 [MessagePreprocessor] 所有可用工具:',
      tools.map((t) => t.function.name),
    );
    console.log('🔧 [MessagePreprocessor] 最终启用工具列表:', finalEnabledTools);

    const filteredTools = tools.filter((tool) => finalEnabledTools.includes(tool.function.name));

    console.log(
      `🔧 [MessagePreprocessor] 可用工具总数: ${tools.length}, 启用工具数: ${filteredTools.length}`,
    );
    console.log(
      `🔧 [MessagePreprocessor] 启用的工具: ${filteredTools.map((t) => t.function.name).join(', ')}`,
    );

    return {
      enabled: true,
      tools: filteredTools,
    };
  }
}

// 导出单例实例
export const messagePreprocessor = new MessagePreprocessorService();
