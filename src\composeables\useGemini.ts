import { computed, type Ref } from 'vue';
import type {
  GeminiContent,
  GeminiGenerateContentRequest,
  GeminiTool,
  GeminiFunctionDeclaration,
  GeminiParameter,
} from 'src/types/gemini';
import type {
  Message,
  MessageWithToolCall,
  Tool,
  ToolParameter,
  FunctionTool,
  ToolCall,
  AssistantMessage,
} from 'src/types/qwen';
import { useUiStore } from 'src/stores/ui';
import { useSqlite } from 'src/composeables/useSqlite';
import { DEFAULT_GEMINI_SETTINGS } from 'src/config/defaultSettings';
import { executeToolCall } from 'src/llm/tools/index';
import { PromptService } from 'src/services/promptService';
import type { ConversationRole } from 'src/services/promptService';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import { $t } from 'src/composables/useTrans';

/**
 * Google Gemini API Hook
 * 遵循统一的 LLM Hook 规范
 */
export const useGemini = () => {
  // 在函数内部初始化 composables 和 stores
  const uiStore = useUiStore();
  const { updateConversation } = useSqlite();

  // 获取当前设置
  const geminiSettings = computed(() => {
    return uiStore.perferences?.llm?.gemini || DEFAULT_GEMINI_SETTINGS;
  });

  // 辅助函数：转换 Tool 类型到 GeminiParameter
  function convertToolParameterToGemini(param: ToolParameter): GeminiParameter {
    const geminiParam: GeminiParameter = {
      type: param.type.toUpperCase() as GeminiParameter['type'],
      description: param.description,
    };

    if (param.enum) {
      geminiParam.enum = param.enum;
    }

    if (param.type === 'array' && param.items) {
      geminiParam.items = convertToolParameterToGemini(param.items);
    }

    if (param.type === 'object' && param.properties) {
      geminiParam.properties = {};
      for (const [key, value] of Object.entries(param.properties)) {
        geminiParam.properties[key] = convertToolParameterToGemini(value);
      }
    }

    return geminiParam;
  }

  // 辅助函数：转换 Tool 到 GeminiTool
  function convertToolToGemini(tool: Tool): GeminiTool | null {
    if (tool.type !== 'function') {
      return null;
    }

    const functionTool = tool as FunctionTool;
    const functionDeclaration: GeminiFunctionDeclaration = {
      name: functionTool.function.name,
      description: functionTool.function.description || '',
    };

    if (functionTool.function.parameters && functionTool.function.parameters.properties) {
      functionDeclaration.parameters = {
        type: 'OBJECT',
        properties: {},
        required: functionTool.function.parameters.required || [],
      };

      for (const [key, value] of Object.entries(functionTool.function.parameters.properties)) {
        functionDeclaration.parameters.properties[key] = convertToolParameterToGemini(value);
      }
    }

    return {
      functionDeclarations: [functionDeclaration],
    };
  }

  // 辅助函数：转换消息到 Gemini 格式
  function convertMessageToGemini(message: Message): GeminiContent {
    const geminiContent: GeminiContent = {
      role: message.role === 'assistant' ? 'model' : 'user',
      parts: [],
    };

    // 处理文本内容
    if (message.content) {
      if (typeof message.content === 'string') {
        geminiContent.parts.push({ text: message.content });
      } else {
        // Handle multimodal content
        for (const item of message.content) {
          if (item.type === 'text' && item.text) {
            geminiContent.parts.push({ text: item.text });
          }
          // Add handling for other content types if needed
        }
      }
    }

    // 处理工具调用
    if ('tool_calls' in message && (message as MessageWithToolCall).tool_calls) {
      for (const toolCall of (message as MessageWithToolCall).tool_calls) {
        if (toolCall.type === 'function') {
          geminiContent.parts.push({
            functionCall: {
              name: toolCall.function.name,
              args: JSON.parse(toolCall.function.arguments),
            },
          });
        }
      }
    }

    // 处理工具响应
    if (message.role === 'tool') {
      const toolMessage = message as { tool_call_id?: string; name?: string; content: string };
      if (toolMessage.tool_call_id) {
        geminiContent.parts.push({
          functionResponse: {
            name: toolMessage.name || 'unknown',
            response: JSON.parse(toolMessage.content),
          },
        });
      }
    }

    return geminiContent;
  }

  // 统一的消息发送接口
  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const { messagesRef, conversation, tools, enableTools, abortController } = params;
    const settings = geminiSettings.value;

    console.log('[Gemini] 发送消息，对话ID:', conversation.id);

    try {
      // 检查设置
      if (!settings || !settings.apiKey) {
        throw new Error('请先配置 Google Gemini API Key');
      }

      // 设置加载状态
      loading.value = true;

      // 转换消息到 Gemini 格式
      const geminiContents: GeminiContent[] = messagesRef.value.map(convertMessageToGemini);

      // 构建请求体
      const requestBody: GeminiGenerateContentRequest = {
        contents: geminiContents,
        generationConfig: {
          maxOutputTokens: settings.maxTokens,
          topK: settings.topK,
          candidateCount: settings.candidateCount || 1,
          stopSequences: settings.stopSequences,
        },
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (settings.temperature !== undefined && !isNaN(settings.temperature)) {
        requestBody.generationConfig.temperature = settings.temperature;
      }
      if (settings.topP !== undefined && !isNaN(settings.topP)) {
        requestBody.generationConfig.topP = settings.topP;
      }

      // 添加工具（如果启用）
      let toolsToUse: Tool[] | undefined;
      if (enableTools && tools) {
        toolsToUse = tools as Tool[];
        const geminiTools: GeminiTool[] = [];
        for (const tool of toolsToUse) {
          const geminiTool = convertToolToGemini(tool);
          if (geminiTool) {
            geminiTools.push(geminiTool);
          }
        }
        if (geminiTools.length > 0) {
          requestBody.tools = geminiTools;
          requestBody.toolConfig = {
            functionCallingConfig: {
              mode: 'AUTO',
            },
          };
        }
      }

      // 确定请求方法
      const method = settings.stream ? 'streamGenerateContent' : 'generateContent';
      const url = `${settings.baseUrl}/models/${settings.model}:${method}?key=${settings.apiKey}`;

      console.log('[Gemini] 发送请求:', url);
      console.log('[Gemini] 请求体:', JSON.stringify(requestBody, null, 2));

      // 添加助手消息占位符
      const assistantMessage: AssistantMessage = {
        role: 'assistant',
        content: '',
        partial: true,
      };
      messagesRef.value.push(assistantMessage as Message);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Gemini] API 错误响应:', errorText);
        throw new Error(`Google Gemini API 错误: ${response.status} ${response.statusText}`);
      }

      // 处理流式响应
      if (settings.stream) {
        await handleStreamResponse(
          response,
          assistantMessage as Message,
          toolsToUse,
          params,
          loading,
        );
      } else {
        // 处理非流式响应
        await handleNonStreamResponse(
          response,
          assistantMessage as Message,
          toolsToUse,
          params,
          loading,
        );
      }
    } catch (error) {
      console.error('[Gemini] 发送消息失败:', error);

      // 添加错误消息
      messagesRef.value.push({
        role: 'assistant',
        content: $t('src.composeables.useGemini.errorOccurred', {
          error: error instanceof Error ? error.message : '未知错误',
        }),
      } as Message);

      loading.value = false;
      throw error;
    } finally {
      // 无论成功还是失败，都更新对话
      if (conversation.id) {
        await updateConversation(
          conversation.id,
          conversation.title,
          JSON.stringify(messagesRef.value),
          conversation.prompt,
        );
      }
    }
  };

  // 处理流式响应
  async function handleStreamResponse(
    response: Response,
    assistantMessage: Message,
    tools: Tool[] | undefined,
    params: SimplifiedLlmParams,
    loading: Ref<boolean>,
  ) {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('响应体不可读');

    const decoder = new TextDecoder();
    let buffer = '';
    const toolCalls: Record<string, ToolCall> = {};

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          if (!line.startsWith('data: ')) continue;

          const data = line.slice(6);
          if (data === '[DONE]') continue;

          try {
            const json = JSON.parse(data);

            if (json.candidates && json.candidates.length > 0) {
              const candidate = json.candidates[0];
              if (candidate.content && candidate.content.parts) {
                for (const part of candidate.content.parts) {
                  if (part.text) {
                    // 处理文本内容
                    if (typeof assistantMessage.content === 'string') {
                      assistantMessage.content += part.text;
                    } else {
                      assistantMessage.content = part.text;
                    }
                  } else if (part.functionCall && tools) {
                    // 处理工具调用
                    const toolCallId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                    toolCalls[toolCallId] = {
                      id: toolCallId,
                      type: 'function' as const,
                      index: Object.keys(toolCalls).length,
                      function: {
                        name: part.functionCall.name,
                        arguments: JSON.stringify(part.functionCall.args),
                      },
                    };
                  }
                }
              }
            }
          } catch (e) {
            console.error('[Gemini] 解析响应数据失败:', e);
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // 标记消息为完整
    (assistantMessage as AssistantMessage).partial = false;

    // 如果有工具调用，处理它们
    if (Object.keys(toolCalls).length > 0 && tools) {
      await handleToolCalls(toolCalls, params, loading);
    } else {
      loading.value = false;
    }
  }

  // 处理非流式响应
  async function handleNonStreamResponse(
    response: Response,
    assistantMessage: Message,
    tools: Tool[] | undefined,
    params: SimplifiedLlmParams,
    loading: Ref<boolean>,
  ) {
    const responseData = await response.json();
    console.log('[Gemini] API 响应:', responseData);

    if (responseData.candidates && responseData.candidates.length > 0) {
      const candidate = responseData.candidates[0];
      const content = candidate.content;

      if (content && content.parts) {
        let responseText = '';
        const toolCalls: Record<string, ToolCall> = {};

        for (const part of content.parts) {
          if (part.text) {
            responseText += part.text;
          } else if (part.functionCall && tools) {
            const toolCallId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            toolCalls[toolCallId] = {
              id: toolCallId,
              type: 'function' as const,
              index: Object.keys(toolCalls).length,
              function: {
                name: part.functionCall.name,
                arguments: JSON.stringify(part.functionCall.args),
              },
            };
          }
        }

        // 更新助手消息
        assistantMessage.content = responseText;
        (assistantMessage as AssistantMessage).partial = false;

        // 如果有工具调用，处理它们
        if (Object.keys(toolCalls).length > 0 && tools) {
          await handleToolCalls(toolCalls, params, loading);
        } else {
          loading.value = false;
        }
      } else {
        loading.value = false;
      }
    } else {
      loading.value = false;
      throw new Error('Google Gemini API 返回了空响应');
    }
  }

  // 处理工具调用
  async function handleToolCalls(
    toolCalls: Record<string, ToolCall>,
    params: SimplifiedLlmParams,
    loading: Ref<boolean>,
  ) {
    const { messagesRef } = params;

    // 将工具调用添加到最后的助手消息
    const lastMessage = messagesRef.value[messagesRef.value.length - 1];
    (lastMessage as Message & { tool_calls?: ToolCall[] }).tool_calls = Object.values(toolCalls);

    // 执行工具调用
    console.log('[Gemini] 执行工具调用:', toolCalls);

    for (const [toolCallId, toolCall] of Object.entries(toolCalls)) {
      if (toolCall.type === 'function') {
        try {
          const args = JSON.parse(toolCall.function.arguments);
          const result = await executeToolCall(toolCall.function.name, args);

          // 添加工具响应消息
          messagesRef.value.push({
            role: 'tool',
            content: JSON.stringify(result),
            tool_call_id: toolCallId,
            name: toolCall.function.name,
          } as Message);
        } catch (error) {
          console.error('[Gemini] 工具调用失败:', error);
          messagesRef.value.push({
            role: 'tool',
            content: JSON.stringify({
              error:
                error instanceof Error
                  ? error.message
                  : $t('src.composeables.useGemini.unknownError'),
            }),
            tool_call_id: toolCallId,
            name: toolCall.function.name,
          } as Message);
        }
      }
    }

    // 递归调用以获取 AI 对工具结果的响应
    console.log('[Gemini] 获取 AI 对工具结果的响应');
    await sendMessage(params, loading);
  }

  // 获取可用角色
  const getAvailableRoles = () => PromptService.getAvailableRoles();

  // 获取角色建议
  const getRoleSuggestions = (role: ConversationRole) => PromptService.getRoleSuggestions(role);

  return {
    geminiSettings,
    sendMessage,
    getAvailableRoles,
    getRoleSuggestions,
  };
};
