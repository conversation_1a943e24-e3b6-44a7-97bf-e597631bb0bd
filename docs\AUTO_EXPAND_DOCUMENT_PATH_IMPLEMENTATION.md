# 自动展开文档路径功能实现文档

## 功能概述

在切换编辑器标签页（toggleTab）时，自动展开文档所在文件夹及其所有父级文件夹，确保用户能够在文件树中看到当前文档的位置。

## 问题背景

当用户切换标签页时，系统会设置 `uiStore.highlightTreeItem` 来高亮文件树中的对应文档。但是，如果该文档所在的文件夹处于折叠状态，用户仍然无法看到文档在文件树中的位置，影响了用户体验。

## 解决方案

实现自动展开文档路径功能，在切换标签页时：

1. 获取文档所在的文件夹ID
2. 递归获取所有父级文件夹路径
3. 逐级展开所有相关文件夹
4. 确保文档在文件树中可见

## 实现细节

### 1. UI Store 扩展

在 `src/stores/ui.ts` 中添加了文件夹展开事件管理：

#### 状态管理

```typescript
// 文件夹展开路径管理
pendingExpansionPath: [] as number[],
expandFolderPath: 0, // 用于触发响应式更新的时间戳
```

#### 展开文档路径方法

```typescript
// 展开文档所在文件夹及其所有父级文件夹
async expandDocumentPath(documentId: number) {
  try {
    // 动态导入docStore，避免循环依赖
    const { useDocStore } = await import('src/stores/doc');
    const docStore = useDocStore();

    // 从文档文件夹映射中获取文档所在的文件夹ID
    const folderId = docStore.documentFolderMap.get(documentId);
    if (!folderId || folderId === -1) {
      console.log('📂 [UI] 文档不在任何文件夹中，无需展开:', documentId);
      return;
    }

    console.log('📂 [UI] 开始展开文档路径:', documentId, '文件夹ID:', folderId);

    // 获取文件夹的完整父级路径
    const getFolderParentPath = (folderId: number): number[] => {
      const path: number[] = [];
      const folder = docStore.folderMap.get(folderId);

      if (folder && folder.parent_id && folder.parent_id !== -1) {
        // 递归获取父级路径
        path.push(...getFolderParentPath(folder.parent_id));
        path.push(folder.parent_id);
      }

      return path;
    };

    // 获取需要展开的完整路径（包括文档所在文件夹）
    const parentPath = getFolderParentPath(folderId);
    const fullPath = [...parentPath, folderId];

    console.log('📂 [UI] 需要展开的文件夹路径:', fullPath);

    // 触发文件夹展开事件
    this.triggerFolderExpansion(fullPath);

    console.log('✅ [UI] 文档路径展开完成:', documentId);
  } catch (error) {
    console.error('❌ [UI] 展开文档路径失败:', error);
  }
}
```

#### 事件触发机制

```typescript
// 触发文件夹展开事件
triggerFolderExpansion(folderPath: number[]) {
  // 设置需要展开的文件夹路径
  this.pendingExpansionPath = folderPath;

  // 触发展开事件，让FolderTree组件监听并处理
  this.expandFolderPath = Date.now(); // 使用时间戳触发响应式更新
}
```

### 2. FolderTree 组件修改

在 `src/components/FolderTree.vue` 中添加了展开事件监听器：

#### 事件监听器

```typescript
// 监听UI store中的文件夹展开事件
watch(
  () => uiStore.expandFolderPath,
  async () => {
    if (uiStore.pendingExpansionPath.length > 0) {
      console.log('📂 [FolderTree] 收到展开事件，路径:', uiStore.pendingExpansionPath);
      await expandFolderPath(uiStore.pendingExpansionPath);
      // 清除待展开路径
      uiStore.pendingExpansionPath = [];
    }
  },
);
```

#### 展开路径处理方法

```typescript
// 展开指定路径的所有文件夹
const expandFolderPath = async (folderPath: number[]) => {
  console.log('📂 [FolderTree] 开始展开文件夹路径:', folderPath);

  for (const folderId of folderPath) {
    // 检查文件夹是否已经展开
    if (!expanded.value[folderId]) {
      console.log('📂 [FolderTree] 展开文件夹:', folderId);

      // 设置展开状态
      expanded.value[folderId] = true;

      // 加载文件夹数据
      await load(folderId);

      // 等待展开动画完成
      await new Promise((resolve) => setTimeout(resolve, 150));
    } else {
      console.log('📂 [FolderTree] 文件夹已展开:', folderId);
    }
  }

  console.log('✅ [FolderTree] 文件夹路径展开完成');
};
```

### 3. EditorGroup 组件修改

在 `src/components/EditorGroup.vue` 中修改了 `toggleTab` 方法：

```typescript
const toggleTab = async (docId: number) => {
  tab.value = docId;
  toggleEditor(winId.value, docId);
  docStore.setLlmDocumentKey(docId);
  uiStore.highlightTreeItem = `document-${docId}`;

  // 确保文档所在文件夹及其父级文件夹都展开
  await uiStore.expandDocumentPath(docId);
};
```

## 技术实现要点

### 1. 数据源利用

- 使用 `docStore.documentFolderMap` 快速获取文档所属文件夹
- 利用 `docStore.folderMap` 进行父级文件夹查找
- 通过 `uiStore.expandedFolders` 管理展开状态

### 2. 路径算法

- **递归查找**：从目标文件夹向上递归查找所有父级
- **路径构建**：构建从根级到目标文件夹的完整路径
- **去重处理**：避免重复展开已经展开的文件夹

### 3. 事件驱动机制

- **响应式触发**：使用时间戳触发响应式更新
- **组件间通信**：通过UI store实现跨组件事件传递
- **状态同步**：确保UI状态与数据状态一致
- **事件清理**：处理完成后自动清除待处理状态

### 4. 异步处理

- **异步方法**：使用 async/await 确保操作顺序
- **延迟控制**：添加适当延迟确保UI更新完成
- **错误处理**：完善的异常捕获和日志记录

### 5. 性能优化

- **状态检查**：只展开当前折叠的文件夹
- **批量操作**：一次性处理整个路径
- **动态导入**：避免循环依赖问题
- **最小延迟**：使用150ms延迟确保动画完成

## 用户体验提升

### 1. 即时定位

- 切换标签页后立即看到文档在文件树中的位置
- 无需手动展开文件夹查找文档
- 提供清晰的文档层级关系视图

### 2. 操作连贯性

- 从编辑器到文件树的无缝体验
- 保持用户操作的连续性
- 减少认知负担

### 3. 视觉反馈

- 文档高亮显示在展开的文件树中
- 完整的文件夹路径一目了然
- 清晰的文档组织结构

## 应用场景

### 1. 多文档编辑

- 在多个文档间快速切换
- 快速定位当前编辑的文档位置
- 了解文档的组织结构

### 2. 深层文件夹结构

- 处理复杂的文件夹层级
- 自动展开深层嵌套的文件夹
- 避免手动逐级展开的繁琐操作

### 3. 文档管理

- 快速了解文档的分类归属
- 便于进行文档的组织和整理
- 提高文档查找效率

## 测试建议

### 1. 基础功能测试

- 测试根级文件夹中的文档切换
- 测试多层嵌套文件夹中的文档切换
- 测试不在任何文件夹中的文档

### 2. 性能测试

- 测试深层嵌套（10+ 层级）的展开速度
- 测试大量文件夹的处理性能
- 测试连续快速切换标签页的响应

### 3. 边界情况测试

- 测试文件夹数据不一致的情况
- 测试文档移动后的路径更新
- 测试并发操作的处理

### 4. 用户体验测试

- 验证展开动画的流畅性
- 确认文档高亮的准确性
- 测试不同文件夹结构的适应性

## 相关文件

- `src/stores/ui.ts` - 添加 `expandDocumentPath` 方法
- `src/components/EditorGroup.vue` - 修改 `toggleTab` 方法
- `src/stores/doc.ts` - 数据源（documentFolderMap, folderMap）

## 总结

自动展开文档路径功能通过智能的路径分析和渐进式展开策略，显著提升了用户在多文档编辑环境中的导航体验。该功能既保证了操作的准确性，又考虑了性能和用户体验，是文档管理系统中的重要用户体验优化。
