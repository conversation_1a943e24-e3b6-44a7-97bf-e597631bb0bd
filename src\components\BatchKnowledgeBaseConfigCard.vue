<template>
  <div>
    <q-card :flat="bolderless" :bordered="!bolderless" class="full-width">
      <q-card-section class="q-pb-none">
        <div class="text-h6 q-mb-sm">
          {{ $t('src.components.BatchKnowledgeBaseConfigCard.batch_add_to_knowledge_base') }}
        </div>
        <div class="text-caption text-grey-6">
          {{
            $t(
              'src.components.BatchKnowledgeBaseConfigCard.select_knowledge_base_and_chunking_strategy',
            )
          }}
        </div>
      </q-card-section>

      <q-card-section>
        <!-- 知识库选择 -->
        <q-select
          v-model="selectedKnowledgeBase"
          :options="knowledgeBaseOptions"
          :label="$t('src.components.BatchKnowledgeBaseConfigCard.select_knowledge_base')"
          outlined
          dense
          emit-value
          map-options
          hide-hint
          hide-bottom-space
          :loading="loadingKnowledgeBases"
          :rules="[
            (val) =>
              !!val ||
              $t('src.components.BatchKnowledgeBaseConfigCard.please_select_knowledge_base'),
          ]"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">{{
                $t('src.components.BatchKnowledgeBaseConfigCard.no_knowledge_base')
              }}</q-item-section>
            </q-item>
          </template>
        </q-select>

        <!-- 创建新知识库选项 -->
        <div class="row q-gutter-sm q-mb-md q-mt-xs">
          <q-btn
            flat
            dense
            color="primary"
            icon="mdi-plus"
            :label="$t('src.components.BatchKnowledgeBaseConfigCard.create_new_knowledge_base')"
            @click="handleCreateNewKnowledgeBase"
          />
        </div>

        <!-- 切割策略选择 -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">
            {{ $t('src.components.BatchKnowledgeBaseConfigCard.chunking_strategy') }}
          </div>

          <!-- 切割方法选择 -->
          <q-select
            v-model="chunkingMethod"
            :options="chunkingMethodOptions"
            :label="$t('src.components.BatchKnowledgeBaseConfigCard.chunking_method')"
            outlined
            dense
            emit-value
            map-options
            class="q-mb-sm"
          />

          <!-- 切割配置 -->
          <div v-if="chunkingMethod" class="q-pl-md">
            <div class="row q-gutter-md">
              <q-input
                v-model.number="chunkingConfig.chunkSize"
                :label="$t('src.components.BatchKnowledgeBaseConfigCard.chunk_size')"
                type="number"
                outlined
                dense
                style="width: 120px"
                :min="100"
                :max="8000"
              />
              <q-input
                v-model.number="chunkingConfig.chunkOverlap"
                :label="$t('src.components.BatchKnowledgeBaseConfigCard.chunk_overlap')"
                type="number"
                outlined
                dense
                style="width: 120px"
                :min="0"
                :max="1000"
              />
            </div>
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          padding="xs md"
          :label="$t('src.components.BatchKnowledgeBaseConfigCard.cancel')"
          @click="$emit('cancel')"
        />
        <q-btn
          color="primary"
          padding="xs md"
          :label="$t('src.components.BatchKnowledgeBaseConfigCard.start_batch_processing')"
          @click="handleStartBatchProcessing"
          :loading="processing"
          :disable="!selectedKnowledgeBase || !chunkingMethod"
        />
      </q-card-actions>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { useKnowledgeStore } from 'src/stores/knowledge';
import type { ChunkingMethod, ChunkingConfig } from 'src/types/qwen';

interface Props {
  bolderless?: boolean;
}

interface Emits {
  (e: 'cancel'): void;
  (
    e: 'start-processing',
    config: {
      knowledgeBaseId: number;
      chunkingMethod: ChunkingMethod;
      chunkingConfig: ChunkingConfig;
    },
  ): void;
}

withDefaults(defineProps<Props>(), {
  bolderless: false,
});

const emit = defineEmits<Emits>();

const { t: $t } = useI18n({ useScope: 'global' });
const $q = useQuasar();
const knowledgeStore = useKnowledgeStore();

// 状态
const selectedKnowledgeBase = ref<number | null>(null);
const chunkingMethod = ref<ChunkingMethod>('recursiveCharacter');
const chunkingConfig = ref<ChunkingConfig>({
  chunkSize: 1000,
  chunkOverlap: 200,
});
const processing = ref(false);
const loadingKnowledgeBases = ref(false);

// 计算属性
const knowledgeBaseOptions = computed(() => {
  return knowledgeStore.knowledgeBases.map((kb) => ({
    label: kb.name,
    value: kb.id,
  }));
});

const chunkingMethodOptions = computed(() => [
  {
    label: $t('src.components.BatchKnowledgeBaseConfigCard.recursive_character_text_splitter'),
    value: 'recursiveCharacter' as ChunkingMethod,
  },
  {
    label: $t('src.components.BatchKnowledgeBaseConfigCard.markdown_text_splitter'),
    value: 'markdown' as ChunkingMethod,
  },
  {
    label: $t('src.components.BatchKnowledgeBaseConfigCard.latex_text_splitter'),
    value: 'latex' as ChunkingMethod,
  },
  {
    label: $t('src.components.BatchKnowledgeBaseConfigCard.smart_text_splitter'),
    value: 'smart' as ChunkingMethod,
  },
]);

// 方法
const loadKnowledgeBases = async () => {
  loadingKnowledgeBases.value = true;
  try {
    await knowledgeStore.refreshKnowledgeBases();
  } finally {
    loadingKnowledgeBases.value = false;
  }
};

const handleCreateNewKnowledgeBase = () => {
  // 简单提示用户去设置页面创建知识库
  $q.notify({
    type: 'info',
    message: '请前往设置页面创建新的知识库',
    position: 'top',
  });
};

const handleStartBatchProcessing = () => {
  if (!selectedKnowledgeBase.value || !chunkingMethod.value) {
    return;
  }

  emit('start-processing', {
    knowledgeBaseId: selectedKnowledgeBase.value,
    chunkingMethod: chunkingMethod.value,
    chunkingConfig: chunkingConfig.value,
  });
};

// 生命周期
onMounted(() => {
  void loadKnowledgeBases();
});
</script>
