#ifndef KNOWLEDGEAPI_H
#define KNOWLEDGEAPI_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QString>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>
#include <QRegularExpression>
#include <QSet>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QEventLoop>
#include <QTimer>
#include <QProcessEnvironment>
#include <QStringList>
#include <QHash>
#include <QThread>
#include <QThreadPool>
#include <QRunnable>
#include <QMutex>
#include <QMutexLocker>
#include <QTimer>
#include <QQueue>
#include <QJsonArray>
#include <QScopeGuard>
#include <QDateTime>
#include <QList>
#include <memory>
#include <vector>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <atomic>

// ObjectBox forward declarations - only C API in header
#include "objectbox.h"

// Forward declarations
class DatabaseApi;
class LocalGGUFEmbedding;

// Forward declare ObjectBox types to avoid including C++ headers
namespace InkCop
{
    namespace Knowledge
    {
        struct KnowledgeBase;
        struct KnowledgeDocument;
        struct KnowledgeChunk;
        struct KnowledgeQuery;
    }
}

namespace obx
{
    class Store;
    template <typename T>
    class Box;
}

// Forward declaration for friend class
class BackgroundVectorizationWorker;

class KnowledgeApi : public QObject
{
    Q_OBJECT

    // Allow BackgroundVectorizationWorker to access private members
    friend class BackgroundVectorizationWorker;

public:
    explicit KnowledgeApi(QObject *parent = nullptr, DatabaseApi *databaseApi = nullptr);
    ~KnowledgeApi();

    Q_INVOKABLE bool initializeDatabase();

signals:
    // 异步向量化完成信号
    void documentVectorized(qint64 docId, int chunkCount);
    // 本地模型加载完成信号
    void localModelLoadCompleted(bool success, const QString &message);
    // 新的向量化进度信号
    void vectorizationProgress(const QString &kbId, const QString &docId, int completed, int total);
    void chunkVectorized(const QString &kbId, const QString &docId, int chunkIndex, int totalChunks);

public slots:
    // 知识库管理
    Q_INVOKABLE QString createKnowledgeBase(const QString &name, const QString &description = "", const QString &userId = "default");
    Q_INVOKABLE QString getAllKnowledgeBases();
    Q_INVOKABLE QString getKnowledgeBase(const QString &kbId);
    Q_INVOKABLE bool deleteKnowledgeBase(const QString &kbId);
    Q_INVOKABLE QString updateKnowledgeBase(const QString &kbId, const QString &name, const QString &description);

    // 文档管理 - 统一接口
    Q_INVOKABLE QString addDocumentToKnowledgeBase(const QString &kbId, const QString &title, const QString &content, const QString &documentType = "text");
    Q_INVOKABLE QString getDocumentsByKnowledgeBase(const QString &kbId);
    Q_INVOKABLE QString getDocument(const QString &docId);
    Q_INVOKABLE bool updateDocument(const QString &docId, const QString &title, const QString &content, const QString &documentType = "markdown");
    Q_INVOKABLE bool deleteDocument(const QString &docId);

    // 新的分离式文档创建和向量化接口
    Q_INVOKABLE QString createKnowledgeDocumentOnly(const QString &kbId, const QString &title, const QString &content, const QString &documentType = "markdown");
    Q_INVOKABLE QString submitChunkingResults(const QString &docId, const QJsonArray &chunks);
    Q_INVOKABLE QString getVectorizationProgress(const QString &docId);
    Q_INVOKABLE QString checkPendingVectorization();
    Q_INVOKABLE QString checkAndResumeVectorization(); // 检查并恢复未完成的向量化

    // 统计和工具
    Q_INVOKABLE QString getKnowledgeBaseStats(const QString &kbId);
    Q_INVOKABLE int getKnowledgeBaseCount();
    Q_INVOKABLE int getDocumentCount(const QString &kbId = "");

    // 测试方法
    Q_INVOKABLE QString testConnection();

    // 搜索功能
    Q_INVOKABLE QString searchKnowledgeBase(const QString &kbId, const QString &query, int limit = 10, double minScore = -1.0);
    Q_INVOKABLE QString searchAllKnowledgeBases(const QString &query, int limit = 10, double minScore = -1.0);

    // HNSW优化搜索功能
    Q_INVOKABLE QString searchKnowledgeBaseWithHNSW(const QString &kbId, const QString &query, int limit = 10, double minScore = -1.0);
    Q_INVOKABLE QString searchAllKnowledgeBasesWithHNSW(const QString &query, int limit = 10, double minScore = -1.0);

    // 向量功能
    Q_INVOKABLE QString generateEmbedding(const QString &text);
    Q_INVOKABLE QString updateChunkEmbeddings(const QString &docId);

    // 维护方法
    Q_INVOKABLE QString regenerateDocumentChunks(const QString &docId);
    Q_INVOKABLE QString deleteDocumentChunks(const QString &docId);

    // 移除复杂的批处理方法以确保稳定性
    // Q_INVOKABLE QString processBatchVectorization(const QJsonArray &documents);
    // Q_INVOKABLE void enableBatchVectorization(bool enabled = true);
    // Q_INVOKABLE QString getVectorizationQueueStatus() const;

    // 数据查看工具
    Q_INVOKABLE QString viewAllData();
    Q_INVOKABLE QString viewKnowledgeBaseData(const QString &kbId);
    Q_INVOKABLE QString viewDocumentChunks(const QString &docId);
    Q_INVOKABLE QString exportKnowledgeBaseData(const QString &kbId);

    // 调试接口函数
    Q_INVOKABLE QString debugEmbeddingGeneration(const QString &text);
    Q_INVOKABLE QString debugObjectBoxData(const QString &filter = "");

    // 本地GGUF模型管理
    Q_INVOKABLE bool loadLocalGGUFModel(const QString &modelPath, int gpuLayers = 20, int contextSize = 2048);
    Q_INVOKABLE bool loadLocalGGUFModelWithGpu(const QString &modelPath, int gpuLayers = 20, int contextSize = 2048, int selectedGpuDevice = -1);
    Q_INVOKABLE void loadLocalGGUFModelAsync(const QString &modelPath, int gpuLayers = 20, int contextSize = 2048);                                    // 异步加载
    Q_INVOKABLE void loadLocalGGUFModelAsyncWithGpu(const QString &modelPath, int gpuLayers = 20, int contextSize = 2048, int selectedGpuDevice = -1); // 异步加载支持GPU选择
    Q_INVOKABLE void unloadLocalGGUFModel();
    Q_INVOKABLE bool isLocalGGUFLoaded() const;
    Q_INVOKABLE QString getLocalGGUFInfo() const;
    Q_INVOKABLE QString getLocalGGUFStatus() const;
    Q_INVOKABLE QString detectGpuCapabilities() const;
    Q_INVOKABLE QString detectAllGpuDevices() const;          // 新增：检测所有GPU设备
    Q_INVOKABLE QString getGpuDeviceInfo(int deviceId) const; // 新增：获取特定GPU设备信息
    Q_INVOKABLE QString testLocalGGUFEmbedding(const QString &text) const;
    Q_INVOKABLE void triggerAutoLoadLocalModel();  // 触发自动加载本地模型和向量化检查（供前端调用）
    Q_INVOKABLE QString getGpuDiagnostics() const; // GPU诊断信息
    Q_INVOKABLE bool forceResetLocalGGUF();        // 强制重置GGUF引擎

    // 文件选择方法
    Q_INVOKABLE QString selectFile(const QString &extension = QString(),
                                   const QString &caption = QString(),
                                   const QString &filter = QString()) const;

private:
#ifndef OBJECTBOX_DISABLED
    // ObjectBox store and boxes
    std::unique_ptr<obx::Store> m_store;
    std::unique_ptr<obx::Box<InkCop::Knowledge::KnowledgeBase>> m_kbBox;
    std::unique_ptr<obx::Box<InkCop::Knowledge::KnowledgeDocument>> m_docBox;
    std::unique_ptr<obx::Box<InkCop::Knowledge::KnowledgeChunk>> m_chunkBox;
    std::unique_ptr<obx::Box<InkCop::Knowledge::KnowledgeQuery>> m_queryBox;
#else
    // Fallback storage when ObjectBox is disabled
    bool m_objectboxDisabled = true;
#endif

    // 网络管理器用于embedding API调用
    QNetworkAccessManager *m_networkManager;

    // 数据库API引用用于读取前端配置
    DatabaseApi *m_databaseApi;

    // 本地GGUF嵌入式模型
    std::unique_ptr<LocalGGUFEmbedding> m_localGGUF;

    // 向量化状态控制
    mutable QMutex m_vectorizationMutex;
    std::atomic_bool m_vectorizationCancelled{false};
    std::atomic_int m_activeVectorizationCount{0};
    std::atomic_int m_currentBatchSize{0};

    // 统一日志方法
    void logInfo(const QString &message, const QString &context = "") const;
    void logDebug(const QString &message, const QString &context = "") const;
    void logWarning(const QString &message, const QString &context = "") const;
    void logError(const QString &message, const QString &context = "") const;

    // Helper methods
    QString objectToJson(const InkCop::Knowledge::KnowledgeBase &kb);
    QString objectToJson(const InkCop::Knowledge::KnowledgeDocument &doc);
    QJsonObject kbToJsonObject(const InkCop::Knowledge::KnowledgeBase &kb);
    QJsonObject docToJsonObject(const InkCop::Knowledge::KnowledgeDocument &doc);
    bool createDirectories();
    QString getKnowledgeDatabasePath();

    // 统一配置管理
    QJsonObject getKnowledgeConfig();
    bool isApiConfigured();

    // 向量计算方法
    std::vector<float> textToEmbedding(const QString &text);
    double calculateCosineSimilarity(const std::vector<float> &vec1, const std::vector<float> &vec2);
    double calculateTextSimilarity(const QString &query, const QString &text); // 文本相似度计算
    std::vector<float> generateSimpleEmbedding(const QString &text);           // 简单的TF-IDF向量化

    // 向量维度检测方法
    Q_INVOKABLE int detectEmbeddingDimension();         // 检测当前embedding模型的维度
    Q_INVOKABLE QString validateHNSWConfiguration();    // 验证HNSW配置与实际向量维度的匹配性
    int getEmbeddingDimensionFromKnowledgeBaseConfig(); // 从知识库配置中获取保存的维度

    // 外部API embedding方法
    std::vector<float> generateApiEmbedding(const QString &text);           // 调用外部API生成向量
    std::vector<float> generateMultiSemanticEmbedding(const QString &text); // 多重语义切割和向量融合
    QString getEmbeddingApiConfig();                                        // 获取embedding API配置
    bool isEmbeddingApiAvailable();                                         // 检查API是否可用
    QJsonObject callEmbeddingApi(const QString &text, int retryCount = 3);  // 调用embedding API

    // 本地GGUF embedding方法
    std::vector<float> generateLocalGGUFEmbedding(const QString &text); // 使用本地GGUF模型生成向量
    bool initializeLocalGGUF();                                         // 初始化本地GGUF引擎
    void cleanupLocalGGUF();                                            // 清理本地GGUF引擎

    // 自动加载本地模型和向量化检查
    void autoLoadLocalModelIfNeeded(); // 检查并自动加载本地模型或触发云端向量化检查（静默方式）

    // 异步向量化处理方法
    void processDocumentVectorization(qint64 docId, const QString &content, const QString &documentType);

    // 向量化工作线程相关 - 支持批处理和取消
    class VectorizationWorker : public QRunnable
    {
    public:
        VectorizationWorker(KnowledgeApi *api, qint64 docId, const QString &content, const QString &documentType, std::atomic_bool *cancelled = nullptr);
        void run() override;

    private:
        KnowledgeApi *m_api;
        qint64 m_docId;
        QString m_content;
        QString m_documentType;
        std::atomic_bool *m_cancelled;
    };

    // 批处理向量化工作线程
    class BatchVectorizationWorker : public QRunnable
    {
    public:
        BatchVectorizationWorker(KnowledgeApi *api, const QList<QPair<qint64, QString>> &chunks, std::atomic_bool *cancelled = nullptr);
        void run() override;

    private:
        KnowledgeApi *m_api;
        QList<QPair<qint64, QString>> m_chunks;
        std::atomic_bool *m_cancelled;
    };

    // 后台向量化工作线程
    class BackgroundVectorizationWorker : public QRunnable
    {
    public:
        struct ProgressTracker
        {
            KnowledgeApi *api;
            obx_id kbId;
            obx_id docId;
            int totalChunks;
            std::atomic_int processed{0};

            void updateProgress(int completed);
            void chunkCompleted(int chunkIndex);
            void completed();
        };

        BackgroundVectorizationWorker(KnowledgeApi *api,
                                      const QList<QPair<qint64, QString>> &chunks,
                                      ProgressTracker *tracker,
                                      std::atomic_bool *cancelled);
        void run() override;

    private:
        KnowledgeApi *m_api;
        QList<QPair<qint64, QString>> m_chunks;
        ProgressTracker *m_tracker;
        std::atomic_bool *m_cancelled;
    };

    void processDocumentVectorizationInThread(qint64 docId, const QString &content, const QString &documentType);
    void processDocumentVectorizationAsync(obx_id docId);
    void processDocumentVectorizationInBackground(obx_id docId, const std::vector<InkCop::Knowledge::KnowledgeChunk> &chunks);
    void checkAndRecoverPendingVectorization();

    // 私有批处理方法
    void processSmallBatch(qint64 docId, const QList<QPair<qint64, QString>> &chunks);
    void processLargeBatch(qint64 docId, const QList<QPair<qint64, QString>> &chunks);
    void updateDocumentCompletionStatus(qint64 docId, int totalChunks);

    // 批量处理控制
    void cancelCurrentVectorization();
    bool isVectorizationInProgress() const;
    int getCurrentBatchSize() const;
    QString getVectorizationStatus() const;

    // 批处理配置
    static constexpr int BATCH_SIZE = 50;     // 每批处理的chunk数量
    static constexpr int BATCH_DELAY_MS = 10; // 批次间延迟(ms)

    // 移除批处理相关方法
    // void processBatchVectorizationInThread(...);

    // 移除批处理槽函数
    // private slots:
    //     void processBatchQueue();
};

#endif // KNOWLEDGEAPI_H
