import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import type {
  Message,
  Conversation,
  AssistantMessage as AssistantMessageType,
  ToolMessage as ToolMessageType,
  Attachment,
  AttachmentState,
  DocumentAttachment,
  ImageAttachment,
  FileAttachment,
  TextSnippetAttachment,
} from 'src/types/qwen';
import type { Document } from 'src/types/doc';
// 移除 Memory 类型导入
import type { KnowledgeBase, KnowledgeSearchResult, KnowledgeServiceStatus } from 'src/env';
import { useLlmRouter } from 'src/composeables/useLlmRouter';
import { messagePreprocessor, type LlmCallParams } from 'src/services/messagePreprocessor';
import { useUiStore } from './ui';
import { ModelType, type CategorizedModels } from 'src/types/modelCategories';
// 移除记忆相关功能

// 移除 getDocumentInfo，已不再需要

export const useLlmStore = defineStore('llm', () => {
  // ==================== 状态定义 ====================

  // 基础状态
  const loading = ref(false);
  const input = ref('');
  const fontSize = ref(Number(localStorage.getItem('fontSize')) || 1);
  const knowledgeServiceStatus = ref<KnowledgeServiceStatus>({ status: 'offline' });

  // 对话模式状态
  const conversationMode = ref<'agent' | 'chat'>('agent'); // 默认为agent模式

  // 对话相关状态
  const conversations = ref<Conversation[]>([]); // 文档关联的对话
  const chatConversations = ref<Conversation[]>([]); // 独立对话（不关联文档）
  const currentConversation = ref<Conversation | undefined>();
  const messages = ref<Message[]>([]);
  const conversationPrompt = ref('');

  // 注意：工具调用能力现在通过TOOLS_CALLS分组来判断，不再使用硬编码的关键字列表

  // 文档相关状态
  const currentDocument = ref<Document | undefined>();
  const relatedDocuments = ref<Document[]>([]);
  // 用户添加的关联文档，焦点文档切换时自动更新关联文档，如果是用户添加的文档，则不删除
  const relatedDocumentsAddByUser = ref<Document[]>([]);

  // 附加内容状态
  const attachmentState = ref<AttachmentState>({
    attachments: [],
    uploading: false,
    uploadProgress: 0,
  });

  // 当前选中的文本片段（特殊处理，只能有一个）
  const currentSelectedText = ref<{
    content: string;
    documentId?: number;
    position?: { from: number; to: number };
  } | null>(null);

  // 移除记忆相关状态

  // 知识库相关状态
  const knowledgeBases = ref<KnowledgeBase[]>([]);
  const selectedKnowledgeBase = ref<KnowledgeBase | null>(null);
  const knowledgeSearchResults = ref<KnowledgeSearchResult[]>([]);
  const showKnowledgeResults = ref(false);

  // 数据加载状态控制
  const isLoadingChatConversations = ref(false);
  const chatConversationsLoaded = ref(false);

  // 对话中断控制
  const abortController = ref<AbortController | null>(null);
  const isAborting = ref(false);

  // 移除冗余的 qwenSettings，直接使用 llmRouter.currentSettings
  // 使用计算属性来响应式地检查是否需要设置
  const needSettings = computed(() => {
    return llmRouter.needSettings.value;
  });

  // 移除记忆相关的UI状态

  // 用户提问状态
  const currentUserQuestion = ref<{
    questionId: string;
    question: string;
    timestamp: number;
  } | null>(null);
  const userAnswer = ref('');

  // 存储当前等待回答的 Promise 的 resolve/reject
  let currentQuestionResolver: {
    resolve: (value: { success: boolean; answer: string }) => void;
    reject: (error: Error) => void;
    id: string;
  } | null = null;

  // 用户提问超时定时器
  let questionTimeoutTimer: NodeJS.Timeout | null = null;

  // ==================== 依赖注入 ====================
  const llmRouter = useLlmRouter();
  const { sendMessage: llmSendMessage } = llmRouter;
  const uiStore = useUiStore();

  // 动态导入 useSqlite 的辅助函数
  const getSqliteApi = async () => {
    const { useSqlite } = await import('src/composeables/useSqlite');
    return useSqlite();
  };

  // 移除记忆服务相关功能

  // 移除记忆服务状态监听

  // ==================== 工具映射逻辑 ====================

  /**
   * 根据对话模式和文档状态获取启用的工具列表
   */
  async function getEnabledToolsForMode(mode: 'agent' | 'chat') {
    // 基础工具 - 两种模式都启用
    const baseTool = [
      'ask_user', // 用户交互工具
      'search_web', // 网络搜索工具
      'search_pexels', // Pexels图片搜索工具
    ];

    // 文档管理工具 - 只读权限，两种模式都启用
    const readOnlyTools = [
      'get_file_tree',
      'search_documents',
      'get_document_info',
      'search_text',
      'open_document',
    ];

    // 文档编辑工具 - 需要修改权限，只在agent模式下启用
    const editingTools = [
      'search_and_replace',
      'search_and_insert',
      'search_and_delete',
      'has_pending_changes',
      'accept_all_changes',
      'reject_all_changes',
      'format_and_reset_document',
    ];

    // 文档和文件夹管理工具 - 需要修改权限，只在agent模式下启用
    const managementTools = [
      'create_document',
      'delete_documents',
      'rename_document',
      'update_document_content',
      'create_folder',
      'delete_folder',
      'rename_folder',
    ];

    const enabledTools = [...baseTool];

    // 根据模式添加工具
    if (mode === 'agent') {
      // Agent模式：拥有所有权限
      const { tools: editorTools } = await import('src/llm/tools/editor');
      const { tools: fileTools } = await import('src/llm/tools/file');
      enabledTools.push(
        ...readOnlyTools,
        ...editingTools,
        ...managementTools,
        ...editorTools.map((t) => t.function.name),
        ...fileTools.map((t) => t.function.name),
      );
    } else if (mode === 'chat') {
      // Chat模式：只有读取权限
      enabledTools.push(...readOnlyTools);
    }
    return enabledTools;
  }

  /**
   * 切换对话模式
   */
  function toggleConversationMode(mode: 'agent' | 'chat') {
    conversationMode.value = mode;
    // 保存到本地存储
    localStorage.setItem('conversationMode', mode);
  }

  // ==================== 计算属性 ====================
  const hasConversations = computed(() => conversations.value.length > 0);
  const hasMessages = computed(() => messages.value.length > 0);
  const canSendMessage = computed(
    () => !loading.value && !needSettings.value && input.value.trim() !== '' && !isAborting.value,
  );

  // 基于对话模式和文档状态的工具启用状态
  const enabledTools = ref<string[]>([]);

  async function updateEnabledTools() {
    enabledTools.value = await getEnabledToolsForMode(conversationMode.value);
  }

  watch([conversationMode, currentDocument], updateEnabledTools, { immediate: true });

  // 知识库选项
  const knowledgeBaseOptions = computed(() => [
    { label: '不使用知识库', value: null },
    ...knowledgeBases.value.map((kb) => ({
      label: kb.name,
      value: kb,
    })),
  ]);

  // ==================== 核心方法 ====================

  /**
   * 初始化LLM设置和数据
   */
  async function initSettings() {
    // needSettings 现在是计算属性，会自动响应设置变化
    // 添加监听器来调试设置变化
    watch(
      () => needSettings.value,
      (newValue, oldValue) => {
        console.log(`[LLM Store] needSettings 变化: ${oldValue} -> ${newValue}`);
        if (!newValue && oldValue) {
          console.log('🎉 [LLM Store] API密钥已配置，对话功能已启用');
        }
      },
      { immediate: true },
    );

    // 从本地存储恢复对话模式
    const savedMode = localStorage.getItem('conversationMode') as 'agent' | 'chat';
    if (savedMode && ['agent', 'chat'].includes(savedMode)) {
      conversationMode.value = savedMode;
    }

    // 初始化时加载独立对话列表和知识库列表
    await Promise.all([loadChatConversations(), loadKnowledgeBasesList()]);
  }

  // 用于跟踪知识库监听器是否已设置的标志
  let knowledgeBasesWatcherSet = false;

  /**
   * 加载知识库列表
   */
  async function loadKnowledgeBasesList() {
    try {
      // 使用 knowledge store 来加载知识库列表
      const { useKnowledgeStore } = await import('src/stores/knowledge');
      const knowledgeStore = useKnowledgeStore();
      const kbs = await knowledgeStore.loadKnowledgeBases();
      knowledgeBases.value = kbs;
      knowledgeServiceStatus.value = { status: 'online' };

      // 设置监听器，当 knowledgeStore 的知识库列表变化时自动同步
      // 使用一个变量来避免重复设置监听器
      if (!knowledgeBasesWatcherSet) {
        watch(
          () => knowledgeStore.knowledgeBases,
          (newKnowledgeBases) => {
            console.log('🔄 [LLM Store] 检测到知识库列表变化，同步更新');
            knowledgeBases.value = [...newKnowledgeBases];
          },
          { deep: true },
        );
        // 标记已设置监听器，避免重复设置
        knowledgeBasesWatcherSet = true;
      }
    } catch (error) {
      console.error('❌ [LLM Store] 加载知识库列表失败:', error);
      knowledgeServiceStatus.value = { status: 'offline' };
    }
  }

  /**
   * 刷新知识库列表
   */
  async function refreshKnowledgeBasesList() {
    try {
      console.log('🔄 [LLM Store] 手动刷新知识库列表');
      const { useKnowledgeStore } = await import('src/stores/knowledge');
      const knowledgeStore = useKnowledgeStore();
      const kbs = await knowledgeStore.loadKnowledgeBases();
      knowledgeBases.value = kbs;
      knowledgeServiceStatus.value = { status: 'online' };
    } catch (error) {
      console.error('❌ [LLM Store] 刷新知识库列表失败:', error);
      knowledgeServiceStatus.value = { status: 'offline' };
    }
  }

  /**
   * 选择知识库
   */
  /**
   * 选择知识库 - 统一的知识库选择入口
   */
  async function selectKnowledgeBase(kb: KnowledgeBase | null) {
    selectedKnowledgeBase.value = kb;

    // 清除之前的搜索结果和状态
    knowledgeSearchResults.value = [];
    showKnowledgeResults.value = false;

    // 设置知识库到统一服务中
    const { knowledgeService } = await import('src/services/knowledgeService');
    knowledgeService.setSelectedKnowledgeBase(kb);

    if (!kb) {
      knowledgeService.clearKnowledgeContext();
    }
  }

  /**
   * 搜索知识库内容 - 仅用于UI显示，不再用于对话上下文
   */
  async function searchKnowledgeContent(query: string) {
    if (!selectedKnowledgeBase.value) {
      console.log('🔍 [LLM Store] 未选择知识库，跳过搜索');
      return [];
    }

    try {
      // 1. 执行搜索
      const { useKnowledge } = await import('src/composeables/useKnowledge');
      const knowledge = useKnowledge();
      const results = await knowledge.searchKnowledge(query, selectedKnowledgeBase.value.id, 5);

      // 2. 更新UI状态（仅用于显示搜索结果）
      knowledgeSearchResults.value = results;
      // showKnowledgeResults.value = results.length > 0;

      return results;
    } catch (error) {
      console.error('❌ [LLM Store] 知识库搜索失败:', error);
      return [];
    }
  }

  /**
   * 设置当前文档并加载相关对话
   */
  async function setCurrentDocument(doc: Document | undefined) {
    // 如果当前有对话正在进行，不要重置状态，只更新文档引用
    const hasActiveConversation = currentConversation.value !== undefined;

    // 只有在文档变化且没有活跃对话时才重置状态
    if (currentDocument.value?.id !== doc?.id && !hasActiveConversation) {
      resetState();
    }

    currentDocument.value = doc;

    if (doc) {
      // 添加到相关文档
      if (!relatedDocuments.value.some((d) => d.id === doc.id)) {
        relatedDocuments.value.push(doc);
      }

      // 只有在没有当前对话时才加载文档关联的对话
      if (!hasActiveConversation) {
        // 加载对话列表
        const convs = doc.conversations as unknown as Partial<Conversation>[];
        if (Array.isArray(convs)) {
          conversations.value = convs as Conversation[];

          // 自动选择第一个对话
          if (conversations.value.length > 0) {
            await selectConversation(conversations.value[0]);
          }
        }
      } else {
        // 如果有活跃对话，只更新对话列表但不切换对话
        const convs = doc.conversations as unknown as Partial<Conversation>[];
        if (Array.isArray(convs)) {
          conversations.value = convs as Conversation[];
        }
      }
    }
  }

  /**
   * 选择对话
   */
  async function selectConversation(conversation: Conversation) {
    currentConversation.value = conversation;
    conversationPrompt.value = conversation.prompt || '';

    // 加载对话历史
    const { getConversation } = await getSqliteApi();
    const conversationData = await getConversation(conversation.id);
    const messageHistory = JSON.parse(conversationData.messages || '[]');
    messages.value = messageHistory;
  }

  /**
   * 创建新对话
   */
  async function createNewConversation(title: string) {
    if (!currentDocument.value) return;

    const { createConversation, getConversation } = await getSqliteApi();
    const newId = await createConversation(currentDocument.value.id, title, JSON.stringify([]), '');

    const newConv = await getConversation(newId);
    conversations.value.unshift(newConv);
    await selectConversation(newConv);

    return newConv;
  }

  /**
   * 删除对话
   */
  async function removeConversation(id: number) {
    const { deleteConversation } = await getSqliteApi();
    await deleteConversation(id);

    const idx = conversations.value.findIndex((c) => c.id === id);
    if (idx !== -1) conversations.value.splice(idx, 1);

    // 如果删除的是当前对话，切换到第一个
    if (currentConversation.value?.id === id) {
      if (conversations.value.length > 0) {
        await selectConversation(conversations.value[0]);
      } else {
        currentConversation.value = undefined;
        messages.value = [];
      }
    }
  }

  /**
   * 重命名对话
   */
  async function renameConversation(id: number, newTitle: string) {
    const conv = conversations.value.find((c) => c.id === id);
    if (!conv) return;

    const { updateConversation } = await getSqliteApi();
    await updateConversation(id, newTitle, conv.messages || '', conv.prompt || '');
    conv.title = newTitle;
  }

  /**
   * 置空当前对话
   */
  function clearCurrentConversation() {
    currentConversation.value = undefined;
    messages.value = [];
    conversationPrompt.value = '';
  }

  // ==================== 独立对话管理 ====================

  /**
   * 加载所有独立对话（不关联文档的对话）
   */
  async function loadChatConversations() {
    // 防止重复加载
    if (isLoadingChatConversations.value || chatConversationsLoaded.value) {
      return;
    }

    isLoadingChatConversations.value = true;

    try {
      const { listChatConversations } = await getSqliteApi();

      // 确保 listChatConversations 函数存在
      if (typeof listChatConversations !== 'function') {
        console.warn('⚠️ [LLM Store] listChatConversations函数不可用，初始化为空数组');
        chatConversations.value = [];
        chatConversationsLoaded.value = true;
        return;
      }

      const result = await listChatConversations();
      chatConversations.value = Array.isArray(result) ? result : [];
      chatConversationsLoaded.value = true;
    } catch (error) {
      console.error('❌ [LLM Store] 加载独立对话失败:', error);
      chatConversations.value = [];
      chatConversationsLoaded.value = true;
    } finally {
      isLoadingChatConversations.value = false;
    }
  }

  /**
   * 创建新的独立对话
   */
  async function createNewChatConversation(title: string) {
    try {
      const { createConversation, getConversation } = await getSqliteApi();
      // document_id 传入 -1 表示不关联任何文档
      const newId = await createConversation(-1, title, JSON.stringify([]), '');
      const newConv = await getConversation(newId);

      // 添加到独立对话列表
      chatConversations.value.unshift(newConv);

      // 选择新创建的对话
      await selectConversation(newConv);

      return newConv;
    } catch (error) {
      console.error('❌ [LLM Store] 创建独立对话失败:', error);
      throw error;
    }
  }

  /**
   * 强制重新加载独立对话列表
   */
  async function reloadChatConversations() {
    chatConversationsLoaded.value = false;
    isLoadingChatConversations.value = false;
    await loadChatConversations();
  }

  /**
   * 选择独立对话
   */
  async function selectChatConversation(conversation: Conversation) {
    // 清除当前文档，因为这是独立对话
    currentDocument.value = undefined;
    relatedDocuments.value = [];

    await selectConversation(conversation);
  }

  /**
   * 中断当前对话
   */
  function abortConversation() {
    if (abortController.value) {
      isAborting.value = true;
      abortController.value.abort();
    } else {
      console.log('⚠️ [LLM Store] 没有可中断的对话');
    }
  }

  /**
   * 清理中断控制器
   */
  function cleanupAbortController() {
    if (abortController.value) {
      abortController.value = null;
    }
    isAborting.value = false;
  }

  /**
   * 发送消息
   */
  /**
   * 发送消息 - 统一的消息发送流程
   */
  const waittingLlm = ref(false);
  async function send() {
    if (!canSendMessage.value) {
      return;
    }

    const messageText = input.value.trim();

    // 创建新的中断控制器
    abortController.value = new AbortController();
    isAborting.value = false;

    // 1. 确保有对话上下文
    if (!currentConversation.value) {
      const conversationTitle =
        messageText.length > 20 ? messageText.substring(0, 20) + '...' : messageText;

      if (currentDocument.value) {
        await createNewConversation(conversationTitle);
      } else {
        await createNewChatConversation(conversationTitle);
      }

      if (!currentConversation.value) {
        console.error('❌ [LLM Store] 无法创建对话');
        return;
      }
    }

    // 2. 更新附加内容上下文
    await updateAttachmentsFromContext();

    // 3. 知识库检索现在由AI工具处理，不再自动搜索
    // 清除之前的知识库上下文，让AI决定是否需要搜索
    const { knowledgeService } = await import('src/services/knowledgeService');
    knowledgeService.clearKnowledgeContext();

    // 4. 添加用户消息到对话历史
    messages.value.push({
      role: 'user',
      content: messageText,
    });

    // 5. 清空输入
    input.value = '';
    waittingLlm.value = true;

    // 6. 构建LLM调用参数
    const llmCallParams: LlmCallParams = {
      messages: messages.value,
      conversation: currentConversation.value,
      conversationMode: conversationMode.value,
      conversationRole: 'inkcop', // 默认角色，可以根据需要调整
      enabledTools: enabledTools.value,
      relatedDocuments: relatedDocuments.value,
      currentDocument: currentDocument.value,
      abortController: abortController.value,
      provider: uiStore.perferences.provider, // 添加当前供应商信息
    };

    // 7. 预处理消息和上下文
    const modelSupportsTools = isCurrentModelToolCapable();
    // console.log(`🔧 [LLM Store] 当前模型支持工具调用: ${modelSupportsTools}`);
    const simplifiedParams = await messagePreprocessor.getSimplifiedParams(
      llmCallParams,
      messages,
      modelSupportsTools,
    );

    // 8. 发送消息到API
    try {
      await llmSendMessage(simplifiedParams, loading);

      // 9. 保存对话到数据库
      await saveConversation();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // 移除最后一条可能不完整的助手消息
        if (
          messages.value.length > 0 &&
          messages.value[messages.value.length - 1].role === 'assistant'
        ) {
          const lastMessage = messages.value[messages.value.length - 1];
          if (
            !lastMessage.content ||
            (typeof lastMessage.content === 'string' && lastMessage.content.trim() === '') ||
            (Array.isArray(lastMessage.content) && lastMessage.content.length === 0)
          ) {
            messages.value.pop();
          } else {
            // 如果有部分内容，添加中断标记
            if (typeof lastMessage.content === 'string') {
              lastMessage.content += '\n\n*[您已中断了当前回复]*';
            } else if (Array.isArray(lastMessage.content)) {
              lastMessage.content.push({
                type: 'text',
                text: '\n\n*[您已中断了当前回复]*',
              });
            }
          }
        }

        // 添加用户中断的系统消息
        messages.value.push({
          role: 'assistant',
          content: '*您已中断了当前回复*',
        });
      } else {
        console.error('❌ [LLM Store] 消息发送失败:', error);
        throw error;
      }
    } finally {
      waittingLlm.value = false;
      // 清理中断控制器
      cleanupAbortController();
    }
  }

  /**
   * 保存对话到数据库
   */
  async function saveConversation() {
    if (!currentConversation.value) return;

    try {
      // 保存到SQLite
      const { updateConversation } = await getSqliteApi();
      await updateConversation(
        currentConversation.value.id,
        currentConversation.value.title,
        JSON.stringify(messages.value),
        currentConversation.value.prompt || '',
      );
    } catch (error) {
      console.error('❌ [LLM Store] 保存对话失败:', error);
    }
  }

  // 移除搜索历史对话功能

  /**
   * 添加相关文档
   */
  function addRelatedDocument(doc: Document) {
    // 检查是否已经存在
    if (relatedDocuments.value.some((d) => d.id === doc.id)) {
      return false;
    }

    relatedDocuments.value.push(doc);
    return true;
  }

  /**
   * 移除相关文档
   */
  function removeRelatedDocument(id: number) {
    // 从关联文档列表中移除
    const idx = relatedDocuments.value.findIndex((d) => d.id === id);
    if (idx !== -1) relatedDocuments.value.splice(idx, 1);

    // 从用户添加的关联文档列表中移除
    const idx2 = relatedDocumentsAddByUser.value.findIndex((d) => d.id === id);
    if (idx2 !== -1) relatedDocumentsAddByUser.value.splice(idx2, 1);

    // 同时从附加内容中移除对应的文档附加内容
    const attachmentIdx = attachmentState.value.attachments.findIndex(
      (a) => a.type === 'document' && (a as DocumentAttachment).documentId === id,
    );
    if (attachmentIdx !== -1) {
      attachmentState.value.attachments.splice(attachmentIdx, 1);
    }
  }

  // ==================== 附加内容管理方法 ====================

  /**
   * 生成附加内容ID
   */
  function generateAttachmentId(): string {
    return `attachment_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 添加文档附加内容
   */
  function addDocumentAttachment(doc: Document): boolean {
    // 检查是否已经存在
    if (
      attachmentState.value.attachments.some(
        (a) => a.type === 'document' && (a as DocumentAttachment).documentId === doc.id,
      )
    ) {
      return false;
    }

    const attachment: DocumentAttachment = {
      id: generateAttachmentId(),
      type: 'document',
      name: doc.title,
      createdAt: Date.now(),
      documentId: doc.id,
      title: doc.title,
      content: doc.content,
      textContent: doc.content ? extractTextFromContent(doc.content) : undefined,
    };

    attachmentState.value.attachments.push(attachment);
    return true;
  }

  /**
   * 添加图片附加内容
   */
  function addImageAttachment(
    src: string,
    fileName: string,
    options?: Partial<ImageAttachment>,
  ): string {
    const attachment: ImageAttachment = {
      id: generateAttachmentId(),
      type: 'image',
      name: fileName,
      createdAt: Date.now(),
      src,
      fileName,
      ...options,
    };

    attachmentState.value.attachments.push(attachment);
    return attachment.id;
  }

  /**
   * 添加文件附加内容
   */
  function addFileAttachment(
    filePath: string,
    fileName: string,
    options?: Partial<FileAttachment>,
  ): string {
    const attachment: FileAttachment = {
      id: generateAttachmentId(),
      type: 'file',
      name: fileName,
      createdAt: Date.now(),
      filePath,
      fileName,
      ...options,
    };

    attachmentState.value.attachments.push(attachment);
    return attachment.id;
  }

  /**
   * 添加文本片段附加内容
   */
  function addTextSnippetAttachment(
    content: string,
    source?: TextSnippetAttachment['source'],
  ): string {
    const attachment: TextSnippetAttachment = {
      id: generateAttachmentId(),
      type: 'text_snippet',
      name: `片段 (${content.substring(0, 22)}${content.length > 22 ? '...' : ''})`,
      createdAt: Date.now(),
      content,
      source,
    };

    attachmentState.value.attachments.push(attachment);
    return attachment.id;
  }

  /**
   * 移除附加内容
   */
  function removeAttachment(id: string): boolean {
    const idx = attachmentState.value.attachments.findIndex((a) => a.id === id);
    if (idx !== -1) {
      const removed = attachmentState.value.attachments.splice(idx, 1)[0];

      // 如果移除的是文档类型的附加内容，同时从关联文档中移除
      if (removed && removed.type === 'document') {
        const docAttachment = removed as DocumentAttachment;
        const relatedIdx = relatedDocuments.value.findIndex(
          (d) => d.id === docAttachment.documentId,
        );
        if (relatedIdx !== -1) {
          relatedDocuments.value.splice(relatedIdx, 1);
        }

        // 同时从用户添加的关联文档列表中移除
        const userAddedIdx = relatedDocumentsAddByUser.value.findIndex(
          (d) => d.id === docAttachment.documentId,
        );
        if (userAddedIdx !== -1) {
          relatedDocumentsAddByUser.value.splice(userAddedIdx, 1);
        }
      }

      return !!removed;
    }
    return false;
  }

  /**
   * 清空所有附加内容
   */
  function clearAllAttachments(): void {
    attachmentState.value.attachments = [];
  }

  /**
   * 获取指定类型的附加内容
   */
  function getAttachmentsByType<T extends Attachment>(type: T['type']): T[] {
    return attachmentState.value.attachments.filter((a) => a.type === type) as T[];
  }

  /**
   * 从文档内容中提取纯文本
   */
  function extractTextFromContent(content: string): string {
    try {
      const parsed = JSON.parse(content);
      // 这里可以实现更复杂的文本提取逻辑
      // 暂时返回简化的文本提取
      return JSON.stringify(parsed);
    } catch {
      return content;
    }
  }

  /**
   * 自动管理附加内容
   * 根据当前聚焦文档和关联文档自动设置附加内容
   */
  async function updateAttachmentsFromContext(): Promise<void> {
    // 清空现有的文档类型附加内容
    attachmentState.value.attachments = attachmentState.value.attachments.filter(
      (a) => a.type !== 'document',
    );
    // 1. 如果有聚焦文档，添加为附加内容
    const { useDocStore } = await import('src/stores/doc');
    const docStore = useDocStore();
    const focusedDocument = docStore.getFocusedDocument();
    if (focusedDocument) {
      addDocumentAttachment(focusedDocument);
    }

    // 2. 添加所有关联文档为附加内容
    for (const doc of relatedDocuments.value) {
      // 避免重复添加聚焦文档
      if (!focusedDocument || doc.id !== focusedDocument.id) {
        addDocumentAttachment(doc);
      }
    }
  }

  // ==================== 选中文本管理方法 ====================

  /**
   * 设置当前选中的文本
   */
  function setSelectedText(
    content: string,
    documentId?: number,
    position?: { from: number; to: number },
  ): void {
    if (!content.trim()) {
      clearSelectedText();
      return;
    }

    currentSelectedText.value = {
      content: content.trim(),
      documentId,
      position,
    };
  }

  /**
   * 清除当前选中的文本
   */
  function clearSelectedText(): void {
    if (currentSelectedText.value) {
      currentSelectedText.value = null;
    }
  }

  /**
   * 获取选中文本的提示词描述
   */
  function getSelectedTextPromptDescription(): string {
    if (!currentSelectedText.value) {
      return '';
    }

    const { content } = currentSelectedText.value;
    return `用户当前选中的文本是：\n${content}`;
  }

  /**
   * 获取附加内容的提示词描述
   */
  function getAttachmentsPromptDescription(): string {
    if (attachmentState.value.attachments.length === 0) {
      return '';
    }

    const descriptions: string[] = [];

    // 按类型分组描述
    const documentAttachments = getAttachmentsByType<DocumentAttachment>('document');
    const imageAttachments = getAttachmentsByType<ImageAttachment>('image');
    const fileAttachments = getAttachmentsByType<FileAttachment>('file');
    const textSnippetAttachments = getAttachmentsByType<TextSnippetAttachment>('text_snippet');

    if (documentAttachments.length > 0) {
      descriptions.push(`文档附件 (${documentAttachments.length}个):`);
      documentAttachments.forEach((doc, index) => {
        descriptions.push(`${index + 1}. 文档: "${doc.title}" (ID: ${doc.documentId})`);
        if (doc.textContent) {
          const preview = doc.textContent.substring(0, 200);
          descriptions.push(`   内容预览: ${preview}${doc.textContent.length > 200 ? '...' : ''}`);
        }
      });
    }

    if (imageAttachments.length > 0) {
      descriptions.push(`图片附件 (${imageAttachments.length}个):`);
      imageAttachments.forEach((img, index) => {
        descriptions.push(`${index + 1}. 图片: "${img.fileName}"`);
        if (img.dimensions) {
          descriptions.push(`   尺寸: ${img.dimensions.width}x${img.dimensions.height}`);
        }
      });
    }

    if (fileAttachments.length > 0) {
      descriptions.push(`文件附件 (${fileAttachments.length}个):`);
      fileAttachments.forEach((file, index) => {
        descriptions.push(`${index + 1}. 文件: "${file.fileName}"`);
        if (file.extension) {
          descriptions.push(`   类型: ${file.extension}`);
        }
        if (file.content) {
          const preview = file.content.substring(0, 200);
          descriptions.push(`   内容预览: ${preview}${file.content.length > 200 ? '...' : ''}`);
        }
      });
    }

    if (textSnippetAttachments.length > 0) {
      descriptions.push(`片段 (${textSnippetAttachments.length}个):`);
      textSnippetAttachments.forEach((snippet, index) => {
        descriptions.push(`片段${index + 1}:${snippet.content}`);
        if (snippet.source?.type) {
          descriptions.push(`   来源: ${snippet.source.type}`);
        }
      });
    }

    return descriptions.join('\n');
  }

  /**
   * 调整字体大小
   */
  function adjustFontSize(delta: number) {
    fontSize.value = Math.max(0.5, Math.min(3, Number(fontSize.value) + delta));
    localStorage.setItem('fontSize', fontSize.value.toString());
  }

  /**
   * 重置状态
   */
  function resetState() {
    currentConversation.value = undefined;
    messages.value = [];
    conversationPrompt.value = '';
    input.value = '';
    showKnowledgeResults.value = false;
  }

  /**
   * AI 工具调用此方法来向用户提问
   */
  function askUserQuestion(question: string): Promise<{ success: boolean; answer: string }> {
    return new Promise((resolve, reject) => {
      // 生成问题ID
      const questionId = Date.now().toString() + Math.random().toString(36).substring(2);

      // 设置当前问题
      currentUserQuestion.value = {
        questionId,
        question,
        timestamp: Date.now(),
      };
      userAnswer.value = '';

      // 保存 Promise 的 resolve/reject
      currentQuestionResolver = { resolve, reject, id: questionId };

      // 清理之前的超时定时器
      if (questionTimeoutTimer) {
        clearTimeout(questionTimeoutTimer);
      }

      // 设置超时（5分钟）
      questionTimeoutTimer = setTimeout(
        () => {
          if (currentQuestionResolver?.id === questionId) {
            currentQuestionResolver = null;
            currentUserQuestion.value = null;
            userAnswer.value = '';
            questionTimeoutTimer = null;
            reject(new Error('用户提问超时'));
          }
        },
        5 * 60 * 1000,
      );
    });
  }

  /**
   * 提交用户答案
   */
  function submitUserAnswer() {
    if (!currentUserQuestion.value || !userAnswer.value.trim() || !currentQuestionResolver) {
      console.warn('⚠️ [LLM Store] 无效的用户答案提交');
      return false;
    }

    try {
      // 解决 Promise，返回用户答案
      currentQuestionResolver.resolve({
        success: true,
        answer: userAnswer.value.trim(),
      });

      // 清理状态
      currentUserQuestion.value = null;
      userAnswer.value = '';
      currentQuestionResolver = null;

      // 清理超时定时器
      if (questionTimeoutTimer) {
        clearTimeout(questionTimeoutTimer);
        questionTimeoutTimer = null;
      }

      return true;
    } catch (error) {
      console.error('❌ [LLM Store] 提交用户答案时发生错误:', error);
      return false;
    }
  }

  /**
   * 取消用户提问
   */
  function cancelUserQuestion() {
    if (!currentUserQuestion.value || !currentQuestionResolver) {
      console.warn('⚠️ [LLM Store] 没有待取消的用户提问');
      return false;
    }

    try {
      // 拒绝 Promise
      currentQuestionResolver.reject(new Error('用户取消了回答'));

      // 清理状态
      currentUserQuestion.value = null;
      userAnswer.value = '';
      currentQuestionResolver = null;

      // 清理超时定时器
      if (questionTimeoutTimer) {
        clearTimeout(questionTimeoutTimer);
        questionTimeoutTimer = null;
      }

      return true;
    } catch (error) {
      console.error('❌ [LLM Store] 取消用户提问时发生错误:', error);
      return false;
    }
  }

  /**
   * 工具调用相关方法
   */

  /**
   * 判断当前模型是否支持工具调用
   * 根据模型是否存在于TOOLS_CALLS分组来确定
   */
  function isCurrentModelToolCapable(): boolean {
    const currentSettings = uiStore.currentLlmSettings;
    if (!currentSettings || typeof currentSettings === 'string') {
      return false;
    }

    // Handle AutoComplete type which has model in body.model
    let modelName: string;
    if ('body' in currentSettings && (currentSettings as { body: { model: string } }).body?.model) {
      modelName = (currentSettings as { body: { model: string } }).body.model;
    } else if ('model' in currentSettings && (currentSettings as { model: string }).model) {
      modelName = (currentSettings as { model: string }).model;
    } else {
      return false;
    }

    // 获取当前供应商的设置
    const llmSettings = uiStore.perferences?.llm;
    if (!llmSettings) {
      console.log(`🔧 [LLM Store] 无法获取LLM设置`);
      return false;
    }

    // 遍历所有供应商，查找当前模型是否存在于TOOLS_CALLS分组中
    let isSupported = false;
    let foundInProvider = '';

    Object.entries(llmSettings).forEach(([providerKey, providerSettings]) => {
      if (
        providerSettings &&
        typeof providerSettings === 'object' &&
        'enabled' in providerSettings &&
        'avaliableModels' in providerSettings &&
        providerSettings.enabled &&
        providerSettings.avaliableModels
      ) {
        const categorizedModels = providerSettings.avaliableModels as CategorizedModels;
        const toolsCallsModels = categorizedModels[ModelType.TOOLS_CALLS];

        if (toolsCallsModels && toolsCallsModels.includes(modelName)) {
          isSupported = true;
          foundInProvider = providerKey;
        }
      }
    });

    // 输出调试信息
    if (isSupported) {
      console.log(
        `🔧 [LLM Store] 模型 "${modelName}" 支持工具调用，在供应商 "${foundInProvider}" 的TOOLS_CALLS分组中找到`,
      );
    } else {
      console.log(
        `🔧 [LLM Store] 模型 "${modelName}" 不支持工具调用，未在任何供应商的TOOLS_CALLS分组中找到`,
      );
    }

    return isSupported;
  }

  /**
   * 判断模型名称是否为 Qwen 系列，用于决定是否传递 parallel_tool_calls 参数
   * 使用关键字匹配：检查是否包含 qwen 相关关键字
   */
  function isQwenModel(): boolean {
    const currentSettings = uiStore.currentLlmSettings;
    if (!currentSettings || typeof currentSettings === 'string') {
      return false;
    }

    // Handle AutoComplete type which has model in body.model
    let modelName: string;
    if ('body' in currentSettings && (currentSettings as { body: { model: string } }).body?.model) {
      modelName = (currentSettings as { body: { model: string } }).body.model.toLowerCase();
    } else if ('model' in currentSettings && (currentSettings as { model: string }).model) {
      modelName = (currentSettings as { model: string }).model.toLowerCase();
    } else {
      return false;
    }

    // Qwen 系列关键字
    const qwenKeywords = ['qwen', 'qwen2', 'qwen2.5', 'qwen3', 'codeqwen'];

    const isQwen = qwenKeywords.some((keyword) => modelName.includes(keyword.toLowerCase()));

    if (isQwen) {
      console.log(`🔧 [LLM Store] 检测到 Qwen 模型: ${modelName}，将启用 parallel_tool_calls`);
    }

    return isQwen;
  }

  /**
   * 类型守卫
   */
  function isAssistantMessage(message: Message): message is AssistantMessageType {
    return message.role === 'assistant';
  }

  function isToolMessage(message: Message): message is ToolMessageType {
    return message.role === 'tool';
  }

  /**
   * 清理所有定时器和资源
   */
  function cleanup() {
    // 清理用户提问超时定时器
    if (questionTimeoutTimer) {
      clearTimeout(questionTimeoutTimer);
      questionTimeoutTimer = null;
    }

    // 清理用户提问状态
    if (currentQuestionResolver) {
      currentQuestionResolver.reject(new Error('Store 已销毁'));
      currentQuestionResolver = null;
    }

    currentUserQuestion.value = null;
    userAnswer.value = '';

    // 清理中断控制器
    cleanupAbortController();
  }

  return {
    // 状态
    loading,
    waittingLlm,
    input,
    fontSize,
    conversations,
    chatConversations,
    currentConversation,
    messages,
    conversationPrompt,
    currentDocument,
    relatedDocuments,
    relatedDocumentsAddByUser,
    needSettings,
    // 用户提问状态
    currentUserQuestion,
    userAnswer,

    // 对话模式相关
    conversationMode,
    enabledTools,

    // 计算属性
    hasConversations,
    hasMessages,
    canSendMessage,

    // 方法
    initSettings,
    setCurrentDocument,
    selectConversation,
    createNewConversation,
    removeConversation,
    renameConversation,
    clearCurrentConversation,
    // 独立对话管理
    loadChatConversations,
    createNewChatConversation,
    selectChatConversation,
    send,
    saveConversation,
    // 移除 searchHistory 方法
    addRelatedDocument,
    removeRelatedDocument,
    adjustFontSize,
    resetState,
    // 用户提问方法
    askUserQuestion,
    submitUserAnswer,
    cancelUserQuestion,
    isAssistantMessage,
    isToolMessage,

    // 工具调用相关方法
    isCurrentModelToolCapable,
    isQwenModel,

    // 对话模式方法
    toggleConversationMode,
    getEnabledToolsForMode,

    // 知识库相关状态
    knowledgeServiceStatus,
    knowledgeBases,
    selectedKnowledgeBase,
    knowledgeSearchResults,
    showKnowledgeResults,
    knowledgeBaseOptions,

    // 知识库方法
    loadKnowledgeBasesList,
    refreshKnowledgeBasesList,
    selectKnowledgeBase,
    searchKnowledgeContent,

    // 数据加载状态
    isLoadingChatConversations,
    chatConversationsLoaded,
    reloadChatConversations,

    // 对话中断功能
    abortController,
    isAborting,
    abortConversation,
    cleanupAbortController,

    // 附加内容状态和方法
    attachmentState,
    addDocumentAttachment,
    addImageAttachment,
    addFileAttachment,
    addTextSnippetAttachment,
    removeAttachment,
    clearAllAttachments,
    getAttachmentsByType,
    updateAttachmentsFromContext,
    getAttachmentsPromptDescription,

    // 选中文本状态和方法
    currentSelectedText,
    setSelectedText,
    clearSelectedText,
    getSelectedTextPromptDescription,

    // 清理方法
    cleanup,
  };
});
