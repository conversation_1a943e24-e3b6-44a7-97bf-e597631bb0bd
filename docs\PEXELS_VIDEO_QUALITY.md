# Pexels 视频多分辨率播放

## 功能概述

Pexels 媒体浏览器现在支持多分辨率视频播放，用户可以在播放过程中切换不同的视频质量。

## 实现细节

### 视频质量处理

根据 Pexels API 返回的 `video_files` 数组，我们支持以下视频质量：

```javascript
// 质量等级映射
const qualityOrder = { 
  'uhd': 3,  // Ultra HD (4K)
  'hd': 2,   // High Definition (1080p/720p)
  'sd': 1    // Standard Definition (480p/360p)
};
```

### 多分辨率源构建

```javascript
const videoSources = video.video_files
  .filter(file => file.file_type === 'video/mp4') // 只使用 MP4 格式
  .sort((a, b) => {
    // 按质量排序：uhd > hd > sd
    const qualityOrder = { 'uhd': 3, 'hd': 2, 'sd': 1 };
    return (qualityOrder[b.quality] || 0) - (qualityOrder[a.quality] || 0);
  })
  .map(file => ({
    html: getQualityLabel(file.quality, file.width, file.height),
    url: file.link,
    type: 'mp4'
  }));
```

### 质量标签生成

```javascript
const getQualityLabel = (quality: string, width: number, height: number): string => {
  const qualityMap = {
    'uhd': 'UHD',
    'hd': 'HD', 
    'sd': 'SD'
  };
  
  const qualityName = qualityMap[quality] || quality.toUpperCase();
  return `${qualityName} (${width}×${height})`;
};
```

## 示例数据结构

### Pexels API 返回的视频数据

```json
{
  "video_files": [
    {
      "id": 9276938,
      "quality": "sd",
      "file_type": "video/mp4",
      "width": 640,
      "height": 338,
      "fps": 23.976,
      "link": "https://videos.pexels.com/.../sd_640_338_24fps.mp4",
      "size": 1305754
    },
    {
      "id": 9277073,
      "quality": "hd",
      "file_type": "video/mp4", 
      "width": 1366,
      "height": 720,
      "fps": 23.976,
      "link": "https://videos.pexels.com/.../hd_1366_720_24fps.mp4",
      "size": 4882454
    },
    {
      "id": 9277348,
      "quality": "uhd",
      "file_type": "video/mp4",
      "width": 4096,
      "height": 2160,
      "fps": 23.976,
      "link": "https://videos.pexels.com/.../uhd_4096_2160_24fps.mp4",
      "size": 40873071
    }
  ]
}
```

### 转换后的 Artplayer 质量源

```javascript
[
  {
    html: "UHD (4096×2160)",
    url: "https://videos.pexels.com/.../uhd_4096_2160_24fps.mp4",
    type: "mp4"
  },
  {
    html: "HD (1366×720)", 
    url: "https://videos.pexels.com/.../hd_1366_720_24fps.mp4",
    type: "mp4"
  },
  {
    html: "SD (640×338)",
    url: "https://videos.pexels.com/.../sd_640_338_24fps.mp4", 
    type: "mp4"
  }
]
```

## Artplayer 配置

### 基本配置

```javascript
artplayerInstance = new Artplayer({
  container: videoPlayerContainer.value,
  url: defaultVideo.link,           // 默认视频源
  poster: video.image,              // 视频封面
  quality: videoSources,            // 多分辨率源
  volume: 0.7,
  autoplay: false,
  pip: true,                        // 画中画
  setting: true,                    // 设置菜单
  playbackRate: true,               // 播放速度
  aspectRatio: true,                // 宽高比
  fullscreen: true,                 // 全屏
  fullscreenWeb: true,              // 网页全屏
  screenshot: true,                 // 截图
  miniProgressBar: true,            // 迷你进度条
  mutex: true,                      // 互斥播放
  backdrop: true,                   // 背景遮罩
  playsInline: true,                // 内联播放
});
```

### 默认质量选择逻辑

```javascript
// 优先级：HD > SD > 第一个可用的
const defaultVideo = 
  video.video_files.find(file => file.quality === 'hd') || 
  video.video_files.find(file => file.quality === 'sd') ||
  video.video_files[0];
```

## 用户体验

### 质量切换

1. **自动选择**：播放器启动时自动选择 HD 质量（如果可用）
2. **手动切换**：用户可以通过设置菜单切换质量
3. **智能降级**：如果 HD 不可用，自动选择 SD

### 质量标签

- **UHD (4096×2160)**：4K 超高清
- **HD (1920×1080)**：1080p 高清
- **HD (1366×720)**：720p 高清
- **SD (640×338)**：标清

### 性能优化

1. **格式过滤**：只使用 MP4 格式，确保兼容性
2. **质量排序**：按质量从高到低排序
3. **智能默认**：选择合适的默认质量

## 事件监听

### 播放器事件

```javascript
// 播放器准备就绪
artplayerInstance.on('ready', () => {
  console.log('🎥 [Artplayer] 播放器准备就绪');
});

// 播放器错误
artplayerInstance.on('error', (error) => {
  console.error('🎥 [Artplayer] 播放器错误:', error);
});

// 质量切换
artplayerInstance.on('quality', (quality) => {
  console.log('🎥 [Quality] 切换到质量:', quality);
});
```

## 调试信息

### 控制台日志

播放视频时会输出以下调试信息：

```
🎥 [Preview Video] 开始预览视频: {video object}
🎥 [Video Sources] 多分辨率源: [{quality sources}]
🎥 [Default Video] 默认视频源: {default video}
🎥 [Artplayer] 播放器准备就绪
```

### 数据验证

可以在控制台中检查视频源数据：

```javascript
// 检查原始视频文件
console.log('Video files:', video.video_files);

// 检查处理后的质量源
console.log('Quality sources:', videoSources);

// 检查默认视频
console.log('Default video:', defaultVideo);
```

## 故障排除

### 常见问题

1. **没有质量选项**
   - 检查 `video_files` 是否包含多个质量
   - 确认 MP4 格式过滤是否正确

2. **播放失败**
   - 检查视频 URL 是否有效
   - 确认网络连接状态
   - 查看浏览器控制台错误

3. **质量切换不工作**
   - 确认 Artplayer 版本支持质量切换
   - 检查质量源格式是否正确

### 调试技巧

1. **启用详细日志**：
   ```javascript
   // 在 previewVideo 函数中添加更多日志
   console.log('All video files:', video.video_files);
   console.log('Filtered MP4 files:', video.video_files.filter(f => f.file_type === 'video/mp4'));
   ```

2. **检查质量源**：
   ```javascript
   // 验证质量源格式
   videoSources.forEach((source, index) => {
     console.log(`Quality ${index}:`, source);
   });
   ```

## 扩展功能

### 可能的改进

1. **自适应质量**：根据网络状况自动选择质量
2. **质量预设**：用户可以设置默认质量偏好
3. **带宽检测**：根据带宽自动调整质量
4. **质量统计**：显示当前质量的详细信息

### 自定义配置

```javascript
// 可配置的质量选项
const qualityConfig = {
  defaultQuality: 'hd',           // 默认质量
  enableAutoQuality: true,        // 启用自动质量
  qualityLabels: {                // 自定义质量标签
    'uhd': '4K Ultra HD',
    'hd': 'Full HD',
    'sd': 'Standard'
  }
};
```

## 兼容性

- ✅ Chrome 88+ (完整支持)
- ✅ Firefox 85+ (完整支持)
- ✅ Safari 14+ (部分支持，可能不支持某些格式)
- ✅ Edge 88+ (完整支持)

## 相关文档

- [Artplayer 质量切换文档](https://artplayer.org/document/quality.html)
- [Pexels Video API](https://www.pexels.com/api/documentation/#videos-search)
- [HTML5 Video 格式支持](https://developer.mozilla.org/en-US/docs/Web/Media/Formats/Video_codecs)
