import { computed, type Ref } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { useSqlite } from 'src/composeables/useSqlite';
import { DEFAULT_MINIMAX_SETTINGS } from 'src/config/defaultSettings';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import type { AssistantMessage } from 'src/types/qwen';
import { executeToolCall } from 'src/llm/tools/index';
import { $t } from 'src/composables/useTrans';

/**
 * MiniMax API 处理逻辑
 * 基于 MiniMax API 文档实现
 * @see https://platform.minimaxi.com/document/对话
 */
export const useMiniMax = () => {
  // 在函数内部获取组合式函数
  const { updateConversation } = useSqlite();
  const uiStore = useUiStore();

  // 使用 computed 来创建响应式的 miniMaxSettings
  const miniMaxSettings = computed(() => {
    return uiStore.perferences?.llm?.minimax || DEFAULT_MINIMAX_SETTINGS;
  });

  // readSettings 方法现在不再需要，因为设置是响应式的
  // 保留此方法以保持向后兼容性，但实际上不执行任何操作
  const readSettings = () => {
    // 设置现在通过 computed 自动同步，无需手动读取
    console.log('[useMiniMax] 设置已自动同步:', miniMaxSettings.value);
  };

  /**
   * 发送消息到 MiniMax API
   */
  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>) => {
    const { messages, messagesRef, conversation, tools, enableTools, abortController } = params;

    try {
      loading.value = true;
      console.log('[useMiniMax] 开始发送消息');
      console.log('[useMiniMax] 消息数量:', messages.length);
      console.log('[useMiniMax] 启用工具:', enableTools);
      console.log('[useMiniMax] 工具数量:', tools?.length || 0);

      // 用于累积工具调用数据的临时存储
      const toolCallsBuffer = new Map<
        string,
        {
          id: string;
          type: string;
          function: {
            name: string;
            arguments: string;
          };
        }
      >();

      // 构建请求体
      const requestBody: {
        model: string;
        messages: typeof messages;
        stream: boolean;
        temperature?: number;
        max_tokens: number;
        top_p?: number;
        tools?: typeof tools;
        tool_choice?: string;
      } = {
        model: miniMaxSettings.value.model,
        messages: [...messages],
        stream: miniMaxSettings.value.stream,
        max_tokens: miniMaxSettings.value.maxTokens,
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (
        miniMaxSettings.value.temperature !== undefined &&
        !isNaN(miniMaxSettings.value.temperature)
      ) {
        requestBody.temperature = miniMaxSettings.value.temperature;
      }
      if (miniMaxSettings.value.topP !== undefined && !isNaN(miniMaxSettings.value.topP)) {
        requestBody.top_p = miniMaxSettings.value.topP;
      }

      // 如果有启用的工具，添加工具定义
      if (enableTools && tools.length > 0) {
        // console.log(`🔧 [useMiniMax] 启用工具数: ${tools.length}`);
        // console.log(`🔧 [useMiniMax] 工具列表: ${tools.map((t) => t.function.name).join(', ')}`);

        requestBody.tools = tools;
        requestBody.tool_choice = 'auto';
      }

      // console.log('[useMiniMax] 请求体:', requestBody);

      // 发送请求
      const response = await fetch(miniMaxSettings.value.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${miniMaxSettings.value.apiKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        // console.error('[useMiniMax] API 请求失败:', response.status, errorText);
        throw new Error(`MiniMax API 错误: ${response.status} ${errorText}`);
      }

      // 添加助手消息
      const lastMessage: AssistantMessage = {
        role: 'assistant',
        content: '',
        reasoning_content: '',
      };
      messagesRef.value.push(lastMessage);

      if (!response.body) {
        throw new Error('响应体为空');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let miniMaxContentBuffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') continue;

              try {
                const parsed = typeof data === 'object' ? data : JSON.parse(data);
                // console.log('[useMiniMax] parsed:', parsed);
                const delta = parsed.choices?.[0]?.delta;
                const finishReason = parsed.choices?.[0]?.finish_reason;

                // 处理内容更新
                if (delta?.content) {
                  miniMaxContentBuffer += delta.content;
                  lastMessage.content = miniMaxContentBuffer;
                }

                // 处理思考内容更新 - MiniMax 直接在 delta 中提供 reasoning_content
                if (delta?.reasoning_content) {
                  if (!lastMessage.reasoning_content) {
                    lastMessage.reasoning_content = '';
                  }
                  lastMessage.reasoning_content += delta.reasoning_content;
                }

                // 处理工具调用 - 累积模式
                if (delta?.tool_calls) {
                  console.log('[useMiniMax] 检测到工具调用数据:', delta.tool_calls);

                  delta.tool_calls.forEach(
                    (
                      toolCall: {
                        id?: string;
                        type?: string;
                        function?: {
                          name?: string;
                          arguments?: string;
                        };
                      },
                      index: number,
                    ) => {
                      // 如果有 ID，说明是第一条消息，创建新的工具调用对象
                      if (toolCall.id && toolCall.id.trim() !== '') {
                        console.log(`[useMiniMax] 创建新的工具调用对象，ID: ${toolCall.id}`);
                        toolCallsBuffer.set(toolCall.id, {
                          id: toolCall.id,
                          type: toolCall.type || 'function',
                          function: {
                            name: toolCall.function?.name || '',
                            arguments: toolCall.function?.arguments || '',
                          },
                        });
                      } else {
                        // ID 为空，说明是后续消息，需要累加到现有的工具调用对象中
                        // 由于可能有多个工具调用，我们需要根据索引来确定累加到哪个工具调用
                        const existingEntries = Array.from(toolCallsBuffer.entries());
                        if (existingEntries.length > index) {
                          const [toolCallId, bufferedToolCall] = existingEntries[index];

                          // 累积工具名称
                          if (toolCall.function?.name) {
                            bufferedToolCall.function.name += toolCall.function.name;
                          }

                          // 累积工具参数
                          if (toolCall.function?.arguments) {
                            bufferedToolCall.function.arguments += toolCall.function.arguments;
                          }

                          console.log(
                            `[useMiniMax] 工具调用累积中 ${toolCallId}:`,
                            bufferedToolCall,
                          );
                        }
                      }
                    },
                  );
                }

                // 当 finish_reason 为 "tool_calls" 时，完成工具调用累积
                if (finishReason === 'tool_calls' && toolCallsBuffer.size > 0) {
                  console.log('[useMiniMax] 工具调用完成，开始处理完整的工具调用数据');

                  // 将累积的工具调用数据转换为最终格式
                  lastMessage.tool_calls = Array.from(toolCallsBuffer.values()).map(
                    (toolCall, index) => ({
                      id: toolCall.id,
                      type: 'function' as const,
                      index: index,
                      function: {
                        name: toolCall.function.name,
                        arguments: toolCall.function.arguments,
                      },
                    }),
                  );

                  console.log('[useMiniMax] 最终工具调用数据:', lastMessage.tool_calls);

                  // 清空缓冲区
                  toolCallsBuffer.clear();
                } else if (finishReason === 'tool_calls' && toolCallsBuffer.size === 0) {
                  console.log('[useMiniMax] 收到重复的 finish_reason: tool_calls，忽略');
                }
              } catch (parseError) {
                console.warn('[useMiniMax] 解析响应失败:', parseError, 'data:', data);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      // 最终处理完整内容
      if (miniMaxContentBuffer) {
        lastMessage.content = miniMaxContentBuffer;
        // reasoning_content 已经在流式处理中直接设置，无需额外处理
      }

      // 处理工具调用
      if (lastMessage.tool_calls && lastMessage.tool_calls.length > 0) {
        // console.log(`[useMiniMax] 检测到 ${lastMessage.tool_calls.length} 个工具调用，开始执行`);

        for (const toolCall of lastMessage.tool_calls) {
          try {
            // console.log(`[useMiniMax] 执行工具: ${toolCall.function.name}`);
            console.log(`[useMiniMax] 工具参数:`, toolCall.function.arguments);

            // 解析工具参数
            let toolArgs: Record<string, unknown>;
            try {
              toolArgs = JSON.parse(toolCall.function.arguments);
            } catch (parseError) {
              console.error('[useMiniMax] 工具参数解析失败:', parseError);
              throw new Error(`工具参数格式错误: ${parseError}`);
            }

            // 执行工具
            const toolResult = await executeToolCall(toolCall.function.name, toolArgs);

            // 添加工具结果消息
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify(toolResult),
              tool_call_id: toolCall.id,
            });

            // console.log(`[useMiniMax] 工具 ${toolCall.function.name} 执行完成:`, toolResult);
          } catch (error) {
            console.error(`[useMiniMax] 工具 ${toolCall.function.name} 执行失败:`, error);

            // 添加错误结果
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify({
                success: false,
                message: $t('src.composeables.useMiniMax.toolExecutionFailed', {
                  error: error.message,
                }),
                toolName: toolCall.function.name,
              }),
              tool_call_id: toolCall.id,
            });
          }
        }

        // 如果有工具调用，需要再次调用模型处理工具结果
        // console.log('[useMiniMax] 工具执行完成，准备递归调用模型处理结果');

        const recursiveParams: SimplifiedLlmParams = {
          ...params,
          messages: messagesRef.value,
        };
        await sendMessage(recursiveParams, loading);
        return;
      }

      // console.log('[useMiniMax] 消息发送完成');

      // 更新对话
      if (conversation) {
        await updateConversation(
          conversation.id,
          conversation.title,
          JSON.stringify(messagesRef.value),
          conversation.prompt,
        );
      }
    } catch (error) {
      console.error('[useMiniMax] 发送消息失败:', error);

      // 移除可能添加的空消息
      if (
        messagesRef.value.length > 0 &&
        messagesRef.value[messagesRef.value.length - 1].content === ''
      ) {
        messagesRef.value.pop();
      }

      throw error;
    } finally {
      loading.value = false;
    }
  };

  return {
    sendMessage,
    readSettings,
    currentSettings: miniMaxSettings,
  };
};
