import { ref, reactive, computed, readonly } from 'vue';
import { useResourceCleanup } from './useResourceCleanup';
import { isDevelopment, PERFORMANCE_MONITOR } from '../config/performance.config';

/**
 * 性能监控器
 * 第二阶段性能优化：全面监控应用性能指标
 *
 * 核心功能：
 * 1. 实时性能监控 - FPS、内存、CPU使用率
 * 2. 用户体验指标 - FCP、LCP、CLS等Web Vitals
 * 3. 自定义指标 - 编辑器响应时间、渲染时间
 * 4. 性能警告 - 自动检测性能问题并发出警告
 * 5. 数据收集 - 收集性能数据用于分析和优化
 */

// 性能指标接口
export interface PerformanceMetrics {
  // 基础性能指标
  fps: number;
  memoryUsage: {
    used: number; // MB
    total: number; // MB
    percentage: number;
  };

  // 渲染性能
  renderMetrics: {
    frameTime: number; // ms
    renderTime: number; // ms
    layoutTime: number; // ms
    paintTime: number; // ms
  };

  // 用户体验指标
  vitals: {
    fcp: number; // First Contentful Paint
    lcp: number; // Largest Contentful Paint
    cls: number; // Cumulative Layout Shift
    fid: number; // First Input Delay
    ttfb: number; // Time to First Byte
  };

  // 自定义应用指标
  application: {
    editorResponseTime: number;
    saveOperationTime: number;
    searchTime: number;
    imageProcessingTime: number;
    documentLoadTime: number;
  };

  // 网络性能
  network: {
    connectionType: string;
    downlink: number; // Mbps
    rtt: number; // ms
    effectiveType: string;
  };

  // 时间戳
  timestamp: number;
}

// 性能警告类型
export interface PerformanceWarning {
  type: 'fps' | 'memory' | 'render' | 'network' | 'custom';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  metric: string;
  value: number;
  threshold: number;
  timestamp: number;
  suggestion?: string;
}

// 性能阈值配置
export interface PerformanceThresholds {
  fps: {
    warning: number;
    critical: number;
  };
  memory: {
    warning: number; // percentage
    critical: number;
  };
  renderTime: {
    warning: number; // ms
    critical: number;
  };
  responseTime: {
    warning: number; // ms
    critical: number;
  };
}

// 监控器配置
export interface PerformanceMonitorConfig {
  enabled: boolean;
  sampleInterval: number; // ms
  maxHistorySize: number;
  enableVitalsTracking: boolean;
  enableNetworkTracking: boolean;
  enableCustomMetrics: boolean;
  autoOptimize: boolean;
  thresholds: PerformanceThresholds;
}

// 默认配置
const DEFAULT_CONFIG: PerformanceMonitorConfig = {
  enabled: true,
  sampleInterval: 1000, // 1秒
  maxHistorySize: 300, // 5分钟历史
  enableVitalsTracking: true,
  enableNetworkTracking: true,
  enableCustomMetrics: true,
  autoOptimize: false,
  thresholds: {
    fps: { warning: 45, critical: 30 },
    memory: { warning: 80, critical: 90 },
    renderTime: { warning: 16, critical: 33 },
    responseTime: { warning: 100, critical: 500 },
  },
};

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private config: PerformanceMonitorConfig;
  private metrics = reactive<PerformanceMetrics>(this.createEmptyMetrics());
  private history = ref<PerformanceMetrics[]>([]);
  private warnings = ref<PerformanceWarning[]>([]);
  private isRunning = ref(false);
  private resourceCleanup: ReturnType<typeof useResourceCleanup>;

  // 监控定时器和观察器
  private sampleTimer: NodeJS.Timeout | null = null;
  private frameCount = 0;
  private lastFrameTime = 0;
  private rafId: number | null = null;
  private performanceObserver: PerformanceObserver | null = null;
  private memoryMonitorTimer: NodeJS.Timeout | null = null;

  // 自定义指标收集器
  private customMetrics = reactive<Map<string, number>>(new Map());
  private operationTimers = reactive<Map<string, number>>(new Map());

  constructor(config: Partial<PerformanceMonitorConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.resourceCleanup = useResourceCleanup();

    // Check global PERFORMANCE_MONITOR.enabled first, then local config
    if (PERFORMANCE_MONITOR.enabled && this.config.enabled) {
      this.initialize();
    }
  }

  /**
   * 创建空的性能指标对象
   */
  private createEmptyMetrics(): PerformanceMetrics {
    return {
      fps: 0,
      memoryUsage: { used: 0, total: 0, percentage: 0 },
      renderMetrics: { frameTime: 0, renderTime: 0, layoutTime: 0, paintTime: 0 },
      vitals: { fcp: 0, lcp: 0, cls: 0, fid: 0, ttfb: 0 },
      application: {
        editorResponseTime: 0,
        saveOperationTime: 0,
        searchTime: 0,
        imageProcessingTime: 0,
        documentLoadTime: 0,
      },
      network: { connectionType: 'unknown', downlink: 0, rtt: 0, effectiveType: 'unknown' },
      timestamp: Date.now(),
    };
  }

  /**
   * 初始化监控器
   */
  private initialize(): void {
    // 注册资源清理
    this.resourceCleanup.addResource('performanceMonitor', {
      cleanup: () => this.destroy(),
      priority: 'medium',
      type: 'monitor',
    });

    // 初始化Web Vitals监控
    if (this.config.enableVitalsTracking) {
      this.initializeVitalsTracking();
    }

    // 初始化网络监控
    if (this.config.enableNetworkTracking) {
      this.initializeNetworkTracking();
    }

    if (isDevelopment) {
      console.log('🔥 [Performance Monitor] Performance monitor initialized');
    }
  }

  /**
   * 启动监控
   */
  start(): void {
    // Check if performance monitoring is globally enabled
    if (!PERFORMANCE_MONITOR.enabled) {
      return;
    }

    if (this.isRunning.value) {
      if (isDevelopment) {
        console.warn('🔥 [Performance Monitor] Performance monitor is already running');
      }
      return;
    }

    this.isRunning.value = true;

    // 启动FPS监控
    this.startFPSMonitoring();

    // 启动内存监控
    this.startMemoryMonitoring();

    // 启动采样定时器
    this.sampleTimer = setInterval(() => {
      this.collectMetrics();
    }, this.config.sampleInterval);

    if (isDevelopment) {
      console.log('🔥 [Performance Monitor] Performance monitor started');
    }
  }

  /**
   * 停止监控
   */
  stop(): void {
    if (!this.isRunning.value) return;

    this.isRunning.value = false;

    // 清理定时器
    if (this.sampleTimer) {
      clearInterval(this.sampleTimer);
      this.sampleTimer = null;
    }

    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }

    if (this.memoryMonitorTimer) {
      clearInterval(this.memoryMonitorTimer);
      this.memoryMonitorTimer = null;
    }

    if (isDevelopment) {
      console.log('🔥 [Performance Monitor] Performance monitor stopped');
    }
  }

  /**
   * 启动FPS监控
   */
  private startFPSMonitoring(): void {
    let frameCount = 0;
    let lastTime = performance.now();

    const countFrames = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        this.metrics.fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        frameCount = 0;
        lastTime = currentTime;

        // 检查FPS警告
        this.checkFPSThresholds();
      }

      this.rafId = requestAnimationFrame(countFrames);
    };

    countFrames();
  }

  /**
   * 启动内存监控
   */
  private startMemoryMonitoring(): void {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (
          performance as { memory?: { usedJSHeapSize: number; totalJSHeapSize: number } }
        ).memory;
        if (memory) {
          const used = memory.usedJSHeapSize / 1048576; // 转换为MB
          const total = memory.totalJSHeapSize / 1048576;
          const percentage = (used / total) * 100;

          this.metrics.memoryUsage = { used, total, percentage };

          // 检查内存警告
          this.checkMemoryThresholds();
        }
      }
    };

    updateMemoryInfo();
    this.memoryMonitorTimer = setInterval(updateMemoryInfo, 5000); // 每5秒更新一次
  }

  /**
   * 初始化Web Vitals监控
   */
  private initializeVitalsTracking(): void {
    if (!('PerformanceObserver' in window)) return;

    try {
      // 监控LCP
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            this.metrics.vitals.lcp = entry.startTime;
          } else if (entry.entryType === 'first-input') {
            const inputEntry = entry as PerformanceEventTiming;
            this.metrics.vitals.fid = inputEntry.processingStart - entry.startTime;
          } else if (entry.entryType === 'layout-shift') {
            const shiftEntry = entry as PerformanceEntry & { value: number };
            this.metrics.vitals.cls += shiftEntry.value;
          }
        }
      });

      this.performanceObserver.observe({
        entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'],
      });

      // 监控FCP
      if ('getEntriesByType' in performance) {
        const paintEntries = performance.getEntriesByType('paint');
        const fcpEntry = paintEntries.find((entry) => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          this.metrics.vitals.fcp = fcpEntry.startTime;
        }
      }
    } catch (error) {
      if (isDevelopment) {
        console.warn('🔥 [Performance Monitor] Web Vitals initialization failed:', error);
      }
    }
  }

  /**
   * 初始化网络监控
   */
  private initializeNetworkTracking(): void {
    if ('connection' in navigator) {
      const connection = (
        navigator as Navigator & {
          connection?: {
            type?: string;
            downlink?: number;
            rtt?: number;
            effectiveType?: string;
            addEventListener?: (event: string, handler: () => void) => void;
          };
        }
      ).connection;

      if (connection) {
        const updateNetworkInfo = () => {
          this.metrics.network = {
            connectionType: connection.type || 'unknown',
            downlink: connection.downlink || 0,
            rtt: connection.rtt || 0,
            effectiveType: connection.effectiveType || 'unknown',
          };
        };

        updateNetworkInfo();
        connection.addEventListener?.('change', updateNetworkInfo);
      }
    }
  }

  /**
   * 收集性能指标
   */
  private collectMetrics(): void {
    // 更新时间戳
    this.metrics.timestamp = Date.now();

    // 收集渲染性能
    this.collectRenderMetrics();

    // 收集自定义应用指标
    this.collectApplicationMetrics();

    // 添加到历史记录
    this.addToHistory({ ...this.metrics });

    // 检查所有阈值
    this.checkAllThresholds();
  }

  /**
   * 收集渲染性能指标
   */
  private collectRenderMetrics(): void {
    if ('getEntriesByType' in performance) {
      const entries = performance.getEntriesByType('measure');
      const renderEntry = entries.find((entry) => entry.name.includes('render'));

      if (renderEntry) {
        this.metrics.renderMetrics.renderTime = renderEntry.duration;
      }
    }
  }

  /**
   * 收集应用性能指标
   */
  private collectApplicationMetrics(): void {
    // 从自定义指标中更新应用指标
    this.metrics.application.editorResponseTime = this.customMetrics.get('editorResponseTime') || 0;
    this.metrics.application.saveOperationTime = this.customMetrics.get('saveOperationTime') || 0;
    this.metrics.application.searchTime = this.customMetrics.get('searchTime') || 0;
    this.metrics.application.imageProcessingTime =
      this.customMetrics.get('imageProcessingTime') || 0;
    this.metrics.application.documentLoadTime = this.customMetrics.get('documentLoadTime') || 0;
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(metrics: PerformanceMetrics): void {
    this.history.value.push(metrics);

    // 限制历史记录大小
    if (this.history.value.length > this.config.maxHistorySize) {
      this.history.value = this.history.value.slice(-this.config.maxHistorySize);
    }
  }

  /**
   * 检查FPS阈值
   */
  private checkFPSThresholds(): void {
    const fps = this.metrics.fps;
    const thresholds = this.config.thresholds.fps;

    if (fps < thresholds.critical) {
      this.addWarning(
        'fps',
        'critical',
        `FPS过低: ${fps}`,
        fps,
        thresholds.critical,
        '建议减少DOM操作或启用虚拟滚动',
      );
    } else if (fps < thresholds.warning) {
      this.addWarning(
        'fps',
        'medium',
        `FPS较低: ${fps}`,
        fps,
        thresholds.warning,
        '建议检查动画和渲染性能',
      );
    }
  }

  /**
   * 检查内存阈值
   */
  private checkMemoryThresholds(): void {
    const percentage = this.metrics.memoryUsage.percentage;
    const thresholds = this.config.thresholds.memory;

    if (percentage > thresholds.critical) {
      this.addWarning(
        'memory',
        'critical',
        `内存使用率过高: ${percentage.toFixed(1)}%`,
        percentage,
        thresholds.critical,
        '建议执行垃圾回收或减少内存使用',
      );
    } else if (percentage > thresholds.warning) {
      this.addWarning(
        'memory',
        'medium',
        `内存使用率较高: ${percentage.toFixed(1)}%`,
        percentage,
        thresholds.warning,
        '建议监控内存使用情况',
      );
    }
  }

  /**
   * 检查所有阈值
   */
  private checkAllThresholds(): void {
    // 检查渲染时间
    const renderTime = this.metrics.renderMetrics.renderTime;
    const renderThresholds = this.config.thresholds.renderTime;

    if (renderTime > renderThresholds.critical) {
      this.addWarning(
        'render',
        'critical',
        `渲染时间过长: ${renderTime.toFixed(2)}ms`,
        renderTime,
        renderThresholds.critical,
        '建议优化渲染逻辑或减少DOM操作',
      );
    } else if (renderTime > renderThresholds.warning) {
      this.addWarning(
        'render',
        'medium',
        `渲染时间较长: ${renderTime.toFixed(2)}ms`,
        renderTime,
        renderThresholds.warning,
        '建议检查渲染性能',
      );
    }

    // 检查响应时间
    const responseTime = this.metrics.application.editorResponseTime;
    const responseThresholds = this.config.thresholds.responseTime;

    if (responseTime > responseThresholds.critical) {
      this.addWarning(
        'custom',
        'critical',
        `编辑器响应时间过长: ${responseTime.toFixed(2)}ms`,
        responseTime,
        responseThresholds.critical,
        '建议优化编辑器逻辑',
      );
    }
  }

  /**
   * 添加性能警告
   */
  private addWarning(
    type: PerformanceWarning['type'],
    severity: PerformanceWarning['severity'],
    message: string,
    value: number,
    threshold: number,
    suggestion?: string,
  ): void {
    const warning: PerformanceWarning = {
      type,
      severity,
      message,
      metric: type,
      value,
      threshold,
      timestamp: Date.now(),
      suggestion,
    };

    this.warnings.value.push(warning);

    // 限制警告数量
    if (this.warnings.value.length > 100) {
      this.warnings.value = this.warnings.value.slice(-50);
    }

    // 输出警告到控制台
    if (severity === 'critical') {
      console.error('🔥 [Performance Monitor] Performance warning:', warning);
    } else {
      if (isDevelopment) {
        console.warn('🔥 [Performance Monitor] Performance warning:', warning);
      }
    }
  }

  /**
   * 开始操作计时
   */
  startTiming(operation: string): void {
    this.operationTimers.set(operation, performance.now());
  }

  /**
   * 结束操作计时
   */
  endTiming(operation: string): number {
    const startTime = this.operationTimers.get(operation);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.customMetrics.set(operation, duration);
    this.operationTimers.delete(operation);

    return duration;
  }

  /**
   * 记录自定义指标
   */
  recordMetric(name: string, value: number): void {
    this.customMetrics.set(name, value);
  }

  /**
   * 获取当前性能指标
   */
  getCurrentMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取历史数据
   */
  getHistory(limit?: number): PerformanceMetrics[] {
    const history = this.history.value;
    return limit ? history.slice(-limit) : [...history];
  }

  /**
   * 获取性能警告
   */
  getWarnings(severity?: PerformanceWarning['severity']): PerformanceWarning[] {
    return severity
      ? this.warnings.value.filter((w) => w.severity === severity)
      : [...this.warnings.value];
  }

  /**
   * 清理警告
   */
  clearWarnings(): void {
    this.warnings.value = [];
  }

  /**
   * 获取性能摘要
   */
  getPerformanceSummary() {
    const history = this.history.value;
    if (history.length === 0) return null;

    const recent = history.slice(-10); // 最近10个样本
    const avgFPS = recent.reduce((sum, m) => sum + m.fps, 0) / recent.length;
    const avgMemory = recent.reduce((sum, m) => sum + m.memoryUsage.percentage, 0) / recent.length;
    const avgRender =
      recent.reduce((sum, m) => sum + m.renderMetrics.renderTime, 0) / recent.length;

    return {
      averageFPS: Math.round(avgFPS),
      averageMemoryUsage: Math.round(avgMemory),
      averageRenderTime: Math.round(avgRender * 100) / 100,
      totalWarnings: this.warnings.value.length,
      criticalWarnings: this.warnings.value.filter((w) => w.severity === 'critical').length,
      isHealthy: avgFPS > 50 && avgMemory < 80 && avgRender < 16,
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<PerformanceMonitorConfig>): void {
    this.config = { ...this.config, ...newConfig };

    if (!this.config.enabled && this.isRunning.value) {
      this.stop();
    } else if (this.config.enabled && !this.isRunning.value) {
      this.start();
    }
  }

  /**
   * 获取运行状态
   */
  getIsRunning() {
    return this.isRunning.value;
  }

  /**
   * 销毁监控器
   */
  destroy(): void {
    this.stop();

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }

    this.history.value = [];
    this.warnings.value = [];
    this.customMetrics.clear();
    this.operationTimers.clear();

    if (isDevelopment) {
      console.log('🔥 [Performance Monitor] Performance monitor destroyed');
    }
  }
}

/**
 * 创建性能监控器实例
 */
export const createPerformanceMonitor = (config?: Partial<PerformanceMonitorConfig>) => {
  return new PerformanceMonitor(config);
};

/**
 * 全局性能监控器实例
 */
export const globalPerformanceMonitor = createPerformanceMonitor({
  enabled: PERFORMANCE_MONITOR.enabled, // Use global config
  sampleInterval: 1000,
  enableVitalsTracking: true,
  enableNetworkTracking: true,
});

/**
 * 性能监控组合式API
 */
export const usePerformanceMonitor = (config?: Partial<PerformanceMonitorConfig>) => {
  const monitor = ref<PerformanceMonitor | null>(null);

  const initialize = () => {
    // Only initialize if performance monitoring is globally enabled
    if (!PERFORMANCE_MONITOR.enabled) {
      return null;
    }

    if (!monitor.value) {
      monitor.value = createPerformanceMonitor(config);
    }
    return monitor.value;
  };

  const start = () => {
    const perfMonitor = initialize();
    perfMonitor?.start();
  };

  const stop = () => {
    monitor.value?.stop();
  };

  const startTiming = (operation: string) => {
    monitor.value?.startTiming(operation);
  };

  const endTiming = (operation: string) => {
    return monitor.value?.endTiming(operation) || 0;
  };

  const recordMetric = (name: string, value: number) => {
    monitor.value?.recordMetric(name, value);
  };

  const getMetrics = () => {
    return monitor.value?.getCurrentMetrics() || null;
  };

  const getSummary = () => {
    return monitor.value?.getPerformanceSummary() || null;
  };

  const cleanup = () => {
    monitor.value?.destroy();
    monitor.value = null;
  };

  // 计算属性
  const isRunning = computed(() => (monitor.value ? monitor.value.getIsRunning() : false));
  const currentFPS = computed(() => monitor.value?.getCurrentMetrics().fps || 0);
  const memoryUsage = computed(
    () => monitor.value?.getCurrentMetrics().memoryUsage.percentage || 0,
  );
  const hasWarnings = computed(() => (monitor.value?.getWarnings().length || 0) > 0);

  return {
    monitor: readonly(monitor),
    initialize,
    start,
    stop,
    startTiming,
    endTiming,
    recordMetric,
    getMetrics,
    getSummary,
    cleanup,
    isRunning,
    currentFPS,
    memoryUsage,
    hasWarnings,
  };
};

export default {
  PerformanceMonitor,
  createPerformanceMonitor,
  globalPerformanceMonitor,
  usePerformanceMonitor,
};
