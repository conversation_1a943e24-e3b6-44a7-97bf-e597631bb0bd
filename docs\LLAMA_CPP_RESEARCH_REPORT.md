# llama.cpp 推理卡死问题研究报告

## 关键发现

### 1. **llama.cpp 2024年已知问题**
- **线程数 > CPU核心数导致死锁**: 当推理线程数超过CPU核心数时会导致服务挂起
- **CUDA后端线程安全**: 2024年已修复CUDA后端线程安全问题，但仍建议单线程使用
- **warmup阶段卡死**: 可以使用 `--no-warmup` 参数跳过warmup阶段
- **批处理内存问题**: 批处理时需要特别注意内存管理

### 2. **最佳实践**
- **一个上下文一个线程**: 每个 `llama_context` 应该只在一个线程中使用
- **避免并发推理**: 即使在不同线程中，也应避免同时进行推理
- **简化批处理**: 复杂的批处理逻辑容易导致死锁
- **资源清理**: 确保及时释放批处理资源

### 3. **推荐的重构方案**

#### 方案A: 单线程队列模式
```cpp
class SequentialInferenceQueue {
    QQueue<InferenceTask> m_tasks;
    QMutex m_queueMutex;
    QThread* m_workerThread;
    
    void processNextTask();
};
```

#### 方案B: 进程隔离模式
```cpp
class ProcessBasedInference {
    QProcess* m_llamaProcess;
    
    std::vector<float> generateEmbedding(const QString& text) {
        // 使用独立进程调用 llama-embedding
        // 避免库级别的死锁问题
    }
};
```

#### 方案C: 简化同步模式
```cpp
class SimplifiedInference {
    static QMutex s_globalMutex; // 全局锁确保严格串行
    
    std::vector<float> generateEmbedding(const QString& text) {
        QMutexLocker locker(&s_globalMutex);
        // 完全串行的推理
    }
};
```

## 推荐重构策略

基于研究结果，建议采用**方案C: 简化同步模式**:

1. **移除所有复杂的锁机制**
2. **使用单一全局锁确保严格串行**
3. **简化批处理逻辑**
4. **添加超时和错误恢复**
5. **优化资源管理**

这种方案虽然性能不是最优，但可以确保稳定性，避免复杂的死锁问题。