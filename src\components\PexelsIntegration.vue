<template>
  <div class="column no-wrap">
    <!-- 选项卡切换 -->
    <q-tabs
      v-model="activeTab"
      dense
      class="text-grey"
      active-color="primary"
      indicator-color="primary"
      align="left"
      narrow-indicator
      keep-alive
    >
      <q-tab name="browser" :label="$t('src.components.PexelsIntegration.browser')" />
      <q-tab name="selected" :label="$t('src.components.PexelsIntegration.selected')" />
    </q-tabs>

    <q-tab-panels v-model="activeTab" animated keep-alive>
      <!-- 媒体浏览器面板 -->
      <q-tab-panel name="browser" class="q-pa-none">
        <div class="browser-container">
          <PexelsMediaBrowser @media-selected="handleMediaSelected" />
        </div>
      </q-tab-panel>

      <!-- 已选择媒体面板 -->
      <q-tab-panel name="selected" class="q-pa-md column">
        <div v-if="selectedMedia.length === 0" class="empty-selection text-center q-pa-lg q-space">
          <q-icon name="mdi-image-off" size="48px" color="grey-5" />
          <div class="q-mt-md text-h6">
            {{ $t('src.components.PexelsIntegration.no_media_selected') }}
          </div>
          <div class="q-mt-sm text-body2 text-grey-6">
            {{ $t('src.components.PexelsIntegration.select_media_in_browser') }}
          </div>
        </div>

        <div v-else class="selected-media-grid q-space">
          <div v-for="(media, index) in selectedMedia" :key="media.id" class="selected-media-item">
            <div class="media-preview">
              <img
                v-if="media.type === 'photo'"
                :src="media.src.medium"
                :alt="media.alt"
                class="media-image"
              />
              <div v-else class="video-preview">
                <img :src="media.image" :alt="`Video by ${media.user.name}`" class="media-image" />
                <div class="video-overlay">
                  <q-icon name="mdi-play-circle" size="32px" color="white" />
                </div>
              </div>
            </div>

            <div class="media-info q-pa-sm">
              <div class="media-title text-body2">
                {{ media.type === 'photo' ? media.photographer : media.user.name }}
              </div>
              <div class="media-meta text-caption text-grey-6">
                {{
                  media.type === 'photo'
                    ? $t('src.components.PexelsIntegration.photo')
                    : $t('src.components.PexelsIntegration.video', {
                        duration: formatDuration(media.duration),
                      })
                }}
              </div>
            </div>

            <div class="media-actions">
              <q-btn
                flat
                dense
                round
                icon="mdi-delete"
                color="negative"
                @click="removeMedia(index)"
              />
            </div>
          </div>
        </div>

        <div v-if="selectedMedia.length > 0" class="selection-actions q-mt-md">
          <q-btn
            flat
            color="negative"
            icon="mdi-delete-sweep"
            :label="$t('src.components.PexelsIntegration.clear_all')"
            @click="clearAllMedia"
          />
          <q-btn
            v-if="false"
            color="primary"
            icon="mdi-export"
            :label="$t('src.components.PexelsIntegration.export_selection')"
            @click="exportSelection"
          />
        </div>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Notify } from 'quasar';
import PexelsMediaBrowser from './PexelsMediaBrowser.vue';
import type { PexelsVideo } from 'src/env.d';
import type { PexelsPhoto } from 'src/llm/tools/pexels';

import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

// 类型定义
type SelectedPhotoMedia = PexelsPhoto & { type: 'photo' };
type SelectedVideoMedia = PexelsVideo & { type: 'video' };
type SelectedMedia = SelectedPhotoMedia | SelectedVideoMedia;

// 响应式数据
const activeTab = ref('browser');
const selectedMedia = ref<SelectedMedia[]>([]);

// 事件处理
const handleMediaSelected = (media: PexelsPhoto | PexelsVideo, type: 'photo' | 'video') => {
  // 检查是否已经选择
  const exists = selectedMedia.value.some((item) => item.id === media.id && item.type === type);

  if (!exists) {
    if (type === 'photo') {
      selectedMedia.value.push({ ...(media as PexelsPhoto), type: 'photo' });
    } else {
      selectedMedia.value.push({ ...(media as PexelsVideo), type: 'video' });
    }
    Notify.create({
      type: 'positive',
      message: $t('src.components.PexelsIntegration.add_media_to_selection', {
        media:
          type === 'photo'
            ? $t('src.components.PexelsIntegration.photo')
            : $t('src.components.PexelsIntegration.video'),
      }),
      position: 'top',
    });
  } else {
    Notify.create({
      type: 'warning',
      message: $t('src.components.PexelsIntegration.media_already_in_selection'),
      position: 'top',
    });
  }
};

const removeMedia = (index: number) => {
  selectedMedia.value.splice(index, 1);
  Notify.create({
    type: 'info',
    message: $t('src.components.PexelsIntegration.remove_media_from_selection'),
    position: 'top',
  });
};

const clearAllMedia = () => {
  selectedMedia.value = [];
  Notify.create({
    type: 'info',
    message: $t('src.components.PexelsIntegration.clear_all_selection'),
    position: 'top',
  });
};

const exportSelection = () => {
  const exportData = {
    timestamp: new Date().toISOString(),
    count: selectedMedia.value.length,
    media: selectedMedia.value,
  };

  console.log('导出的媒体数据:', exportData);

  // 这里可以实现实际的导出逻辑
  // 例如：下载为 JSON 文件、发送到服务器等

  Notify.create({
    type: 'positive',
    message: $t('src.components.PexelsIntegration.export_selection_success', {
      count: selectedMedia.value.length,
    }),
    position: 'top',
  });
};

// 工具函数
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};
</script>

<style scoped>
.pexels-integration-example {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.example-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background: rgba(0, 0, 0, 0.02);
}

.example-content {
  flex: 1;
  overflow: hidden;
}

.browser-container {
  height: 100%;
}

.empty-selection {
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.selected-media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.selected-media-item {
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  overflow: hidden;
  transition: box-shadow 0.2s ease;
}

.selected-media-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.media-preview {
  position: relative;
  aspect-ratio: 1;
}

.media-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-preview {
  position: relative;
}

.video-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.8;
}

.media-actions {
  position: absolute;
  top: 8px;
  right: 8px;
}

.selection-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.settings-section {
  max-width: 500px;
}

.settings-actions {
  display: flex;
  gap: 12px;
}

/* 深色主题适配 */
.body--dark .example-header {
  border-color: rgba(255, 255, 255, 0.12);
  background: rgba(255, 255, 255, 0.02);
}

.body--dark .selected-media-item {
  border-color: rgba(255, 255, 255, 0.12);
}

.body--dark .selection-actions {
  border-color: rgba(255, 255, 255, 0.12);
}
</style>
