import { computed, reactive } from 'vue';
import { $t } from 'src/composables/useTrans';

export interface LoadingState {
  isLoading: boolean;
  loadingText: string;
  progress?: number;
  error?: string | null;
}

export interface LoadingOptions {
  text?: string;
  showProgress?: boolean;
  timeout?: number;
}

/**
 * 加载状态管理组合式函数
 */
export function useLoadingState() {
  const loadingStates = reactive<Map<string, LoadingState>>(new Map());

  /**
   * 开始加载
   */
  const startLoading = (key: string, options: LoadingOptions = {}) => {
    const state: LoadingState = {
      isLoading: true,
      loadingText: options.text || $t('src.composables.useLoadingState.loading'),
      progress: options.showProgress ? 0 : undefined,
      error: null,
    };

    loadingStates.set(key, state);

    // 设置超时
    if (options.timeout) {
      setTimeout(() => {
        const currentState = loadingStates.get(key);
        if (currentState?.isLoading) {
          stopLoading(key, $t('src.composables.useLoadingState.loadingTimeout'));
        }
      }, options.timeout);
    }
  };

  /**
   * 更新加载进度
   */
  const updateProgress = (key: string, progress: number, text?: string) => {
    const state = loadingStates.get(key);
    if (state) {
      state.progress = Math.max(0, Math.min(100, progress));
      if (text) {
        state.loadingText = text;
      }
    }
  };

  /**
   * 停止加载
   */
  const stopLoading = (key: string, error?: string) => {
    const state = loadingStates.get(key);
    if (state) {
      state.isLoading = false;
      state.error = error || null;

      // 延迟清理状态，给用户时间看到结果
      setTimeout(
        () => {
          loadingStates.delete(key);
        },
        error ? 3000 : 1000,
      );
    }
  };

  /**
   * 获取加载状态
   */
  const getLoadingState = (key: string): LoadingState | null => {
    return loadingStates.get(key) || null;
  };

  /**
   * 检查是否正在加载
   */
  const isLoading = (key: string): boolean => {
    return loadingStates.get(key)?.isLoading || false;
  };

  /**
   * 获取所有加载状态
   */
  const getAllLoadingStates = computed(() => {
    return Array.from(loadingStates.entries()).map(([key, state]) => ({
      key,
      ...state,
    }));
  });

  /**
   * 检查是否有任何加载中的状态
   */
  const hasAnyLoading = computed(() => {
    return Array.from(loadingStates.values()).some((state) => state.isLoading);
  });

  /**
   * 清理所有加载状态
   */
  const clearAllLoading = () => {
    loadingStates.clear();
  };

  /**
   * 包装异步操作的便捷方法
   */
  const withLoading = async <T>(
    key: string,
    asyncFn: () => Promise<T>,
    options: LoadingOptions = {},
  ): Promise<T> => {
    try {
      startLoading(key, options);
      const result = await asyncFn();
      stopLoading(key);
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : $t('src.composables.useLoadingState.operationFailed');
      stopLoading(key, errorMessage);
      throw error;
    }
  };

  return {
    startLoading,
    updateProgress,
    stopLoading,
    getLoadingState,
    isLoading,
    getAllLoadingStates,
    hasAnyLoading,
    clearAllLoading,
    withLoading,
  };
}

/**
 * 全局加载状态管理
 */
const globalLoadingState = useLoadingState();

export const useGlobalLoading = () => globalLoadingState;
