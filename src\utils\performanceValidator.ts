import { getDocStore } from 'src/composeables/useStore';
import type { useDocStore } from 'src/stores/doc';

export interface PerformanceMetrics {
  // 实例管理性能
  instanceCreationTime: number;
  instanceLookupTime: number;
  instanceCleanupTime: number;

  // 内容同步性能
  contentSyncTime: number;
  syncedInstancesCount: number;

  // 拖拽性能
  dragUpdateTime: number;
  dragFrameRate: number;

  // 内存使用
  memoryUsage: {
    editorInstances: number;
    documentContents: number;
    editorStates: number;
  };

  // 错误统计
  errorCount: number;

  timestamp: number;
}

export class PerformanceValidator {
  private metrics: PerformanceMetrics[] = [];
  private isMonitoring = false;
  private docStoreCache: ReturnType<typeof useDocStore> | null = null;
  
  private get docStore() {
    if (!this.docStoreCache) {
      this.docStoreCache = getDocStore();
    }
    return this.docStoreCache;
  }

  /**
   * 开始性能监控
   */
  startMonitoring(): void {
    this.isMonitoring = true;
    this.collectMetrics();

    // 每5秒收集一次指标
    const interval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(interval);
        return;
      }
      this.collectMetrics();
    }, 5000);
  }

  /**
   * 停止性能监控
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
  }

  /**
   * 收集性能指标
   */
  private collectMetrics(): void {
    // 测试实例创建性能
    const instanceCreationTime = this.measureInstanceCreation();

    // 测试实例查找性能
    const instanceLookupTime = this.measureInstanceLookup();

    // 测试内容同步性能
    const { contentSyncTime, syncedInstancesCount } = this.measureContentSync();

    // 收集内存使用情况
    const memoryUsage = this.collectMemoryUsage();

    const metrics: PerformanceMetrics = {
      instanceCreationTime,
      instanceLookupTime,
      instanceCleanupTime: 0, // 将在实际清理时测量
      contentSyncTime,
      syncedInstancesCount,
      dragUpdateTime: 0, // 将在拖拽时测量
      dragFrameRate: 0, // 将在拖拽时测量
      memoryUsage,
      errorCount: 0, // 从错误处理器获取
      timestamp: Date.now(),
    };

    this.metrics.push(metrics);

    // 保持最近100条记录
    if (this.metrics.length > 100) {
      this.metrics.shift();
    }

    console.log('Performance metrics collected:', metrics);
  }

  /**
   * 测试实例创建性能
   */
  private measureInstanceCreation(): number {
    const start = performance.now();

    // 模拟实例创建（不实际创建）
    for (let i = 0; i < 100; i++) {
      this.docStore.generateEditorInstanceKey(i, i);
      // 只测量键生成时间
    }

    return (performance.now() - start) / 100; // 平均时间
  }

  /**
   * 测试实例查找性能
   */
  private measureInstanceLookup(): number {
    const start = performance.now();

    // 测试现有实例的查找
    const instanceKeys = Array.from(this.docStore.instanceManager.instances.keys());
    if (instanceKeys.length > 0) {
      for (let i = 0; i < Math.min(100, instanceKeys.length); i++) {
        const key = instanceKeys[i % instanceKeys.length];
        this.docStore.instanceManager.instances.get(key);
      }
    }

    return performance.now() - start;
  }

  /**
   * 测试内容同步性能
   */
  private measureContentSync(): { contentSyncTime: number; syncedInstancesCount: number } {
    const start = performance.now();

    // 获取所有文档的实例数量
    let totalInstances = 0;
    for (const instanceSet of this.docStore.instanceManager.docToInstances.values()) {
      totalInstances += instanceSet.size;
    }

    const contentSyncTime = performance.now() - start;

    return {
      contentSyncTime,
      syncedInstancesCount: totalInstances,
    };
  }

  /**
   * 收集内存使用情况
   */
  private collectMemoryUsage(): PerformanceMetrics['memoryUsage'] {
    return {
      editorInstances: this.docStore.instanceManager.instances.size,
      documentContents: this.docStore.documentContents.size,
      editorStates: this.docStore.editorStates.size,
    };
  }

  /**
   * 测试拖拽性能
   */
  measureDragPerformance(updateCount: number, duration: number): void {
    const avgUpdateTime = duration / updateCount;
    const frameRate = 1000 / avgUpdateTime;

    if (this.metrics.length > 0) {
      const lastMetric = this.metrics[this.metrics.length - 1];
      lastMetric.dragUpdateTime = avgUpdateTime;
      lastMetric.dragFrameRate = frameRate;
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    summary: {
      avgInstanceCreationTime: number;
      avgInstanceLookupTime: number;
      avgContentSyncTime: number;
      avgDragFrameRate: number;
      currentMemoryUsage: PerformanceMetrics['memoryUsage'];
    };
    trends: {
      instanceCreationTrend: 'improving' | 'stable' | 'degrading';
      contentSyncTrend: 'improving' | 'stable' | 'degrading';
      memoryTrend: 'improving' | 'stable' | 'degrading';
    };
    recommendations: string[];
  } {
    if (this.metrics.length === 0) {
      return {
        summary: {
          avgInstanceCreationTime: 0,
          avgInstanceLookupTime: 0,
          avgContentSyncTime: 0,
          avgDragFrameRate: 0,
          currentMemoryUsage: { editorInstances: 0, documentContents: 0, editorStates: 0 },
        },
        trends: {
          instanceCreationTrend: 'stable',
          contentSyncTrend: 'stable',
          memoryTrend: 'stable',
        },
        recommendations: ['开始性能监控以获取数据'],
      };
    }

    const recentMetrics = this.metrics.slice(-10); // 最近10条记录

    const summary = {
      avgInstanceCreationTime: this.average(recentMetrics.map((m) => m.instanceCreationTime)),
      avgInstanceLookupTime: this.average(recentMetrics.map((m) => m.instanceLookupTime)),
      avgContentSyncTime: this.average(recentMetrics.map((m) => m.contentSyncTime)),
      avgDragFrameRate: this.average(
        recentMetrics.map((m) => m.dragFrameRate).filter((r) => r > 0),
      ),
      currentMemoryUsage: recentMetrics[recentMetrics.length - 1].memoryUsage,
    };

    const trends = {
      instanceCreationTrend: this.analyzeTrend(recentMetrics.map((m) => m.instanceCreationTime)),
      contentSyncTrend: this.analyzeTrend(recentMetrics.map((m) => m.contentSyncTime)),
      memoryTrend: this.analyzeTrend(recentMetrics.map((m) => m.memoryUsage.editorInstances)),
    };

    const recommendations = this.generateRecommendations(summary, trends);

    return { summary, trends, recommendations };
  }

  /**
   * 计算平均值
   */
  private average(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  }

  /**
   * 分析趋势
   */
  private analyzeTrend(values: number[]): 'improving' | 'stable' | 'degrading' {
    if (values.length < 3) return 'stable';

    const first = values.slice(0, Math.floor(values.length / 2));
    const second = values.slice(Math.floor(values.length / 2));

    const firstAvg = this.average(first);
    const secondAvg = this.average(second);

    const change = (secondAvg - firstAvg) / firstAvg;

    if (change < -0.1) return 'improving'; // 性能提升10%以上
    if (change > 0.1) return 'degrading'; // 性能下降10%以上
    return 'stable';
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(
    summary: Record<string, unknown>,
    trends: Record<string, unknown>,
  ): string[] {
    const recommendations: string[] = [];

    if ((summary.avgInstanceCreationTime as number) > 1) {
      recommendations.push('实例创建时间较长，考虑优化实例键生成算法');
    }

    if ((summary.avgContentSyncTime as number) > 10) {
      recommendations.push('内容同步时间较长，考虑减少同步频率或优化同步算法');
    }

    if ((summary.avgDragFrameRate as number) > 0 && (summary.avgDragFrameRate as number) < 30) {
      recommendations.push('拖拽帧率较低，考虑优化拖拽更新逻辑');
    }

    if (
      (summary.currentMemoryUsage as { editorInstances?: number })?.editorInstances &&
      (summary.currentMemoryUsage as { editorInstances: number }).editorInstances > 50
    ) {
      recommendations.push('编辑器实例数量较多，考虑实现实例回收机制');
    }

    if (trends.instanceCreationTrend === 'degrading') {
      recommendations.push('实例创建性能呈下降趋势，需要调查原因');
    }

    if (trends.contentSyncTrend === 'degrading') {
      recommendations.push('内容同步性能呈下降趋势，可能存在内存泄漏');
    }

    if (trends.memoryTrend === 'degrading') {
      recommendations.push('内存使用呈上升趋势，检查是否存在内存泄漏');
    }

    if (recommendations.length === 0) {
      recommendations.push('性能表现良好，继续保持');
    }

    return recommendations;
  }

  /**
   * 清理监控数据
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * 导出性能数据
   */
  exportMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }
}

// 全局性能验证器实例
export const performanceValidator = new PerformanceValidator();
