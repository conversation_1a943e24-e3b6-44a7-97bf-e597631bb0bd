export const processer = () => {
  const tagContent = (content: string, tag: string): { content: string; tag_content: string } => {
    let tag_content = '';
    let processedContent = '';
    let currentPos = 0;
    let isInTagMode = false;

    while (currentPos < content.length) {
      if (!isInTagMode) {
        // 查找 tag 开始标签
        const thinkStartIndex = content.indexOf(tag, currentPos);

        if (thinkStartIndex === -1) {
          // 没有找到开始标签，剩余内容都是回答
          processedContent += content.substring(currentPos);
          break;
        }

        // 将开始标签之前的内容添加到回答中
        processedContent += content.substring(currentPos, thinkStartIndex);

        // 切换到 tag 模式，跳过 tag 标签
        isInTagMode = true;
        currentPos = thinkStartIndex + tag.length; // 标签占用的字符长度
      } else {
        const endTag = `</${tag.slice(1)}`;
        // 在 tag 模式中，查找 tag 结束标签
        const thinkEndIndex = content.indexOf(endTag, currentPos);

        if (thinkEndIndex === -1) {
          // 没有找到结束标签，剩余内容都是 tag 内容
          tag_content += content.substring(currentPos);
          break;
        }

        // 将结束标签之前的内容添加到 tag 内容中
        tag_content += content.substring(currentPos, thinkEndIndex);

        // 切换回回答模式，跳过 tag 标签
        isInTagMode = false;
        currentPos = thinkEndIndex + endTag.length; // 结束标签占用的字符长度
      }
    }

    return {
      content: processedContent.trim(),
      tag_content: tag_content.trim(),
    };
  };
  // 检测是否使用Ollama
  const isOllamaProvider = (baseUrl: string): boolean => {
    return (
      baseUrl.includes('localhost:11434') ||
      baseUrl.includes('127.0.0.1:11434') ||
      baseUrl.includes('ollama')
    );
  };

  const thinkContent = (content: string) => {
    const { content: processedContent, tag_content: reasoning_content } = tagContent(
      content,
      '<think>',
    );
    return {
      content: processedContent,
      reasoning_content,
    };
  };

  return {
    tagContent,
    thinkContent,
    isOllamaProvider,
  };
};
