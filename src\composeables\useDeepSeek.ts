import type { AssistantMessage as AssistantMessageType } from 'src/types/qwen';
import { computed, type Ref } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import { useUiStore } from 'src/stores/ui';
import { DEFAULT_DEEPSEEK_SETTINGS } from 'src/config/defaultSettings';
import { executeToolCall } from 'src/llm/tools/index';
import { PromptService, type ConversationRole } from 'src/services/promptService';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import { $t } from 'src/composables/useTrans';

export const useDeepSeek = () => {
  // 在函数内部获取组合式函数
  const { updateConversation } = useSqlite();
  const uiStore = useUiStore();

  // 使用 computed 来创建响应式的 deepSeekSettings
  const deepSeekSettings = computed(() => {
    return uiStore.perferences?.llm?.deepseek || DEFAULT_DEEPSEEK_SETTINGS;
  });

  // readSettings 方法现在不再需要，因为设置是响应式的
  // 保留此方法以保持向后兼容性，但实际上不执行任何操作
  const readSettings = () => {
    // 设置现在通过 computed 自动同步，无需手动读取
    console.log('[useDeepSeek] 设置已自动同步:', deepSeekSettings.value);
  };

  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const { messages, messagesRef, conversation, tools, enableTools, abortController } = params;

    if (!deepSeekSettings.value) return;

    console.log('[useDeepSeek] 开始发送消息');
    console.log('[useDeepSeek] 消息数量:', messages.length);
    console.log('[useDeepSeek] 工具数量:', tools.length);
    console.log('[useDeepSeek] 启用工具:', enableTools);

    loading.value = true;

    try {
      // 构建请求体
      const requestBody: {
        model: string;
        messages: typeof messages;
        stream: boolean;
        temperature?: number;
        max_tokens: number;
        top_p?: number;
        tools?: typeof tools;
        tool_choice?: string;
      } = {
        model: deepSeekSettings.value.model,
        messages: [...messages],
        stream: deepSeekSettings.value.stream,
        max_tokens: deepSeekSettings.value.maxTokens,
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (
        deepSeekSettings.value.temperature !== undefined &&
        !isNaN(deepSeekSettings.value.temperature)
      ) {
        requestBody.temperature = deepSeekSettings.value.temperature;
      }
      if (deepSeekSettings.value.topP !== undefined && !isNaN(deepSeekSettings.value.topP)) {
        requestBody.top_p = deepSeekSettings.value.topP;
      }

      // 如果有启用的工具，添加工具定义
      if (enableTools && tools.length > 0) {
        console.log(`🔧 [useDeepSeek] 启用工具数: ${tools.length}`);
        console.log(`🔧 [useDeepSeek] 工具列表: ${tools.map((t) => t.function.name).join(', ')}`);

        requestBody.tools = tools;
        requestBody.tool_choice = 'auto';
      }

      console.log('[useDeepSeek] 请求体:', requestBody);

      const response = await fetch(`${deepSeekSettings.value.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${deepSeekSettings.value.apiKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('ReadableStream not supported');
      }

      // 直接操作响应式消息数组，实现流式更新
      messagesRef.value.push({
        role: 'assistant',
        content: '',
        reasoning_content: '',
      });
      const lastMessage = messagesRef.value[messagesRef.value.length - 1] as AssistantMessageType;

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      // 用于累积工具调用数据的临时存储 - 按MiniMax方式
      const toolCallsBuffer = new Map<
        string,
        {
          id: string;
          type: string;
          function: {
            name: string;
            arguments: string;
          };
        }
      >();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '' || line.trim() === 'data: [DONE]') continue;
          if (!line.startsWith('data: ')) continue;

          const data = line.slice(6);
          if (data.trim() === '[DONE]') break;

          try {
            const parsed = JSON.parse(data);
            const delta = parsed.choices?.[0]?.delta;
            const finishReason = parsed.choices?.[0]?.finish_reason;

            // 处理内容更新
            if (delta?.content) {
              if (typeof lastMessage.content === 'string') {
                lastMessage.content += delta.content;
              } else {
                lastMessage.content = delta.content;
              }
            }

            // 处理思考内容更新 - 按qwen方式处理
            if (delta?.reasoning_content) {
              if (lastMessage.reasoning_content === '思考中...') {
                lastMessage.reasoning_content = '';
              }
              lastMessage.reasoning_content += delta.reasoning_content;
            }

            // 处理工具调用 - 按MiniMax累积模式
            if (delta?.tool_calls) {
              console.log('[useDeepSeek] 检测到工具调用数据:', delta.tool_calls);

              delta.tool_calls.forEach(
                (
                  toolCall: {
                    id?: string;
                    type?: string;
                    function?: {
                      name?: string;
                      arguments?: string;
                    };
                  },
                  index: number,
                ) => {
                  // 如果有 ID，说明是第一条消息，创建新的工具调用对象
                  if (toolCall.id && toolCall.id.trim() !== '') {
                    console.log(`[useDeepSeek] 创建新的工具调用对象，ID: ${toolCall.id}`);
                    toolCallsBuffer.set(toolCall.id, {
                      id: toolCall.id,
                      type: toolCall.type || 'function',
                      function: {
                        name: toolCall.function?.name || '',
                        arguments: toolCall.function?.arguments || '',
                      },
                    });
                  } else {
                    // ID 为空，说明是后续消息，需要累加到现有的工具调用对象中
                    const existingEntries = Array.from(toolCallsBuffer.entries());
                    if (existingEntries.length > index) {
                      const [toolCallId, bufferedToolCall] = existingEntries[index];

                      // 累积工具名称
                      if (toolCall.function?.name) {
                        bufferedToolCall.function.name += toolCall.function.name;
                      }

                      // 累积工具参数
                      if (toolCall.function?.arguments) {
                        bufferedToolCall.function.arguments += toolCall.function.arguments;
                      }

                      console.log(`[useDeepSeek] 工具调用累积中 ${toolCallId}:`, bufferedToolCall);
                    }
                  }
                },
              );
            }

            // 当 finish_reason 为 "tool_calls" 时，完成工具调用累积
            if (finishReason === 'tool_calls' && toolCallsBuffer.size > 0) {
              console.log('[useDeepSeek] 工具调用完成，开始处理完整的工具调用数据');

              // 将累积的工具调用数据转换为最终格式
              lastMessage.tool_calls = Array.from(toolCallsBuffer.values()).map(
                (toolCall, index) => ({
                  id: toolCall.id,
                  type: 'function' as const,
                  index: index,
                  function: {
                    name: toolCall.function.name,
                    arguments: toolCall.function.arguments,
                  },
                }),
              );

              console.log('[useDeepSeek] 最终工具调用数据:', lastMessage.tool_calls);

              // 清空缓冲区
              toolCallsBuffer.clear();
            } else if (finishReason === 'tool_calls' && toolCallsBuffer.size === 0) {
              console.log('[useDeepSeek] 收到重复的 finish_reason: tool_calls，忽略');
            }
          } catch (parseError) {
            console.warn('[useDeepSeek] 解析响应失败:', parseError, 'data:', data);
          }
        }
      }

      // 处理工具调用
      if (lastMessage.tool_calls && lastMessage.tool_calls.length > 0) {
        console.log('[useDeepSeek] 开始执行工具调用');

        for (const toolCall of lastMessage.tool_calls) {
          try {
            console.log(`[useDeepSeek] 工具参数:`, toolCall.function.arguments);

            // 解析工具参数
            let toolArgs: Record<string, unknown>;
            try {
              toolArgs = JSON.parse(toolCall.function.arguments);
            } catch (parseError) {
              console.error('[useDeepSeek] 工具参数解析失败:', parseError);
              throw new Error(`工具参数格式错误: ${parseError}`);
            }

            // 执行工具
            const toolResult = await executeToolCall(toolCall.function.name, toolArgs);

            // 添加工具结果消息
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify(toolResult),
              tool_call_id: toolCall.id,
            });

            console.log(`[useDeepSeek] 工具 ${toolCall.function.name} 执行完成:`, toolResult);
          } catch (error) {
            console.error(`[useDeepSeek] 工具 ${toolCall.function.name} 执行失败:`, error);

            // 添加错误结果
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify({
                success: false,
                message: $t('src.composeables.useDeepSeek.toolExecutionFailed', {
                  error: error.message,
                }),
                toolName: toolCall.function.name,
              }),
              tool_call_id: toolCall.id,
            });
          }
        }

        // 如果有工具调用，递归调用获取AI对工具结果的响应
        console.log('工具执行完成，开始递归调用获取AI响应...');
        console.log('当前消息数量:', messages.length);

        // 递归调用以获取最终响应
        console.log('🔄 [useDeepSeek] 递归调用获取最终响应');
        const recursiveParams: SimplifiedLlmParams = {
          ...params,
          messages: messagesRef.value,
        };
        await sendMessage(recursiveParams, loading);
        return;
      }
      console.log('[useDeepSeek] 消息发送完成');
    } catch (error) {
      console.error('[useDeepSeek] Error sending message:', error);
      messagesRef.value.push({
        role: 'assistant',
        content: $t('src.composeables.useDeepSeek.errorOccurred'),
      });
    } finally {
      loading.value = false;

      await updateConversation(
        conversation.id,
        conversation.title,
        JSON.stringify(messagesRef.value),
        conversation.prompt,
      );
    }
  };

  return {
    readSettings,
    deepSeekSettings,
    sendMessage,
    // 导出prompt服务的方法
    getAvailableRoles: () => PromptService.getAvailableRoles(),
    getRoleSuggestions: (role: ConversationRole) => PromptService.getRoleSuggestions(role),
  };
};
