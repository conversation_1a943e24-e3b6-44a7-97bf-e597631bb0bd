<template>
  <node-view-wrapper class="radius-sm">
    <div class="radius-sm border overflow-hidden">
      <!-- 工具栏 -->
      <div
        class="row no-wrap items-center border-bottom q-pr-sm"
        :class="$q.dark.isActive ? 'bg-grey-10' : 'bg-grey-1'"
      >
        <q-tabs
          v-model="activeTab"
          dense
          class="text-grey"
          active-color="primary"
          indicator-color="primary"
          align="left"
        >
          <q-tab name="code" :label="$t('src.components.tiptap.mermaid.code')" />
          <q-tab name="preview" :label="$t('src.components.tiptap.mermaid.preview')" />
        </q-tabs>
        <q-space />

        <div class="toolbar-actions">
          <!-- 撤销/重做按钮 - 仅在代码模式下显示 -->
          <template v-if="activeTab === 'code'">
            <q-btn flat dense size="sm" icon="mdi-undo" @click="undo" :disable="historyIndex <= 0">
              <q-tooltip>{{ $t('src.components.tiptap.mermaid.undo') }} (Ctrl+Z)</q-tooltip>
            </q-btn>
            <q-btn
              flat
              dense
              size="sm"
              icon="mdi-redo"
              @click="redo"
              :disable="historyIndex >= history.length - 1"
            >
              <q-tooltip>{{ $t('src.components.tiptap.mermaid.redo') }} (Ctrl+Y)</q-tooltip>
            </q-btn>
            <q-separator vertical inset spaced />
          </template>

          <q-btn
            flat
            dense
            size="sm"
            icon="mdi-magnify-plus-outline"
            @click="viewLargeImage"
            :disable="activeTab === 'code' || !svgContent"
          >
            <q-tooltip>{{ $t('src.components.tiptap.mermaid.viewLargeImage') }}</q-tooltip>
          </q-btn>
          <q-btn flat dense size="sm" icon="mdi-content-copy" @click="copyContent">
            <q-tooltip>{{ $t('src.components.tiptap.mermaid.copy') }}</q-tooltip>
          </q-btn>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="mermaid-content">
        <!-- 代码编辑器 -->
        <div v-if="activeTab === 'code'" class="q-pa-xs">
          <q-input
            ref="codeInputRef"
            v-model="code"
            type="textarea"
            filled
            autogrow
            square
            :placeholder="$t('src.components.tiptap.mermaid.enterCode')"
            @update:model-value="onCodeChange"
            @keydown="handleKeyDown"
            class="mermaid-textarea"
          />
        </div>

        <!-- 预览区域 -->
        <div v-else class="preview-container cursor-pointer" @click="viewLargeImage">
          <div v-if="svgContent" class="mermaid-preview" v-html="svgContent"></div>
          <div v-else-if="renderError" class="error-message">
            <q-icon name="mdi-alert-circle" color="negative" />
            <span>{{ $t('src.components.tiptap.mermaid.renderError') }}: {{ renderError }}</span>
          </div>
          <div v-else class="loading-message">
            <q-spinner color="primary" size="2em" />
            <span>{{ $t('src.components.tiptap.mermaid.renderingChart') }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 大图预览对话框 -->
    <q-dialog
      v-model="showLargeImage"
      full-height
      full-width
      transition-duration="100"
      :class="$q.dark.isActive ? 'bg-grey-10' : 'bg-grey-1'"
    >
      <q-card bordered>
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">{{ $t('src.components.tiptap.mermaid.largePreview') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        <q-card-section class="large-image-content">
          <div v-if="svgContent" class="large-mermaid-preview" v-html="svgContent"></div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { NodeViewWrapper } from '@tiptap/vue-3';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { copyToClipboard } from 'src/utils/clipboard';
import type { NodeViewProps } from '@tiptap/core';

// 动态导入mermaid
let mermaid: {
  initialize: (config: Record<string, unknown>) => void;
  render: (id: string, code: string) => Promise<{ svg: string }>;
} | null = null;

const props = defineProps<NodeViewProps>();

const $q = useQuasar();
const { t: $t } = useI18n({ useScope: 'global' });

// 响应式数据
const activeTab = ref<'code' | 'preview'>('preview');
const code = ref((props.node.attrs.code as string) || '');
const svgContent = ref('');
const renderError = ref('');
const showLargeImage = ref(false);
const codeInputRef = ref();

// 防抖定时器
let renderTimer: NodeJS.Timeout | null = null;
let historyTimer: NodeJS.Timeout | null = null;

// 历史记录管理
const history = ref<string[]>([]);
const historyIndex = ref(-1);
const maxHistorySize = 50;
let isUndoRedo = false;

// 历史记录管理函数
const addToHistory = (value: string) => {
  if (isUndoRedo) return;

  // 如果当前值与历史记录中的最后一个值相同，则不添加
  if (history.value.length > 0 && history.value[history.value.length - 1] === value) {
    return;
  }

  // 如果不在历史记录的末尾，删除当前位置之后的所有记录
  if (historyIndex.value < history.value.length - 1) {
    history.value = history.value.slice(0, historyIndex.value + 1);
  }

  // 添加新记录
  history.value.push(value);
  historyIndex.value = history.value.length - 1;

  // 限制历史记录大小
  if (history.value.length > maxHistorySize) {
    history.value.shift();
    historyIndex.value--;
  }
};

const undo = () => {
  if (historyIndex.value > 0) {
    isUndoRedo = true;
    historyIndex.value--;
    code.value = history.value[historyIndex.value];
    props.updateAttributes({ code: code.value });

    // 清空渲染内容，触发重新渲染
    svgContent.value = '';
    renderError.value = '';
    if (activeTab.value === 'preview') {
      void renderMermaid();
    }

    setTimeout(() => {
      isUndoRedo = false;
    }, 100);
  }
};

const redo = () => {
  if (historyIndex.value < history.value.length - 1) {
    isUndoRedo = true;
    historyIndex.value++;
    code.value = history.value[historyIndex.value];
    props.updateAttributes({ code: code.value });

    // 清空渲染内容，触发重新渲染
    svgContent.value = '';
    renderError.value = '';
    if (activeTab.value === 'preview') {
      void renderMermaid();
    }

    setTimeout(() => {
      isUndoRedo = false;
    }, 100);
  }
};

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    if (event.key === 'z' && !event.shiftKey) {
      event.preventDefault();
      undo();
    } else if ((event.key === 'z' && event.shiftKey) || event.key === 'y') {
      event.preventDefault();
      redo();
    }
  }
};

// 初始化mermaid
const initMermaid = async () => {
  try {
    if (!mermaid) {
      const mermaidModule = await import('mermaid');
      mermaid = mermaidModule.default;

      mermaid.initialize({
        startOnLoad: false,
        theme: 'dark',
        securityLevel: 'loose',
        fontFamily: 'monospace',
      });
    }
  } catch (error) {
    console.error('Failed to load mermaid:', error);
    renderError.value = $t('src.components.tiptap.mermaid.failedToLoadMermaid');
  }
};

// 渲染图表
const renderMermaid = async () => {
  if (!mermaid || !code.value.trim()) {
    svgContent.value = '';
    return;
  }

  try {
    renderError.value = '';
    const { svg } = await mermaid.render(`mermaid-${Date.now()}`, code.value);
    svgContent.value = svg;
  } catch (error) {
    console.error('Mermaid render error:', error);
    renderError.value = error instanceof Error ? error.message : $t('src.components.tiptap.mermaid.renderFailed');
    svgContent.value = '';
  }
};

// 代码变化处理
const onCodeChange = (newCode: string) => {
  code.value = newCode;
  props.updateAttributes({ code: newCode });

  // 使用防抖添加到历史记录（避免每次输入都添加）
  if (historyTimer) {
    clearTimeout(historyTimer);
  }
  historyTimer = setTimeout(() => {
    addToHistory(newCode);
  }, 1000); // 1秒后添加到历史记录

  // 清空当前的渲染内容，强制重新渲染
  svgContent.value = '';
  renderError.value = '';

  // 如果当前在预览模式，立即重新渲染
  if (activeTab.value === 'preview') {
    void renderMermaid();
  }
};

// 查看大图
const viewLargeImage = () => {
  showLargeImage.value = true;
};

// 复制内容
const copyContent = async () => {
  const contentToCopy = activeTab.value === 'code' ? code.value : svgContent.value;

  try {
    const success = await copyToClipboard(contentToCopy);
    if (success) {
      $q.notify({
        type: 'positive',
        message: activeTab.value === 'code' ? $t('src.components.tiptap.mermaid.codeCopied') : $t('src.components.tiptap.mermaid.chartCopied'),
        position: 'top',
      });
    } else {
      throw new Error($t('src.components.tiptap.mermaid.copyFailed'))
    }
  } catch {
    $q.notify({
      type: 'negative',
      message: $t('src.components.tiptap.mermaid.copyFailed'),
      position: 'top',
    });
  }
};

// 监听tab切换
watch(activeTab, (newTab) => {
  if (newTab === 'preview' && code.value.trim()) {
    void nextTick(() => {
      void renderMermaid();
    });
  }
});

// 监听props变化，同步代码内容
watch(
  () => props.node.attrs.code,
  (newCode) => {
    if (newCode !== code.value) {
      code.value = newCode as string;
      // 清空当前渲染内容
      svgContent.value = '';
      renderError.value = '';
      if (activeTab.value === 'preview') {
        void renderMermaid();
      }
    }
  },
  { immediate: true },
);

// 监听代码内容变化，在预览模式下自动重新渲染
watch(code, (newCode) => {
  if (activeTab.value === 'preview' && newCode.trim()) {
    // 使用防抖避免频繁渲染
    clearTimeout(renderTimer);
    renderTimer = setTimeout(() => {
      void renderMermaid();
    }, 500);
  }
});

// 组件挂载
onMounted(async () => {
  // 初始化历史记录
  if (code.value) {
    addToHistory(code.value);
  }

  await initMermaid();
  if (activeTab.value === 'preview') {
    await renderMermaid();
  }
});

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  if (renderTimer) {
    clearTimeout(renderTimer);
    renderTimer = null;
  }
  if (historyTimer) {
    clearTimeout(historyTimer);
    historyTimer = null;
  }
});
</script>

<style scoped>
.mermaid-container {
  border: 1px solid var(--q-border-color);
  border-radius: 8px;
  overflow: hidden;
  background: var(--q-card-background);
}

.toolbar-actions {
  display: flex;
  gap: 0.25rem;
}

.mermaid-textarea :deep(.q-field__control) {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.preview-container {
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.mermaid-preview {
  max-width: 100%;
  overflow: auto;
  /* 禁用文本选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.mermaid-preview :deep(svg) {
  max-width: 100%;
  height: auto;
  /* 禁用SVG内容选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  /* 禁用拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  /* 设置鼠标指针为默认 */
  cursor: default;
}

.mermaid-preview :deep(svg *) {
  /* 禁用SVG内部所有元素的选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.error-message,
.loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--q-text-color);
}

.large-image-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 100px);
  overflow: auto;
}

.large-mermaid-preview {
  /* 禁用文本选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.large-mermaid-preview :deep(svg) {
  max-width: 90vw;
  max-height: 90vh;
  /* 禁用SVG内容选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  /* 禁用拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  /* 设置鼠标指针为默认 */
  cursor: default;
}

.large-mermaid-preview :deep(svg *) {
  /* 禁用SVG内部所有元素的选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
</style>
