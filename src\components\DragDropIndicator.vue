<template>
  <div
    v-if="visible"
    class="drag-drop-indicator"
    :class="{
      'indicator--top': position === 'top',
      'indicator--bottom': position === 'bottom',
      'indicator--inside': position === 'inside',
    }"
    @dragover.prevent="handleDragOver"
    @drop="handleDrop"
    @dragleave="handleDragLeave"
  >
    <div class="indicator-line"></div>
    <div v-if="position === 'inside'" class="indicator-text">
      {{ $t('src.components.DragDropIndicator.dropHere') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });
interface Props {
  visible: boolean;
  position: 'top' | 'bottom' | 'inside';
  targetId: number;
  targetType: 'folder' | 'document';
}

interface Emits {
  (
    e: 'drop',
    data: { targetId: number; targetType: string; position: string; dragData: unknown },
  ): void;
  (e: 'dragOver'): void;
  (e: 'dragLeave'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move';
  }
  emit('dragOver');
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();

  try {
    const data = event.dataTransfer?.getData('text/plain');
    if (!data) return;

    const dragData = JSON.parse(data);

    console.log('🎯 [DragDropIndicator] 在占位符上放置:', {
      position: props.position,
      targetId: props.targetId,
      targetType: props.targetType,
      dragData,
    });

    emit('drop', {
      targetId: props.targetId,
      targetType: props.targetType,
      position: props.position,
      dragData,
    });
  } catch (error) {
    console.error('拖拽数据解析失败:', error);
  }
};

const handleDragLeave = (event: DragEvent) => {
  // 检查是否真正离开了指示器区域
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const x = event.clientX;
  const y = event.clientY;

  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    emit('dragLeave');
  }
};
</script>

<style scoped>
.drag-drop-indicator {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 1000;
  pointer-events: auto;
  cursor: copy;
  transition: all 0.2s ease;
}

.indicator--top {
  top: -12px;
  height: 12px;
}

.indicator--bottom {
  bottom: -12px;
  height: 12px;
}

.indicator--inside {
  top: 0;
  bottom: 0;
  background-color: rgba(25, 118, 210, 0.1);
  border: 2px dashed rgba(25, 118, 210, 0.5);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.indicator-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #1976d2;
  border-radius: 2px;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 4px rgba(25, 118, 210, 0.5);
}

.indicator--inside .indicator-line {
  display: none;
}

.indicator-text {
  color: #1976d2;
  font-size: 12px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #1976d2;
}

.drag-drop-indicator:hover {
  background-color: rgba(25, 118, 210, 0.05);
}

.indicator--inside:hover {
  background-color: rgba(25, 118, 210, 0.15);
}
</style>
