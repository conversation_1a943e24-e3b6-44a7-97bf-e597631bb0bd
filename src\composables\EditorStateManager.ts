import type { Editor } from '@tiptap/vue-3';
import { ref, computed, reactive, nextTick, readonly } from 'vue';
import { VirtualDragSystem } from './VirtualDragSystem';
import { MemoryManager } from './MemoryManager';
import { useResourceCleanup } from './useResourceCleanup';
import { isDevelopment } from '../config/performance.config';

/**
 * 编辑器状态管理器
 * 第二阶段性能优化：集中管理编辑器状态，减少状态分散和重复计算
 *
 * 核心功能：
 * 1. 集中状态管理 - 统一管理所有编辑器实例状态
 * 2. 状态缓存 - 智能缓存计算结果，避免重复计算
 * 3. 状态同步 - 自动同步编辑器状态变化
 * 4. 性能监控 - 监控状态变化性能影响
 * 5. 内存优化 - 自动清理无用状态，防止内存泄漏
 */

// 编辑器状态接口
export interface EditorState {
  id: string;
  editor: Editor | null;
  isActive: boolean;
  isFocused: boolean;
  documentChanged: boolean;
  lastChangeTime: number;
  changeCount: number;
  selectionStart: number;
  selectionEnd: number;
  wordCount: number;
  characterCount: number;
  isReadOnly: boolean;
  hasUnsavedChanges: boolean;
  errorState: string | null;
  performanceMetrics: {
    renderTime: number;
    updateCount: number;
    lastRenderTime: number;
  };
}

// 状态缓存接口
export interface StateCache {
  [key: string]: {
    value: unknown;
    timestamp: number;
    computeTime: number;
    hitCount: number;
  };
}

// 状态管理器配置
export interface StateManagerConfig {
  cacheTimeout: number; // 缓存超时时间(ms)
  maxCacheSize: number; // 最大缓存大小
  enablePerformanceTracking: boolean; // 是否启用性能跟踪
  autoCleanupInterval: number; // 自动清理间隔(ms)
  stateUpdateDebounce: number; // 状态更新防抖时间(ms)
}

// 默认配置
const DEFAULT_CONFIG: StateManagerConfig = {
  cacheTimeout: 30000, // 30秒
  maxCacheSize: 1000,
  enablePerformanceTracking: true,
  autoCleanupInterval: 60000, // 1分钟
  stateUpdateDebounce: 100, // 100ms
};

/**
 * 编辑器状态管理器类
 */
export class EditorStateManager {
  private states = reactive<Map<string, EditorState>>(new Map());
  private cache: StateCache = reactive({});
  private config: StateManagerConfig;
  private memoryManager: MemoryManager;
  private resourceCleanup: ReturnType<typeof useResourceCleanup>;
  private updateTimers = new Map<string, NodeJS.Timeout>();
  private cleanupTimer: NodeJS.Timeout | null = null;
  private virtualDragSystem: VirtualDragSystem | null = null;

  // 性能统计
  private stats = reactive({
    totalStates: 0,
    activeStates: 0,
    cacheHits: 0,
    cacheMisses: 0,
    totalUpdates: 0,
    averageUpdateTime: 0,
    memoryUsage: 0,
  });

  constructor(config: Partial<StateManagerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.memoryManager = new MemoryManager({
      // 移除不存在的配置项
    });
    this.resourceCleanup = useResourceCleanup();

    this.initialize();
  }

  /**
   * 初始化状态管理器
   */
  private initialize() {
    // 启动自动清理
    if (this.config.autoCleanupInterval > 0) {
      this.cleanupTimer = setInterval(() => {
        this.performCleanup();
      }, this.config.autoCleanupInterval);
    }

    // 注册资源清理
    this.resourceCleanup.addResource('editorStateManager', {
      cleanup: () => this.destroy(),
      priority: 'high',
      type: 'manager',
    });

    // 初始化虚拟拖拽系统
    this.virtualDragSystem = VirtualDragSystem.getInstance();

    console.log('editor state manager initialized');
  }

  /**
   * 注册编辑器实例
   */
  registerEditor(id: string, editor: Editor): void {
    if (this.states.has(id)) {
      console.warn(`editor instance ${id} already exists, will be updated`);
      this.unregisterEditor(id);
    }

    const state: EditorState = {
      id,
      editor,
      isActive: false,
      isFocused: false,
      documentChanged: false,
      lastChangeTime: Date.now(),
      changeCount: 0,
      selectionStart: 0,
      selectionEnd: 0,
      wordCount: 0,
      characterCount: 0,
      isReadOnly: false,
      hasUnsavedChanges: false,
      errorState: null,
      performanceMetrics: {
        renderTime: 0,
        updateCount: 0,
        lastRenderTime: Date.now(),
      },
    };

    this.states.set(id, state);
    this.stats.totalStates++;

    // 绑定编辑器事件
    this.bindEditorEvents(id, editor);

    // 初始化状态
    this.updateEditorState(id);

    console.log(`editor instance ${id} registered successfully`);
  }

  /**
   * 注销编辑器实例
   */
  unregisterEditor(id: string): void {
    const state = this.states.get(id);
    if (!state) return;

    // 清理定时器
    const timer = this.updateTimers.get(id);
    if (timer) {
      clearTimeout(timer);
      this.updateTimers.delete(id);
    }

    // 清理相关缓存
    this.clearCacheForEditor(id);

    // 移除状态
    this.states.delete(id);
    this.stats.totalStates--;

    console.log(`editor instance ${id} unregistered successfully`);
  }

  /**
   * 绑定编辑器事件
   */
  private bindEditorEvents(id: string, editor: Editor): void {
    const updateState = () => this.scheduleStateUpdate(id);

    // 监听内容变化
    editor.on('update', updateState);
    editor.on('selectionUpdate', updateState);
    editor.on('focus', () => {
      this.setEditorFocus(id, true);
      updateState();
    });
    editor.on('blur', () => {
      this.setEditorFocus(id, false);
      updateState();
    });

    // 监听事务
    editor.on('transaction', () => {
      const state = this.states.get(id);
      if (state) {
        state.changeCount++;
        state.lastChangeTime = Date.now();
        state.documentChanged = true;
        state.hasUnsavedChanges = true;
      }
    });
  }

  /**
   * 调度状态更新（防抖）
   */
  private scheduleStateUpdate(id: string): void {
    const existingTimer = this.updateTimers.get(id);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = setTimeout(() => {
      this.updateEditorState(id);
      this.updateTimers.delete(id);
    }, this.config.stateUpdateDebounce);

    this.updateTimers.set(id, timer);
  }

  /**
   * 更新编辑器状态
   */
  private updateEditorState(id: string): void {
    const state = this.states.get(id);
    if (!state || !state.editor) return;

    const startTime = performance.now();

    try {
      const { editor } = state;
      const selection = editor.state.selection;
      const doc = editor.state.doc;
      const text = doc.textContent;

      // 更新基本状态
      state.selectionStart = selection.from;
      state.selectionEnd = selection.to;
      state.characterCount = text.length;
      state.wordCount = text.split(/\s+/).filter((word) => word.length > 0).length;
      state.isReadOnly = !editor.isEditable;

      // 更新性能指标
      const renderTime = performance.now() - startTime;
      state.performanceMetrics.renderTime = renderTime;
      state.performanceMetrics.updateCount++;
      state.performanceMetrics.lastRenderTime = Date.now();

      // 更新统计
      this.stats.totalUpdates++;
      this.stats.averageUpdateTime = (this.stats.averageUpdateTime + renderTime) / 2;

      if (this.config.enablePerformanceTracking && renderTime > 16) {
        console.warn(`editor ${id} state update took ${renderTime.toFixed(2)}ms`);
      }
    } catch (error) {
      state.errorState = error instanceof Error ? error.message : String(error);
      console.error(`editor ${id} state update failed:`, error);
    }
  }

  /**
   * 设置编辑器焦点状态
   */
  setEditorFocus(id: string, focused: boolean): void {
    const state = this.states.get(id);
    if (!state) return;

    state.isFocused = focused;
    state.isActive = focused;

    // 更新活跃状态统计
    this.updateActiveStatesCount();
  }

  /**
   * 更新活跃状态计数
   */
  private updateActiveStatesCount(): void {
    this.stats.activeStates = Array.from(this.states.values()).filter(
      (state) => state.isActive,
    ).length;
  }

  /**
   * 获取编辑器状态
   */
  getEditorState(id: string): EditorState | null {
    return (this.states.get(id) as EditorState) || null;
  }

  /**
   * 获取所有编辑器状态
   */
  getAllStates(): EditorState[] {
    return Array.from(this.states.values()) as EditorState[];
  }

  /**
   * 获取活跃编辑器状态
   */
  getActiveStates(): EditorState[] {
    return Array.from(this.states.values()).filter((state) => state.isActive) as EditorState[];
  }

  /**
   * 缓存计算结果
   */
  private cacheResult<T>(key: string, computeFn: () => T): T {
    const cached = this.cache[key];
    const now = Date.now();

    // 检查缓存是否有效
    if (cached && now - cached.timestamp < this.config.cacheTimeout) {
      cached.hitCount++;
      this.stats.cacheHits++;
      return cached.value as T;
    }

    // 计算新值
    const startTime = performance.now();
    const value = computeFn();
    const computeTime = performance.now() - startTime;

    // 更新缓存
    this.cache[key] = {
      value,
      timestamp: now,
      computeTime,
      hitCount: cached ? cached.hitCount : 0,
    };

    this.stats.cacheMisses++;

    // 清理过大的缓存
    this.trimCache();

    return value;
  }

  /**
   * 清理缓存
   */
  private trimCache(): void {
    const cacheKeys = Object.keys(this.cache);
    if (cacheKeys.length <= this.config.maxCacheSize) return;

    // 按照命中次数和时间排序，删除最少使用的
    const sortedKeys = cacheKeys.sort((a, b) => {
      const itemA = this.cache[a];
      const itemB = this.cache[b];
      const scoreA = itemA.hitCount * 1000 + itemA.timestamp;
      const scoreB = itemB.hitCount * 1000 + itemB.timestamp;
      return scoreA - scoreB;
    });

    const keysToDelete = sortedKeys.slice(0, cacheKeys.length - this.config.maxCacheSize);
    keysToDelete.forEach((key) => delete this.cache[key]);
  }

  /**
   * 清理特定编辑器的缓存
   */
  private clearCacheForEditor(id: string): void {
    const keysToDelete = Object.keys(this.cache).filter((key) => key.includes(id));
    keysToDelete.forEach((key) => delete this.cache[key]);
  }

  /**
   * 获取编辑器字数统计（带缓存）
   */
  getWordCount(id: string): number {
    return this.cacheResult(`wordCount_${id}`, () => {
      const state = this.states.get(id);
      return state?.wordCount || 0;
    });
  }

  /**
   * 获取编辑器字符统计（带缓存）
   */
  getCharacterCount(id: string): number {
    return this.cacheResult(`charCount_${id}`, () => {
      const state = this.states.get(id);
      return state?.characterCount || 0;
    });
  }

  /**
   * 获取编辑器选区信息（带缓存）
   */
  getSelectionInfo(id: string): { from: number; to: number; length: number } {
    return this.cacheResult(`selection_${id}`, () => {
      const state = this.states.get(id);
      if (!state) return { from: 0, to: 0, length: 0 };

      return {
        from: state.selectionStart,
        to: state.selectionEnd,
        length: state.selectionEnd - state.selectionStart,
      };
    });
  }

  /**
   * 批量更新编辑器状态
   */
  async batchUpdateStates(ids: string[]): Promise<void> {
    const updates = ids.map(
      (id) =>
        new Promise<void>((resolve) => {
          void nextTick(() => {
            this.updateEditorState(id);
            resolve();
          });
        }),
    );

    await Promise.all(updates);
  }

  /**
   * 强制刷新所有状态
   */
  async refreshAllStates(): Promise<void> {
    const ids = Array.from(this.states.keys());
    await this.batchUpdateStates(ids);
  }

  /**
   * 执行清理操作
   */
  private performCleanup(): void {
    // 清理过期缓存
    const now = Date.now();
    const expiredKeys = Object.keys(this.cache).filter((key) => {
      const item = this.cache[key];
      return now - item.timestamp > this.config.cacheTimeout;
    });

    expiredKeys.forEach((key) => delete this.cache[key]);

    // 更新内存使用统计
    this.stats.memoryUsage = this.memoryManager.getCurrentUsage();

    // 触发内存管理器清理
    this.memoryManager.performCleanup();

    if (expiredKeys.length > 0) {
      console.log(`cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  /**
   * 设置批量更新延迟
   */
  setBatchDelay(delay: number): void {
    this.config.stateUpdateDebounce = delay;
  }

  /**
   * 启用持久化
   */
  enablePersistence(): void {
    // This is a placeholder - actual persistence would need to be implemented
    console.log('State persistence enabled');
  }

  /**
   * 持久化状态
   */
  persistState(): void {
    // Save current states to storage
    const statesToPersist = Array.from(this.states.entries()).map(([id, state]) => ({
      id,
      isActive: state.isActive,
      hasUnsavedChanges: state.hasUnsavedChanges,
      wordCount: state.wordCount,
      characterCount: state.characterCount,
    }));

    // In a real implementation, this would save to localStorage or a database
    if (isDevelopment) {
      console.log('Persisting states:', statesToPersist);
    }
  }

  /**
   * 压缩历史记录
   */
  compressHistory(): void {
    // This would compress the history of changes
    // For now, just clear old cache entries
    const now = Date.now();
    const keysToDelete = Object.keys(this.cache).filter((key) => {
      const item = this.cache[key];
      return now - item.timestamp > this.config.cacheTimeout * 2;
    });

    keysToDelete.forEach((key) => delete this.cache[key]);

    if (isDevelopment) {
      console.log(`Compressed history, removed ${keysToDelete.length} old cache entries`);
    }
  }

  /**
   * 批量回收非活跃实例
   */
  recycleInactiveInstances(): number {
    let recycled = 0;
    const now = Date.now();
    const inactiveThreshold = 60000; // 1 minute

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    for (const [id, state] of this.states) {
      if (!state.isActive && now - state.lastChangeTime > inactiveThreshold) {
        // Mark for recycling
        state.editor = null;
        recycled++;
      }
    }

    return recycled;
  }

  /**
   * 获取所有实例
   */
  getAllInstances() {
    return Array.from(this.states.values()).map((state) => ({
      id: state.id,
      status: state.isActive ? 'active' : 'idle',
      editor: state.editor,
    }));
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      ...this.stats,
      cacheSize: Object.keys(this.cache).length,
      cacheHitRatio: this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) || 0,
      memoryManager: this.memoryManager.getStats(),
    };
  }

  /**
   * 获取虚拟拖拽系统
   */
  getVirtualDragSystem(): VirtualDragSystem | null {
    return this.virtualDragSystem;
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats.cacheHits = 0;
    this.stats.cacheMisses = 0;
    this.stats.totalUpdates = 0;
    this.stats.averageUpdateTime = 0;
    console.log('performance stats reset');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<StateManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('state manager config updated', newConfig);
  }

  /**
   * 销毁状态管理器
   */
  destroy(): void {
    // 清理所有定时器
    this.updateTimers.forEach((timer) => clearTimeout(timer));
    this.updateTimers.clear();

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // 注销所有编辑器
    const editorIds = Array.from(this.states.keys());
    editorIds.forEach((id) => this.unregisterEditor(id));

    // 清理资源
    this.cache = reactive({});
    this.states.clear();
    this.memoryManager.destroy();

    if (this.virtualDragSystem) {
      this.virtualDragSystem.destroy();
      this.virtualDragSystem = null;
    }

    console.log('editor state manager destroyed');
  }
}

/**
 * 创建状态管理器实例
 */
export const createEditorStateManager = (config?: Partial<StateManagerConfig>) => {
  return new EditorStateManager(config);
};

/**
 * 全局状态管理器实例
 */
export const globalEditorStateManager = createEditorStateManager({
  enablePerformanceTracking: true,
  cacheTimeout: 30000,
  maxCacheSize: 500,
});

/**
 * 状态管理器组合式API
 */
export const useEditorStateManager = (config?: Partial<StateManagerConfig>) => {
  const manager = ref<EditorStateManager | null>(null);

  const initialize = () => {
    if (!manager.value) {
      manager.value = createEditorStateManager(config);
    }
    return manager.value;
  };

  const registerEditor = (id: string, editor: Editor) => {
    const stateManager = initialize();
    stateManager.registerEditor(id, editor);
  };

  const unregisterEditor = (id: string) => {
    manager.value?.unregisterEditor(id);
  };

  const getState = (id: string) => {
    return manager.value?.getEditorState(id) || null;
  };

  const getAllStates = () => {
    return manager.value?.getAllStates() || [];
  };

  const getStats = () => {
    return manager.value?.getPerformanceStats() || null;
  };

  const cleanup = () => {
    manager.value?.destroy();
    manager.value = null;
  };

  // 计算属性
  const totalEditors = computed(() => getAllStates().length);
  const activeEditors = computed(() => getAllStates().filter((s) => s.isActive).length);
  const hasUnsavedChanges = computed(() => getAllStates().some((s) => s.hasUnsavedChanges));

  return {
    manager: readonly(manager),
    initialize,
    registerEditor,
    unregisterEditor,
    getState,
    getAllStates,
    getStats,
    cleanup,
    totalEditors,
    activeEditors,
    hasUnsavedChanges,
  };
};

export default {
  EditorStateManager,
  createEditorStateManager,
  globalEditorStateManager,
  useEditorStateManager,
};
