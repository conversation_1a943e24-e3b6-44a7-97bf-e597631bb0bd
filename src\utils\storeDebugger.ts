/**
 * Store 调试工具，帮助追踪 Pinia 初始化错误
 */
import { getActivePinia } from 'pinia';

// 保存原始的 useStore 函数
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const originalUseStore = {
  useUiStore: null as unknown,
  useDocStore: null as unknown,
  useLlmStore: null as unknown,
};

// 错误追踪信息
const errorTraces: Array<{
  storeName: string;
  stack: string;
  timestamp: number;
}> = [];

/**
 * 包装 store 函数，添加错误追踪
 */
export function wrapStoreWithDebug<T extends (...args: unknown[]) => unknown>(
  storeName: string,
  useStoreFn: T,
): T {
  return ((...args: unknown[]) => {
    try {
      return useStoreFn(...args);
    } catch (error) {
      // 记录错误信息
      const trace = {
        storeName,
        stack: new Error().stack || '',
        timestamp: Date.now(),
      };
      errorTraces.push(trace);

      console.error(`[Store Debug] Failed to initialize ${storeName}:`, error);
      console.error('Stack trace:', trace.stack);

      // 打印所有错误追踪
      console.error('All store access attempts:', errorTraces);

      throw error;
    }
  }) as T;
}

/**
 * 获取错误追踪信息
 */
export function getStoreErrorTraces() {
  return [...errorTraces];
}

/**
 * 清除错误追踪
 */
export function clearStoreErrorTraces() {
  errorTraces.length = 0;
}

/**
 * 检查 Pinia 是否已初始化
 */
export function isPiniaInitialized(): boolean {
  try {
    // 尝试获取活跃的 Pinia 实例
    const pinia = getActivePinia();
    return !!pinia;
  } catch {
    return false;
  }
}

/**
 * 打印 Store 初始化状态
 */
export function printStoreInitStatus() {
  console.group('[Store Debug] Initialization Status');
  console.log('Pinia initialized:', isPiniaInitialized());
  console.log('Error traces:', errorTraces.length);

  if (errorTraces.length > 0) {
    console.group('Error Details');
    errorTraces.forEach((trace, index) => {
      console.log(`#${index + 1} ${trace.storeName} at ${new Date(trace.timestamp).toISOString()}`);
      console.log(trace.stack);
    });
    console.groupEnd();
  }

  console.groupEnd();
}

// 在开发环境下自动启用调试
if (process.env.NODE_ENV === 'development') {
  // 监听未捕获的错误
  if (typeof window !== 'undefined') {
    window.addEventListener('error', (event) => {
      if (event.error?.message?.includes('getActivePinia')) {
        console.error('[Store Debug] Caught Pinia initialization error');
        printStoreInitStatus();
      }
    });
  }
}
