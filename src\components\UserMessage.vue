<template>
  <q-item
    class="radius-xs border text-body-2 q-my-md q-mx-xs"
    :class="$q.dark.isActive ? 'bg-grey-9' : 'bg-grey-3'"
  >
    <q-item-section>
      <q-item-label>
        {{ message.content }}
      </q-item-label>
    </q-item-section>
  </q-item>
</template>
<script setup lang="ts">
import type { Message } from 'src/types/qwen';
import { useQuasar } from 'quasar';

const $q = useQuasar();

defineProps<{
  message: Message;
}>();
</script>
