# inkCop Qt 应用重构说明

## 项目结构优化

本次重构将原来的单一主窗口类拆分为多个独立的组件，提高了代码的可维护性和复用性。

### 文件结构

```
qt-src/
├── main.cpp                  # 应用程序入口
├── mainwindow.h              # 主窗口头文件（简化后）
├── mainwindow.cpp            # 主窗口实现（简化后）
├── customtoolbar.h           # 自定义工具栏头文件
├── customtoolbar.cpp         # 自定义工具栏实现
├── build.sh                  # 构建脚本
└── README.md                # 本文档
```

### 组件拆分详情

#### 1. CustomToolBar 类

- **文件**: `customtoolbar.h` / `customtoolbar.cpp`
- **职责**: 管理应用程序顶部工具栏的所有功能
- **包含功能**:
  - 应用图标和标题显示
  - 抽屉控制按钮（左侧/右侧）
  - 功能按钮（重载、开发工具、模式切换、主题切换）
  - 窗口控制按钮（最小化、最大化、关闭）
  - 主题样式管理
  - 按钮样式和交互效果

#### 2. MainWindow 类（简化后）

- **文件**: `mainwindow.h` / `mainwindow.cpp`
- **职责**: 主窗口核心功能管理
- **保留功能**:
  - WebEngineView 管理
  - 窗口事件处理（拖拽、大小调整）
  - JavaScript 接口
  - 主题系统集成
  - 应用生命周期管理

### 主要改进

1. **代码分离**: 工具栏相关代码完全独立，便于维护和测试
2. **信号槽机制**: 使用Qt信号槽机制进行组件间通信，降低耦合度
3. **主题管理**: 统一的主题管理接口，支持动态主题切换
4. **按钮管理**: 独立的按钮创建和样式管理方法
5. **事件处理**: 清晰的事件处理分工

### 信号槽连接

```cpp
// 在 MainWindow::setupToolBarConnections() 中建立连接
connect(m_customToolBar, &CustomToolBar::leftDrawerToggled,
        this, &MainWindow::toggleLeftDrawer);
connect(m_customToolBar, &CustomToolBar::reloadRequested,
        this, &MainWindow::reloadPage);
// ... 其他连接
```

### 构建说明

#### 使用构建脚本（推荐）

```bash
cd qt-src
./build.sh
```

#### 手动使用 CMake 构建

```bash
mkdir build
cd build
cmake ..
make
```

### 依赖要求

- Qt 6.x
- Qt WebEngine
- Qt WebChannel
- C++17 标准

### 扩展性

新的架构使得添加新功能更加容易：

1. **新工具栏按钮**: 在 CustomToolBar 中添加新按钮和对应信号
2. **新窗口组件**: 可以创建类似 CustomToolBar 的独立组件
3. **主题扩展**: 通过 updateTheme() 接口轻松添加新主题支持

### 测试建议

1. **单元测试**: 可以独立测试 CustomToolBar 组件
2. **集成测试**: 测试主窗口与工具栏的交互
3. **主题测试**: 验证明暗主题切换功能
4. **按钮测试**: 确保所有按钮功能正常

这次重构大大提高了代码的模块化程度，为后续功能扩展奠定了良好的基础。

# Qt 源码模块

这是 InkCop 的 Qt 桌面应用程序源码目录。

## 项目结构

- `main.cpp` - 应用程序入口点
- `mainwindow.h/cpp` - 主窗口实现
- `customwebengineview.h/cpp` - 自定义 Web 引擎视图
- `databaseapi.h/cpp` - 数据库 API 接口
- `knowledgeapi.h/cpp` - 知识库 API 接口
- `windowapi.h/cpp` - 窗口 API 接口
- `settings.h/cpp` - 应用设置管理
- `objectbox/` - ObjectBox 数据库相关文件

## 编译和运行

使用项目根目录下的构建脚本：

```bash
# 开发模式（包含调试信息）
./build-qt-dev.sh

# 生产模式
./build-qt.sh
```

## Qt 日志配置

### 字体调试信息问题

在启动Qt应用时，您可能会看到大量类似以下的字体相关调试信息：

```
qt.text.font.db: Adding font: familyName "Noto Sans Devanagari" stylename "SemiBold" weight 600 style QFont::StyleNormal pixelSize 0 antialiased true fixed false colorFont false
```

这些是Qt字体数据库的调试输出，不是错误，但会污染控制台输出。

### 解决方案

应用已经配置了日志过滤规则来禁用这些冗余输出：

1. **代码级别配置**：在 `main.cpp` 中设置了 `QLoggingCategory::setFilterRules()`
2. **环境变量配置**：构建脚本中设置了 `QT_LOGGING_RULES` 环境变量
3. **配置文件**：可以通过 `qt-logging.rules` 文件自定义日志规则

### 自定义日志配置

在项目根目录创建 `qt-logging.rules` 文件，可以精细控制日志输出：

```
# 禁用字体相关调试信息
qt.text.font.db=false
qt.text.font=false

# 禁用Qt系统调试信息
qt.*.debug=false

# 保留警告和错误
*.warning=true
*.critical=true

# 允许应用自定义日志
inkcop.*=true
```

### 环境变量方式

也可以通过环境变量临时控制：

```bash
export QT_LOGGING_RULES="qt.text.font.db=false;qt.*.debug=false;*.warning=true;*.critical=true"
./build/bin/InkCop
```

这样就可以有效减少控制台输出中的冗余信息，专注于应用的实际日志和调试信息。

## API 接口

Qt 应用通过以下 API 类与 Quasar 前端通信：

- `DatabaseAPI` - 数据库操作接口
- `KnowledgeAPI` - 知识库管理接口
- `WindowAPI` - 窗口控制接口

这些 API 通过 `QWebChannel` 暴露给前端 JavaScript 环境。
