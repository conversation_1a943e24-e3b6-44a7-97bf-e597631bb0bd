# Qt WebEngine 渲染配置文件 - Windows平台专用
# Qt 6.9.1 测试配置 - 启用之前禁用的特性以测试升级后的改进
# 测试是否已解决UI渲染错误和像素破碎问题

[GPU_RENDERING]
# 是否启用硬件加速 (true/false)
# Qt 6.9.1测试：启用硬件加速以测试改进
enable_hardware_acceleration=true

# 是否启用2D Canvas硬件加速 (true/false)
# Qt 6.9.1测试：启用2D Canvas加速以测试像素破碎问题是否已解决
enable_2d_canvas_acceleration=true

# 是否启用WebGL (true/false)
# Qt 6.9.1测试：启用WebGL以测试GPU驱动兼容性改进
enable_webgl=true

# 是否启用滚动动画 (true/false)
# Qt 6.9.1测试：启用滚动动画以测试渲染撕裂问题是否已解决
enable_scroll_animation=true

[CHROMIUM_FLAGS]
# Windows平台专用Chromium标志 - Qt 6.9.1测试配置
# 启用之前禁用的特性以测试Qt 6.9.1的改进
# 仅保留必要的安全相关禁用选项
custom_flags=--disable-features=TranslateUI --enable-gpu-rasterization --enable-oop-rasterization --enable-zero-copy --enable-gpu-memory-buffer-video-frames

[DISPLAY]
# 缩放因子 (0.5-3.0)
# 设置为1.0以避免缩放相关的渲染问题
zoom_factor=1.0

# 是否启用高DPI支持 (true/false)
enable_high_dpi=true

[PERFORMANCE]
# 内存缓存大小 (MB)
memory_cache_size=150

# 磁盘缓存大小 (MB)
disk_cache_size=300

# 是否启用JavaScript (true/false)
enable_javascript=true

# 是否自动加载图片 (true/false)
auto_load_images=true

[WINDOWS_NATIVE]
# Windows平台原生窗口效果设置 - Qt 6.9.1测试配置
# 启用这些效果以测试Qt 6.9.1是否改善了兼容性

# 是否启用窗口阴影 (true/false)
# Qt 6.9.1测试：启用以测试渲染冲突是否已解决
enable_window_shadow=true

# 是否启用圆角窗口 (true/false) - 仅Windows 11
# Qt 6.9.1测试：启用以测试WebEngine兼容性改进
enable_rounded_corners=true

# 是否启用DWM合成 (true/false)
# Qt 6.9.1测试：启用以测试渲染冲突是否已解决
enable_dwm_composition=true

[NETWORK_ACCESS]
# 网络访问配置选项
# 用于解决生产环境中访问本地服务（如Ollama）的问题

# 是否禁用Web安全策略 (true/false)
# 启用此选项可以解决CORS问题，但会降低安全性
disable_web_security=true

# 是否允许运行不安全内容 (true/false)
# 允许HTTPS页面加载HTTP资源
allow_running_insecure_content=true

# 是否启用本地内容访问远程URL (true/false)
# 允许qrc://协议的页面访问http://localhost等本地服务
local_content_can_access_remote_urls=true

# 是否启用本地内容访问文件URL (true/false)
# 允许访问本地文件系统
local_content_can_access_file_urls=true

[TROUBLESHOOTING]
# 故障排除选项 - Qt 6.9.1测试配置

# 强制软件渲染 (true/false)
# Qt 6.9.1测试：禁用强制软件渲染，允许硬件渲染
force_software_rendering=false

# 禁用GPU线程 (true/false)
# Qt 6.9.1测试：启用GPU线程以测试多线程渲染改进
disable_gpu_thread=false

# 启用详细日志 (true/false)
# 启用以观察Qt 6.9.1的渲染行为
enable_verbose_logging=true

# 禁用Windows原生效果 (true/false)
# Qt 6.9.1测试：允许Windows原生效果以测试兼容性改进
disable_native_effects=false

# 注意事项:
# 1. 修改此文件后需要重启应用程序
# 2. 如果遇到严重渲染问题，建议将所有硬件加速选项设置为false
# 3. 在高性能GPU上，可以尝试启用部分硬件加速功能
# 4. 如果应用运行正常，不建议修改默认设置
