export function useUtils() {
  /**
   * 安全地解析 JSON 字符串
   * @param json JSON 字符串
   * @param defaultValue 解析失败时的默认值
   */
  const safeJsonParse = <T>(json: string | null | undefined, defaultValue: T): T => {
    if (!json) return defaultValue;
    try {
      return JSON.parse(json) as T;
    } catch (e) {
      console.warn('JSON parse failed:', e);
      return defaultValue;
    }
  };

  /**
   * 安全地序列化对象为 JSON 字符串
   * @param data 要序列化的数据
   * @param defaultValue 序列化失败时的默认值
   * @param maxSizeBytes 最大允许的字节大小（默认50MB）
   */
  const safeJsonStringify = <T>(
    data: T,
    defaultValue: string = '{}',
    maxSizeBytes: number = 50 * 1024 * 1024, // 50MB
  ): string => {
    try {
      // 首先尝试序列化
      const jsonString = JSON.stringify(data);

      // 检查生成的字符串大小
      const sizeBytes = new Blob([jsonString]).size;

      if (sizeBytes > maxSizeBytes) {
        const sizeMB = (sizeBytes / (1024 * 1024)).toFixed(2);
        const maxSizeMB = (maxSizeBytes / (1024 * 1024)).toFixed(2);

        console.error(`JSON stringify failed: Content too large (${sizeMB}MB > ${maxSizeMB}MB)`);
        throw new Error(`内容过大无法保存：${sizeMB}MB 超过限制 ${maxSizeMB}MB`);
      }

      return jsonString;
    } catch (e) {
      if (e instanceof Error) {
        // 检查是否是字符串长度错误
        if (e.message.includes('Invalid string length') || e.message.includes('string too long')) {
          console.error('JSON stringify failed: String length exceeded JavaScript limit');
          throw new Error('文档内容过大，超过了系统限制，请减少内容后重试');
        }
        // 检查是否是我们自定义的大小错误
        if (e.message.includes('内容过大无法保存')) {
          throw e; // 重新抛出我们的自定义错误
        }
      }

      console.warn('JSON stringify failed:', e);
      return defaultValue;
    }
  };
  // 定义TipTap节点类型
  interface TipTapNode {
    type: string;
    text?: string;
    content?: TipTapNode[];
    [key: string]: unknown;
  }

  const extractTiptapContentByJson = (jsonString: string): string => {
    try {
      const json = JSON.parse(jsonString);

      // 递归提取文本内容的函数
      const extractTextFromNode = (node: TipTapNode): string => {
        if (!node || typeof node !== 'object') {
          return '';
        }

        let text = '';

        // 如果是文本节点，直接返回文本内容
        if (node.type === 'text' && node.text) {
          return node.text;
        }

        // 如果节点有content数组，递归处理每个子节点
        if (node.content && Array.isArray(node.content)) {
          for (const childNode of node.content) {
            const childText = extractTextFromNode(childNode);
            if (childText) {
              // 根据节点类型添加适当的分隔符
              if (node.type === 'paragraph' || node.type === 'heading') {
                text += childText + '\n';
              } else if (node.type === 'listItem') {
                text += '• ' + childText + '\n';
              } else if (node.type === 'blockquote') {
                text += '> ' + childText + '\n';
              } else {
                text += childText;
              }
            }
          }
        }

        // 处理特殊节点类型
        switch (node.type) {
          case 'hardBreak':
            return '\n';
          case 'horizontalRule':
            return '\n---\n';
          case 'codeBlock':
            // 代码块保持原格式
            if (node.content) {
              const codeText = node.content
                .map((child: TipTapNode) => extractTextFromNode(child))
                .join('');
              return '```\n' + codeText + '\n```\n';
            }
            break;
          case 'table':
            // 表格处理 - 添加换行
            return text + '\n';
          case 'tableRow':
            // 表格行处理 - 添加换行
            return text + '\n';
          case 'tableCell':
          case 'tableHeader':
            // 表格单元格处理 - 添加制表符分隔
            return text + '\t';
          default:
            break;
        }

        return text;
      };

      // 从根节点开始提取文本
      const extractedText = extractTextFromNode(json);

      // 清理多余的空白字符
      return extractedText
        .replace(/\n{3,}/g, '\n\n') // 将连续3个以上换行替换为2个
        .replace(/\t+/g, ' ') // 将制表符替换为空格
        .trim(); // 去除首尾空白
    } catch (error) {
      console.error('parse TipTap JSON content failed:', error);
      return '';
    }
  };

  /**
   * 估算对象序列化后的大小（字节）
   * @param data 要估算的数据
   * @returns 估算的字节大小，如果无法估算则返回 -1
   */
  const estimateJsonSize = <T>(data: T): number => {
    try {
      // 对于简单估算，我们可以先转换为字符串然后计算大小
      const jsonString = JSON.stringify(data);
      return new Blob([jsonString]).size;
    } catch (e) {
      console.warn('无法估算对象大小:', e);
      return -1;
    }
  };

  /**
   * 检查内容是否过大
   * @param data 要检查的数据
   * @param maxSizeBytes 最大允许大小（字节）
   * @returns 检查结果
   */
  const checkContentSize = <T>(data: T, maxSizeBytes: number = 50 * 1024 * 1024) => {
    const size = estimateJsonSize(data);
    if (size === -1) {
      return { isValid: false, size: -1, error: '无法估算内容大小' };
    }

    const isValid = size <= maxSizeBytes;
    const sizeMB = (size / (1024 * 1024)).toFixed(2);
    const maxSizeMB = (maxSizeBytes / (1024 * 1024)).toFixed(2);

    return {
      isValid,
      size,
      sizeMB,
      maxSizeMB,
      error: isValid ? null : `内容过大：${sizeMB}MB 超过限制 ${maxSizeMB}MB`,
    };
  };

  return {
    safeJsonParse,
    safeJsonStringify,
    extractTiptapContentByJson,
    estimateJsonSize,
    checkContentSize,
  };
}
