import type { Message, ToolCall } from 'src/types/azureOpenai';
import type { AssistantMessage as AssistantMessageType } from 'src/types/openai';
import { computed, type Ref } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import { useUiStore } from 'src/stores/ui';
import { DEFAULT_AZURE_OPENAI_SETTINGS } from 'src/config/defaultSettings';
import { executeToolCall } from 'src/llm/tools/index';
import { PromptService, type ConversationRole } from 'src/services/promptService';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import { $t } from 'src/composables/useTrans';

export const useAzureOpenAI = () => {
  const { updateConversation } = useSqlite();
  const uiStore = useUiStore();

  const azureOpenaiSettings = computed(() => {
    return uiStore.perferences?.llm?.azureOpenai || DEFAULT_AZURE_OPENAI_SETTINGS;
  });

  const readSettings = () => {
    console.log('[useAzureOpenAI] 设置已自动同步:', azureOpenaiSettings.value);
  };

  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const { messages, messagesRef, conversation, tools, enableTools, abortController } = params;

    if (!azureOpenaiSettings.value) return;

    // 验证必要的 Azure 配置
    if (!azureOpenaiSettings.value.deploymentName) {
      throw new Error('Azure OpenAI 部署名称未配置');
    }

    console.log('[useAzureOpenAI] 开始发送消息');
    console.log('[useAzureOpenAI] 消息数量:', messages.length);
    console.log('[useAzureOpenAI] 工具数量:', tools.length);
    console.log('[useAzureOpenAI] 启用工具:', enableTools);

    loading.value = true;

    try {
      // 构建 Azure OpenAI 端点 URL
      const endpoint = `${azureOpenaiSettings.value.baseUrl}/openai/deployments/${azureOpenaiSettings.value.deploymentName}/chat/completions?api-version=${azureOpenaiSettings.value.apiVersion}`;

      // 构建请求头
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'api-key': azureOpenaiSettings.value.apiKey,
      };

      // 构建请求体（与 OpenAI 兼容）
      const requestBody: {
        messages: Message[];
        max_tokens?: number | null;
        temperature?: number;
        top_p?: number;
        frequency_penalty?: number;
        presence_penalty?: number;
        stream: boolean;
        stream_options?: { include_usage?: boolean };
        tools?: Array<{
          type: 'function';
          function: {
            name: string;
            description?: string;
            parameters: Record<string, unknown>;
          };
        }>;
        tool_choice?: 'auto' | 'none' | { type: 'function'; function: { name: string } };
      } = {
        messages: messages as Message[],
        frequency_penalty: azureOpenaiSettings.value.frequencyPenalty,
        presence_penalty: azureOpenaiSettings.value.presencePenalty,
        stream: true,
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (
        azureOpenaiSettings.value.temperature !== undefined &&
        !isNaN(azureOpenaiSettings.value.temperature)
      ) {
        requestBody.temperature = azureOpenaiSettings.value.temperature;
      }
      if (azureOpenaiSettings.value.topP !== undefined && !isNaN(azureOpenaiSettings.value.topP)) {
        requestBody.top_p = azureOpenaiSettings.value.topP;
      }

      // 添加 max_tokens 如果设置了
      if (azureOpenaiSettings.value.maxTokens) {
        requestBody.max_tokens = azureOpenaiSettings.value.maxTokens;
      }

      // 添加流式选项
      if (azureOpenaiSettings.value.stream) {
        requestBody.stream_options = { include_usage: true };
      }

      // 如果有启用的工具，添加工具定义
      if (enableTools && tools.length > 0) {
        console.log(`🔧 [useAzureOpenAI] 启用工具数: ${tools.length}`);
        console.log(
          `🔧 [useAzureOpenAI] 工具列表: ${tools.map((t) => t.function.name).join(', ')}`,
        );

        requestBody.tools = tools as Array<{
          type: 'function';
          function: {
            name: string;
            description?: string;
            parameters: Record<string, unknown>;
          };
        }>;
        requestBody.tool_choice = 'auto';
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `Azure OpenAI API error: ${errorData.error?.message || response.statusText}`,
        );
      }

      if (!response.body) {
        throw new Error('ReadableStream not supported');
      }

      // 直接操作响应式消息数组，实现流式更新
      messagesRef.value.push({
        role: 'assistant',
        content: '',
      });
      const lastMessage = messagesRef.value[messagesRef.value.length - 1];

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      const toolCallsBuffer: Record<string, ToolCall> = {};

      while (true) {
        // 检查是否被中断
        if (abortController?.signal.aborted) {
          void reader.cancel();
          throw new DOMException('Operation was aborted', 'AbortError');
        }

        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              const choice = parsed.choices?.[0];

              if (choice?.delta) {
                // 处理文本内容
                if (choice.delta.content) {
                  (lastMessage as AssistantMessageType).content += choice.delta.content;
                }

                // 处理工具调用
                if (choice.delta.tool_calls) {
                  for (const toolCallDelta of choice.delta.tool_calls) {
                    const index = toolCallDelta.index;
                    const bufferKey = `tool_${index}`;

                    if (!toolCallsBuffer[bufferKey]) {
                      toolCallsBuffer[bufferKey] = {
                        id: toolCallDelta.id || '',
                        type: 'function',
                        function: {
                          name: '',
                          arguments: '',
                        },
                      };
                    }

                    if (toolCallDelta.id) {
                      toolCallsBuffer[bufferKey].id = toolCallDelta.id;
                    }

                    if (toolCallDelta.function?.name) {
                      toolCallsBuffer[bufferKey].function.name = toolCallDelta.function.name;
                    }

                    if (toolCallDelta.function?.arguments) {
                      toolCallsBuffer[bufferKey].function.arguments +=
                        toolCallDelta.function.arguments;
                    }
                  }
                }
              }

              // 检查是否完成
              if (choice?.finish_reason) {
                console.log('[useAzureOpenAI] 消息生成完成，原因:', choice.finish_reason);
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }

      // 处理完整的工具调用
      const toolCalls = Object.values(toolCallsBuffer);
      if (toolCalls.length > 0 && enableTools) {
        console.log('完整的工具调用列表:', toolCalls);

        // 验证工具调用的完整性
        toolCalls.forEach((toolCall, index) => {
          console.log(`工具调用 ${index + 1}:`, {
            id: toolCall.id,
            functionName: toolCall.function.name,
            argumentsLength: toolCall.function.arguments.length,
            arguments: toolCall.function.arguments,
          });
        });

        // 将工具调用添加到助手消息中
        (lastMessage as AssistantMessageType).tool_calls = toolCalls;

        // 执行工具调用并添加结果消息
        for (const toolCall of toolCalls) {
          try {
            // 验证工具调用完整性
            if (!toolCall.function.name) {
              throw new Error('工具函数名称为空');
            }

            // 解析工具参数
            let parameters = {};
            if (toolCall.function.arguments) {
              try {
                parameters = JSON.parse(toolCall.function.arguments);
                console.log(`解析工具参数成功 - ${toolCall.function.name}:`, parameters);
              } catch (parseError) {
                console.error(`工具参数解析失败 - ${toolCall.function.name}:`, {
                  rawArguments: toolCall.function.arguments,
                  error: parseError,
                });
                throw new Error(`工具参数格式错误: ${parseError.message}`);
              }
            }

            // 执行工具
            console.log(`开始执行工具: ${toolCall.function.name}`, parameters);
            const toolResult = await executeToolCall(toolCall.function.name, parameters);

            // 添加工具结果消息
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify(toolResult),
              tool_call_id: toolCall.id,
            });

            console.log(`工具执行成功 ${toolCall.function.name}:`, {
              parameters,
              result: toolResult,
            });
          } catch (error) {
            console.error(`工具执行失败 ${toolCall.function.name}:`, {
              error: error.message,
              functionName: toolCall.function.name,
              rawArguments: toolCall.function.arguments,
            });

            // 添加错误结果
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify({
                success: false,
                message: $t('src.composeables.useAzureOpenAI.toolExecutionFailed', {
                  error: error.message,
                }),
                toolName: toolCall.function.name,
              }),
              tool_call_id: toolCall.id,
            });
          }
        }

        // 如果有工具调用，递归调用获取AI对工具结果的响应
        console.log('工具执行完成，开始递归调用获取AI响应...');
        console.log('当前消息数量:', messages.length);
        console.log(
          '最后几条消息:',
          messages.slice(-3).map((m) => ({
            role: m.role,
            content:
              typeof m.content === 'string'
                ? m.content.substring(0, 100)
                : JSON.stringify(m.content).substring(0, 100),
          })),
        );

        // 递归调用以获取最终响应
        console.log('🔄 [useAzureOpenAI] 递归调用获取最终响应');
        const recursiveParams: SimplifiedLlmParams = {
          ...params,
          messages: messagesRef.value,
        };
        await sendMessage(recursiveParams, loading);
        return;
      }
      console.log('[useAzureOpenAI] 消息发送完成');
    } catch (error) {
      console.error('[useAzureOpenAI] Error sending message:', error);
      messagesRef.value.push({
        role: 'assistant',
        content: $t('src.composeables.useAzureOpenAI.errorOccurred'),
      });
    } finally {
      loading.value = false;

      await updateConversation(
        conversation.id,
        conversation.title,
        JSON.stringify(messagesRef.value),
        conversation.prompt,
      );
    }
  };

  return {
    readSettings,
    azureOpenaiSettings,
    sendMessage,
    // 导出prompt服务的方法
    getAvailableRoles: () => PromptService.getAvailableRoles(),
    getRoleSuggestions: (role: ConversationRole) => PromptService.getRoleSuggestions(role),
  };
};
