import { Node, mergeAttributes } from '@tiptap/core';
import { VueNodeViewRenderer } from '@tiptap/vue-3';
import AgentWriterView from './AgentWriterView.vue';
import { Plugin } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    agentWriter: {
      insertAgentWriter: () => ReturnType;
    };
  }
}

export const AgentWriter = Node.create({
  name: 'agentWriter',
  group: 'block',
  atom: true,
  selectable: false,
  draggable: false,

  addAttributes() {
    return {
      id: {
        default: () => Math.random().toString(36).slice(2),
      },
      selectedText: {
        default: null,
      },
      currentText: {
        default: null,
      },
      prevNodesText: {
        default: [],
      },
      nextNodesText: {
        default: [],
      },
      selectedNodePos: {
        default: null,
      },
      selectedNodeSize: {
        default: null,
      },
      insertPos: {
        default: null,
      },
      currentNodePos: {
        default: null,
      },
      originalInsertPos: {
        default: null,
      },
    };
  },

  addCommands() {
    return {
      insertAgentWriter:
        () =>
        ({ editor, chain }) => {
          const { state } = editor;
          const { selection } = state;
          const { $from, $to } = selection;

          // 记录原始光标位置，使用 resolvedPos 来获取更准确的位置
          const resolvedPos = state.doc.resolve(selection.from);
          const originalInsertPos = resolvedPos.pos;

          // 找到当前光标所在节点的位置
          const currentNodePos = $from.before(1);

          // 获取选中的文本
          const selectedText = selection.empty ? null : state.doc.textBetween($from.pos, $to.pos);

          // 获取当前节点的内容
          const currentNode = state.doc.nodeAt(currentNodePos);
          const currentText = currentNode?.textContent || null;

          // 获取前两个非空内容节点的文本
          const prevNodesText = [];
          let pos = currentNodePos;
          let count = 0;
          while (pos > 0 && count < 2) {
            const resolvedPos = state.doc.resolve(pos);
            // 检查是否已经到达文档开始
            if (resolvedPos.depth === 0 && resolvedPos.pos === 0) break;

            // 获取前一个节点的位置
            const prevPos = resolvedPos.before(1);
            // 检查是否已经到达文档开始
            if (prevPos < 0) break;

            const node = state.doc.nodeAt(prevPos);
            if (node && node.textContent.trim()) {
              prevNodesText.unshift(node.textContent);
              count++;
            }
            pos = prevPos;
          }

          // 获取后两个非空内容节点的文本
          const nextNodesText = [];
          pos = currentNodePos + (currentNode?.nodeSize || 0);
          count = 0;
          while (pos < state.doc.content.size && count < 2) {
            const node = state.doc.nodeAt(pos);
            // 检查是否已经到达文档结束
            if (!node) break;

            if (node.textContent.trim()) {
              nextNodesText.push(node.textContent);
              count++;
            }
            // 移动到下一个节点
            const nextPos = pos + node.nodeSize;
            // 检查是否已经到达文档结束
            if (nextPos >= state.doc.content.size) break;
            pos = nextPos;
          }

          // 记录选中文本的位置和大小
          const selectedNodePos = selection.empty ? null : $from.pos;
          const selectedNodeSize = selection.empty ? null : $to.pos - $from.pos;

          // 计算插入后 agentWriter 节点的插入点
          let adjustedInsertPos = originalInsertPos;
          if (originalInsertPos > currentNodePos) {
            // 由于插入节点后，原始位置会后移一个节点大小（atom block节点通常为1）
            adjustedInsertPos = originalInsertPos + 1;
          }

          // 如果当前节点是文本节点，需要调整位置以考虑字符的实际宽度
          if (currentNode?.isText) {
            const nodeStart = currentNodePos + 1; // +1 是因为节点类型占用一个位置
            const relativePos = originalInsertPos - nodeStart;
            const textBeforeCursor = currentText.slice(0, relativePos);
            // 计算实际字符数（考虑中文字符）
            const actualCharCount = [...textBeforeCursor].length;
            // 调整位置
            adjustedInsertPos = nodeStart + actualCharCount;
          }

          const attrs = {
            id: Math.random().toString(36).slice(2),
            selectedText,
            currentText,
            prevNodesText,
            nextNodesText,
            selectedNodePos,
            selectedNodeSize,
            insertPos: adjustedInsertPos,
            currentNodePos,
            originalInsertPos,
          };
          console.log('attrs', attrs);

          // 在当前节点之前插入 agentWriter 节点，并直接写入 insertPos
          return chain()
            .insertContentAt(currentNodePos, {
              type: this.name,
              attrs,
            })
            .run();
        },
    };
  },

  parseHTML() {
    return [{ tag: 'div[data-agent-writer]' }];
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-agent-writer': 'true' }), 0];
  },

  addNodeView() {
    return VueNodeViewRenderer(AgentWriterView);
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        props: {
          decorations: (state) => {
            const decorations = [];
            state.doc.descendants((node /*, pos*/) => {
              if (node.type.name === 'agentWriter') {
                const offset = 1; // agentWriter 节点插入后占有1个位置，这里需要补偿
                const { originalInsertPos, selectedNodePos, selectedNodeSize } = node.attrs;
                if (
                  selectedNodePos !== undefined &&
                  selectedNodePos !== null &&
                  selectedNodeSize !== undefined &&
                  selectedNodeSize !== null
                ) {
                  // 有选区：高亮选区并在上方加标记
                  decorations.push(
                    Decoration.inline(
                      selectedNodePos + offset,
                      selectedNodePos + selectedNodeSize + offset,
                      {
                        class: 'agentwriter-selection-highlight',
                      },
                    ),
                    Decoration.widget(originalInsertPos + offset, () => {
                      const wrapper = document.createElement('span');
                      wrapper.className = 'agentwriter-range';
                      return wrapper;
                    }),
                  );
                } else if (originalInsertPos !== undefined && originalInsertPos !== null) {
                  // 无选区：插入自定义flag占位
                  decorations.push(
                    Decoration.widget(originalInsertPos + offset, () => {
                      const flag = document.createElement('span');
                      flag.className = 'agentwriter-flag';
                      return flag;
                    }),
                  );
                }
              }
            });
            return DecorationSet.create(state.doc, decorations);
          },
        },
      }),
    ];
  },
});
