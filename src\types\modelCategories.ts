/**
 * 模型分类类型定义
 * 用于对LLM供应商的可用模型进行分类管理
 */

/**
 * 模型类型枚举
 */
export enum ModelType {
  /** 文本生成 */
  TEXT_GENERATION = 'textGeneration',
  /** 全模态 */
  MULTIMODAL = 'multimodal',
  /** 推理模型 */
  REASONING = 'reasoning',
  /** 音频理解 */
  AUDIO_UNDERSTANDING = 'audioUnderstanding',
  /** 视频理解 */
  VIDEO_UNDERSTANDING = 'videoUnderstanding',
  /** 视频生成 */
  VIDEO_GENERATION = 'videoGeneration',
  /** 图片处理 */
  IMAGE_PROCESSING = 'imageProcessing',
  /** 图片理解 */
  IMAGE_UNDERSTANDING = 'imageUnderstanding',
  /** 图片生成 */
  IMAGE_GENERATION = 'imageGeneration',
  /** 向量模型 */
  EMBEDDING = 'embedding',
  /** 语音合成 */
  TEXT_TO_SPEECH = 'textToSpeech',
  /** 语音识别 */
  SPEECH_TO_TEXT = 'speechToText',
  /** 排序模型 */
  RANKING = 'ranking',
  /** 工具调用 */
  TOOLS_CALLS = 'toolsCalls',
}

/**
 * 模型类型显示名称映射
 */
export const ModelTypeLabels: Record<ModelType, string> = {
  [ModelType.TEXT_GENERATION]: '文本生成',
  [ModelType.MULTIMODAL]: '全模态',
  [ModelType.REASONING]: '推理模型',
  [ModelType.AUDIO_UNDERSTANDING]: '音频理解',
  [ModelType.VIDEO_UNDERSTANDING]: '视频理解',
  [ModelType.VIDEO_GENERATION]: '视频生成',
  [ModelType.IMAGE_PROCESSING]: '图片处理',
  [ModelType.IMAGE_UNDERSTANDING]: '图片理解',
  [ModelType.IMAGE_GENERATION]: '图片生成',
  [ModelType.EMBEDDING]: '向量模型',
  [ModelType.TEXT_TO_SPEECH]: '语音合成',
  [ModelType.SPEECH_TO_TEXT]: '语音识别',
  [ModelType.RANKING]: '排序模型',
  [ModelType.TOOLS_CALLS]: '工具调用',
};

/**
 * 按类型分组的模型结构
 */
export interface CategorizedModels {
  [ModelType.TEXT_GENERATION]?: string[];
  [ModelType.MULTIMODAL]?: string[];
  [ModelType.REASONING]?: string[];
  [ModelType.AUDIO_UNDERSTANDING]?: string[];
  [ModelType.VIDEO_UNDERSTANDING]?: string[];
  [ModelType.VIDEO_GENERATION]?: string[];
  [ModelType.IMAGE_PROCESSING]?: string[];
  [ModelType.IMAGE_UNDERSTANDING]?: string[];
  [ModelType.IMAGE_GENERATION]?: string[];
  [ModelType.EMBEDDING]?: string[];
  [ModelType.TEXT_TO_SPEECH]?: string[];
  [ModelType.SPEECH_TO_TEXT]?: string[];
  [ModelType.RANKING]?: string[];
  [ModelType.TOOLS_CALLS]?: string[];
}

/**
 * 对话组件中需要显示的模型类型
 */
export const CONVERSATION_MODEL_TYPES = [
  ModelType.TEXT_GENERATION,
  ModelType.REASONING,
  ModelType.MULTIMODAL,
] as const;

/**
 * 自动补全需要的模型类型
 */
export const AUTOCOMPLETE_MODEL_TYPES = [ModelType.TEXT_GENERATION, ModelType.REASONING] as const;
export const FLOATAGENT_MODEL_TYPES = [ModelType.TEXT_GENERATION, ModelType.REASONING] as const;

/**
 * 知识库嵌入需要的模型类型
 */
export const EMBEDDING_MODEL_TYPES = [ModelType.EMBEDDING] as const;

/**
 * 工具函数：获取指定类型的所有模型
 */
export function getModelsByType(
  categorizedModels: CategorizedModels,
  types: readonly ModelType[],
): string[] {
  const models: string[] = [];

  types.forEach((type) => {
    const typeModels = categorizedModels[type];
    if (typeModels && Array.isArray(typeModels)) {
      models.push(...typeModels);
    }
  });

  return models;
}

/**
 * 工具函数：检查供应商是否有指定类型的模型
 */
export function hasModelsOfType(
  categorizedModels: CategorizedModels,
  types: readonly ModelType[],
): boolean {
  return types.some((type) => {
    const typeModels = categorizedModels[type];
    return typeModels && Array.isArray(typeModels) && typeModels.length > 0;
  });
}

/**
 * 工具函数：获取所有模型（扁平化）
 */
export function getAllModels(categorizedModels: CategorizedModels): string[] {
  const allModels: string[] = [];

  Object.values(categorizedModels).forEach((models) => {
    if (models && Array.isArray(models)) {
      allModels.push(...models);
    }
  });

  return allModels;
}

/**
 * 工具函数：根据模型名称查找其类型
 */
export function findModelType(
  categorizedModels: CategorizedModels,
  modelName: string,
): ModelType | null {
  for (const [type, models] of Object.entries(categorizedModels)) {
    if (models && Array.isArray(models) && models.includes(modelName)) {
      return type as ModelType;
    }
  }
  return null;
}

/**
 * 工具函数：检查模型是否属于指定类型之一
 */
export function isModelOfType(
  categorizedModels: CategorizedModels,
  modelName: string,
  types: readonly ModelType[],
): boolean {
  const modelType = findModelType(categorizedModels, modelName);
  return modelType !== null && types.includes(modelType);
}
