{"name": "inkcop", "version": "0.0.1", "description": "A copilot app for writer", "productName": "inkcop", "author": "JERR <<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "build:prod": "quasar build --mode spa", "postinstall": "quasar prepare"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.7.2", "@atlaskit/pragmatic-drag-and-drop-flourish": "^2.0.3", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.1.0", "@excalidraw/excalidraw": "^0.18.0", "@handsontable/vue3": "^15.3.0", "@hocuspocus/provider": "^3.1.3", "@hocuspocus/transformer": "^3.1.3", "@intlify/vite-plugin-vue-i18n": "^7.0.0", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.61", "@langchain/textsplitters": "^0.1.0", "@lobehub/icons-static-svg": "^1.48.0", "@quasar/cli": "^2.5.0", "@quasar/extras": "^1.16.4", "@sereneinserenade/tiptap-search-and-replace": "^0.1.1", "@tiptap-pro/extension-details": "^2.21.0", "@tiptap-pro/extension-details-content": "^2.21.0", "@tiptap-pro/extension-details-summary": "^2.21.0", "@tiptap-pro/extension-drag-handle": "^2.21.0", "@tiptap-pro/extension-drag-handle-vue-3": "^2.21.4", "@tiptap-pro/extension-emoji": "^2.21.0", "@tiptap-pro/extension-export": "^2.21.5", "@tiptap-pro/extension-file-handler": "^2.21.0", "@tiptap-pro/extension-import": "^2.21.5", "@tiptap-pro/extension-mathematics": "^2.21.0", "@tiptap-pro/extension-node-range": "^2.21.0", "@tiptap-pro/extension-table-of-contents": "^2.21.0", "@tiptap-pro/extension-unique-id": "^2.21.0", "@tiptap/core": "^2.14.0", "@tiptap/extension-blockquote": "2.14.0", "@tiptap/extension-bold": "2.14.0", "@tiptap/extension-bullet-list": "2.14.0", "@tiptap/extension-code": "2.14.0", "@tiptap/extension-code-block": "2.14.0", "@tiptap/extension-code-block-lowlight": "^2.14.0", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-document": "2.14.0", "@tiptap/extension-dropcursor": "^2.14.0", "@tiptap/extension-font-family": "^2.14.0", "@tiptap/extension-heading": "2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-history": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-italic": "2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "2.14.0", "@tiptap/extension-node-range": "^2.23.0", "@tiptap/extension-ordered-list": "2.14.0", "@tiptap/extension-paragraph": "2.14.0", "@tiptap/extension-placeholder": "2.14.0", "@tiptap/extension-strike": "2.14.0", "@tiptap/extension-subscript": "^2.14.0", "@tiptap/extension-superscript": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-task-item": "^2.14.0", "@tiptap/extension-task-list": "^2.14.0", "@tiptap/extension-text": "2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/pm": "2.14.0", "@tiptap/starter-kit": "^2.14.0", "@tiptap/suggestion": "^3.0.1", "@tiptap/vue-3": "2.14.0", "@types/better-sqlite3": "^7.6.13", "@types/handsontable": "^0.35.0", "@types/highlight.js": "^10.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vueuse/core": "^13.5.0", "add": "^2.0.6", "artplayer": "^5.2.3", "async": "^3.2.6", "axios": "^1.2.1", "better-sqlite3": "^11.10.0", "dexie": "^4.0.11", "emoji-mart": "^5.6.0", "handsontable": "^15.3.0", "highlight.js": "^11.11.1", "katex": "^0.16.22", "lowlight": "^3.3.0", "marked": "^15.0.12", "mermaid": "^11.8.0", "nanoid": "^5.1.5", "node-fetch": "^3.3.2", "openai": "^5.5.0", "pexels": "^1.4.0", "pinia": "^3.0.1", "pinyin-pro": "^3.26.0", "quasar": "^2.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tippy.js": "^6.3.7", "tiptap-markdown": "^0.8.10", "vue": "^3.5.17", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^11.1.7", "vue-router": "^4.0.12"}, "devDependencies": {"@eslint/js": "^9.14.0", "@intlify/unplugin-vue-i18n": "^4.0.0", "@quasar/app-vite": "^2.2.0", "@types/lowlight": "^2.0.1", "@types/node": "^20.19.0", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.4.0", "autoprefixer": "^10.4.2", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "hevue-img-preview": "^7.0.1", "prettier": "^3.3.3", "typescript": "~5.5.3", "vite-plugin-checker": "^0.9.0", "vue-tsc": "^2.0.29"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}, "trustedDependencies": ["core-js", "protobufjs"]}