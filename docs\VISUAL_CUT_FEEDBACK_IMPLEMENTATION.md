# 剪切文档视觉反馈实现文档

## 功能概述

为被剪切的文档添加视觉提示，使用 `op-5` class 让文档显示为半透明状态，提供清晰的用户反馈。

## 实现细节

### 1. UI Store 扩展

在 `src/stores/ui.ts` 中添加了检查文档是否被剪切的方法：

```typescript
isDocumentCut(documentId: number): boolean {
  return this.copiedDocument?.mode === 'cut' && this.copiedDocument?.id === documentId;
}
```

### 2. DocumentItem 组件修改

在 `src/components/DocumentItem.vue` 中修改了 class 绑定：

```vue
<div
  class="row no-wrap hover-highlight q-px-sm document-drag-container full-width"
  :class="[
    uiStore.highlightTreeItem === `document-${doc?.id}`
      ? 'highlight-tree-item'
      : keepHighlightDocumentId === doc.id
        ? $q.dark.mode
          ? 'bg-grey-9'
          : 'bg-grey-2'
        : 'border-placehoder',
    uiStore.isDocumentCut(doc.id) ? 'op-5' : ''
  ]"
>
```

## 工作原理

1. **状态检查**：`uiStore.isDocumentCut(doc.id)` 检查当前文档是否被剪切
2. **条件渲染**：如果文档被剪切，添加 `op-5` class
3. **视觉效果**：`op-5` class 使元素透明度降低到 50%
4. **实时更新**：状态变化时视觉效果自动更新

## 用户体验

- **即时反馈**：剪切操作后文档立即变为半透明
- **清晰区分**：被剪切的文档与正常文档有明显视觉区别
- **状态持续**：视觉提示会持续到粘贴操作完成
- **自动清理**：粘贴成功后视觉提示自动消失

## CSS 类说明

- `op-5`：Quasar 框架提供的透明度工具类
- 效果：`opacity: 0.5`（50% 透明度）
- 适用场景：需要表示"临时状态"或"待处理"的元素

## 技术优势

1. **响应式**：基于 Vue 3 响应式系统，状态变化自动更新视图
2. **性能优化**：只在需要时添加 class，无额外性能开销
3. **一致性**：使用 Quasar 标准工具类，保持设计一致性
4. **可维护性**：逻辑清晰，易于理解和维护

## 测试场景

### 基本功能测试
1. 右键点击文档，选择"剪切"
2. 验证文档变为半透明状态
3. 右键点击目标文件夹，选择"粘贴"
4. 验证文档恢复正常显示

### 状态管理测试
1. 剪切文档A，验证A变为半透明
2. 剪切文档B，验证A恢复正常，B变为半透明
3. 取消操作，验证B恢复正常

### 边界情况测试
1. 剪切后刷新页面，验证状态正确恢复
2. 同时打开多个文档，验证只有被剪切的文档显示半透明
3. 在不同文件夹间切换，验证视觉状态保持一致

## 相关文件

- `src/stores/ui.ts` - 添加 `isDocumentCut` 方法
- `src/components/DocumentItem.vue` - 修改 class 绑定逻辑
- `docs/DOCUMENT_COPY_PASTE_FEATURE.md` - 更新功能文档

## 总结

视觉反馈功能为剪切操作提供了直观的用户体验，通过简单的 CSS 类切换实现了清晰的状态指示。这种实现方式既保持了代码的简洁性，又提供了良好的用户体验。
