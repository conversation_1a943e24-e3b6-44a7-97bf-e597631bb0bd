import os
import sys

def generate_qrc(web_dir, qrc_file, prefix="/"):
    """
    Generates a .qrc file from the contents of a directory, making files
    available at the root of the resource system using aliases.

    :param web_dir: The directory containing the web assets (e.g., 'dist/spa').
    :param qrc_file: The path to the output .qrc file (e.g., 'webapp.qrc').
    :param prefix: The prefix to use for the resource paths in the .qrc file.
    """
    if not os.path.isdir(web_dir):
        print(f"Warning: Directory '{web_dir}' not found. This is normal if Quasar app has not been built yet.", file=sys.stderr)
        print(f"Creating an empty '{qrc_file}' to prevent build errors.")
        with open(qrc_file, 'w', encoding='utf-8') as f:
            f.write('<!DOCTYPE RCC>\n<RCC version="1.0">\n  <qresource/>\n</RCC>\n')
        return

    qrc_dir = os.path.dirname(os.path.abspath(qrc_file))
    web_dir_rel_to_qrc = os.path.relpath(web_dir, qrc_dir).replace('\\', '/')

    with open(qrc_file, 'w', encoding='utf-8') as f:
        f.write('<!DOCTYPE RCC>\n<RCC version="1.0">\n')
        f.write(f'  <qresource prefix="{prefix}">\n')

        for root, _, files in sorted(os.walk(web_dir)):
            for file in sorted(files):
                file_path = os.path.join(root, file)
                alias_path = os.path.relpath(file_path, web_dir).replace('\\', '/')
                
                # Make sure alias for index.html is just 'index.html'
                if alias_path == 'index.html':
                    # provide a root alias for index.html
                    f.write(f'    <file alias="index.html">{web_dir_rel_to_qrc}/{alias_path}</file>\n')
                else:
                    f.write(f'    <file alias="{alias_path}">{web_dir_rel_to_qrc}/{alias_path}</file>\n')

        f.write('  </qresource>\n</RCC>\n')
    print(f"✅ Successfully generated '{qrc_file}' for directory '{web_dir}'")

if __name__ == '__main__':
    project_root = os.path.dirname(os.path.abspath(__file__))
    quasar_dist_dir = os.path.join(project_root, 'dist', 'spa')
    output_qrc_file = os.path.join(project_root, 'webapp.qrc')

    generate_qrc(quasar_dist_dir, output_qrc_file, prefix="/") 