/**
 * 知识库相关工具函数
 * 包含文档切割、向量化等知识库处理功能
 */
import { $t } from 'src/composables/useTrans';

import {
  MarkdownTextSplitter,
  RecursiveCharacterTextSplitter,
  LatexTextSplitter,
} from '@langchain/textsplitters';

export interface ChunkingConfig {
  chunkSize: number;
  chunkOverlap: number;
}

export interface DocumentChunkMetadata {
  mergedFrom?: DocumentChunkMetadata[];
  [key: string]: unknown;
}

export interface DocumentChunk {
  pageContent: string;
  metadata: DocumentChunkMetadata;
}

export interface SplitterInfo {
  name: string;
  displayName: string;
  description: string;
  useCases: string[];
  advantages: string[];
  limitations: string[];
  recommendedFor: string[];
  defaultConfig: ChunkingConfig;
  function: (
    content: string,
    config?: ChunkingConfig,
    enableLogging?: boolean,
  ) => Promise<ChunkingResult>;
}

export interface ChunkingResult {
  chunks: DocumentChunk[];
  originalLength: number;
  chunkCount: number;
  summary: {
    averageChunkSize: number;
    minChunkSize: number;
    maxChunkSize: number;
  };
}

/**
 * 使用 LangChain MarkdownTextSplitter 进行文档切割
 * @param markdownContent Markdown格式的文档内容
 * @param config 切割配置
 * @param enableLogging 是否启用详细日志输出
 * @returns 切割结果
 */
export async function splitMarkdownWithLangChain(
  markdownContent: string,
  config: ChunkingConfig = { chunkSize: 800, chunkOverlap: 200 },
  enableLogging: boolean = false,
): Promise<ChunkingResult> {
  if (!markdownContent || markdownContent.trim().length === 0) {
    throw new Error('document content is empty');
  }

  try {
    // 创建切割器实例
    const splitter = new MarkdownTextSplitter({
      chunkSize: config.chunkSize || 800,
      chunkOverlap: config.chunkOverlap || 200,
    });

    // 执行文档切割
    const chunks = await splitter.createDocuments([markdownContent]);

    // 计算统计信息
    const chunkSizes = chunks.map((chunk) => chunk.pageContent.length);
    const summary = {
      averageChunkSize: Math.round(chunkSizes.reduce((a, b) => a + b, 0) / chunkSizes.length),
      minChunkSize: Math.min(...chunkSizes),
      maxChunkSize: Math.max(...chunkSizes),
    };

    const result: ChunkingResult = {
      chunks,
      originalLength: markdownContent.length,
      chunkCount: chunks.length,
      summary,
    };

    // 可选的详细日志输出
    if (enableLogging) {
      logChunkingResult(result);
    }

    return result;
  } catch (error) {
    console.error('LangChain document splitting failed:', error);
    throw new Error(
      `document splitting failed: ${error instanceof Error ? error.message : 'unknown error'}`,
    );
  }
}

/**
 * 使用 LangChain RecursiveCharacterTextSplitter 进行文档切割
 * 适用于各种文本类型，会尝试在自然边界处分割（段落、句子、单词）
 * @param textContent 文本内容
 * @param config 切割配置
 * @param enableLogging 是否启用详细日志输出
 * @returns 切割结果
 */
export async function splitTextWithRecursiveCharacter(
  textContent: string,
  config: ChunkingConfig = { chunkSize: 800, chunkOverlap: 200 },
  enableLogging: boolean = false,
): Promise<ChunkingResult> {
  if (!textContent || textContent.trim().length === 0) {
    throw new Error('document content is empty');
  }

  try {
    // 创建递归字符切割器实例
    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: config.chunkSize || 800,
      chunkOverlap: config.chunkOverlap || 200,
      separators: ['\n\n', '\n', ' ', ''], // 优先在段落、行、单词边界分割
    });

    // 执行文档切割
    const chunks = await splitter.createDocuments([textContent]);

    // 计算统计信息
    const chunkSizes = chunks.map((chunk) => chunk.pageContent.length);
    const summary = {
      averageChunkSize: Math.round(chunkSizes.reduce((a, b) => a + b, 0) / chunkSizes.length),
      minChunkSize: Math.min(...chunkSizes),
      maxChunkSize: Math.max(...chunkSizes),
    };

    const result: ChunkingResult = {
      chunks,
      originalLength: textContent.length,
      chunkCount: chunks.length,
      summary,
    };

    // 可选的详细日志输出
    if (enableLogging) {
      console.log('=== RecursiveCharacterTextSplitter splitting result ===');
      logChunkingResult(result);
    }

    return result;
  } catch (error) {
    console.error('RecursiveCharacterTextSplitter document splitting failed:', error);
    throw new Error(
      `document splitting failed: ${error instanceof Error ? error.message : 'unknown error'}`,
    );
  }
}

/**
 * 输出切割结果的详细日志
 * @param result 切割结果
 */
export function logChunkingResult(result: ChunkingResult): void {
  console.log('=== document splitting result ===');
  console.log(`original document length: ${result.originalLength} characters`);
  console.log(`number of chunks: ${result.chunkCount}`);
  console.log(`average chunk size: ${result.summary.averageChunkSize} characters`);
  console.log(`minimum chunk size: ${result.summary.minChunkSize} characters`);
  console.log(`maximum chunk size: ${result.summary.maxChunkSize} characters`);
  console.log('chunk details:');

  result.chunks.forEach((chunk, index) => {
    console.log(`\n--- chunk ${index + 1} ---`);
    console.log(`length: ${chunk.pageContent.length} characters`);
    console.log(`preview: ${chunk.pageContent.substring(0, 200)}...`);
    console.log('metadata:', chunk.metadata);
  });

  console.log('=== document splitting completed ===');
}

/**
 * 使用 LangChain LatexTextSplitter 进行 LaTeX 文档切割
 * 专门针对 LaTeX 文档的结构化切割，保持数学公式和环境的完整性
 * @param latexContent LaTeX 格式的文档内容
 * @param config 切割配置
 * @param enableLogging 是否启用详细日志输出
 * @returns 切割结果
 */
export async function splitLatexWithLangChain(
  latexContent: string,
  config: ChunkingConfig = { chunkSize: 800, chunkOverlap: 200 },
  enableLogging: boolean = false,
): Promise<ChunkingResult> {
  if (!latexContent || latexContent.trim().length === 0) {
    throw new Error('LaTeX document content is empty');
  }

  try {
    // 创建 LaTeX 切割器实例
    const splitter = new LatexTextSplitter({
      chunkSize: config.chunkSize || 800,
      chunkOverlap: config.chunkOverlap || 200,
    });

    // 执行文档切割
    const chunks = await splitter.createDocuments([latexContent]);

    // 计算统计信息
    const chunkSizes = chunks.map((chunk) => chunk.pageContent.length);
    const summary = {
      averageChunkSize: Math.round(chunkSizes.reduce((a, b) => a + b, 0) / chunkSizes.length),
      minChunkSize: Math.min(...chunkSizes),
      maxChunkSize: Math.max(...chunkSizes),
    };

    const result: ChunkingResult = {
      chunks,
      originalLength: latexContent.length,
      chunkCount: chunks.length,
      summary,
    };

    // 可选的详细日志输出
    if (enableLogging) {
      console.log('=== LatexTextSplitter splitting result ===');
      logChunkingResult(result);
    }

    return result;
  } catch (error) {
    console.error('LatexTextSplitter document splitting failed:', error);
    throw new Error(
      `LaTeX document splitting failed: ${error instanceof Error ? error.message : 'unknown error'}`,
    );
  }
}

/**
 * 智能文档切割 - 根据内容类型自动选择合适的切割器
 * @param content 文档内容
 * @param config 切割配置
 * @param contentType 内容类型提示 ('markdown' | 'text' | 'latex' | 'auto')
 * @param enableLogging 是否启用详细日志输出
 * @returns 切割结果
 */
export async function splitDocumentSmart(
  content: string,
  config: ChunkingConfig = { chunkSize: 800, chunkOverlap: 200 },
  contentType: 'markdown' | 'text' | 'latex' | 'auto' = 'auto',
  enableLogging: boolean = false,
): Promise<ChunkingResult> {
  if (!content || content.trim().length === 0) {
    throw new Error('document content is empty');
  }

  // 自动检测内容类型
  let detectedType = contentType;
  if (contentType === 'auto') {
    // 简单的内容类型检测
    const hasLatexCommands = /\\[a-zA-Z]+(\{[^}]*\}|\[[^\]]*\])*/.test(content);
    const hasLatexMath = /\$\$[\s\S]*?\$\$|\$[^$]*\$/.test(content);
    const hasLatexEnvironments = /\\begin\{[^}]+\}[\s\S]*?\\end\{[^}]+\}/.test(content);

    const hasMarkdownHeaders = /^#{1,6}\s+.+$/m.test(content);
    const hasMarkdownLinks = /\[.+\]\(.+\)/.test(content);
    const hasMarkdownCode = /```[\s\S]*?```/.test(content);

    if (hasLatexCommands || hasLatexMath || hasLatexEnvironments) {
      detectedType = 'latex';
    } else if (hasMarkdownHeaders || hasMarkdownLinks || hasMarkdownCode) {
      detectedType = 'markdown';
    } else {
      detectedType = 'text';
    }
  }

  if (enableLogging) {
    console.log(`🔍 [smart splitting] detected content type: ${detectedType}`);
  }

  // 根据检测到的类型选择合适的切割器
  if (detectedType === 'latex') {
    return await splitLatexWithLangChain(content, config, enableLogging);
  } else if (detectedType === 'markdown') {
    return await splitMarkdownWithLangChain(content, config, enableLogging);
  } else {
    return await splitTextWithRecursiveCharacter(content, config, enableLogging);
  }
}

/**
 * 验证切割配置的有效性
 * @param config 切割配置
 * @returns 验证结果
 */
export function validateChunkingConfig(config: {
  chunkSize?: number;
  chunkOverlap?: number;
  [key: string]: unknown;
}): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!config.chunkSize || config.chunkSize <= 0) {
    errors.push('chunkSize must be a number greater than 0');
  }

  if (config.chunkSize && config.chunkSize > 4000) {
    errors.push(
      'chunkSize is not recommended to be greater than 4000 characters, it may affect vectorization effect',
    );
  }

  if (config.chunkOverlap !== undefined && config.chunkOverlap < 0) {
    errors.push('chunkOverlap must be a number greater than or equal to 0');
  }

  if (
    config.chunkSize &&
    config.chunkOverlap !== undefined &&
    config.chunkOverlap >= config.chunkSize
  ) {
    errors.push('chunkOverlap must be less than chunkSize');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 获取推荐的切割配置
 * @param documentLength 文档长度
 * @returns 推荐的切割配置
 */
export function getRecommendedChunkingConfig(documentLength: number): ChunkingConfig {
  // 根据文档长度推荐不同的切割策略
  if (documentLength < 2000) {
    // 短文档：较小的块大小
    return { chunkSize: 400, chunkOverlap: 100 };
  } else if (documentLength < 10000) {
    // 中等文档：标准块大小
    return { chunkSize: 800, chunkOverlap: 200 };
  } else {
    // 长文档：较大的块大小
    return { chunkSize: 1200, chunkOverlap: 300 };
  }
}

/**
 * 预处理 Markdown 内容，优化切割效果
 * @param markdownContent 原始 Markdown 内容
 * @returns 预处理后的内容
 */
export function preprocessMarkdownForSplitting(markdownContent: string): string {
  // 移除多余的空行
  let processed = markdownContent.replace(/\n{3,}/g, '\n\n');

  // 确保标题前后有适当的空行
  processed = processed.replace(/([^\n])\n(#{1,6}\s)/g, '$1\n\n$2');
  processed = processed.replace(/(#{1,6}\s[^\n]+)\n([^\n#])/g, '$1\n\n$2');

  // 确保代码块前后有适当的空行
  processed = processed.replace(/([^\n])\n(```)/g, '$1\n\n$2');
  processed = processed.replace(/(```[^\n]*)\n([^\n`])/g, '$1\n\n$2');

  return processed.trim();
}

/**
 * 合并小的文档块，优化切割结果
 * @param chunks 原始切割块
 * @param minChunkSize 最小块大小阈值
 * @returns 合并后的切割块
 */
export function mergeSmallChunks(
  chunks: DocumentChunk[],
  minChunkSize: number = 200,
): DocumentChunk[] {
  if (chunks.length <= 1) return chunks;

  const mergedChunks: DocumentChunk[] = [];
  let currentChunk: DocumentChunk | null = null;

  for (const chunk of chunks) {
    if (!currentChunk) {
      currentChunk = { ...chunk };
    } else if (
      chunk.pageContent.length < minChunkSize &&
      currentChunk.pageContent.length + chunk.pageContent.length < minChunkSize * 3
    ) {
      // 合并小块
      currentChunk.pageContent += '\n\n' + chunk.pageContent;
      currentChunk.metadata = {
        ...currentChunk.metadata,
        mergedFrom: [...(currentChunk.metadata.mergedFrom || []), chunk.metadata],
      };
    } else {
      mergedChunks.push(currentChunk);
      currentChunk = { ...chunk };
    }
  }

  if (currentChunk) {
    mergedChunks.push(currentChunk);
  }

  return mergedChunks;
}

/**
 * 可用的文档切割方案配置
 * 包含每种切割方案的详细信息和适用场景
 */
export const strategies = () => {
  const CHUNKING_STRATEGIES: Record<string, SplitterInfo> = {
    markdown: {
      name: 'markdown',
      displayName: $t('src.utils.knowledgeBase.markdown.displayName'),
      description: $t('src.utils.knowledgeBase.markdown.description'),
      useCases: $t('src.utils.knowledgeBase.markdown.useCases').split(','),
      advantages: $t('src.utils.knowledgeBase.markdown.advantages').split(','),
      limitations: $t('src.utils.knowledgeBase.markdown.limitations').split(','),
      recommendedFor: $t('src.utils.knowledgeBase.markdown.recommendedFor').split(','),
      defaultConfig: { chunkSize: 800, chunkOverlap: 200 },
      function: splitMarkdownWithLangChain,
    },

    recursiveCharacter: {
      name: 'recursiveCharacter',
      displayName: $t('src.utils.knowledgeBase.recursiveCharacter.displayName'),
      description: $t('src.utils.knowledgeBase.recursiveCharacter.description'),
      useCases: $t('src.utils.knowledgeBase.recursiveCharacter.useCases').split(','),
      advantages: $t('src.utils.knowledgeBase.recursiveCharacter.advantages').split(','),
      limitations: $t('src.utils.knowledgeBase.recursiveCharacter.limitations').split(','),
      recommendedFor: $t('src.utils.knowledgeBase.recursiveCharacter.recommendedFor').split(','),
      defaultConfig: { chunkSize: 800, chunkOverlap: 200 },
      function: splitTextWithRecursiveCharacter,
    },

    latex: {
      name: 'latex',
      displayName: $t('src.utils.knowledgeBase.latex.displayName'),
      description: $t('src.utils.knowledgeBase.latex.description'),
      useCases: $t('src.utils.knowledgeBase.latex.useCases').split(','),
      advantages: $t('src.utils.knowledgeBase.latex.advantages').split(','),
      limitations: $t('src.utils.knowledgeBase.latex.limitations').split(','),
      recommendedFor: $t('src.utils.knowledgeBase.latex.recommendedFor').split(','),
      defaultConfig: { chunkSize: 800, chunkOverlap: 200 },
      function: splitLatexWithLangChain,
    },

    smart: {
      name: 'smart',
      displayName: $t('src.utils.knowledgeBase.smart.displayName'),
      description: $t('src.utils.knowledgeBase.smart.description'),
      useCases: $t('src.utils.knowledgeBase.smart.useCases').split(','),
      advantages: $t('src.utils.knowledgeBase.smart.advantages').split(','),
      limitations: $t('src.utils.knowledgeBase.smart.limitations').split(','),
      recommendedFor: $t('src.utils.knowledgeBase.smart.recommendedFor').split(','),
      defaultConfig: { chunkSize: 800, chunkOverlap: 200 },
      function: (content: string, config?: ChunkingConfig, enableLogging?: boolean) =>
        splitDocumentSmart(content, config, 'auto', enableLogging),
    },
  };
  return CHUNKING_STRATEGIES;
};

/**
 * 获取所有可用的切割方案列表
 * @returns 切割方案信息数组
 */
export function getAvailableStrategies(): SplitterInfo[] {
  return Object.values(strategies());
}

/**
 * 根据名称获取切割方案信息
 * @param strategyName 切割方案名称
 * @returns 切割方案信息，如果不存在则返回 null
 */
export function getStrategyInfo(strategyName: string): SplitterInfo | null {
  return strategies()[strategyName] || null;
}

/**
 * 获取推荐的切割方案
 * @param contentType 内容类型
 * @returns 推荐的切割方案名称
 */
export function getRecommendedStrategy(
  contentType: 'markdown' | 'latex' | 'text' | 'unknown',
): string {
  // 根据内容类型推荐策略
  switch (contentType) {
    case 'markdown':
      return 'markdown';
    case 'latex':
      return 'latex';
    case 'text':
      return 'recursiveCharacter';
    case 'unknown':
    default:
      return 'smart';
  }
}

/**
 * 使用指定的切割方案处理文档
 * @param strategyName 切割方案名称
 * @param content 文档内容
 * @param config 切割配置（可选）
 * @param enableLogging 是否启用日志（可选）
 * @returns 切割结果
 */
export async function splitWithStrategy(
  strategyName: string,
  content: string,
  config?: ChunkingConfig,
  enableLogging?: boolean,
): Promise<ChunkingResult> {
  const strategy = getStrategyInfo(strategyName);
  if (!strategy) {
    throw new Error(`未知的切割方案: ${strategyName}`);
  }

  const finalConfig = config || strategy.defaultConfig;
  return await strategy.function(content, finalConfig, enableLogging);
}

// 定义任务接口以避免使用 any
export interface BackgroundTask {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  strategy: string;
  contentLength: number;
  config?: ChunkingConfig;
  result?: ChunkingResult;
  error?: string;
  progress?: {
    stage: string;
    percentage: number;
  };
  createdAt: Date;
  completedAt?: Date;
}

/**
 * 在后台 Worker 中执行文档切割
 * @param strategy 切割策略名称
 * @param content 文档内容
 * @param config 切割配置（可选）
 * @param enableLogging 是否启用日志（可选）
 * @param callbacks 回调函数（可选）
 * @returns 任务ID的 Promise
 */
export async function splitInBackground(
  strategy: 'markdown' | 'recursiveCharacter' | 'latex' | 'smart',
  content: string,
  config?: ChunkingConfig,
  enableLogging?: boolean,
  callbacks?: {
    onProgress?: (task: BackgroundTask) => void;
    onCompletion?: (task: BackgroundTask, result?: ChunkingResult) => void;
    onError?: (task: BackgroundTask, error: string) => void;
  },
): Promise<string> {
  // 生成任务ID
  const taskId = `chunk_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  try {
    // 1. 首先在IndexedDB中创建任务记录
    const { chunkingTaskManager } = await import('./chunkingTaskDB');
    await chunkingTaskManager.createTask(taskId, strategy, content, config, enableLogging);

    // 2. 创建Worker执行切割
    const worker = new Worker(new URL('../workers/chunkingWorker.ts', import.meta.url), {
      type: 'module',
    });

    // 3. 监听Worker消息并更新数据库
    worker.addEventListener('message', (event) => {
      const response = event.data;

      if (response.id !== taskId) return;

      // 使用void操作符处理async操作
      void (async () => {
        try {
          switch (response.type) {
            case 'progress':
              // 更新任务状态为处理中
              await chunkingTaskManager.updateTaskStatus(taskId, 'processing');

              if (callbacks?.onProgress && response.progress) {
                const task: BackgroundTask = {
                  id: taskId,
                  status: 'running',
                  strategy,
                  contentLength: content.length,
                  progress: response.progress,
                  createdAt: new Date(),
                };
                callbacks.onProgress(task);
              }
              break;

            case 'success':
              // 保存切割结果到数据库
              if (response.result) {
                await chunkingTaskManager.saveChunkingResult(taskId, response.result);
              }

              if (callbacks?.onCompletion && response.result) {
                const task: BackgroundTask = {
                  id: taskId,
                  status: 'completed',
                  strategy,
                  contentLength: content.length,
                  createdAt: new Date(),
                  completedAt: new Date(),
                };
                callbacks.onCompletion(task, response.result);
              }
              worker.terminate();
              break;

            case 'error':
              // 更新任务状态为失败
              await chunkingTaskManager.updateTaskStatus(taskId, 'failed', response.error);

              if (callbacks?.onError) {
                const task: BackgroundTask = {
                  id: taskId,
                  status: 'failed',
                  strategy,
                  contentLength: content.length,
                  error: response.error,
                  createdAt: new Date(),
                  completedAt: new Date(),
                };
                callbacks.onError(task, response.error || '未知错误');
              }
              worker.terminate();
              break;
          }
        } catch (dbError) {
          console.error('❌ [splitInBackground] 数据库操作失败:', dbError);
        }
      })();
    });

    // 4. 监听Worker错误
    worker.addEventListener('error', (error) => {
      // 使用void操作符处理async操作
      void (async () => {
        try {
          await chunkingTaskManager.updateTaskStatus(taskId, 'failed', error.message);
        } catch (dbError) {
          console.error('❌ [splitInBackground] 数据库更新失败:', dbError);
        }

        if (callbacks?.onError) {
          const task: BackgroundTask = {
            id: taskId,
            status: 'failed',
            strategy,
            contentLength: content.length,
            error: error.message,
            createdAt: new Date(),
            completedAt: new Date(),
          };
          callbacks.onError(task, error.message);
        }
        worker.terminate();
      })();
    });

    // 5. 发送任务到Worker
    worker.postMessage({
      id: taskId,
      type: 'chunk',
      strategy,
      content,
      config,
      enableLogging,
    });

    // 6. 立即返回任务ID
    return taskId;
  } catch (error) {
    // 如果创建任务失败，尝试更新状态
    try {
      const { chunkingTaskManager } = await import('./chunkingTaskDB');
      await chunkingTaskManager.updateTaskStatus(
        taskId,
        'failed',
        error instanceof Error ? error.message : '未知错误',
      );
    } catch (dbError) {
      console.error('❌ [splitInBackground] 数据库错误处理失败:', dbError);
    }

    if (callbacks?.onError) {
      const errorTask: BackgroundTask = {
        id: taskId,
        status: 'failed',
        strategy,
        contentLength: content.length,
        error: error instanceof Error ? error.message : '未知错误',
        createdAt: new Date(),
        completedAt: new Date(),
      };
      callbacks.onError(errorTask, errorTask.error);
    }
    throw error;
  }
}

/**
 * 获取切割任务状态
 * @param taskId 任务ID
 * @returns 任务信息
 */
export async function getChunkingTaskStatus(taskId: string) {
  try {
    const { chunkingTaskManager } = await import('./chunkingTaskDB');
    return await chunkingTaskManager.getTask(taskId);
  } catch (error) {
    console.error('❌ [getChunkingTaskStatus] 获取任务状态失败:', error);
    return null;
  }
}

/**
 * 获取所有切割任务
 * @returns 任务列表
 */
export async function getAllChunkingTasks() {
  try {
    const { chunkingTaskManager } = await import('./chunkingTaskDB');
    return await chunkingTaskManager.getAllTasks();
  } catch (error) {
    console.error('❌ [getAllChunkingTasks] 获取任务列表失败:', error);
    return [];
  }
}

/**
 * 删除切割任务
 * @param taskId 任务ID
 */
export async function deleteChunkingTask(taskId: string) {
  try {
    const { chunkingTaskManager } = await import('./chunkingTaskDB');
    await chunkingTaskManager.deleteTask(taskId);
    return true;
  } catch (error) {
    console.error('❌ [deleteChunkingTask] 删除任务失败:', error);
    return false;
  }
}
