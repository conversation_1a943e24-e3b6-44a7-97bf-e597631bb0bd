#include "mainwindow.h"
#include <QFileInfo>
#include <QCoreApplication>
#include <QStandardPaths>
#include <QWebEngineSettings>
#include <QWebEnginePage>
// #include <QWebEnginePermission>  // 在Qt 6.7.3中可能不可用
#include <QKeySequence>
#include <QScreen>
#include <QDebug>
#include <QWebEngineProfile>
#include <QUrl>
#include <QWebChannel>
#include <QMouseEvent>
#include <QMoveEvent>
#include <QResizeEvent>
#include <QPushButton>
#include <QCloseEvent>
#include <QFile>
#include <QSettings>
#include <QTimer>
#include <QTextStream>
#include <QRegularExpression>
#include <QStringConverter>
#include <QPainter>
#include <QPen>
#include "settings.h"
#include <QWebEngineCookieStore>
#include <QStyleHints>
#include <QGuiApplication>
#include <QSystemTrayIcon>
#include <QMenu>
#include <QIcon>
#include <QWindow>
#include <QLoggingCategory>
#include <QProcessEnvironment>
#include <QTcpSocket>
#include <QStyle>

// 定义自定义日志类别
Q_DECLARE_LOGGING_CATEGORY(inkcop)
Q_LOGGING_CATEGORY(inkcop, "inkcop")

// 在 main.cpp 中设置日志过滤规则
// QLoggingCategory::setFilterRules(QStringLiteral("inkcop.debug=true\n*.debug=false\n"));

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), m_webView(nullptr), m_centralWidget(nullptr), m_layout(nullptr), m_zoomFactor(1.0), m_dragPosition(QPoint()), m_dragging(false), m_windowApi(nullptr), m_isDarkTheme(false), m_resizeDirection(ResizeNone), m_resizing(false), m_resizeStartPos(QPoint()), m_resizeStartGeometry(QRect()), m_isDevMode(false), m_databaseApi(nullptr)

      ,
      m_trayIcon(nullptr), m_trayMenu(nullptr)
{
    // 根据平台选择窗口模式

    // 使用QWindowKit创建带原生边框的窗口
    setWindowFlags(Qt::Window);

    // 为了支持Mica效果，需要设置窗口属性
    setAttribute(Qt::WA_TranslucentBackground, false); // Mica不需要透明背景
    setAttribute(Qt::WA_NoSystemBackground, false);    // 保持系统背景

    // 初始化QWindowKit
    m_windowAgent = new QWK::WidgetWindowAgent(this);
    m_windowAgent->setup(this);

    // 启用窗口大小调整
    setMouseTracking(true);

    // 初始化API对象
    m_windowApi = new WindowApi(this);
    m_databaseApi = new DatabaseApi(this);

    m_knowledgeApi = new KnowledgeApi(this, m_databaseApi);

// 检查是否为开发模式
#ifdef DEV_MODE
    m_isDevMode = true;
#else
    m_isDevMode = false;
#endif

    // 先设置UI，再初始化主题设置
    setupUI();

    // 加载渲染配置
    loadRenderingConfig();

    // 初始化主题设置
    loadThemeSetting();

    setupWebViewCommands();
    loadWebApp();
    syncThemeWithVue();

    // 注意：Qt6中paletteChanged信号已弃用，我们将通过事件处理来监听主题变化

    // 设置窗口属性
    setWindowTitle("inkCop");

    // 设置窗口几何（大小和位置）
    setupWindowGeometry();

    // 初始化系统托盘
    setupTrayIcon();

#ifdef Q_OS_WIN
    // 使用QWindowKit，不再需要Windows原生窗口效果

#endif
}

MainWindow::~MainWindow()
{

    // 首先删除 QWindowKit agent
    if (m_windowAgent)
    {
        delete m_windowAgent;
        m_windowAgent = nullptr;
    }

    // 首先删除 WebChannel 和 API 对象
    if (m_webChannel)
    {
        delete m_webChannel;
        m_webChannel = nullptr;
    }

    if (m_windowApi)
    {
        delete m_windowApi;
        m_windowApi = nullptr;
    }

    if (m_databaseApi)
    {
        delete m_databaseApi;
        m_databaseApi = nullptr;
    }

    if (m_knowledgeApi)
    {
        delete m_knowledgeApi;
        m_knowledgeApi = nullptr;
    }

    // 删除QWindowKit标题栏
    if (m_qwkTitleBar)
    {
        delete m_qwkTitleBar;
        m_qwkTitleBar = nullptr;
    }

    // 删除 WebView（它会自动删除其页面）
    if (m_webView)
    {
        delete m_webView;
        m_webView = nullptr;
    }

    // 删除布局
    if (m_layout)
    {
        delete m_layout;
        m_layout = nullptr;
    }

    // 删除中心部件
    if (m_centralWidget)
    {
        delete m_centralWidget;
        m_centralWidget = nullptr;
    }
}

void MainWindow::setupUI()
{
    // 创建中心部件
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // 创建布局
    m_layout = new QVBoxLayout(m_centralWidget);
    m_layout->setContentsMargins(0, 0, 0, 0);
    m_layout->setSpacing(0);

    // 创建WebView
    m_webView = new CustomWebEngineView(this);

    // 创建QWindowKit自定义标题栏
    m_qwkTitleBar = new QWKCustomTitleBar(this);
    m_qwkTitleBar->setObjectName("qwkTitleBar");

    // 配置QWindowKit使用自定义标题栏
    m_windowAgent->setTitleBar(m_qwkTitleBar);
    m_windowAgent->setSystemButton(QWK::WindowAgentBase::Minimize, m_qwkTitleBar->findChild<QPushButton *>("minimizeButton"));
    m_windowAgent->setSystemButton(QWK::WindowAgentBase::Maximize, m_qwkTitleBar->findChild<QPushButton *>("maximizeButton"));
    m_windowAgent->setSystemButton(QWK::WindowAgentBase::Close, m_qwkTitleBar->findChild<QPushButton *>("closeButton"));

#ifdef Q_OS_WIN
    // 启用Mica效果并通知标题栏
    // 先清除可能存在的其他效果
    m_windowAgent->setWindowAttribute("dwm-blur", false);
    m_windowAgent->setWindowAttribute("acrylic", false);
    m_windowAgent->setWindowAttribute("mica-alt", false);

    // 方案3：启动时默认关闭Mica效果
    bool micaResult = m_windowAgent->setWindowAttribute("mica", false);
    qDebug() << "Mica effect disabled by default:" << micaResult;

    // 通知标题栏不使用透明背景
    m_qwkTitleBar->setMicaEffectEnabled(false);
    qDebug() << "MainWindow: Mica effect disabled for title bar:" << m_qwkTitleBar->isMicaEffectEnabled();

    // 设置窗口属性以支持Mica效果
    setProperty("custom-style", true);
    style()->polish(this);
#endif

    // 设置工具按钮为可点击（接收鼠标事件）
    auto leftDrawerBtn = m_qwkTitleBar->findChild<QPushButton *>("leftDrawerBtn");
    auto rightDrawerBtn = m_qwkTitleBar->findChild<QPushButton *>("rightDrawerBtn");
    auto themeBtn = m_qwkTitleBar->findChild<QPushButton *>("themeBtn");

    if (leftDrawerBtn)
    {
        m_windowAgent->setHitTestVisible(leftDrawerBtn, true);
    }
    if (rightDrawerBtn)
    {
        m_windowAgent->setHitTestVisible(rightDrawerBtn, true);
    }
    if (themeBtn)
    {
        m_windowAgent->setHitTestVisible(themeBtn, true);
    }

    // 开发模式按钮（如果存在）
    if (auto reloadBtn = m_qwkTitleBar->findChild<QPushButton *>("reloadBtn"))
    {
        m_windowAgent->setHitTestVisible(reloadBtn, true);
    }
    if (auto devToolsBtn = m_qwkTitleBar->findChild<QPushButton *>("devToolsBtn"))
    {
        m_windowAgent->setHitTestVisible(devToolsBtn, true);
    }
    if (auto toggleModeBtn = m_qwkTitleBar->findChild<QPushButton *>("toggleModeBtn"))
    {
        m_windowAgent->setHitTestVisible(toggleModeBtn, true);
    }
    if (auto micaTestBtn = m_qwkTitleBar->findChild<QPushButton *>("micaTestBtn"))
    {
        m_windowAgent->setHitTestVisible(micaTestBtn, true);
    }

    // 连接标题栏信号
    connect(m_qwkTitleBar, &QWKCustomTitleBar::minimizeClicked, this, &MainWindow::showMinimized);
    connect(m_qwkTitleBar, &QWKCustomTitleBar::maximizeClicked, this, &MainWindow::toggleMaximizeWindow);
    connect(m_qwkTitleBar, &QWKCustomTitleBar::closeClicked, this, &MainWindow::minimizeToTray);

    // 连接工具按钮信号
    connect(m_qwkTitleBar, &QWKCustomTitleBar::leftDrawerToggled, this, &MainWindow::toggleLeftDrawer);
    connect(m_qwkTitleBar, &QWKCustomTitleBar::rightDrawerToggled, this, &MainWindow::toggleRightDrawer);
    connect(m_qwkTitleBar, &QWKCustomTitleBar::reloadRequested, this, &MainWindow::reloadPage);
    connect(m_qwkTitleBar, &QWKCustomTitleBar::devToolsRequested, this, &MainWindow::onDevToolsRequested);
    connect(m_qwkTitleBar, &QWKCustomTitleBar::modeToggleRequested, this, &MainWindow::onModeToggleRequested);
    connect(m_qwkTitleBar, &QWKCustomTitleBar::themeToggleRequested, this, &MainWindow::onThemeToggleRequested);
    connect(m_qwkTitleBar, &QWKCustomTitleBar::micaTestRequested, this, &MainWindow::toggleMicaEffect);

    // 连接抽屉状态同步信号
    connect(m_qwkTitleBar, &QWKCustomTitleBar::requestDrawerStateSync, this, &MainWindow::syncDrawerStatesFromFrontend);

    // 添加标题栏和WebView到布局
    m_layout->addWidget(m_qwkTitleBar);
    m_layout->addWidget(m_webView);

    // 不再连接自定义工具栏信号，使用QWindowKit的系统标题栏
    // setupToolBarConnections();

    // 配置WebEngine设置（调试增强）
    QWebEngineSettings *settings = m_webView->settings();
    settings->setAttribute(QWebEngineSettings::JavascriptEnabled, true);
    settings->setAttribute(QWebEngineSettings::LocalStorageEnabled, true);

    // 从配置文件读取网络访问设置
    QSettings config("qt-rendering.conf", QSettings::IniFormat);
    bool localContentCanAccessRemoteUrls = config.value("NETWORK_ACCESS/local_content_can_access_remote_urls", true).toBool();
    bool localContentCanAccessFileUrls = config.value("NETWORK_ACCESS/local_content_can_access_file_urls", true).toBool();
    bool allowRunningInsecureContent = config.value("NETWORK_ACCESS/allow_running_insecure_content", true).toBool();

    settings->setAttribute(QWebEngineSettings::LocalContentCanAccessRemoteUrls, localContentCanAccessRemoteUrls);
    settings->setAttribute(QWebEngineSettings::LocalContentCanAccessFileUrls, localContentCanAccessFileUrls);
    settings->setAttribute(QWebEngineSettings::ErrorPageEnabled, true);

    // 禁用自动加载图片以减少渲染负载（可选）
    settings->setAttribute(QWebEngineSettings::AutoLoadImages, true);

    // 禁用JavaScript可以打开窗口
    settings->setAttribute(QWebEngineSettings::JavascriptCanOpenWindows, false);

    // 禁用JavaScript可以访问剪贴板
    settings->setAttribute(QWebEngineSettings::JavascriptCanAccessClipboard, false);

    // GPU渲染配置 - 可通过环境变量或配置文件控制
    configureGpuRendering(settings);

    // 设置页面缩放因子为1.0以避免缩放相关的渲染问题
    m_webView->setZoomFactor(1.0);

// 开发模式和生产模式网络访问设置
#ifdef DEV_MODE
    // 开发模式：使用配置文件设置或默认启用所有网络访问
    settings->setAttribute(QWebEngineSettings::AllowRunningInsecureContent, allowRunningInsecureContent);
    qCDebug(inkcop) << "[DEV MODE] WebEngine network access enabled for development";
#else
    // 生产模式：使用配置文件设置，保持本地网络访问能力以支持Ollama等本地服务
    settings->setAttribute(QWebEngineSettings::AllowRunningInsecureContent, allowRunningInsecureContent);
    qCDebug(inkcop) << "[PRODUCTION] Local network access configured for Ollama and other local services";
#endif

    // 禁用右键菜单
    m_webView->setContextMenuPolicy(Qt::NoContextMenu);

    // 开发模式下启用开发者工具
    QWebEngineProfile *profile = m_webView->page()->profile();
    profile->setHttpUserAgent("InkCop-Qt-Debug/1.0");

    // 连接权限请求信号，自动允许剪贴板权限
    // 注释掉：Qt 6.7.3中QWebEnginePermission API可能有变化
    /*
    connect(m_webView->page(), &QWebEnginePage::permissionRequested,
            this, [this](QWebEnginePermission permission) {
                // 自动允许剪贴板读写权限
                if (permission.permissionType() == QWebEnginePermission::PermissionType::ClipboardReadWrite) {
                    permission.grant();
                }
            });
    */

    // 连接信号和槽
    connect(m_webView, &QWebEngineView::loadStarted,
            this, &MainWindow::onLoadStarted);
    connect(m_webView, &QWebEngineView::loadProgress,
            this, &MainWindow::onLoadProgress);
    connect(m_webView, &QWebEngineView::loadFinished,
            this, &MainWindow::onLoadFinished);
    connect(m_webView, &QWebEngineView::urlChanged,
            this, &MainWindow::onUrlChanged);
}

void MainWindow::setupWebViewCommands()
{
    // 初始化 QWebChannel
    m_webChannel = new QWebChannel(this);
    m_webChannel->registerObject(QStringLiteral("qtWindow"), m_windowApi);
    m_webChannel->registerObject(QStringLiteral("databaseApi"), m_databaseApi);

    m_webChannel->registerObject(QStringLiteral("knowledgeApi"), m_knowledgeApi);
    m_webView->page()->setWebChannel(m_webChannel);
}

void MainWindow::loadWebApp()
{
    // 在加载Web应用之前注入主题设置
    QString script =
        "window.QT_THEME_SETTINGS = {"
        "  isDarkTheme: " +
        QString(m_isDarkTheme ? "true" : "false") + ""
                                                    "};"
                                                    "console.log('Qt injected theme settings, initial theme:', " +
        QString(m_isDarkTheme ? "'Dark'" : "'Light'") + ");";
    m_webView->page()->runJavaScript(script);
    qCDebug(inkcop) << "[THEME] Theme settings injected to web app:" << (m_isDarkTheme ? "Dark" : "Light");

// 根据构建类型决定加载方式
#ifdef DEV_MODE
    // 开发模式：优先尝试连接开发服务器
    QUrl devServerUrl("http://localhost:9000");
    qCDebug(inkcop) << "[DEV MODE] Trying to connect to dev server:" << devServerUrl.toString();

    if (isDevServerAvailable())
    {
        qCDebug(inkcop) << "[DEV MODE] Dev server available, using hot reload mode";
        m_webView->load(devServerUrl);
        setWindowTitle("inkCop (Development Mode - Hot Reload)");
        return;
    }
    else
    {
        qCDebug(inkcop) << "[DEV MODE] Dev server not available, fallback to embedded resources";
    }
#endif

    // 生产模式或开发服务器不可用时：从Qt资源加载嵌入式WebApp
    QUrl resourceUrl("qrc:/index.html");
    qCDebug(inkcop) << "[LOAD] Loading WebApp from embedded resources:" << resourceUrl.toString();

    // 验证资源文件是否存在
    if (!QFile::exists(":/index.html"))
    {
        qCCritical(inkcop) << "Error: Embedded resource file does not exist, please ensure the web application has been built correctly";
        // 显示错误页面或提示
        QString errorHtml =
            "<!DOCTYPE html>"
            "<html><head><title>Load Error</title></head>"
            "<body style='font-family: Arial; text-align: center; padding: 50px;'>"
            "<h1>Application Load Failed</h1>"
            "<p>Cannot find the web application resource file.</p>"
            "<p>Please ensure the application has been built correctly.</p>"
            "</body></html>";
        m_webView->setHtml(errorHtml);
        setWindowTitle("inkCop (Load Error)");
        return;
    }

    m_webView->load(resourceUrl);

#ifdef DEV_MODE
    setWindowTitle("inkCop (Development Mode - Embedded)");
#else
    setWindowTitle("inkCop");
#endif
}

bool MainWindow::isDevServerAvailable()
{
// 只在开发模式下检查开发服务器
#ifndef DEV_MODE
    return false;
#endif

    // 使用TCP socket检查开发服务器是否可用
    QTcpSocket socket;
    socket.connectToHost("localhost", 9000);
    bool connected = socket.waitForConnected(5000); // 等待5秒

    if (connected)
    {
        qCDebug(inkcop) << "[DEV MODE] Dev server connection successful (localhost:9000)";
        socket.disconnectFromHost();
        if (socket.state() != QAbstractSocket::UnconnectedState)
        {
            socket.waitForDisconnected(1000);
        }
    }
    else
    {
        qCDebug(inkcop) << "[DEV MODE] Dev server connection failed:" << socket.errorString();
    }

    return connected;
}

void MainWindow::onLoadStarted()
{
    qCDebug(inkcop) << "[LOAD] Page loading started";
}

void MainWindow::onLoadProgress(int progress)
{
    qCDebug(inkcop) << "[LOAD] Loading progress:" << progress << "%";
}

void MainWindow::onLoadFinished(bool ok)
{
    QUrl currentUrl = m_webView->url();
    qCDebug(inkcop) << "[LOAD] Page load finished, URL:" << currentUrl.toString() << ", Success:" << ok;

    if (ok)
    {
        // 延迟执行主题同步逻辑，确保Vue应用完全加载
        QTimer::singleShot(1000, this, [this]()
                           { syncThemeWithVue(); });

        // 再延迟一点同步抽屉状态，确保uiStore完全初始化
        QTimer::singleShot(1500, this, [this]()
                           {
                               if (m_qwkTitleBar) {
                                   m_qwkTitleBar->syncDrawerIconsFromFrontend();
                               } });
    }
    else
    {

        // 根据当前URL判断失败原因
        QString errorMessage;
        QString detailedInfo;

        if (currentUrl.scheme() == "http" || currentUrl.scheme() == "https")
        {
            // 网络URL加载失败
            errorMessage = "Development server connection failed";
            detailedInfo = "Cannot connect to development server (http://localhost:9000).\n\n"
                           "Possible reasons:\n"
                           "1. Development server not started (run: npm run dev)\n"
                           "2. Port 9000 is occupied by other programs\n"
                           "3. Network connection issues\n\n"
                           "The application will attempt to load embedded resources...";

#ifdef DEV_MODE
            // 开发模式下，尝试回退到嵌入式资源
            qCDebug(inkcop) << "[DEV MODE] Dev server connection failed, trying fallback to embedded resources";
            QTimer::singleShot(1000, this, [this]()
                               {
                    QUrl resourceUrl("qrc:/index.html");
                    qCDebug(inkcop) << "[LOAD] Fallback loading embedded resources:" << resourceUrl.toString();
                    m_webView->load(resourceUrl);
                    setWindowTitle("inkCop (Development Mode - Embedded)"); });
            return; // 不显示错误对话框，直接尝试回退
#endif
        }
        else if (currentUrl.scheme() == "qrc")
        {
            // 资源文件加载失败
            errorMessage = "Application resource load failed";
            detailedInfo = "Cannot load embedded web application resources.\n\n"
                           "Possible reasons:\n"
                           "1. Application build is incomplete\n"
                           "2. Resource file is damaged\n"
                           "3. Qt resource system error\n\n"
                           "Please rebuild the application or contact technical support.";
        }
        else
        {
            // 其他类型的加载失败
            errorMessage = "Page load failed";
            detailedInfo = "Unknown load error.\n\n"
                           "Please check the console output for detailed information.";
        }

        // 显示错误信息
        QMessageBox::warning(this, errorMessage, detailedInfo);
    }
}

void MainWindow::onUrlChanged(const QUrl &url)
{
    qCDebug(inkcop) << "URL changed:" << url;
}

void MainWindow::showAbout()
{
    QMessageBox::about(this, "关于 InkCop",
                       "<h3>InkCop - Writer's Copilot</h3>"
                       "<p>版本: 1.0.0</p>"
                       "<p>一个为作家设计的智能助手应用</p>"
                       "<p>基于Quasar框架和Qt构建</p>"
                       "<p>© 2024 InkCop Team</p>");
}

void MainWindow::toggleFullScreen()
{
    if (isFullScreen())
    {
        showNormal();
    }
    else
    {
        showFullScreen();
    }
}

void MainWindow::reloadPage()
{
    m_webView->reload();
}

void MainWindow::openDevTools()
{
    // 添加调试信息
    qCDebug(inkcop) << "[DEV] Current dev mode status:" << m_isDevMode;

    // 在生产模式下禁用开发者工具
    if (!m_isDevMode)
    {
        qCDebug(inkcop) << "[DEV] Dev tools not allowed in production mode";
        return;
    }

    // Qt WebEngine的开发者工具需要单独的窗口
    if (m_webView->page()->devToolsPage() == nullptr)
    {
        QWebEngineView *devToolsView = new QWebEngineView();
        devToolsView->setWindowTitle("InkCop Developer Tools");
        devToolsView->resize(1440, 960);

#ifdef Q_OS_WIN
        // 确保开发者工具窗口保持原生标题栏和边框
        devToolsView->setWindowFlags(Qt::Window);
        devToolsView->setAttribute(Qt::WA_NativeWindow, true);
        qCDebug(inkcop) << "[DEV] Developer tools window configured with native decorations";
#endif

        // 设置开发者工具页面
        m_webView->page()->setDevToolsPage(devToolsView->page());

        // 显示开发者工具窗口
        devToolsView->show();
        devToolsView->raise();
        devToolsView->activateWindow();

        qCDebug(inkcop) << "[DEV] Developer tools window opened";
    }
    else
    {
        qCDebug(inkcop) << "[DEV] Developer tools already open";
    }
}

void MainWindow::zoomIn()
{
    m_zoomFactor *= 1.1;
    m_webView->setZoomFactor(m_zoomFactor);
    qCDebug(inkcop) << "[ZOOM] Zoom:" << qRound(m_zoomFactor * 100) << "%";
}

void MainWindow::zoomOut()
{
    m_zoomFactor /= 1.1;
    m_webView->setZoomFactor(m_zoomFactor);
    qCDebug(inkcop) << "[ZOOM] Zoom:" << qRound(m_zoomFactor * 100) << "%";
}

void MainWindow::resetZoom()
{
    m_zoomFactor = 1.0;
    m_webView->setZoomFactor(m_zoomFactor);
    qCDebug(inkcop) << "Zoom: 100%";
}

void MainWindow::toggleMode()
{
    // 这个功能在当前设计下可能需要重新考虑。
    // 简单实现为重新加载页面
    qCDebug(inkcop) << "Switching mode, reloading page...";
    reloadPage();
}

// JavaScript可调用的窗口控制方法
Q_INVOKABLE void MainWindow::minimizeWindow()
{
    showMinimized();
}

Q_INVOKABLE void MainWindow::toggleMaximizeWindow()
{
    if (isMaximized())
    {
        showNormal();
        if (m_qwkTitleBar)
        {
            m_qwkTitleBar->updateMaximizeButton(false);
        }
    }
    else
    {
        showMaximized();
        if (m_qwkTitleBar)
        {
            m_qwkTitleBar->updateMaximizeButton(true);
        }
    }

    // 保存窗口状态
    saveWindowGeometry();
}

Q_INVOKABLE void MainWindow::closeWindow()
{
    minimizeToTray(); // 改为最小化到托盘
}

// JavaScript可调用的拖拽开始方法
Q_INVOKABLE void MainWindow::startWindowDrag(int x, int y)
{
    // 使用系统原生的窗口移动功能，这样可以突破窗口管理器的边界限制
    // 支持 X11、Windows、macOS 和主流 Wayland 合成器（GNOME、KDE）
    if (windowHandle())
    {
        bool success = windowHandle()->startSystemMove();
    }
}

// JavaScript可调用的拖拽移动方法
Q_INVOKABLE void MainWindow::moveWindow(int x, int y)
{
    if (m_dragging)
    {
        QPoint globalPos = QCursor::pos();
        move(globalPos - m_dragPosition);
    }
}

// JavaScript可调用的拖拽结束方法
Q_INVOKABLE void MainWindow::endWindowDrag()
{
    m_dragging = false;
}

// 获取鼠标在窗口边缘的位置
Qt::CursorShape MainWindow::getCursorShape(const QPoint &point)
{
    const int margin = 15;     // 增大边缘检测范围
    const int cornerSize = 20; // 角落检测范围更大
    QRect rect = this->rect();

    bool left = point.x() <= margin;
    bool right = point.x() >= rect.width() - margin;
    bool top = point.y() <= margin;
    bool bottom = point.y() >= rect.height() - margin;

    // 优先检测角落（更大的范围）
    bool cornerLeft = point.x() <= cornerSize;
    bool cornerRight = point.x() >= rect.width() - cornerSize;
    bool cornerTop = point.y() <= cornerSize;
    bool cornerBottom = point.y() >= rect.height() - cornerSize;

    if (cornerTop && cornerLeft)
        return Qt::SizeFDiagCursor; // 左上角
    if (cornerTop && cornerRight)
        return Qt::SizeBDiagCursor; // 右上角
    if (cornerBottom && cornerLeft)
        return Qt::SizeBDiagCursor; // 左下角
    if (cornerBottom && cornerRight)
        return Qt::SizeFDiagCursor; // 右下角
    if (left)
        return Qt::SizeHorCursor; // 左边
    if (right)
        return Qt::SizeHorCursor; // 右边
    if (top)
        return Qt::SizeVerCursor; // 上边
    if (bottom)
        return Qt::SizeVerCursor; // 下边

    return Qt::ArrowCursor; // 默认鼠标形状
}

// 获取窗口大小调整的方向
MainWindow::ResizeDirection MainWindow::getResizeDirection(const QPoint &point)
{
    const int margin = 15;     // 边缘检测范围
    const int cornerSize = 20; // 角落检测范围
    QRect rect = this->rect();

    bool left = point.x() <= margin;
    bool right = point.x() >= rect.width() - margin;
    bool top = point.y() <= margin;
    bool bottom = point.y() >= rect.height() - margin;

    // 优先检测角落
    bool cornerLeft = point.x() <= cornerSize;
    bool cornerRight = point.x() >= rect.width() - cornerSize;
    bool cornerTop = point.y() <= cornerSize;
    bool cornerBottom = point.y() >= rect.height() - cornerSize;

    if (cornerTop && cornerLeft)
        return ResizeTopLeft;
    if (cornerTop && cornerRight)
        return ResizeTopRight;
    if (cornerBottom && cornerLeft)
        return ResizeBottomLeft;
    if (cornerBottom && cornerRight)
        return ResizeBottomRight;
    if (left)
        return ResizeLeft;
    if (right)
        return ResizeRight;
    if (top)
        return ResizeTop;
    if (bottom)
        return ResizeBottom;

    return ResizeNone;
}

// 覆盖原有的鼠标事件处理
void MainWindow::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        QPoint localPos = event->position().toPoint();

        // 检查是否在工具栏区域
        if (localPos.y() <= 40)
        { // 工具栏高度改为40
            // 使用系统原生的窗口移动功能
            if (windowHandle())
            {
                windowHandle()->startSystemMove();
            }
            else
            {
                // 备用方案
                m_dragging = true;
                m_dragPosition = event->globalPosition().toPoint() - frameGeometry().topLeft();
            }
            return;
        }

        // 检查是否在窗口边缘进行大小调整
        m_resizeDirection = getResizeDirection(localPos);
        if (m_resizeDirection != ResizeNone)
        {
            m_resizing = true;
            m_resizeStartPos = event->globalPosition().toPoint();
            m_resizeStartGeometry = geometry();
            qCDebug(inkcop) << "MainWindow: Start window resizing, direction:" << m_resizeDirection;
        }
    }

    QMainWindow::mousePressEvent(event);
}

void MainWindow::mouseMoveEvent(QMouseEvent *event)
{
    if (m_resizing)
    {
        // 处理窗口大小调整
        QPoint globalPos = event->globalPosition().toPoint();
        QRect newGeometry = geometry();
        QPoint delta = globalPos - m_resizeStartPos;

        switch (m_resizeDirection)
        {
        case ResizeTop:
            newGeometry.setTop(newGeometry.top() + delta.y());
            break;
        case ResizeBottom:
            newGeometry.setBottom(newGeometry.bottom() + delta.y());
            break;
        case ResizeLeft:
            newGeometry.setLeft(newGeometry.left() + delta.x());
            break;
        case ResizeRight:
            newGeometry.setRight(newGeometry.right() + delta.x());
            break;
        case ResizeTopLeft:
            newGeometry.setTopLeft(newGeometry.topLeft() + delta);
            break;
        case ResizeTopRight:
            newGeometry.setTop(newGeometry.top() + delta.y());
            newGeometry.setRight(newGeometry.right() + delta.x());
            break;
        case ResizeBottomLeft:
            newGeometry.setBottom(newGeometry.bottom() + delta.y());
            newGeometry.setLeft(newGeometry.left() + delta.x());
            break;
        case ResizeBottomRight:
            newGeometry.setBottomRight(newGeometry.bottomRight() + delta);
            break;
        default:
            break;
        }

        // 确保窗口不会小于最小尺寸
        if (newGeometry.width() >= 400 && newGeometry.height() >= 300)
        {
            setGeometry(newGeometry);
            m_resizeStartPos = globalPos;
        }
    }
    else if (m_dragging)
    {
        // 处理窗口拖拽
        QPoint globalPos = event->globalPosition().toPoint();
        move(globalPos - m_dragPosition);
    }
    else
    {
        // 不在拖拽状态下，动态更新鼠标指针形状
        QPoint localPos = event->position().toPoint();
        Qt::CursorShape shape = getCursorShape(localPos);
        setCursor(shape);
    }

    QMainWindow::mouseMoveEvent(event);
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        if (m_resizing)
        {
            m_resizing = false;
            m_resizeDirection = ResizeNone;

            // 恢复鼠标指针形状
            setCursor(Qt::ArrowCursor);

            qCDebug(inkcop) << "MainWindow: Stop window resizing";
        }
        else if (m_dragging)
        {
            m_dragging = false;
            qCDebug(inkcop) << "MainWindow: Stop window dragging";
        }
    }

    QMainWindow::mouseReleaseEvent(event);
}

// 事件过滤器处理工具栏拖拽
bool MainWindow::eventFilter(QObject *obj, QEvent *event)
{

    return QMainWindow::eventFilter(obj, event);
}

// 处理系统主题变化事件
void MainWindow::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::ApplicationPaletteChange)
    {
        qCDebug(inkcop) << "Detected system palette change, reapplying theme";
        // 不再调用 detectSystemTheme()
        // 可以选择重新加载主题设置
        loadThemeSetting();
    }
    QMainWindow::changeEvent(event);
}

// 初始化主题设置
void MainWindow::loadThemeSetting()
{
    // 优先从数据库读取主题设置
    bool themeFromDatabase = false;
    bool hasThemeInDatabase = false;

    if (m_databaseApi)
    {
        QJsonObject appSettings = m_databaseApi->getAppSettings();
        if (!appSettings.isEmpty() && appSettings.contains("base"))
        {
            QJsonObject baseSettings = appSettings["base"].toObject();
            if (baseSettings.contains("theme"))
            {
                QString theme = baseSettings["theme"].toString();
                themeFromDatabase = (theme == "dark");
                hasThemeInDatabase = true;
                qCDebug(inkcop) << "[THEME] 从数据库读取主题设置:" << (themeFromDatabase ? "暗色" : "亮色");
            }
        }
    }

    if (hasThemeInDatabase)
    {
        m_isDarkTheme = themeFromDatabase;
    }
    else
    {
        // 如果数据库中没有主题设置，使用QSettings作为fallback
        Settings settings;
        settings.syncSettings();
        m_isDarkTheme = settings.getSavedTheme();
        qCDebug(inkcop) << "[THEME] 数据库中无主题设置，使用QSettings fallback:" << (m_isDarkTheme ? "暗色" : "亮色");
    }

    updateTheme();

    // 同步主题设置到Web应用
    QString script = QString("if(window.uiStore) { window.uiStore.setTheme(%1); }")
                         .arg(m_isDarkTheme ? "true" : "false");
    m_webView->page()->runJavaScript(script);
}

// 与Vue应用同步主题设置
void MainWindow::syncThemeWithVue()
{
    // Settings settings;
    // m_isDarkTheme = settings.getSavedTheme();
    // 主题设置现在通过 QT_THEME_SETTINGS 在页面加载前注入
    // 这里只需要确保WebChannel已建立连接即可
    QString script = QString("if(window.uiStore) { window.uiStore.setTheme(%1); }")
                         .arg(m_isDarkTheme ? "true" : "false");
    m_webView->page()->runJavaScript(script);
}

// JavaScript可调用的方法：检查是否有主题设置
Q_INVOKABLE bool MainWindow::hasThemeSetting()
{
    Settings settings;
    return settings.hasThemeSetting();
}

// JavaScript可调用的方法：获取保存的主题设置
Q_INVOKABLE bool MainWindow::getSavedTheme()
{
    Settings settings;
    return settings.getSavedTheme();
}

// JavaScript可调用的方法：保存主题设置
Q_INVOKABLE void MainWindow::saveThemeSetting(bool isDark)
{
    Settings settings;
    settings.saveTheme(isDark);
    qCDebug(inkcop) << "Saved theme settings:" << (isDark ? "Dark" : "Light");
}

// JavaScript可调用的方法：从Vue应用设置主题
Q_INVOKABLE void MainWindow::setThemeFromVue(bool isDark)
{
    qCDebug(inkcop) << "Vue application notified theme state:" << (isDark ? "Dark" : "Light");

    // 如果Qt应用还没有保存的主题设置，则保存Vue应用的主题状态
    Settings settings;
    if (!settings.hasThemeSetting())
    {
        settings.saveTheme(isDark);
        qCDebug(inkcop) << "First saved theme settings:" << (isDark ? "Dark" : "Light");
    }

    // 更新Qt应用的主题
    if (m_isDarkTheme != isDark)
    {
        m_isDarkTheme = isDark;
        updateTheme();
        qCDebug(inkcop) << "Qt application theme updated to:" << (isDark ? "Dark" : "Light");
    }
}

// JavaScript可调用的方法：前端通知主题已变化，Qt端重新从数据库读取
Q_INVOKABLE void MainWindow::onThemeChangedFromFrontend()
{
    qCDebug(inkcop) << "[THEME] 前端通知主题已变化，重新从数据库读取主题设置";

    // 重新从数据库读取主题设置
    bool themeFromDatabase = false;
    bool hasThemeInDatabase = false;

    if (m_databaseApi)
    {
        QJsonObject appSettings = m_databaseApi->getAppSettings();
        if (!appSettings.isEmpty() && appSettings.contains("base"))
        {
            QJsonObject baseSettings = appSettings["base"].toObject();
            if (baseSettings.contains("theme"))
            {
                QString theme = baseSettings["theme"].toString();
                themeFromDatabase = (theme == "dark");
                hasThemeInDatabase = true;
                qCDebug(inkcop) << "[THEME] 从数据库重新读取主题设置:" << (themeFromDatabase ? "暗色" : "亮色");
            }
        }
    }

    if (hasThemeInDatabase)
    {
        // 无论当前状态如何，都强制更新主题以确保UI同步
        m_isDarkTheme = themeFromDatabase;
        updateTheme();
        qCDebug(inkcop) << "[THEME] Qt端主题已强制同步更新:" << (m_isDarkTheme ? "暗色" : "亮色");
    }
    else
    {
        qCDebug(inkcop) << "[THEME] 数据库中未找到主题设置，保持当前状态";
    }
}

// JavaScript可调用的方法：前端直接设置Qt端主题
Q_INVOKABLE void MainWindow::setThemeDirectly(bool isDark)
{
    qCDebug(inkcop) << "[THEME] 前端直接设置Qt端主题:" << (isDark ? "暗色" : "亮色");

    // 直接更新Qt端主题状态
    m_isDarkTheme = isDark;

    // 立即更新Qt端UI
    updateTheme();

    qCDebug(inkcop) << "[THEME] Qt端主题已直接更新完成";
}

void MainWindow::toggleMicaEffect()
{
#ifdef Q_OS_WIN
    static bool micaEnabled = false; // 默认关闭状态
    micaEnabled = !micaEnabled;

    qDebug() << "Toggling Mica effect to:" << micaEnabled;

    if (micaEnabled)
    {
        // 启用Mica效果
        m_windowAgent->setWindowAttribute("dwm-blur", false);
        m_windowAgent->setWindowAttribute("acrylic", false);
        m_windowAgent->setWindowAttribute("mica-alt", false);

        bool result = m_windowAgent->setWindowAttribute("mica", true);
        qDebug() << "Mica enable result:" << result;

        m_qwkTitleBar->setMicaEffectEnabled(true);
        setProperty("custom-style", true);
    }
    else
    {
        // 禁用Mica效果
        bool result = m_windowAgent->setWindowAttribute("mica", false);
        qDebug() << "Mica disable result:" << result;

        m_qwkTitleBar->setMicaEffectEnabled(false);
        setProperty("custom-style", false);
    }

    style()->polish(this);

    // 更新主窗口主题以匹配Mica效果状态
    updateTheme();
#endif
}

void MainWindow::paintEvent(QPaintEvent *event)
{
    QMainWindow::paintEvent(event);
    // 移除1px边框绘制 - 不再绘制窗口边框
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    // 拦截窗口关闭事件，最小化到托盘而不是退出应用
    event->ignore();  // 忽略关闭事件
    minimizeToTray(); // 最小化到托盘
    qCDebug(inkcop) << "[CLOSE] Window close intercepted, minimized to tray";
}

void MainWindow::onDevToolsRequested()
{
    openDevTools();
}

void MainWindow::onModeToggleRequested()
{
    toggleMode();
}

void MainWindow::onThemeToggleRequested()
{
    toggleTheme();
}

void MainWindow::toggleLeftDrawer()
{
    // 调用Vue应用中的uiStore.toggleLeftDrawer()方法
    QString script =
        "if (window.uiStore && window.uiStore.toggleLeftDrawer) { "
        "  window.uiStore.toggleLeftDrawer(); "
        "  setTimeout(function() { "
        "    if (window.qtWindow && window.qtWindow.updateLeftDrawerState) { "
        "      window.qtWindow.updateLeftDrawerState(window.uiStore.leftDrawerOpen); "
        "    } "
        "  }, 50); "
        "} else { "
        "  console.warn('uiStore not found'); "
        "}";

    if (m_webView)
    {
        m_webView->page()->runJavaScript(script);
    }
}

void MainWindow::toggleRightDrawer()
{
    // 调用Vue应用中的uiStore.toggleRightDrawer()方法
    QString script =
        "if (window.uiStore && window.uiStore.toggleRightDrawer) { "
        "  window.uiStore.toggleRightDrawer(); "
        "  setTimeout(function() { "
        "    if (window.qtWindow && window.qtWindow.updateRightDrawerState) { "
        "      window.qtWindow.updateRightDrawerState(window.uiStore.rightDrawerOpen); "
        "    } "
        "  }, 50); "
        "} else { "
        "  console.warn('uiStore not found'); "
        "}";

    if (m_webView)
    {
        m_webView->page()->runJavaScript(script);
    }
}

void MainWindow::syncDrawerStatesFromFrontend()
{
    // 从前端获取当前抽屉状态并同步到Qt
    QString script =
        "if (window.uiStore && window.qtWindow) { "
        "  if (window.qtWindow.updateLeftDrawerState) { "
        "    window.qtWindow.updateLeftDrawerState(window.uiStore.leftDrawerOpen); "
        "  } "
        "  if (window.qtWindow.updateRightDrawerState) { "
        "    window.qtWindow.updateRightDrawerState(window.uiStore.rightDrawerOpen); "
        "  } "
        "}";

    if (m_webView)
    {
        m_webView->page()->runJavaScript(script);
    }
}

void MainWindow::loadRenderingConfig()
{
    QSettings config("qt-rendering.conf", QSettings::IniFormat);

    // 读取GPU渲染配置
    bool enable2DCanvas = config.value("GPU_RENDERING/enable_2d_canvas_acceleration", false).toBool();
    bool enableWebGL = config.value("GPU_RENDERING/enable_webgl", false).toBool();
    bool enableScrollAnimation = config.value("GPU_RENDERING/enable_scroll_animation", false).toBool();

    // 读取显示配置
    double zoomFactor = config.value("DISPLAY/zoom_factor", 1.0).toDouble();

    // 读取故障排除配置
    bool forceSwRendering = config.value("TROUBLESHOOTING/force_software_rendering", true).toBool();
    bool disableGpuThread = config.value("TROUBLESHOOTING/disable_gpu_thread", true).toBool();
    bool verboseLogging = config.value("TROUBLESHOOTING/enable_verbose_logging", false).toBool();

    qCDebug(inkcop) << "[CONFIG] Rendering configuration loaded:";
    qCDebug(inkcop) << "  - 2D Canvas acceleration:" << enable2DCanvas;
    qCDebug(inkcop) << "  - WebGL:" << enableWebGL;
    qCDebug(inkcop) << "  - Scroll animation:" << enableScrollAnimation;
    qCDebug(inkcop) << "  - Zoom factor:" << zoomFactor;
    qCDebug(inkcop) << "  - Force software rendering:" << forceSwRendering;

    // 设置缩放因子
    if (m_webView && zoomFactor > 0.5 && zoomFactor <= 3.0)
    {
        m_webView->setZoomFactor(zoomFactor);
    }
}

void MainWindow::configureGpuRendering(QWebEngineSettings *settings)
{
    // 先加载配置文件
    QSettings config("qt-rendering.conf", QSettings::IniFormat);

    // 从配置文件读取设置，如果不存在则使用安全默认值
    bool enable2DCanvas = config.value("GPU_RENDERING/enable_2d_canvas_acceleration", false).toBool();
    bool enableWebGL = config.value("GPU_RENDERING/enable_webgl", false).toBool();
    bool enableScrollAnimation = config.value("GPU_RENDERING/enable_scroll_animation", false).toBool();
    bool autoLoadImages = config.value("PERFORMANCE/auto_load_images", true).toBool();
    bool enableJavaScript = config.value("PERFORMANCE/enable_javascript", true).toBool();

    // 应用GPU渲染设置
    settings->setAttribute(QWebEngineSettings::Accelerated2dCanvasEnabled, enable2DCanvas);
    settings->setAttribute(QWebEngineSettings::WebGLEnabled, enableWebGL);
    settings->setAttribute(QWebEngineSettings::ScrollAnimatorEnabled, enableScrollAnimation);

    // 应用性能设置
    settings->setAttribute(QWebEngineSettings::AutoLoadImages, autoLoadImages);
    settings->setAttribute(QWebEngineSettings::JavascriptEnabled, enableJavaScript);

    // 安全设置（始终应用）
    settings->setAttribute(QWebEngineSettings::SpatialNavigationEnabled, false);
    settings->setAttribute(QWebEngineSettings::TouchIconsEnabled, false);
    settings->setAttribute(QWebEngineSettings::PlaybackRequiresUserGesture, true);
    settings->setAttribute(QWebEngineSettings::PdfViewerEnabled, true);
    settings->setAttribute(QWebEngineSettings::PluginsEnabled, false);

    qCDebug(inkcop) << "[GPU] Rendering configuration applied:";
    qCDebug(inkcop) << "  - 2D Canvas acceleration:" << enable2DCanvas;
    qCDebug(inkcop) << "  - WebGL:" << enableWebGL;
    qCDebug(inkcop) << "  - Scroll animation:" << enableScrollAnimation;
    qCDebug(inkcop) << "  - Auto load images:" << autoLoadImages;
}

void MainWindow::onWebPageLoadFinished(bool ok)
{
    if (ok)
    {
        // 页面加载成功后，将Qt的当前主题状态同步给Quasar
        qCDebug(inkcop) << "Web page loaded. Syncing theme to Quasar. isDark:" << m_isDarkTheme;
        QString script = QString("if(window.uiStore) { window.uiStore.setTheme(%1); }").arg(m_isDarkTheme ? "true" : "false");
        m_webView->page()->runJavaScript(script);
    }
}

void MainWindow::setupConnections()
{
    // ... (其他 connect 不变) ...
    // ...
    connect(m_webView->page(), &QWebEnginePage::loadFinished, this, &MainWindow::onWebPageLoadFinished);
}

void MainWindow::toggleTheme()
{

    // 确保从设置中获取最新主题状态
    Settings settings;
    m_isDarkTheme = settings.getSavedTheme();
    m_isDarkTheme = !m_isDarkTheme;

    // 保存新的主题设置
    settings.saveTheme(m_isDarkTheme);

    // 更新Qt应用主题
    updateTheme();

    // 通知Vue应用主题变化
    QString script =
        "if (window.uiStore && window.uiStore.toggleTheme) {"
        "  window.uiStore.toggleTheme();"
        "  console.log('Qt set theme through uiStore, current state:', window.uiStore.isDarkTheme);"
        "} else {"
        "  console.warn('uiStore not found, cannot set theme');"
        "}";

    m_webView->page()->runJavaScript(script);
}

void MainWindow::updateTheme()
{
    // 检查是否启用了Mica效果（默认关闭）
    bool micaEnabled = false;
#ifdef Q_OS_WIN
    if (m_qwkTitleBar && m_qwkTitleBar->isMicaEffectEnabled())
    {
        micaEnabled = true;
    }
#endif

    // 更新主窗口主题
    QString style;
    if (micaEnabled)
    {
        // 如果启用了Mica效果，使用透明背景
        style = QString(
            "QMainWindow {"
            "    background-color: transparent;"
            "}"
            "QWidget#centralWidget {"
            "    background-color: transparent;"
            "}");
        qDebug() << "MainWindow: Using transparent background for Mica effect";
    }
    else
    {
        // 使用实色背景
        QString bgColor = m_isDarkTheme ? "#1e1e1e" : "#ffffff";
        style = QString(
                    "QMainWindow {"
                    "    background-color: %1;"
                    "}"
                    "QWidget#centralWidget {"
                    "    background-color: %1;"
                    "}")
                    .arg(bgColor);
    }
    setStyleSheet(style);

    // 更新QWindowKit标题栏主题
    if (m_qwkTitleBar)
    {
        // 保存当前Mica效果状态
        bool currentMicaState = m_qwkTitleBar->isMicaEffectEnabled();

        m_qwkTitleBar->updateTheme(m_isDarkTheme);

        // 恢复Mica效果状态（因为updateTheme可能会重置它）
        if (currentMicaState)
        {
            m_qwkTitleBar->setMicaEffectEnabled(true);
            qDebug() << "MainWindow: Restored Mica effect after theme update";
        }

        // 主题变化后同步前端抽屉状态以更新图标
        m_qwkTitleBar->syncDrawerIconsFromFrontend();

        // 强制刷新标题栏UI
        m_qwkTitleBar->update();
        m_qwkTitleBar->repaint();
    }

    // 通知Vue应用主题变化
    emit themeChanged(m_isDarkTheme);

    // 同步注入主题设置到Web应用
    QString script =
        "window.QT_THEME_SETTINGS = {"
        "  isDarkTheme: " +
        QString(m_isDarkTheme ? "true" : "false") + ""
                                                    "};"
                                                    "console.log('Qt updated and injected theme settings, current theme:', " +
        QString(m_isDarkTheme ? "'Dark'" : "'Light'") + ");";
    m_webView->page()->runJavaScript(script);
}

void MainWindow::setupTrayIcon()
{
    // 创建托盘菜单
    m_trayMenu = new QMenu(this);
    QAction *showAction = m_trayMenu->addAction("Show Main Window");
    m_trayMenu->addSeparator(); // 添加分隔线
    QAction *quitAction = m_trayMenu->addAction("Quit Application");

    // 创建托盘图标
    m_trayIcon = new QSystemTrayIcon(this);
    m_trayIcon->setIcon(QIcon(":/icons/favicon-32x32.png"));
    m_trayIcon->setToolTip("inkCop - Click to show/hide window");
    m_trayIcon->setContextMenu(m_trayMenu);

    // 连接信号
    connect(showAction, &QAction::triggered, this, [this]()
            {
        showNormal();
        activateWindow();
        raise(); });
    connect(quitAction, &QAction::triggered, this, [this]()
            {
                m_trayIcon->hide();   // 先隐藏托盘图标
                QApplication::quit(); // 彻底退出应用
            });
    connect(m_trayIcon, &QSystemTrayIcon::activated, this, &MainWindow::onTrayIconActivated);

    // 显示托盘图标
    m_trayIcon->show();
}

void MainWindow::onTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    if (reason == QSystemTrayIcon::DoubleClick)
    {
        if (isVisible())
        {
            hide();
        }
        else
        {
            showNormal();
            activateWindow();
        }
    }
}

void MainWindow::minimizeToTray()
{
    hide();
    // 移除托盘提示
}

// 窗口几何管理方法
void MainWindow::setupWindowGeometry()
{
    Settings settings;

    // 检查是否有保存的窗口几何信息
    if (settings.hasWindowGeometry() && settings.hasWindowState())
    {
        // 恢复保存的窗口几何信息
        restoreWindowGeometry();
    }
    else
    {
        // 第一次启动，设置默认窗口大小和位置
        QScreen *screen = QApplication::primaryScreen();
        QRect screenGeometry = screen->availableGeometry();

        // 使用桌面分辨率的一半作为窗口大小
        int width = screenGeometry.width() / 2;
        int height = screenGeometry.height() / 2;

        // 确保窗口不会太小
        width = qMax(width, 800);
        height = qMax(height, 600);

        // 窗口位于桌面正中心
        int x = screenGeometry.x() + (screenGeometry.width() - width) / 2;
        int y = screenGeometry.y() + (screenGeometry.height() - height) / 2;

        setGeometry(x, y, width, height);

        // qCDebug(inkcop) << "[GEOMETRY] First startup, set default window geometry:";
        // qCDebug(inkcop) << "  Screen available area:" << screenGeometry;
        // qCDebug(inkcop) << "  Window geometry:" << QRect(x, y, width, height);

        // 保存初始几何信息
        saveWindowGeometry();
    }
}

void MainWindow::saveWindowGeometry()
{
    Settings settings;

    // 保存窗口几何信息（位置和大小）
    settings.saveWindowGeometry(geometry());

    // 保存窗口状态（是否最大化）
    settings.saveWindowState(isMaximized());

    // 同步设置
    settings.syncSettings();
}

void MainWindow::restoreWindowGeometry()
{
    Settings settings;

    // 恢复窗口几何信息
    QRect savedGeometry = settings.getSavedWindowGeometry();
    bool wasMaximized = settings.getSavedWindowState();

    if (!savedGeometry.isEmpty())
    {
        // 验证保存的几何信息是否在当前屏幕范围内
        QScreen *screen = QApplication::primaryScreen();
        QRect screenGeometry = screen->availableGeometry();

        // 确保窗口至少部分在屏幕内
        if (savedGeometry.intersects(screenGeometry))
        {
            setGeometry(savedGeometry);
        }
        else
        {
            // 如果保存的位置不在屏幕内，重新居中但保持大小
            int width = qMax(savedGeometry.width(), 800);
            int height = qMax(savedGeometry.height(), 600);
            int x = screenGeometry.x() + (screenGeometry.width() - width) / 2;
            int y = screenGeometry.y() + (screenGeometry.height() - height) / 2;

            setGeometry(x, y, width, height);
            // qCDebug(inkcop) << "[GEOMETRY] Saved position not in screen, re-centered:" << QRect(x, y, width, height);
        }

        // 恢复最大化状态
        if (wasMaximized)
        {
            showMaximized();
        }
    }
}

// 窗口几何事件处理
void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);

    // 只有在窗口不是最大化状态时才保存几何信息
    // 避免在窗口初始化过程中频繁保存
    static bool isInitialized = false;
    if (!isInitialized)
    {
        isInitialized = true;
        return;
    }

    if (!isMaximized())
    {
        // 使用定时器延迟保存，避免在拖拽过程中频繁保存
        static QTimer *saveTimer = nullptr;
        if (!saveTimer)
        {
            saveTimer = new QTimer(this);
            saveTimer->setSingleShot(true);
            saveTimer->setInterval(500); // 500ms延迟
            connect(saveTimer, &QTimer::timeout, this, &MainWindow::saveWindowGeometry);
        }
        saveTimer->start();
    }
}

void MainWindow::moveEvent(QMoveEvent *event)
{
    QMainWindow::moveEvent(event);

    // 只有在窗口不是最大化状态时才保存几何信息
    // 避免在窗口初始化过程中频繁保存
    static bool isInitialized = false;
    if (!isInitialized)
    {
        isInitialized = true;
        return;
    }

    if (!isMaximized())
    {
        // 使用定时器延迟保存，避免在拖拽过程中频繁保存
        static QTimer *saveTimer = nullptr;
        if (!saveTimer)
        {
            saveTimer = new QTimer(this);
            saveTimer->setSingleShot(true);
            saveTimer->setInterval(500); // 500ms延迟
            connect(saveTimer, &QTimer::timeout, this, &MainWindow::saveWindowGeometry);
        }
        saveTimer->start();
    }
}