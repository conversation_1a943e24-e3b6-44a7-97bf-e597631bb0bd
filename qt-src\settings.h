#ifndef SETTINGS_H
#define SETTINGS_H

#include <QSettings>
#include <QVariant>
#include <QRect>
#include <QPoint>
#include <QSize>

class Settings
{
public:
    Settings();

    // 主题设置
    void saveTheme(bool isDark);
    bool getSavedTheme() const;
    bool hasThemeSetting() const;
    void syncSettings(); // 确保设置文件已同步
    bool detectSystemTheme() const; // 探测系统主题

    // 窗口几何设置
    void saveWindowGeometry(const QRect &geometry);
    QRect getSavedWindowGeometry() const;
    bool hasWindowGeometry() const;

    void saveWindowState(bool isMaximized);
    bool getSavedWindowState() const;
    bool hasWindowState() const;

private:
    QSettings m_settings;
};

#endif // SETTINGS_H