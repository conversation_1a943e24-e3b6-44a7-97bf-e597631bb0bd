# 抽屉图标实现完成

## 功能概述

成功实现了左右抽屉按钮的动态图标切换功能，支持明暗主题和开启/关闭状态。

## 实现的功能

### 1. 动态图标切换

- ✅ 根据抽屉状态（开启/关闭）自动切换图标
- ✅ 根据主题（明暗）自动选择对应颜色的图标
- ✅ 支持自适应主题和专用主题版本

### 2. 状态同步

- ✅ 前端抽屉状态变化时自动更新Qt标题栏图标
- ✅ 通过JavaScript桥接实现状态同步
- ✅ 支持左右抽屉独立状态管理

### 3. 图标资源

- ✅ 创建了12个SVG图标文件（左右抽屉 × 开启关闭 × 明暗主题）
- ✅ 右侧图标通过水平翻转左侧图标实现
- ✅ 所有图标已添加到Qt资源系统

## 文件结构

### 图标文件 (public/icons/ui/)

```
left_drawer_closed.svg          # 自适应主题版本
left_drawer_opened.svg
left_drawer_closed_light.svg    # 专用主题版本
left_drawer_closed_dark.svg
left_drawer_opened_light.svg
left_drawer_opened_dark.svg
right_drawer_closed.svg         # 自适应主题版本
right_drawer_opened.svg
right_drawer_closed_light.svg   # 专用主题版本
right_drawer_closed_dark.svg
right_drawer_opened_light.svg
right_drawer_opened_dark.svg
```

### 修改的代码文件

- `qt-src/qwkcustomtitlebar.h` - 添加状态变量和方法声明
- `qt-src/qwkcustomtitlebar.cpp` - 实现图标更新逻辑
- `qt-src/mainwindow.h` - 添加标题栏访问方法
- `qt-src/mainwindow.cpp` - 修复JavaScript语法，添加状态同步
- `qt-src/windowapi.h` - 添加状态更新方法声明
- `qt-src/windowapi.cpp` - 实现状态更新方法
- `webapp.qrc` - 添加图标资源

## 技术实现

### 图标路径格式

```cpp
QString iconPath = QString(":/icons/ui/%1_drawer_%2%3.svg")
    .arg(side)        // "left" 或 "right"
    .arg(state)       // "opened" 或 "closed"
    .arg(themeSuffix); // "_light" 或 "_dark"
```

### 图标切换逻辑

1. **根据主题选择明暗图标**：`_light` 或 `_dark` 后缀
2. **根据前端状态选择开启/关闭图标**：基于 `uiStore.leftDrawerOpen` 和 `uiStore.rightDrawerOpen`

### 状态更新流程

1. 用户点击抽屉按钮
2. Qt触发JavaScript代码切换前端抽屉状态
3. JavaScript使用50ms延迟后调用qtWindow.updateDrawerState()同步状态到Qt
4. Qt根据接收到的真实状态更新标题栏按钮图标

### 状态同步时机

- **应用启动时**：页面加载完成后自动同步前端状态
- **主题切换时**：主题变化后重新同步状态以更新图标颜色
- **抽屉切换时**：每次切换后延迟50ms同步最新状态（确保Vue响应式更新完成）

### 主题适配

- 浅色主题：使用深色图标 (#2c2c2c)
- 深色主题：使用浅色图标 (#e0e0e0)
- 自适应版本：使用CSS媒体查询自动切换

## 使用方法

### 在Qt中更新状态

```cpp
// 通过WindowApi更新
windowApi->updateLeftDrawerState(true);
windowApi->updateRightDrawerState(false);

// 直接通过标题栏更新
titleBar->updateLeftDrawerState(true);
titleBar->updateRightDrawerState(false);
```

### 在JavaScript中更新状态

```javascript
// 通过qtWindow更新（基于uiStore的真实状态）
window.qtWindow.updateLeftDrawerState(window.uiStore.leftDrawerOpen);
window.qtWindow.updateRightDrawerState(window.uiStore.rightDrawerOpen);
```

### 同步前端状态到Qt

```javascript
// 从前端同步当前状态到Qt（用于初始化或主题切换后）
if (window.uiStore && window.qtWindow) {
  window.qtWindow.updateLeftDrawerState(window.uiStore.leftDrawerOpen);
  window.qtWindow.updateRightDrawerState(window.uiStore.rightDrawerOpen);
}
```

## 图标设计特点

### SVG变换（右侧图标）

```svg
<g transform="scale(-1,1) translate(-1024,0)">
  <!-- 左侧图标路径 -->
</g>
```

### 自适应主题CSS

```css
@media (prefers-color-scheme: dark) {
  .theme-adaptive {
    fill: #e0e0e0;
  }
}
@media (prefers-color-scheme: light) {
  .theme-adaptive {
    fill: #2c2c2c;
  }
}
```

## 回退机制

如果SVG图标文件不存在，会显示Unicode符号：

- 左侧抽屉：◧
- 右侧抽屉：◨

## 构建要求

1. 确保前端已构建：`npm run build` 或 `bun run build`
2. 重新构建Qt项目以包含新的资源文件
3. 确保webapp.qrc文件包含所有图标资源

## 测试验证

- [x] 按钮点击事件正确触发
- [x] JavaScript代码正确执行
- [x] 前端抽屉状态正确切换
- [x] Qt图标根据状态正确更新
- [x] 主题切换时图标颜色正确变化
- [x] 左右抽屉独立工作

## 注意事项

1. 图标更新依赖于前端的uiStore和qtBridge对象
2. 确保QWindowKit正确设置了按钮的HitTestVisible
3. 图标文件路径使用Qt资源系统格式：`:/icons/ui/filename.svg`
4. 状态同步需要JavaScript桥接正常工作
