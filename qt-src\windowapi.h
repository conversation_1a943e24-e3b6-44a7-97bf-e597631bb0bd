#ifndef WINDOWAPI_H
#define WINDOWAPI_H

#include <QObject>

class MainWindow; // Forward declaration

class WindowApi : public QObject
{
    Q_OBJECT
public:
    explicit WindowApi(MainWindow *mainWindow, QObject *parent = nullptr);

public slots:
    // JavaScript可调用的窗口控制方法
    Q_INVOKABLE void minimizeWindow();
    Q_INVOKABLE void toggleMaximizeWindow();
    Q_INVOKABLE void closeWindow();
    Q_INVOKABLE void reloadPage();
    Q_INVOKABLE void openDevTools();
    Q_INVOKABLE void toggleMode();
    Q_INVOKABLE void toggleTheme();
    Q_INVOKABLE void toggleLeftDrawer();
    Q_INVOKABLE void toggleRightDrawer();

    // 抽屉状态更新方法
    Q_INVOKABLE void updateLeftDrawerState(bool isOpened);
    Q_INVOKABLE void updateRightDrawerState(bool isOpened);

    // JavaScript可调用的拖拽方法
    Q_INVOKABLE void startWindowDrag(int x, int y);
    Q_INVOKABLE void moveWindow(int x, int y);
    Q_INVOKABLE void endWindowDrag();

    // 主题设置相关的JavaScript可调用方法
    Q_INVOKABLE bool hasThemeSetting();
    Q_INVOKABLE bool getSavedTheme();
    Q_INVOKABLE void saveThemeSetting(bool isDark);
    Q_INVOKABLE void setThemeFromVue(bool isDark);
    Q_INVOKABLE void onThemeChangedFromFrontend();
    Q_INVOKABLE void setThemeDirectly(bool isDark);

    // 外部链接打开方法
    Q_INVOKABLE void openExternalUrl(const QString &url);

private:
    MainWindow *m_mainWindow;
};

#endif // WINDOWAPI_H