#ifndef LOCALGGUFEMBEDDING_H
#define LOCALGGUFEMBEDDING_H

#include <QObject>
#include <QString>
#include <QDebug>
#include <QFileInfo>
#include <QDir>
#include <QThread>
#include <QMutex>
#include <QReadWriteLock>
#include <QSemaphore>
#include <QFuture>
#include <QtConcurrent/QtConcurrent>
#include <QElapsedTimer>
#include <QEventLoop>
#include <QTimer>
#include <QFutureWatcher>
#include <QProcess>
#include <QProcessEnvironment>
#include <QRegularExpression>
#include <vector>
#include <memory>
#include <atomic>

// CUDA相关包含（仅在CUDA可用时）
#ifdef ENABLE_LOCAL_GGUF
#ifdef __CUDACC__
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#endif
#endif

// Forward declarations for llama.cpp
struct llama_model;
struct llama_context;
struct llama_model_params;
struct llama_context_params;

/**
 * @brief 本地GGUF格式嵌入式模型管理类
 * 
 * 使用llama.cpp库加载和推理GGUF格式的嵌入式模型
 * 支持GPU加速和智能层分配
 */
class LocalGGUFEmbedding : public QObject
{
    Q_OBJECT

public:
    struct ModelConfig {
        QString modelPath;           // 模型文件路径
        int contextSize = 2048;      // 上下文大小
        int gpuLayers = 20;          // GPU层数，-1为全部，0为仅CPU
        int threads = 4;             // CPU线程数
        bool useGpu = true;          // 是否尝试使用GPU
        int selectedGpuDevice = -1;  // 选择的GPU设备ID，-1为默认/CPU
        float temperatureNotUsed = 0.0f; // 嵌入式模型不使用温度参数
        
        // 添加切割参数以统一云端和本地的切割行为
        int chunkSize = 800;         // 文档切割大小（字符数）
        int chunkOverlap = 200;      // 文档切割重叠（字符数）
    };

    struct GpuDeviceInfo {
        int deviceId = -1;           // 设备ID，-1表示CPU
        bool available = false;      // GPU是否可用
        QString deviceName;          // GPU设备名称
        int maxLayers = 0;           // 支持的最大GPU层数
        QString backend;             // 后端类型 (CUDA/OpenCL/Metal等)
        int recommendedLayers = 20;  // 推荐的GPU层数
        size_t memorySize = 0;       // 显存大小(MB)
        size_t availableMemory = 0;  // 可用显存(MB)
    };

    struct GpuInfo {
        bool available = false;      // GPU是否可用
        QString deviceName;          // GPU设备名称
        int maxLayers = 0;           // 支持的最大GPU层数
        QString backend;             // 后端类型 (CUDA/OpenCL/Metal等)
    };

    explicit LocalGGUFEmbedding(QObject *parent = nullptr);
    ~LocalGGUFEmbedding();

    // 模型管理
    bool loadModel(const ModelConfig &config);
    void unloadModel();
    bool isModelLoaded() const;
    QString getModelInfo() const;
    
    // GPU检测和配置
    static GpuInfo detectGpuCapabilities();
    static std::vector<GpuDeviceInfo> detectAllGpuDevices();
    static GpuDeviceInfo getGpuDeviceInfo(int deviceId);
    static int getRecommendedGpuLayers(const QString &modelPath);
    static size_t getGpuMemoryInfo(int deviceId);
    static bool isGpuDeviceAvailable(int deviceId);
    
    // 嵌入式向量生成 - 简化版本
    std::vector<float> generateEmbedding(const QString &text);
    
    // 移除复杂的异步和批处理功能以确保稳定性
    // QFuture<std::vector<float>> generateEmbeddingAsync(const QString &text);
    // std::vector<std::vector<float>> generateBatchEmbedding(const QStringList &texts);
    
    // 配置和状态 - 线程安全访问
    ModelConfig getCurrentConfig() const;
    bool isGpuEnabled() const;
    QString getLastError() const;
    
    // 推理状态监控 - 简化版本
    bool isInferenceInProgress() const;
    void setInferenceTimeout(int timeoutMs);
    
    // 移除复杂的并发控制
    // int getActiveInferenceCount() const;

signals:
    void modelLoaded(const QString &modelPath);
    void modelUnloaded();
    void errorOccurred(const QString &error);

private slots:
    void handleLoadingError(const QString &error);

private:
    // llama.cpp相关
    llama_model *m_model = nullptr;
    llama_context *m_context = nullptr;
    
    // 配置和状态
    ModelConfig m_config;
    GpuInfo m_gpuInfo;
    std::vector<GpuDeviceInfo> m_availableGpuDevices;
    QString m_lastError;
    bool m_modelLoaded = false;
    
    // 推理控制 - 简化版本
    int m_inferenceTimeout = 30000;        // 推理超时30秒
    std::atomic<bool> m_inferenceInProgress{false};
    
    // 线程安全 - 简化锁设计
    static QMutex s_globalInferenceMutex;   // 全局推理锁，确保严格串行
    mutable QReadWriteLock m_rwLock;        // 状态访问锁
    
    // 内部方法
    bool initializeLlamaBackend();
    void cleanupLlamaBackend();
    llama_model_params getModelParams() const;
    llama_context_params getContextParams() const;
    
    // GPU设备管理
    void initializeGpuDevices();
    static std::vector<GpuDeviceInfo> enumerateGpuDevices();
    static size_t queryGpuMemory(int deviceId);
    static bool validateGpuDevice(int deviceId);
    
    // GPU检测fallback方法
    static int detectGpuDeviceCountFallback();
    static size_t queryGpuMemoryFallback(int deviceId);
    static QString getGpuDeviceNameFallback(int deviceId);
    
    // 文本处理
    std::vector<int> tokenizeText(const QString &text);
    std::vector<float> extractEmbeddingFromTokens(const std::vector<int> &tokens);
    
    // 新增：分块处理长文本
    std::vector<float> extractEmbeddingWithChunking(const std::vector<int> &tokens, size_t maxTokens);
    void normalizeEmbedding(std::vector<float> &embedding);
    
    // 工具方法
    void logInfo(const QString &message) const;
    void logError(const QString &message) const;
    void logDebug(const QString &message) const;
    void logWarning(const QString &message) const;
    static QString formatFileSize(qint64 bytes);
    static bool isValidGGUFFile(const QString &filePath);
};

#endif // LOCALGGUFEMBEDDING_H