# Download FlatBuffers from GitHub
# This script downloads FlatBuffers headers for ObjectBox compatibility

param(
    [string]$Version = "v23.5.26"
)

Write-Host "======================================" -ForegroundColor Cyan
Write-Host "Downloading FlatBuffers" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "Version: $Version" -ForegroundColor Blue
Write-Host ""

# Create target directory
$targetDir = "third-party\flatbuffers"
if (Test-Path $targetDir) {
    Write-Host "Removing existing FlatBuffers directory..." -ForegroundColor Yellow
    Remove-Item -Path $targetDir -Recurse -Force
}

Write-Host "Creating directory: $targetDir" -ForegroundColor Yellow
New-Item -ItemType Directory -Path $targetDir -Force | Out-Null

# Download URL
$downloadUrl = "https://github.com/google/flatbuffers/archive/refs/tags/$Version.zip"
$zipFile = "$targetDir\flatbuffers-$Version.zip"

Write-Host "Downloading from: $downloadUrl" -ForegroundColor Blue
Write-Host "Saving to: $zipFile" -ForegroundColor Blue

try {
    # Download the zip file
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipFile -UseBasicParsing
    Write-Host "Download completed!" -ForegroundColor Green
    
    # Extract the zip file
    Write-Host "Extracting archive..." -ForegroundColor Yellow
    Expand-Archive -Path $zipFile -DestinationPath $targetDir -Force
    
    # Find the extracted directory
    $extractedDir = Get-ChildItem -Path $targetDir -Directory | Where-Object { $_.Name -like "flatbuffers-*" } | Select-Object -First 1
    
    if ($extractedDir) {
        Write-Host "Found extracted directory: $($extractedDir.Name)" -ForegroundColor Blue
        
        # Copy include directory to the expected location
        $sourceInclude = Join-Path $extractedDir.FullName "include"
        $targetInclude = Join-Path $targetDir "include"
        
        if (Test-Path $sourceInclude) {
            Write-Host "Copying include directory..." -ForegroundColor Yellow
            Copy-Item -Path $sourceInclude -Destination $targetInclude -Recurse -Force
            Write-Host "Include directory copied successfully!" -ForegroundColor Green
        } else {
            Write-Host "Warning: Include directory not found in extracted archive" -ForegroundColor Yellow
        }
        
        # Clean up - remove the extracted directory and zip file
        Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
        Remove-Item -Path $extractedDir.FullName -Recurse -Force
        Remove-Item -Path $zipFile -Force
        
        # Verify the installation
        $flatbuffersHeader = Join-Path $targetInclude "flatbuffers\flatbuffers.h"
        if (Test-Path $flatbuffersHeader) {
            Write-Host ""
            Write-Host "======================================" -ForegroundColor Green
            Write-Host "FlatBuffers Download Successful!" -ForegroundColor Green
            Write-Host "======================================" -ForegroundColor Green
            Write-Host "Header file: $flatbuffersHeader" -ForegroundColor Blue
            Write-Host "Directory structure:" -ForegroundColor Yellow
            Get-ChildItem -Path $targetInclude -Recurse -Directory | ForEach-Object {
                Write-Host "  $($_.FullName.Replace($targetInclude, ''))" -ForegroundColor White
            }
            Write-Host ""
            Write-Host "FlatBuffers is now ready for use with ObjectBox!" -ForegroundColor Green
        } else {
            Write-Host "Error: FlatBuffers header file not found after extraction" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "Error: Could not find extracted FlatBuffers directory" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "Error downloading FlatBuffers: $($_.Exception.Message)" -ForegroundColor Red
    
    # Check if it's a network connectivity issue
    Write-Host ""
    Write-Host "Troubleshooting suggestions:" -ForegroundColor Yellow
    Write-Host "1. Check your internet connection" -ForegroundColor White
    Write-Host "2. Try using a VPN if GitHub is blocked" -ForegroundColor White
    Write-Host "3. Download manually from: $downloadUrl" -ForegroundColor White
    Write-Host "4. Extract to: $targetDir" -ForegroundColor White
    
    exit 1
}
