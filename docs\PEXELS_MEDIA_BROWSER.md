# Pexels 媒体浏览器组件

## 概述

`PexelsMediaBrowser` 是一个功能完整的 Vue 3 组件，用于通过 Pexels API 搜索和预览高质量的免费图片和视频。

## 功能特性

### 🔍 搜索功能
- 支持图片和视频搜索
- 实时搜索建议
- 关键词搜索

### 📱 响应式设计
- 自适应网格布局
- 移动端友好
- 每页12条内容，每行6个（可响应式调整）

### 🖼️ 图片功能
- 高质量图片预览
- 使用 `hevue-img-preview` 进行图片预览
- 支持多种尺寸（tiny, small, medium, large, original）
- 显示摄影师信息

### 🎬 视频功能
- 视频缩略图预览
- 使用 `artplayer` 进行视频播放
- 支持多种视频质量（HD, SD等）
- 显示视频时长和作者信息

### 📄 分页功能
- 完整的分页控制
- 页面导航
- 结果统计显示

## 安装和配置

### 1. 依赖项
确保项目中已安装以下依赖：
```json
{
  "artplayer": "^5.2.3",
  "hevue-img-preview": "^7.0.1"
}
```

### 2. Pexels API 配置
在应用设置中配置 Pexels API：
```typescript
// 通过 uiStore.perferences.llm.pexels 访问
{
  apiKey: "YOUR_PEXELS_API_KEY",
  baseUrl: "https://api.pexels.com",
  maxResults: 12
}
```

### 3. 获取 Pexels API Key
1. 访问 [Pexels API](https://www.pexels.com/api/)
2. 注册账户并创建应用
3. 获取 API Key
4. 在应用设置中配置

## 使用方法

### 基本使用
```vue
<template>
  <div class="media-browser-container">
    <PexelsMediaBrowser />
  </div>
</template>

<script setup lang="ts">
import PexelsMediaBrowser from 'src/components/PexelsMediaBrowser.vue';
</script>
```

### 在页面中使用
```vue
<template>
  <q-page class="pexels-page">
    <PexelsMediaBrowser />
  </q-page>
</template>
```

## API 接口

### 图片搜索
- **端点**: `GET /v1/search`
- **参数**:
  - `query`: 搜索关键词
  - `per_page`: 每页结果数（默认12）
  - `page`: 页码

### 视频搜索
- **端点**: `GET /videos/search`
- **参数**:
  - `query`: 搜索关键词
  - `per_page`: 每页结果数（默认12）
  - `page`: 页码

## 组件结构

```
PexelsMediaBrowser/
├── 搜索栏
├── 媒体类型切换（图片/视频）
├── 内容区域
│   ├── 加载状态
│   ├── 错误状态
│   ├── 空状态
│   ├── 媒体网格
│   └── 分页控制
└── 视频预览对话框
```

## 样式特性

### 网格布局
- 桌面端：6列
- 平板端：4列
- 手机端：2列
- 小屏手机：1列

### 交互效果
- 悬停放大效果
- 渐变覆盖层
- 平滑过渡动画

### 深色主题支持
- 自动适配深色/浅色主题
- 边框和背景色自动调整

## 预览功能

### 图片预览
使用 `hevue-img-preview` 库：
- 全屏预览
- 缩放功能
- 左右切换
- 键盘导航

### 视频预览
使用 `artplayer` 播放器：
- 全屏播放
- 画中画模式
- 播放速度控制
- 音量控制
- 全屏模式

## 错误处理

### API 错误
- 网络连接错误
- API 密钥无效
- 请求限制超出
- 服务器错误

### 用户友好提示
- 加载状态指示
- 错误信息显示
- 重试功能
- 空结果提示

## 性能优化

### 图片懒加载
- 使用 `loading="lazy"` 属性
- 减少初始加载时间

### 组件懒加载
- 视频播放器按需加载
- 减少包体积

### 内存管理
- 视频播放器自动销毁
- 避免内存泄漏

## 测试

### 测试页面
访问 `/pexels-test` 路由查看组件演示。

### 测试用例
1. 搜索功能测试
2. 分页功能测试
3. 图片预览测试
4. 视频播放测试
5. 响应式布局测试

## 故障排除

### 常见问题

1. **API 密钥错误**
   - 检查 Pexels API 密钥是否正确
   - 确认 API 密钥权限

2. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

3. **视频播放问题**
   - 检查浏览器兼容性
   - 确认视频格式支持

4. **图片加载缓慢**
   - 检查网络速度
   - 考虑使用 CDN

## 扩展功能

### 可能的扩展
- 收藏功能
- 下载功能
- 分类筛选
- 颜色筛选
- 尺寸筛选

### 自定义配置
- 网格列数配置
- 预览模式配置
- 主题色配置

## 许可证

本组件遵循项目许可证。Pexels 内容遵循 [Pexels License](https://www.pexels.com/license/)。
