# Qt 日志过滤规则配置
# 禁用Qt系统相关的调试信息，减少控制台输出

# 禁用字体相关的调试信息（主要问题来源）
qt.text.font.db=false
qt.text.font=false

# 禁用其他Qt系统调试信息
qt.core.qobject.connect=false
qt.widgets.gestures=false
qt.qpa.*=false
qt.webenginecontext=false
qt.webengine.view.debug=false

# 禁用WebEngine相关的警告和调试信息
qt.webengine.*=false
qt.webenginecore.*=false

# 禁用Chrome/Chromium相关的警告
chromium.*=false

# 禁用VAAPI相关的警告
qt.multimedia.vaapi=false

# 禁用DevTools相关的错误和警告
qt.webengine.devtools=false

# 禁用Autofill相关的错误
qt.webengine.autofill=false

# 禁用所有Qt调试信息，但保留警告和严重错误
qt.*.debug=false
qt.*.info=false
qt.*.warning=false
qt.*.critical=true

# 允许应用自定义日志
inkcop.*=true

# 启用知识库相关的日志，但避免重复
# 只保留关键的信息日志，禁用过多的调试信息
inkcop.knowledge.debug=false
inkcop.knowledge.info=true
inkcop.knowledge.warning=true
inkcop.knowledge.critical=true

# 保留重要的系统级别日志，但减少噪音
*.warning=true
*.critical=true
*.fatal=true

# 禁用其他所有调试信息
*.debug=false
*.info=false