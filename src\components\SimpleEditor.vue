<template>
  <div class="tiptap column no-wrap">
    <!-- 工具栏 -->
    <div class="editor-toolbar row items-center border-bottom">
      <slot name="toolbar-left" />
      <slot name="toolbar-actions"></slot>
    </div>

    <!-- 编辑器内容区域 -->
    <div v-if="editor" class="q-space scroll-y">
      <bubble-menu
        :editor="editor"
        :tippy-options="{ duration: 100, maxWidth: 'none' }"
        :class="$q.dark.isActive ? 'bg-dark' : 'bg-white'"
        class="border radius-sm shadow-24 overflow-hidden"
      >
        <!-- 工具栏 -->
        <div class="editor-toolbar row items-center q-pa-xs border-bottom z-max">
          <!-- 标题 -->
          <q-btn-dropdown flat dense padding="xs sm" label="标题" :disable="!editor">
            <q-list bordered class="tiptap radius-sm">
              <q-item
                v-for="level in [1, 2, 3, 4, 5, 6] as const"
                :key="level"
                clickable
                v-close-popup
                class="q-py-xs"
                :class="{ 'bg-primary text-white': editor?.isActive('heading', { level }) }"
                @click="editor?.chain().focus().toggleHeading({ level }).run()"
              >
                <q-item-section class="no-margin" :class="`text-h${level}`">{{
                  $t('src.components.SimpleEditor.title', { level })
                }}</q-item-section>
              </q-item>
              <q-item
                clickable
                v-close-popup
                @click="editor?.chain().focus().setParagraph().run()"
                :class="{ 'bg-primary text-white': editor?.isActive('paragraph') }"
              >
                <q-item-section>{{ $t('src.components.SimpleEditor.paragraph') }}</q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
          <!-- 基础格式化 -->
          <q-btn
            flat
            dense
            icon="format_bold"
            :color="editor?.isActive('bold') ? 'primary' : 'grey-7'"
            @click="editor?.chain().focus().toggleBold().run()"
            :disable="!editor"
          />
          <q-btn
            flat
            dense
            icon="format_italic"
            :color="editor?.isActive('italic') ? 'primary' : 'grey-7'"
            @click="editor?.chain().focus().toggleItalic().run()"
            :disable="!editor"
          />
          <q-btn
            flat
            dense
            icon="format_underlined"
            :color="editor?.isActive('underline') ? 'primary' : 'grey-7'"
            @click="editor?.chain().focus().toggleUnderline().run()"
            :disable="!editor"
          />
          <q-btn
            flat
            dense
            icon="strikethrough_s"
            :color="editor?.isActive('strike') ? 'primary' : 'grey-7'"
            @click="editor?.chain().focus().toggleStrike().run()"
            :disable="!editor"
          />
          <q-btn
            flat
            dense
            icon="code"
            :color="editor?.isActive('code') ? 'primary' : 'grey-7'"
            @click="editor?.chain().focus().toggleCode().run()"
            :disable="!editor"
          />
          <q-btn
            flat
            dense
            icon="mdi-format-clear"
            @click="editor?.chain().focus().clearNodes().unsetAllMarks().run()"
            :disable="!editor"
          />

          <q-separator vertical class="q-mx-sm" />

          <!-- 列表 -->
          <q-btn
            flat
            dense
            icon="format_list_bulleted"
            :color="editor?.isActive('bulletList') ? 'primary' : 'grey-7'"
            @click="editor?.chain().focus().toggleBulletList().run()"
            :disable="!editor"
          />
          <q-btn
            flat
            dense
            icon="format_list_numbered"
            :color="editor?.isActive('orderedList') ? 'primary' : 'grey-7'"
            @click="editor?.chain().focus().toggleOrderedList().run()"
            :disable="!editor"
          />
          <q-btn
            flat
            dense
            icon="format_quote"
            :color="editor?.isActive('blockquote') ? 'primary' : 'grey-7'"
            @click="editor?.chain().focus().toggleBlockquote().run()"
            :disable="!editor"
          />
        </div>
      </bubble-menu>
      <editor-content :editor="editor" class="full-height q-px-xs q-py-md" />
      <div class="absolute-bottom q-py-lg flex flex-center">
        <q-btn
          color="primary"
          icon="check"
          class="shadow-24"
          :label="$t('src.components.KnowledgeBaseManager.edit_original_document')"
          @click="editOriginalDocument()"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, onBeforeUnmount, nextTick } from 'vue';
import { EditorContent, BubbleMenu, useEditor } from '@tiptap/vue-3';
import { getStandardEditorExtensions } from '../utils/editorFactory';
import { convertHtmlTableToMarkdown } from '../utils/tiptap';
import { useDocumentActions } from '../composeables/useDocumentActions';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { convertContentToTiptap } from 'src/utils/tiptap';

import { useUiStore } from 'src/stores/ui';
import { useSqlite } from 'src/composeables/useSqlite';

const { t: $t } = useI18n();
const router = useRouter();
const { onOpenDocument } = useDocumentActions();
const uiStore = useUiStore();

const $q = useQuasar();
interface OriginalDocumentType {
  success: boolean;
  id?: number;
  title?: string;
  content?: string;
  metadata?: string;
  created_at?: string;
  updated_at?: string;
  message?: string;
  error?: string;
}

// Props 和 Emits
const props = defineProps<{
  modelValue?: string;
  OriginalDocument?: OriginalDocumentType;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'ready'): void;
  (e: 'close'): void;
}>();

// 编辑器初始化
const editor = useEditor({
  content: convertContentToTiptap(props.modelValue || ''),
  autofocus: false,
  editable: !props.OriginalDocument,
  extensions: getStandardEditorExtensions({
    placeholder: $t('src.components.SimpleEditor.placeholder'),
  }),
  onUpdate: ({ editor: editorInstance }) => {
    emit('update:modelValue', editorInstance.getHTML());
  },
  onCreate: () => {
    emit('ready');
  },
});

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (editor.value) {
      // 获取当前编辑器的HTML内容
      const currentHTML = editor.value.getHTML();
      const newHTML = newValue || '';

      // 只有当内容真正不同时才更新
      if (newHTML !== currentHTML && newHTML.trim() !== '') {
        editor.value.commands.setContent(newHTML);
      }
    }
  },
);

// 获取编辑器标题的方法
const getTitle = (): string => {
  if (!editor.value) return $t('src.components.SimpleEditor.unnamed_document');

  try {
    // 查找第一个标题节点
    const doc = editor.value.state.doc;
    let title = $t('src.components.SimpleEditor.unnamed_document');

    doc.descendants((node) => {
      if (node.type.name === 'heading' && node.attrs.level === 1) {
        title = node.textContent.trim() || $t('src.components.SimpleEditor.unnamed_document');
        return false; // 停止遍历
      }
      return true;
    });

    return title;
  } catch (error) {
    console.error($t('src.components.SimpleEditor.get_title_failed'), error);
    return $t('src.components.SimpleEditor.unnamed_document');
  }
};

// 获取完整的纯文本内容（包含标题）
const getPlainText = (): string => {
  if (!editor.value) return '';
  return editor.value.getText();
};

// 获取完整的HTML内容
const getHTML = (): string => {
  if (!editor.value) return '';
  return editor.value.getHTML();
};
// 获取完整的HTML内容
const getJSON = (): object | null => {
  if (!editor.value) return null;
  return editor.value.getJSON();
};

// 获取Markdown格式内容
const getMarkdown = (): string => {
  if (!editor.value) return '';
  try {
    // 使用Markdown扩展的getMarkdown方法
    let markdown = editor.value.storage.markdown.getMarkdown();

    // 如果获取失败，使用纯文本
    if (!markdown) {
      markdown = editor.value.getText();
    }

    // 转换HTML表格为Markdown表格
    markdown = convertHtmlTableToMarkdown(markdown);

    return markdown;
  } catch (error) {
    console.warn($t('src.components.SimpleEditor.get_markdown_failed'), error);
    return editor.value.getText();
  }
};

const close = () => {
  emit('close');
  editor.value?.destroy();
};
const editOriginalDocument = async () => {
  // 获取原始文档在文件树中的层级位置，并展开对应文件树结构
  const originalDocument = props.OriginalDocument;

  if (!originalDocument?.id) {
    console.warn('原始文档ID不存在，无法打开');
    return;
  }

  try {
    close();
    await nextTick();
    // 跳转到文档页面
    uiStore.app = 'inkcop';
    await router.push('/doc');
    // 通过API获取原始文档的完整信息
    const sqliteInstance = useSqlite();
    const fullDocument = await sqliteInstance.getDocument(originalDocument.id);

    // 获取文档所在的文件夹ID
    const folderId = fullDocument.folder_id || -1;

    // 如果文档在文件夹中，递归获取文件夹路径并展开
    if (folderId !== -1) {
      const folderPath = await getFolderParentPath(folderId);

      // 触发文件夹展开事件
      if (folderPath.length > 0) {
        uiStore.triggerFolderExpansion(folderPath);
      }
    }

    // 打开原始文档
    onOpenDocument(fullDocument, folderId);
  } catch (error) {
    console.error('打开原始文档失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.SimpleEditor.open_original_document_failed'),
      position: 'top',
    });
  }
};

/**
 * 递归获取文件夹的完整父级路径
 * @param folderId 文件夹ID
 * @returns 从根级到目标文件夹的完整路径数组
 */
const getFolderParentPath = async (folderId: number): Promise<number[]> => {
  const path: number[] = [];

  try {
    const sqliteInstance = useSqlite();
    const folder = await sqliteInstance.getFolder(folderId);

    // 如果有父级文件夹且不是根级别（parent_id !== -1）
    if (folder.parent_id && folder.parent_id !== -1) {
      // 递归获取父级路径
      const parentPath = await getFolderParentPath(folder.parent_id);
      path.push(...parentPath);
      path.push(folder.parent_id);
    }

    // 添加当前文件夹到路径
    path.push(folderId);

    return path;
  } catch (error) {
    console.error('获取文件夹路径失败:', error);
    // 如果获取失败，至少返回当前文件夹ID
    return [folderId];
  }
};
// 暴露方法给父组件
defineExpose({
  getTitle,
  getPlainText,
  getHTML,
  getJSON,
  getMarkdown,
  editor: () => editor.value,
  close,
});

// 清理
onBeforeUnmount(() => {
  editor.value?.destroy();
});
</script>
