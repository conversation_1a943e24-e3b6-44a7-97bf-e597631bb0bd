{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/qt-src", "${workspaceFolder}/qt-src/objectbox", "${workspaceFolder}/third-party/qwindowkit/src/core", "${workspaceFolder}/third-party/qwindowkit/src/widgets", "${workspaceFolder}/third-party/objectbox-windows/include", "${workspaceFolder}/third-party/flatbuffers/include", "C:/Qt/6.7.3/msvc2022_64/include", "C:/Qt/6.7.3/msvc2022_64/include/QtCore", "C:/Qt/6.7.3/msvc2022_64/include/QtWidgets", "C:/Qt/6.7.3/msvc2022_64/include/QtWebEngineWidgets", "C:/Qt/6.7.3/msvc2022_64/include/QtWebChannel", "C:/Qt/6.7.3/msvc2022_64/include/QtSql", "C:/Qt/6.7.3/msvc2022_64/include/QtNetwork"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "WIN32", "_WIN32", "Q_OS_WIN", "QT_CORE_LIB", "QT_WIDGETS_LIB", "QT_WEBENGINEWIDGETS_LIB", "QT_WEBCHANNEL_LIB", "QT_SQL_LIB", "QT_NETWORK_LIB", "SQLITE_THREADSAFE=1"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.40.33807/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}