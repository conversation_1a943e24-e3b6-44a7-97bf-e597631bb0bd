# InkCop Memory Service 集成方案

## 🏗️ 系统架构

本方案将 **Mem0 + Qdrant** 集成到现有的 **Qt + Quasar** 技术栈中，采用 **Python 微服务** 架构：

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Qt Frontend   │    │  Quasar Frontend │    │  Python Service │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ MemoryAPI   │ │◄──►│ │MemoryClient  │ │◄──►│ │ FastAPI +   │ │
│ │ (Qt C++)    │ │    │ │ (TypeScript) │ │    │ │ Mem0 +      │ │
│ │             │ │    │ │              │ │    │ │ Qdrant     │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
│                 │    │                  │    │                 │
│   WebEngine     │    │   Vue 3 +        │    │   HTTP API      │
│   SQLite        │    │   Quasar         │    │   Port: 38899    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📂 目录结构

```
inkcop/
├── llm/                           # AI 功能目录
│   ├── memory-service/           # Python 微服务
│   │   ├── main.py              # 服务入口
│   │   ├── memory_api.py        # Mem0 API 封装
│   │   ├── config.py            # 配置管理
│   │   ├── requirements.txt     # Python 依赖
│   │   └── build.spec           # PyInstaller 配置
│   ├── integration/             # 集成层
│   │   ├── memory_client.ts     # 前端 Memory 客户端
│   │   └── memory_tools.ts      # AI 工具扩展
│   ├── data/                    # 数据存储
│   │   ├── memory.db           # Mem0 数据
│   │   └── qdrant_db/          # Qdrant 向量存储
│   ├── build-memory-service.sh  # 构建脚本
│   ├── run-memory-service.sh    # 开发启动脚本
│   └── README.md               # 本文档
├── qt-src/                      # Qt 源码
│   ├── memoryapi.h             # Qt Memory API 头文件
│   ├── memoryapi.cpp           # Qt Memory API 实现
│   └── ...                     # 现有Qt文件
├── src/                         # Quasar 源码
│   ├── composeables/
│   │   └── useMemory.ts        # Memory Composable
│   └── ...                     # 现有Quasar文件
└── build/                       # 构建输出
    ├── inkcop-memory-service    # 打包后的可执行文件
    └── memory-service-deploy/   # 部署包
```

## 🚀 快速开始

### 1. 构建开发环境

```bash
# 进入 llm 目录
cd llm

# 构建开发环境
./build-memory-service.sh

# 启动开发服务
./run-memory-service.sh
```

### 2. 生产环境打包

```bash
# 生产环境构建和打包
./build-memory-service.sh --production

# 部署包位置
ls -la ../build/inkcop-memory-service-*.tar.gz
```

### 3. 测试服务

```bash
# 健康检查
curl http://127.0.0.1:38899/health

# API 文档
open http://127.0.0.1:38899/docs
```

## 🔧 技术实现

### Python 微服务层

#### 1. FastAPI + Mem0 集成

```python
# memory_api.py 核心功能
class MemoryAPI:
    def __init__(self, config):
        self.memory = Memory(config.get_mem0_config())

    async def add_memory(self, messages, user_id, metadata):
        return await self.memory.add(messages, user_id=user_id, metadata=metadata)

    async def search_memory(self, query, user_id, limit):
        return await self.memory.search(query, user_id=user_id, limit=limit)
```

#### 2. Qdrant 向量存储

```python
# 配置 Qdrant 作为向量存储
vector_store_config = {
    "provider": "qdrant",
    "config": {
        "collection_name": "inkcop_memory",
        "path": "./data/qdrant_db",
        "persist_directory": "./data/qdrant_db"
    }
}
```

### Qt 集成层

#### 1. MemoryAPI 类

```cpp
// memoryapi.h
class MemoryAPI : public QObject {
    Q_OBJECT
public:
    void addMemory(const QJsonArray &messages, const QString &userId);
    void searchMemory(const QString &query, const QString &userId);

signals:
    void memoryAdded(bool success, const QJsonObject &result);
    void memorySearchCompleted(bool success, const QJsonArray &results);
};
```

#### 2. 进程管理

```cpp
// 自动启动和管理 Python 服务进程
bool MemoryAPI::startService(const QString &executablePath) {
    m_serviceProcess = new QProcess(this);
    m_serviceProcess->start(executablePath);
    return waitForServiceStart();
}
```

### Quasar 前端层

#### 1. Memory Client

```typescript
// memory_client.ts
export class MemoryClient {
  async addMemory(messages: Message[], userId?: string): Promise<MemoryAddResult> {
    const response = await this.client.post('/memory/add', {
      messages,
      user_id: userId,
    });
    return response.data;
  }
}
```

#### 2. AI 工具集成

```typescript
// memory_tools.ts
export async function executeMemoryTool(
  toolName: string,
  parameters: Record<string, any>,
): Promise<any> {
  const memoryTools = useMemoryTools();

  switch (toolName) {
    case 'search_memory':
      return await memoryTools.searchRelevantMemories(parameters.query);
    case 'add_conversation_memory':
      return await memoryTools.addConversationMemory(
        parameters.user_message,
        parameters.assistant_response,
      );
  }
}
```

## 🛠️ 配置管理

### 环境变量

```bash
# 服务配置
MEMORY_SERVICE_HOST=127.0.0.1
MEMORY_SERVICE_PORT=38899

# LLM 配置（必须设置）
LLM_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
LLM_API_KEY=your_api_key_here
LLM_MODEL=qwen-plus

# 嵌入模型配置
EMBEDDING_MODEL=text-embedding-v1

# 数据目录
INKCOP_DATA_DIR=./data

# Qdrant 配置
QDRANT_COLLECTION=inkcop_memory
```

### Python 配置

```python
# config.py
class Config:
    def get_mem0_config(self):
        return {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": self.llm_model,
                    "base_url": self.llm_base_url,
                    "api_key": self.llm_api_key,
                }
            },
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "collection_name": self.qdrant_collection,
                    "path": str(self.data_dir / "qdrant_db"),
                }
            }
        }
```

## 📦 部署方案

### 开发环境

1. **直接运行 Python 服务**：

   ```bash
   cd llm/memory-service
   source memory-service-env/bin/activate
   python main.py
   ```

2. **Qt 应用自动管理**：
   - Qt 应用启动时自动检测和启动 Python 服务
   - 失败时提供友好的错误提示

### 生产环境

1. **PyInstaller 打包**：

   ```bash
   # 生成单个可执行文件
   pyinstaller build.spec --clean --noconfirm
   ```

2. **部署包结构**：

   ```
   memory-service-deploy/
   ├── inkcop-memory-service      # 可执行文件
   ├── start-memory-service.sh    # 启动脚本
   ├── config.env.template        # 配置模板
   ├── data/                      # 数据目录
   └── README.md                  # 部署说明
   ```

3. **一键安装**：
   - 将部署包集成到主应用安装包
   - 安装时自动配置环境变量和数据目录
   - 首次运行时引导用户配置 LLM API

## 🔍 监控和调试

### 服务监控

```typescript
// 前端健康检查
const memoryClient = useMemoryClient();

setInterval(async () => {
  const isHealthy = await memoryClient.checkHealth();
  if (!isHealthy) {
    console.warn('Memory Service 不可用');
    // 尝试重启或提示用户
  }
}, 30000);
```

### 日志管理

```python
# Python 服务日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('memory-service.log')
    ]
)
```

## 🚨 故障排除

### 常见问题

1. **Python 服务无法启动**：

   - 检查 Python 环境和依赖
   - 查看 `memory-service.log` 日志
   - 验证 LLM API 密钥配置

2. **记忆功能异常**：

   - 检查 Qdrant 数据目录权限
   - 验证向量存储是否正常初始化
   - 查看网络连接和 API 限制

3. **性能问题**：
   - 调整 Qdrant 索引参数
   - 限制记忆搜索结果数量
   - 优化嵌入模型调用频率

### 调试工具

```bash
# 测试 API 端点
curl -X POST http://127.0.0.1:38899/memory/add \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "测试消息"}]}'

# 查看服务状态
curl http://127.0.0.1:38899/health

# 获取所有记忆
curl "http://127.0.0.1:38899/memory/all?limit=10"
```

## 🎯 使用示例

### 基础记忆操作

```typescript
// 添加对话记忆
const memoryTools = useMemoryTools();
await memoryTools.addConversationMemory(
  '用户问题：如何使用 AI 写作？',
  'AI 回答：可以通过以下步骤...',
  '文档编辑上下文',
);

// 搜索相关记忆
const memories = await memoryTools.searchRelevantMemories('AI 写作', 5);
console.log('找到相关记忆:', memories);
```

### AI 系统集成

```typescript
// 增强 AI 提示词
const basePrompt = '你是一个专业的写作助手...';
const enhancedPrompt = await memoryTools.enhanceSystemPromptWithMemory(basePrompt, '用户问题内容');

// 调用 LLM 时使用增强后的提示词
const response = await callLLM(enhancedPrompt, userMessage);
```

## 🔮 扩展计划

1. **多模态记忆**：支持图片、音频等多媒体内容记忆
2. **分布式部署**：支持记忆服务集群部署
3. **记忆压缩**：自动压缩和归档历史记忆
4. **个性化推荐**：基于记忆的内容推荐系统
5. **协作记忆**：团队共享记忆空间

## 📚 相关文档

- [Mem0 官方文档](https://docs.mem0.ai/)
- [Qdrant 文档](https://qdrant.tech/)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [PyInstaller 文档](https://pyinstaller.readthedocs.io/)

---

**注意**：本方案针对 Linux 环境设计，适用于 InkCop AI 写作助手的记忆管理需求。在实际部署前，请确保所有依赖正确安装并根据实际环境调整配置参数。
