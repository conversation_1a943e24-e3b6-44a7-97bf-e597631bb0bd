<template>
  <div>
    <q-card :flat="bolderless" :bordered="!bolderless" class="full-width">
      <q-card-section class="q-pb-none">
        <div class="text-h6 q-mb-sm">
          {{ $t('src.components.CreatekKnowlegeDocCard.save_to_knowledge_base') }}
        </div>
        <div class="text-caption text-grey-6">
          {{ $t('src.components.CreatekKnowlegeDocCard.select_knowledge_base_and_set_title') }}
        </div>
      </q-card-section>

      <q-card-section>
        <!-- 文档标题输入 -->
        <q-input
          v-model="documentTitle"
          :label="$t('src.components.CreatekKnowlegeDocCard.document_title')"
          outlined
          dense
          :rules="[
            (val) => !!val || $t('src.components.CreatekKnowlegeDocCard.please_input_title'),
          ]"
          ref="titleInputRef"
        />

        <!-- 知识库选择 -->
        <q-select
          v-model="selectedKnowledgeBase"
          :options="knowledgeBaseOptions"
          :label="$t('src.components.CreatekKnowlegeDocCard.select_knowledge_base')"
          outlined
          dense
          emit-value
          map-options
          class="q-mb-xs"
          :loading="loadingKnowledgeBases"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">{{
                $t('src.components.CreatekKnowlegeDocCard.no_knowledge_base')
              }}</q-item-section>
            </q-item>
          </template>
          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section avatar>
                <q-icon name="storage" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
                <q-item-label v-if="scope.opt.caption" caption class="text-deep-orange">{{
                  $t('src.components.CreatekKnowlegeDocCard.already_added')
                }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-select>

        <!-- 创建新知识库选项 -->
        <div class="row q-gutter-sm q-mb-md">
          <q-btn
            flat
            dense
            color="primary"
            icon="add"
            :label="$t('src.components.CreatekKnowlegeDocCard.create_new_knowledge_base')"
            @click="showCreateKBDialog = true"
          />
        </div>

        <!-- 切割策略选择 -->
        <div class="q-mb-md">
          <ChunkingStrategySelector
            v-model="chunkingStrategy"
            @validation-change="onChunkingValidationChange"
          />
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          :label="$t('src.components.CreatekKnowlegeDocCard.cancel')"
          padding="xs md"
          @click="$emit('cancel')"
        />
        <q-btn
          color="primary"
          :label="$t('src.components.CreatekKnowlegeDocCard.save')"
          padding="xs md"
          @click="handleSave"
          :loading="saving"
          :disable="!canSave"
        />
      </q-card-actions>

      <!-- 创建知识库对话框 -->
      <q-dialog v-model="showCreateKBDialog">
        <q-card style="min-width: 400px">
          <q-card-section>
            <div class="text-h6">
              {{ $t('src.components.CreatekKnowlegeDocCard.create_new_knowledge_base') }}
            </div>
          </q-card-section>

          <q-card-section class="q-pt-none">
            <q-input
              v-model="newKBName"
              :label="$t('src.components.CreatekKnowlegeDocCard.knowledge_base_name')"
              outlined
              dense
              class="q-mb-md"
              :rules="[
                (val) =>
                  !!val ||
                  $t('src.components.CreatekKnowlegeDocCard.please_input_knowledge_base_name'),
              ]"
              ref="kbNameInputRef"
            />
            <q-input
              v-model="newKBDescription"
              :label="$t('src.components.CreatekKnowlegeDocCard.knowledge_base_description')"
              outlined
              dense
              type="textarea"
              rows="3"
            />
          </q-card-section>

          <q-card-actions align="right">
            <q-btn
              flat
              :label="$t('src.components.CreatekKnowlegeDocCard.cancel')"
              @click="cancelCreateKB"
            />
            <q-btn
              color="primary"
              :label="$t('src.components.CreatekKnowlegeDocCard.create')"
              @click="handleCreateKB"
              :loading="creatingKB"
              :disable="!newKBName.trim()"
            />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { useQuasar } from 'quasar';
import { storeToRefs } from 'pinia';
import { useKnowledgeStore } from '../stores/knowledge';
import { useKnowledge } from '../composeables/useKnowledge';
import ChunkingStrategySelector from './ChunkingStrategySelector.vue';
import type { ChunkingMethod, ChunkingConfig } from '../types/qwen';
import { useI18n } from 'vue-i18n';
import { useDocumentToKnowledge } from '../composeables/useDocumentToKnowledge';

const { t: $t } = useI18n({ useScope: 'global' });

// Props
const props = defineProps<{
  content?: string;
  defaultTitle?: string;
  bolderless?: boolean;
  originalDocumentId?: number; // 原始文档ID，用于更新知识库字段
  documentId?: number; // 文档ID，用于检查知识库关联
  excludeKnowledgeBaseId?: number; // 要排除的知识库ID（文档已添加到的知识库）
  documentKnowledgeAssociations?: Array<{
    id: number;
    knowledge_document_id: number;
    knowledge_base_id: number;
    created_at: string;
  }>;
}>();

// Emits
const emit = defineEmits<{
  cancel: [];
  saved: [knowledgeBaseId: number, documentId: number, title: string];
}>();

// Composables
const $q = useQuasar();
const knowledgeStore = useKnowledgeStore();
const knowledge = useKnowledge();
const documentToKnowledge = useDocumentToKnowledge();

// Store数据
const { knowledgeBases } = storeToRefs(knowledgeStore);

// 响应式数据
const documentTitle = ref(props.defaultTitle || '');
const selectedKnowledgeBase = ref<number | null>(null);
const saving = ref(false);
const loadingKnowledgeBases = ref(false);

// 创建知识库相关
const showCreateKBDialog = ref(false);
const newKBName = ref('');
const newKBDescription = ref('');
const creatingKB = ref(false);

// 切割策略相关
const chunkingStrategy = ref<{ method: ChunkingMethod; config: ChunkingConfig }>({
  method: 'markdown',
  config: {
    chunkSize: 800,
    chunkOverlap: 200,
  },
});
const chunkingConfigValid = ref(true);

// 模板引用
const titleInputRef = ref();
const kbNameInputRef = ref();

// 计算属性
const knowledgeBaseOptions = computed(() => {
  return knowledgeBases.value.map((kb) => {
    // 检查文档是否已经在该知识库中
    const isDocumentInKB =
      props.documentKnowledgeAssociations?.some(
        (association) => association.knowledge_base_id === kb.id,
      ) || false;

    return {
      label: kb.name,
      value: kb.id,
      disable: isDocumentInKB, // 如果文档已在该知识库中，则禁用
      caption: isDocumentInKB ? 'already_added' : undefined,
    };
  });
});

const canSave = computed(() => {
  return (
    documentTitle.value.trim() &&
    selectedKnowledgeBase.value !== null &&
    !saving.value &&
    chunkingConfigValid.value
  );
});

// 方法
const loadKnowledgeBases = async () => {
  loadingKnowledgeBases.value = true;
  try {
    await knowledgeStore.loadKnowledgeBases();
  } catch (error) {
    console.error('加载知识库失败:', error);
    $q.notify({
      type: 'negative',
      message: '加载知识库失败',
    });
  } finally {
    loadingKnowledgeBases.value = false;
  }
};

const handleSave = async () => {
  if (!canSave.value || selectedKnowledgeBase.value === null) return;

  console.log('💾 [CreateKnowledgeDocCard] 开始保存文档到知识库:', {
    knowledgeBaseId: selectedKnowledgeBase.value,
    title: documentTitle.value.trim(),
    contentLength: props.content?.length || 0,
    contentPreview: props.content?.substring(0, 200) || 'empty',
    chunkingStrategy: chunkingStrategy.value,
  });

  saving.value = true;
  try {
    // 如果有原始文档ID，使用与快速添加相同的逻辑
    if (props.originalDocumentId) {
      console.log('📄 [CreateKnowledgeDocCard] 使用文档处理流程 (与快速添加相同)');

      const result = await documentToKnowledge.processDocument(
        props.originalDocumentId,
        {
          knowledgeBaseId: selectedKnowledgeBase.value,
          chunkingMethod: chunkingStrategy.value.method,
          chunkingConfig: chunkingStrategy.value.config,
        },
        {
          onProgress: (current, total, currentTitle) => {
            console.log(
              `📊 [CreateKnowledgeDocCard] 处理进度: ${current}/${total} - ${currentTitle}`,
            );
          },
          onDocumentCompleted: (result) => {
            console.log(`✅ [CreateKnowledgeDocCard] 文档处理完成:`, result);
          },
          onChunkingProgress: (docId, progress) => {
            console.log(
              `📊 [CreateKnowledgeDocCard] 文档 ${docId} 切割进度: ${progress.stage} (${progress.percentage}%)`,
            );
          },
          onChunkingCompleted: (docId, result) => {
            console.log(
              `✅ [CreateKnowledgeDocCard] 文档 ${docId} 切割完成，共 ${result.chunkCount} 个块`,
            );
            $q.notify({
              type: 'info',
              message: `文档切割完成，共生成 ${result.chunkCount} 个文本块`,
              position: 'top',
            });
          },
          onVectorizationCompleted: (docId, chunkCount) => {
            console.log(
              `🎯 [CreateKnowledgeDocCard] 文档 ${docId} 向量化完成，处理了 ${chunkCount} 个块`,
            );
            $q.notify({
              type: 'positive',
              message: `文档"${documentTitle.value.trim()}"向量化完成，已可用于搜索`,
              position: 'top',
              timeout: 5000,
            });
          },
          onError: (docId, error, stage) => {
            console.error(`❌ [CreateKnowledgeDocCard] 文档 ${docId} 处理失败 (${stage}):`, error);
            $q.notify({
              type: 'negative',
              message: `文档处理失败: ${error}`,
              position: 'top',
            });
          },
        },
      );

      if (result.success && result.knowledgeDocumentId) {
        // 刷新知识库列表数据，更新文档数量统计
        await knowledgeStore.loadKnowledgeBases();

        $q.notify({
          type: 'positive',
          message: '文档已保存到知识库',
          caption: `使用 ${chunkingStrategy.value.method} 切割策略`,
          timeout: 3000,
        });

        emit(
          'saved',
          selectedKnowledgeBase.value,
          result.knowledgeDocumentId,
          documentTitle.value.trim(),
        );
        return;
      } else {
        throw new Error('文档处理失败');
      }
    }

    // 如果没有原始文档ID，回退到原来的逻辑（保持向后兼容）
    console.log('📄 [CreateKnowledgeDocCard] 使用传统创建流程 (无原始文档ID)');

    // 创建知识库文档，使用新的扩展API
    const result = await knowledge.createKnowledgeDocWithChunking(
      selectedKnowledgeBase.value,
      documentTitle.value.trim(),
      props.content || '',
      'other', // sourceType
      '', // filePath
      '', // fileType
      chunkingStrategy.value.method,
      chunkingStrategy.value.config,
      true, // _processContent
      'markdown', // documentType
    );

    // 处理返回结果，可能是数字ID或包含处理状态的对象
    let documentId: number;
    if (typeof result === 'object' && 'data' in result) {
      documentId = parseInt(result.data.id);
    } else {
      documentId = result;
    }

    console.log('💾 [CreateKnowledgeDocCard] 文档保存成功，ID:', documentId);

    // 刷新知识库列表数据，更新文档数量统计
    await knowledgeStore.loadKnowledgeBases();

    $q.notify({
      type: 'positive',
      message: '文档已保存到知识库',
      caption: `使用 ${chunkingStrategy.value.method} 切割策略`,
      timeout: 3000,
    });

    emit('saved', selectedKnowledgeBase.value, documentId, documentTitle.value.trim());
  } catch (error) {
    console.error('💾 [CreateKnowledgeDocCard] 保存文档失败:', error);
    $q.notify({
      type: 'negative',
      message: '保存失败，请重试',
    });
  } finally {
    saving.value = false;
  }
};

const handleCreateKB = async () => {
  if (!newKBName.value.trim()) return;

  creatingKB.value = true;
  try {
    const kbId = await knowledge.createKB(newKBName.value.trim(), newKBDescription.value.trim());

    // 刷新知识库列表
    await knowledgeStore.loadKnowledgeBases();

    // 自动选择新创建的知识库
    selectedKnowledgeBase.value = kbId;

    $q.notify({
      type: 'positive',
      message: `知识库 "${newKBName.value}" 创建成功`,
    });

    cancelCreateKB();
  } catch (error) {
    console.error('创建知识库失败:', error);
    $q.notify({
      type: 'negative',
      message: '创建知识库失败',
    });
  } finally {
    creatingKB.value = false;
  }
};

const cancelCreateKB = () => {
  showCreateKBDialog.value = false;
  newKBName.value = '';
  newKBDescription.value = '';
};

const onChunkingValidationChange = (isValid: boolean) => {
  chunkingConfigValid.value = isValid;
};

// 生命周期
onMounted(async () => {
  console.log('🎯 [CreateKnowledgeDocCard] 组件初始化，接收到的props:', {
    contentLength: props.content?.length || 0,
    contentPreview: props.content?.substring(0, 100) || 'empty',
    defaultTitle: props.defaultTitle || 'no title',
  });

  await loadKnowledgeBases();

  // 聚焦标题输入框
  await nextTick();
  if (titleInputRef.value) {
    titleInputRef.value.focus();
  }

  // 设置文档向量化完成监听
  const unsubscribe = knowledge.onDocumentVectorized((docId: string, chunkCount: number) => {
    console.log('📡 [CreateKnowledgeDocCard] 收到文档向量化完成:', docId, chunkCount);

    if (chunkCount > 0) {
      // 显示成功通知
      $q.notify({
        type: 'positive',
        message: '文档向量化完成',
        caption: `文档ID: ${docId}，生成了 ${chunkCount} 个知识片段`,
        timeout: 3000,
      });
    } else {
      // 处理失败
      $q.notify({
        type: 'negative',
        message: '文档向量化失败',
        caption: `文档ID: ${docId}`,
        timeout: 5000,
      });
    }

    // 刷新知识库列表数据
    void knowledgeStore.loadKnowledgeBases();
  });

  // 组件卸载时取消监听
  onUnmounted(() => {
    unsubscribe();
  });
});
</script>
