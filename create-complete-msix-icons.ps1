# 创建完整的 MSIX 图标集合，专门解决 Win32 应用的 plated 图标问题
param(
    [string]$SourceIcon = "public\icons\icon-512x512.png",
    [string]$OutputDir = "msix-icons-complete"
)

Write-Host "Creating Complete MSIX Icon Set for Win32 Apps" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "Source: $SourceIcon" -ForegroundColor Blue
Write-Host "Output: $OutputDir" -ForegroundColor Blue

# 检查源图标是否存在
if (-not (Test-Path $SourceIcon)) {
    Write-Host "Source icon not found: $SourceIcon" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# 检查是否有 System.Drawing
try {
    Add-Type -AssemblyName System.Drawing
    Write-Host "Found System.Drawing" -ForegroundColor Green
} catch {
    Write-Host "System.Drawing not available" -ForegroundColor Red
    exit 1
}

# 完整的图标规格 - 基于 Microsoft 文档和社区最佳实践
$iconSpecs = @(
    # 标准图标
    @{ Size = 44; Name = "Square44x44Logo.png"; Description = "小图标" },
    @{ Size = 150; Name = "Square150x150Logo.png"; Description = "中等图标" },
    @{ Size = 50; Name = "StoreLogo.png"; Description = "商店图标" },
    
    # Unplated 图标（关键用于任务栏）
    @{ Size = 16; Name = "Square44x44Logo.targetsize-16_altform-unplated.png"; Description = "16px unplated" },
    @{ Size = 20; Name = "Square44x44Logo.targetsize-20_altform-unplated.png"; Description = "20px unplated" },
    @{ Size = 24; Name = "Square44x44Logo.targetsize-24_altform-unplated.png"; Description = "24px unplated" },
    @{ Size = 30; Name = "Square44x44Logo.targetsize-30_altform-unplated.png"; Description = "30px unplated" },
    @{ Size = 32; Name = "Square44x44Logo.targetsize-32_altform-unplated.png"; Description = "32px unplated" },
    @{ Size = 36; Name = "Square44x44Logo.targetsize-36_altform-unplated.png"; Description = "36px unplated" },
    @{ Size = 40; Name = "Square44x44Logo.targetsize-40_altform-unplated.png"; Description = "40px unplated" },
    @{ Size = 44; Name = "Square44x44Logo.targetsize-44_altform-unplated.png"; Description = "44px unplated" },
    @{ Size = 48; Name = "Square44x44Logo.targetsize-48_altform-unplated.png"; Description = "48px unplated" },
    @{ Size = 60; Name = "Square44x44Logo.targetsize-60_altform-unplated.png"; Description = "60px unplated" },
    @{ Size = 64; Name = "Square44x44Logo.targetsize-64_altform-unplated.png"; Description = "64px unplated" },
    @{ Size = 72; Name = "Square44x44Logo.targetsize-72_altform-unplated.png"; Description = "72px unplated" },
    @{ Size = 80; Name = "Square44x44Logo.targetsize-80_altform-unplated.png"; Description = "80px unplated" },
    @{ Size = 96; Name = "Square44x44Logo.targetsize-96_altform-unplated.png"; Description = "96px unplated" },
    @{ Size = 256; Name = "Square44x44Logo.targetsize-256_altform-unplated.png"; Description = "256px unplated" },
    
    # 标准 targetsize 图标（plated 版本）
    @{ Size = 16; Name = "Square44x44Logo.targetsize-16.png"; Description = "16px plated" },
    @{ Size = 24; Name = "Square44x44Logo.targetsize-24.png"; Description = "24px plated" },
    @{ Size = 32; Name = "Square44x44Logo.targetsize-32.png"; Description = "32px plated" },
    @{ Size = 48; Name = "Square44x44Logo.targetsize-48.png"; Description = "48px plated" },
    @{ Size = 256; Name = "Square44x44Logo.targetsize-256.png"; Description = "256px plated" }
)

# 宽图标规格
$wideIconSpecs = @(
    @{ Width = 310; Height = 150; Name = "Wide310x150Logo.png"; Description = "宽图标" },
    @{ Width = 310; Height = 150; Name = "Wide310x150Logo.targetsize-310_altform-unplated.png"; Description = "宽图标 unplated" }
)

Write-Host ""
Write-Host "Creating icon files..." -ForegroundColor Yellow

# 创建图标处理函数
function Create-ResizedIcon {
    param(
        [string]$SourcePath,
        [string]$OutputPath,
        [int]$Width,
        [int]$Height = $Width
    )
    
    try {
        $sourceImage = [System.Drawing.Image]::FromFile((Resolve-Path $SourcePath).Path)
        $resizedImage = New-Object System.Drawing.Bitmap($Width, $Height)
        $graphics = [System.Drawing.Graphics]::FromImage($resizedImage)
        
        # 设置高质量渲染
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
        $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
        $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
        
        # 清除背景为透明
        $graphics.Clear([System.Drawing.Color]::Transparent)
        
        # 绘制图像
        if ($Width -eq $Height) {
            # 正方形图标
            $graphics.DrawImage($sourceImage, 0, 0, $Width, $Height)
        } else {
            # 宽图标 - 居中绘制
            $iconSize = [Math]::Min($Width, $Height)
            $x = ($Width - $iconSize) / 2
            $y = ($Height - $iconSize) / 2
            $graphics.DrawImage($sourceImage, $x, $y, $iconSize, $iconSize)
        }
        
        $resizedImage.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        $graphics.Dispose()
        $resizedImage.Dispose()
        $sourceImage.Dispose()
        
        return $true
    } catch {
        Write-Host "Error creating $OutputPath : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 创建所有图标
$successCount = 0
$totalCount = $iconSpecs.Count + $wideIconSpecs.Count

# 创建标准和 unplated 图标
foreach ($spec in $iconSpecs) {
    $iconPath = "$OutputDir\$($spec.Name)"
    $success = Create-ResizedIcon -SourcePath $SourceIcon -OutputPath $iconPath -Width $spec.Size
    
    if ($success) {
        Write-Host "Created $($spec.Name) ($($spec.Description))" -ForegroundColor Green
        $successCount++
    }
}

# 创建宽图标
foreach ($spec in $wideIconSpecs) {
    $iconPath = "$OutputDir\$($spec.Name)"
    $success = Create-ResizedIcon -SourcePath $SourceIcon -OutputPath $iconPath -Width $spec.Width -Height $spec.Height
    
    if ($success) {
        Write-Host "Created $($spec.Name) ($($spec.Description))" -ForegroundColor Green
        $successCount++
    }
}

# 验证结果
Write-Host ""
Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "Created: $successCount/$totalCount icon files" -ForegroundColor Green

if ($successCount -eq $totalCount) {
    Write-Host ""
    Write-Host "Complete MSIX icon set created successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "This includes:" -ForegroundColor Cyan
    Write-Host "- Standard icons for tiles and general display" -ForegroundColor White
    Write-Host "- Comprehensive unplated icons for taskbar (16px-256px)" -ForegroundColor White
    Write-Host "- Both plated and unplated versions for maximum compatibility" -ForegroundColor White
    Write-Host "- Wide icons for Start menu tiles" -ForegroundColor White
    Write-Host ""
    Write-Host "Next: Run MSIX build to use these icons with PRI file generation" -ForegroundColor Yellow
} else {
    Write-Host "Some icons failed to create. Check errors above." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Icon files location: $OutputDir" -ForegroundColor Blue
