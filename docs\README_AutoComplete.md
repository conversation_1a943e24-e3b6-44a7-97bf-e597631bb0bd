# AI 自动补全功能使用说明

## 功能概述

AI 自动补全功能是基于 DeepSeek API 的智能写作助手，能够在用户写作过程中实时提供文本续写建议。

## 主要特性

- **智能触发**: 当用户输入超过 10 个字符时自动触发
- **上下文感知**: 考虑文档标题、前后文内容等上下文信息
- **防抖优化**: 500ms 防抖延迟，避免频繁请求
- **多建议展示**: 同时显示 3 个不同的续写建议
- **置信度显示**: 每个建议都有置信度评分
- **键盘导航**: 支持方向键选择，Enter 键接受，Esc 键关闭

## 使用方法

### 1. 基本使用

在 TipTap 编辑器中，当您开始输入文本时，AI 会自动分析您的内容并提供续写建议：

```typescript
// 在编辑器中输入文本
const editor = useEditor({
  extensions: [
    // ... 其他扩展
    AutoComplete.configure({
      enabled: true,
      triggerLength: 10,
      debounceTime: 500,
    }),
  ],
});
```

### 2. 配置选项

```typescript
interface AutoCompleteConfig {
  enabled: boolean; // 是否启用自动补全
  triggerLength: number; // 触发自动补全的最小字符数
  maxSuggestions: number; // 最大建议数量
  debounceTime: number; // 防抖时间（毫秒）
  temperature: number; // AI生成温度
  maxTokens: number; // 最大生成token数
}
```

### 3. 使用 Composable Hook

```typescript
import { useAutoComplete } from 'src/composeables/useAutoComplete';

const {
  suggestions, // 建议列表
  isLoading, // 加载状态
  hasSuggestions, // 是否有建议
  triggerAutoComplete, // 触发自动补全
  clearSuggestions, // 清除建议
  selectSuggestion, // 选择建议
  updateConfig, // 更新配置
  toggleEnabled, // 启用/禁用
} = useAutoComplete();

// 初始化
await initialize();

// 触发自动补全
await triggerAutoComplete(currentText, cursorPosition, {
  beforeText: '上文内容',
  afterText: '下文内容',
  documentTitle: '文档标题',
  documentType: 'document',
});
```

### 4. 自定义建议面板

```vue
<template>
  <AutoCompletePanel
    v-if="showAutoCompletePanel && hasSuggestions"
    :style="panelStyle"
    @suggestion-selected="handleSuggestionSelected"
  />
</template>

<script setup>
import AutoCompletePanel from 'src/components/AutoCompletePanel.vue';

const handleSuggestionSelected = (text: string) => {
  // 插入选中的建议文本
  editor.value?.chain()
    .focus()
    .insertContent(text)
    .run();
};
</script>
```

## API 配置

### DeepSeek API 设置

确保在 LLM 设置中配置了正确的 DeepSeek API 信息：

```typescript
const qwenSettings = {
  baseUrl: 'https://api.deepseek.com/v1',
  apiKey: 'your-api-key',
  model: 'deepseek-chat',
  temperature: 0.7,
  maxTokens: 50,
  topP: 0.9,
};
```

### 提示词模板

系统使用以下提示词模板来生成建议：

```
你是一个专业的写作助手，专门提供文本自动补全建议。请根据用户输入的文本，生成3个自然的续写建议。

要求：
1. 每个建议应该是1-3个完整的句子
2. 建议要自然流畅，符合中文表达习惯
3. 考虑上下文语境和写作风格
4. 不要重复用户已有的内容
5. 直接返回建议文本，不要添加任何解释或格式

请为以下文本提供3个自然的续写建议：

上文：{beforeText}
当前文本：{textBeforeCursor}
下文：{afterText}
文档标题：{documentTitle}
文档类型：{documentType}

请提供3个续写建议，每个建议用换行符分隔：
```

## 键盘快捷键

- **↑/↓**: 在建议间导航
- **Enter**: 接受当前选中的建议
- **Esc**: 关闭建议面板
- **Tab**: 接受第一个建议

## 性能优化

1. **防抖处理**: 避免用户快速输入时频繁请求 API
2. **上下文限制**: 只使用光标前 100 个字符作为上下文
3. **建议缓存**: 相同输入不会重复请求
4. **错误处理**: API 请求失败时优雅降级

## 注意事项

1. 确保网络连接稳定，API 请求需要网络访问
2. 建议在较长的文本段落中使用，效果更佳
3. 可以根据写作风格调整 temperature 参数
4. 建议在正式文档中使用前先在测试环境验证效果

## 故障排除

### 常见问题

1. **建议不显示**: 检查 API 配置和网络连接
2. **建议质量差**: 调整 temperature 和 maxTokens 参数
3. **响应慢**: 检查网络延迟，考虑增加 debounceTime
4. **频繁触发**: 增加 triggerLength 参数

### 调试信息

在浏览器控制台中可以看到详细的调试信息：

```javascript
// 查看自动补全状态
console.log('Suggestions:', suggestions.value);
console.log('Loading:', isLoading.value);
console.log('Config:', config.value);
```

## 扩展开发

如需扩展自动补全功能，可以参考以下接口：

```typescript
interface AutoCompleteSuggestion {
  id: string;
  text: string;
  confidence: number;
  type: 'completion' | 'correction' | 'enhancement';
}
```

可以添加新的建议类型，如语法纠正、风格优化等。
