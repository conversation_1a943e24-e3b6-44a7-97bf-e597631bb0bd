/**
 * 通义千问 API 类型定义
 * 基于阿里云通义千问 API 文档
 * @see https://help.aliyun.com/zh/model-studio/use-qwen-by-calling-api
 */

import type { CategorizedModels } from './modelCategories';

export interface QwenSettings {
  baseUrl: string;
  apiKey: string;
  helpUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  enable_thinking?: boolean;
  thinking_budget?: number;
  seed?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
}

export interface OllamaSettings {
  baseUrl: string;
  apiKey: string;
  helpUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  seed?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
}

export interface MiniMaxSettings {
  baseUrl: string;
  apiKey: string;
  helpUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  seed?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
}

export interface DeepSeekSettings {
  baseUrl: string;
  apiKey: string;
  helpUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  seed?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
}

export interface MoonshotSettings {
  baseUrl: string;
  apiKey: string;
  helpUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  seed?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
}

export interface AnthropicSettings {
  baseUrl: string;
  apiKey: string;
  helpUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  thinking_enabled?: boolean;
  thinking_budget?: number;
  interleaved_thinking?: boolean;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
}

export interface GrokSettings {
  baseUrl: string;
  apiKey: string;
  helpUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
}

export interface Conversation {
  id?: number;
  document_id?: number;
  title?: string;
  messages?: string;
  prompt?: string;
  created_at?: string;
  updated_at?: string;
  success?: boolean;
  error?: string;
}

export interface ConversationMetadata {
  messages: Message[]; // 使用从 qwen.ts 导入的 Message 类型
  lastUpdated: string;
  title?: string;
  tags?: string[];
}

// ================ 基础类型定义 ================

/** 消息角色类型 */
type MessageRole = 'system' | 'user' | 'assistant' | 'tool';

/** 内容类型 */
type ContentType = 'text' | 'image_url' | 'input_audio' | 'video' | 'video_url';

/** 完成原因 */
type FinishReason = 'stop' | 'length' | 'tool_calls';

/** 工具类型 */
type ToolType = 'function';

/** 工具选择策略 */
type ToolChoice = 'auto' | 'none' | { type: 'function'; function: { name: string } };

/** 搜索策略 */
type SearchStrategy = 'standard' | 'pro';

/** 响应格式类型 */
type ResponseFormat = { type: 'text' } | { type: 'json_object' };

// ================ 消息相关类型定义 ================

/** 基础消息接口 */
interface BaseMessage {
  role: MessageRole;
}

/** 系统消息 */
interface SystemMessage extends BaseMessage {
  role: 'system';
  content: string;
}

/** 图片 URL 对象 */
interface ImageUrl {
  url: string;
}

/** 音频输入对象 */
interface InputAudio {
  data: string;
  format: string;
}

/** 视频 URL 对象 */
interface VideoUrl {
  url: string;
}

/** 多模态内容项 */
interface MultimodalContentItem {
  type: ContentType;
  text?: string;
  image_url?: ImageUrl;
  input_audio?: InputAudio;
  video?: string[];
  video_url?: VideoUrl;
}

/** 用户消息 */
interface UserMessage extends BaseMessage {
  role: 'user';
  content: string | MultimodalContentItem[];
}

/** 助手消息 */
interface AssistantMessage extends BaseMessage {
  role: 'assistant';
  content?: string;
  reasoning_content?: string;
  tool_calls?: ToolCall[];
  partial?: boolean;
}

/** 工具消息 */
interface ToolMessage extends BaseMessage {
  role: 'tool';
  content: string;
  tool_call_id?: string;
  name?: string;
}

/** 消息类型联合 */
type Message = SystemMessage | UserMessage | AssistantMessage | ToolMessage;

// ================ 工具调用相关类型定义 ================

/** JSON Schema 类型定义 */
interface JSONSchema {
  type?: string;
  properties?: Record<string, JSONSchema>;
  required?: string[];
  items?: JSONSchema;
  additionalProperties?: boolean | JSONSchema;
  description?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  enum?: any[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

/** 函数定义 */
interface FunctionDefinition {
  name: string;
  description: string;
  parameters: JSONSchema;
}

/** 工具定义 */
interface Tool {
  type: ToolType;
  function: FunctionDefinition;
}

/** 函数调用 */
interface FunctionCall {
  name: string;
  arguments: string;
}

/** 工具调用 */
interface ToolCall {
  id: string;
  type: ToolType;
  index: number;
  function: FunctionCall;
}

// ================ 请求参数类型定义 ================

/** 翻译选项 */
interface TranslationOptions {
  source_lang: string;
  target_lang: string;
  terms?: Array<{ source: string; target: string }>;
  tm_list?: Array<{ source: string; target: string }>;
  domains?: string;
}

/** 搜索选项 */
interface SearchOptions {
  forced_search?: boolean;
  search_strategy?: SearchStrategy;
}

/** 音频输出选项 */
interface AudioOptions {
  voice: 'Cherry' | 'Serena' | 'Ethan' | 'Chelsie';
  format: 'wav';
}

/** 流式输出选项 */
interface StreamOptions {
  include_usage?: boolean;
}

/** 聊天请求参数 */
interface ChatCompletionRequest {
  /**
   * 模型名称（必选）
   * 支持的模型：通义千问大语言模型（商业版、开源版、Qwen-Long）、通义千问VL、通义千问Omni、数学模型、代码模型
   * 通义千问Audio暂不支持OpenAI兼容模式，仅支持DashScope方式
   */
  model: string;

  /**
   * 由历史对话组成的消息列表（必选）
   * 包含系统消息、用户消息、助手消息和工具消息
   */
  messages: Message[];

  /**
   * 是否流式输出回复（可选，默认false）
   * true: 边生成边输出，每生成一部分内容就立即输出一个片段
   * false: 模型生成完所有内容后一次性返回结果
   * 注意：Qwen3商业版（思考模式）、Qwen3开源版、QwQ、QVQ只支持流式输出
   */
  stream?: boolean;

  /**
   * 流式输出选项（可选）
   * 当启用流式输出时，可通过设置 include_usage 为 true 在最后一行显示使用的Token数
   * 仅在 stream 为 true 时生效
   */
  stream_options?: StreamOptions;

  /**
   * 输出数据的模态（可选，默认["text"]）
   * 仅支持 Qwen-Omni 模型指定
   * ["text","audio"]: 输出文本与音频
   * ["text"]: 输出文本
   */
  modalities?: ('text' | 'audio')[];

  /**
   * 输出音频的音色与格式（可选）
   * 仅支持 Qwen-Omni 模型，且 modalities 参数需要包含 "audio"
   */
  audio?: AudioOptions;

  /**
   * 采样温度（可选）
   * 控制模型生成文本的多样性，取值范围：[0, 2)
   * 值越高，生成的文本更多样；值越低，生成的文本更确定
   * 建议与 top_p 只设置其中一个
   */
  temperature?: number;

  /**
   * 核采样的概率阈值（可选）
   * 控制模型生成文本的多样性，取值范围：(0,1.0]
   * 值越高，生成的文本更多样；值越低，生成的文本更确定
   * 建议与 temperature 只设置其中一个
   */
  top_p?: number;

  /**
   * 生成过程中采样候选集的大小（可选）
   * 取值越大，生成的随机性越高；取值越小，生成的确定性越高
   * 取值为 None 或大于 100 时，表示不启用 top_k 策略
   * 取值需要大于或等于 0
   */
  top_k?: number;

  /**
   * 控制模型生成文本时的内容重复度（可选）
   * 取值范围：[-2.0, 2.0]
   * 正数会减少重复度，负数会增加重复度
   */
  presence_penalty?: number;

  /**
   * 返回内容的格式（可选，默认{"type": "text"}）
   * 可选值：{"type": "text"} 或 {"type": "json_object"}
   * 设置为 {"type": "json_object"} 时会输出标准格式的 JSON 字符串
   */
  response_format?: ResponseFormat;

  /**
   * 本次请求返回的最大 Token 数（可选）
   * 默认值和最大值都是模型的最大输出长度
   * 如果模型生成的 Token 数超过 max_tokens，会返回截断后的内容
   */
  max_tokens?: number;

  /**
   * 生成响应的个数（可选，默认1）
   * 取值范围：1-4
   * 当前仅支持 qwen-plus 与 Qwen3（非思考模式）模型
   * 在传入 tools 参数时固定为 1
   */
  n?: number;

  /**
   * 是否开启思考模式（可选）
   * 适用于 Qwen3 模型
   * Qwen3 商业版模型默认值为 false
   * Qwen3 开源版模型默认值为 true
   */
  enable_thinking?: boolean;

  /**
   * 思考过程的最大长度（可选）
   * 只在 enable_thinking 为 true 时生效
   * 适用于 Qwen3 的商业版与开源版模型
   */
  thinking_budget?: number;

  /**
   * 设置随机种子（可选）
   * 取值范围：0 到 2^31-1
   * 使文本生成过程更具有确定性，通常用于使模型每次运行的结果一致
   */
  seed?: number;

  /**
   * 停止生成的条件（可选）
   * 当模型生成的文本即将包含指定的字符串或 token_id 时，将自动停止生成
   * 可用于控制模型输出，如传入敏感词
   * 为数组类型时，不可以将 token_id 和字符串同时作为元素输入
   */
  stop?: string | string[];

  /**
   * 可供模型调用的工具数组（可选）
   * 可以包含一个或多个工具对象
   * 一次 Function Calling 流程模型会从中选择一个工具（开启 parallel_tool_calls 可以选择多个工具）
   * 目前不支持通义千问VL/Audio，也不建议用于数学和代码模型
   */
  tools?: Tool[];

  /**
   * 工具选择策略（可选，默认"auto"）
   * "auto": 由大模型进行工具策略的选择
   * "none": 强制不使用工具
   * {"type": "function", "function": {"name": "the_function_to_call"}}: 强制调用指定工具
   */
  tool_choice?: ToolChoice;

  /**
   * 是否开启并行工具调用（可选，默认false）
   * true: 开启并行工具调用
   * false: 不开启并行工具调用
   */
  parallel_tool_calls?: boolean;

  /**
   * 翻译参数（可选）
   * 使用翻译模型时需要配置的翻译参数
   */
  translation_options?: TranslationOptions;

  /**
   * 是否启用互联网搜索（可选，默认false）
   * true: 启用互联网搜索，模型会将搜索结果作为参考信息
   * false: 关闭互联网搜索
   * 启用互联网搜索功能可能会增加 Token 的消耗
   */
  enable_search?: boolean;

  /**
   * 联网搜索的策略（可选）
   * 仅当 enable_search 为 true 时生效
   */
  search_options?: SearchOptions;
}

// ================ 响应类型定义 ================

/** Token 使用详情 */
interface TokenUsageDetails {
  /** Qwen-Omni 模型输出的音频转换为 Token 后的长度 */
  audio_tokens?: number;
  /** Qwen3 模型思考过程转换为 Token 后的长度 */
  reasoning_tokens?: number;
  /** Qwen-VL/QVQ/Qwen-Omni 模型输出的文本转换为 Token 后的长度 */
  text_tokens?: number;
  /** Qwen-VL/QVQ/Qwen-Omni 模型输入的视频转换为 Token 后的长度 */
  video_tokens?: number;
  /** Qwen-VL/QVQ/Qwen-Omni 模型输入的图片转换为 Token 后的长度 */
  image_tokens?: number;
  /** 命中 Cache 的 Token 数 */
  cached_tokens?: number;
}

/** Token 使用统计 */
interface TokenUsage {
  /** 模型生成回复转换为 Token 后的长度 */
  completion_tokens: number;
  /** 用户的输入转换成 Token 后的长度 */
  prompt_tokens: number;
  /** prompt_tokens 与 completion_tokens 的总和 */
  total_tokens: number;
  /** 输出 Token 的细粒度分类 */
  completion_tokens_details?: TokenUsageDetails;
  /** 输入 Token 的细粒度分类 */
  prompt_tokens_details?: TokenUsageDetails;
}

/** 音频响应 */
interface AudioResponse {
  /** 流式输出的文本内容 */
  transcript: string;
  /** 流式输出的 Base64 音频编码数据 */
  data: string;
  /** 创建请求时的时间戳 */
  expires_at: number;
}

/** 函数调用（即将废弃） */
interface FunctionCall {
  /** 需要被调用的函数名 */
  name: string;
  /** 需要输入到工具中的参数，为 JSON 字符串 */
  arguments: string;
}

/** 工具调用 */
interface ToolCall {
  /** 本次工具响应的 ID */
  id: string;
  /** 工具的类型，当前只支持 function */
  type: ToolType;
  /** 工具信息在 tool_calls 列表中的索引 */
  index: number;
  /** 需要被调用的函数 */
  function: {
    /** 需要被调用的函数名 */
    name: string;
    /** 需要输入到工具中的参数，为 JSON 字符串 */
    arguments: string;
  };
}

/** 消息对象 */
interface MessageObject {
  /** 本次调用模型生成的文本 */
  content?: string;
  /** 当前固定为 null */
  refusal?: null;
  /** 消息的角色，固定为 assistant */
  role: MessageRole;
  /** 当前固定为 null */
  audio?: null;
  /** 即将废弃，请参考 tool_calls 参数 */
  function_call?: null;
  /** 在发起 Function Calling 后，模型回复的要调用的工具以及调用工具所需的参数 */
  tool_calls?: ToolCall[];
}

/** 增量消息对象（用于流式输出） */
interface DeltaObject {
  /** chunk 的消息内容 */
  content?: string;
  /** QwQ 模型的深度思考内容 */
  reasoning_content?: string;
  /** 即将废弃，请参考 tool_calls 参数 */
  function_call?: null;
  /** 使用 Qwen-Omni 模型时生成的回复 */
  audio?: AudioResponse;
  /** 当前固定为 null */
  refusal?: null;
  /** 增量消息对象的角色，只在第一个 chunk 中有值 */
  role?: MessageRole;
  /** 模型回复的要调用的工具以及调用工具所需的参数 */
  tool_calls?: ToolCall[];
}

/** 选择项 */
interface Choice {
  /** 当前响应在 choices 数组中的序列编号 */
  index: number;
  /** 本次调用模型输出的消息（非流式输出） */
  message?: MessageObject;
  /** chat 请求的增量对象（流式输出） */
  delta?: DeltaObject;
  /**
   * 完成原因
   * stop: 因触发输入参数中的 stop 条件，或自然停止输出
   * length: 因生成长度过长而结束
   * tool_calls: 因需要调用工具而结束
   * null: 当生成未结束时（仅流式输出）
   */
  finish_reason: FinishReason | null;
}

/** 聊天完成响应 */
interface ChatCompletionResponse {
  /** 本次调用的唯一标识符 */
  id: string;
  /** 模型生成内容的数组，可以包含一个或多个 choices 对象 */
  choices: Choice[];
  /** 本次 chat 请求被创建时的时间戳 */
  created: number;
  /** 本次 chat 请求使用的模型名称 */
  model: string;
  /** 响应类型：chat.completion 或 chat.completion.chunk */
  object: 'chat.completion' | 'chat.completion.chunk';
  /** 当前固定为 null */
  service_tier?: null;
  /** 当前固定为 null */
  system_fingerprint?: null;
  /** 本次 chat 请求使用的 Token 信息 */
  usage?: TokenUsage;
}

// ================ 附加内容相关类型定义 ================

/** 附加内容类型 */
type AttachmentType = 'document' | 'image' | 'file' | 'text_snippet';

/** 基础附加内容接口 */
interface BaseAttachment {
  /** 唯一标识符 */
  id: string;
  /** 附加内容类型 */
  type: AttachmentType;
  /** 显示名称 */
  name: string;
  /** 创建时间 */
  createdAt: number;
}

/** 文档附加内容 */
interface DocumentAttachment extends BaseAttachment {
  type: 'document';
  /** 文档ID */
  documentId: number;
  /** 文档标题 */
  title: string;
  /** 文档内容（JSON格式） */
  content?: string;
  /** 文档纯文本内容 */
  textContent?: string;
}

/** 图片附加内容 */
interface ImageAttachment extends BaseAttachment {
  type: 'image';
  /** 图片文件路径或URL */
  src: string;
  /** 图片文件名 */
  fileName: string;
  /** 图片大小（字节） */
  size?: number;
  /** 图片MIME类型 */
  mimeType?: string;
  /** 图片尺寸 */
  dimensions?: {
    width: number;
    height: number;
  };
}

/** 文件附加内容 */
interface FileAttachment extends BaseAttachment {
  type: 'file';
  /** 文件路径 */
  filePath: string;
  /** 文件名 */
  fileName: string;
  /** 文件大小（字节） */
  size?: number;
  /** 文件MIME类型 */
  mimeType?: string;
  /** 文件扩展名 */
  extension?: string;
  /** 文件内容（如果已解析） */
  content?: string;
}

/** 文本片段附加内容 */
interface TextSnippetAttachment extends BaseAttachment {
  type: 'text_snippet';
  /** 文本内容 */
  content: string;
  /** 来源信息 */
  source?: {
    /** 来源类型 */
    type: 'selection' | 'clipboard' | 'manual';
    /** 来源文档ID（如果来自文档选择） */
    documentId?: number;
    /** 来源位置信息 */
    position?: {
      from: number;
      to: number;
    };
  };
}

/** 附加内容联合类型 */
type Attachment = DocumentAttachment | ImageAttachment | FileAttachment | TextSnippetAttachment;

/** 附加内容管理状态 */
interface AttachmentState {
  /** 所有附加内容 */
  attachments: Attachment[];
  /** 是否正在上传文件 */
  uploading: boolean;
  /** 上传进度 */
  uploadProgress: number;
}

// ================ Volces 相关类型定义 ================

/** Volces 设置接口 */
export interface VolcesSettings {
  baseUrl: string;
  apiKey: string;
  helpUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  enable_thinking?: boolean;
  thinking_budget?: number;
  seed?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
}

// ================ 知识库配置类型定义 ================

/** 文档切割方法枚举 */
export type ChunkingMethod =
  | 'markdown' // Markdown文档
  | 'recursiveCharacter' // 通用文本
  | 'latex' // 专业论文
  | 'smart'; // 智能检测内容类型并选择最合适的切割策略

/** 切割配置参数 */
export interface ChunkingConfig {
  /** 目标切割大小（字符数） */
  chunkSize?: number;
  /** 切割重叠大小（字符数） */
  chunkOverlap?: number;
  /** 语义相似度阈值 */
  semanticThreshold?: number;
  /** 是否启用多重语义处理 */
  enableMultiSemantic?: boolean;
  /** 文档类型提示 */
  documentType?: string;
  /** 是否启用层次化切割 */
  enableHierarchical?: boolean;
  /** 最大切割大小限制 */
  maxChunkSize?: number;
  /** 最小切割大小限制 */
  minChunkSize?: number;
  /** 是否保留格式信息 */
  preserveFormatting?: boolean;
}

/** 切割结果统计信息 */
export interface ChunkingStatistics {
  /** 切割后的块数量 */
  totalChunks: number;
  /** 使用的切割方法 */
  method: string;
  /** 是否成功 */
  success: boolean;
  /** 总字符数 */
  totalCharacters?: number;
  /** 平均块大小 */
  averageChunkSize?: number;
  /** 最小块大小 */
  minChunkSize?: number;
  /** 最大块大小 */
  maxChunkSize?: number;
  /** 错误信息 */
  errorMessage?: string;
}

/** 切割结果 */
export interface ChunkingResult {
  /** 切割是否成功 */
  success: boolean;
  /** 使用的切割方法 */
  method: string;
  /** 切割后的文本块列表 */
  chunks?: string[];
  /** 块数量 */
  chunkCount?: number;
  /** 统计信息 */
  statistics?: ChunkingStatistics;
  /** 元数据信息 */
  metadata?: Record<string, unknown>;
  /** 成功/错误信息 */
  message?: string;
  /** 错误信息 */
  error?: string;
}

/** 切割方法信息 */
export interface ChunkingMethodInfo {
  /** 方法标识 */
  method: string;
  /** 方法名称 */
  name: string;
  /** 方法描述 */
  description: string;
  /** 是否可用 */
  available: boolean;
  /** 默认配置 */
  defaultConfig?: ChunkingConfig;
}

/** 可用切割方法列表响应 */
export interface AvailableChunkingMethodsResponse {
  /** 请求是否成功 */
  success: boolean;
  /** 方法列表 */
  methods?: ChunkingMethodInfo[];
  /** 响应消息 */
  message?: string;
  /** 错误信息 */
  error?: string;
}

/** 切割配置验证结果 */
export interface ChunkingConfigValidation {
  /** 请求是否成功 */
  success: boolean;
  /** 方法名称 */
  method: string;
  /** 配置是否有效 */
  configValid?: boolean;
  /** 验证后的配置 */
  config?: ChunkingConfig;
  /** 验证消息 */
  message?: string;
  /** 错误信息 */
  error?: string;
}

/** GPU设备信息 */
export interface GpuDeviceInfo {
  /** 设备ID */
  deviceId: number;
  /** 设备名称 */
  deviceName: string;
  /** 后端类型(CUDA/OpenCL/Metal等) */
  backend: string;
  /** 是否可用 */
  available: boolean;
  /** 支持的最大GPU层数 */
  maxLayers?: number;
  /** 推荐的GPU层数 */
  recommendedLayers?: number;
  /** 显存大小(MB) */
  memorySize?: number;
  /** 可用显存(MB) */
  availableMemory?: number;
}

/** 知识库嵌入式模型配置 */
export interface KnowledgeBaseSettings {
  /** 嵌入式模型API基础URL */
  baseUrl: string;
  /** API密钥 */
  apiKey: string;
  /** 嵌入式模型名称 */
  model: string;
  /** 嵌入式模型向量维度（自动检测，只读） */
  embeddingDimension?: number;
  /** 嵌入式模型向量维度 */
  embeddingDimension?: number;

  // 文档切割配置 - 新的扩展系统
  /** 默认切割方法 */
  defaultChunkingMethod?: ChunkingMethod;
  /** 切割配置参数 */
  chunkingConfig?: ChunkingConfig;
  /** 默认切割策略 */
  defaultChunkStrategy?: string;

  // 传统切割配置 - 保持向后兼容
  /** 文档切割最大长度 */
  chunkSize?: number;
  /** 文档切割重叠长度 */
  chunkOverlap?: number;
  /** 语义相似度阈值 */
  semanticThreshold?: number;
  /** 搜索结果数量限制 */
  searchLimit?: number;
  /** 是否启用多重语义切割 */
  enableMultiSemanticSplit?: boolean;
  /** 是否启用层次化切割 */
  enableHierarchicalSplit?: boolean;

  // 本地GGUF模型设置
  /** 嵌入模式：cloud(云端)、local(本地)、auto(自动) */
  embeddingMode?: 'cloud' | 'local' | 'auto';
  /** 本地GGUF模型文件路径 */
  localModelPath?: string;
  /** 本地模型GPU层数 */
  localGpuLayers?: number;
  /** 本地模型上下文大小 */
  localContextSize?: number;
  /** 本地模型是否使用GPU加速 */
  localUseGpu?: boolean;
  /** 选定的GPU设备ID */
  selectedGpuDevice?: number;
}

// ================ 导出类型 ================

export type {
  MessageRole,
  ContentType,
  FinishReason,
  ToolType,
  ToolChoice,
  SearchStrategy,
  ResponseFormat,
  BaseMessage,
  SystemMessage,
  UserMessage,
  AssistantMessage,
  ToolMessage,
  Message,
  Tool,
  ToolCall,
  FunctionCall,
  FunctionDefinition,
  ChatCompletionRequest,
  ChatCompletionResponse,
  Choice,
  TokenUsage,
  TokenUsageDetails,
  AudioResponse,
  TranslationOptions,
  SearchOptions,
  AudioOptions,
  StreamOptions,
  // 附加内容相关类型
  AttachmentType,
  BaseAttachment,
  DocumentAttachment,
  ImageAttachment,
  FileAttachment,
  TextSnippetAttachment,
  Attachment,
  AttachmentState,
  // Volces 相关类型
  VolcesSettings,
  // 知识库配置类型
  KnowledgeBaseSettings,
  GpuDeviceInfo,
  // 新增切割系统类型
  ChunkingMethod,
  ChunkingConfig,
  ChunkingResult,
  ChunkingStatistics,
  ChunkingMethodInfo,
  AvailableChunkingMethodsResponse,
  ChunkingConfigValidation,
  // 工具参数相关类型
  JSONSchema,
  MultimodalContentItem,
};

// 添加特定类型别名导出
export interface MessageWithToolCall extends AssistantMessage {
  tool_calls: ToolCall[];
}

export type ToolParameter = JSONSchema;

export interface FunctionTool extends Tool {
  type: 'function';
}
