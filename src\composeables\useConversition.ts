import type {
  QwenSettings,
  OllamaSettings,
  MiniMaxSettings,
  DeepSeekSettings,
  VolcesSettings,
  MoonshotSettings,
  AnthropicSettings,
  GrokSettings,
} from 'src/types/qwen';
import type { OpenAISettings } from 'src/types/openai';
import type { AzureOpenAISettings } from 'src/types/azureOpenai';
import type { GeminiSettings } from 'src/types/gemini';
import type { GlmSettings } from 'src/types/glm';
import { computed } from 'vue';
import {
  CONVERSATION_MODEL_TYPES,
  getModelsByType,
  hasModelsOfType,
  type CategorizedModels,
} from 'src/types/modelCategories';

import { useUiStore } from 'src/stores/ui';
import { $t } from 'src/composables/useTrans';

const uiStore = useUiStore();
// 大模型选择相关方法
interface ModelOption {
  label: string;
  value: string;
  provider: string;
  model: string;
}

interface SeparatorOption {
  type: 'separator';
  provider: string;
}

type MenuOption = ModelOption | SeparatorOption;

export default function useConversition() {
  const getModelOptions = (): MenuOption[] => {
    const options: MenuOption[] = [];
    let hasAddedItems = false;
    const addedModelNames = new Set<string>(); // 用于跟踪已添加的模型名称

    // 获取LLM设置
    const llmSettings = uiStore.perferences?.llm;
    if (!llmSettings) return options;

    // 遍历所有供应商
    Object.entries(llmSettings).forEach(([providerKey, providerSettings]) => {
      // 检查供应商是否启用且有可用模型
      if (
        providerSettings &&
        typeof providerSettings === 'object' &&
        'enabled' in providerSettings &&
        'avaliableModels' in providerSettings &&
        providerSettings.enabled &&
        providerSettings.avaliableModels
      ) {
        const categorizedModels = providerSettings.avaliableModels as CategorizedModels;

        // 检查是否有对话组件需要的模型类型
        if (hasModelsOfType(categorizedModels, CONVERSATION_MODEL_TYPES)) {
          // 获取对话组件需要的模型
          const conversationModels = getModelsByType(categorizedModels, CONVERSATION_MODEL_TYPES);

          // 过滤掉已经添加过的模型名称
          const uniqueModels = conversationModels.filter((model: string) => {
            if (addedModelNames.has(model)) {
              return false; // 模型名称已存在，跳过
            }
            addedModelNames.add(model); // 记录模型名称
            return true;
          });

          if (uniqueModels.length > 0) {
            // 如果之前已经添加了其他供应商的模型，添加分割线
            if (hasAddedItems) {
              options.push({ type: 'separator', provider: providerKey });
            }

            uniqueModels.forEach((model: string) => {
              options.push({
                label: `${model}`,
                value: `${providerKey}-${model}`,
                provider: providerKey,
                model: model,
              });
            });
            hasAddedItems = true;
          }
        }
      }
    });

    return options;
  };

  /**
   * 获取当前选中的模型名称（用于按钮显示）
   * 格式：供应商 / 模型
   */
  const getCurrentModelName = (): string => {
    const currentProvider = uiStore.currentLlmProvider;
    const currentSettings = uiStore.currentLlmSettings as
      | QwenSettings
      | OllamaSettings
      | MiniMaxSettings
      | DeepSeekSettings
      | VolcesSettings
      | MoonshotSettings
      | AnthropicSettings
      | OpenAISettings
      | AzureOpenAISettings
      | GeminiSettings
      | GrokSettings
      | GlmSettings;

    if (!currentSettings || !currentSettings.model || !currentProvider) {
      return $t('src.composeables.useConversition.selectModel');
    }
    return `${currentSettings.model}`;
  };

  /**
   * 获取当前选中的模型值（用于高亮显示）
   */
  const getCurrentModelValue = (): string => {
    const currentProvider = uiStore.currentLlmProvider;
    const currentSettings = uiStore.currentLlmSettings as
      | QwenSettings
      | OllamaSettings
      | MiniMaxSettings
      | DeepSeekSettings
      | VolcesSettings
      | MoonshotSettings
      | AnthropicSettings
      | OpenAISettings
      | AzureOpenAISettings
      | GeminiSettings
      | GrokSettings
      | GlmSettings;

    if (!currentSettings || !currentSettings.model || !currentProvider) {
      return '';
    }

    return `${currentProvider}-${currentSettings.model}`;
  };

  // 附加内容相关状态
  // const showAttachmentMenu = ref(false);

  const conversationModes = [
    {
      key: 'agent',
      label: $t('src.composeables.useConversition.agent'),
      icon: 'mdi-infinity',
      description: $t('src.composeables.useConversition.agentDescription'),
      enable: false,
    },
    {
      key: 'copilot',
      label: $t('src.composeables.useConversition.copilot'),
      icon: 'mdi-cursor-text',
      description: $t('src.composeables.useConversition.copilotDescription'),
      enable: true,
    },
    {
      key: 'chat',
      label: $t('src.composeables.useConversition.chat'),
      icon: 'mdi-chat',
      description: $t('src.composeables.useConversition.chatDescription'),
      enable: true,
    },
  ];

  const hasEnabledModel = computed(() => {
    return Object.values(uiStore.perferences.llm).some(
      (provider) =>
        provider && typeof provider === 'object' && 'enabled' in provider && provider.enabled,
    );
  });

  return {
    getModelOptions,
    getCurrentModelName,
    getCurrentModelValue,
    conversationModes,
    hasEnabledModel,
  };
}
