# Utils Directory

这个目录包含了项目中使用的各种工具函数和实用程序。

## 工具模块

### 1. Markdown to TipTap Conversion Utility (`tiptap.ts`)

提供了将Markdown内容转换为TipTap JSON格式的功能。

### 2. Qt Image Download Utility (`qtImageDownload.ts`)

提供了统一的Qt图片下载接口，支持URL图片下载并转换为本地引用。

### 3. Knowledge Base Utility (`knowledgeBase.ts`)

提供了知识库相关的工具函数，包含文档切割、向量化等知识库处理功能。主要使用 LangChain 的 MarkdownTextSplitter 进行智能文档切割。

## 文件位置

`src/utils/tiptap.ts`

## 导出的函数

### `isMarkdownContent(content: string): boolean`

检测文本是否为Markdown格式。

**参数:**

- `content`: 要检测的文本内容

**返回值:**

- `boolean`: 如果是Markdown格式返回true，否则返回false

**示例:**

```typescript
import { isMarkdownContent } from 'src/utils/tiptap';

const text1 = '# Hello World\n\nThis is **bold** text.';
const text2 = 'This is plain text.';

console.log(isMarkdownContent(text1)); // true
console.log(isMarkdownContent(text2)); // false
```

### `convertMarkdownToTiptap(markdownContent: string): JSONContent`

将Markdown内容转换为TipTap JSON格式。

**参数:**

- `markdownContent`: Markdown格式的文本内容

**返回值:**

- `JSONContent`: TipTap JSON格式的内容

**示例:**

```typescript
import { convertMarkdownToTiptap } from 'src/utils/tiptap';

const markdown = '# Title\n\nThis is **bold** and *italic* text.';
const tiptapContent = convertMarkdownToTiptap(markdown);
```

### `convertHtmlTableToMarkdown(html: string): string`

将HTML表格转换为标准的Markdown表格格式。

**参数:**

- `html`: 包含HTML表格的字符串

**返回值:**

- `string`: 转换后的Markdown格式字符串

**示例:**

```typescript
import { convertHtmlTableToMarkdown } from 'src/utils/tiptap';

const htmlTable =
  '<table><tr><td>Header 1</td><td>Header 2</td></tr><tr><td>Cell 1</td><td>Cell 2</td></tr></table>';
const markdownTable = convertHtmlTableToMarkdown(htmlTable);
// 结果: | Header 1 | Header 2 |\n| --- | --- |\n| Cell 1 | Cell 2 |\n
```

### `convertContentToTiptap(content: string): JSONContent`

智能转换函数，自动检测内容是否为Markdown格式并进行相应转换。

**参数:**

- `content`: 要转换的文本内容

**返回值:**

- `JSONContent`: TipTap JSON格式的内容

**示例:**

```typescript
import { convertContentToTiptap } from 'src/utils/tiptap';

// 自动检测并转换Markdown
const content1 = '# Hello\n\nThis is **markdown**.';
const result1 = convertContentToTiptap(content1); // 转换为TipTap格式

// 普通文本会被作为段落处理
const content2 = 'This is plain text.';
const result2 = convertContentToTiptap(content2); // 作为普通段落
```

### `convertTextToTiptap(textContent: string): JSONContent`

将纯文本转换为TipTap JSON格式。

**参数:**

- `textContent`: 纯文本内容

**返回值:**

- `JSONContent`: TipTap JSON格式的内容

**示例:**

```typescript
import { convertTextToTiptap } from 'src/utils/tiptap';

const text = 'This is plain text.';
const tiptapContent = convertTextToTiptap(text);
```

## Markdown检测规则

工具会检测以下Markdown语法：

- 标题: `# ## ### ####`
- 粗体: `**text**` 或 `__text__`
- 斜体: `*text*` 或 `_text_`
- 行内代码: `` `code` ``
- 代码块: ` ```code``` `
- 无序列表: `- * +`
- 有序列表: `1. 2. 3.`
- 引用: `>`
- 链接: `[text](url)`
- 图片: `![alt](url)`
- 表格: `|col1|col2|`
- 分割线: `---`

## 检测逻辑

- 如果匹配到2个或以上的Markdown语法，则认为是Markdown
- 如果只匹配到1个语法但内容较短（<200字符），也认为是Markdown
- 否则作为纯文本处理

## 在项目中的使用

这个工具已经集成到 `src/llm/tools/file.ts` 中，用于：

1. 创建新文档时自动转换内容格式
2. 设置文档内容时自动转换格式

## 错误处理

如果Markdown转换过程中出现错误，工具会自动回退到纯文本格式，确保系统稳定性。

---

# Qt Image Download Utility

这个工具模块提供了统一的Qt图片下载接口，支持URL图片下载并转换为本地引用。

## 文件位置

`src/utils/qtImageDownload.ts`

## 导出的函数

### `downloadImageWithQt(options: ImageDownloadOptions): Promise<ImageDownloadResult>`

使用Qt后端下载单个图片并获取本地引用。

**参数:**

- `options.docId`: 文档ID
- `options.imageUrl`: 图片URL
- `options.timeout`: 下载超时时间（毫秒），默认30秒

**返回值:**

- `Promise<ImageDownloadResult>`: 下载结果，包含成功状态、图片ID和本地引用

**示例:**

```typescript
import { downloadImageWithQt } from 'src/utils/qtImageDownload';

const result = await downloadImageWithQt({
  docId: 123,
  imageUrl: 'https://example.com/image.jpg',
  timeout: 30000,
});

if (result.success) {
  console.log('图片ID:', result.imageId);
  console.log('本地引用:', result.localSrc); // image://123456
} else {
  console.error('下载失败:', result.error);
}
```

### `downloadMultipleImagesWithQt(options: ImageDownloadOptions[], concurrency?: number, delayBetweenRequests?: number): Promise<ImageDownloadResult[]>`

批量下载多个图片，支持并发控制和请求间延迟。

**参数:**

- `options`: 批量下载选项数组
- `concurrency`: 并发数量，默认为3
- `delayBetweenRequests`: 请求间延迟（毫秒），默认300ms

**返回值:**

- `Promise<ImageDownloadResult[]>`: 批量下载结果数组

**示例:**

```typescript
import { downloadMultipleImagesWithQt } from 'src/utils/qtImageDownload';

const urls = [
  'https://example.com/image1.jpg',
  'https://example.com/image2.png',
  'https://example.com/image3.gif',
];

const options = urls.map((url) => ({
  docId: 123,
  imageUrl: url,
  timeout: 30000,
}));

const results = await downloadMultipleImagesWithQt(options, 3, 300);

results.forEach((result, index) => {
  if (result.success) {
    console.log(`图片 ${index + 1} 下载成功:`, result.localSrc);
  } else {
    console.error(`图片 ${index + 1} 下载失败:`, result.error);
  }
});
```

### `isHttpImageUrl(url: string): boolean`

检查URL是否为HTTP/HTTPS图片链接。

**参数:**

- `url`: 要检查的URL

**返回值:**

- `boolean`: 是否为HTTP图片链接

**示例:**

```typescript
import { isHttpImageUrl } from 'src/utils/qtImageDownload';

console.log(isHttpImageUrl('https://example.com/image.jpg')); // true
console.log(isHttpImageUrl('data:image/png;base64,iVBOR...')); // false
console.log(isHttpImageUrl('image://123456')); // false
```

### `extractHttpImageUrls(htmlContent: string): string[]`

从HTML内容中提取所有HTTP图片URL。

**参数:**

- `htmlContent`: HTML内容

**返回值:**

- `string[]`: HTTP图片URL数组（已去重）

**示例:**

```typescript
import { extractHttpImageUrls } from 'src/utils/qtImageDownload';

const html = `
  <div>
    <img src="https://example.com/image1.jpg" alt="Image 1">
    <img src="data:image/png;base64,iVBOR..." alt="Base64 Image">
    <img src="https://example.com/image2.png" alt="Image 2">
  </div>
`;

const urls = extractHttpImageUrls(html);
console.log(urls); // ['https://example.com/image1.jpg', 'https://example.com/image2.png']
```

### `generateLocalImageSrc(imageId: number): string`

生成本地图片引用。

**参数:**

- `imageId`: 图片ID

**返回值:**

- `string`: 本地图片引用

**示例:**

```typescript
import { generateLocalImageSrc } from 'src/utils/qtImageDownload';

const localSrc = generateLocalImageSrc(123456);
console.log(localSrc); // "image://123456"
```

## 类型定义

### `ImageDownloadOptions`

```typescript
interface ImageDownloadOptions {
  docId: number;
  imageUrl: string;
  timeout?: number; // 下载超时时间（毫秒），默认30秒
}
```

### `ImageDownloadResult`

```typescript
interface ImageDownloadResult {
  success: boolean;
  imageId?: number;
  localSrc?: string;
  error?: string;
}
```

## 在项目中的使用

这个工具已经集成到以下组件中：

1. `src/components/tiptap/TipTap.vue` - 用于处理粘贴的图片URL和批量处理HTTP图片
2. `src/components/tiptap/useCommands.ts` - 用于编辑器命令中的图片下载

## 特性

- **统一接口**: 提供一致的API用于图片下载
- **错误处理**: 完善的错误处理和超时机制
- **批量处理**: 支持批量下载，可控制并发数和请求间延迟
- **类型安全**: 完整的TypeScript类型定义
- **可复用**: 可在任何需要Qt图片下载功能的组件中使用

## 错误处理

- 自动检查Qt API可用性
- 参数验证（URL、文档ID等）
- 超时处理（默认30秒）
- 详细的错误信息返回

---

## Knowledge Base Utility Functions (`knowledgeBase.ts`)

### `splitMarkdownWithLangChain(markdownContent: string, config?: ChunkingConfig, enableLogging?: boolean): Promise<ChunkingResult>`

使用 LangChain MarkdownTextSplitter 进行智能文档切割。

**参数:**

- `markdownContent`: Markdown格式的文档内容
- `config`: 切割配置（可选）
  - `chunkSize`: 块大小，默认 800 字符
  - `chunkOverlap`: 块重叠大小，默认 200 字符
- `enableLogging`: 是否启用详细日志输出，默认 false

**返回值:**

- `Promise<ChunkingResult>`: 包含切割结果的对象
  - `chunks`: 切割后的文档块数组
  - `originalLength`: 原始文档长度
  - `chunkCount`: 切割块数量
  - `summary`: 统计信息（平均、最小、最大块大小）

**示例:**

```typescript
import { splitMarkdownWithLangChain } from 'src/utils/knowledgeBase';

const markdownContent = '# 标题\n\n这是一段很长的文档内容...';
const result = await splitMarkdownWithLangChain(
  markdownContent,
  { chunkSize: 800, chunkOverlap: 200 },
  true,
);

console.log(`切割为 ${result.chunkCount} 个块`);
console.log(`平均块大小: ${result.summary.averageChunkSize} 字符`);
```

### `splitTextWithRecursiveCharacter(textContent: string, config?: ChunkingConfig, enableLogging?: boolean): Promise<ChunkingResult>`

使用 LangChain RecursiveCharacterTextSplitter 进行智能文档切割，适用于各种文本类型。

**特点:**

- 会尝试在自然边界处分割（段落、句子、单词）
- 适用于普通文本、代码、混合内容等
- 使用递归策略，优先保持语义完整性

**参数:**

- `textContent`: 文本内容
- `config`: 切割配置（可选）
  - `chunkSize`: 块大小，默认 800 字符
  - `chunkOverlap`: 块重叠大小，默认 200 字符
- `enableLogging`: 是否启用详细日志输出，默认 false

**示例:**

```typescript
import { splitTextWithRecursiveCharacter } from 'src/utils/knowledgeBase';

const textContent = '这是一段很长的文档内容...';
const result = await splitTextWithRecursiveCharacter(
  textContent,
  { chunkSize: 800, chunkOverlap: 200 },
  true,
);

console.log(`切割完成，共 ${result.chunkCount} 个块`);
```

### `splitLatexWithLangChain(latexContent: string, config?: ChunkingConfig, enableLogging?: boolean): Promise<ChunkingResult>`

使用 LangChain LatexTextSplitter 进行 LaTeX 文档切割，专门针对 LaTeX 文档的结构化切割。

**特点:**

- 保持数学公式和环境的完整性
- 识别 LaTeX 命令和结构
- 适用于学术论文、数学文档等

**参数:**

- `latexContent`: LaTeX 格式的文档内容
- `config`: 切割配置（可选）
  - `chunkSize`: 块大小，默认 800 字符
  - `chunkOverlap`: 块重叠大小，默认 200 字符
- `enableLogging`: 是否启用详细日志输出，默认 false

**示例:**

```typescript
import { splitLatexWithLangChain } from 'src/utils/knowledgeBase';

const latexContent = `
\\documentclass{article}
\\begin{document}
\\section{Introduction}
This is a LaTeX document with math: $E = mc^2$.
\\end{document}
`;

const result = await splitLatexWithLangChain(
  latexContent,
  { chunkSize: 800, chunkOverlap: 200 },
  true,
);

console.log(`切割完成，共 ${result.chunkCount} 个块`);
```

### `splitDocumentSmart(content: string, config?: ChunkingConfig, contentType?: 'markdown' | 'text' | 'latex' | 'auto', enableLogging?: boolean): Promise<ChunkingResult>`

智能文档切割函数，根据内容类型自动选择最合适的切割器。

**特点:**

- 自动检测内容类型（LaTeX、Markdown 或普通文本）
- 根据检测结果选择最优的切割策略
- 支持手动指定内容类型

**参数:**

- `content`: 文档内容
- `config`: 切割配置（可选）
- `contentType`: 内容类型提示，默认 'auto'
  - `'auto'`: 自动检测
  - `'latex'`: 强制使用 LaTeX 切割器
  - `'markdown'`: 强制使用 Markdown 切割器
  - `'text'`: 强制使用递归字符切割器
- `enableLogging`: 是否启用详细日志输出，默认 false

**示例:**

```typescript
import { splitDocumentSmart } from 'src/utils/knowledgeBase';

// 自动检测内容类型
const result = await splitDocumentSmart(
  content,
  { chunkSize: 800, chunkOverlap: 200 },
  'auto',
  true,
);

// 手动指定为 Markdown
const markdownResult = await splitDocumentSmart(
  markdownContent,
  { chunkSize: 800, chunkOverlap: 200 },
  'markdown',
);

// 手动指定为 LaTeX
const latexResult = await splitDocumentSmart(
  latexContent,
  { chunkSize: 800, chunkOverlap: 200 },
  'latex',
);
```

## 切割方案配置对象

### `CHUNKING_STRATEGIES: Record<string, SplitterInfo>`

导出的切割方案配置对象，包含所有可用的切割方案及其详细信息。

**包含的切割方案:**

| 方案名称             | 显示名称        | 适用场景                     | 主要优势                        |
| -------------------- | --------------- | ---------------------------- | ------------------------------- |
| `markdown`           | Markdown 切割器 | 技术文档、README、博客文章   | 保持结构完整性、识别标题层级    |
| `recursiveCharacter` | 递归字符切割器  | 普通文本、小说、新闻文章     | 通用性强、智能边界识别          |
| `latex`              | LaTeX 切割器    | 学术论文、数学文档、科学报告 | 保持数学公式完整、识别LaTeX环境 |
| `smart`              | 智能切割器      | 混合格式、未知格式、批量处理 | 自动格式检测、无需手动选择      |

**示例:**

```typescript
import { CHUNKING_STRATEGIES } from 'src/utils/knowledgeBase';

// 获取所有可用的切割方案
const strategies = Object.values(CHUNKING_STRATEGIES);

// 获取特定切割方案的信息
const markdownStrategy = CHUNKING_STRATEGIES.markdown;
console.log(markdownStrategy.displayName); // "Markdown 切割器"
console.log(markdownStrategy.useCases); // ["技术文档", "README 文件", ...]

// 使用切割方案
const result = await markdownStrategy.function(content, config, true);
```

### 辅助函数

#### `getAvailableStrategies(): SplitterInfo[]`

获取所有可用的切割方案列表。

```typescript
import { getAvailableStrategies } from 'src/utils/knowledgeBase';

const strategies = getAvailableStrategies();
strategies.forEach((strategy) => {
  console.log(`${strategy.displayName}: ${strategy.description}`);
});
```

#### `getStrategyInfo(strategyName: string): SplitterInfo | null`

根据名称获取切割方案信息。

```typescript
import { getStrategyInfo } from 'src/utils/knowledgeBase';

const info = getStrategyInfo('markdown');
if (info) {
  console.log(`推荐用于: ${info.recommendedFor.join(', ')}`);
}
```

#### `getRecommendedStrategy(contentType: string): string`

根据内容类型获取推荐的切割方案。

```typescript
import { getRecommendedStrategy } from 'src/utils/knowledgeBase';

const strategy = getRecommendedStrategy('markdown'); // 返回 'markdown'
const unknownStrategy = getRecommendedStrategy('unknown'); // 返回 'smart'
```

#### `splitWithStrategy(strategyName: string, content: string, config?: ChunkingConfig, enableLogging?: boolean): Promise<ChunkingResult>`

使用指定的切割方案处理文档。

```typescript
import { splitWithStrategy } from 'src/utils/knowledgeBase';

// 使用指定策略切割文档
const result = await splitWithStrategy(
  'markdown',
  content,
  { chunkSize: 600, chunkOverlap: 150 },
  true,
);

// 使用默认配置
const result2 = await splitWithStrategy('smart', content);
```

## 后台切割功能

### `splitInBackground(strategy, content, config?, enableLogging?, callbacks?): Promise<string>`

使用 Web Worker 在后台执行文档切割，避免阻塞主线程。

**特点:**

- 非阻塞执行，提升用户体验
- 支持进度回调和状态监控
- 自动任务管理和错误处理
- 支持批量处理

**参数:**

- `strategy`: 切割策略 ('markdown' | 'recursiveCharacter' | 'latex' | 'smart')
- `content`: 文档内容
- `config`: 切割配置（可选）
- `enableLogging`: 是否启用日志（可选）
- `callbacks`: 回调函数（可选）
  - `onProgress`: 进度回调
  - `onCompletion`: 完成回调
  - `onError`: 错误回调

**示例:**

```typescript
import { splitInBackground } from 'src/utils/knowledgeBase';

// 基础用法
const taskId = await splitInBackground(
  'markdown',
  content,
  { chunkSize: 800, chunkOverlap: 200 },
  true,
  {
    onProgress: (task) => {
      console.log(`进度: ${task.progress?.percentage}%`);
    },
    onCompletion: (task, result) => {
      console.log(`完成: ${result?.chunkCount} 个块`);
    },
    onError: (task, error) => {
      console.error(`错误: ${error}`);
    },
  },
);

console.log(`任务ID: ${taskId}`);
// 前端立即释放，切割在后台进行
```

### `getBackgroundTaskStatus(taskId: string): Promise<BackgroundTask | null>`

获取后台切割任务的状态信息。

```typescript
import { getBackgroundTaskStatus } from 'src/utils/knowledgeBase';

const task = await getBackgroundTaskStatus(taskId);
if (task) {
  console.log(`状态: ${task.status}`);
  console.log(`进度: ${task.progress?.percentage}%`);
  if (task.result) {
    console.log(`结果: ${task.result.chunkCount} 个块`);
  }
}
```

### Worker 管理器

#### `chunkingWorkerManager`

全局 Worker 管理器实例，提供任务管理功能。

```typescript
import { chunkingWorkerManager } from 'src/utils/chunkingWorkerManager';

// 获取所有任务
const allTasks = chunkingWorkerManager.getAllTasks();

// 获取运行中的任务
const runningTasks = chunkingWorkerManager.getRunningTasks();

// 清理已完成的任务
chunkingWorkerManager.cleanupCompletedTasks();

// 销毁 Worker（应用关闭时）
chunkingWorkerManager.destroy();
```

### 批量处理示例

```typescript
// 批量提交多个切割任务
const documents = [
  { strategy: 'markdown', content: markdownContent },
  { strategy: 'latex', content: latexContent },
  { strategy: 'smart', content: unknownContent },
];

const taskIds = await Promise.all(
  documents.map((doc) =>
    splitInBackground(doc.strategy, doc.content, undefined, false, {
      onCompletion: (task, result) => {
        console.log(`${doc.strategy} 完成: ${result?.chunkCount} 个块`);
      },
    }),
  ),
);

console.log(`已提交 ${taskIds.length} 个任务`);
```

### `validateChunkingConfig(config: ChunkingConfig): { isValid: boolean; errors: string[] }`

验证切割配置的有效性。

**参数:**

- `config`: 切割配置对象

**返回值:**

- 包含验证结果的对象
  - `isValid`: 配置是否有效
  - `errors`: 错误信息数组

### `getRecommendedChunkingConfig(documentLength: number): ChunkingConfig`

根据文档长度获取推荐的切割配置。

**参数:**

- `documentLength`: 文档长度（字符数）

**返回值:**

- `ChunkingConfig`: 推荐的切割配置

### `preprocessMarkdownForSplitting(markdownContent: string): string`

预处理 Markdown 内容，优化切割效果。

**功能:**

- 移除多余的空行
- 确保标题前后有适当的空行
- 确保代码块前后有适当的空行

### `mergeSmallChunks(chunks: DocumentChunk[], minChunkSize?: number): DocumentChunk[]`

合并过小的文档块，优化切割结果。

**参数:**

- `chunks`: 原始切割块数组
- `minChunkSize`: 最小块大小阈值，默认 200 字符

**返回值:**

- `DocumentChunk[]`: 合并后的切割块数组
