import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/doc',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/IndexPage.vue'),
        children: [
          {
            path: '/doc',
            component: () => import('pages/DocPage.vue'),
          },
          {
            path: '/settings',
            component: () => import('pages/SettingPage.vue'),
          },
          {
            path: '/debug',
            component: () => import('pages/DebugPage.vue'),
          },
          {
            path: '/fluent-icons-test',
            component: () => import('pages/FluentIconsPage.vue'),
          },
        ],
      },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
