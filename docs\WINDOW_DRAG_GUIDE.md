# 选择性窗口拖拽功能使用指南

## 🎯 功能概述

InkCop 现在支持选择性窗口拖拽功能，只有带有特定CSS类的元素才能拖拽应用窗口，其他元素保持正常的交互行为。

## 🔧 实现原理

### 核心工具函数
- **文件位置**: `src/utils/windowDrag.ts`
- **主要函数**: `useSelectiveWindowDrag()`
- **检测逻辑**: 检查点击目标是否具有 `drag-indicator` 类或其父元素具有该类

### 技术特点
- ✅ 只有带有 `drag-indicator` 类的元素可以拖拽窗口
- ✅ 支持嵌套元素（子元素继承父元素的拖拽能力）
- ✅ 其他UI元素（按钮、输入框等）保持正常交互
- ✅ 支持双击最大化/还原窗口
- ✅ 可自定义拖拽指示器类名

## 📝 使用方法

### 1. 在Vue组件中使用

```vue
<template>
  <div @mousedown="handleMouseDown" @dblclick="handleDoubleClick">
    <!-- 可拖拽区域 -->
    <div class="drag-indicator bg-primary text-white">
      <h3>标题栏 - 可拖拽</h3>
    </div>
    
    <!-- 不可拖拽区域 -->
    <div class="content">
      <button>普通按钮 - 不可拖拽</button>
      <input type="text" placeholder="输入框 - 不可拖拽" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSelectiveWindowDrag } from 'src/utils/windowDrag';

// 启用选择性窗口拖拽
const { handleMouseDown, handleDoubleClick } = useSelectiveWindowDrag();
</script>
```

### 2. 自定义拖拽指示器类名

```typescript
// 使用自定义类名
const { handleMouseDown, handleDoubleClick } = useSelectiveWindowDrag('my-drag-handle');
```

```html
<!-- HTML中使用自定义类名 -->
<div class="my-drag-handle">可拖拽区域</div>
```

### 3. 直接在DOM元素上使用

```typescript
import { addSelectiveWindowDrag } from 'src/utils/windowDrag';

// 为DOM元素添加拖拽功能
const element = document.getElementById('my-element');
const cleanup = addSelectiveWindowDrag(element);

// 清理事件监听器
cleanup();
```

## 🎨 CSS样式建议

```css
.drag-indicator {
  cursor: move;
  user-select: none;
  transition: background-color 0.2s ease;
}

.drag-indicator:hover {
  opacity: 0.8;
}
```

## 📋 示例场景

### 1. 标题栏拖拽
```html
<header class="drag-indicator app-header">
  <h1>应用标题</h1>
  <div class="header-actions">
    <button>设置</button>
    <button>关闭</button>
  </div>
</header>
```

### 2. 混合布局
```html
<div class="toolbar">
  <div class="drag-indicator drag-handle">
    <i class="icon-drag"></i>
    拖拽手柄
  </div>
  <div class="toolbar-content">
    <button>操作1</button>
    <button>操作2</button>
  </div>
</div>
```

### 3. 卡片式布局
```html
<div class="card">
  <div class="drag-indicator card-header">
    <h3>卡片标题</h3>
  </div>
  <div class="card-body">
    <p>卡片内容区域不可拖拽</p>
    <button>操作按钮</button>
  </div>
</div>
```

## 🧪 测试页面

访问 `/drag-test` 路由可以查看完整的测试示例，包括：
- 可拖拽和不可拖拽区域的对比
- 混合布局示例
- 使用说明和最佳实践

## 🔍 API参考

### `useSelectiveWindowDrag(dragClass?: string)`
Vue组合式函数，返回事件处理器。

**参数:**
- `dragClass` (可选): 拖拽指示器类名，默认为 `'drag-indicator'`

**返回值:**
```typescript
{
  handleMouseDown: (event: MouseEvent) => boolean;
  handleDoubleClick: (event: MouseEvent) => boolean;
  isDragIndicator: (element: Element | null) => boolean;
}
```

### `isDragIndicator(element, dragClass?)`
检查元素是否可以拖拽窗口。

### `handleSelectiveWindowDrag(event, dragClass?)`
处理选择性窗口拖拽的鼠标按下事件。

### `handleSelectiveWindowDoubleClick(event, dragClass?)`
处理选择性窗口双击事件（最大化/还原）。

### `addSelectiveWindowDrag(element, dragClass?)`
为DOM元素添加选择性窗口拖拽功能，返回清理函数。

## 🚀 最佳实践

1. **明确的视觉指示**: 使用 `cursor: move` 和适当的样式来指示可拖拽区域
2. **合理的拖拽区域**: 确保拖拽区域足够大，便于用户操作
3. **避免冲突**: 不要在交互元素（按钮、链接等）上添加拖拽功能
4. **响应式设计**: 在移动设备上考虑禁用或调整拖拽行为
5. **用户体验**: 提供清晰的视觉反馈，让用户知道哪些区域可以拖拽

## 🐛 故障排除

### 问题：点击任何地方都会拖拽窗口
**解决方案**: 检查是否正确使用了 `useSelectiveWindowDrag()` 而不是直接绑定 `startWindowDrag`

### 问题：拖拽区域不响应
**解决方案**: 
1. 确保元素具有正确的 `drag-indicator` 类
2. 检查事件处理器是否正确绑定
3. 确认Qt集成是否正常工作

### 问题：双击不能最大化窗口
**解决方案**: 确保同时绑定了 `@dblclick="handleDoubleClick"` 事件

---

现在您可以在InkCop应用中享受精确的窗口拖拽控制！🎉
