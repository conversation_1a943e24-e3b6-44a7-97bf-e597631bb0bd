# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

InkCop is a Qt-based AI-powered writing assistant with llama.cpp integration for local GGUF model support. The application combines a Quasar/Vue.js frontend with a Qt WebEngine backend, plus optional local embedding capabilities.

## Common Build Commands

### Windows Development

```powershell
# Development build with hot reload and console output
.\win-dev.ps1

# Production build
.\win-prod.ps1

# Create installer
.\build-installer.ps1 -Type MSIX
```

### Frontend Development

```bash
# Install dependencies
bun install

# Start development server (runs on localhost:9000)
bun run dev

# Build for production
bun run build
```

### Testing

```bash
# Frontend tests
bun run test

# Qt application testing
# Tests are integrated into CMake build - check build output for test results
```

## Architecture & Structure

### Hybrid Architecture

- **Frontend**: Vue.js 3 + Quasar Framework (SPA)
- **Backend**: Qt 6.9.1 + QtWebEngine
- **Database**: ObjectBox (embedded)
- **AI Integration**: Multiple LLM providers + optional local llama.cpp
- **Build System**: CMake + Vite + PowerShell scripts

### Key Components

#### Qt Backend (`qt-src/`)

- `main.cpp` - Application entry point with WebEngine configuration
- `mainwindow.cpp/.h` - Main window and WebEngine container
- `localggufembedding.cpp/.h` - Local GGUF model support via llama.cpp
- `knowledgeapi.cpp/.h` - Knowledge base management API
- `databaseapi.cpp/.h` - ObjectBox database interface
- `windowapi.cpp/.h` - Window management and system integration
- `customwebengineview.cpp/.h` - Enhanced WebEngine view
- `objectbox/` - ObjectBox schema and generated code

#### Frontend (`src/`)

- `App.vue` - Root Vue component
- `layouts/MainLayout.vue` - Main application layout
- `pages/` - Vue.js pages (IndexPage, DocPage, SettingPage, etc.)
- `components/` - Reusable Vue components
- `composables/` - Vue composition API utilities
- `stores/` - Pinia state management stores

#### Build System

- `CMakeLists.txt` - Main CMake configuration with llama.cpp integration
- `quasar.config.ts` - Quasar framework configuration
- `win-dev.ps1` - Development build script
- `win-prod.ps1` - Production build script

### llama.cpp Integration

The project includes optional local GGUF model support:

#### Build Configuration

- Enable with: `-DENABLE_LOCAL_GGUF=ON` in CMake
- Requires llama.cpp to be built in `third-party/llama.cpp/build/`
- Links against: `llama`, `common`, `ggml`, `ggml-base`, `ggml-cpu` libraries

#### Key Integration Points

- `LocalGGUFEmbedding` class manages model loading and inference
- Supports GPU acceleration via CUDA (if available)
- Thread-safe embedding generation
- Automatic GPU capability detection

#### Common Issues with llama.cpp

1. **Missing Libraries**: Ensure llama.cpp is built with required libraries
2. **GPU Support**: CUDA runtime must be installed for GPU acceleration
3. **Memory Issues**: Large models require sufficient RAM/VRAM
4. **Library Paths**: CMake must find llama.cpp libraries in expected locations

## Development Workflow

### Frontend Development

1. Start Quasar dev server: `bun run dev`
2. Use development build: `.\win-dev.ps1` (loads from localhost:9000)
3. Hot reload is automatically enabled in dev mode

### Qt Development

1. Build with Visual Studio 2022 Community
2. Use development mode for debugging: `-DCONSOLE_MODE=ON`
3. Check Qt 6.9.1 installation at `C:\Qt\6.9.1\msvc2022_64`

### Building with llama.cpp Support

1. First build llama.cpp:
   ```bash
   cd third-party/llama.cpp
   mkdir build && cd build
   cmake .. -DGGML_CUDA=ON  # For CUDA support
   cmake --build . --config Release
   ```
2. Build InkCop with GGUF support:
   ```powershell
   # Use build scripts that enable GGUF support
   .\build-with-gguf.ps1
   ```

## Runtime Issues Troubleshooting

### Silent Exit on Startup

This is typically caused by:

1. **Missing Qt Dependencies**: Run `windeployqt` on the executable
2. **DLL Loading Issues**: Check that `objectbox.dll` is in the same directory as the executable
3. **llama.cpp Library Issues**: If `ENABLE_LOCAL_GGUF=ON`, ensure all llama.cpp libraries are available
4. **WebEngine Initialization**: Check that Qt WebEngine can initialize properly

### Debug Steps

1. **Enable Console Mode**: Build with `-DCONSOLE_MODE=ON` to see debug output
2. **Check Dependencies**: Use Dependency Walker or similar tools
3. **Verify Resource Loading**: Ensure QRC resources are embedded properly
4. **Test Without llama.cpp**: Build without `-DENABLE_LOCAL_GGUF=ON` to isolate issues

### Configuration Files

- `qt-logging.rules` - Qt logging configuration
- `qt-rendering.conf` - WebEngine Chromium flags
- `.env.production` - API keys and environment variables

## Dependencies

### Required

- Qt 6.9.1 (MSVC 2022)
- Visual Studio 2022 Community
- CMake 3.16+
- Node.js (for Bun)
- Bun package manager

### Optional

- CUDA Toolkit (for GPU acceleration)
- llama.cpp libraries (for local GGUF support)

### Third-party Libraries

- **ObjectBox**: Embedded database (Windows x64 binaries included)
- **FlatBuffers**: Required by ObjectBox (headers included)
- **llama.cpp**: Local LLM inference (source included, must be built)

## Important Notes

### Type Safety Requirements

- **No `any` types allowed**: The compiler is configured to disallow `any` types. Use precise type definitions.
- **Minimize `unknown` usage**: Use `unknown` sparingly and only when absolutely necessary.
- **Explicit typing**: Always provide explicit types for function parameters, return values, and variables.
- **Type inference**: Leverage TypeScript's type inference where appropriate, but be explicit when clarity is needed.

### Memory Management

- ObjectBox handles data persistence
- WebEngine caches are configured per build mode
- llama.cpp models can use significant RAM/VRAM

### Security Considerations

- WebEngine security is relaxed for local development
- API keys should be stored in `.env.production` (not committed)
- Local models run entirely offline

### Platform Support

- Primary target: Windows x64
- Linux build scripts available but may need adjustment
- macOS support planned but not currently maintained

## Adding New LLM Providers

### Overview

InkCop supports multiple LLM providers. Follow this guide to add a new provider.

### Prerequisites - MUST READ FIRST

**Before starting any implementation:**

1. **Research the Latest API Documentation**
   - Use WebSearch or WebFetch to find and read the provider's official API documentation
   - Look for the latest API version, endpoints, and authentication methods
   - Understand message formats, streaming protocols, and special features
   - Check for any breaking changes or deprecations
   - Note any provider-specific requirements or limitations

2. **Document Key Findings**
   - API endpoint structure
   - Authentication mechanism (headers, tokens, etc.)
   - Message format requirements
   - Streaming response format
   - Tool/function calling format (if supported)
   - Rate limits and quotas
   - Special features (e.g., thinking modes, vision support)

### Required Steps

1. **Type Definitions** (`src/types/provider.d.ts` or extend `src/types/qwen.d.ts`)
   - Add provider settings interface with standard fields:
     - baseUrl, apiKey, model, temperature, maxTokens, stream, topP
     - avaliableModels array, enabled flag
     - Provider-specific fields (e.g., thinking_enabled for Anthropic)
   - **Type Safety Checks**:
     - Ensure all types are properly exported
     - Use type aliases for simple type extensions: `type ToolParameter = JSONSchema`
     - Add necessary type exports like `MessageWithToolCall`, `FunctionTool`
     - Avoid empty interfaces - use type aliases instead

2. **Environment Types** (`src/env.d.ts`)
   - Import the new settings type
   - Add to LlmSettings interface

3. **Default Settings** (`src/config/defaultSettings.ts`)
   - Define PROVIDER_BASEURL and PROVIDER_APIKEY constants
   - Create DEFAULT_PROVIDER_SETTINGS object
   - Add to DEFAULT_LLM_SETTINGS

4. **Create Composable** (`src/composeables/useProvider.ts`)
   - **CRITICAL**: Call Pinia stores inside the function, never at module level:
     ```typescript
     export default function useProvider() {
       const uiStore = useUiStore(); // ✅ Inside function
       // ... rest of implementation
     }
     ```
   - Implement sendMessage method with:
     - Proper API request formatting
     - Streaming response handling (SSE)
     - Tool call support if applicable
     - Error handling and retry logic
   - **Type Compatibility**:
     - Use type assertions when needed: `messages as Message[]`
     - Handle multimodal content: check if content is string or array
     - Cast tools array: `tools as Tool[]`
   - **ESLint Compliance**:
     - Avoid `any` types - define proper interfaces
     - Don't use unnecessary parentheses in type assertions
     - Remove unused imports
     - Don't use `await` on synchronous functions

5. **Update Router** (`src/composeables/useLlmRouter.ts`)
   - Import and initialize the new hook
   - Add to currentSettings computed property
   - Add to needSettings validation
   - Add to sendMessage routing with proper type handling:
     ```typescript
     await providerHook.sendMessage(
       params.messages,
       params.tools as Tool[], // Type assertion for tools
       // ... other parameters
     );
     ```
   - Export the hook
   - Import any required types (e.g., `Tool` from qwen.d.ts)

6. **Update Message Preprocessor** (if needed)
   - Check `SimplifiedLlmParams` interface in `src/services/messagePreprocessor.ts`
   - Add any missing properties like `onToolCall`, `signal`

7. **Settings Component** (`src/components/settings/llms/ProviderOptions.vue`)
   - Create settings UI with:
     - API key and base URL inputs
     - Model selection dropdown
     - Parameter sliders (temperature, topP)
     - Enable/disable toggle
     - Provider-specific options
   - **Vue Best Practices**:
     - Import only what's needed from stores
     - Use proper TypeScript types in components
     - Avoid `any` types in event handlers

8. **UI Store Updates** (`src/stores/ui.ts`)
   - Add updateProviderSettings method
   - Handle autoAdjustAutoComplete when enabled status changes
   - **Important**: Methods are synchronous, don't use `async/await`

9. **Settings Panel** (`src/components/settings/LlmSettings.vue`)
   - Import the settings component
   - Add provider to the sidebar list
   - Add component template condition

10. **Model Selection**
    - Update `src/composeables/useConversition.ts`:
      - Import settings type
      - Add to getModelOptions() function
      - Include in type unions for getCurrentModelName/Value
    - Update `src/components/ConversitonContainer.vue`:
      - Add case in selectModel() function

11. **Resource Map** (`src/config/resourceMap.ts`)
    - Add provider icon configuration
    - Copy icon files to public/icons/llm/

12. **Tool Support** (`src/stores/llm.ts` - if applicable)
    - Add model keywords to toolCapableModels array

13. **Type Compatibility Verification**
    - Run `npm run lint` to check for ESLint errors
    - Run `npx vue-tsc --noEmit` to check for TypeScript errors
    - Fix any type incompatibilities:
      - Message format differences between providers
      - Tool array type constraints
      - Missing type exports
      - Optional property additions to interfaces

### Important Considerations

1. **API Compatibility**
   - Study provider's API documentation thoroughly
   - Handle authentication properly (headers, format)
   - Understand message format requirements
   - Process streaming responses correctly

2. **Error Handling**
   - Provide clear error messages
   - Handle network and API errors gracefully
   - Implement request cancellation

3. **Type Safety**
   - Ensure all types are properly imported and used
   - Don't forget to update type unions (common mistake)

4. **Testing Checklist**
   - Basic conversation flow
   - Streaming responses
   - Tool calls (if supported)
   - Settings persistence
   - Model switching
   - Error scenarios
   - Icon display in light/dark themes

### Common Issues

1. **Icon Not Showing**: Check file path and existence
2. **Models Not in Dropdown**: Verify enabled status and API key
3. **Message Send Fails**: Check API format and auth headers
4. **Tool Calls Not Working**: Add to toolCapableModels
5. **Type Errors**: Update all type unions consistently
6. **Message Type Incompatibility**: When pushing messages to `messagesRef.value`, ensure proper type casting:
   - Import `Message` type from `src/types/qwen`
   - Cast messages as `Message` type when pushing: `messagesRef.value.push({...} as Message)`
   - For tool_calls assignment, use proper type intersection: `(lastMessage as Message & { tool_calls?: typeof toolCalls })`

### Common Integration Pitfalls and Solutions

Based on real integration experience, here are the most common errors and their solutions:

1. **Pinia Store Initialization Error**

   ```
   Error: getActivePinia was called with no active Pinia
   ```

   - **Cause**: Calling `useStore()` at module level
   - **Solution**: Always call stores inside functions, not at module level
   - **Example**: See step 4 in Required Steps

2. **ESLint Violations**
   - **Empty interfaces**: Use type aliases instead of empty interfaces
   - **Unnecessary parentheses**: Use `{} as Type` not `({} as Type)`
   - **Any types**: Define proper interfaces for all data structures
   - **Unused imports**: Remove imports that are already available via computed properties
   - **Async/await misuse**: Don't use `await` on synchronous functions like store updates

3. **TypeScript Type Errors**
   - **Message type incompatibility**: Different providers have different message formats
     - Solution: Use type assertions `messages as Message[]`
   - **Tool type constraints**: Tools must have literal type `'function'` not string
     - Solution: Cast tool arrays `tools as Tool[]`
   - **Missing exports**: Some types may not be exported from type files
     - Solution: Add exports to type definition files
   - **Multimodal content**: Handle both string and array content types
     - Solution: Check content type before operations

4. **Missing Interface Properties**
   - **SimplifiedLlmParams**: May need additional properties like `onToolCall`, `signal`
   - **Solution**: Update interfaces in `messagePreprocessor.ts`

5. **Import Issues**
   - **AssistantMessage**: May need to be imported from different files for different providers
   - **Tool type**: Often forgotten when updating router
   - **Solution**: Check and add all necessary imports

### Pre-Integration Checklist

Before starting integration:

- [ ] Research latest API documentation
- [ ] Understand streaming format (SSE, WebSocket, etc.)
- [ ] Check tool/function calling support
- [ ] Note any special authentication requirements
- [ ] Identify provider-specific features

### Post-Integration Verification

After completing integration:

- [ ] Run `npm run lint` - fix all ESLint errors
- [ ] Run `npx vue-tsc --noEmit` - fix all TypeScript errors
- [ ] Test basic conversation flow
- [ ] Test streaming responses
- [ ] Test tool calls (if supported)
- [ ] Verify settings persistence
- [ ] Test model switching
- [ ] Check error scenarios
- [ ] Verify icon display in both themes

### Example: Grok AI Integration

Here's a real example of adding Grok AI (xAI) support:

1. **API Research**: Grok uses OpenAI-compatible API at `https://api.x.ai/v1`
2. **Type Definitions**: Created GrokSettings interface in `qwen.d.ts`
3. **Composable**: Implemented `useGrok.ts` with OpenAI-compatible format
4. **Message Type Fix**: When TypeScript complained about `GrokMessage` vs `Message`:

   ```typescript
   // ❌ Wrong: Argument of type 'GrokMessage' is not assignable to parameter of type 'Message'
   messagesRef.value.push({...} as GrokMessage);

   // ✅ Correct: Import and use the shared Message type
   import type { Message } from 'src/types/qwen';
   messagesRef.value.push({...} as Message);
   ```

5. **Tool Calls**: Added support with proper type casting for `lastMessage`
6. **Icons**: Created custom SVG icons for light/dark themes
7. **Testing**: Verified streaming, tool calls, and settings persistence
