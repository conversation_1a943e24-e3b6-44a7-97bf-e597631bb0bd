# 工具按钮SVG图标实现

## 概述
为标题栏的工具按钮实现了新的SVG图标，支持明暗主题自动切换。

## 按钮图标对应关系

| 按钮功能 | 图标文件 | 描述 |
|---------|---------|------|
| reload (刷新) | refresh.svg | 页面刷新图标 |
| developer tools (开发工具) | code.svg | 代码/开发工具图标 |
| toggle mode (切换模式) | exchange.svg | 交换/切换图标 |
| theme (主题切换) | theme-light-dark.svg | 明暗主题切换图标 |

## 创建的图标文件

### 自适应主题版本
- `refresh.svg` - 刷新图标（自适应主题）
- `code.svg` - 代码图标（自适应主题）
- `exchange.svg` - 交换图标（自适应主题）
- `theme-light-dark.svg` - 主题切换图标（自适应主题）

### 专用主题版本
- `refresh_light.svg` / `refresh_dark.svg` - 刷新图标明暗版本
- `code_light.svg` / `code_dark.svg` - 代码图标明暗版本
- `exchange_light.svg` / `exchange_dark.svg` - 交换图标明暗版本
- `theme-light-dark_light.svg` / `theme-light-dark_dark.svg` - 主题图标明暗版本

## 技术实现

### 图标路径格式
```cpp
QString iconPath = QString(":/icons/ui/%1%2.svg")
    .arg(iconName)     // "refresh", "code", "exchange", "theme-light-dark"
    .arg(themeSuffix); // "_light" 或 "_dark"
```

### 主题适配
- **浅色主题**：使用深色图标 (#2c2c2c)
- **深色主题**：使用浅色图标 (#e0e0e0)
- **自适应版本**：使用CSS媒体查询自动切换

### 自适应主题CSS
```css
@media (prefers-color-scheme: dark) {
  .theme-adaptive { fill: #e0e0e0; }
}
@media (prefers-color-scheme: light) {
  .theme-adaptive { fill: #2c2c2c; }
}
```

## 代码修改

### 新增方法
- `updateToolButtonIcons()` - 更新工具按钮图标
- 在 `applyTheme()` 中调用图标更新
- 在构造函数中初始化图标

### 按钮创建修改
```cpp
// 修改前
m_reloadBtn = createToolButton(":/icons/refresh.png", "reload (F5)");

// 修改后
m_reloadBtn = createToolButton("refresh", "reload (F5)");
```

### 图标更新逻辑
```cpp
void QWKCustomTitleBar::updateToolButtonIcons()
{
    QString themeSuffix = m_isDarkTheme ? "_dark" : "_light";
    
    // 为每个按钮设置对应的主题图标
    if (m_reloadBtn) {
        QString iconPath = QString(":/icons/ui/refresh%1.svg").arg(themeSuffix);
        if (QFile::exists(iconPath)) {
            m_reloadBtn->setIcon(QIcon(iconPath));
            m_reloadBtn->setIconSize(QSize(16, 16));
        }
    }
    // ... 其他按钮类似处理
}
```

## 回退机制
如果SVG图标文件不存在，会显示Unicode符号：
- 刷新按钮：⟳
- 开发工具按钮：⚙
- 切换模式按钮：⇄
- 主题按钮：🌣 (暗色) / ☪︎ (亮色)

## 资源文件更新
在 `webapp.qrc` 中添加了所有新的图标文件：
```xml
<file alias="icons/ui/refresh.svg">dist/spa/icons/ui/refresh.svg</file>
<file alias="icons/ui/refresh_light.svg">dist/spa/icons/ui/refresh_light.svg</file>
<file alias="icons/ui/refresh_dark.svg">dist/spa/icons/ui/refresh_dark.svg</file>
<!-- ... 其他图标文件 -->
```

## 功能特点

### 1. 主题感知
- 图标会根据当前主题自动切换颜色
- 支持系统主题偏好检测
- 手动主题切换时立即更新

### 2. 开发/生产模式适配
- 开发模式：显示所有工具按钮
- 生产模式：只显示主题切换按钮
- 按钮存在性检查，避免空指针错误

### 3. 高质量图标
- 使用SVG格式，支持任意缩放
- 统一的视觉风格
- 清晰的语义化图标

## 构建要求
1. 确保前端已构建：`npm run build` 或 `bun run build`
2. 重新构建Qt项目以包含新的资源文件
3. 确保所有SVG文件都在正确的目录中

## 测试验证
- [x] 按钮图标正确显示
- [x] 主题切换时图标颜色正确变化
- [x] 开发/生产模式下按钮显示正确
- [x] 图标文件不存在时回退到Unicode符号
- [x] 图标尺寸和样式统一

## 注意事项
1. 图标更新依赖于主题状态变化
2. 确保Qt资源系统正确包含所有图标文件
3. 图标文件路径使用Qt资源系统格式：`:/icons/ui/filename.svg`
4. SVG图标支持主题色彩自动适配
