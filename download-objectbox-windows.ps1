# Download ObjectBox Windows binaries
param(
    [string]$Version = "v4.3.0",
    [string]$TargetDir = "third-party/objectbox-windows"
)

Write-Host "Downloading ObjectBox Windows binaries..." -ForegroundColor Cyan
Write-Host "Version: $Version" -ForegroundColor Blue
Write-Host "Target Directory: $TargetDir" -ForegroundColor Blue

# Create target directory
if (-not (Test-Path $TargetDir)) {
    New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
    Write-Host "Created directory: $TargetDir" -ForegroundColor Green
}

# ObjectBox Windows download URL
$downloadUrl = "https://github.com/objectbox/objectbox-c/releases/download/$Version/objectbox-windows-x64.zip"
$zipFile = "$TargetDir/objectbox-windows-x64.zip"

Write-Host ""
Write-Host "Downloading from: $downloadUrl" -ForegroundColor Yellow

try {
    # Download the zip file
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipFile -UseBasicParsing
    Write-Host "Download completed!" -ForegroundColor Green
    
    # Extract the zip file
    Write-Host "Extracting archive..." -ForegroundColor Yellow
    Expand-Archive -Path $zipFile -DestinationPath $TargetDir -Force
    
    # Remove the zip file
    Remove-Item $zipFile -Force
    Write-Host "Archive extracted and cleaned up!" -ForegroundColor Green
    
    # List the contents
    Write-Host ""
    Write-Host "Downloaded ObjectBox contents:" -ForegroundColor Cyan
    Get-ChildItem -Path $TargetDir -Recurse | ForEach-Object {
        $relativePath = $_.FullName.Replace((Get-Location).Path + "\", "")
        if ($_.PSIsContainer) {
            Write-Host "  [DIR] $relativePath" -ForegroundColor Blue
        } else {
            $size = [math]::Round($_.Length / 1KB, 1)
            Write-Host "  [FILE] $relativePath ($size KB)" -ForegroundColor White
        }
    }
    
    Write-Host ""
    Write-Host "ObjectBox Windows binaries downloaded successfully!" -ForegroundColor Green
    Write-Host "You can now build the project using the local ObjectBox installation." -ForegroundColor Green
    
} catch {
    Write-Host "Error downloading ObjectBox: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check your internet connection and try again." -ForegroundColor Yellow
    exit 1
}
