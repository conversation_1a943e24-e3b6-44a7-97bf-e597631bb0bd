import { computed, type Ref } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { useSqlite } from './useSqlite';
import { DEFAULT_OLLAMA_SETTINGS } from 'src/config/defaultSettings';
import type { AssistantMessage } from 'src/types/qwen';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import { executeToolCall } from 'src/llm/tools/index';
import { processer } from 'src/utils/processer';
import { $t } from 'src/composables/useTrans';
import { llmStore } from './useStore';

export const useOllama = () => {
  // 在函数内部获取组合式函数
  const { updateConversation } = useSqlite();
  const uiStore = useUiStore();
  const { thinkContent } = processer();

  // 使用 computed 来创建响应式的 ollamaSettings
  const ollamaSettings = computed(() => {
    return uiStore.perferences?.llm?.ollama || DEFAULT_OLLAMA_SETTINGS;
  });

  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const { messages, messagesRef, conversation, tools, enableTools, abortController } = params;

    if (!ollamaSettings.value) return;

    console.log('[useOllama] 开始发送消息到Ollama');
    console.log('[useOllama] 消息数量:', messages.length);
    console.log('[useOllama] 当前设置:', ollamaSettings.value);

    try {
      loading.value = true;

      // 构建请求体
      const requestBody: {
        model: string;
        messages: typeof messages;
        stream: boolean;
        temperature?: number;
        max_tokens: number;
        top_p?: number;
        stop: string[];
        seed: number;
        tools?: typeof tools;
        tool_choice?: string;
      } = {
        model: ollamaSettings.value.model,
        messages: [...messages],
        stream: ollamaSettings.value.stream,
        max_tokens: ollamaSettings.value.maxTokens,
        stop: Array.isArray(ollamaSettings.value.stop)
          ? ollamaSettings.value.stop
          : ollamaSettings.value.stop
            ? [ollamaSettings.value.stop]
            : [],
        seed: ollamaSettings.value.seed,
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (
        ollamaSettings.value.temperature !== undefined &&
        !isNaN(ollamaSettings.value.temperature)
      ) {
        requestBody.temperature = ollamaSettings.value.temperature;
      }
      if (ollamaSettings.value.topP !== undefined && !isNaN(ollamaSettings.value.topP)) {
        requestBody.top_p = ollamaSettings.value.topP;
      }

      // 如果有启用的工具，添加工具定义
      if (enableTools && tools.length > 0) {
        console.log(`🔧 [useOllama] 启用工具数: ${tools.length}`);
        console.log(`🔧 [useOllama] 工具列表: ${tools.map((t) => t.function.name).join(', ')}`);

        requestBody.tools = tools;
        requestBody.tool_choice = 'auto';
      }

      console.log('[useOllama] 请求体:', requestBody);

      const response = await fetch(`${ollamaSettings.value.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${ollamaSettings.value.apiKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('ReadableStream not supported');
      }
      llmStore.waittingLlm = false;

      // 添加助手消息
      messagesRef.value.push({
        role: 'assistant',
        content: '',
        reasoning_content: '',
      });
      const lastMessage = messagesRef.value[messagesRef.value.length - 1] as AssistantMessage;

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let ollamaContentBuffer = ''; // 用于累积Ollama的完整内容

      while (true) {
        // 检查是否被中断
        if (abortController?.signal.aborted) {
          void reader.cancel();
          throw new DOMException('Operation was aborted', 'AbortError');
        }

        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices?.[0]?.delta;

              if (delta?.content) {
                ollamaContentBuffer += delta.content;

                // 处理思考内容
                const { content, reasoning_content } = thinkContent(ollamaContentBuffer);

                lastMessage.content = content;
                if (reasoning_content) {
                  lastMessage.reasoning_content = reasoning_content;
                }
              }

              // 处理工具调用
              if (delta?.tool_calls) {
                console.log('[useOllama] 检测到工具调用:', delta.tool_calls);

                // 确保 lastMessage 有 tool_calls 属性
                if (!lastMessage.tool_calls) {
                  lastMessage.tool_calls = [];
                }

                // 处理工具调用增量更新
                for (const toolCall of delta.tool_calls) {
                  const existingToolCall = lastMessage.tool_calls.find(
                    (tc) => tc.id === toolCall.id,
                  );

                  if (existingToolCall) {
                    // 更新现有工具调用
                    if (toolCall.function?.arguments) {
                      existingToolCall.function.arguments += toolCall.function.arguments;
                    }
                  } else {
                    // 添加新的工具调用
                    lastMessage.tool_calls.push({
                      id: toolCall.id || `call_${Date.now()}`,
                      type: toolCall.type || 'function',
                      index: lastMessage.tool_calls.length,
                      function: {
                        name: toolCall.function?.name || '',
                        arguments: toolCall.function?.arguments || '',
                      },
                    });
                  }
                }
              }
            } catch (e) {
              console.warn('[useOllama] 解析响应失败:', e);
            }
          }
        }
      }

      // 最终处理完整内容
      if (ollamaContentBuffer) {
        const { content, reasoning_content } = thinkContent(ollamaContentBuffer);
        lastMessage.content = content;
        lastMessage.reasoning_content = reasoning_content;
      }

      // 处理工具调用
      if (lastMessage.tool_calls && lastMessage.tool_calls.length > 0) {
        console.log(`[useOllama] 检测到 ${lastMessage.tool_calls.length} 个工具调用，开始执行`);

        for (const toolCall of lastMessage.tool_calls) {
          try {
            console.log(`[useOllama] 执行工具: ${toolCall.function.name}`);
            console.log(`[useOllama] 工具参数:`, toolCall.function.arguments);

            // 解析工具参数
            let toolArgs: Record<string, unknown>;
            try {
              toolArgs = JSON.parse(toolCall.function.arguments);
            } catch (parseError) {
              console.error('[useOllama] 工具参数解析失败:', parseError);
              throw new Error(`工具参数格式错误: ${parseError}`);
            }

            // 执行工具
            const toolResult = await executeToolCall(toolCall.function.name, toolArgs);

            // 添加工具结果消息
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify(toolResult),
              tool_call_id: toolCall.id,
            });

            console.log(`[useOllama] 工具 ${toolCall.function.name} 执行完成:`, toolResult);
          } catch (error) {
            console.error(`[useOllama] 工具 ${toolCall.function.name} 执行失败:`, error);

            // 添加错误结果
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify({
                success: false,
                message: $t('src.composeables.useOllama.toolExecutionFailed', {
                  error: error.message,
                }),
                toolName: toolCall.function.name,
              }),
              tool_call_id: toolCall.id,
            });
          }
        }

        // 如果有工具调用，需要再次调用模型处理工具结果
        console.log('[useOllama] 工具执行完成，准备递归调用模型处理结果');

        const recursiveParams: SimplifiedLlmParams = {
          ...params,
          messages: messagesRef.value,
        };
        await sendMessage(recursiveParams, loading);
        return;
      }

      console.log('[useOllama] 消息发送完成');
    } catch (error) {
      console.error('[useOllama] Error sending message:', error);
      messagesRef.value.push({
        role: 'assistant',
        content: $t('src.composeables.useOllama.errorOccurred'),
      });
    } finally {
      loading.value = false;

      await updateConversation(
        conversation.id,
        conversation.title,
        JSON.stringify(messagesRef.value),
        conversation.prompt,
      );
    }
  };

  return {
    ollamaSettings,
    sendMessage,
  };
};

export default useOllama;
