#include "windowapi.h"
#include "mainwindow.h"
#include <QDesktopServices>
#include <QUrl>
#include <QDebug>

WindowApi::WindowApi(MainWindow *mainWindow, QObject *parent)
    : QObject(parent), m_mainWindow(mainWindow)
{
}

void WindowApi::minimizeWindow()
{
    if (m_mainWindow)
        m_mainWindow->minimizeWindow();
}

void WindowApi::toggleMaximizeWindow()
{
    if (m_mainWindow)
        m_mainWindow->toggleMaximizeWindow();
}

void WindowApi::closeWindow()
{
    if (m_mainWindow)
        m_mainWindow->closeWindow();
}

void WindowApi::reloadPage()
{
    if (m_mainWindow)
        m_mainWindow->reloadPage();
}

void WindowApi::openDevTools()
{
    if (m_mainWindow)
        m_mainWindow->openDevTools();
}

void WindowApi::toggleMode()
{
    if (m_mainWindow)
        m_mainWindow->toggleMode();
}

void WindowApi::toggleTheme()
{
    if (m_mainWindow)
        m_mainWindow->toggleTheme();
}

void WindowApi::toggleLeftDrawer()
{
    if (m_mainWindow)
    {
        m_mainWindow->toggleLeftDrawer();
    }
}

void WindowApi::toggleRightDrawer()
{
    if (m_mainWindow)
    {
        m_mainWindow->toggleRightDrawer();
    }
}

void WindowApi::updateLeftDrawerState(bool isOpened)
{
    if (m_mainWindow && m_mainWindow->getQWKTitleBar())
    {
        m_mainWindow->getQWKTitleBar()->updateLeftDrawerState(isOpened);
    }
}

void WindowApi::updateRightDrawerState(bool isOpened)
{
    if (m_mainWindow && m_mainWindow->getQWKTitleBar())
    {
        m_mainWindow->getQWKTitleBar()->updateRightDrawerState(isOpened);
    }
}

void WindowApi::startWindowDrag(int x, int y)
{
    if (m_mainWindow)
        m_mainWindow->startWindowDrag(x, y);
}

void WindowApi::moveWindow(int x, int y)
{
    if (m_mainWindow)
        m_mainWindow->moveWindow(x, y);
}

void WindowApi::endWindowDrag()
{
    if (m_mainWindow)
        m_mainWindow->endWindowDrag();
}

bool WindowApi::hasThemeSetting()
{
    return m_mainWindow ? m_mainWindow->hasThemeSetting() : false;
}

bool WindowApi::getSavedTheme()
{
    return m_mainWindow ? m_mainWindow->getSavedTheme() : false;
}

void WindowApi::saveThemeSetting(bool isDark)
{
    if (m_mainWindow)
        m_mainWindow->saveThemeSetting(isDark);
}

void WindowApi::setThemeFromVue(bool isDark)
{
    if (m_mainWindow)
        m_mainWindow->setThemeFromVue(isDark);
}

void WindowApi::onThemeChangedFromFrontend()
{
    if (m_mainWindow)
        m_mainWindow->onThemeChangedFromFrontend();
}

void WindowApi::setThemeDirectly(bool isDark)
{
    if (m_mainWindow)
        m_mainWindow->setThemeDirectly(isDark);
}

void WindowApi::openExternalUrl(const QString &url)
{
    if (url.isEmpty())
    {
        qWarning() << "[WindowApi] 尝试打开空URL";
        return;
    }

    QUrl qurl(url);
    if (!qurl.isValid())
    {
        qWarning() << "[WindowApi] 无效的URL:" << url;
        return;
    }

    // 确保URL是http或https协议
    if (qurl.scheme() != "http" && qurl.scheme() != "https")
    {
        qWarning() << "[WindowApi] 不支持的URL协议:" << qurl.scheme();
        return;
    }

    qDebug() << "[WindowApi] 在默认浏览器中打开URL:" << url;

    bool success = QDesktopServices::openUrl(qurl);
    if (!success)
    {
        qWarning() << "[WindowApi] 无法打开URL:" << url;
    }
}