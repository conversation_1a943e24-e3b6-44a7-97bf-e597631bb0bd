diff --git a/node_modules/tiptap-markdown/dist/tiptap-markdown.cjs.js b/node_modules/tiptap-markdown/dist/tiptap-markdown.cjs.js
index 1234567..abcdefg 100644
--- a/node_modules/tiptap-markdown/dist/tiptap-markdown.cjs.js
+++ b/node_modules/tiptap-markdown/dist/tiptap-markdown.cjs.js
@@ -1,6 +1,6 @@
     image(state, node) {
-        state.write("![" + state.esc(node.attrs.alt || "") + "](" + node.attrs.src.replace(/[\(\)]/g, "\\$&") +
+        state.write("![" + state.esc(node.attrs.alt || "") + "](" + (node.attrs.src || "").replace(/[\(\)]/g, "\\$&") +
             (node.attrs.title ? ' "' + node.attrs.title.replace(/"/g, '\\"') + '"' : "") + ")");
     },
 
diff --git a/node_modules/tiptap-markdown/dist/tiptap-markdown.esm.js b/node_modules/tiptap-markdown/dist/tiptap-markdown.esm.js
index 1234567..abcdefg 100644
--- a/node_modules/tiptap-markdown/dist/tiptap-markdown.esm.js
+++ b/node_modules/tiptap-markdown/dist/tiptap-markdown.esm.js
@@ -1,6 +1,6 @@
     image(state, node) {
-        state.write("![" + state.esc(node.attrs.alt || "") + "](" + node.attrs.src.replace(/[\(\)]/g, "\\$&") +
+        state.write("![" + state.esc(node.attrs.alt || "") + "](" + (node.attrs.src || "").replace(/[\(\)]/g, "\\$&") +
             (node.attrs.title ? ' "' + node.attrs.title.replace(/"/g, '\\"') + '"' : "") + ")");
     },
