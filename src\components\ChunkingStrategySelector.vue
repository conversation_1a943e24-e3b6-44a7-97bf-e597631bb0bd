<template>
  <div class="chunking-strategy-selector">
    <div class="text-subtitle2 q-mb-sm">
      {{ $t('src.components.ChunkingStrategySelector.title') }}
    </div>
    <div class="text-caption text-grey-6 q-mb-md">
      {{ $t('src.components.ChunkingStrategySelector.description') }}
    </div>

    <!-- 切割方法选择 -->
    <q-select
      v-model="selectedMethod"
      :options="methodOptions"
      :label="$t('src.components.ChunkingStrategySelector.methodLabel')"
      outlined
      dense
      emit-value
      map-options
      class="q-mb-md"
      :loading="loadingMethods"
      @update:model-value="onMethodChange"
    >
      <template v-slot:option="scope">
        <q-item v-bind="scope.itemProps">
          <q-item-section>
            <q-item-label>{{ scope.opt.label }}</q-item-label>
            <q-item-label caption>{{ scope.opt.description }}</q-item-label>
          </q-item-section>
          <q-item-section side v-if="!scope.opt.available">
            <q-chip size="sm" color="grey-5" text-color="white">
              {{ $t('src.components.ChunkingStrategySelector.comingSoon') }}
            </q-chip>
          </q-item-section>
        </q-item>
      </template>
    </q-select>

    <!-- 方法说明 -->
    <q-card v-if="selectedMethodInfo" flat bordered class="q-mb-md">
      <q-card-section class="q-pa-md">
        <div class="text-body2 q-mb-sm">
          <q-icon name="info" color="primary" class="q-mr-xs" />
          {{ selectedMethodInfo.name }}
        </div>
        <div class="text-caption text-grey-7">
          {{ selectedMethodInfo.description }}
        </div>
      </q-card-section>
    </q-card>

    <!-- 配置参数 - 所有策略都使用相同的基本配置 -->
    <div v-if="selectedMethod" class="configuration-section">
      <div class="text-body2 q-mb-sm">
        {{ $t('src.components.ChunkingStrategySelector.configuration') }}
      </div>

      <div class="row q-gutter-md q-mb-md">
        <div class="col-5">
          <q-input
            v-model.number="config.chunkSize"
            type="number"
            :label="$t('src.components.ChunkingStrategySelector.chunkSize')"
            outlined
            dense
            :suffix="$t('src.components.ChunkingStrategySelector.characters')"
            :rules="[
              (val) => val > 0 || $t('src.components.ChunkingStrategySelector.chunkSizeRule1'),
              (val) => val <= 5000 || $t('src.components.ChunkingStrategySelector.chunkSizeRule2'),
            ]"
            @update:model-value="onConfigChange"
          />
        </div>
        <div class="col-5">
          <q-input
            v-model.number="config.chunkOverlap"
            type="number"
            :label="$t('src.components.ChunkingStrategySelector.chunkOverlap')"
            outlined
            dense
            :suffix="$t('src.components.ChunkingStrategySelector.characters')"
            :rules="[
              (val) => val >= 0 || $t('src.components.ChunkingStrategySelector.chunkOverlapRule1'),
              (val) =>
                val < config.chunkSize ||
                $t('src.components.ChunkingStrategySelector.chunkOverlapRule2'),
            ]"
            @update:model-value="onConfigChange"
          />
        </div>
      </div>

      <!-- 根据不同策略显示不同的建议 -->
      <div class="text-caption text-grey-6 q-mb-md">
        <q-icon name="lightbulb" color="amber" class="q-mr-xs" />
        <span v-if="selectedMethod === 'markdown'">
          {{ $t('src.components.ChunkingStrategySelector.markdownSuggestion') }}
        </span>
        <span v-else-if="selectedMethod === 'latex'">
          {{ $t('src.components.ChunkingStrategySelector.latexSuggestion') }}
        </span>
        <span v-else-if="selectedMethod === 'recursiveCharacter'">
          {{ $t('src.components.ChunkingStrategySelector.recursiveCharacterSuggestion') }}
        </span>
        <span v-else-if="selectedMethod === 'smart'">
          {{ $t('src.components.ChunkingStrategySelector.smartSuggestion') }}
        </span>
        <span v-else> {{ $t('src.components.ChunkingStrategySelector.defaultSuggestion') }} </span>
      </div>

      <!-- 显示策略的详细信息 -->
      <div v-if="selectedMethodInfo" class="q-mt-md">
        <q-expansion-item
          icon="info"
          :label="$t('src.components.ChunkingStrategySelector.strategyInfo')"
          dense
          header-class="text-caption"
        >
          <div class="q-pa-md">
            <div v-if="selectedMethodInfo.useCases" class="q-mb-sm">
              <div class="text-caption text-weight-medium q-mb-xs">
                {{ $t('src.components.ChunkingStrategySelector.useCases') }}:
              </div>
              <q-chip
                v-for="useCase in selectedMethodInfo.useCases"
                :key="useCase"
                size="sm"
                color="primary"
                text-color="white"
                class="q-mr-xs q-mb-xs"
              >
                {{ useCase }}
              </q-chip>
            </div>

            <div v-if="selectedMethodInfo.advantages" class="q-mb-sm">
              <div class="text-caption text-weight-medium q-mb-xs">
                {{ $t('src.components.ChunkingStrategySelector.mainAdvantages') }}:
              </div>
              <ul class="text-caption q-ma-none q-pl-md">
                <li v-for="advantage in selectedMethodInfo.advantages" :key="advantage">
                  {{ advantage }}
                </li>
              </ul>
            </div>

            <div v-if="selectedMethodInfo.limitations">
              <div class="text-caption text-weight-medium q-mb-xs">
                {{ $t('src.components.ChunkingStrategySelector.usageRestrictions') }}:
              </div>
              <ul class="text-caption q-ma-none q-pl-md">
                <li v-for="limitation in selectedMethodInfo.limitations" :key="limitation">
                  {{ limitation }}
                </li>
              </ul>
            </div>
          </div>
        </q-expansion-item>
      </div>
    </div>

    <!-- 配置验证状态 -->
    <div v-if="validationResult" class="q-mt-md">
      <q-banner
        :class="
          validationResult.configValid
            ? 'text-positive bg-positive-1'
            : 'text-negative bg-negative-1'
        "
        inline-actions
      >
        <q-icon :name="validationResult.configValid ? 'check_circle' : 'error'" />
        {{ validationResult.message }}
      </q-banner>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { strategies, validateChunkingConfig, type SplitterInfo } from '../utils/knowledgeBase';
import type { ChunkingConfig } from '../types/qwen';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

// 更新 ChunkingMethod 类型以匹配 CHUNKING_STRATEGIES 的 key
type ChunkingMethod = 'markdown' | 'recursiveCharacter' | 'latex' | 'smart';

// 适配器接口，将 SplitterInfo 转换为组件期望的格式
interface ChunkingMethodInfo {
  method: string;
  name: string;
  description: string;
  available: boolean;
  defaultConfig: ChunkingConfig;
  useCases?: string[];
  advantages?: string[];
  limitations?: string[];
  recommendedFor?: string[];
}

// 配置验证结果接口
interface ChunkingConfigValidation {
  success: boolean;
  method: string;
  configValid: boolean;
  config?: ChunkingConfig;
  message: string;
  error?: string;
}

// Props
const props = defineProps<{
  modelValue?: {
    method: ChunkingMethod;
    config: ChunkingConfig;
  };
  disabled?: boolean;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: { method: ChunkingMethod; config: ChunkingConfig }];
  'validation-change': [isValid: boolean];
}>();

// Composables
const $q = useQuasar();

// 响应式数据
const selectedMethod = ref<ChunkingMethod>('markdown');
const config = ref<ChunkingConfig>({
  chunkSize: 800,
  chunkOverlap: 200,
});

const loadingMethods = ref(false);
const availableMethods = ref<ChunkingMethodInfo[]>([]);
const validationResult = ref<ChunkingConfigValidation | null>(null);

// 计算属性
const methodOptions = computed(() => {
  return availableMethods.value.map((method) => ({
    label: method.name,
    value: method.method as ChunkingMethod,
    description: method.description,
    available: method.available,
    // 暂时禁用未实现的方法
    disable: !method.available,
  }));
});

const selectedMethodInfo = computed(() => {
  return availableMethods.value.find((method) => method.method === selectedMethod.value);
});

// 将 SplitterInfo 转换为 ChunkingMethodInfo 的适配器函数
const convertSplitterInfoToMethodInfo = (name: string, info: SplitterInfo): ChunkingMethodInfo => ({
  method: name,
  name: info.displayName,
  description: info.description,
  available: true, // 所有本地策略都是可用的
  defaultConfig: {
    chunkSize: info.defaultConfig.chunkSize,
    chunkOverlap: info.defaultConfig.chunkOverlap,
  },
  useCases: info.useCases,
  advantages: info.advantages,
  limitations: info.limitations,
  recommendedFor: info.recommendedFor,
});

// 方法
const loadAvailableMethods = () => {
  loadingMethods.value = true;
  try {
    // 使用本地的 CHUNKING_STRATEGIES 配置
    console.log('加载本地切割策略配置...');

    availableMethods.value = Object.keys(strategies()).map((key) =>
      convertSplitterInfoToMethodInfo(key, strategies()[key]),
    );

    console.log('成功加载切割策略:', availableMethods.value.length, '个策略');
  } catch (error) {
    console.error('加载切割方法失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.ChunkingStrategySelector.loadFailed'),
    });

    // 提供最基本的默认配置
    availableMethods.value = [
      {
        method: 'markdown',
        name: $t('src.components.ChunkingStrategySelector.markdown'),
        description: $t('src.components.ChunkingStrategySelector.markdownDescription'),
        available: true,
        defaultConfig: { chunkSize: 800, chunkOverlap: 200 },
      },
    ];
  } finally {
    loadingMethods.value = false;
  }
};

const validateConfig = () => {
  try {
    // 使用本地的 validateChunkingConfig 函数
    const validationResult_local = validateChunkingConfig(config.value);

    validationResult.value = {
      success: true,
      method: selectedMethod.value,
      configValid: validationResult_local.isValid,
      config: config.value,
      message: validationResult_local.isValid
        ? $t('src.components.ChunkingStrategySelector.configValid')
        : $t('src.components.ChunkingStrategySelector.configInvalid', {
            errors: validationResult_local.errors.join(', '),
          }),
    };

    emit('validation-change', validationResult_local.isValid);
  } catch (error) {
    console.error('验证配置失败:', error);

    // 降级到简单的本地验证
    const isValid = validateConfigLocally();
    validationResult.value = {
      success: false,
      method: selectedMethod.value,
      configValid: isValid,
      error: $t('src.components.ChunkingStrategySelector.configFailed'),
      message: isValid
        ? $t('src.components.ChunkingStrategySelector.configValid')
        : $t('src.components.ChunkingStrategySelector.configInvalid'),
    };
    emit('validation-change', isValid);
  }
};

const validateConfigLocally = (): boolean => {
  // 所有策略都使用基本的 chunkSize 和 chunkOverlap 验证
  return (
    config.value.chunkSize !== undefined &&
    config.value.chunkSize > 0 &&
    config.value.chunkSize <= 5000 &&
    config.value.chunkOverlap !== undefined &&
    config.value.chunkOverlap >= 0 &&
    config.value.chunkOverlap < config.value.chunkSize
  );
};

const onMethodChange = (method: ChunkingMethod) => {
  selectedMethod.value = method;

  // 设置默认配置
  const methodInfo = availableMethods.value.find((m) => m.method === method);
  if (methodInfo?.defaultConfig) {
    config.value = { ...config.value, ...methodInfo.defaultConfig };
  }

  emitValue();
  validateConfig(); // 同步调用
};

const onConfigChange = () => {
  emitValue();
  validateConfig(); // 同步调用
};

const emitValue = () => {
  emit('update:modelValue', {
    method: selectedMethod.value,
    config: config.value,
  });
};

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      selectedMethod.value = newValue.method;
      config.value = { ...config.value, ...newValue.config };
    }
  },
  { deep: true, immediate: true },
);

// 生命周期
onMounted(() => {
  loadAvailableMethods();

  // 初始化配置
  if (props.modelValue) {
    selectedMethod.value = props.modelValue.method;
    config.value = { ...config.value, ...props.modelValue.config };
  }

  // 初始验证
  validateConfig();

  // 发出初始值
  emitValue();
});
</script>

<style scoped>
.chunking-strategy-selector {
  border-radius: 4px;
}

.configuration-section {
  padding: 12px;
  background-color: var(--q-color-grey-1);
  border-radius: 4px;
}

.q-dark .configuration-section {
  background-color: var(--q-color-grey-9);
}
</style>
