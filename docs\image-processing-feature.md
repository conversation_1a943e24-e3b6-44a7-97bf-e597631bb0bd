# 编辑器图片处理功能完善

## 功能概述

完善了TipTap编辑器的图片处理逻辑，当用户粘贴内容后，编辑器会自动检查DOM中的img标签，如果发现src属性以http开头的图片链接，会自动触发Qt端的图片下载和处理流程。

## 主要改进

### 1. 自动检测HTTP图片链接

- 在编辑器的`onUpdate`回调中添加了`checkAndProcessHttpImages`函数调用
- 自动检测编辑器DOM中所有以`http`开头的img标签
- 使用防抖机制避免频繁触发处理逻辑

### 2. 防抖和去重机制

- 使用500ms防抖延迟，避免编辑器频繁更新时重复处理
- 维护处理队列`processingHttpImages`，避免同一图片被重复处理
- 在组件销毁时清理定时器和处理队列

### 3. 智能图片下载和替换

- 使用Qt后端的`downloadAndSaveImage` API下载网络图片
- 下载成功后自动更新编辑器中的图片节点，将HTTP链接替换为本地引用`image://id`
- 同时更新DOM和编辑器JSON数据结构

### 4. 用户体验优化

- 处理完成后显示成功提示，告知用户有多少图片被保存到本地
- 错误处理机制，单个图片处理失败不会影响其他图片
- 自动保存文档，确保图片引用更新被持久化

## 技术实现细节

### 核心函数

```typescript
const checkAndProcessHttpImages = (currentEditor: Editor) => {
  // 防抖处理
  if (httpImageProcessTimeout) {
    clearTimeout(httpImageProcessTimeout);
  }
  
  httpImageProcessTimeout = setTimeout(async () => {
    // 查找所有HTTP图片
    const imgElements = editorDom.querySelectorAll('img[src^="http"]');
    
    // 逐个处理图片
    for (const imgElement of imgElements) {
      // 下载并替换图片引用
      await downloadAndReplaceImage(imgElement);
    }
  }, 500);
};
```

### 集成点

- 在`onUpdate`回调中调用图片检查函数
- 在`onBeforeUnmount`中清理相关资源
- 复用现有的Qt图片下载API

## 使用场景

1. **粘贴网页内容**：用户从网页复制包含图片的内容粘贴到编辑器时
2. **插入网络图片**：用户通过各种方式插入网络图片链接时
3. **导入外部文档**：导入包含网络图片的文档时

## 优势

- **自动化**：无需用户手动操作，自动处理网络图片
- **透明**：用户感知不到处理过程，体验流畅
- **可靠**：使用Qt后端下载，避免CORS等前端限制
- **高效**：防抖和去重机制避免重复处理

## 注意事项

- 仅在Qt环境中生效，Web环境会自动跳过
- 处理过程中会有网络请求，可能需要一定时间
- 图片下载失败不会影响编辑器正常使用
- 处理完成后会自动保存文档
