# 生产模式工具栏按钮隐藏实现

## 功能概述

在生产模式下隐藏自定义工具栏中的开发专用按钮：
- 重新加载按钮 (F5)
- 开发者工具按钮 (F12)  
- 切换模式按钮 (Ctrl+T)

这些按钮只在开发模式（QT_DEBUG）下显示，在生产模式下完全隐藏，提供更简洁的用户界面。

## 实现方案

### 1. 条件编译控制

使用 `#ifdef QT_DEBUG` 预处理指令来控制按钮的创建：

```cpp
// 开发模式专用按钮 - 只在开发模式下显示
#ifdef QT_DEBUG
    m_reloadBtn = createToolButton(":/icons/refresh.png", "重新加载 (F5)");
    m_reloadBtn->setObjectName("reloadBtn");
    m_layout->addWidget(m_reloadBtn);
    
    m_devToolsBtn = createToolButton(":/icons/dev-tools.png", "开发者工具 (F12)");
    m_devToolsBtn->setObjectName("devToolsBtn");
    m_layout->addWidget(m_devToolsBtn);
    
    m_toggleModeBtn = createToolButton(":/icons/swap.png", "切换模式 (Ctrl+T)");
    m_toggleModeBtn->setObjectName("toggleModeBtn");
    m_layout->addWidget(m_toggleModeBtn);
    
    qDebug() << "开发模式：已添加开发工具按钮";
#else
    // 生产模式下这些按钮为空指针
    m_reloadBtn = nullptr;
    m_devToolsBtn = nullptr;
    m_toggleModeBtn = nullptr;
    qDebug() << "生产模式：隐藏开发工具按钮";
#endif
```

### 2. 信号连接的安全处理

只为存在的按钮设置信号连接：

```cpp
void CustomToolBar::setupConnections()
{
    // 基础按钮连接
    connect(m_leftDrawerBtn, &QPushButton::clicked, this, &CustomToolBar::onLeftDrawerClicked);
    connect(m_rightDrawerBtn, &QPushButton::clicked, this, &CustomToolBar::onRightDrawerClicked);
    connect(m_themeBtn, &QPushButton::clicked, this, &CustomToolBar::onThemeToggleClicked);
    
    // 开发模式专用按钮连接 - 只在按钮存在时连接
    if (m_reloadBtn) {
        connect(m_reloadBtn, &QPushButton::clicked, this, &CustomToolBar::onReloadClicked);
    }
    if (m_devToolsBtn) {
        connect(m_devToolsBtn, &QPushButton::clicked, this, &CustomToolBar::onDevToolsClicked);
    }
    if (m_toggleModeBtn) {
        connect(m_toggleModeBtn, &QPushButton::clicked, this, &CustomToolBar::onModeToggleClicked);
    }
    
    // 窗口控制按钮连接
    connect(m_minimizeBtn, &QPushButton::clicked, this, &CustomToolBar::onMinimizeClicked);
    connect(m_maximizeBtn, &QPushButton::clicked, this, &CustomToolBar::onMaximizeClicked);
    connect(m_closeBtn, &QPushButton::clicked, this, &CustomToolBar::onCloseClicked);
}
```

### 3. 主题更新的安全处理

只为存在的按钮应用主题：

```cpp
void CustomToolBar::updateTheme(bool isDarkTheme)
{
    // 更新工具按钮主题
    applyToolButtonTheme(m_leftDrawerBtn, isDarkTheme);
    applyToolButtonTheme(m_rightDrawerBtn, isDarkTheme);
    applyToolButtonTheme(m_themeBtn, isDarkTheme);
    
    // 只为存在的开发模式按钮应用主题
    if (m_reloadBtn) {
        applyToolButtonTheme(m_reloadBtn, isDarkTheme);
    }
    if (m_devToolsBtn) {
        applyToolButtonTheme(m_devToolsBtn, isDarkTheme);
    }
    if (m_toggleModeBtn) {
        applyToolButtonTheme(m_toggleModeBtn, isDarkTheme);
    }
    
    // ... 其他主题更新代码
}
```

### 4. 信号槽方法的防护

为开发模式专用的信号槽方法添加条件编译防护：

```cpp
void CustomToolBar::onReloadClicked()
{
    #ifdef QT_DEBUG
        emit reloadRequested();
    #else
        qDebug() << "生产模式下重新加载功能不可用";
    #endif
}

void CustomToolBar::onDevToolsClicked()
{
    #ifdef QT_DEBUG
        emit devToolsRequested();
    #else
        qDebug() << "生产模式下开发者工具不可用";
    #endif
}

void CustomToolBar::onModeToggleClicked()
{
    #ifdef QT_DEBUG
        emit modeToggleRequested();
    #else
        qDebug() << "生产模式下模式切换不可用";
    #endif
}
```

### 5. 析构函数的安全清理

只删除存在的按钮：

```cpp
CustomToolBar::~CustomToolBar()
{
    // 删除基础按钮
    if (m_leftDrawerBtn) delete m_leftDrawerBtn;
    if (m_rightDrawerBtn) delete m_rightDrawerBtn;
    if (m_themeBtn) delete m_themeBtn;
    
    // 删除开发模式按钮（如果存在）
    if (m_reloadBtn) delete m_reloadBtn;
    if (m_devToolsBtn) delete m_devToolsBtn;
    if (m_toggleModeBtn) delete m_toggleModeBtn;
    
    // ... 其他清理代码
}
```

## 构建模式区别

### 开发模式 (QT_DEBUG)
- 使用 `./build-qt-dev.sh debug` 构建
- 显示所有工具栏按钮，包括开发工具
- 启用调试功能和远程调试端口
- 控制台输出：`"开发模式：已添加开发工具按钮"`

### 生产模式 (Release)
- 使用 `./build-qt.sh` 构建
- 隐藏开发工具按钮，只显示基础功能按钮
- 禁用调试功能
- 控制台输出：`"生产模式：隐藏开发工具按钮"`

## 保留的按钮

在生产模式下仍然保留的按钮：
- ✅ 左侧抽屉切换按钮
- ✅ 右侧抽屉切换按钮  
- ✅ 主题切换按钮
- ✅ 窗口控制按钮（最小化、最大化、关闭）

## 隐藏的按钮

在生产模式下隐藏的按钮：
- ❌ 重新加载按钮 (F5)
- ❌ 开发者工具按钮 (F12)
- ❌ 切换模式按钮 (Ctrl+T)

## 验证方法

1. **开发模式验证**：
   ```bash
   ./build-qt-dev.sh debug
   # 应该看到所有按钮，包括开发工具按钮
   ```

2. **生产模式验证**：
   ```bash
   ./build-qt.sh
   # 应该只看到基础功能按钮，开发工具按钮被隐藏
   ```

3. **控制台日志验证**：
   - 开发模式：显示 "开发模式：已添加开发工具按钮"
   - 生产模式：显示 "生产模式：隐藏开发工具按钮"

## 优势

1. **用户体验**：生产模式下界面更简洁，避免用户误操作开发功能
2. **安全性**：防止最终用户访问开发者工具和调试功能
3. **性能**：减少不必要的按钮创建和事件处理
4. **维护性**：使用条件编译，代码清晰易维护

这个实现确保了开发和生产环境的明确分离，提供了更专业的用户体验。
