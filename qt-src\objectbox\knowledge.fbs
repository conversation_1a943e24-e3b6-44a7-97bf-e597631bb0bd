namespace InkCop.Knowledge;

// ObjectBox knowledge schema for InkCop
// Knowledge base entity
table KnowledgeBase {
    id: ulong;
    name: string;
    description: string;
    user_id: string;
    created_at: ulong;
    updated_at: ulong;
}

// Knowledge document entity  
table KnowledgeDocument {
    id: ulong;
    kb_id: ulong; // relation to KnowledgeBase
    title: string;
    content: string;
    document_type: string;
    metadata: string; // JSON string
    created_at: ulong;
    updated_at: ulong;
    chunking_method: string; // 切割方法标识 (byChunkSize, bySemantic, etc.)
    chunking_config: string; // 切割配置参数 (JSON string)
}

table KnowledgeChunk {
    id: ulong;
    knowledge_document_id: ulong;
    chunk_index: uint;
    content: string;
    /// objectbox: index=hnsw, hnsw-dimensions=1024
    /// objectbox: hnsw-distance-type=Cosine, hnsw-neighbors-per-node=30, hnsw-indexing-search-count=200
    embedding: [float];  // 向量数据，1024维，使用HNSW索引优化搜索性能
    metadata: string;    // JSON字符串
    created_at: ulong;
    is_vectorized: bool = false;  // 向量化状态标记
}

table KnowledgeQuery {
    id: ulong;
    knowledge_base_id: ulong;
    query_text: string;
    query_embedding: [float];  // 查询向量
    results: string;           // JSON格式的结果
    created_at: ulong;
}
