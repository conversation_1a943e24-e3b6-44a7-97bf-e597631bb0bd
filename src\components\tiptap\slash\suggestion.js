import { VueRenderer } from '@tiptap/vue-3';
import { computed } from 'vue';
import tippy from 'tippy.js';
// import EditorToolbar from '../EditorToolbar.vue';
import CommandsList from './CommandsList.vue';
import useCommands from '../useCommands';

export default {
  items: (props) => {
    const { toolbarItems, getToolbarItemsByType } = useCommands(props.editor);
    // 工具栏按钮顺序数组
    const toolbarOrder = computed(() => {
      return getToolbarItemsByType('slash');
    });
    // 根据顺序数组获取工具栏按钮配置
    const toolbarButtons = computed(() => {
      return toolbarOrder.value.map((i) => {
        return {
          ...i,
          item: i.items.map((key) => toolbarItems[key]),
        };
      });
    });
    return toolbarButtons.value;
  },

  render: () => {
    let component;
    let popup;

    return {
      onStart: (props) => {
        component = new VueRenderer(CommandsList, {
          props: {
            ...props,
            toolbarType: 'slash',
            // 传递 items 和 range 给 EditorToolbar
            slashItems: props.items,
            slashRange: props.range,
            editor: props.editor,
          },
          editor: props.editor,
        });

        if (!props.clientRect) {
          return;
        }

        popup = tippy('body', {
          getReferenceClientRect: props.clientRect,
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: 'manual',
          placement: 'bottom-start',
        });
      },

      onUpdate(props) {
        component.updateProps({
          ...props,
          slashItems: props.items,
          slashRange: props.range,
        });

        if (!props.clientRect) {
          return;
        }

        popup[0].setProps({
          getReferenceClientRect: props.clientRect,
        });
      },

      onKeyDown(props) {
        if (props.event.key === 'Escape') {
          popup[0].hide();
          return true;
        }
        return component.ref?.onKeyDown?.(props);
      },

      onExit() {
        popup[0].destroy();
        component.destroy();
      },
    };
  },
};
