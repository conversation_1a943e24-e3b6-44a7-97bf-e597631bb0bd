interface OperationResult {
  success: boolean;
  answer: string;
}

/**
 * 向用户提问并等待回答
 */
export async function askUser(params: { question: string }): Promise<OperationResult> {
  const { question } = params;

  // 动态导入 LLM store 以避免循环依赖
  const { useLlmStore } = await import('src/stores/llm');
  const llmStore = useLlmStore();

  // 直接调用 store 的方法来设置问题并等待答案
  return llmStore.askUserQuestion(question);
}

// 导出所有用户交互工具函数
export const userTools = {
  askUser,
};

// 用户工具schema定义
export const tools = [
  {
    type: 'function' as const,
    function: {
      name: 'ask_user',
      description: '向用户提问并等待回答。当AI需要更多信息或需要用户确认时使用。',
      parameters: {
        type: 'object',
        properties: {
          question: {
            type: 'string',
            description: '要向用户提出的问题',
          },
        },
        required: ['question'],
      },
    },
  },
];

// 用户工具映射表
export const userToolMappings = {
  ask_user: askUser,
} as const;
