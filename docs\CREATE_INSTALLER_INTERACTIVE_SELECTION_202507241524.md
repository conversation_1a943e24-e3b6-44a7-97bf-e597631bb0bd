# create-installer.ps1 交互式构建选择功能

## 修改日期
2025-07-24 15:24

## 功能概述
为 `create-installer.ps1` 脚本添加了交互式构建产物选择功能，用户现在可以在运行脚本时选择要打包的构建产物，而不是固定使用 `build-installer` 目录。

## 新增功能

### 1. 构建产物检测 (`Get-AvailableBuilds`)
自动检测以下构建目录中的可用构建产物：
- `build-dev\bin\Release\InkCop.exe` - 开发构建
- `build-prod\bin\Release\InkCop.exe` - 生产构建  
- `build\bin\Release\InkCop.exe` - 标准构建
- `build-installer\bin\Release\InkCop.exe` - 安装包专用构建

对每个构建显示：
- 构建名称和描述
- 文件大小（MB）
- 最后修改时间
- 完整路径

### 2. 交互式选择菜单 (`Show-BuildSelectionMenu`)
提供用户友好的选择界面：
- 列出所有可用构建产物
- 显示详细信息（大小、时间、路径）
- 支持数字选择 [1-N]
- 支持 'q' 退出
- 包含确认步骤

### 3. 智能构建流程
根据不同情况提供不同的处理方式：

#### 情况 1：使用 `-SkipBuild` 参数
- 直接显示构建选择菜单
- 要求用户选择现有构建产物
- 如果没有可用构建，显示构建指导

#### 情况 2：不使用 `-SkipBuild` 参数
- **有现有构建时**：提供选择
  - [1] 使用现有构建产物
  - [2] 重新构建应用程序
- **无现有构建时**：自动开始构建

## 用户体验改进

### 1. 清晰的状态显示
```
🔍 检测到以下构建产物：

[1] Development Build (build-dev)
     描述: 开发构建 - 包含调试信息和热重载支持
     大小: 45.2 MB
     修改时间: 2025-07-24 15:20:33
     路径: build-dev\bin\Release\InkCop.exe

[2] Production Build (build-prod)
     描述: 生产构建 - 优化性能，无调试信息
     大小: 42.8 MB
     修改时间: 2025-07-24 14:55:12
     路径: build-prod\bin\Release\InkCop.exe
```

### 2. 友好的错误处理
- 无构建产物时提供构建指导
- 无效选择时给出明确提示
- 支持用户取消操作

### 3. 详细的确认信息
```
📦 准备打包构建产物:
   名称: Production Build (build-prod)
   路径: build-prod\bin\Release\InkCop.exe

✅ 安装包创建成功！
安装包路径: dist-packages\InkCop_1.0.0_x64_Setup.exe
安装包大小: 85.4 MB
源构建产物: Production Build (build-prod)
源构建路径: build-prod\bin\Release\InkCop.exe
```

## 技术实现

### 1. 动态 Inno Setup 脚本生成
修改了 `Create-InnoSetupScript` 函数：
- 接受 `$BuildDirectory` 参数
- 动态设置源文件路径：`Source: "$BuildDirectory\*"`
- 支持任意构建目录

### 2. 构建信息结构
每个构建产物包含以下信息：
```powershell
@{
    Path = "完整的可执行文件路径"
    Directory = "构建目录路径"
    Name = "显示名称"
    Description = "详细描述"
    Size = "文件大小(MB)"
    LastModified = "最后修改时间"
}
```

### 3. 错误处理和验证
- 验证选定构建文件是否存在
- 检查 Inno Setup 是否安装
- 提供详细的错误信息和解决建议

## 使用方法

### 1. 使用现有构建
```powershell
.\create-installer.ps1 -Version "1.0.0" -SkipBuild
```
- 显示所有可用构建
- 用户选择要打包的构建
- 直接创建安装包

### 2. 重新构建（默认）
```powershell
.\create-installer.ps1 -Version "1.0.0"
```
- 检查现有构建
- 用户选择使用现有构建或重新构建
- 根据选择执行相应操作

### 3. 强制重新构建
选择 [2] 重新构建应用程序选项

## 兼容性
- 保持与原有参数的完全兼容
- 支持所有原有的构建配置
- 不影响现有的自动化脚本

## 优势
1. **灵活性**：可以选择任何已有的构建产物
2. **效率**：避免不必要的重复构建
3. **透明性**：清楚显示正在使用的构建版本
4. **用户友好**：直观的选择界面和详细信息
5. **错误预防**：在打包前验证构建产物的存在

这个功能大大提升了安装包创建的灵活性和用户体验，让开发者可以更精确地控制要分发的版本。
