# create-installer.ps1 发行版构建脚本优化

## 修改日期
2025-07-24 15:24

## 功能概述
将 `create-installer.ps1` 脚本优化为专门用于构建发行版本的安装包创建工具，移除了复杂的构建选择功能，专注于创建生产就绪的安装包。

## 主要改进

### 1. 简化构建流程
- **移除复杂的构建选择菜单**：不再扫描多个构建目录
- **专注发行版构建**：只处理 `build-installer` 目录中的发行版本
- **统一构建目标**：所有构建都针对安装包分发进行优化

### 2. 发行版环境配置
- **自动使用 `.env.release` 配置**：确保不包含 API 密钥的干净发行版
- **生产优化构建**：使用 Release 模式和性能优化设置
- **完整依赖库复制**：包含所有必要的 DLL 和资源文件

### 3. 智能构建检测
新增 `Get-ReleaseBuildsStatus` 函数：
- 检查 `build-installer\bin\Release\InkCop.exe` 是否存在
- 返回文件大小、修改时间等详细信息
- 提供构建状态的完整报告

## 使用方式

### 1. 完整构建模式（推荐）
```powershell
.\create-installer.ps1 -Version "1.0.0"
```

**执行流程**：
1. 设置发行版环境（使用 `.env.release`）
2. 构建 Quasar 前端（生产模式）
3. 配置 Qt 6.7.3 MSVC 环境
4. 构建 Qt 应用程序（Release 模式）
5. 部署 Qt 库和依赖
6. 复制 ObjectBox、llama.cpp、QWindowKit DLL
7. 创建 Inno Setup 安装包

### 2. 跳过构建模式
```powershell
.\create-installer.ps1 -Version "1.0.0" -SkipBuild
```

**执行流程**：
1. 检查现有发行版构建
2. 显示构建信息（大小、时间）
3. 用户确认使用现有构建
4. 直接创建安装包

## 构建输出

### 成功构建示例
```
======================================
InkCop Inno Setup Installer Builder
======================================
Version: 1.0.0
Skip Build: False

======================================
Preparing Release Build for Installer
======================================
Building fresh release version for installer...

Step 0: Setting up release environment...
Release .env configuration applied!

Step 1: Building frontend...
Frontend build completed!

Step 2: Setting up Qt 6.7.3 MSVC environment...
Qt environment configured!

Step 4: Building Qt application...
Build completed successfully!

Release build completed successfully!
  Executable: build-installer\bin\Release\InkCop.exe
  Size: 45.2 MB

Creating Inno Setup script...
Compiling installer...

======================================
Installer Created Successfully!
======================================
Installer Path: dist-packages\InkCop_1.0.0_x64_Setup.exe
Installer Size: 154.13 MB
Source Build: Fresh Release Build
Source Path: build-installer\bin\Release\InkCop.exe

Installer is ready for distribution!
```

## 技术特性

### 1. 发行版环境配置
- **环境文件**：自动使用 `.env.release`（不包含 API 密钥）
- **前端构建**：`quasar build` 生产模式，移除调试代码
- **Qt 构建**：Release 模式，性能优化

### 2. 完整依赖管理
- **Qt 库**：通过 `windeployqt` 自动部署
- **ObjectBox**：数据库引擎 DLL
- **llama.cpp**：本地 GGUF 模型支持（如果存在）
- **QWindowKit**：现代窗口样式支持

### 3. 安装包特性
- **Inno Setup 6**：现代安装程序创建工具
- **x64 架构**：64位 Windows 支持
- **自动卸载**：完整的卸载支持
- **桌面图标**：可选的桌面快捷方式

## 文件结构

### 构建目录
```
build-installer/
├── bin/
│   └── Release/
│       ├── InkCop.exe          # 主程序
│       ├── objectbox.dll       # 数据库引擎
│       ├── Qt6*.dll            # Qt 库文件
│       ├── ggml*.dll           # llama.cpp 库（可选）
│       ├── QWindowKit*.dll     # 窗口库（可选）
│       └── ...                 # 其他依赖文件
```

### 输出目录
```
dist-packages/
└── InkCop_1.0.0_x64_Setup.exe  # 最终安装包
```

## 错误处理

### 常见问题和解决方案

1. **Inno Setup 未安装**
   ```
   Error: Inno Setup not found!
   ```
   - 从 https://jrsoftware.org/isinfo.php 下载安装

2. **Qt 环境未配置**
   ```
   Error: Qt installation not found at: C:\Qt\6.7.3\msvc2022_64
   ```
   - 安装 Qt 6.7.3 MSVC 版本

3. **前端构建失败**
   ```
   Frontend build failed!
   ```
   - 检查 Node.js 和 bun 是否正确安装
   - 运行 `bun install` 安装依赖

4. **构建产物不存在**
   ```
   No release build found at: build-installer\bin\Release\InkCop.exe
   ```
   - 不使用 `-SkipBuild` 参数重新构建

## 性能优化

- **编译时间**：约 2-3 分钟（取决于硬件）
- **安装包大小**：约 150-200 MB（包含所有依赖）
- **构建模式**：Release 优化，最佳性能
- **依赖打包**：只包含必要的运行时库

## 总结

优化后的 `create-installer.ps1` 脚本现在是一个专业的发行版构建工具：

✅ **简化操作**：一键构建完整安装包
✅ **生产就绪**：使用发行版配置和优化
✅ **完整依赖**：自动处理所有必要的库文件
✅ **专业安装包**：使用 Inno Setup 创建标准 Windows 安装程序
✅ **错误处理**：详细的状态报告和错误提示

这个脚本现在可以可靠地为 InkCop 应用程序创建生产级别的安装包，适合分发给最终用户。
