{"permissions": {"allow": ["Bash(rg:*)", "Bash(grep:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(npx tsc:*)", "Bash(ls:*)", "Bash(timeout 10 npm run build)", "Bash(timeout 30 npm run build:*)", "<PERSON><PERSON>(timeout 10 npm run dev)", "Bash(cmake:*)", "<PERSON><PERSON>(.win-dev.ps1)", "Bash(powershell.exe:*)", "<PERSON><PERSON>(dir:*)", "<PERSON><PERSON>(windeployqt:*)", "Bash(\"C:\\Qt\\6.7.3\\msvc2022_64\\bin\\windeployqt.exe\" \"bin\\Release\\InkCop.exe\" --webenginewidgets --webenginecore --webchannel --force)", "Bash(copy \"third-party\\objectbox-windows\\lib\\objectbox.dll\" \"build-dev\\bin\\Release\"\")", "Bash(copy \"third-party\\objectbox-windows\\lib\\objectbox.dll\" \"build-dev\\bin\\Release\\\")", "Bash(cp \"third-party/objectbox-windows/lib/objectbox.dll\" \"build-dev/bin/Release/\")", "Bash(\"C:\\Users\\<USER>\\www\\inkcop\\build-dev\\bin\\Release\\InkCop.exe\" --version)", "Bash(find:*)", "Bash(rm:*)", "WebFetch(domain:help.aliyun.com)", "<PERSON><PERSON>(timeout:*)", "Bash(git config:*)", "Bash(cp:*)", "Bash(bun run lint:*)", "Bash(npx eslint:*)", "Bash(cmd /c \"bun run lint\")", "Bash(bun x eslint:*)", "Bash(bun run:*)", "<PERSON><PERSON>(powershell:*)", "Bash(npx vue-tsc:*)", "Bash(cmd /c:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(if not exist build-debug mkdir build-debug)", "WebFetch(domain:platform.moonshot.cn)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:platform.openai.com)", "WebFetch(domain:ai.google.dev)", "WebFetch(domain:docs.x.ai)", "WebFetch(domain:latenode.com)", "WebFetch(domain:apidog.com)", "WebFetch(domain:open.bigmodel.cn)"], "deny": []}}