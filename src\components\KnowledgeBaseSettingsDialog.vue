<template>
  <q-card v-if="visible" flat square class="column no-wrap transparent fit">
    <!-- 标题栏 -->
    <q-card-section class="row items-center q-pb-none">
      <div class="text-h6">
        {{
          isEditing
            ? $t('src.components.KnowledgeBaseSettingsDialog.edit_knowledge_base')
            : $t('src.components.KnowledgeBaseSettingsDialog.create_knowledge_base')
        }}
      </div>
      <q-space />
      <q-btn icon="close" flat dense size="sm" :disable="loading" @click="close()" />
    </q-card-section>

    <!-- 表单内容 -->
    <q-card-section class="col scroll-y">
      <q-form
        ref="formRef"
        @submit="handleSave"
        @validation-error="onValidationError"
        class="q-gutter-md"
      >
        <!-- 知识库名称 -->
        <q-input
          v-model="formData.name"
          :label="$t('src.components.KnowledgeBaseSettingsDialog.knowledge_base_name') + ' *'"
          outlined
          dense
          maxlength="100"
          :rules="nameRules"
          :disable="loading"
          :hint="$t('src.components.KnowledgeBaseSettingsDialog.please_input_knowledge_base_name')"
        >
          <template #prepend>
            <q-icon name="storage" />
          </template>
        </q-input>

        <!-- 描述 -->
        <q-input
          v-model="formData.description"
          :label="$t('src.components.KnowledgeBaseSettingsDialog.description')"
          outlined
          dense
          type="textarea"
          rows="3"
          maxlength="500"
          :disable="loading"
          :hint="$t('src.components.KnowledgeBaseSettingsDialog.optional_description_hint')"
        >
          <template #prepend>
            <q-icon name="description" />
          </template>
        </q-input>

        <!-- 设置 -->
        <div class="column gap-sm">
          <div class="row items-center gap-sm">
            <q-icon name="settings" />
            {{ $t('src.components.KnowledgeBaseSettingsDialog.advanced_settings') }}
          </div>
          <div class="q-gutter-md">
            <!-- 分块策略 -->
            <q-select
              v-model="visualSettings.chunkStrategy"
              :options="chunkStrategyOptions"
              :label="$t('src.components.KnowledgeBaseSettingsDialog.chunking_strategy')"
              outlined
              dense
              emit-value
              map-options
              :disable="loading"
              :hint="$t('src.components.KnowledgeBaseSettingsDialog.chunking_strategy_hint')"
            >
              <template #prepend>
                <q-icon name="view_module" />
              </template>
            </q-select>

            <!-- 分块大小 -->
            <q-input
              v-model.number="visualSettings.chunkSize"
              :label="$t('src.components.KnowledgeBaseSettingsDialog.chunk_size')"
              outlined
              dense
              type="number"
              min="100"
              max="4000"
              :disable="loading"
              :hint="$t('src.components.KnowledgeBaseSettingsDialog.chunk_size_hint')"
            >
              <template #prepend>
                <q-icon name="text_fields" />
              </template>
            </q-input>

            <!-- 分块重叠 -->
            <q-input
              v-model.number="visualSettings.chunkOverlap"
              :label="$t('src.components.KnowledgeBaseSettingsDialog.chunk_overlap')"
              outlined
              dense
              type="number"
              min="0"
              :max="Math.floor(visualSettings.chunkSize * 0.5)"
              :disable="loading"
              :hint="$t('src.components.KnowledgeBaseSettingsDialog.chunk_overlap_hint')"
            >
              <template #prepend>
                <q-icon name="compare_arrows" />
              </template>
            </q-input>

            <!-- 相似度阈值 -->
            <q-input
              v-model.number="visualSettings.similarityThreshold"
              :label="$t('src.components.KnowledgeBaseSettingsDialog.similarity_threshold')"
              outlined
              dense
              type="number"
              min="0"
              max="1"
              step="0.1"
              :disable="loading"
              :hint="$t('src.components.KnowledgeBaseSettingsDialog.similarity_threshold_hint')"
            >
              <template #prepend>
                <q-icon name="tune" />
              </template>
            </q-input>

            <!-- 最大检索结果数 -->
            <q-input
              v-model.number="visualSettings.maxResults"
              :label="$t('src.components.KnowledgeBaseSettingsDialog.max_search_results')"
              outlined
              dense
              type="number"
              min="1"
              max="50"
              :disable="loading"
              :hint="$t('src.components.KnowledgeBaseSettingsDialog.max_search_results_hint')"
            >
              <template #prepend>
                <q-icon name="format_list_numbered" />
              </template>
            </q-input>
          </div>
        </div>
      </q-form>
    </q-card-section>

    <!-- 操作按钮 -->
    <q-card-actions align="right">
      <q-btn
        flat
        dense
        :label="$t('src.components.KnowledgeBaseSettingsDialog.cancel')"
        :disable="loading"
        @click="close()"
      />
      <q-btn
        color="primary"
        :label="$t('src.components.KnowledgeBaseSettingsDialog.confirm')"
        type="submit"
        dense
        :loading="loading"
        @click="handleSave"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import type { KnowledgeBase } from '../env';
// import type { KnowledgeBaseSettings } from '../types/qwen';
import { strategies } from '../utils/knowledgeBase';
import { useUiStore } from '../stores/ui';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

interface Props {
  modelValue: boolean;
  knowledgeBase?: KnowledgeBase | null;
  loading?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'save', data: { name: string; description: string; settings: string }): void;
  (e: 'close'): void;
}

interface VisualSettings {
  chunkStrategy: string;
  chunkSize?: number;
  chunkOverlap?: number;
  similarityThreshold?: number;
  maxResults?: number;
}

const props = withDefaults(defineProps<Props>(), {
  knowledgeBase: null,
  loading: false,
});

const emit = defineEmits<Emits>();
const close = () => {
  emit('close');
};

const $q = useQuasar();
const store = useUiStore();

// 响应式数据
const formRef = ref();
const settingsTab = ref('visual');

// 表单数据
const formData = ref({
  name: '',
  description: '',
  settings: '',
});

const kbSettings = computed(() => store.perferences?.knowledgeBase);

// 组件默认设置值 - 确保所有地方使用一致的默认值
const DEFAULT_VISUAL_SETTINGS: VisualSettings = {
  chunkStrategy: kbSettings.value?.defaultChunkStrategy || 'markdown',
  chunkSize: kbSettings.value?.chunkSize || 1000,
  chunkOverlap: kbSettings.value?.chunkOverlap || 200,
  similarityThreshold: kbSettings.value?.semanticThreshold || 0.7,
  maxResults: kbSettings.value?.searchLimit || 5,
};

// 可视化设置
const visualSettings = ref<VisualSettings>({ ...DEFAULT_VISUAL_SETTINGS });

// 从 CHUNKING_STRATEGIES 获取分块策略选项
const chunkStrategyOptions = computed(() => {
  return Object.entries(strategies()).map(([key, strategy]) => ({
    label: strategy.displayName,
    value: key,
  }));
});

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const isEditing = computed(() => !!props.knowledgeBase?.id);

// 验证规则
const nameRules = [
  (val: string) =>
    !!val?.trim() ||
    $t('src.components.KnowledgeBaseSettingsDialog.please_input_knowledge_base_name'),
  (val: string) =>
    (val?.trim().length || 0) >= 2 ||
    $t('src.components.KnowledgeBaseSettingsDialog.knowledge_base_name_at_least_2_characters'),
  (val: string) =>
    (val?.trim().length || 0) <= 100 ||
    $t(
      'src.components.KnowledgeBaseSettingsDialog.knowledge_base_name_cannot_exceed_100_characters',
    ),
];

// 方法
const getDefaultSettings = (): VisualSettings => {
  // 使用组件内部定义的默认值，确保一致性
  return { ...DEFAULT_VISUAL_SETTINGS };
};

const syncVisualToJson = () => {
  try {
    const settings = { ...visualSettings.value };
    formData.value.settings = JSON.stringify(settings, null, 2);
  } catch (error) {
    console.warn('同步可视化设置到 JSON 失败:', error);
  }
};

const syncJsonToVisual = () => {
  try {
    if (!formData.value.settings?.trim()) {
      // 如果 JSON 为空，使用默认值
      visualSettings.value = { ...getDefaultSettings() };
      return;
    }

    const parsed = JSON.parse(formData.value.settings);
    visualSettings.value = {
      ...getDefaultSettings(),
      ...parsed,
    };
  } catch (error) {
    console.warn('同步 JSON 到可视化设置失败:', error);
    // JSON 解析失败时，保持当前可视化设置不变
  }
};

const initializeFormData = () => {
  if (props.knowledgeBase) {
    // 编辑模式
    formData.value.name = props.knowledgeBase.name || '';
    formData.value.description = props.knowledgeBase.description || '';

    try {
      const settings = props.knowledgeBase.settings ? JSON.parse(props.knowledgeBase.settings) : {};
      // 编辑模式：使用当前组件的默认值作为基础，然后合并现有设置
      visualSettings.value = {
        ...getDefaultSettings(),
        ...settings,
      };
      syncVisualToJson();
    } catch (error) {
      console.warn('解析知识库设置失败，使用默认设置:', error);
      visualSettings.value = { ...getDefaultSettings() };
      syncVisualToJson();
    }
  } else {
    // 创建模式：使用当前组件定义的默认值
    formData.value.name = '';
    formData.value.description = '';
    visualSettings.value = { ...getDefaultSettings() };
    syncVisualToJson();
  }
};

const onValidationError = () => {
  $q.notify({
    type: 'negative',
    message: $t('src.components.KnowledgeBaseSettingsDialog.please_check_form_errors'),
  });
};

const handleSave = async () => {
  if (!formRef.value) return;

  const isValid = await formRef.value.validate();
  if (!isValid) return;

  try {
    // 如果当前在可视化编辑模式，同步到 JSON
    if (settingsTab.value === 'visual') {
      syncVisualToJson();
    }

    // 验证最终的 JSON
    let finalSettings = formData.value.settings;
    if (finalSettings?.trim()) {
      try {
        JSON.parse(finalSettings); // 验证 JSON 格式
      } catch {
        $q.notify({
          type: 'negative',
          message: $t('src.components.KnowledgeBaseSettingsDialog.invalid_json_format'),
        });
        return;
      }
    } else {
      finalSettings = '{}'; // 空值时使用空对象
    }

    emit('save', {
      name: formData.value.name.trim(),
      description: formData.value.description.trim(),
      settings: finalSettings,
    });
  } catch (error) {
    console.error('保存知识库失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.KnowledgeBaseSettingsDialog.save_failed'),
    });
  }
};

// 监听器
watch(
  () => props.knowledgeBase,
  () => {
    if (visible.value) {
      initializeFormData();
    }
  },
  { immediate: true },
);

watch(visible, (newValue) => {
  if (newValue) {
    initializeFormData();
    settingsTab.value = 'visual'; // 重置到可视化编辑
  }
});

// 监听可视化设置变化，自动同步到 JSON
watch(
  visualSettings,
  () => {
    if (settingsTab.value === 'visual') {
      syncVisualToJson();
    }
  },
  { deep: true },
);

// 监听 JSON 变化，当用户在 JSON 编辑模式时自动同步到可视化
watch(
  () => formData.value.settings,
  () => {
    if (settingsTab.value === 'json') {
      syncJsonToVisual();
    }
  },
);

// 监听标签页切换
watch(settingsTab, (newTab) => {
  if (newTab === 'visual') {
    syncJsonToVisual();
  } else if (newTab === 'json') {
    syncVisualToJson();
  }
});

// 生命周期
onMounted(() => {
  if (visible.value) {
    initializeFormData();
  }
});
</script>

<style scoped>
.q-tab-panel {
  padding: 0;
}

.q-card {
  min-height: 600px;
}

:deep(.q-field__control) {
  border-radius: 4px;
}

:deep(.q-textarea .q-field__control) {
  min-height: 100px;
}
</style>
