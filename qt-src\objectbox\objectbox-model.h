// Code generated by ObjectBox; DO NOT EDIT.

#pragma once

#ifdef __cplusplus
#include <cstdbool>
#include <cstdint>
extern "C" {
#else
#include <stdbool.h>
#include <stdint.h>
#endif
#include "objectbox.h"

/// Initializes an ObjectBox model for all entities. 
/// The returned pointer may be NULL if the allocation failed. If the returned model is not NULL, you should check if   
/// any error occurred by calling obx_model_error_code() and/or obx_model_error_message(). If an error occurred, you're
/// responsible for freeing the resources by calling obx_model_free().
/// In case there was no error when setting the model up (i.e. obx_model_error_code() returned 0), you may configure 
/// OBX_store_options with the model by calling obx_opt_model() and subsequently opening a store with obx_store_open().
/// As soon as you call obx_store_open(), the model pointer is consumed and MUST NOT be freed manually.
static inline OBX_model* create_obx_model() {
    OBX_model* model = obx_model();
    if (!model) return NULL;
    
    obx_model_entity(model, "KnowledgeBase", 1, 3949294300218591452);
    obx_model_property(model, "id", OBXPropertyType_Long, 1, 7531008119805268794);
    obx_model_property_flags(model, OBXPropertyFlags_ID);
    obx_model_property(model, "name", OBXPropertyType_String, 2, 21934135692226650);
    obx_model_property(model, "description", OBXPropertyType_String, 3, 451418701539114460);
    obx_model_property(model, "user_id", OBXPropertyType_String, 4, 6158363564014332978);
    obx_model_property(model, "created_at", OBXPropertyType_Long, 5, 3523244650214261479);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_property(model, "updated_at", OBXPropertyType_Long, 6, 709534172512370542);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_entity_last_property_id(model, 6, 709534172512370542);
    
    obx_model_entity(model, "KnowledgeChunk", 2, 8795075947832050951);
    obx_model_property(model, "id", OBXPropertyType_Long, 1, 5937032808035608278);
    obx_model_property_flags(model, OBXPropertyFlags_ID);
    obx_model_property(model, "knowledge_document_id", OBXPropertyType_Long, 2, 6310919275765138032);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_property(model, "chunk_index", OBXPropertyType_Int, 3, 4980646911408025986);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_property(model, "content", OBXPropertyType_String, 4, 7325986613359822732);
    obx_model_property(model, "embedding", OBXPropertyType_FloatVector, 5, 3856835680003592994);
    obx_model_property(model, "metadata", OBXPropertyType_String, 6, 8998231822202423138);
    obx_model_property(model, "created_at", OBXPropertyType_Long, 7, 4343717854730353370);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_entity_last_property_id(model, 7, 4343717854730353370);
    
    obx_model_entity(model, "KnowledgeDocument", 3, 385499215144046332);
    obx_model_property(model, "id", OBXPropertyType_Long, 1, 8333729431925314847);
    obx_model_property_flags(model, OBXPropertyFlags_ID);
    obx_model_property(model, "kb_id", OBXPropertyType_Long, 2, 6846311570408068073);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_property(model, "title", OBXPropertyType_String, 3, 7192336042927921508);
    obx_model_property(model, "content", OBXPropertyType_String, 4, 6608566304664173243);
    obx_model_property(model, "document_type", OBXPropertyType_String, 5, 2919313615578495158);
    obx_model_property(model, "metadata", OBXPropertyType_String, 6, 8750254811109259188);
    obx_model_property(model, "created_at", OBXPropertyType_Long, 7, 7160933384511857749);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_property(model, "updated_at", OBXPropertyType_Long, 8, 5026428457556335999);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_entity_last_property_id(model, 8, 5026428457556335999);
    
    obx_model_entity(model, "KnowledgeQuery", 4, 4974203339306359292);
    obx_model_property(model, "id", OBXPropertyType_Long, 1, 4390612496884281821);
    obx_model_property_flags(model, OBXPropertyFlags_ID);
    obx_model_property(model, "knowledge_base_id", OBXPropertyType_Long, 2, 4466017119370622106);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_property(model, "query_text", OBXPropertyType_String, 3, 8263901171964607509);
    obx_model_property(model, "query_embedding", OBXPropertyType_FloatVector, 4, 8196151273929672341);
    obx_model_property(model, "results", OBXPropertyType_String, 5, 8398478495510852213);
    obx_model_property(model, "created_at", OBXPropertyType_Long, 6, 4576182974888155207);
    obx_model_property_flags(model, OBXPropertyFlags_UNSIGNED);
    obx_model_entity_last_property_id(model, 6, 4576182974888155207);
    
    obx_model_last_entity_id(model, 4, 4974203339306359292);
    return model; // NOTE: the returned model will contain error information if an error occurred.
}

#ifdef __cplusplus
}
#endif
