# 性能配置系统使用指南

## 概述

所有性能相关的配置已经统一到 `src/config/performance.config.ts` 文件中。这确保了配置的一致性，并且可以根据开发/生产环境自动调整行为。

## 主要特性

### 1. 环境检测
- 自动检测当前环境（开发/生产）
- 生产环境下自动禁用性能监控、详细日志等功能
- 开发环境下启用所有调试功能

### 2. 内存管理配置
- **桌面应用阈值**：
  - 警告：512MB
  - 临界：1GB  
  - 紧急：2GB
- **自动清理**：默认启用
- **内存泄漏检测**：仅开发环境

### 3. 性能监控
- 开发环境：5秒监控间隔
- 生产环境：30秒监控间隔（如果启用）
- FPS追踪、操作追踪仅在开发环境可用

## 使用方法

### 1. 使用默认配置

```typescript
import { useMemoryManager } from 'src/composables/MemoryManager'

// 直接使用，会自动应用默认配置
const memoryManager = useMemoryManager()
```

### 2. 覆盖特定配置

```typescript
import { useMemoryManager } from 'src/composables/MemoryManager'

// 只覆盖需要的配置项
const memoryManager = useMemoryManager({
  monitorInterval: 10000, // 10秒而不是默认的5秒
})
```

### 3. 检查环境

```typescript
import { isDevelopment } from 'src/config/performance.config'

if (isDevelopment) {
  console.log('开发环境特有功能')
}
```

### 4. 使用性能优化层

```typescript
import { usePerformanceOptimization } from 'src/composables/PerformanceOptimizationLayer'

const perfLayer = usePerformanceOptimization()

// 初始化时会使用统一配置
perfLayer.initialize({
  // 可以覆盖特定配置
  maxEditorInstances: 30
})
```

## 配置项说明

### 内存管理
- `MEMORY_THRESHOLDS`: 内存阈值配置
- `MEMORY_MONITOR`: 内存监控配置
- `MEMORY_LEAK_DETECTION`: 内存泄漏检测配置
- `CLEANUP_STRATEGIES`: 资源清理策略

### 性能监控
- `PERFORMANCE_MONITOR`: 性能监控配置
- `VIRTUAL_RENDERING`: 虚拟渲染配置

### 编辑器
- `EDITOR_POOL`: 编辑器实例池配置
- `STATE_MANAGEMENT`: 状态管理配置

### UI交互
- `COMMAND_SYSTEM`: 命令系统配置
- `DRAG_SYSTEM`: 拖拽系统配置

### 调试
- `LOGGING`: 日志配置
- `DEBUG_TOOLS`: 调试工具配置

## 测试配置

在控制台运行以下命令查看当前配置：

```javascript
testPerformanceConfig()
```

这会显示：
- 当前环境
- 所有配置值
- 功能开关状态
- 监控间隔设置

## 生产环境优化

在生产环境中，以下功能会自动禁用：
1. 内存泄漏检测
2. 详细性能监控
3. FPS追踪
4. 操作日志
5. 调试工具

这确保了生产环境的最佳性能，同时在开发环境中保留了完整的调试能力。

## 修改配置

如需修改默认配置，请编辑 `src/config/performance.config.ts` 文件。修改后会自动应用到所有使用该配置的组件。

注意：修改配置前请确保理解各项配置的含义，不当的配置可能会影响应用性能。