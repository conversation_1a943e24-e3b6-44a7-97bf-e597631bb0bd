# 抽屉图标更新说明

## 概述
本次更新为左右抽屉按钮添加了新的SVG图标，支持明暗主题和开启/关闭状态的动态切换。

## 新增文件

### SVG图标文件
在 `public/icons/ui/` 目录下新增了以下图标文件：

#### 左侧抽屉图标
- `left_drawer_closed.svg` - 自适应主题版本（关闭状态）
- `left_drawer_opened.svg` - 自适应主题版本（打开状态）
- `left_drawer_closed_light.svg` - 浅色主题版本（关闭状态）
- `left_drawer_closed_dark.svg` - 深色主题版本（关闭状态）
- `left_drawer_opened_light.svg` - 浅色主题版本（打开状态）
- `left_drawer_opened_dark.svg` - 深色主题版本（打开状态）

#### 右侧抽屉图标
- `right_drawer_closed.svg` - 自适应主题版本（关闭状态）
- `right_drawer_opened.svg` - 自适应主题版本（打开状态）
- `right_drawer_closed_light.svg` - 浅色主题版本（关闭状态）
- `right_drawer_closed_dark.svg` - 深色主题版本（关闭状态）
- `right_drawer_opened_light.svg` - 浅色主题版本（打开状态）
- `right_drawer_opened_dark.svg` - 深色主题版本（打开状态）

### 测试文件
- `test_drawer_icons.js` - JavaScript测试脚本，用于验证图标切换功能

## 修改的文件

### Qt C++ 代码

#### `qt-src/qwkcustomtitlebar.h`
- 添加了抽屉状态变量：`m_isLeftDrawerOpened`, `m_isRightDrawerOpened`
- 添加了状态更新方法：`updateLeftDrawerState()`, `updateRightDrawerState()`
- 添加了图标更新方法：`updateDrawerButtonIcons()`

#### `qt-src/qwkcustomtitlebar.cpp`
- 更新构造函数以初始化抽屉状态变量
- 实现了 `updateDrawerButtonIcons()` 方法，根据主题和状态动态选择图标
- 修改了 `createToolButton()` 方法，移除了旧的抽屉图标处理逻辑
- 在 `applyTheme()` 中添加了抽屉图标更新调用

#### `qt-src/mainwindow.h`
- 添加了 `getQWKTitleBar()` 方法以供外部访问标题栏对象

#### `qt-src/mainwindow.cpp`
- 修改了 `toggleLeftDrawer()` 和 `toggleRightDrawer()` 方法，添加了状态同步逻辑

#### `qt-src/windowapi.h`
- 添加了 `updateLeftDrawerState()` 和 `updateRightDrawerState()` 方法声明

#### `qt-src/windowapi.cpp`
- 实现了抽屉状态更新方法，将前端状态变化传递给标题栏

### 资源文件

#### `webapp.qrc`
- 添加了所有新的抽屉图标文件到Qt资源系统中

## 功能特性

### 1. 主题适配
- 支持浅色和深色主题
- 自适应版本可根据系统主题偏好自动切换
- 专用版本可手动选择使用

### 2. 状态感知
- 图标会根据抽屉的开启/关闭状态动态切换
- 左右抽屉独立管理状态

### 3. 图标设计
- 右侧抽屉图标是左侧图标的水平翻转版本
- 使用SVG格式，支持高分辨率显示
- 颜色方案：浅色主题使用深色图标（#2c2c2c），深色主题使用浅色图标（#e0e0e0）

## 使用方法

### 在Qt中更新抽屉状态
```cpp
// 更新左侧抽屉状态
titleBar->updateLeftDrawerState(true);  // 打开
titleBar->updateLeftDrawerState(false); // 关闭

// 更新右侧抽屉状态
titleBar->updateRightDrawerState(true);  // 打开
titleBar->updateRightDrawerState(false); // 关闭
```

### 在JavaScript中更新抽屉状态
```javascript
// 通过qtBridge更新状态
window.qtBridge.updateLeftDrawerState(true);
window.qtBridge.updateRightDrawerState(false);
```

### 测试功能
在浏览器控制台中运行：
```javascript
// 加载测试脚本后运行
testDrawerIcons();
```

## 技术实现

### SVG变换
右侧抽屉图标使用SVG变换实现水平翻转：
```svg
<g transform="scale(-1,1) translate(-1024,0)">
  <!-- 原始路径 -->
</g>
```

### 主题切换
使用CSS媒体查询实现自适应主题：
```css
@media (prefers-color-scheme: dark) {
  .theme-adaptive { fill: #e0e0e0; }
}
@media (prefers-color-scheme: light) {
  .theme-adaptive { fill: #2c2c2c; }
}
```

### 状态同步
前端抽屉状态变化时，通过JavaScript调用Qt方法同步状态：
```javascript
if (window.qtBridge && window.qtBridge.updateLeftDrawerState) {
  window.qtBridge.updateLeftDrawerState(window.uiStore.leftDrawerOpen);
}
```

## 注意事项

1. 确保在构建前运行前端构建命令，以便新的图标文件被包含在资源中
2. 图标文件路径使用Qt资源系统格式：`:/icons/ui/filename.svg`
3. 状态更新需要前端和后端的配合，确保JavaScript桥接正常工作
4. 如果图标文件不存在，会回退到Unicode符号显示
