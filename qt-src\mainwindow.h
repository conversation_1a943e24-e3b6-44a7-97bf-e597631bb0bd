#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QWebEngineView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QAction>
#include <QLabel>
#include <QProgressBar>
#include <QMessageBox>
#include <QApplication>
#include <QDir>
#include <QTcpSocket>
#include <QWebChannel>
#include <QMouseEvent>
#include <QPoint>
#include <QPushButton>
#include <QFrame>
#include <QEvent>
#include <QWidget>
#include <QSystemTrayIcon>
#include <QMenu>
#include <QOperatingSystemVersion>
#include <QCloseEvent>

#include "windowapi.h"
#include "databaseapi.h"

#include "knowledgeapi.h"
#include "customwebengineview.h"
#include "qwkcustomtitlebar.h"

// QWindowKit integration
#include <QWKWidgets/widgetwindowagent.h>

// 前向声明，避免循环引用
class CustomWebEngineView;

class WindowApi;
class DatabaseApi;

class KnowledgeApi;
class QWebChannel;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    // 抽屉控制方法
    void toggleLeftDrawer();
    void toggleRightDrawer();
    void syncDrawerStatesFromFrontend();

    // 获取标题栏对象
    QWKCustomTitleBar *getQWKTitleBar() const { return m_qwkTitleBar; }

    // 窗口大小调整方向枚举
    enum ResizeDirection
    {
        ResizeNone,
        ResizeTop,
        ResizeBottom,
        ResizeLeft,
        ResizeRight,
        ResizeTopLeft,
        ResizeTopRight,
        ResizeBottomLeft,
        ResizeBottomRight
    };

public slots:
    // JavaScript可调用的窗口控制方法
    void minimizeWindow();
    void toggleMaximizeWindow();
    void closeWindow();
    void reloadPage();
    void openDevTools();
    void toggleMode();
    void toggleTheme();

    // JavaScript可调用的拖拽方法
    void startWindowDrag(int x, int y);
    void moveWindow(int x, int y);
    void endWindowDrag();

    // 主题设置相关的JavaScript可调用方法
    bool hasThemeSetting();
    bool getSavedTheme();
    void saveThemeSetting(bool isDark);
    void setThemeFromVue(bool isDark);
    void onThemeChangedFromFrontend();
    void setThemeDirectly(bool isDark);

    // Mica效果测试方法
    void toggleMicaEffect();

signals:
    // 通知Vue应用主题变化
    void themeChanged(bool isDark);

private slots:
    void showAbout();
    void toggleFullScreen();
    void zoomIn();
    void zoomOut();
    void resetZoom();

    // WebView事件处理
    void onLoadStarted();
    void onLoadProgress(int progress);
    void onLoadFinished(bool ok);
    void onUrlChanged(const QUrl &url);
    void onDevToolsRequested();
    void onModeToggleRequested();
    void onThemeToggleRequested();
    void onWebPageLoadFinished(bool ok);
    void setupTrayIcon();
    void onTrayIconActivated(QSystemTrayIcon::ActivationReason reason);
    void minimizeToTray();

protected:
    // 鼠标事件处理（用于窗口拖拽和大小调整）
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    bool eventFilter(QObject *obj, QEvent *event) override;

    // 窗口几何事件处理（用于保存窗口位置和大小）
    void resizeEvent(QResizeEvent *event) override;
    void moveEvent(QMoveEvent *event) override;

    // 事件处理（用于监听系统主题变化）
    void changeEvent(QEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void closeEvent(QCloseEvent *event) override;

private:
    void initializeTheme();
    void setupUI();
    void setupWebViewCommands();
    void loadWebApp();
    bool isDevServerAvailable();
    void detectSystemTheme();
    void onSystemThemeChanged();
    void updateTheme();
    void loadThemeSetting();
    void syncThemeWithVue();
    void setupConnections();
    void configureGpuRendering(QWebEngineSettings *settings);
    void loadRenderingConfig();

    // 窗口大小调整辅助方法
    Qt::CursorShape getCursorShape(const QPoint &point);
    ResizeDirection getResizeDirection(const QPoint &point);

    // 窗口几何管理
    void setupWindowGeometry();
    void saveWindowGeometry();
    void restoreWindowGeometry();

    // UI组件
    CustomWebEngineView *m_webView;
    QWidget *m_centralWidget;
    QVBoxLayout *m_layout;

    // QWindowKit custom title bar
    QWKCustomTitleBar *m_qwkTitleBar;

    // QWindowKit integration
    QWK::WidgetWindowAgent *m_windowAgent;

    // 与JS交互的API
    WindowApi *m_windowApi;

    // 应用状态
    double m_zoomFactor;
    QPoint m_dragPosition; // 用于窗口拖拽
    bool m_dragging;       // 拖拽状态
    bool m_isDarkTheme;    // 主题状态

    // 窗口大小调整状态
    ResizeDirection m_resizeDirection;
    bool m_resizing;
    QPoint m_resizeStartPos;
    QRect m_resizeStartGeometry;

    // 数据库 API
    DatabaseApi *m_databaseApi;

    // 向量 API

    // 知识库 API
    KnowledgeApi *m_knowledgeApi;

    // Web Channel
    QWebChannel *m_webChannel;

    // 新增成员变量
    bool m_isDevMode;

    // 系统托盘相关
    QSystemTrayIcon *m_trayIcon;
    QMenu *m_trayMenu;
};

#endif // MAINWINDOW_H