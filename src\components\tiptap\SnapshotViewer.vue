<template>
  <div class="column no-wrap border-left">
    <div class="q-pa-sm row no-wrap gap-md">
      <q-btn flat dense round icon="mdi-close" @click="closeSnapshotViewer">
        <q-tooltip>关闭</q-tooltip>
      </q-btn>
    </div>
    <template v-if="doc.snapshot.length > 0">
      <div class="scroll-y column no-wrap q-px-md" style="width: 100%; max-height: 360px">
        <span
          v-for="s in doc.snapshot"
          :key="s.created_at"
          class="q-py-xs q-px-sm cursor-pointer"
          :class="`
            ${s.name.includes('auto') ? 'op-2' : 'op-7'}
            ${s.name === activeSnapshotName ? 'text-primary' : ''}
          `"
          @click="setSnapshotContent(s.name)"
          >{{ s.name }}</span
        >
      </div>
      <q-separator spaced inset class="op-5" />
      <TipTap
        :isSnapshot="true"
        :hideToolbar="true"
        :snapshotContent="snapshotContent"
        class="q-space"
      />
      <q-separator spaced inset class="op-5" />
      <div class="q-pa-md column no-wrap gap-xs">
        <q-btn
          dense
          flat
          rounded
          label="从快照恢复"
          class="full-width"
          @click="restoreSnapshotHandler"
        />
        <q-btn
          flat
          dense
          rounded
          icon="mdi-camera-iris"
          label="创建快照"
          @click="createSnapshotHandler(doc.id, '')"
        />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import type { Document } from 'src/types/doc';
import TipTap from './TipTap.vue';
import type { JSONContent } from '@tiptap/vue-3';
import { useDocumentActions } from 'src/composeables/useDocumentActions';
import { useDocStore } from 'src/stores/doc';
const docStore = useDocStore();
const { onGetDocument } = useDocumentActions();

const props = defineProps<{
  doc: Document;
}>();
const emit = defineEmits<{
  (e: 'snapshotCreated'): void;
  (e: 'close'): void;
  (e: 'restore', snapshotContent: JSONContent): void;
  (e: 'updateDocument', doc: Document): void;
}>();

const closeSnapshotViewer = () => {
  emit('close');
};
const snapshotContent = ref<JSONContent>();
const activeSnapshotName = ref<string>();
onMounted(() => {
  if (props.doc.snapshot?.[0]?.name) {
    setSnapshotContent(props.doc.snapshot[0].name);
  }
});
const setSnapshotContent = (snapshotName: string) => {
  activeSnapshotName.value = snapshotName;
  snapshotContent.value = JSON.parse(
    props.doc.snapshot.find((s) => s.name === snapshotName)?.content,
  );
};
const { createSnapshot } = useSqlite();

const createSnapshotHandler = async (docId: number, snapshotName: string) => {
  const _snapshotName = snapshotName || new Date().toISOString();
  await createSnapshot(docId, _snapshotName);

  emit('snapshotCreated');
};

const restoreSnapshotHandler = async () => {
  try {
    // 1. 先保存当前内容作为快照
    await createSnapshot(props.doc.id, `auto-${new Date().toISOString()}`);
    const doc = await onGetDocument(props.doc.id);
    docStore.syncDocuments(doc);
    emit('updateDocument', doc);
    // 2. 确保快照内容存在
    if (!snapshotContent.value) {
      console.error('快照内容不存在');
      return;
    }
    emit('restore', snapshotContent.value);
  } catch (error) {
    console.error('恢复快照失败:', error);
  }
};

onMounted(() => {
  if (props.doc.snapshot.length > 0) {
    setSnapshotContent(props.doc.snapshot[0].name);
  }
});
</script>

<style scoped></style>
