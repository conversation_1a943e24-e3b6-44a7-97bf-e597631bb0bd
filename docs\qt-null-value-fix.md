# Qt 警告修复：QJsonValue(null) to int 转换问题

## 问题描述

在编译和运行应用时出现大量以下警告信息：

```
[Warning] Could not convert argument QJsonValue(null) to target type int .
```

## 问题根因

这个警告源于前端JavaScript代码向Qt C++方法传递`null`值，而C++方法期望`int`类型参数。具体问题：

1. **前端调用**：`db.listFolders(null, callback)` - 传递null给parent_id
2. **Qt接口**：`Q_INVOKABLE void listFolders(int parent_id, QJSValue callback)` - 期望int类型
3. **类型冲突**：Qt无法将`QJsonValue(null)`自动转换为`int`

## 修复方案

### 1. 修改Qt C++接口参数类型

将所有可能接收null值的int参数改为`QVariant`类型：

```cpp
// 修改前
Q_INVOKABLE void listFolders(int parent_id, QJSValue callback);

// 修改后
Q_INVOKABLE void listFolders(const QVariant &parent_id, QJSValue callback);
```

### 2. 添加安全转换函数

在`DatabaseApi`类中添加工具函数处理null值：

```cpp
private:
    int variantToIntSafe(const QVariant &variant, int defaultValue = -1);

// 实现
int DatabaseApi::variantToIntSafe(const QVariant &variant, int defaultValue)
{
    if (variant.isNull() || !variant.isValid()) {
        return defaultValue;
    }

    bool ok;
    int value = variant.toInt(&ok);
    return ok ? value : defaultValue;
}
```

### 3. 更新方法实现

使用安全转换函数处理所有可能的null值：

```cpp
void DatabaseApi::listFolders(const QVariant &parent_id, QJSValue callback)
{
    int parentId = variantToIntSafe(parent_id);  // null -> -1
    QJsonObject result = this->listFolders(parentId);
    callWithJson(callback, result);
}
```

### 4. 简化前端代码

移除前端不必要的类型转换：

```typescript
// 修改前
db.listFolders(parent_id ?? null, callback);

// 修改后（Qt端已处理）
db.listFolders(parent_id ?? null, callback);
```

## 影响的方法

以下Qt API方法已修复null值处理：

- `createDocument(title, content, folder_id, metadata, callback)`
- `updateDocument(id, title, content, folder_id, metadata, callback)`
- `listDocuments(folder_id, callback)`
- `createFolder(name, parent_id, callback)`
- `updateFolder(id, name, parent_id, callback)`
- `listFolders(parent_id, callback)`
- `updateFolderOrder(parent_id, folder_ids, callback)`
- `updateDocumentOrder(folder_id, document_ids, callback)`

## 修复结果

✅ **消除警告**：不再出现`QJsonValue(null) to int`转换警告  
✅ **保持兼容**：前端代码无需大幅修改  
✅ **更安全**：统一的null值处理逻辑  
✅ **更清晰**：明确的参数类型和默认值处理

## 验证

重新编译应用后，应该不再看到相关警告信息：

```bash
./build-qt-dev.sh debug
```

如果仍有警告，请检查是否还有其他方法需要类似处理。

## 后续修复（2024-12-29）

在初始修复后，发现 `useSqlite.ts` 和类型定义文件中仍有遗漏：

### 额外修复的问题

1. **useSqlite.ts中的类型问题**：

   - `createDocument` 方法的 `folder_id` 参数类型从 `number` 改为 `number | null`
   - `updateDocument` 方法的 `folder_id` 参数类型从 `number` 改为 `number | null`
   - 添加了 `?? null` 安全转换确保正确传递

2. **类型定义文件更新**：
   - 更新 `env.d.ts` 中 `DatabaseApi` 接口的相应方法签名
   - 确保TypeScript类型与实际实现保持一致

### 修复的代码

```typescript
// src/composeables/useSqlite.ts
const createDocument = async (
  title: string,
  content: JSONContent,
  folder_id: number | null, // 修改类型
  metadata: string = '{}',
) => {
  // ...
  const safeFolderId = folder_id ?? null; // 安全转换
  db.createDocument(title, contentJson, safeFolderId, metadataJson, callback);
};
```

这确保了从TypeScript到Qt C++的完整null值处理链路。

### 最终发现的问题（2024-12-29）

在启动时分析控制台警告的根源时，发现还有对话相关的方法存在相同问题：

1. **对话方法的参数类型问题**：

   - `createConversation` 方法的 `document_id` 参数在独立对话时传递 `-1`
   - `listConversations` 方法的 `document_id` 参数在查询独立对话时可能传递null

2. **修复的方法**：

   ```cpp
   // qt-src/databaseapi.h
   Q_INVOKABLE void createConversation(const QVariant &document_id, ...);
   Q_INVOKABLE void listConversations(const QVariant &document_id, ...);

   // qt-src/databaseapi.cpp
   void DatabaseApi::createConversation(const QVariant &document_id, ...) {
       int docId = variantToIntSafe(document_id);  // 安全转换
       // ...
   }
   ```

3. **类型定义更新**：
   ```typescript
   // src/env.d.ts
   createConversation(document_id: number | null, ...): void;
   listConversations(document_id: number | null, ...): void;
   ```

这个修复解决了应用启动时创建独立对话和查询对话列表时产生的null值转换警告。
