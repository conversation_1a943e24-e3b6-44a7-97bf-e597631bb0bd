<template>
  <div class="fit column no-wrap">
    <!-- 知识库列表视图 -->
    <KnowledgeBaseList
      v-if="!selectedKnowledgeBase && !showCreateDialog"
      :knowledge-bases="knowledgeBases"
      :current-knowledge-base="currentKnowledgeBase"
      :store-loading="storeLoading"
      :show-search="showSearch"
      :showCreateDialog="showCreateDialog"
      @select-knowledge-base="selectKnowledgeBase"
      @create-knowledge-base="showCreateDialog = true"
      @edit-knowledge-base="editKnowledgeBase"
      @delete-knowledge-base="handleDeleteKnowledgeBase"
      @toggle-search="showSearch = !showSearch"
    />

    <!-- 知识库详情视图 -->
    <KnowledgeBaseDetailView
      v-else-if="!showCreateDialog && !showSettingsDialog"
      :key="`detail-${selectedKnowledgeBase?.id}-${forceRefreshKey}`"
      :knowledge-base="selectedKnowledgeBase"
      :knowledge-documents="knowledgeDocuments"
      :stats="stats"
      :detail-loading="detailLoading"
      :processing-documents="processingDocuments"
      :recently-completed-docs="recentlyCompletedDocs"
      :vectorization-progress="vectorizationProgress"
      @go-back="goBackToList"
      @create-document="openCreateDocumentDialog"
      @refresh="refreshDetailData"
      @settings="showSettingsDialog = true"
      @open-document="openKnowledgeDocument"
      @delete-document="confirmRemoveDocument"
    />

    <!-- 创建/编辑知识库对话框 -->
    <KnowledgeBaseSettingsDialog
      v-model="showCreateDialog"
      :knowledge-base="editingKb"
      :loading="isProcessing"
      class="fit"
      @save="saveKnowledgeBase"
      @close="showCreateDialog = false"
    />

    <!-- 创建文档对话框 -->
    <CreateDocumentDialog
      v-model="showCreateDocumentDialog"
      :loading="processing"
      @save="saveCreatedDocument"
      @chunking-completed="onChunkingCompleted"
      @chunking-error="onChunkingError"
    />

    <!-- 知识库设置对话框 -->
    <KnowledgeBaseSettingsDialog
      v-model="showSettingsDialog"
      :knowledge-base="selectedKnowledgeBase"
      :loading="saving"
      @save="saveSettings"
      @close="showSettingsDialog = false"
    />

    <!-- 文档查看编辑对话框 -->
    <div
      v-if="showDocumentViewer"
      class="absolute-full z-max"
      :class="$q.dark.isActive ? 'bg-dark' : 'bg-grey-1'"
    >
      <SimpleEditor
        ref="viewEditorRef"
        v-model="documentContent"
        :OriginalDocument="OriginalDocument"
        @update:modelValue="onDocumentContentChange"
        @ready="onDocumentEditorReady"
        @close="showDocumentViewer = false"
        class="fit"
      >
        <template #toolbar-left>
          <q-input
            v-model="documentTitle"
            dense
            type="text"
            class="q-space"
            hide-bottom-space
            :placeholder="$t('src.components.KnowledgeBaseManager.please_input_document_title')"
            :rules="[
              (val) =>
                !!val?.trim() ||
                $t('src.components.KnowledgeBaseManager.please_input_document_title'),
            ]"
            @blur="validateEditTitle"
          >
            <template #prepend>
              <q-btn
                flat
                dense
                size="sm"
                icon="arrow_back"
                class="q-mx-sm"
                @click="viewEditorRef?.close()"
              />
            </template>
            <template #append>
              <q-btn-dropdown
                split
                color="primary"
                flat
                dense
                class="q-mx-sm border"
                @click="saveDocument"
                :loading="processing"
                :disable-main-btn="
                  !documentContent?.trim() || !documentTitle?.trim() || !!OriginalDocument
                "
              >
                <template #label>
                  <div class="q-px-sm">
                    {{
                      isDocumentEdited
                        ? $t('src.components.KnowledgeBaseManager.save') + ' *'
                        : $t('src.components.KnowledgeBaseManager.save')
                    }}
                  </div>
                </template>
                <q-list dense>
                  <q-item clickable v-close-popup @click="closeDocumentViewer">
                    <q-item-section side>
                      <q-icon name="close" size="18px" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{
                        $t('src.components.KnowledgeBaseManager.close_editor')
                      }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </template>
          </q-input>
        </template>
      </SimpleEditor>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import { useQuasar } from 'quasar';
import { storeToRefs } from 'pinia';
import { useKnowledge } from '../composeables/useKnowledge';
import type { KnowledgeBaseStats } from '../composeables/useKnowledge';
import { useKnowledgeStore } from '../stores/knowledge';
import { useLlmStore } from '../stores/llm';
import type { KnowledgeBase, KnowledgeDocument } from '../env';
import type { ChunkingMethod, ChunkingConfig } from '../types/qwen';
import type { ChunkingResult } from '../utils/knowledgeBase';

// 组件导入
import KnowledgeBaseList from './KnowledgeBaseList.vue';
import KnowledgeBaseDetailView from './KnowledgeBaseDetailView.vue';
import KnowledgeBaseSettingsDialog from './KnowledgeBaseSettingsDialog.vue';
import CreateDocumentDialog from './CreateDocumentDialog.vue';
import SimpleEditor from './SimpleEditor.vue';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

// 组合式 API
const $q = useQuasar();
const knowledge = useKnowledge();
const knowledgeStore = useKnowledgeStore();
const llmStore = useLlmStore();
const { getOriginalDocumentByKnowledgeDocumentId } = useSqlite();

// 从store中获取响应式数据
const {
  knowledgeBases,
  currentKnowledgeBase,
  knowledgeDocuments,
  isLoading: storeLoading,
  isProcessing,
} = storeToRefs(knowledgeStore);

// 本地响应式数据
const showSearch = ref(false);
const showCreateDialog = ref(false);
const showCreateDocumentDialog = ref(false);
const showSettingsDialog = ref(false);
const showDocumentViewer = ref(false);

// 编辑状态
const editingKb = ref<KnowledgeBase | null>(null);
const selectedKnowledgeBase = ref<KnowledgeBase | null>(null);
const detailLoading = ref(false);
const stats = ref<KnowledgeBaseStats | null>(null);
const saving = ref(false);
const processing = ref(false);

// 处理状态跟踪
const processingDocuments = ref(new Set<number>());
const recentlyCompletedDocs = ref(new Set<number>());

// 向量化进度跟踪
const vectorizationProgress = ref(new Map<number, { completed: number; total: number }>());

// 跟踪每个文档已完成的chunk数量，用于实时更新统计
const completedChunksTracker = ref(new Map<number, number>());

// 文档查看编辑相关
const currentViewingDocument = ref<KnowledgeDocument | null>(null);
const documentContent = ref('');
const documentTitle = ref('');
const isDocumentEdited = ref(false);
const isInitializingDocument = ref(false);
const viewEditorRef = ref();

// 方法
const selectKnowledgeBase = async (kb: KnowledgeBase) => {
  try {
    await knowledgeStore.setCurrentKnowledgeBase(kb.id);
    selectedKnowledgeBase.value = kb;
    await loadKnowledgeBaseDetail(kb.id);
  } catch (error) {
    console.error('❌ [KnowledgeBaseManager] 设置当前知识库失败:', error);
  }
};

const goBackToList = async () => {
  selectedKnowledgeBase.value = null;
  await knowledgeStore.setCurrentKnowledgeBase(null);
};

const loadKnowledgeBaseDetail = async (knowledgeBaseId: number) => {
  detailLoading.value = true;
  try {
    await knowledgeStore.setCurrentKnowledgeBase(knowledgeBaseId);
    if (currentKnowledgeBase.value) {
      selectedKnowledgeBase.value = currentKnowledgeBase.value;
    }
    await loadStats(knowledgeBaseId);
  } catch (error) {
    console.error('加载知识库信息失败:', error);
  } finally {
    detailLoading.value = false;
  }
};

const loadStats = async (knowledgeBaseId: number) => {
  try {
    const statsData = await knowledge.getKnowledgeBaseStats(knowledgeBaseId);
    stats.value = statsData;

    // 重新初始化跟踪器，保持当前的向量化进度状态
    const newTracker = new Map<number, number>();

    // 遍历当前的向量化进度，重建跟踪器状态（不影响统计数据）
    vectorizationProgress.value.forEach((progress, docId) => {
      if (progress.completed > 0) {
        newTracker.set(docId, progress.completed);
      }
    });

    completedChunksTracker.value = newTracker;
  } catch (error) {
    console.error('❌ [KnowledgeBaseManager] 加载统计信息失败:', error);
  }
};

const refreshDetailData = async () => {
  if (selectedKnowledgeBase.value) {
    const kbId = selectedKnowledgeBase.value.id;
    await Promise.all([
      loadKnowledgeBaseDetail(kbId),
      knowledgeStore.loadKnowledgeDocuments(kbId),
      // 同时刷新知识库列表，确保统计数据同步更新
      knowledgeStore.loadKnowledgeBases(),
    ]);
    // 刷新完成后清除该知识库的刷新标志位
    knowledgeStore.clearKnowledgeBaseRefreshFlag(kbId);
  }
};

const editKnowledgeBase = (kb: KnowledgeBase) => {
  editingKb.value = kb;
  showCreateDialog.value = true;
};

const handleDeleteKnowledgeBase = (kb: KnowledgeBase) => {
  $q.dialog({
    title: $t('src.components.KnowledgeBaseManager.confirm_delete'),
    message: $t('src.components.KnowledgeBaseManager.confirm_delete_message', { name: kb.name }),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    void knowledgeStore.deleteKnowledgeBase(kb.id);
  });
};

const saveKnowledgeBase = async (data: { name: string; description: string; settings: string }) => {
  try {
    const parsedSettings = JSON.parse(data.settings || '{}');

    if (editingKb.value) {
      await knowledgeStore.updateKnowledgeBase(
        editingKb.value.id,
        data.name,
        data.description,
        parsedSettings,
      );
    } else {
      await knowledgeStore.createKnowledgeBase(data.name, data.description, parsedSettings);
      await llmStore.refreshKnowledgeBasesList();
    }

    showCreateDialog.value = false;
    editingKb.value = null;
  } catch (error) {
    console.error('知识库操作失败:', error);
  }
};

// 创建文档
const openCreateDocumentDialog = () => {
  showCreateDocumentDialog.value = true;
};

// 存储待处理的文档数据
const pendingDocuments = ref<
  Map<
    string,
    {
      documentId: number;
      data: {
        title: string;
        content: string;
        chunkingMethod: ChunkingMethod;
        chunkingConfig: ChunkingConfig;
      };
    }
  >
>(new Map());

const saveCreatedDocument = async (data: {
  title: string;
  content: string;
  chunkingMethod: ChunkingMethod;
  chunkingConfig: ChunkingConfig;
}) => {
  if (!selectedKnowledgeBase.value) return;

  try {
    // 第一步：创建知识库文档（不包含chunks）
    const result = await knowledge.createKnowledgeDocumentOnly(
      selectedKnowledgeBase.value.id,
      data.title,
      data.content,
      'markdown', // documentType
    );

    // callKnowledgeApi 已经处理了 success 检查，直接返回 data 字段内容
    if (!result || !result.id) {
      throw new Error($t('src.components.KnowledgeBaseManager.create_document_failed'));
    }

    // 保持文档ID为数字格式，与数据库字段类型一致
    const documentId = typeof result.id === 'string' ? parseInt(result.id) : result.id;

    // 存储待处理的文档数据
    const documentKey = `${selectedKnowledgeBase.value.id}-${documentId}`;
    pendingDocuments.value.set(documentKey, {
      documentId,
      data,
    });

    // 刷新数据显示新文档（只刷新当前知识库的详细信息，不刷新整个知识库列表）
    await refreshDetailData();

    $q.notify({
      type: 'positive',
      message: $t('src.components.KnowledgeBaseManager.document_created_successfully'),
      timeout: 3000,
    });
  } catch (error) {
    console.error('💥 [KnowledgeBaseManager] 创建文档失败:', error);
    const errorMessage =
      error instanceof Error
        ? error.message
        : $t('src.components.KnowledgeBaseManager.create_document_failed');
    $q.notify({
      type: 'negative',
      message: errorMessage,
    });
  }
};

// 处理切割完成事件
const onChunkingCompleted = async (eventData: {
  documentData: {
    title: string;
    content: string;
    chunkingMethod: ChunkingMethod;
    chunkingConfig: ChunkingConfig;
  };
  chunkingResult: ChunkingResult;
}) => {
  if (!selectedKnowledgeBase.value) return;

  try {
    // 查找对应的文档ID
    const documentKey = Array.from(pendingDocuments.value.keys()).find((key) => {
      const pending = pendingDocuments.value.get(key);
      return pending?.data.title === eventData.documentData.title;
    });

    if (!documentKey) {
      console.error('💥 [KnowledgeBaseManager] 找不到对应的待处理文档');
      return;
    }

    const pendingDoc = pendingDocuments.value.get(documentKey);
    if (!pendingDoc) return;

    // 第三步：提交切割结果到Qt后端
    const chunkArray = eventData.chunkingResult.chunks.map((chunk) => ({
      pageContent: chunk.pageContent,
      metadata: chunk.metadata || {},
    }));

    const submitResult = await knowledge.submitChunkingResults(pendingDoc.documentId, chunkArray);

    if (submitResult.success) {
      $q.notify({
        type: 'positive',
        message: $t('src.components.KnowledgeBaseManager.chunking_result_submitted'),
        timeout: 3000,
      });
    } else {
      throw new Error(
        submitResult.message ||
          $t('src.components.KnowledgeBaseManager.submit_chunking_result_failed'),
      );
    }

    // 从待处理列表中移除
    pendingDocuments.value.delete(documentKey);

    // 关闭对话框
    showCreateDocumentDialog.value = false;
  } catch (error) {
    console.error('💥 [KnowledgeBaseManager] 提交切割结果失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.KnowledgeBaseManager.submit_chunking_result_failed'),
    });
  }
};

// 处理切割错误事件
const onChunkingError = (eventData: {
  documentData: {
    title: string;
    content: string;
    chunkingMethod: ChunkingMethod;
    chunkingConfig: ChunkingConfig;
  };
  error: string;
}) => {
  console.error('💥 [KnowledgeBaseManager] 切割失败:', eventData.error);

  // 查找并清理待处理的文档
  const documentKey = Array.from(pendingDocuments.value.keys()).find((key) => {
    const pending = pendingDocuments.value.get(key);
    return pending?.data.title === eventData.documentData.title;
  });

  if (documentKey) {
    pendingDocuments.value.delete(documentKey);
  }

  $q.notify({
    type: 'negative',
    message: $t('src.components.KnowledgeBaseManager.chunking_failed'),
    timeout: 5000,
  });
};

// 文档查看编辑
const OriginalDocument = ref<{
  success: boolean;
  id?: number;
  title?: string;
  content?: string;
  metadata?: string;
  created_at?: string;
  updated_at?: string;
  message?: string;
  error?: string;
}>();
const openKnowledgeDocument = async (knowledgeDocumentId: number | string) => {
  if (!knowledgeDocumentId || !selectedKnowledgeBase.value) return;
  try {
    const docId =
      typeof knowledgeDocumentId === 'string'
        ? parseInt(knowledgeDocumentId, 10)
        : knowledgeDocumentId;

    let doc = knowledgeDocuments.value.find(
      (d) => d.id === docId || d.id.toString() === docId.toString(),
    );

    if (!doc) {
      await refreshDetailData();
      doc = knowledgeDocuments.value.find((d) => {
        return d.id === docId || d.id.toString() === docId.toString();
      });
    }

    if (!doc) {
      $q.notify({
        type: 'negative',
        message: $t('src.components.KnowledgeBaseManager.document_not_found'),
      });
      return;
    }
    OriginalDocument.value = await getOriginalDocumentByKnowledgeDocumentId(doc.id);

    const editableTypes = ['markdown', 'md', 'txt', 'text', 'created', undefined, null, ''];
    const fileType = doc.file_type?.toLowerCase();
    const isEditable = editableTypes.includes(fileType);

    if (isEditable) {
      currentViewingDocument.value = doc;
      documentTitle.value = doc.title;
      isInitializingDocument.value = true;
      documentContent.value = doc.content || '';
      isDocumentEdited.value = false;
      showDocumentViewer.value = true;
    } else {
      $q.notify({
        type: 'info',
        message:
          $t('src.components.KnowledgeBaseManager.document_type') +
          ' "' +
          (doc.file_type || $t('src.components.KnowledgeBaseManager.unknown')) +
          '" ' +
          $t('src.components.KnowledgeBaseManager.not_support_online_editing'),
      });
    }
  } catch (error) {
    console.error('打开文档失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.KnowledgeBaseManager.open_document_failed'),
    });
  }
};

const closeDocumentViewer = () => {
  if (isDocumentEdited.value) {
    $q.dialog({
      title: $t('src.components.KnowledgeBaseManager.confirm_close'),
      message: $t('src.components.KnowledgeBaseManager.document_has_been_modified_but_not_saved'),
      cancel: true,
      persistent: true,
    }).onOk(() => {
      resetDocumentViewer();
    });
  } else {
    resetDocumentViewer();
  }
};

const resetDocumentViewer = () => {
  showDocumentViewer.value = false;
  currentViewingDocument.value = null;
  documentTitle.value = '';
  documentContent.value = '';
  isDocumentEdited.value = false;
  isInitializingDocument.value = false;
};

const onDocumentContentChange = () => {
  if (!isInitializingDocument.value) {
    isDocumentEdited.value = true;
  }
};

const onDocumentEditorReady = () => {
  isInitializingDocument.value = false;
  isDocumentEdited.value = false;
};

const validateEditTitle = () => {
  if (!documentTitle.value.trim()) {
    $q.notify({
      type: 'warning',
      message: $t('src.components.KnowledgeBaseManager.please_input_document_title'),
    });
    return false;
  }
  return true;
};

const saveDocument = async () => {
  if (!validateEditTitle() || !currentViewingDocument.value) return;

  try {
    const updatedTitle = documentTitle.value.trim();
    const markdownContent = viewEditorRef.value?.getMarkdown() || '';

    const result = await knowledge.updateKnowledgeDoc(
      currentViewingDocument.value.id,
      updatedTitle,
      markdownContent,
      currentViewingDocument.value.source_type || 'updated',
      currentViewingDocument.value.file_path || '',
      currentViewingDocument.value.file_type || 'markdown',
      true,
      'markdown',
    );

    if (result && typeof result === 'object' && 'data' in result) {
      const docData = result.data as { id: string; processing?: boolean };
      if (docData.processing) {
        processingDocuments.value.add(parseInt(docData.id));
      }
    }

    await Promise.all([refreshDetailData(), knowledgeStore.refreshKnowledgeBases()]);

    resetDocumentViewer();

    $q.notify({
      type: 'positive',
      message: $t('src.components.KnowledgeBaseManager.document_saved_successfully'),
    });
  } catch (error) {
    console.error('保存文档失败:', error);
    $q.notify({
      type: 'negative',
      message:
        error instanceof Error
          ? error.message
          : $t('src.components.KnowledgeBaseManager.save_document_failed'),
    });
  }
};

const confirmRemoveDocument = (doc: KnowledgeDocument) => {
  $q.dialog({
    title: $t('src.components.KnowledgeBaseManager.confirm_remove'),
    message: $t('src.components.KnowledgeBaseManager.confirm_remove_message', { name: doc.title }),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    void (async () => {
      try {
        // 删除知识库文档（deleteKnowledgeDoc方法已经包含了清除关联关系的逻辑）
        await knowledge.deleteKnowledgeDoc(doc.id, true);

        // 刷新界面数据
        await Promise.all([refreshDetailData(), knowledgeStore.refreshKnowledgeBases()]);
      } catch (error) {
        console.error('❌ [KnowledgeBaseManager] 移除文档失败:', error);
        $q.notify({
          type: 'negative',
          message: '移除文档失败: ' + (error instanceof Error ? error.message : '未知错误'),
          position: 'top',
        });
      }
    })();
  });
};

const saveSettings = async (data: { name: string; description: string; settings: string }) => {
  if (!selectedKnowledgeBase.value) return;

  try {
    const parsedSettings = JSON.parse(data.settings || '{}');
    await knowledge.updateKB(
      selectedKnowledgeBase.value.id,
      data.name,
      data.description,
      parsedSettings,
    );

    await Promise.all([
      loadKnowledgeBaseDetail(selectedKnowledgeBase.value.id),
      knowledgeStore.refreshKnowledgeBases(),
    ]);

    showSettingsDialog.value = false;
  } catch (error) {
    console.error('保存设置失败:', error);
  }
};

// 生命周期
onMounted(() => {
  void knowledgeStore.loadKnowledgeBases();

  // 设置文档向量化完成监听
  const unsubscribeVectorized = knowledge.onDocumentVectorized(
    (docId: string, chunkCount: number) => {
      const docIdNum = parseInt(docId);

      // 确保清理所有状态数据
      processingDocuments.value.delete(docIdNum);
      vectorizationProgress.value.delete(docIdNum);
      completedChunksTracker.value.delete(docIdNum);

      if (chunkCount > 0) {
        // 如果还没有在完成列表中，添加进去
        if (!recentlyCompletedDocs.value.has(docIdNum)) {
          recentlyCompletedDocs.value.add(docIdNum);

          // 3秒后移除完成状态
          setTimeout(() => {
            recentlyCompletedDocs.value.delete(docIdNum);
          }, 3000);
        }

        $q.notify({
          type: 'positive',
          message: $t('src.components.KnowledgeBaseManager.document_vectorized_successfully'),
          caption:
            $t('src.components.KnowledgeBaseManager.document_id') +
            ': ' +
            docId +
            '，' +
            $t('src.components.KnowledgeBaseManager.generated') +
            ' ' +
            chunkCount +
            ' ' +
            $t('src.components.KnowledgeBaseManager.knowledge_fragments'),
          timeout: 3000,
        });

        // 延迟刷新数据，确保数据库已更新
        setTimeout(() => {
          void knowledgeStore.refreshKnowledgeBases();
          void refreshDetailData();
        }, 1000);
      } else {
        $q.notify({
          type: 'negative',
          message: $t('src.components.KnowledgeBaseManager.document_vectorization_failed'),
          caption: $t('src.components.KnowledgeBaseManager.document_id') + ': ' + docId,
          timeout: 5000,
        });
      }
    },
  );

  // 设置向量化进度监听
  const unsubscribeProgress = knowledge.onVectorizationProgress(
    (kbId: string, docId: string, completed: number, total: number) => {
      const kbIdNum = parseInt(kbId);
      const docIdNum = parseInt(docId);

      // 只处理当前选中知识库的向量化进度
      if (!selectedKnowledgeBase.value || selectedKnowledgeBase.value.id !== kbIdNum) {
        return;
      }

      // 不手动更新stats.total_chunks，避免与后端数据冲突
      // 让stats数据始终来自后端数据库，避免race condition

      // 更新跟踪器
      completedChunksTracker.value.set(docIdNum, completed);

      // 如果向量化完成，清理进度数据并标记为完成
      if (completed === total && total > 0) {
        vectorizationProgress.value.delete(docIdNum);
        processingDocuments.value.delete(docIdNum);
        recentlyCompletedDocs.value.add(docIdNum);

        // 清理跟踪器
        completedChunksTracker.value.delete(docIdNum);

        // 3秒后移除完成状态
        setTimeout(() => {
          recentlyCompletedDocs.value.delete(docIdNum);
        }, 3000);
      } else {
        // 检查是否需要重建向量化进度状态（应用重启后恢复向量化时）
        if (!vectorizationProgress.value.has(docIdNum)) {
          // 重建进度状态
          vectorizationProgress.value.set(docIdNum, { completed, total });

          // 添加到处理中列表
          processingDocuments.value.add(docIdNum);
        } else {
          // 更新现有进度数据
          vectorizationProgress.value.set(docIdNum, { completed, total });
        }

        // 如果是第一次收到进度，确保添加到处理中列表
        if (completed === 1 && total > 0) {
          processingDocuments.value.add(docIdNum);
        }
      }
    },
  );

  onUnmounted(() => {
    unsubscribeVectorized();
    unsubscribeProgress();
    // 清理跟踪器
    completedChunksTracker.value.clear();
  });
});

// 强制刷新标志位 - 用于解决keep-alive缓存导致的刷新问题
const forceRefreshKey = ref(0);

// 监听知识库选择变化
watch(selectedKnowledgeBase, (newKb) => {
  if (newKb && knowledgeStore.isKnowledgeBaseNeedsRefresh(newKb.id)) {
    void refreshDetailData();
  }
});

// 监听store中的刷新标志位变化 - 这是关键的解决方案
watch(
  () => knowledgeStore.knowledgeBasesNeedRefresh,
  (newSet) => {
    // 如果当前有选中的知识库，且该知识库在需要刷新的列表中
    if (selectedKnowledgeBase.value) {
      const kbId = selectedKnowledgeBase.value.id;
      if (newSet.has(kbId)) {
        // 强制更新key来重新渲染组件
        forceRefreshKey.value += 1;
        void refreshDetailData();
      }
    }
  },
  { deep: true },
);
</script>
