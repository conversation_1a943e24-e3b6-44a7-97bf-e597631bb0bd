import { ref, onUnmounted } from 'vue';
import { useGlobalErrorHandler } from './useErrorHandler';
import {
  MEMORY_THRESHOLDS,
  MEMORY_MONITOR,
  MEMORY_LEAK_DETECTION,
  CLEANUP_STRATEGIES,
  isDevelopment,
} from '../config/performance.config';

// 可释放资源接口
interface Disposable {
  dispose(): void;
}

// 内存监控配置
interface MemoryMonitorConfig {
  // 内存阈值（字节）
  thresholds: {
    warning: number; // 警告阈值
    critical: number; // 临界阈值
    emergency: number; // 紧急阈值
  };
  // 监控间隔（毫秒）
  monitorInterval: number;
  // 阈值超出时的回调
  onThresholdExceeded: (level: MemoryPressureLevel, usage: MemoryUsage) => void;
  // 是否启用自动清理
  autoCleanup: boolean;
}

// 内存压力级别
type MemoryPressureLevel = 'warning' | 'critical' | 'emergency';

// 内存使用情况
interface MemoryUsage {
  used: number;
  total: number;
  percentage: number;
  jsHeapSizeLimit: number;
  usedJSHeapSize: number;
  totalJSHeapSize: number;
}

// 资源类型
type ResourceType = 'editor' | 'image' | 'event' | 'timer' | 'observer' | 'cache' | 'other';

// 资源元数据
interface ResourceMetadata {
  id: string;
  type: ResourceType;
  createdAt: number;
  lastAccessed: number;
  size?: number;
  description?: string;
  priority: 'low' | 'medium' | 'high';
}

// 清理策略
interface CleanupStrategy {
  type: ResourceType;
  maxAge: number; // 最大存活时间（毫秒）
  maxIdle: number; // 最大空闲时间（毫秒）
  maxCount: number; // 最大数量
  cleanupRatio: number; // 清理比例 (0-1)
}

// 内存泄漏检测器
class MemoryLeakDetector {
  private suspiciousObjects = new WeakSet();
  private objectCounts = new Map<string, number>();
  private detectionInterval: NodeJS.Timeout | null = null;

  start() {
    // 仅在开发环境启用
    if (!MEMORY_LEAK_DETECTION.enabled) {
      return;
    }

    this.detectionInterval = setInterval(() => {
      this.detectLeaks();
    }, MEMORY_LEAK_DETECTION.detectionInterval);
  }

  stop() {
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval);
      this.detectionInterval = null;
    }
  }

  private detectLeaks() {
    // 检测对象数量异常增长
    const currentCounts = this.getCurrentObjectCounts();

    for (const [type, count] of currentCounts) {
      const previousCount = this.objectCounts.get(type) || 0;
      const growthRate = count > 0 ? (count - previousCount) / previousCount : 0;

      if (
        growthRate > MEMORY_LEAK_DETECTION.objectGrowthThreshold &&
        count > MEMORY_LEAK_DETECTION.minObjectCount
      ) {
        if (isDevelopment) {
          console.warn(
            `🔥 [Memory Leak Detection] Suspected memory leak: ${type} object count increased abnormally by ${growthRate.toFixed(2)}x (${count} objects)`,
          );
        }
      }
    }

    this.objectCounts = currentCounts;
  }

  private getCurrentObjectCounts(): Map<string, number> {
    const counts = new Map<string, number>();

    // 这里可以添加更多的对象类型检测
    // 由于JavaScript的限制，我们只能检测一些可观察的对象

    // 检测DOM元素数量
    counts.set('DOM_ELEMENTS', document.querySelectorAll('*').length);

    // 检测事件监听器数量（需要自定义追踪）
    counts.set('EVENT_LISTENERS', this.getEventListenerCount());

    return counts;
  }

  private getEventListenerCount(): number {
    // 这里需要与事件监听器追踪系统集成
    // 暂时返回0，实际应用中需要实现自定义追踪
    return 0;
  }
}

/**
 * 内存管理器
 * 负责资源的生命周期管理、内存监控和自动清理
 */
export class MemoryManager {
  private resources = new Map<string, { resource: Disposable; metadata: ResourceMetadata }>();
  private errorHandler: ReturnType<typeof useGlobalErrorHandler>;
  private config: MemoryMonitorConfig;
  private monitorInterval: NodeJS.Timeout | null = null;
  private leakDetector = new MemoryLeakDetector();
  private cleanupStrategies = new Map<ResourceType, CleanupStrategy>();
  private isDestroyed = false;

  // 统计信息
  private stats = {
    totalResources: 0,
    activeResources: 0,
    cleanupCount: 0,
    memoryPressureEvents: 0,
    lastCleanupTime: 0,
  };

  constructor(config: Partial<MemoryMonitorConfig> = {}) {
    this.errorHandler = useGlobalErrorHandler();

    // 使用统一配置，同时允许覆盖
    this.config = {
      thresholds: {
        warning: MEMORY_THRESHOLDS.current.warning,
        critical: MEMORY_THRESHOLDS.current.critical,
        emergency: MEMORY_THRESHOLDS.current.emergency,
      },
      monitorInterval: MEMORY_MONITOR.monitorInterval,
      autoCleanup: MEMORY_MONITOR.autoCleanup,
      onThresholdExceeded: this.handleMemoryPressure.bind(this),
      ...config,
    };

    this.initializeCleanupStrategies();
    this.startMonitoring();
  }

  /**
   * 初始化清理策略
   */
  private initializeCleanupStrategies() {
    // 使用统一配置中的清理策略
    Object.entries(CLEANUP_STRATEGIES).forEach(([type, strategy]) => {
      this.cleanupStrategies.set(type as ResourceType, {
        type: type as ResourceType,
        ...strategy,
      });
    });
  }

  /**
   * 注册资源
   */
  register(resource: Disposable, metadata: Partial<ResourceMetadata> = {}): string {
    if (this.isDestroyed) {
      if (isDevelopment) {
        console.warn('⚠️ [MemoryManager] 尝试在已销毁的内存管理器中注册资源');
      }
      return '';
    }

    const id = metadata.id || this.generateResourceId();
    const now = Date.now();

    const fullMetadata: ResourceMetadata = {
      id,
      type: 'other',
      createdAt: now,
      lastAccessed: now,
      priority: 'medium',
      ...metadata,
    };

    this.resources.set(id, { resource, metadata: fullMetadata });
    this.stats.totalResources++;
    this.stats.activeResources++;

    if (isDevelopment) {
      console.log(`📝 [MemoryManager] 注册资源: ${id} (${fullMetadata.type})`);
    }

    return id;
  }

  /**
   * 取消注册资源
   */
  unregister(id: string): boolean {
    const entry = this.resources.get(id);
    if (!entry) return false;

    try {
      entry.resource.dispose();
      this.resources.delete(id);
      this.stats.activeResources--;
      this.stats.cleanupCount++;

      if (isDevelopment) {
        console.log(`🗑️ [MemoryManager] 取消注册资源: ${id}`);
      }
      return true;
    } catch (error) {
      this.errorHandler.captureError(error as Error, 'memory-manager-unregister', {
        userFriendlyMessage: `资源清理失败: ${id}`,
        severity: 'medium',
      });
      return false;
    }
  }

  /**
   * 动态设置内存阈值
   */
  setThresholds(warning: number, critical: number, emergency?: number): void {
    this.config.thresholds.warning = warning;
    this.config.thresholds.critical = critical;
    if (emergency !== undefined) {
      this.config.thresholds.emergency = emergency;
    }
    if (isDevelopment) {
      console.log(
        `📊 [MemoryManager] 内存阈值已更新 - 警告: ${Math.round(warning / 1024 / 1024)}MB, 临界: ${Math.round(critical / 1024 / 1024)}MB, 紧急: ${Math.round(this.config.thresholds.emergency / 1024 / 1024)}MB`,
      );
    }
  }

  /**
   * 访问资源（更新访问时间）
   */
  access(id: string): void {
    const entry = this.resources.get(id);
    if (entry) {
      entry.metadata.lastAccessed = Date.now();
    }
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage(): MemoryUsage {
    const memory = (
      performance as typeof performance & {
        memory?: {
          usedJSHeapSize: number;
          totalJSHeapSize: number;
          jsHeapSizeLimit: number;
        };
      }
    ).memory;

    if (memory) {
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
      };
    }

    // 降级处理：如果performance.memory不可用
    return {
      used: 0,
      total: 0,
      percentage: 0,
      jsHeapSizeLimit: 0,
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
    };
  }

  /**
   * 开始内存监控
   */
  private startMonitoring(): void {
    this.monitorInterval = setInterval(() => {
      this.checkMemoryUsage();
      this.performRoutineCleanup();
    }, this.config.monitorInterval);

    // 启动内存泄漏检测
    this.leakDetector.start();
  }

  /**
   * 检查内存使用情况
   */
  private checkMemoryUsage(): void {
    const usage = this.getMemoryUsage();
    const { thresholds } = this.config;

    if (usage.used > thresholds.emergency) {
      this.config.onThresholdExceeded('emergency', usage);
    } else if (usage.used > thresholds.critical) {
      this.config.onThresholdExceeded('critical', usage);
    } else if (usage.used > thresholds.warning) {
      this.config.onThresholdExceeded('warning', usage);
    }
  }

  /**
   * 处理内存压力
   */
  private handleMemoryPressure(level: MemoryPressureLevel, usage: MemoryUsage): void {
    if (isDevelopment) {
      console.warn(
        `⚠️ [MemoryManager] 内存压力警告: ${level} (${(usage.used / 1024 / 1024).toFixed(2)}MB)`,
      );
    }

    this.stats.memoryPressureEvents++;

    switch (level) {
      case 'warning':
        this.performSelectiveCleanup(['cache', 'image']);
        break;
      case 'critical':
        this.performSelectiveCleanup(['cache', 'image', 'other']);
        break;
      case 'emergency':
        this.performEmergencyCleanup();
        break;
    }
  }

  /**
   * 执行选择性清理
   */
  private performSelectiveCleanup(types: ResourceType[]): void {
    const now = Date.now();
    const resourcesToClean: string[] = [];

    for (const [id, entry] of this.resources) {
      if (types.includes(entry.metadata.type)) {
        const strategy = this.cleanupStrategies.get(entry.metadata.type);
        if (strategy && this.shouldCleanup(entry.metadata, strategy, now)) {
          resourcesToClean.push(id);
        }
      }
    }

    // 按优先级排序，低优先级先清理
    resourcesToClean.sort((a, b) => {
      const priorityOrder = { low: 0, medium: 1, high: 2 };
      const aEntry = this.resources.get(a);
      const bEntry = this.resources.get(b);
      if (!aEntry || !bEntry) return 0;
      return priorityOrder[aEntry.metadata.priority] - priorityOrder[bEntry.metadata.priority];
    });

    if (isDevelopment) {
      console.log(`🧹 [MemoryManager] 选择性清理: ${resourcesToClean.length} 个资源`);
    }

    resourcesToClean.forEach((id) => this.unregister(id));
    this.stats.lastCleanupTime = now;
  }

  /**
   * 执行紧急清理
   */
  private performEmergencyCleanup(): void {
    if (isDevelopment) {
      console.warn('🚨 [MemoryManager] 执行紧急清理');
    }

    // 保留高优先级资源
    const resourcesToKeep = new Set<string>();
    const resourcesToClean: string[] = [];

    for (const [id, entry] of this.resources) {
      if (entry.metadata.priority === 'high') {
        resourcesToKeep.add(id);
      } else {
        resourcesToClean.push(id);
      }
    }

    // 清理非高优先级资源
    resourcesToClean.forEach((id) => this.unregister(id));

    // 尝试手动触发垃圾回收
    this.forceGarbageCollection();

    if (isDevelopment) {
      console.log(
        `🧹 [MemoryManager] 紧急清理完成: 清理了 ${resourcesToClean.length} 个资源，保留了 ${resourcesToKeep.size} 个高优先级资源`,
      );
    }
  }

  /**
   * 执行常规清理
   */
  private performRoutineCleanup(): void {
    if (!this.config.autoCleanup) return;

    const now = Date.now();
    const resourcesToClean: string[] = [];

    for (const [id, entry] of this.resources) {
      const strategy = this.cleanupStrategies.get(entry.metadata.type);
      if (strategy && this.shouldCleanup(entry.metadata, strategy, now)) {
        resourcesToClean.push(id);
      }
    }

    if (resourcesToClean.length > 0) {
      if (isDevelopment) {
        console.log(`🧹 [MemoryManager] 常规清理: ${resourcesToClean.length} 个资源`);
      }
      resourcesToClean.forEach((id) => this.unregister(id));
      this.stats.lastCleanupTime = now;
    }
  }

  /**
   * 判断是否应该清理资源
   */
  private shouldCleanup(
    metadata: ResourceMetadata,
    strategy: CleanupStrategy,
    now: number,
  ): boolean {
    const age = now - metadata.createdAt;
    const idle = now - metadata.lastAccessed;

    // 超过最大年龄
    if (age > strategy.maxAge) return true;

    // 超过最大空闲时间
    if (idle > strategy.maxIdle) return true;

    // 检查数量限制
    const typeCount = Array.from(this.resources.values()).filter(
      (entry) => entry.metadata.type === metadata.type,
    ).length;

    if (typeCount > strategy.maxCount) {
      // 清理最老的资源
      return metadata.priority === 'low';
    }

    return false;
  }

  /**
   * 强制垃圾回收
   */
  private forceGarbageCollection(): void {
    try {
      // 尝试手动触发垃圾回收（仅在开发环境或特定浏览器中有效）
      const globalObj = globalThis as typeof globalThis & { gc?: () => void };
      if (typeof globalObj.gc === 'function') {
        globalObj.gc();
        if (isDevelopment) {
          console.log('🗑️ [MemoryManager] 手动触发垃圾回收');
        }
      } else {
        // 创建一些临时对象来触发垃圾回收
        const temp = [];
        for (let i = 0; i < 1000; i++) {
          temp.push(new Array(1000).fill(0));
        }
        temp.length = 0;
      }
    } catch (error) {
      if (isDevelopment) {
        console.warn('⚠️ [MemoryManager] 强制垃圾回收失败:', error);
      }
    }
  }

  /**
   * 生成资源ID
   */
  private generateResourceId(): string {
    return `resource_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      memoryUsage: this.getMemoryUsage(),
      resourcesByType: this.getResourcesByType(),
    };
  }

  /**
   * 按类型统计资源
   */
  private getResourcesByType(): Record<ResourceType, number> {
    const counts: Record<ResourceType, number> = {
      editor: 0,
      image: 0,
      event: 0,
      timer: 0,
      observer: 0,
      cache: 0,
      other: 0,
    };

    for (const entry of this.resources.values()) {
      counts[entry.metadata.type]++;
    }

    return counts;
  }

  /**
   * 获取当前内存使用情况
   */
  getCurrentUsage(): number {
    return this.getMemoryUsage().used;
  }

  /**
   * 执行清理操作
   */
  performCleanup(): void {
    this.performRoutineCleanup();
  }

  /**
   * 销毁内存管理器
   */
  destroy(): void {
    if (this.isDestroyed) return;

    if (isDevelopment) {
      console.log('💀 [MemoryManager] 销毁内存管理器');
    }

    // 停止监控
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    // 停止内存泄漏检测
    this.leakDetector.stop();

    // 清理所有资源
    const allResourceIds = Array.from(this.resources.keys());
    allResourceIds.forEach((id) => this.unregister(id));

    this.isDestroyed = true;
  }
}

/**
 * 使用内存管理器的组合式函数
 */
export function useMemoryManager(config?: Partial<MemoryMonitorConfig>) {
  const manager = new MemoryManager(config);
  const stats = ref(manager.getStats());

  // 定期更新统计信息
  const updateStats = () => {
    stats.value = manager.getStats();
  };

  const statsInterval = setInterval(updateStats, 1000);

  // 组件卸载时自动清理
  onUnmounted(() => {
    clearInterval(statsInterval);
    manager.destroy();
  });

  return {
    manager,
    stats,
    register: manager.register.bind(manager),
    unregister: manager.unregister.bind(manager),
    access: manager.access.bind(manager),
    getMemoryUsage: manager.getMemoryUsage.bind(manager),
  };
}

// 导出类型
export type {
  Disposable,
  MemoryMonitorConfig,
  MemoryPressureLevel,
  MemoryUsage,
  ResourceType,
  ResourceMetadata,
  CleanupStrategy,
};
