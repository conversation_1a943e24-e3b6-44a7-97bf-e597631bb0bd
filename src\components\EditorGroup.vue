<template>
  <q-bar dark :class="$q.dark.isActive ? 'bg-grey-10' : 'bg-grey-1'" style="height: 36px">
    <div v-draggable="dragOptions" class="row no-wrap full-height q-space">
      <div
        v-for="d in memoizedDocuments"
        :key="d.id"
        :data-id="d.id"
        class="q-py-xs q-pr-sm q-pl-md row no-wrap items-center cursor-pointer hover-item"
        :class="d.id === tab ? ($q.dark.isActive ? 'ink-dark' : 'bg-grey-3') : ''"
        @click="toggleTab(d.id)"
      >
        <span class="q-pr-md" :class="d.id === tab ? 'text-primary' : 'op-5'"
          >{{ d.title.slice(0, 8) }}<span v-if="d.title.length > 10">...</span></span
        >

        <div class="flex flex-center">
          <q-btn
            dense
            size="xs"
            round
            flat
            icon="mdi-close"
            :class="d.id === tab ? '' : 'hover-show-item'"
            @click.stop.prevent="remove(d.id)"
          />
        </div>
      </div>
    </div>
    <q-btn dense flat icon="mdi-swap-vertical" @click.stop="toggleShowToolbar()">
      <q-tooltip>
        {{ uiStore.perferences.editor.enableToolbar ? $t('hide') : $t('show') }} {{ $t('toolbar') }}
      </q-tooltip>
    </q-btn>
    <q-btn dense flat @click.stop="newSplit()">
      <q-icon name="flip" style="transform: rotate(180deg)" />
    </q-btn>
  </q-bar>
  <q-tab-panels
    v-model="tab"
    keep-alive
    class="q-space"
    :class="$q.dark.isActive ? 'ink-dark' : 'bg-white'"
  >
    <q-tab-panel
      v-for="doc in documents"
      :key="doc.id"
      :name="doc.id"
      class="q-pa-none"
      :class="$q.dark.isActive ? 'transparent' : 'bg-grey-3'"
    >
      <TipTap
        :hideToolbar="true"
        :docId="doc.id || 0"
        :title="doc.title"
        :folderId="folderId"
        :winId="winId"
        class="q-space fit"
        @click="toggleEditor(winId, doc.id)"
        @ready="() => onReady(doc.id)"
        @contentChange="(content) => onContentChange(doc.id, content)"
      />
    </q-tab-panel>
  </q-tab-panels>
</template>
<script setup lang="ts">
import { computed, toRefs } from 'vue';
import { useQuasar } from 'quasar';
import TipTap from './tiptap/TipTap.vue';
import { vDraggable } from 'vue-draggable-plus';
import type { JSONContent } from '@tiptap/vue-3';
import type { Document } from 'src/types/doc';
import type { DraggableEvent } from 'vue-draggable-plus';
import { useI18n } from 'vue-i18n';

import { useUiStore } from 'src/stores/ui';
import { useDocStore } from 'src/stores/doc';
const docStore = useDocStore();
const uiStore = useUiStore();
const { t: $t } = useI18n({ useScope: 'global' });

const $q = useQuasar();

const props = defineProps<{
  documents: Document[];
  paneIndex: number;
  winId: number;
  folderId: number;
}>();

// 使用 toRefs 进行细粒度响应式
const { documents, paneIndex, winId, folderId } = toRefs(props);

// 优化的文档列表计算，只在文档实际变化时重新计算
const memoizedDocuments = computed(() => {
  return documents.value.map((doc) => ({
    id: doc.id,
    title: doc.title || '',
    // 只包含渲染需要的属性，减少不必要的响应式开销
  }));
});

// 优化的活动文档ID计算
const activeDocumentId = computed(() => docStore.getActiveDocumentId(winId.value) || 0);

// 优化的拖拽配置 - 使用 computed 避免重复创建
const dragOptions = computed(() => [
  documents.value,
  {
    animation: 150,
    group: 'documents',
    disabled: memoizedDocuments.value?.length === 1,
    ghostClass: 'ghost',
    onStart: (evt: DraggableEvent) => {
      const docId = Number(evt.item.getAttribute('data-id') || evt.item.dataset.id);
      if (docId) {
        // 使用新的拖拽方法
        docStore.startDragDocument(winId.value, docId);
      }
    },
    onAdd: (evt: DraggableEvent) => {
      // 设置当前窗口为活动窗口
      docStore.setActiveSplitterWindowId(winId.value);
      // 使用新的拖拽完成方法
      if (docStore.isDragging) {
        docStore.handleDragComplete(winId.value, evt.newIndex);
      }
    },
    onRemove: () => {
      // 如果拖拽到无效区域，结束拖拽
      if (docStore.isDragging) {
        docStore.endDragDocument();
      }
    },
    onSort: (evt: DraggableEvent) => {
      // 同一窗口内排序
      if (docStore.isDragging) {
        docStore.sortDocumentsInWindow(winId.value, evt.oldIndex, evt.newIndex);
        docStore.endDragDocument();
      }
    },
  },
]);

const emit = defineEmits<{
  (e: 'onReady', docId: number): void;
  (e: 'onRemoveDoc', leftDocId: number): void;
  (e: 'setPaneIndex', paneIndex: number): void;
  (e: 'onDocumentChange', docId: number): void;
}>();

// 使用 computed 替代 ref，减少状态管理复杂度
const tab = computed({
  get: () => activeDocumentId.value,
  set: (value: number) => {
    if (value !== activeDocumentId.value) {
      docStore.setActiveDocumentId(winId.value, value);
      emit('onDocumentChange', value);
    }
  },
});

// 移除重复的 watch，使用 computed 的 setter 处理更新

// === 优化的事件处理方法 ===

const onReady = (_docId: number) => {
  emit('onReady', _docId);
};

const toggleEditor = (currentWinId: number, docId: number) => {
  docStore.setActiveDocumentId(currentWinId, docId);
  docStore.setActiveSplitterWindowId(currentWinId);
  emit('onDocumentChange', docId);
};

const remove = (docId: number) => {
  docStore.removeDocument(winId.value, docId);
};

const newSplit = () => {
  const activeDoc = documents.value.find((d) => d.id === activeDocumentId.value);

  if (activeDoc) {
    docStore.splitDocument(paneIndex.value, activeDoc, folderId.value);
  }
  emit('setPaneIndex', paneIndex.value + 1);
};

// 处理文档内容变化 - 使用新的实例管理方法
const onContentChange = (docId: number, content: JSONContent) => {
  // 使用新的字符串实例键
  const currentInstanceKey = docStore.generateEditorInstanceKey(winId.value, docId);
  // 调用新的内容更新方法
  docStore.updateEditorContentV2(docId, content, currentInstanceKey);
};

const toggleTab = async (docId: number) => {
  tab.value = docId;
  toggleEditor(winId.value, docId);
  docStore.setLlmDocumentKey(docId);
  uiStore.highlightTreeItem = `document-${docId}`;

  // 确保文档所在文件夹及其父级文件夹都展开
  await uiStore.expandDocumentPath(docId);
};

const toggleShowToolbar = () => {
  uiStore.perferences.editor.enableToolbar = !uiStore.perferences.editor.enableToolbar;
};
</script>
