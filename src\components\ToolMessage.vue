<template>
  <div class="q-pa-md rounded-borders op-5">
    <!-- pexels_search tool result -->
    <template v-if="toolResult.tool_name === 'search_pexels' && toolResult.images">
      <ImageViewer :images="toolResult.images" />
    </template>
    <!-- other tool results -->
    <template v-else>
      <!-- 操作结果 -->
      <div class="q-mb-sm">
        <strong>{{ $t('src.components.ToolMessage.operation') }}:</strong>
        {{ toolResult.operation || $t('src.components.ToolMessage.unknown') }}
      </div>

      <!-- 消息 -->
      <div class="q-mb-sm">
        <strong>{{ $t('src.components.ToolMessage.message') }}: </strong>
        <span>
          {{ toolResult.message }}
        </span>
      </div>

      <!-- 匹配信息 -->
      <div v-if="toolResult.matchInfo" class="q-mb-sm">
        <strong>{{ $t('src.components.ToolMessage.match_info') }}:</strong>
        <div class="q-ml-md">
          <div>
            <strong>{{ $t('src.components.ToolMessage.position') }}:</strong>
            {{ toolResult.matchInfo.position }}
          </div>
          <div>
            <strong>{{ $t('src.components.ToolMessage.original_text') }}:</strong> "{{
              toolResult.matchInfo.originalText
            }}"
          </div>
          <div v-if="toolResult.searchText">
            <strong>{{ $t('src.components.ToolMessage.search') }}:</strong> "{{
              toolResult.searchText
            }}"
          </div>
          <div v-if="toolResult.newContent">
            <strong>{{ $t('src.components.ToolMessage.new_content') }}:</strong> "{{
              toolResult.newContent
            }}"
          </div>
        </div>
      </div>

      <!-- 完整结果 JSON -->
      <q-expansion-item
        dense
        :label="$t('src.components.ToolMessage.view_full_result')"
        class="q-mt-sm"
      >
        <pre class="tool-result">{{ formattedResult }}</pre>
      </q-expansion-item>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { ToolMessage } from 'src/types/qwen';
import { computed } from 'vue';
import ImageViewer from './ImageViewer.vue';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

const props = defineProps<{
  message: ToolMessage;
}>();

// 解析工具结果
const toolResult = computed(() => {
  try {
    return JSON.parse(props.message.content);
  } catch {
    return {
      success: false,
      message: props.message.content,
      operation: $t('src.components.ToolMessage.parse_error'),
    };
  }
});

// 格式化完整结果
const formattedResult = computed(() => {
  return JSON.stringify(toolResult.value, null, 2);
});
</script>
