<template>
  <!-- Qt初始化加载界面 -->
  <div v-if="isLoading" class="loading-container">
    <div class="loading-content">
      <q-spinner-cube color="primary" size="3em" class="loading-spinner" />
      <div class="loading-text">{{ $t('appInitializing') }}</div>
      <div class="loading-subtext">
        {{ loadingMessage }}
      </div>
    </div>
  </div>

  <!-- 主应用界面 -->
  <router-view v-else />
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useI18n } from 'vue-i18n';
import { cleanupAllStores } from 'src/utils/storeCleanup';

const { t: $t } = useI18n({ useScope: 'global' });

const isLoading = ref(true);
const loadingMessage = ref($t('qtConnecting'));

onMounted(async () => {
  try {
    // 检查是否在Qt环境中
    if (typeof window !== 'undefined' && window.qt && window.qt.webChannelTransport) {
      loadingMessage.value = $t('qtApiInitializing');

      // 等待Qt初始化完成
      if (window.qtInitialized instanceof Promise) {
        await window.qtInitialized;
        loadingMessage.value = $t('initCompleted');
      }
    } else {
      // 非Qt环境，直接进入
      loadingMessage.value = $t('appLoading');
    }

    // 添加一个短暂的延迟，确保所有初始化都完成
    await new Promise((resolve) => setTimeout(resolve, 500));

    isLoading.value = false;

    // UI加载完成后，通知后端可以开始静默加载模型
    setTimeout(() => {
      if (window.knowledgeApi?.triggerAutoLoadLocalModel) {
        console.log('🔄 [App] 前端UI已加载完成，触发静默加载本地模型...');
        window.knowledgeApi.triggerAutoLoadLocalModel();
      }
    }, 1000); // 额外延迟1秒，确保UI完全渲染
  } catch (error) {
    console.error('init field:', error);
    loadingMessage.value = $t('appLoading');

    // 即使初始化失败，也要显示主界面
    setTimeout(() => {
      isLoading.value = false;

      // 即使初始化失败，也尝试加载模型
      if (window.knowledgeApi?.triggerAutoLoadLocalModel) {
        console.log('🔄 [App] 前端UI已加载完成（初始化失败后），触发静默加载本地模型...');
        window.knowledgeApi.triggerAutoLoadLocalModel();
      }
    }, 2000);
  }
});

// 在应用销毁前清理所有 Store 资源
onBeforeUnmount(() => {
  cleanupAllStores();
});

// 监听窗口关闭事件，确保在页面卸载前清理资源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    cleanupAllStores();
  });
}
</script>

<style scoped>
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  margin-bottom: 1.5rem;
}

.loading-text {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.loading-subtext {
  font-size: 1rem;
  opacity: 0.7;
  font-weight: 300;
}
</style>
