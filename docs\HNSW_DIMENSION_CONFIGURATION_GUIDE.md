# HNSW维度配置指南

## 概述

HNSW索引的维度配置是向量搜索性能的关键因素。**维度必须与实际向量化时的维度数据精确匹配**，否则会导致索引失效或性能严重下降。

## 为什么维度配置如此重要？

### ObjectBox HNSW索引的维度要求

根据ObjectBox官方文档：

1. **精确匹配**：HNSW索引的dimensions参数必须与实际向量维度完全一致
2. **高维度向量**：如果向量维度高于配置维度，只有前N维会被索引，其余维度被忽略
3. **低维度向量**：如果向量维度低于配置维度，该向量会被完全忽略，不参与索引
4. **性能影响**：维度不匹配会导致搜索结果不准确，甚至完全失效

### 当前应用的向量化模式

InkCop支持多种向量化模式，每种模式产生不同维度的向量：

```cpp
// 1. 云端API模型 - 维度取决于具体模型
std::vector<float> cloudResult = generateApiEmbedding(text);

// 2. 本地GGUF模型 - 维度取决于模型文件
std::vector<float> localResult = generateLocalGGUFEmbedding(text);

// 3. 简单TF-IDF向量化 - 固定100维
std::vector<float> simpleResult = generateSimpleEmbedding(text); // 100维
```

## 常见Embedding模型维度

### OpenAI兼容模型
- **text-embedding-ada-002**: 1536维
- **text-embedding-3-small**: 1536维
- **text-embedding-3-large**: 3072维

### 阿里云模型
- **text-embedding-v1**: 1536维
- **text-embedding-v2**: 1536维
- **text-embedding-v3**: 1536维
- **text-embedding-v4**: 1536维

### 其他常见模型
- **sentence-transformers/all-MiniLM-L6-v2**: 384维
- **sentence-transformers/all-mpnet-base-v2**: 768维
- **本地GGUF模型**: 取决于具体模型文件

### 回退方案
- **简单TF-IDF**: 100维（应用内置）

## 当前配置状态

### 当前HNSW配置
```cpp
// qt-src/objectbox/model.h
chunkEntity.hnswIndex(&InkCop::Knowledge::KnowledgeChunk::embedding)
    .dimensions(1536)  // 当前配置为1536维
    .distanceType(obx::VectorDistanceType::Cosine)
    .neighborsPerNode(30)
    .indexingSearchCount(200);
```

### 配置选择理由
- **1536维**是最常见的embedding模型维度
- 适配OpenAI、阿里云等主流模型
- 覆盖大部分生产环境使用场景

## 维度检测和验证

### 新增的检测方法

```cpp
// 检测当前embedding模型的实际维度
Q_INVOKABLE int detectEmbeddingDimension();

// 验证HNSW配置与实际维度的匹配性
Q_INVOKABLE QString validateHNSWConfiguration();
```

### 使用方式

#### 1. 检测当前维度
```javascript
const dimension = window.knowledgeApi.detectEmbeddingDimension();
console.log(`当前embedding维度: ${dimension}`);
```

#### 2. 验证配置匹配性
```javascript
const validationResult = window.knowledgeApi.validateHNSWConfiguration();
const result = JSON.parse(validationResult);

if (result.success) {
    console.log("✅ HNSW配置正确");
} else {
    console.log("⚠️ 需要调整配置:", result.recommendation);
}
```

## 配置调整指南

### 场景1：使用不同的embedding模型

如果您使用的embedding模型维度不是1536，需要修改配置：

```cpp
// 例如：使用384维模型
chunkEntity.hnswIndex(&InkCop::Knowledge::KnowledgeChunk::embedding)
    .dimensions(384)  // 修改为实际维度
    .distanceType(obx::VectorDistanceType::Cosine)
    .neighborsPerNode(30)
    .indexingSearchCount(200);
```

### 场景2：混合使用多种模型

如果应用中混合使用不同维度的模型，建议：

1. **统一维度**：选择一个主要模型，统一所有向量维度
2. **动态配置**：根据实际使用的模型动态调整HNSW配置
3. **维度转换**：实现向量维度标准化处理

### 场景3：本地GGUF模型

本地GGUF模型的维度取决于具体模型文件：

```bash
# 查看模型信息
./llama-cpp-python --model your-model.gguf --embedding --print-model-info
```

## 部署前检查清单

### 1. 维度验证
- [ ] 运行`detectEmbeddingDimension()`确认实际维度
- [ ] 运行`validateHNSWConfiguration()`验证配置匹配
- [ ] 确保测试环境和生产环境使用相同的embedding模型

### 2. 性能测试
- [ ] 测试向量搜索响应时间
- [ ] 验证搜索结果准确性
- [ ] 监控内存和CPU使用情况

### 3. 配置文档
- [ ] 记录使用的embedding模型和维度
- [ ] 更新部署文档
- [ ] 设置监控和告警

## 故障排除

### 常见问题

#### 1. 搜索结果不准确
**可能原因**：HNSW维度配置与实际向量维度不匹配
**解决方案**：运行维度检测，调整HNSW配置

#### 2. 搜索性能差
**可能原因**：向量维度过高或HNSW参数不当
**解决方案**：优化HNSW参数或考虑降维

#### 3. 部分向量无法搜索
**可能原因**：向量维度低于HNSW配置维度
**解决方案**：确保所有向量维度一致

### 调试工具

使用提供的测试工具进行诊断：
```bash
# 在InkCop应用中打开
file:///path/to/test-hnsw-dimension.html
```

## 最佳实践

1. **生产部署前必须验证维度配置**
2. **使用一致的embedding模型和配置**
3. **定期监控向量维度和搜索性能**
4. **保持HNSW配置与embedding模型同步更新**
5. **在测试环境中验证配置变更**

## 总结

HNSW维度配置是向量搜索系统的基础，必须与实际向量维度精确匹配。通过使用提供的检测和验证工具，可以确保配置正确，获得最佳的搜索性能和准确性。
