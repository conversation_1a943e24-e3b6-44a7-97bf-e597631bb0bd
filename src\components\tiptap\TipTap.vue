<template>
  <div v-if="editor" class="column no-wrap" @keydown="handleContainerKeydown" tabindex="-1">
    <EditorToolbar
      v-if="!hideToolbar"
      :editor="editor"
      :docId="docId"
      toolbarType="classic"
      class="border-bottom"
      :catalogVisible="catalogVisible[docId]"
    >
      <template #attach>
        <q-space />
        <div class="row items-center no-wrap">
          <!-- 错误状态时显示警告图标 -->
          <q-icon
            v-if="saveStatus === 'error'"
            name="warning"
            color="negative"
            size="20px"
            class="q-mr-xs"
          >
            <q-tooltip>{{ $t('src.components.tiptap.tipTap.saveFailedRetry') }}</q-tooltip>
          </q-icon>

          <!-- 保存按钮 - saved和error状态时显示 -->
          <q-btn
            v-if="saveStatus === 'saved' || saveStatus === 'error'"
            flat
            dense
            round
            icon="save"
            size="sm"
            @click="() => immediatelySave(props.title, props.folderId)"
            :color="saveStatus === 'error' ? 'negative' : 'grey-7'"
          >
            <q-tooltip>{{
              saveStatus === 'error'
                ? $t('src.components.tiptap.tipTap.reSave')
                : $t('src.components.tiptap.tipTap.save')
            }}</q-tooltip>
          </q-btn>

          <!-- 其他状态显示文字 -->
          <span
            v-else
            class="text-caption q-px-sm"
            :class="{
              'text-primary': saveStatus === 'saving',
              'text-grey-6': saveStatus === 'pending',
            }"
          >
            {{
              saveStatus === 'saving'
                ? $t('src.components.tiptap.tipTap.saving')
                : $t('src.components.tiptap.tipTap.pendingSave')
            }}
          </span>
        </div>
      </template>
    </EditorToolbar>
    <div class="row no-wrap q-space">
      <q-resize-observer @resize="onResize" />
      <div
        v-if="!isSnapshot && tocItems.length > 1"
        class="relative-position"
        :style="{ flex: `0 0 ${getCatalogState(instanceKey) ? catalogWidth : 0}px` }"
      >
        <div
          v-if="getCatalogState(instanceKey)"
          class="catalog-resize-handle z-fab column flex-center"
          @mousedown="startResize"
        />
        <q-icon
          :name="getCatalogState(instanceKey) ? 'chevron_left' : 'chevron_right'"
          size="1rem"
          :color="getCatalogState(instanceKey) ? 'primary' : 'grey-6'"
          class="absolute cursor-pointer z-max"
          style="bottom: 50%; right: -1.5rem"
          @click="toggleCatalog(instanceKey)"
        >
          <q-tooltip
            class="text-body1 border"
            :class="$q.dark.isActive ? 'bg-dark text-white' : 'bg-grey-3 text-black'"
          >
            {{
              getCatalogState(instanceKey)
                ? $t('src.components.tiptap.tipTap.hideCatalog')
                : $t('src.components.tiptap.tipTap.showCatalog')
            }}
          </q-tooltip>
        </q-icon>
        <q-scroll-area v-if="getCatalogState(instanceKey)" class="fit border-right">
          <div class="table-of-contents column gap-sm q-pa-md">
            <div
              v-for="item in tocItems"
              :key="item.id"
              :style="{ paddingLeft: `${(item.originalLevel - 1) * 0.5}rem` }"
              :class="{ 'is-active': item.id === activedTocItem?.id }"
              class="transition"
            >
              <a :href="`#${item.id}`" @click.prevent="scrollToHeading(item.id)">
                {{ item.textContent }}
              </a>
            </div>
          </div>
        </q-scroll-area>
      </div>
      <div class="q-space column no-wrap relative-position">
        <!-- 查找替换面板 -->
        <SearchReplacePanel
          :editor="editor"
          :visible="getSearchReplacePanelState(instanceKey)"
          :initial-search-term="initialSearchTerm"
          :instance-key="instanceKey"
          @close="
            () => {
              searchReplacePanelVisible.set(instanceKey, false);
              initialSearchTerm = '';
            }
          "
        />
        <q-scroll-area
          ref="editorScrollArea"
          class="q-space q-pb-md optimized-scroll"
          @scroll="getActivedTocItem"
        >
          <div
            class="q-px-lg column no-wrap tiptap"
            :style="`max-width: calc(${report.width - (getCatalogState(instanceKey) ? catalogWidth : 0)}px)`"
          >
            <div
              v-if="uiStore.perferences.editor.enableEmbeddTitle"
              ref="titleEditorRef"
              :contenteditable="!props.isSnapshot"
              class="q-px-md q-my-md doc-title editable-title text-h1"
              style="margin-left: 2rem; margin-right: 2rem; outline: none; min-height: 1.2em"
              :data-placeholder="
                docTitle ? '' : $t('src.components.tiptap.tipTap.enterDocumentTitle')
              "
              @input="onTitleInput"
              @keydown="onTitleKeydown"
              @blur="onTitleBlur"
              @paste="onTitlePaste"
              @compositionstart="onTitleCompositionStart"
              @compositionend="onTitleCompositionEnd"
            >
              {{ docTitle }}
            </div>
            <div class="editor-container" :style="{ fontSize: fontSize, lineHeight: lineHeight }">
              <editor-content
                :editor="editor"
                :catalogVisible="catalogVisible[docId]"
                class="q-px-md"
                :class="uiStore.perferences?.editor?.enableEmbeddTitle ? '' : 'q-pt-md'"
                :style="`min-height: ${report.height - 36}px`"
              />
              <bubble-menu
                :editor="editor"
                :tippy-options="{ duration: 100, maxWidth: 'none' }"
                :should-show="shouldShowBubbleMenu"
                :class="$q.dark.isActive ? 'bg-dark' : 'bg-light'"
                class="border radius-sm shadow-24 overflow-hidden"
              >
                <EditorToolbar
                  :editor="editor"
                  :docId="docId"
                  :catalogVisible="catalogVisible[docId]"
                  toolbarType="bubble"
                  class="no-wrap"
                />
              </bubble-menu>
              <DragHandle :editor="editor">
                <div class="custom-drag-handle" />
              </DragHandle>
            </div>
          </div>
        </q-scroll-area>
      </div>
      <SnapshotViewer
        v-if="!isSnapshot && getSnapshotViewerState(instanceKey)"
        :doc="doc"
        class="col-6"
        @snapshotCreated="getAndSetEditorContent"
        @close="snapshotViewerVisible.set(instanceKey, false)"
        @restore="applyContent"
        @updateDocument="updateDocument"
      />
    </div>
    <q-dialog v-model="showjson">
      <q-card bordered>
        {{ editor.getJSON() }}
      </q-card>
    </q-dialog>

    <!-- AI修改操作栏 -->
    <AIChangeActionBar />
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, watch, ref, computed, toRefs } from 'vue';
import { useEditor, EditorContent, VueNodeViewRenderer } from '@tiptap/vue-3';
import type { JSONContent } from '@tiptap/vue-3';
import type { Editor } from '@tiptap/core';
import EditorToolbar from './EditorToolbar.vue';
import { BubbleMenu } from '@tiptap/vue-3';
import CodeBlockComponent from './CodeBlockComponent.vue';
import type { NodeViewProps } from '@tiptap/vue-3';
import type { Component } from 'vue';
import { useQuasar } from 'quasar';
import 'highlight.js/styles/atom-one-dark.css';
import 'highlight.js/styles/atom-one-light.css';
import 'katex/dist/katex.min.css';

// 导入编辑器工厂方法
import { getStandardEditorExtensions } from 'src/utils/editorFactory';

import {
  catalogVisible,
  toggleCatalog,
  getCatalogState,
  getSnapshotViewerState,
  snapshotViewerVisible,
  getSaveStatus,
  cleanupSaveData,
  getSearchReplacePanelState,
  searchReplacePanelVisible,
  openSearchReplacePanel,
} from './useCommands';
import useCommands from './useCommands';
import { tiptap, addTableCommands } from './tiptap';
import { useDocumentActions } from 'src/composeables/useDocumentActions';
import type { EditorImageHandler } from './imageHandler';
import { createImageHandler } from './imageHandler';

import { useUtils } from 'src/composeables/useUtils';
import SnapshotViewer from './SnapshotViewer.vue';
import { useAutoComplete } from 'src/composeables/useAutoComplete';
import AIChangeActionBar from './AIChangeActionBar.vue';
import SearchReplacePanel from './SearchReplacePanel.vue';
import { DragHandle } from '@tiptap-pro/extension-drag-handle-vue-3';

import { useUiStore } from 'src/stores/ui';
import { useDocStore } from 'src/stores/doc';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });
const docStore = useDocStore();
const uiStore = useUiStore();

const emit = defineEmits<{
  (e: 'ready'): void;
  (e: 'contentChange', content: JSONContent): void;
}>();

const props = defineProps<{
  hideToolbar?: boolean;
  docId?: number;
  title?: string;
  folderId?: number;
  winId?: number;
  isSnapshot?: boolean;
  snapshotContent?: JSONContent;
}>();
const { snapshotContent } = toRefs(props);
const showjson = ref(false);
const initialSearchTerm = ref<string>('');

const instanceKey = computed(() => docStore.generateEditorInstanceKey(props.winId, props.docId));

const { updateTheme, setEditorDestroying } = tiptap();
const { onGetDocument } = useDocumentActions();

const { safeJsonParse } = useUtils();
const { perferences } = uiStore;

// 获取保存函数和状态以及图片处理函数
const {
  immediatelySave,
  smartWriteDocToDb,
  attachImage,
  handlePaste,
  convertImagesInContent,
  cleanupBlobUrlMappings,
} = computed(() => useCommands(props.docId)).value;
const saveStatus = computed(() => getSaveStatus(props.docId));

// 计算字号和行高的实际值
const fontSize = computed(() => {
  const baseSize = 16; // 基础字号
  const scale = perferences.editor.fontSize;
  return `${baseSize * scale}px`;
});

const lineHeight = computed(() => {
  const baseHeight = 1.5; // 基础行高
  const scale = perferences.editor.lineHeight;
  return baseHeight * scale;
});

const $q = useQuasar();

// 监听主题变化
watch(
  () => $q.dark.isActive,
  () => {
    updateTheme();
  },
  { immediate: true },
);

const report = ref({
  width: 0,
  height: 0,
});
function onResize(size: { width: number; height: number }) {
  report.value = size;
  // {
  //   width: 20 // width of container (in px)
  //   height: 50 // height of container (in px)
  // }
}

// 控制bubble-menu的显示条件
const shouldShowBubbleMenu = ({
  state,
  from,
  to,
}: {
  state: unknown;
  from: number;
  to: number;
}) => {
  const stateObj = state as {
    selection: { empty: boolean };
    doc: {
      slice: (
        from: number,
        to: number,
      ) => { content: { forEach: (callback: (node: { type: { name: string } }) => void) => void } };
    };
  };
  const { selection } = stateObj;
  const { empty } = selection;

  // 如果没有选中内容，不显示bubble-menu
  if (empty) {
    return false;
  }

  // 检查选中的内容是否只包含图片节点
  const selectedContent = stateObj.doc.slice(from, to);
  let hasOnlyImages = true;
  let hasContent = false;

  selectedContent.content.forEach((node: { type: { name: string } }) => {
    hasContent = true;
    if (node.type.name !== 'image') {
      hasOnlyImages = false;
    }
  });

  // 如果选中的内容只包含图片，不显示bubble-menu
  if (hasContent && hasOnlyImages) {
    return false;
  }

  // 其他情况显示bubble-menu
  return true;
};

// 目录项
const tocItems = ref<
  Array<{
    id: string;
    textContent: string;
    originalLevel: number;
    isActive: boolean;
  }>
>([]);

// 自定义层级索引生成函数
const getHierarchicalIndexes = (
  anchor: { level: number },
  previousAnchors: Array<{ level: number }>,
  level: number,
) => {
  const sameLevelAnchors = previousAnchors.filter((a) => a.level === level);
  return sameLevelAnchors.length + 1;
};
// 监听全局自动补全设置
const autoCompleteEnabled = computed(
  () => perferences.autoComplete.enabled && perferences.editor.enableAutoComplete,
);

// 自动补全相关
const { clearSuggestions } = useAutoComplete();

// ==================== 选择变化处理 ==============
/**
 * 处理编辑器选择变化
 */
const handleSelectionChangeInternal = async (editor: Editor) => {
  try {
    const { useLlmStore } = await import('src/stores/llm');
    const llmStore = useLlmStore();
    const { state } = editor;
    const { selection } = state;

    // 检查是否有选中文本
    if (selection.empty) {
      // 没有选中文本，清除选中文本状态
      llmStore.clearSelectedText();
    } else {
      // 有选中文本，获取选中的文本内容
      const selectedText = state.doc.textBetween(selection.from, selection.to);

      if (selectedText.trim()) {
        // 设置选中文本到store
        llmStore.setSelectedText(selectedText, props.docId, {
          from: selection.from,
          to: selection.to,
        });
      } else {
        // 选中的是空白内容，清除选中文本状态
        llmStore.clearSelectedText();
      }
    }
  } catch (error) {
    console.error('❌ [TipTap] 处理选择变化失败:', error);
  }
};

// -----------------------------初始化编辑器-----------------------------
const isLocked = ref(true);
const isContentSyncing = ref(false);
const isInitializing = ref(true); // 添加初始化标志

// 监听编辑器内容同步事件
const handleEditorContentSync = (event: CustomEvent) => {
  const { instanceKey: eventInstanceKey, isStart } = event.detail;
  if (eventInstanceKey === instanceKey.value) {
    isContentSyncing.value = isStart;
  }
};

// 图片处理器
let imageHandler: EditorImageHandler | null = null;

// 获取标准编辑器扩展配置
const editorExtensions = computed(() => {
  const extensions = getStandardEditorExtensions({
    docId: props.docId,
    enableAutoComplete: autoCompleteEnabled.value,
    documentTitle: props.title || '',
    placeholder: $t('src.components.tiptap.tipTap.startTypingContent'),
    onSuggestionSelected: (text: string) => {
      // 插入选中的建议文本
      editor.value?.chain().focus().insertContent(text).run();
      // 清理建议
      if (autoCompleteEnabled.value) {
        clearSuggestions();
      }
    },
  });

  // 需要特殊配置的扩展
  const customizedExtensions = extensions.map((extension) => {
    // 为 CodeBlockLowlight 添加自定义 NodeView
    if (extension.name === 'codeBlockLowlight') {
      return extension.extend({
        addNodeView() {
          return VueNodeViewRenderer(CodeBlockComponent as unknown as Component<NodeViewProps>);
        },
      });
    }

    // 为 TableOfContents 添加自定义配置
    if (extension.name === 'tableOfContents') {
      return extension.configure({
        HTMLAttributes: {
          class: 'table-of-contents',
        },
        onUpdate: (anchors) => {
          tocItems.value =
            anchors?.map((anchor) => ({
              id: anchor.id,
              textContent: anchor.textContent,
              originalLevel: anchor.originalLevel,
              isActive: anchor.isActive,
            })) || [];

          // 更新本地的 activedTocItem，使用扩展提供的 isActive 状态
          const activeAnchor = anchors?.find((anchor) => anchor.isActive);
          if (activeAnchor && !isClickCatalog.value) {
            activedTocItem.value = {
              id: activeAnchor.id,
              textContent: activeAnchor.textContent,
              originalLevel: activeAnchor.originalLevel,
              isActive: activeAnchor.isActive,
            };
          }
        },
        scrollParent: () =>
          editorScrollArea.value?.$el?.querySelector('.q-scrollarea__container') || window,
        anchorTypes: ['heading'],
        getLevel: (anchor) => anchor.level,
        getIndex: getHierarchicalIndexes,
      });
    }

    // 为 FileHandler 添加自定义配置
    if (extension.name === 'fileHandler') {
      return extension.configure({
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
        onDrop: (currentEditor, files, pos) => {
          files.forEach((file) => {
            void attachImage(currentEditor, file, pos);
          });
        },
      });
    }

    return extension;
  });

  return customizedExtensions;
});

const editor = useEditor({
  content: (props.isSnapshot && props.snapshotContent) || '',
  autofocus: true,
  editable: !props.isSnapshot && !isLocked.value,
  extensions: editorExtensions.value,
  editorProps: {
    handleKeyDown: (view, event) => {
      // 处理 Ctrl+A 全选快捷键 - 确保选中状态可见
      if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {
        // 让默认行为执行，但确保选中状态可见
        setTimeout(() => {
          // 强制重新渲染选中状态
          const selection = view.state.selection;
          if (selection.from !== selection.to) {
            // 有选中内容，确保样式正确应用
            const editorElement = view.dom;
            editorElement.classList.add('has-selection');

            // 移除之前可能存在的类，然后重新添加以触发样式更新
            setTimeout(() => {
              editorElement.classList.remove('has-selection');
              editorElement.classList.add('has-selection');
            }, 10);
          }
        }, 10);
        return false; // 让默认行为继续
      }

      // 处理 Ctrl+K 快捷键
      if (event.key === 'k' && (event.ctrlKey || event.metaKey)) {
        event.preventDefault();
        editor.value?.commands.insertAgentWriter();
        return true;
      }

      // 处理 Ctrl+S 快捷键 - 立即保存
      if (event.key === 's' && (event.ctrlKey || event.metaKey)) {
        event.preventDefault();
        immediatelySave(props.title, props.folderId);
        return true;
      }

      // 处理 Ctrl+F 查找快捷键
      if (event.key === 'f' && (event.ctrlKey || event.metaKey)) {
        event.preventDefault();

        // 获取当前选中的文本
        const selection = view.state.selection;
        let selectedText = '';
        if (!selection.empty) {
          selectedText = view.state.doc.textBetween(selection.from, selection.to);
        }

        // 检查搜索面板是否已经打开
        const isSearchPanelVisible = getSearchReplacePanelState(instanceKey.value);

        if (isSearchPanelVisible) {
          // 面板已经打开，直接更新搜索关键字
          if (selectedText.trim()) {
            // 有选中文本，设置为搜索关键字
            editor.value?.commands.setSearchTerm(selectedText.trim());
            // 重置索引到第一个结果
            editor.value?.commands.resetIndex();
          }
          // 如果没有选中文本，保持当前搜索状态不变
        } else {
          // 面板未打开，设置初始搜索词并打开面板
          if (selectedText.trim()) {
            initialSearchTerm.value = selectedText.trim();
          } else {
            initialSearchTerm.value = '';
          }
          // 打开搜索替换面板
          openSearchReplacePanel(instanceKey.value);
        }

        return true;
      }

      // 处理 ESC 键 - 关闭当前编辑器的搜索替换面板
      if (event.key === 'Escape') {
        const isSearchPanelVisible = getSearchReplacePanelState(instanceKey.value);
        if (isSearchPanelVisible) {
          event.preventDefault();
          event.stopPropagation();
          // 清除搜索效果
          editor.value?.commands.setSearchTerm('');
          editor.value?.commands.resetIndex();
          // 关闭面板
          searchReplacePanelVisible.value.set(instanceKey.value, false);
          initialSearchTerm.value = '';
          return true;
        }
      }

      return false;
    },
    handlePaste: (view, event) => {
      return handlePaste(view, event, props.isSnapshot);
    },
    // 添加选中状态变化处理
    handleDOMEvents: {
      selectionchange: (view) => {
        const selection = view.state.selection;
        const editorElement = view.dom;

        if (selection.from !== selection.to) {
          // 有选中内容
          editorElement.classList.add('has-selection');
        } else {
          // 无选中内容
          editorElement.classList.remove('has-selection');
        }

        return false;
      },
    },
  },
  onUpdate({ editor }) {
    if (props.isSnapshot) return;
    if (!editor.commands.mergeCells) {
      addTableCommands(editor);
    }

    // 只有在非滚动状态下才计算目录高亮
    if (!isScrolling.value) {
      getActivedTocItem();
    }

    // 发送内容更新事件
    const content = editor.getJSON();
    emit('contentChange', content);

    // 只有在非同步状态下才触发保存
    if (!isContentSyncing.value && !isInitializing.value) {
      console.log('触发更新时自动保存');

      void smartWriteDocToDb(props.title, props.folderId);
    }

    // HTTP图片处理现在由imageHandler自动处理
  },
  onCreate() {
    if (props.isSnapshot) {
      // 转换快照内容中的base64图片和image://id格式为blob URL
      void (async () => {
        // 创建图片处理器用于转换内容
        const tempImageHandler = createImageHandler(editor.value, props.docId);
        const processedContent = await tempImageHandler.convertImagesInContent(
          props.snapshotContent,
        );
        editor.value?.commands.setContent(processedContent);
      })();
      return;
    }

    // 初始化图片处理器
    if (editor.value && props.docId !== undefined) {
      imageHandler = createImageHandler(editor.value, props.docId);
      imageHandler.init();
    }

    // 使用新的实例管理 API
    if (props.winId !== undefined && props.docId !== undefined) {
      docStore.registerEditorInstance(props.winId, props.docId, editor.value);
      // console.log('Registered editor instance:', newInstanceKey);
    }

    void getAndSetEditorContent();

    // 添加同步事件监听器
    window.addEventListener('editorContentSync', handleEditorContentSync);

    emit('ready');
    // 劫持 destroy 方法，在销毁前阻止节点的销毁事件
    // 防止在关闭编辑器时触发节点销毁事件
    // 图片被删除时触发节点销毁关联删除数据库中图片，因此不能在关闭编辑器时触发节点的销毁事件，否则所有图片都会从数据库中删除
    const originalDestroy = this.destroy;
    editor.value.destroy = function (...args) {
      setEditorDestroying(instanceKey.value, true);

      // 清理图片处理器
      if (imageHandler) {
        imageHandler.destroy();
        imageHandler = null;
      }

      return originalDestroy.apply(this, args);
    };
  },
  onPaste() {
    if (props.isSnapshot) return;
    // 确保在粘贴后更新，但只在非滚动状态下
    if (!isScrolling.value) {
      getActivedTocItem();
    }
  },
  onTransaction() {
    // 只在非滚动状态下计算目录高亮，减少频繁调用
    if (!isScrolling.value) {
      getActivedTocItem();
    }
  },
  onSelectionUpdate({ editor }) {
    if (props.isSnapshot) return;

    // 处理选择变化
    void handleSelectionChange(editor);

    // 自动补全扩展会处理面板位置更新
  },
});

// 编辑器初始化之后，获取最新内容
const doc = ref(null);
const docTitle = ref('');
const titleEditorRef = ref<HTMLDivElement | null>(null);
const isEditingTitle = ref(false);
const isComposingTitle = ref(false); // 中文输入法组合输入状态
const getAndSetEditorContent = async () => {
  isInitializing.value = true;
  // console.log('开始获取文档内容， 锁定编辑器');
  doc.value = await onGetDocument(props.docId);
  docTitle.value = doc.value?.title || '';
  docStore.syncDocuments(doc.value);
  if (doc.value.content) {
    docStore.setLlmDocumentsMap(doc.value);
    docStore.setLlmDocumentKey(doc.value.id);
    const content = safeJsonParse<JSONContent>(doc.value.content, { type: 'doc', content: [] });

    // 转换文档中的图片引用为blob URL
    if (imageHandler) {
      const processedContent = await imageHandler.convertImagesInContent(content);
      editor.value?.commands.setContent(processedContent);
    } else {
      // 降级处理：使用旧的转换方法
      const processedContent = await convertImagesInContent(content);
      editor.value?.commands.setContent(processedContent);
    }
  }

  editor.value?.setEditable(true);
  isLocked.value = false;
  isInitializing.value = false;
};

const updateDocument = (_doc: unknown) => {
  doc.value = _doc;
};

const applyContent = async (content: JSONContent) => {
  // 转换文档中的图片引用为blob URL
  if (imageHandler) {
    const processedContent = await imageHandler.convertImagesInContent(content);
    editor.value?.commands.setContent(processedContent);
  } else {
    // 降级处理：使用旧的转换方法
    const processedContent = await convertImagesInContent(content);
    editor.value?.commands.setContent(processedContent);
  }
};

// ==================== 标题编辑相关方法 ====================

/**
 * 处理中文输入法开始组合输入
 */
const onTitleCompositionStart = () => {
  isComposingTitle.value = true;
};

/**
 * 处理中文输入法结束组合输入
 */
const onTitleCompositionEnd = (event: CompositionEvent) => {
  isComposingTitle.value = false;
  // 组合输入结束后更新标题
  const target = event.target as HTMLDivElement;
  const newTitle = target.textContent || '';
  docTitle.value = newTitle;
  isEditingTitle.value = true;
};

/**
 * 处理标题输入事件
 */
const onTitleInput = (event: Event) => {
  // 如果正在进行中文输入法组合输入，不更新docTitle，避免干扰输入
  if (isComposingTitle.value) {
    return;
  }

  const target = event.target as HTMLDivElement;
  const newTitle = target.textContent || '';
  docTitle.value = newTitle;
  isEditingTitle.value = true;
};

/**
 * 处理标题键盘事件
 */
const onTitleKeydown = (event: KeyboardEvent) => {
  // 如果正在进行中文输入法组合输入，不处理快捷键
  if (isComposingTitle.value) {
    return;
  }

  // 按下回车键时保存标题并失焦
  if (event.key === 'Enter') {
    event.preventDefault();
    (event.target as HTMLDivElement).blur();
  }

  // 按下Escape键时取消编辑
  if (event.key === 'Escape') {
    event.preventDefault();
    cancelTitleEdit();
  }
};

/**
 * 处理标题失焦事件
 */
const onTitleBlur = async () => {
  // 如果正在进行中文输入法组合输入，延迟处理失焦事件
  if (isComposingTitle.value) {
    // 等待组合输入结束
    setTimeout(() => {
      void (async () => {
        if (isEditingTitle.value && !isComposingTitle.value) {
          await saveTitleChanges();
        }
      })();
    }, 100);
    return;
  }

  if (isEditingTitle.value) {
    await saveTitleChanges();
  }
};

/**
 * 处理标题粘贴事件
 */
const onTitlePaste = (event: ClipboardEvent) => {
  event.preventDefault();

  // 获取纯文本内容
  const text = event.clipboardData?.getData('text/plain') || '';

  // 移除换行符和多余空格
  const cleanText = text.replace(/[\r\n]+/g, ' ').trim();

  // 使用现代方法插入纯文本
  const selection = window.getSelection();
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    range.deleteContents();
    range.insertNode(document.createTextNode(cleanText));
    range.collapse(false);
    selection.removeAllRanges();
    selection.addRange(range);
  }

  // 触发input事件以更新docTitle
  const inputEvent = new Event('input', { bubbles: true });
  (event.target as HTMLDivElement).dispatchEvent(inputEvent);
};

/**
 * 保存标题修改
 */
const saveTitleChanges = async () => {
  if (!props.docId || props.isSnapshot || !doc.value) return;

  const newTitle = docTitle.value.trim();
  const oldTitle = doc.value.title || '';

  // 如果标题没有变化，直接返回
  if (newTitle === oldTitle) {
    isEditingTitle.value = false;
    return;
  }

  try {
    // 导入必要的模块
    const { useDocumentActions } = await import('src/composeables/useDocumentActions');
    const { onRenameDocument } = useDocumentActions();

    // 重命名文档
    const updatedDoc = await onRenameDocument(props.docId, newTitle);

    if (!updatedDoc.error) {
      // 更新本地文档对象
      doc.value = updatedDoc;
      docTitle.value = updatedDoc.title || '';

      // 更新store中的文件树
      docStore.updateDocumentInTree(updatedDoc);

      // 同步所有窗口中的文档信息
      docStore.syncDocuments(updatedDoc);

      console.log(`标题修改成功: "${oldTitle}" -> "${newTitle}"`);
    } else {
      console.error('标题修改失败:', updatedDoc.error);
      // 恢复原标题
      docTitle.value = oldTitle;
      if (titleEditorRef.value) {
        titleEditorRef.value.textContent = oldTitle;
      }
    }
  } catch (error) {
    console.error('标题修改失败:', error);
    // 恢复原标题
    docTitle.value = oldTitle;
    if (titleEditorRef.value) {
      titleEditorRef.value.textContent = oldTitle;
    }
  } finally {
    isEditingTitle.value = false;
  }
};

/**
 * 取消标题编辑
 */
const cancelTitleEdit = () => {
  if (!doc.value) return;

  const originalTitle = doc.value.title || '';
  docTitle.value = originalTitle;
  if (titleEditorRef.value) {
    titleEditorRef.value.textContent = originalTitle;
  }
  isEditingTitle.value = false;
  titleEditorRef.value?.blur();
};

watch(
  () => snapshotContent.value,
  () => {
    if (props.isSnapshot) {
      // 转换快照内容中的base64图片和image://id格式为blob URL
      void (async () => {
        const processedContent = await convertImagesInContent(snapshotContent.value);
        editor.value?.commands.setContent(processedContent);
      })();
    }
  },
);

// 监听设置变化并应用到编辑器
watch(
  [fontSize, lineHeight],
  () => {
    if (editor.value) {
      const style = editor.value.view.dom.style;
      style.fontSize = fontSize.value;
      style.lineHeight = lineHeight.value.toString();
    }
  },
  { immediate: true },
);

const isClickCatalog = ref(false);
const clickTocItemId = ref();
const clickTimeout = ref<NodeJS.Timeout | null>(null);

// 滚动到标题
const scrollToHeading = (id: string) => {
  if (!editor.value) return;

  // 清除之前的点击超时
  if (clickTimeout.value) {
    clearTimeout(clickTimeout.value);
  }

  isClickCatalog.value = true;
  clickTocItemId.value = id;

  const element = editor.value.view.dom.querySelector(`[data-toc-id='${id}']`);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });

    // 立即设置高亮为点击的条目
    activedTocItem.value = tocItems.value.find((item) => item.id === id) || null;

    // 设置延迟，确保滚动完成后仍然高亮点击的条目
    clickTimeout.value = setTimeout(() => {
      // 再次确保高亮的是点击的条目，防止滚动过程中被其他逻辑覆盖
      activedTocItem.value = tocItems.value.find((item) => item.id === id) || null;
      isClickCatalog.value = false;
      clickTocItemId.value = null;
    }, 800); // 给足够时间让滚动动画完成
  }
};

const editorScrollArea = ref(null);
const activedTocItem = ref(null);

// 防抖工具函数
const debounce = <T extends (...args: unknown[]) => unknown>(func: T, wait: number): T => {
  let timeout: NodeJS.Timeout | null = null;
  return ((...args: Parameters<T>) => {
    const later = () => {
      timeout = null;
      func(...args);
    };
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  }) as T;
};

// 滚动状态管理
const isScrolling = ref(false);
let scrollStopTimer: NodeJS.Timeout | null = null;

// 备用的目录高亮计算函数（当 TableOfContents 扩展的内置高亮失效时使用）
const calculateActiveTocItem = () => {
  // 如果是点击触发的，不执行正常的滚动高亮逻辑
  if (isClickCatalog.value) {
    return;
  }

  const scrollArea = editorScrollArea.value;
  if (!scrollArea || !editor.value || !tocItems.value.length) return;

  try {
    const { verticalContainerSize } = scrollArea.getScroll();
    const scrollAreaRect = scrollArea.$el.getBoundingClientRect();

    let activeItem = null;
    let minDistance = Infinity;

    // 遍历 tocItems，找到最接近视口顶部的标题
    for (const item of tocItems.value) {
      const heading = editor.value.view.dom.querySelector(`[data-toc-id='${item.id}']`);
      if (!heading) continue;

      const rect = heading.getBoundingClientRect();
      const headingTop = rect.top - scrollAreaRect.top;

      // 计算标题距离视口顶部的距离
      const distance = Math.abs(headingTop);

      // 如果标题在视口内或刚刚滚过视口顶部，且距离最小
      if (headingTop <= verticalContainerSize * 0.3 && distance < minDistance) {
        minDistance = distance;
        activeItem = item;
      }
    }

    // 更新高亮项
    if (activeItem && !isClickCatalog.value) {
      activedTocItem.value = activeItem;
      // console.log('备用函数设置高亮:', activeItem.textContent);
    }
  } catch (error) {
    console.warn('计算目录高亮时出错:', error);
  }
};

// 滚动停止后计算目录高亮
const getActivedTocItem = () => {
  // 如果是点击触发的，不执行正常的滚动高亮逻辑
  if (isClickCatalog.value) {
    return;
  }

  // 标记正在滚动
  isScrolling.value = true;

  // 清除之前的定时器
  if (scrollStopTimer) {
    clearTimeout(scrollStopTimer);
  }

  // 设置定时器，滚动停止后再计算高亮
  scrollStopTimer = setTimeout(() => {
    isScrolling.value = false;

    // 使用 requestAnimationFrame 确保在下一帧执行，避免阻塞
    requestAnimationFrame(() => {
      // 检查 TableOfContents 扩展是否正确设置了高亮
      const hasActiveFromExtension = tocItems.value.some((item) => item.isActive);

      // 如果扩展没有设置高亮，使用备用计算函数
      if (!hasActiveFromExtension) {
        // console.log('扩展未设置高亮，使用备用计算函数');
        calculateActiveTocItem();
      }
    });
  }, 150); // 滚动停止150ms后计算高亮
};

// 使用防抖优化选择变化处理
const handleSelectionChange = debounce(handleSelectionChangeInternal, 50);

// 正文中存在标题时，显示目录面板,计算高亮
const tocItemsCount = computed(() => tocItems.value?.length);
watch(
  tocItemsCount,
  () => {
    if (
      tocItemsCount.value > 1 && //标题数量大于1
      !getCatalogState(instanceKey.value) && //目录面板未打开
      !getSnapshotViewerState(instanceKey.value) //快照面板未打开
    ) {
      toggleCatalog(instanceKey.value);
    }
  },
  { immediate: true },
);

// 添加目录面板宽度相关的状态
const catalogWidth = ref(240);
const isResizing = ref(false);
const startX = ref(0);
const startWidth = ref(0);

// 开始拖拽调整宽度
const startResize = (e: MouseEvent) => {
  isResizing.value = true;
  startX.value = e.clientX;
  startWidth.value = catalogWidth.value;

  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);
};

// 处理拖拽调整宽度
const handleResize = (e: MouseEvent) => {
  if (!isResizing.value) return;

  const deltaX = e.clientX - startX.value; // 修改为正值增加宽度
  const newWidth = Math.min(
    Math.max(startWidth.value + deltaX, Math.min(report.value.width / 6, 100)),
    report.value.width / 2,
  );
  catalogWidth.value = newWidth;
};

// 停止拖拽调整宽度
const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
};

// 组件销毁时清理事件监听
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
  // 清理同步事件监听器
  window.removeEventListener('editorContentSync', handleEditorContentSync);
  // 清理点击超时
  if (clickTimeout.value) {
    clearTimeout(clickTimeout.value);
    clickTimeout.value = null;
  }
  // 清理滚动停止定时器
  if (scrollStopTimer) {
    clearTimeout(scrollStopTimer);
    scrollStopTimer = null;
  }

  // 使用新的实例管理方法清理编辑器实例
  if (props.winId !== undefined && props.docId !== undefined) {
    docStore.unregisterEditorInstance(props.winId, props.docId);
  } else {
    editor.value?.destroy();
  }
  // 清理销毁状态
  setEditorDestroying(instanceKey.value, false);

  // 清理保存相关数据
  cleanupSaveData(props.docId);

  // 清理blob URL映射
  void cleanupBlobUrlMappings();
});

// 监听全局自动补全设置变化
watch(
  autoCompleteEnabled,
  (newEnabled) => {
    if (editor.value) {
      // 使用命令更新AutoComplete扩展的启用状态
      editor.value.commands.updateAutoCompleteOptions({ enabled: newEnabled });

      // 如果禁用自动补全，清除当前建议
      if (!newEnabled) {
        clearSuggestions();
      }
    }
  },
  { immediate: true },
);

// 添加编辑器容器的键盘事件监听
const handleContainerKeydown = (event: KeyboardEvent) => {
  // 只处理ESC键
  if (event.key === 'Escape') {
    const isSearchPanelVisible = getSearchReplacePanelState(instanceKey.value);
    if (isSearchPanelVisible) {
      event.preventDefault();
      event.stopPropagation();
      // 清除搜索效果
      editor.value?.commands.setSearchTerm('');
      editor.value?.commands.resetIndex();
      // 关闭面板
      searchReplacePanelVisible.value.set(instanceKey.value, false);
      initialSearchTerm.value = '';
    }
  }
};
</script>

<style scoped>
/* 滚动性能优化 */
.optimized-scroll {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  will-change: scroll-position;
  /* 使用GPU加速合成层 */
  backface-visibility: hidden;
  /* 优化重绘性能 */
  contain: layout style paint;
}

/* 编辑器容器优化 */
.editor-container {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化重绘 */
  contain: layout style;
}

/* 目录项过渡优化 */
.table-of-contents .transition {
  /* 使用transform而不是其他属性进行动画 */
  transition:
    transform 0.2s ease,
    opacity 0.2s ease;
  /* 启用硬件加速 */
  will-change: transform, opacity;
}

/* 减少重绘的元素 */
.table-of-contents a {
  /* 避免文本选择导致的重绘 */
  user-select: none;
  /* 优化文本渲染 */
  text-rendering: optimizeSpeed;
}

/* 可编辑标题样式 */
.editable-title {
  /* 基础样式 */
  border: none;
  background: transparent;
  resize: none;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-word;

  /* 占位符样式 */
  position: relative;
}

.editable-title:empty::before {
  content: attr(data-placeholder);
  color: #9e9e9e;
  pointer-events: none;
  position: absolute;
  left: 0;
  top: 0;
}

.body--dark .editable-title:empty::before {
  color: #616161;
}
</style>
