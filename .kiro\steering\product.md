# InkCop Product Overview

InkCop is an AI-powered writing assistant built as a cross-platform desktop application. It combines a modern web-based frontend with a native Qt backend to provide writers with intelligent assistance, knowledge management, and rich text editing capabilities.

## Core Features

- **AI-Powered Writing**: Integrated LLM support for writing assistance with multiple model backends
- **Rich Text Editor**: TipTap-based editor with advanced formatting, tables, mathematics, and collaborative features
- **Knowledge Management**: Built-in knowledge base with vector search and semantic chunking
- **Cross-Platform**: Native desktop app for Windows, Linux, and macOS
- **Offline Capable**: Embedded ObjectBox database with optional local GGUF model support
- **Modern UI**: Vue.js + Quasar Framework interface with dark/light theme support

## Target Users

Writers, content creators, researchers, and knowledge workers who need AI assistance with writing tasks while maintaining control over their data and working offline when needed.

## Architecture

Hybrid desktop application with Qt WebEngine hosting a Vue.js frontend, providing native OS integration while maintaining modern web UI capabilities.
