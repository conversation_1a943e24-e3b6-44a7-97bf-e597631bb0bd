# InkCop Technology Stack

## Frontend Stack

- **Framework**: Vue 3 with Composition API
- **UI Library**: Quasar Framework v2.16+ with Material Design icons
- **Editor**: TipTap v2.14+ with Pro extensions (drag-handle, mathematics, export/import)
- **State Management**: Pinia stores
- **Internationalization**: Vue I18n
- **Build Tool**: Vite via Quasar CLI
- **Package Manager**: Bun (preferred) or npm

## Backend Stack

- **Native Framework**: Qt 6.7.3 with QtWebEngine
- **Database**: ObjectBox (embedded NoSQL)
- **AI/ML**: LangChain.js, OpenAI API, optional local GGUF models via llama.cpp
- **Build System**: CMake with Visual Studio 2022 (Windows)

## Key Libraries

- **Text Processing**: marked (Markdown), highlight.js (syntax highlighting)
- **Media**: Pexels API integration, Excalidraw for diagrams
- **Utilities**: axios (HTTP), nanoid (IDs), better-sqlite3 (fallback DB)

## Build Commands

### Frontend Development

```bash
# Install dependencies
bun install

# Development server (hot reload)
bun run dev

# Production build
bun run build

# Linting and formatting
bun run lint
bun run format
```

### Qt Application

```powershell
# Windows development build (with console output)
.\win-dev.ps1

# Windows production build
.\win-prod.ps1

# Create installer
.\build-installer.ps1 -Type MSIX
```

### Linux

```bash
# Development build
./build-qt-dev.sh

# Production build
./build-qt.sh
```

## Development Environment

- **Qt Version**: 6.7.3 MSVC 2022 (Windows)
- **Node.js**: v18+ (for frontend tooling)
- **Visual Studio**: 2022 Community (Windows builds)
- **CMake**: 3.16+ for Qt builds

## Configuration Files

- `quasar.config.ts`: Frontend build configuration
- `CMakeLists.txt`: Qt application build configuration
- `eslint.config.js`: Code linting rules
- `tsconfig.json`: TypeScript configuration
- `.env.production`: Production environment variables
