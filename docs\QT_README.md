# InkCop Qt桌面应用

这是一个使用Qt框架包装Quasar应用的桌面版本，让您可以将Web应用作为原生桌面应用运行。

## 🎯 功能特性

- **原生桌面体验**: 使用Qt WebEngineView提供流畅的桌面应用体验
- **完整菜单栏**: 提供文件、视图、帮助等标准桌面应用菜单
- **快捷键支持**: 支持常用快捷键（刷新、缩放、全屏等）
- **开发者工具**: 内置Web开发者工具支持（F12）
- **自适应窗口**: 智能窗口大小和位置管理
- **状态栏**: 显示加载进度和应用状态

## 📋 系统要求

### 必需依赖

- **Qt6**: Qt6.2或更高版本，包含WebEngine模块
- **CMake**: 3.16或更高版本
- **Node.js**: 用于构建Quasar应用
- **C++编译器**: 支持C++17的编译器（GCC 8+, Clang 7+, MSVC 2019+）

### Linux系统安装命令

#### Ubuntu/Debian

```bash
sudo apt-get update
sudo apt-get install qt6-base-dev qt6-webengine-dev cmake build-essential
```

#### Fedora

```bash
sudo dnf install qt6-qtbase-devel qt6-qtwebengine-devel cmake gcc-c++
```

#### Arch Linux

```bash
sudo pacman -S qt6-base qt6-webengine cmake gcc
```

### macOS

```bash
brew install qt6 cmake
```

### Windows

1. 安装 [Qt6](https://www.qt.io/download-qt-installer)
2. 安装 [CMake](https://cmake.org/download/)
3. 安装 Visual Studio 2019或更高版本

## 🚀 快速开始

### 自动构建（推荐）

```bash
# 一键构建所有内容
./build-qt.sh

# 运行应用
./build/bin/InkCop
```

### 手动构建

#### 1. 构建Quasar应用

```bash
# 安装依赖
npm install

# 构建为SPA
npm run build
```

#### 2. 构建Qt应用

```bash
# 创建构建目录
mkdir build && cd build

# 配置CMake
cmake ..

# 编译
make -j$(nproc)

# 运行
./bin/InkCop
```

### 其他构建选项

```bash
# 仅构建Quasar应用
./build-qt.sh quasar-only

# 仅构建Qt应用（需要先构建Quasar）
./build-qt.sh qt-only

# 清理所有构建文件
./build-qt.sh clean
```

## 🎮 使用说明

### 菜单功能

#### 文件菜单

- **重新加载 (F5)**: 重新加载Web应用
- **退出 (Ctrl+Q)**: 退出应用程序

#### 视图菜单

- **全屏 (F11)**: 切换全屏模式
- **放大 (Ctrl++)**: 放大页面内容
- **缩小 (Ctrl+-)**: 缩小页面内容
- **重置缩放 (Ctrl+0)**: 恢复默认缩放
- **开发者工具 (F12)**: 打开Web开发者工具

#### 帮助菜单

- **关于**: 显示应用程序信息

### 快捷键

| 功能       | 快捷键       |
| ---------- | ------------ |
| 重新加载   | F5 或 Ctrl+R |
| 全屏切换   | F11          |
| 退出应用   | Ctrl+Q       |
| 放大页面   | Ctrl++       |
| 缩小页面   | Ctrl+-       |
| 重置缩放   | Ctrl+0       |
| 开发者工具 | F12          |

## 🔧 配置选项

### Web应用路径

Qt应用会按以下顺序查找Quasar构建文件：

1. `./build/bin/webapp/index.html` (运行时目录)
2. `./dist/spa/index.html` (项目dist目录)
3. `../../dist/spa/index.html` (相对于构建目录的项目dist目录)

### 自定义配置

您可以在 `qt-src/mainwindow.cpp` 中修改以下设置：

- 窗口大小和位置
- WebEngine设置
- 菜单和工具栏布局
- 快捷键绑定

## 🐛 故障排除

### 常见问题

#### "未找到Web应用文件"错误

**解决方案**: 确保已经构建了Quasar应用

```bash
npm run build
```

#### Qt6找不到或WebEngine模块缺失

**解决方案**: 确保安装了完整的Qt6开发包，包括WebEngine模块

```bash
# Ubuntu/Debian
sudo apt-get install qt6-webengine-dev

# Fedora
sudo dnf install qt6-qtwebengine-devel
```

#### CMake配置失败

**解决方案**: 检查CMake版本和Qt6环境变量

```bash
cmake --version  # 应该 >= 3.16
echo $CMAKE_PREFIX_PATH  # 应该包含Qt6路径
```

#### 编译错误

**解决方案**: 确保C++编译器支持C++17

```bash
gcc --version  # GCC应该 >= 8.0
clang --version  # Clang应该 >= 7.0
```

### 开发调试

#### 启用详细输出

```bash
export QT_LOGGING_RULES="qt.webenginecontext.debug=true"
./build/bin/InkCop
```

#### 调试WebEngine

1. 按F12打开开发者工具
2. 或者在应用运行时访问: `http://localhost:9222`

## 📦 打包分发

### Linux AppImage

```bash
# 安装linuxdeploy工具
wget https://github.com/linuxdeploy/linuxdeploy/releases/download/continuous/linuxdeploy-x86_64.AppImage
chmod +x linuxdeploy-x86_64.AppImage

# 创建AppImage
./linuxdeploy-x86_64.AppImage --appdir AppDir --executable build/bin/InkCop --create-desktop-file --output appimage
```

### macOS应用包

```bash
# 创建app bundle
macdeployqt build/bin/InkCop.app -dmg
```

### Windows安装程序

使用CMake的CPack或NSIS创建Windows安装程序。

## 🤝 贡献

欢迎提交问题和功能请求！

## 📄 许可证

本项目采用与主项目相同的许可证。

## 🔗 相关链接

- [Qt官方网站](https://www.qt.io/)
- [Qt WebEngine文档](https://doc.qt.io/qt-6/qtwebengine-index.html)
- [Quasar框架](https://quasar.dev/)
- [CMake文档](https://cmake.org/documentation/)
