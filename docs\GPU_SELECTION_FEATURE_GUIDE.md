# GPU选择功能使用指南

## 功能概述

新增的GPU选择功能允许用户在使用本地GGUF大模型时，选择特定的GPU设备来执行推理任务。该功能包括：

1. **自动GPU检测** - 组件加载时自动检测系统中可用的GPU设备
2. **设备选择界面** - 提供直观的单选界面让用户选择硬件设备
3. **配置持久化** - 用户选择自动保存到数据库
4. **状态显示** - 实时显示当前选择的GPU设备状态
5. **完整测试** - 提供测试按钮验证功能完整性

## 使用步骤

### 1. 访问设置页面
- 打开应用程序
- 进入设置页面
- 选择"知识库配置"选项

### 2. 检测GPU设备
- 组件会在加载时自动执行GPU检测
- 也可以手动点击"检测 GPU 能力"按钮
- 系统会显示检测结果和可用设备列表

### 3. 选择GPU设备
- 在"GPU设备选择"区域中选择要使用的硬件：
  - **CPU选项**: 仅使用CPU进行推理（兼容性最好，速度较慢）
  - **GPU选项**: 使用指定GPU进行推理（速度快，需要足够显存）
- 每个GPU选项会显示：
  - 设备名称和后端类型
  - 最大支持层数和推荐层数
  - 使用建议

### 4. 配置验证
- 选择设备后，系统会自动保存配置
- 在模型状态区域可以看到当前选择的设备状态
- 可以点击"测试GPU功能"按钮验证配置正确性

### 5. 使用本地模型
- 确保已选择GGUF模型文件
- 点击"加载模型"按钮
- 系统会使用选择的GPU设备加载和运行模型
- 可以通过"测试本地模型"验证GPU加速效果

## 功能特性

### 自动检测和配置
- 组件加载时自动检测GPU能力
- 智能选择默认GPU设备（如果可用）
- 自动更新推荐的GPU层数配置

### 完善的回退机制
- GPU不可用时自动提供CPU选项
- 检测失败时提供详细的错误信息和解决建议
- 支持云端API和本地模型的灵活切换

### 配置持久化
- 用户选择自动保存到数据库
- 应用重启后自动恢复上次配置
- 支持配置的深度合并和验证

### 详细的状态反馈
- 实时显示GPU设备检测状态
- 清晰的设备信息展示（名称、后端、性能参数）
- 模型加载和测试过程的进度反馈

## 技术实现

### 前端组件增强
- 添加GPU设备状态管理
- 实现设备选择UI组件
- 集成自动检测和测试功能

### 类型定义扩展
- 新增`GpuDeviceInfo`接口定义GPU设备信息
- 扩展`KnowledgeBaseSettings`支持`selectedGpuDevice`字段
- 更新默认配置以包含GPU设备选择

### 配置系统集成
- 扩展UI Store的配置验证逻辑
- 支持本地模式下的额外配置检查
- 实现配置的自动保存和恢复

### 测试和验证
- 提供完整的功能测试流程
- 模拟设备切换验证配置生效
- 集成模型加载和推理测试

## 注意事项

### 当前限制
1. **后端API扩展**: 当前后端API尚不支持多GPU设备选择，前端已做好准备，等待后端扩展
2. **设备枚举**: 目前只检测主要GPU设备，未来可扩展为检测多个GPU设备
3. **动态切换**: 模型运行时暂不支持动态切换GPU设备

### 系统要求
- 支持CUDA的NVIDIA GPU（推荐）
- 足够的GPU显存（取决于模型大小）
- 正确安装的GPU驱动程序和CUDA运行时

### 性能建议
- 对于16GB以下显存的GPU，建议使用云端API
- 大型模型建议选择高端GPU设备
- CPU模式适用于测试和兼容性场景

## 故障排除

### GPU检测失败
- 检查GPU驱动程序是否正确安装
- 确认CUDA运行时库在系统PATH中
- 验证llama.cpp编译时包含CUDA支持

### 模型加载失败
- 确认选择了有效的GGUF模型文件
- 检查GPU显存是否足够
- 尝试减少GPU层数设置

### 配置不生效
- 点击"测试GPU功能"按钮验证配置
- 检查浏览器控制台的详细日志
- 重新检测GPU设备并重新选择

## 未来扩展计划

1. **多GPU支持**: 扩展后端API以支持多GPU设备枚举和选择
2. **显存监控**: 添加GPU显存使用情况的实时监控
3. **性能基准**: 提供不同GPU配置的性能测试和比较
4. **动态调整**: 支持运行时动态调整GPU设备和层数配置
5. **智能推荐**: 基于模型大小和GPU性能自动推荐最佳配置

---

该功能为InkCop应用的本地GGUF模型支持提供了重要的硬件选择能力，将显著提升用户的本地AI推理体验。