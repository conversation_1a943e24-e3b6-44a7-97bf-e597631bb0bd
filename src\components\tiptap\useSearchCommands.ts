import type { Editor } from '@tiptap/vue-3';
import { ref, computed } from 'vue';

/**
 * 搜索替换命令模块
 * 负责文档内搜索、替换、查找定位等功能
 */

// 搜索状态接口
export interface SearchState {
  query: string;
  replacement: string;
  caseSensitive: boolean;
  wholeWord: boolean;
  useRegex: boolean;
  currentMatch: number;
  totalMatches: number;
  isSearching: boolean;
  matches: Array<{
    from: number;
    to: number;
    text: string;
  }>;
}

// 搜索替换面板状态管理
export const searchReplacePanelVisible = ref<Map<string, boolean>>(new Map());

// 每个编辑器实例的搜索状态
const searchStates = ref<Map<string, SearchState>>(new Map());

/**
 * 初始化搜索状态
 */
const initSearchState = (): SearchState => {
  return {
    query: '',
    replacement: '',
    caseSensitive: false,
    wholeWord: false,
    useRegex: false,
    currentMatch: 0,
    totalMatches: 0,
    isSearching: false,
    matches: [],
  };
};

/**
 * 获取搜索状态
 */
export const getSearchState = (instanceKey: string): SearchState => {
  if (!searchStates.value.has(instanceKey)) {
    searchStates.value.set(instanceKey, initSearchState());
  }
  return searchStates.value.get(instanceKey) as SearchState;
};

/**
 * 设置搜索状态
 */
export const setSearchState = (instanceKey: string, state: Partial<SearchState>) => {
  const currentState = getSearchState(instanceKey);
  searchStates.value.set(instanceKey, { ...currentState, ...state });
};

/**
 * 初始化搜索替换面板状态
 */
export const initSearchReplaceState = (instanceKey: string) => {
  if (!searchReplacePanelVisible.value.has(instanceKey)) {
    searchReplacePanelVisible.value.set(instanceKey, false);
  }
};

/**
 * 获取搜索替换面板状态
 */
export const getSearchReplaceState = (instanceKey: string): boolean => {
  return searchReplacePanelVisible.value.get(instanceKey) ?? false;
};

/**
 * 切换搜索替换面板状态
 */
export const toggleSearchReplace = (instanceKey: string) => {
  const currentState = searchReplacePanelVisible.value.get(instanceKey) ?? false;
  searchReplacePanelVisible.value.set(instanceKey, !currentState);
  
  // 如果打开面板，初始化搜索状态
  if (!currentState) {
    const searchState = getSearchState(instanceKey);
    searchState.query = '';
    searchState.currentMatch = 0;
    searchState.totalMatches = 0;
    searchState.matches = [];
  }
};

/**
 * 创建搜索功能
 */
export const createSearchFunction = (
  getCurrentEditor: () => Editor | null,
  instanceKey: string
) => {
  /**
   * 构建搜索正则表达式
   */
  const buildSearchRegex = (query: string, options: {
    caseSensitive: boolean;
    wholeWord: boolean;
    useRegex: boolean;
  }): RegExp | null => {
    if (!query) return null;

    try {
      let pattern = query;
      
      if (!options.useRegex) {
        // 转义特殊字符
        pattern = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      }
      
      if (options.wholeWord) {
        pattern = `\\b${pattern}\\b`;
      }
      
      const flags = options.caseSensitive ? 'g' : 'gi';
      return new RegExp(pattern, flags);
    } catch (error) {
      console.error('构建搜索正则表达式失败:', error);
      return null;
    }
  };

  /**
   * 在文档中搜索
   */
  const searchInDocument = (query: string, options: {
    caseSensitive?: boolean;
    wholeWord?: boolean;
    useRegex?: boolean;
  } = {}) => {
    const editor = getCurrentEditor();
    if (!editor || !query) {
      setSearchState(instanceKey, {
        matches: [],
        totalMatches: 0,
        currentMatch: 0,
        isSearching: false,
      });
      return;
    }

    const searchOptions = {
      caseSensitive: options.caseSensitive ?? false,
      wholeWord: options.wholeWord ?? false,
      useRegex: options.useRegex ?? false,
    };

    const regex = buildSearchRegex(query, searchOptions);
    if (!regex) {
      setSearchState(instanceKey, {
        matches: [],
        totalMatches: 0,
        currentMatch: 0,
        isSearching: false,
      });
      return;
    }

    const doc = editor.state.doc;
    const matches: Array<{ from: number; to: number; text: string }> = [];
    
    // 遍历文档查找匹配项
    doc.descendants((node, pos) => {
      if (node.isText && node.text) {
        let match;
        regex.lastIndex = 0; // 重置正则表达式状态
        
        while ((match = regex.exec(node.text)) !== null) {
          matches.push({
            from: pos + match.index,
            to: pos + match.index + match[0].length,
            text: match[0],
          });
          
          // 防止无限循环
          if (!regex.global) break;
        }
      }
    });

    setSearchState(instanceKey, {
      query,
      matches,
      totalMatches: matches.length,
      currentMatch: matches.length > 0 ? 1 : 0,
      isSearching: true,
      ...searchOptions,
    });

    // 如果有匹配项，跳转到第一个
    if (matches.length > 0) {
      goToMatch(1);
    }

    return matches;
  };

  /**
   * 跳转到指定匹配项
   */
  const goToMatch = (matchIndex: number) => {
    const editor = getCurrentEditor();
    const state = getSearchState(instanceKey);
    
    if (!editor || !state.matches.length) return;

    const index = Math.max(1, Math.min(matchIndex, state.matches.length));
    const match = state.matches[index - 1];
    
    if (match) {
      // 选中匹配的文本
      editor.chain()
        .focus()
        .setTextSelection({ from: match.from, to: match.to })
        .run();
      
      setSearchState(instanceKey, { currentMatch: index });
    }
  };

  /**
   * 下一个匹配项
   */
  const nextMatch = () => {
    const state = getSearchState(instanceKey);
    if (state.matches.length === 0) return;
    
    const nextIndex = state.currentMatch >= state.totalMatches ? 1 : state.currentMatch + 1;
    goToMatch(nextIndex);
  };

  /**
   * 上一个匹配项
   */
  const previousMatch = () => {
    const state = getSearchState(instanceKey);
    if (state.matches.length === 0) return;
    
    const prevIndex = state.currentMatch <= 1 ? state.totalMatches : state.currentMatch - 1;
    goToMatch(prevIndex);
  };

  /**
   * 替换当前匹配项
   */
  const replaceCurrentMatch = (replacement: string) => {
    const editor = getCurrentEditor();
    const state = getSearchState(instanceKey);
    
    if (!editor || !state.matches.length || state.currentMatch === 0) return;

    const match = state.matches[state.currentMatch - 1];
    if (!match) return;

    // 替换文本
    editor.chain()
      .focus()
      .insertContentAt(
        { from: match.from, to: match.to },
        replacement
      )
      .run();

    // 重新搜索以更新匹配项位置
    setTimeout(() => {
      searchInDocument(state.query, {
        caseSensitive: state.caseSensitive,
        wholeWord: state.wholeWord,
        useRegex: state.useRegex,
      });
    }, 100);
  };

  /**
   * 替换所有匹配项
   */
  const replaceAllMatches = (replacement: string) => {
    const editor = getCurrentEditor();
    const state = getSearchState(instanceKey);
    
    if (!editor || !state.matches.length) return;

    // 从后往前替换，避免位置偏移问题
    const sortedMatches = [...state.matches].sort((a, b) => b.from - a.from);
    
    editor.chain().focus();
    
    for (const match of sortedMatches) {
      editor.commands.insertContentAt(
        { from: match.from, to: match.to },
        replacement
      );
    }

    // 重新搜索
    setTimeout(() => {
      searchInDocument(state.query, {
        caseSensitive: state.caseSensitive,
        wholeWord: state.wholeWord,
        useRegex: state.useRegex,
      });
    }, 100);
  };

  /**
   * 清除搜索结果
   */
  const clearSearch = () => {
    setSearchState(instanceKey, {
      query: '',
      matches: [],
      totalMatches: 0,
      currentMatch: 0,
      isSearching: false,
    });
  };

  /**
   * 快速搜索（Ctrl+F 功能）
   */
  const quickSearch = (query?: string) => {
    const editor = getCurrentEditor();
    if (!editor) return;

    // 如果没有提供查询字符串，使用选中的文本
    let searchQuery = query;
    if (!searchQuery) {
      const { from, to } = editor.state.selection;
      if (from !== to) {
        searchQuery = editor.state.doc.textBetween(from, to);
      }
    }

    if (searchQuery) {
      // 打开搜索面板
      searchReplacePanelVisible.value.set(instanceKey, true);
      
      // 执行搜索
      searchInDocument(searchQuery);
      
      // 更新搜索状态
      setSearchState(instanceKey, { query: searchQuery });
    } else {
      // 只打开搜索面板
      searchReplacePanelVisible.value.set(instanceKey, true);
    }
  };

  /**
   * 跳转到指定行
   */
  const goToLine = (lineNumber: number) => {
    const editor = getCurrentEditor();
    if (!editor) return;

    const doc = editor.state.doc;
    let currentLine = 1;
    let targetPos = 0;

    // 找到指定行的位置
    doc.descendants((node, pos) => {
      if (node.isText && node.text) {
        const lines = node.text.split('\n');
        for (let i = 0; i < lines.length; i++) {
          if (currentLine === lineNumber) {
            targetPos = pos + (i === 0 ? 0 : node.text.indexOf('\n', node.text.indexOf('\n') * (i - 1)) + 1);
            return false; // 停止遍历
          }
          if (i < lines.length - 1) {
            currentLine++;
          }
        }
      }
    });

    // 跳转到目标位置
    editor.chain()
      .focus()
      .setTextSelection(targetPos)
      .run();
  };

  return {
    searchInDocument,
    goToMatch,
    nextMatch,
    previousMatch,
    replaceCurrentMatch,
    replaceAllMatches,
    clearSearch,
    quickSearch,
    goToLine,
    getSearchState: () => getSearchState(instanceKey),
    setSearchState: (state: Partial<SearchState>) => setSearchState(instanceKey, state),
  };
};

/**
 * 计算搜索统计信息
 */
export const getSearchStats = computed(() => {
  const stats = {
    totalPanels: searchReplacePanelVisible.value.size,
    activePanels: 0,
    totalMatches: 0,
    activeSearches: 0,
  };

  for (const [key, visible] of searchReplacePanelVisible.value.entries()) {
    if (visible) {
      stats.activePanels++;
      const searchState = searchStates.value.get(key);
      if (searchState) {
        stats.totalMatches += searchState.totalMatches;
        if (searchState.isSearching) {
          stats.activeSearches++;
        }
      }
    }
  }

  return stats;
});

/**
 * 清理搜索状态
 */
export const cleanupSearchStates = (instanceKey?: string) => {
  if (instanceKey) {
    searchStates.value.delete(instanceKey);
    searchReplacePanelVisible.value.delete(instanceKey);
  } else {
    searchStates.value.clear();
    searchReplacePanelVisible.value.clear();
  }
};

export default {
  searchReplacePanelVisible,
  initSearchReplaceState,
  getSearchReplaceState,
  toggleSearchReplace,
  createSearchFunction,
  getSearchStats,
  cleanupSearchStates,
  getSearchState,
  setSearchState,
};