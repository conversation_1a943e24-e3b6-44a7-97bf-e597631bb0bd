# Tavily 搜索工具集成文档

## 概述

Tavily 搜索工具为 AI 助手提供了强大的网络搜索能力，支持实时获取最新信息和深度搜索功能。

## 功能特性

- **智能搜索**: 支持基础和高级搜索模式
- **答案摘要**: 提供 AI 生成的答案摘要
- **内容提取**: 获取网页内容和原始文本
- **图片搜索**: 支持相关图片和图片描述
- **域名过滤**: 支持包含/排除特定域名
- **灵活配置**: 丰富的参数配置选项

## 文件结构

```
src/
├── llm/tools/search.ts           # 搜索工具实现
├── components/settings/llms/
│   └── TavilyOptions.vue         # Tavily 设置组件
├── config/defaultSettings.ts     # 默认配置
├── stores/ui.ts                  # UI 状态管理
└── env.d.ts                      # 类型定义

public/icons/llm/
└── tavily-color.svg              # Tavily 图标
```

## 环境配置

在项目根目录的 `.env` 文件中配置 Tavily API Key：

```bash
# Tavily 搜索 API 配置
TAVILY_API_KEY=your_tavily_api_key_here
```

## 工具使用

### AI 工具调用

AI 可以通过以下方式调用搜索工具：

```javascript
{
  "name": "search_web",
  "arguments": {
    "query": "人工智能最新发展",
    "search_depth": "advanced",
    "include_answer": true,
    "max_results": 10
  }
}
```

### 参数说明

| 参数                         | 类型                  | 默认值  | 说明                 |
| ---------------------------- | --------------------- | ------- | -------------------- |
| `query`                      | string                | -       | 必需，搜索查询字符串 |
| `search_depth`               | 'basic' \| 'advanced' | 'basic' | 搜索深度模式         |
| `include_answer`             | boolean               | true    | 是否包含AI答案摘要   |
| `include_raw_content`        | boolean               | false   | 是否包含原始网页内容 |
| `max_results`                | number                | 5       | 最大结果数量 (1-20)  |
| `include_domains`            | string[]              | -       | 只搜索指定域名       |
| `exclude_domains`            | string[]              | -       | 排除指定域名         |
| `include_images`             | boolean               | false   | 是否包含相关图片     |
| `include_image_descriptions` | boolean               | false   | 是否包含图片描述     |

## 用户设置

### 访问设置

1. 打开应用设置页面
2. 选择"LLM 设置"
3. 点击"Tavily 搜索"标签

### 配置选项

#### API 配置

- **API Key**: Tavily API 密钥
- **基础 URL**: API 请求地址（默认：https://api.tavily.com/search）

#### 搜索设置

- **搜索深度**: 基础搜索（快速）或高级搜索（深度）
- **最大结果数量**: 1-20 个结果
- **包含AI答案摘要**: 获取AI生成的答案
- **包含原始网页内容**: 获取完整网页文本
- **包含相关图片**: 获取搜索相关的图片
- **包含图片描述**: 获取图片的描述信息

#### 测试连接

设置页面提供"测试连接"功能，验证 API Key 和网络连接是否正常。

## 技术实现

### 类型定义

```typescript
interface TavilySettings {
  apiKey: string;
  baseUrl: string;
  searchDepth: 'basic' | 'advanced';
  includeAnswer: boolean;
  includeRawContent: boolean;
  maxResults: number;
  includeImages: boolean;
  includeImageDescriptions: boolean;
}
```

### 搜索响应

```typescript
interface TavilyResponse {
  answer?: string; // AI 生成的答案摘要
  query: string; // 搜索查询
  response_time: number; // 响应时间（毫秒）
  images?: string[]; // 相关图片URL列表
  results: TavilySearchResult[]; // 搜索结果列表
  follow_up_questions?: string[]; // 后续问题建议
}
```

### 错误处理

工具实现了完善的错误处理机制：

- API Key 未配置检查
- 网络请求失败处理
- 响应数据验证
- 用户友好的错误消息

## 集成点

### 1. 工具注册

搜索工具已注册到 LLM 工具映射表：

```typescript
// src/llm/tools/index.ts
export const toolMappings = {
  // ... 其他工具
  search_web: searchWeb,
};
```

### 2. LLM Store 配置

在 LLM Store 中启用搜索工具：

```typescript
// src/stores/llm.ts
const baseTool = [
  'ask_user',
  'search_web', // 网络搜索工具
];
```

### 3. Prompt 系统提示

AI 助手已知晓搜索工具的存在和用法：

```
### 网络搜索工具：
- search_web: 使用Tavily搜索引擎在互联网上搜索信息
  - 支持基础和高级搜索模式
  - 可以获取网页内容、答案摘要、图片等
  - 当需要最新信息或实时数据时使用
```

## 使用场景

### 适用场景

- 获取最新新闻和时事信息
- 查找技术文档和教程
- 获取实时数据和统计信息
- 研究特定主题的资料
- 验证事实和引用

### 最佳实践

- 使用具体、清晰的搜索查询
- 根据需求选择合适的搜索深度
- 合理设置结果数量避免信息过载
- 利用域名过滤提高搜索精度

## 注意事项

1. **API 限制**: Tavily API 可能有请求频率和使用量限制
2. **网络依赖**: 需要稳定的互联网连接
3. **数据时效性**: 搜索结果的时效性取决于网页的更新频率
4. **内容质量**: 搜索结果质量依赖于源网站的可靠性

## 故障排除

### 常见问题

**Q: 搜索失败，提示API Key未配置**
A: 请在设置中配置有效的 Tavily API Key

**Q: 搜索超时或连接失败**
A: 检查网络连接，或使用"测试连接"功能验证配置

**Q: 搜索结果质量不佳**
A: 尝试调整搜索查询，或使用高级搜索模式

**Q: 无法获取某些网站内容**
A: 某些网站可能限制爬虫访问，可尝试调整域名过滤设置

## 更新日志

### v1.0.0 (2024-01)

- 初始实现 Tavily 搜索工具
- 添加完整的用户设置界面
- 集成到 AI 工具系统
- 支持所有 Tavily API 功能参数
