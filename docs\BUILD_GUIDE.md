# InkCop 构建指南

## 📋 概述

InkCop是一个基于Qt 6.9.1和Vue.js的跨平台写作助手应用程序。本文档提供完整的构建、打包和分发指南。

## 🛠️ 系统要求

### Windows

- **Qt**: 6.9.1 MSVC版本
- **Visual Studio**: 2022 Community (含MSVC编译器)
- **Windows SDK**: 10.0.26100.0或更高
- **Node.js**: 18+
- **Bun**: 最新版本 (包管理器)

### Linux

- **Qt**: 6.9.1
- **GCC**: 9+或Clang 10+
- **CMake**: 3.16+
- **Node.js**: 18+

## 🚀 快速开始

### 开发模式 (推荐)

```powershell
# Windows开发构建 (支持热重载)
.\win-dev.ps1

# Linux开发构建
./build-qt-dev.sh
```

### 生产构建

```powershell
# Windows生产构建
.\prod-build.ps1

# Linux生产构建
./build-qt.sh
```

## 📦 Windows发行包构建

### 统一构建脚本

使用 `build-installer.ps1` 作为主要构建脚本：

```powershell
# 构建签名MSIX包 (推荐)
.\build-installer.ps1 -Type MSIX

# 构建未签名MSIX包 (需要开发者模式)
.\build-installer.ps1 -Type MSIX -SkipSigning

# 构建传统安装程序
.\build-installer.ps1 -Type InnoSetup

# 构建便携版
.\build-installer.ps1 -Type Portable

# 构建所有格式
.\build-installer.ps1 -Type All
```

### 参数说明

| 参数             | 类型   | 默认值              | 说明                                   |
| ---------------- | ------ | ------------------- | -------------------------------------- |
| `-Type`          | String | MSIX                | 构建类型 (MSIX/InnoSetup/Portable/All) |
| `-Version`       | String | 1.0.0               | 应用程序版本号                         |
| `-SkipBuild`     | Switch | False               | 跳过应用程序构建                       |
| `-SkipSigning`   | Switch | False               | 跳过MSIX包签名                         |
| `-PublisherName` | String | CN=InkCop Developer | 证书发布者名称                         |

### 输出文件

```
dist-packages/
├── InkCop_1.0.0_x64.msix          # 未签名包 (可选)
├── InkCop_1.0.0_x64_Signed.msix   # 签名包 (默认)
├── InkCop-SelfSigned.cer          # 自签名证书
├── install-all.ps1                # 一键安装脚本
├── install-certificate.ps1        # 证书安装脚本
└── install-signed.ps1             # 签名包安装脚本
```

## 🔧 技术架构

### 前端技术栈

- **框架**: Vue 3 + Composition API
- **UI库**: Quasar Framework
- **构建工具**: Vite
- **编辑器**: TipTap (ProseMirror)
- **状态管理**: Pinia

### 后端技术栈

- **GUI框架**: Qt 6.9.1
- **Web引擎**: QtWebEngine
- **数据库**: ObjectBox (嵌入式)
- **构建系统**: CMake

### 集成方式

- **通信**: Qt WebChannel (JavaScript ↔ C++)
- **资源嵌入**: Qt Resource System
- **部署**: Qt部署工具 + 自定义脚本

## 🎯 关键修复

### 1. GUI应用程序配置

**问题**: 应用启动时显示终端窗口
**解决**: 在CMakeLists.txt中使用WIN32标志

```cmake
if(WIN32)
    add_executable(InkCop WIN32 ${SOURCES} ${HEADERS} ${RESOURCES} ${ICON_RESOURCE})
else()
    add_executable(InkCop ${SOURCES} ${HEADERS} ${RESOURCES})
endif()
```

### 2. 图标资源集成

**问题**: 任务栏显示橙色背景图标
**解决**: 嵌入Windows图标资源

```cmake
# Windows图标资源
if(WIN32)
    set(ICON_RESOURCE qt-src/app.rc)
    enable_language(RC)
endif()
```

文件结构:

- `qt-src/app.ico` - Windows图标文件
- `qt-src/app.rc` - 资源定义文件

### 3. 自签名证书

**功能**: 自动创建和应用自签名证书
**优势**:

- 无需开发者模式即可安装MSIX包
- 专业的安装体验
- 完整的安装脚本套件

## 📁 项目结构

```
inkcop/
├── src/                    # Vue.js前端源码
├── qt-src/                 # Qt C++源码
├── dist/spa/              # 构建的前端资源
├── build-prod/            # Qt生产构建目录
├── dist-packages/         # 发行包输出目录
├── third-party/           # 第三方依赖
│   ├── flatbuffers/       # FlatBuffers库
│   └── objectbox-windows/ # ObjectBox Windows二进制
├── win-dev.ps1          # 开发构建脚本
├── prod-build.ps1         # 生产构建脚本
├── build-installer.ps1    # 发行包构建脚本
└── CMakeLists.txt         # CMake配置文件
```

## 🔍 构建流程

### 开发构建流程

1. **前端构建**: Quasar开发模式 (localhost:9000)
2. **Qt构建**: Debug模式，连接到开发服务器
3. **热重载**: 前端修改实时反映到Qt应用

### 生产构建流程

1. **前端构建**: Quasar SPA模式，优化压缩
2. **资源嵌入**: 前端资源嵌入到Qt资源系统
3. **Qt构建**: Release模式，包含所有资源
4. **依赖部署**: windeployqt自动部署Qt依赖
5. **包创建**: 创建MSIX/安装程序包

## 🚀 安装和分发

### MSIX包安装 (推荐)

```powershell
# 一键安装 (自动安装证书和应用)
.\install-all.ps1

# 分步安装
.\install-certificate.ps1  # 安装证书 (需管理员权限)
.\install-signed.ps1       # 安装应用
```

### 手动安装

1. 右键 `InkCop-SelfSigned.cer` → 安装证书
2. 选择"本地计算机" → "受信任的根证书颁发机构"
3. 双击 `InkCop_1.0.0_x64_Signed.msix` → 安装

### 企业部署

```powershell
# 批量部署脚本
Import-Certificate -FilePath "InkCop-SelfSigned.cer" -CertStoreLocation Cert:\LocalMachine\Root
Add-AppxPackage -Path "InkCop_1.0.0_x64_Signed.msix"
```

## 🐛 故障排除

### 常见问题

**1. Qt环境未找到**

```powershell
# 确保Qt路径正确
$env:Qt6_DIR = "C:\Qt\6.7.3\msvc2022_64"
```

**2. Visual Studio环境未加载**

```powershell
# 手动加载VS环境
& "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
```

**3. ObjectBox依赖缺失**

```powershell
# 下载ObjectBox Windows二进制
.\download-objectbox-windows.ps1
```

**4. 证书安装失败**

- 确保以管理员身份运行PowerShell
- 手动安装证书到"受信任的根证书颁发机构"

**5. MSIX包安装失败**

- 确认证书已正确安装
- 检查Windows版本兼容性 (需要Windows 10 1809+)

## 📊 性能优化

### 构建优化

- **并行构建**: 使用多核CPU加速编译
- **增量构建**: 仅重新编译修改的文件
- **资源压缩**: 前端资源自动压缩优化

### 运行时优化

- **嵌入式资源**: 减少文件I/O操作
- **Qt部署优化**: 仅包含必要的Qt模块
- **WebEngine缓存**: 优化Web内容加载

## 📞 技术支持

### 构建环境检查

```powershell
# 检查构建环境
.\win-dev.ps1 -CheckOnly
```

### 日志和调试

- **Qt日志**: 查看 `qt-logging.rules`
- **构建日志**: PowerShell脚本输出详细信息
- **WebEngine调试**: 启用开发者工具

---

**文档版本**: 1.0.3  
**最后更新**: 2025年6月29日  
**状态**: ✅ 完整且经过验证
