<template>
  <div style="height: fit-content">
    <q-input
      ref="inputRef"
      v-model="folderName"
      :placeholder="$t('src.components.CreateFolder.title')"
      dense
      square
      autofocus
      filled
      color="primary"
      hide-bottom-space
      class="border input-pr-none"
      input-class="q-pr-none items-center"
      @keydown.enter="handleEnterKey"
      @keydown.esc="emit('cancel')"
      @compositionstart="isComposing = true"
      @compositionend="isComposing = false"
    >
      <template v-if="folderName.trim().length > 0">
        <div class="flex flex-center q-pr-sm">
          <q-btn icon="mdi-plus" dense size="sm" round flat @click="createFolderHandler" />
        </div>
      </template>
      <template v-else>
        <div class="flex flex-center q-pr-sm">
          <q-btn icon="close" dense size="sm" round flat @click="emit('cancel')" />
        </div>
      </template>
    </q-input>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import type { Folder } from 'src/types/doc';
import { QInput } from 'quasar';
import { useUiStore } from 'src/stores/ui';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

const uiStore = useUiStore();

const { parentId } = defineProps<{
  parentId: number | null;
}>();

const emit = defineEmits<{
  (e: 'folderCreated', folder: Folder): void;
  (e: 'cancel'): void;
}>();

const folderName = ref('');
const isComposing = ref(false);
const inputRef = ref<InstanceType<typeof QInput> | null>(null);

// 组件挂载后手动聚焦输入框
onMounted(async () => {
  await nextTick();
  if (inputRef.value) {
    inputRef.value.focus();
  }
  uiStore.lowlight = true;
});
onBeforeUnmount(() => {
  uiStore.lowlight = false;
});

const handleEnterKey = (event: KeyboardEvent) => {
  // 如果正在进行中文输入法组合输入，不触发创建文件夹事件
  if (isComposing.value) {
    return;
  }

  // 阻止默认行为并触发创建文件夹
  event.preventDefault();
  void createFolderHandler();
};

const createFolderHandler = async () => {
  if (!folderName.value) return;
  try {
    const sqlite = useSqlite();
    const folderId = await sqlite.createFolder(folderName.value, parentId);
    const newFolder = await sqlite.getFolder(folderId);
    emit('folderCreated', newFolder);
    emit('cancel');
    folderName.value = '';
  } catch (error) {
    console.error('创建文件夹失败:', error);
  }
};
</script>
