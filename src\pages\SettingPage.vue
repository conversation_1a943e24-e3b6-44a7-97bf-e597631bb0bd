<template>
  <EditorSettings v-if="uiStore.settingfor === 'editor'" />
  <LlmSettings v-if="uiStore.settingfor === 'llm'" />
  <BaseSettings v-if="uiStore.settingfor === 'base'" />
  <SearchEngineSettings v-if="uiStore.settingfor === 'searchEngine'" />
  <ResourceProviderSettings v-if="uiStore.settingfor === 'resourceProvider'" />
  <AutoCompleteSettings v-if="uiStore.settingfor === 'autoComplete'" />
  <FloatAgentConfig v-if="uiStore.settingfor === 'floatAgent'" />
  <KnowledgeBaseSettings v-if="uiStore.settingfor === 'knowledgeBase'" />
  <PromptSettings v-if="uiStore.settingfor === 'prompt'" />
</template>
<script setup lang="ts">
import EditorSettings from 'src/components/settings/EditorSettings.vue';
import LlmSettings from 'src/components/settings/LlmSettings.vue';
import BaseSettings from 'src/components/settings/BaseSettings.vue';
import SearchEngineSettings from 'src/components/settings/SearchEngineSettings.vue';
import ResourceProviderSettings from 'src/components/settings/ResourceProviderSettings.vue';
import AutoCompleteSettings from 'src/components/settings/AutoCompleteSetting.vue';
import FloatAgentConfig from 'src/components/settings/FloatAgentConfig.vue';
import KnowledgeBaseSettings from 'src/components/settings/KnowledgeBaseSettings.vue';
import PromptSettings from 'src/components/settings/PromptSettings.vue';
import { useUiStore } from 'src/stores/ui';

const uiStore = useUiStore();
</script>
