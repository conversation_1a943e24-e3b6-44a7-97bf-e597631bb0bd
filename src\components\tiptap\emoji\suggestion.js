import { V<PERSON><PERSON><PERSON><PERSON> } from '@tiptap/vue-3';
import tippy from 'tippy.js';

import EmojiList from './EmojiList.vue';
const commonEmojis = [
  { emoji: '😀', name: 'grinning' },
  { emoji: '😂', name: 'joy' },
  { emoji: '😍', name: 'heart_eyes' },
  { emoji: '👍', name: 'thumbsup' },
  { emoji: '🙏', name: 'pray' },
  { emoji: '🎉', name: 'tada' },
  { emoji: '🥰', name: 'smiling_face_with_3_hearts' },
  { emoji: '😎', name: 'sunglasses' },
  { emoji: '😭', name: 'sob' },
  { emoji: '😡', name: 'rage' },
  { emoji: '🥳', name: 'partying_face' },
  { emoji: '🤔', name: 'thinking' },
  { emoji: '😏', name: 'smirk' },
  { emoji: '😅', name: 'sweat_smile' },
  { emoji: '😇', name: 'innocent' },
  { emoji: '😋', name: 'yum' },
  { emoji: '😜', name: 'stuck_out_tongue_winking_eye' },
  { emoji: '😤', name: 'triumph' },
  { emoji: '😱', name: 'scream' },
  { emoji: '😴', name: 'sleeping' },
  { emoji: '🤗', name: 'hugs' },
  { emoji: '🤩', name: 'star_struck' },
  { emoji: '🥺', name: 'pleading_face' },
  { emoji: '😬', name: 'grimacing' },
  { emoji: '🤤', name: 'drooling_face' },
  { emoji: '😈', name: 'smiling_imp' },
  { emoji: '💩', name: 'poop' },
  { emoji: '🔥', name: 'fire' },
  { emoji: '❤️', name: 'heart' },
  { emoji: '✨', name: 'sparkles' },
];
export default {
  items: ({ query }) => {
    return commonEmojis.filter(({ name }) => {
      return name.startsWith(query.toLowerCase());
    });
  },

  render: () => {
    let component;
    let popup;

    return {
      onStart: (props) => {
        console.log('emoji onStart');
        component = new VueRenderer(EmojiList, {
          props,
          editor: props.editor,
        });

        popup = tippy('body', {
          getReferenceClientRect: props.clientRect,
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: 'manual',
          placement: 'bottom-start',
          maxWidth: 'none', // 让弹框宽度自适应内容
          minWidth: 'auto', // 最小宽度自适应
        });
      },

      onUpdate(props) {
        console.log('emoji onUpdate');
        component.updateProps(props);

        popup[0].setProps({
          getReferenceClientRect: props.clientRect,
        });
      },

      onKeyDown(props) {
        console.log('emoji onKeyDown');
        if (props.event.key === 'Escape') {
          popup[0].hide();
          component.destroy();

          return true;
        }

        return component.ref?.onKeyDown(props);
      },

      onExit() {
        console.log('emoji onExit');
        popup[0].destroy();
        component.destroy();
      },
    };
  },
};
