<template>
  <node-view-wrapper class="custom-table">
    <hot-table
      :data="tableData"
      :settings="hotSettings"
      @afterChange="handleChange"
      @afterCreateRow="handleCreateRow"
      @afterCreateCol="handleCreateCol"
      @afterRemoveRow="handleRemoveRow"
      @afterRemoveCol="handleRemoveCol"
    />
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { HotTable } from '@handsontable/vue3';
import { registerAllModules } from 'handsontable/registry';
import { NodeViewWrapper } from '@tiptap/vue-3';
import 'handsontable/dist/handsontable.full.min.css';
import type { CellChange } from 'handsontable/common';
import type { NodeViewProps } from '@tiptap/core';
import type { Node } from 'prosemirror-model';

// 注册所有 Handsontable 模块
registerAllModules();

interface TableNode extends Node {
  attrs: {
    data: string;
  };
}

interface Props extends Omit<NodeViewProps, 'node'> {
  node: TableNode;
  updateAttributes: (attrs: Record<string, unknown>) => void;
}

const props = defineProps<Props>();

// 表格数据
const tableData = ref<string[][]>([]);

// Handsontable 配置
const hotSettings = ref({
  licenseKey: 'non-commercial-and-evaluation',
  height: 'auto',
  width: '100%',
  colHeaders: true,
  rowHeaders: true,
  contextMenu: true,
  manualColumnResize: true,
  manualRowResize: true,
  mergeCells: true,
  dropdownMenu: true,
  filters: true,
  comments: true,
  columnSorting: true,
  autoColumnSize: true,
  autoRowSize: true,
  stretchH: 'all',
});

// 初始化表格数据
onMounted(() => {
  try {
    const data = JSON.parse(props.node.attrs.data || '[]');
    tableData.value = data;
  } catch {
    tableData.value = [['']];
  }
});

// 处理数据变更
const handleChange = (changes: CellChange[] | null, source: string) => {
  if (source === 'loadData' || !changes) return;

  props.updateAttributes({
    data: JSON.stringify(tableData.value),
  });
};

// 处理行创建
const handleCreateRow = () => {
  props.updateAttributes({
    data: JSON.stringify(tableData.value),
  });
};

// 处理列创建
const handleCreateCol = () => {
  props.updateAttributes({
    data: JSON.stringify(tableData.value),
  });
};

// 处理行删除
const handleRemoveRow = () => {
  props.updateAttributes({
    data: JSON.stringify(tableData.value),
  });
};

// 处理列删除
const handleRemoveCol = () => {
  props.updateAttributes({
    data: JSON.stringify(tableData.value),
  });
};
</script>

<style lang="scss">
.custom-table {
  margin: 1em 0;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;

  .handsontable,
  .ht_master,
  .wtHolder,
  .htCore,
  table {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box;
  }

  .handsontable {
    font-size: 14px;
    width: 100% !important;

    .htCore {
      td,
      th {
        padding: 4px 8px;
      }
    }
  }
}

// 暗色主题适配
:root.dark {
  .custom-table {
    .handsontable {
      background-color: #1e1e1e;
      color: #ffffff;

      .htCore {
        td,
        th {
          background-color: #1e1e1e;
          color: #ffffff;
          border-color: #333333;
        }
      }
    }
  }
}
</style>
