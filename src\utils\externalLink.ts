/**
 * 外部链接处理工具
 * 在Qt环境中使用系统默认浏览器打开链接，在Web环境中使用window.open
 */

/**
 * 打开外部链接
 * @param url 要打开的URL
 * @param fallbackToWindowOpen 在非Qt环境中是否使用window.open作为后备方案，默认为true
 */
export function openExternalLink(url: string, fallbackToWindowOpen = true): void {
  if (!url) {
    console.warn('[ExternalLink] 尝试打开空URL');
    return;
  }

  // 检查是否在Qt环境中
  if (window.qtWindow && window.qtWindow.openExternalUrl) {
    console.log('[ExternalLink] 使用Qt方法打开外部链接:', url);
    window.qtWindow.openExternalUrl(url);
  } else if (fallbackToWindowOpen) {
    // 在非Qt环境中使用传统方式打开链接
    console.log('[ExternalLink] 使用window.open打开外部链接:', url);
    window.open(url, '_blank', 'noopener,noreferrer');
  } else {
    console.warn('[ExternalLink] Qt环境不可用且禁用了window.open后备方案');
  }
}

/**
 * 检查是否在Qt环境中
 */
export function isQtEnvironment(): boolean {
  return !!(window.qtWindow && window.qtWindow.openExternalUrl);
}

/**
 * 创建一个可以安全打开外部链接的点击处理器
 * @param url 要打开的URL
 * @param fallbackToWindowOpen 在非Qt环境中是否使用window.open作为后备方案
 */
export function createExternalLinkHandler(url: string, fallbackToWindowOpen = true) {
  return (event?: Event) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    openExternalLink(url, fallbackToWindowOpen);
  };
}
