<template>
  <div class="column no-wrap">
    <div
      v-if="!showCreateFolder"
      class="gap-xs row q-pa-xs transparent"
      :class="hasFolders ? 'border-bottom' : 'q-px-sm q-pt-md'"
    >
      <q-btn
        v-if="hasFolders"
        dense
        size="0.7rem"
        flat
        icon="mdi-refresh"
        :loading="docStore.folderTreeLoading"
        @click="refreshFolderTree"
      />
      <q-btn
        v-if="hasFolders"
        dense
        size="0.7rem"
        flat
        :icon="
          uiStore.sortMode === 'alphabetical'
            ? 'mdi-sort-alphabetical-ascending'
            : 'mdi-sort-numeric-ascending'
        "
        @click="toggleSortMode"
      >
        <q-tooltip
          :class="`${$q.dark.isActive ? 'text-white bg-dark' : 'text-black bg-white'} text-body2 border shadow-24`"
        >
          {{
            uiStore.sortMode === 'alphabetical'
              ? $t('src.components.DocManager.customSort')
              : $t('src.components.DocManager.alphabeticalSort')
          }}
        </q-tooltip>
      </q-btn>
      <q-space v-if="hasFolders" />
      <q-btn
        dense
        size="0.7rem"
        unelevated
        icon="mdi-folder-plus"
        :flat="hasFolders"
        :label="hasFolders ? void 0 : $t('src.components.DocManager.newFolder')"
        :color="!hasFolders ? 'primary' : void 0"
        :class="!hasFolders ? 'q-space' : ''"
        @click="showCreateFolder = true"
      />
    </div>
    <div :class="hasFolders ? 'q-py-sm' : 'q-pa-sm'" class="column q-space scroll-y">
      <CreateFolder
        v-if="showCreateFolder"
        :parentId="null"
        @cancel="showCreateFolder = false"
        @folderCreated="folderCreated"
      />
      <FolderTree ref="folderTree" :parentId="null" :depth="0" />
      <div
        ref="dropSpaceRef"
        class="drop-space-area q-space"
        style="flex: 1; min-height: 100px; background: transparent; position: relative; z-index: 1"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import CreateFolder from './CreateFolder.vue';
import FolderTree from './FolderTree.vue';
import { useI18n } from 'vue-i18n';
import { useUiStore } from 'src/stores/ui';
import { SortMode, sortFoldersAndDocuments } from 'src/utils/sortUtils';
import { useDocStore } from 'src/stores/doc';
import type { DragData } from 'src/utils/dragAndDrop';
import { globalDragState } from 'src/utils/dragAndDrop';
import type { Folder } from 'src/types/doc';
import { useQuasar } from 'quasar';
// import { useSqlite } from 'src/composeables/useSqlite';
const docStore = useDocStore();
const uiStore = useUiStore();
const { t: $t } = useI18n({ useScope: 'global' });
const $q = useQuasar();

const folderTree = ref<InstanceType<typeof FolderTree> | null>(null);
const dropSpaceRef = ref<HTMLElement | null>(null);
const showCreateFolder = ref(false);

const folderCreated = () => {
  showCreateFolder.value = false;
  folderTree.value?.load(null);
};

// 处理拖拽到空白区域的放置事件
const handleDropSpaceDrop = async (dragData: DragData) => {
  console.log('🔄 [DocManager] 拖拽到空白区域:', { dragData });

  if (dragData.type === 'folder') {
    // 获取 docStore 实例
    const { useDocStore } = await import('src/stores/doc');
    const docStoreInstance = useDocStore();

    const draggedFolder = docStoreInstance.folderMap.get(dragData.id);
    if (!draggedFolder) {
      console.error('❌ [DocManager] 找不到拖拽的文件夹');
      return;
    }

    // 获取根目录文件夹列表并应用排序（与UI显示保持一致）
    const rootFolders = docStoreInstance.folderTree;
    const { sortedFolders } = sortFoldersAndDocuments(rootFolders, [], uiStore.sortMode);
    const draggedFolderIndex = sortedFolders.findIndex((f) => f.id === dragData.id);

    console.log('🔄 [DocManager] 拖拽状态检查:', {
      draggedFolderId: dragData.id,
      draggedFolderName: draggedFolder.name,
      parent_id: draggedFolder.parent_id,
      draggedFolderIndex,
      rootFoldersLength: sortedFolders.length,
      isLastPosition: draggedFolderIndex === sortedFolders.length - 1,
      rootFolderNames: sortedFolders.map((f) => f.name),
    });

    // 基本判断：1. 拖拽的是文件夹；2. 拖拽的文件夹不是根目录下的最后一个文件夹
    const isRootFolder = draggedFolder.parent_id === null || draggedFolder.parent_id === -1;
    const isLastRootFolder = isRootFolder && draggedFolderIndex === sortedFolders.length - 1;

    // 额外检查：确保拖拽的文件夹确实在根目录中，且确实是最后一个
    if (isLastRootFolder && sortedFolders.length > 1) {
      // 双重验证：检查最后一个文件夹是否真的是拖拽的文件夹
      const lastFolder = sortedFolders[sortedFolders.length - 1];
      if (lastFolder && lastFolder.id === dragData.id) {
        console.log('🔄 [DocManager] 文件夹已经在根目录最后位置，无需移动');
        return;
      } else {
        console.log('🔄 [DocManager] 索引检查不一致，继续执行移动操作');
      }
    }

    // 如果不是根文件夹，先移动到根目录
    if (!isRootFolder) {
      console.log('🔄 [DocManager] 将文件夹移动到根目录');
      const moveSuccess = await docStoreInstance.moveFolderToParent(dragData.id, null);
      if (!moveSuccess) {
        console.error('❌ [DocManager] 移动文件夹到根目录失败');
        return;
      }
    }

    // 直接设置拖拽文件夹为根目录文件夹且排序在最后
    const otherFolders = rootFolders.filter((f) => f.id !== dragData.id);
    const draggedFolderInList = rootFolders.find((f) => f.id === dragData.id);

    if (draggedFolderInList) {
      const reorderedFolders = [...otherFolders, draggedFolderInList];

      // 更新前端对象的 sort_order 字段，确保与数组顺序一致
      reorderedFolders.forEach((folder, index) => {
        folder.sort_order = index;
      });

      // 更新前端数据存储中的根级文件夹顺序
      docStoreInstance.folderTree.splice(
        0,
        docStoreInstance.folderTree.length,
        ...reorderedFolders,
      );

      // 更新数据库排序
      const folderIds = reorderedFolders.map((f) => f.id);
      const { useSqlite } = await import('src/composeables/useSqlite');
      await useSqlite().updateFolderOrder(-1, folderIds);

      console.log('✅ [DocManager] 文件夹已排序到根目录最后位置');
    }
  }
};

// 刷新文件夹树数据，保持展开状态
const refreshFolderTree = async () => {
  if (folderTree.value && typeof folderTree.value.refreshWithExpandedState === 'function') {
    await folderTree.value.refreshWithExpandedState();
  } else {
    // 降级到普通刷新
    await folderTree.value?.load(null);
  }
};

// 重新加载展开的文件夹数据
const reloadExpandedFolders = async () => {
  if (folderTree.value && typeof folderTree.value.refreshWithExpandedState === 'function') {
    console.log('🔄 [DocManager] 重新加载展开的文件夹数据');
    await folderTree.value.refreshWithExpandedState();
  }
};

// 切换排序模式
const toggleSortMode = () => {
  uiStore.toggleSortMode();
};

// 缓存计算属性，避免频繁重新计算
const hasFolders = computed(() => {
  return docStore.folderTree.length > 0;
});

// 监听应用切换，当切换到inkcop时重新加载数据
watch(
  () => uiStore.app,
  async (newApp, oldApp) => {
    if (newApp === 'inkcop' && oldApp !== 'inkcop') {
      // 延迟确保组件已完全显示
      await new Promise((resolve) => setTimeout(resolve, 150));
      await reloadExpandedFolders();
    }
  },
);

// 简单的拖拽处理变量
let simpleDropSpacePlaceholder: HTMLElement | null = null;
let isDragOverDropSpace = false;

// 创建简单的占位符
const createSimpleDropSpacePlaceholder = () => {
  // 清理现有占位符
  cleanupSimpleDropSpacePlaceholder();

  // 检查是否是无意义的拖拽操作
  if (globalDragState.dragData) {
    // 如果拖拽的是文档，根目录空白区域不显示占位符
    if (globalDragState.dragData.type === 'document') {
      console.log('🎯 [DocManager] 文档不能拖拽到根目录空白区域，不显示占位符');
      return;
    }

    // 如果拖拽的是文件夹，检查是否为无意义操作
    if (globalDragState.dragData.type === 'folder') {
      const draggedFolderId = globalDragState.dragData.id;
      const folders = docStore.folderTree;

      if (folders.length > 0) {
        // 使用排序后的文件夹数组进行判断（与UI显示保持一致）
        const { sortedFolders } = sortFoldersAndDocuments(folders, [], uiStore.sortMode);
        const draggedFolderIndex = sortedFolders.findIndex((f: Folder) => f.id === draggedFolderId);

        if (draggedFolderIndex !== -1) {
          // 只有拖拽的文件夹是最后一个时，拖拽到空白区域才是无意义的
          if (draggedFolderIndex === sortedFolders.length - 1) {
            console.log('🎯 [DocManager] 拖拽最后一个文件夹到空白区域，不显示占位符');
            return;
          }
        }
      }
    }
  }

  // 创建新占位符
  const placeholder = document.createElement('div');
  placeholder.className = 'row no-wrap gap-xs flex-center q-space hover-item folder-drag-container';
  placeholder.style.cssText = `
    height: 32px;
    margin: 2px 0;
    border: 2px dashed var(--q-primary);
    background-color: var(--q-primary-light);
    opacity: 0.7;
    pointer-events: none;
  `;
  placeholder.innerHTML = `<div style="color: var(--q-primary); font-size: 14px;">${$t('src.components.DragDropIndicator.dropHere')}</div>`;

  // 添加到drop-space区域
  if (dropSpaceRef.value) {
    dropSpaceRef.value.appendChild(placeholder);
    simpleDropSpacePlaceholder = placeholder;
  }
};

// 清理简单占位符
const cleanupSimpleDropSpacePlaceholder = () => {
  if (simpleDropSpacePlaceholder) {
    simpleDropSpacePlaceholder.remove();
    simpleDropSpacePlaceholder = null;
  }
};

// 设置空白区域的简单拖拽处理
onMounted(() => {
  void nextTick(() => {
    if (dropSpaceRef.value) {
      const dropSpaceElement = dropSpaceRef.value;

      // 监听拖拽进入
      dropSpaceElement.addEventListener('dragenter', (e) => {
        e.preventDefault();
        console.log(
          '🎯 [DocManager] dragenter 被调用, sortMode:',
          uiStore.sortMode,
          'CUSTOM:',
          SortMode.CUSTOM,
        );

        // 检查是否为自定义排序模式
        if (uiStore.sortMode === SortMode.CUSTOM) {
          console.log('🎯 [DocManager] 设置 isDragOverDropSpace = true');
          isDragOverDropSpace = true;

          // 清理之前在文件夹间显示的占位符
          const existingPlaceholders = document.querySelectorAll('.drag-placeholder');
          existingPlaceholders.forEach((placeholder) => {
            console.log('🎯 [DocManager] 清理文件夹间的占位符');
            placeholder.remove();
          });

          createSimpleDropSpacePlaceholder();
        } else {
          console.log('🎯 [DocManager] 不是自定义排序模式，跳过');
        }
      });

      // 监听拖拽悬停
      dropSpaceElement.addEventListener('dragover', (e) => {
        e.preventDefault();
        console.log('🎯 [DocManager] dragover 被调用');
      });

      // 监听拖拽离开
      dropSpaceElement.addEventListener('dragleave', (e) => {
        e.preventDefault();
        console.log('🎯 [DocManager] dragleave 被调用');

        // 检查是否真的离开了drop-space区域
        const rect = dropSpaceElement.getBoundingClientRect();
        const x = e.clientX;
        const y = e.clientY;

        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
          isDragOverDropSpace = false;
          cleanupSimpleDropSpacePlaceholder();
        }
      });

      // 监听拖拽放置
      dropSpaceElement.addEventListener('drop', (e) => {
        e.preventDefault();
        console.log(
          '🎯 [DocManager] drop 被调用, isDragOverDropSpace:',
          isDragOverDropSpace,
          'sortMode:',
          uiStore.sortMode,
        );

        if (isDragOverDropSpace && uiStore.sortMode === SortMode.CUSTOM) {
          // 获取拖拽数据
          const dragDataStr = e.dataTransfer?.getData('application/json');
          console.log('🎯 [DocManager] 拖拽数据字符串:', dragDataStr);

          if (dragDataStr) {
            try {
              const dragData = JSON.parse(dragDataStr);
              console.log('🎯 [DocManager] 解析拖拽数据:', dragData);

              if (dragData.type === 'folder') {
                console.log('🎯 [DocManager] 调用 handleDropSpaceDrop');
                // 使用 void 来忽略 Promise 返回值
                void handleDropSpaceDrop(dragData);
              } else if (dragData.type === 'document') {
                console.log('🎯 [DocManager] 文档不能拖拽到根目录空白区域，忽略');
                // 文档必须有明确的父文件夹，不能拖拽到根目录空白区域
              } else {
                console.log('🎯 [DocManager] 未知拖拽类型:', dragData.type);
              }
            } catch (error) {
              console.error('🎯 [DocManager] 解析拖拽数据失败:', error);
            }
          } else {
            console.log('🎯 [DocManager] 没有拖拽数据');
          }
        } else {
          console.log(
            '🎯 [DocManager] 条件不满足 - isDragOverDropSpace:',
            isDragOverDropSpace,
            'sortMode:',
            uiStore.sortMode,
          );
        }

        // 清理状态
        isDragOverDropSpace = false;
        cleanupSimpleDropSpacePlaceholder();
      });
    }
  });
});
</script>
