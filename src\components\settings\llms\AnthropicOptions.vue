<template>
  <div v-if="settings" class="anthropic-settings">
    <div class="row items-center q-mb-lg">
      <div class="column">
        <div class="text-h6">Anthropic Claude {{ $t('settings.title') }}</div>
        <div class="text-caption text-grey-6">
          {{ $t('configuration') }} Anthropic Claude API
          {{ $t('src.components.settings.llmProviders.titleSuffix') }}
        </div>
      </div>
      <q-space />
      <!-- 连接测试按钮 -->
      <div class="row q-gutter-sm">
        <q-toggle
          v-model="settings.enabled"
          color="positive"
          :label="$t('src.components.settings.llmProviders.enabled')"
          :disable="!settings.apiKey || settings.apiKey.trim() === ''"
        />
      </div>
    </div>

    <q-input
      v-model="settings.baseUrl"
      :label="$t('src.components.settings.llmProviders.baseUrl')"
      outlined
      type="text"
      class="q-mb-md"
      :rules="[(val) => !!val || $t('src.components.settings.llmProviders.apiKeyRueles.required')]"
      :hint="`Anthropic ${$t('src.components.settings.llmProviders.apiKeyRueles.hint')}`"
      @update:model-value="updateSettings({ baseUrl: String($event) })"
    >
      <template v-slot:prepend>
        <q-icon name="link" />
      </template>
    </q-input>
    <!-- API 密钥设置 -->
    <q-input
      v-model="settings.apiKey"
      :label="$t('src.components.settings.llmProviders.apiKey')"
      :type="showApiKey ? 'text' : 'password'"
      outlined
      class="q-mb-md"
      :rules="[(val) => !!val || $t('src.components.settings.llmProviders.apiKeyRules.required')]"
      @update:model-value="updateSettings({ apiKey: String($event) })"
    >
      <template v-slot:prepend>
        <q-icon name="key" />
      </template>
      <template v-slot:append>
        <q-icon
          :name="showApiKey ? 'visibility_off' : 'visibility'"
          class="cursor-pointer"
          @click="showApiKey = !showApiKey"
        />
      </template>
      <template v-slot:hint>
        <span class="text-primary cursor-pointer" @click="openExternalLink(settings.helpUrl)">
          {{ $t('src.components.settings.llmProviders.getApiKey') }}
        </span>
      </template>
    </q-input>

    <!-- 模型选择已移至对话组件中 -->

    <!-- 最大输出 Tokens -->
    <q-input
      v-model="settings.maxTokens"
      :label="$t('src.components.settings.llmProviders.maxTokens')"
      type="number"
      outlined
      class="q-mb-md"
      :rules="[
        (val) => val > 0 || $t('src.components.settings.llmProviders.maxTokensRules.required'),
      ]"
      :hint="$t('src.components.settings.llmProviders.maxTokensRules.hint')"
      @update:model-value="updateSettings({ maxTokens: Number($event) })"
    >
      <template v-slot:prepend>
        <q-icon name="format_list_numbered" />
      </template>
    </q-input>

    <!-- 随机性控制方式选择 -->
    <q-select
      v-model="randomnessControlType"
      :options="randomnessControlOptions"
      :label="$t('src.components.settings.llmProviders.randomnessType')"
      outlined
      emit-value
      map-options
      class="q-mb-md"
      @update:model-value="onRandomnessControlTypeChange"
    >
      <template v-slot:prepend>
        <q-icon name="settings" />
      </template>
    </q-select>

    <!-- 温度设置 -->
    <q-item v-if="randomnessControlType === 'temperature'" class="q-pa-none q-mb-md">
      <q-item-section avatar>
        <q-icon name="thermostat" />
      </q-item-section>
      <q-item-section>
        <q-slider
          v-model="currentTemperature"
          :min="0"
          :max="1"
          :step="0.1"
          label
          :label-always="true"
          :label-value="`${$t('src.components.settings.llmProviders.temperature')}: ${currentTemperature}`"
          track-size="2px"
          color="grey"
          label-color="primary"
          thumb-color="primary"
          @update:model-value="updateTemperature"
        />
        <span class="text-grey">{{
          $t('src.components.settings.llmProviders.temperatureDescription')
        }}</span>
      </q-item-section>
    </q-item>

    <!-- Top P 设置 -->
    <q-item v-if="randomnessControlType === 'topP'" class="q-pa-none q-mb-md">
      <q-item-section avatar>
        <q-icon name="tune" />
      </q-item-section>
      <q-item-section>
        <q-slider
          v-model="currentTopP"
          :min="0"
          :max="1"
          :step="0.1"
          label
          :label-value="`${$t('src.components.settings.llmProviders.topP')}: ${currentTopP}`"
          :label-always="true"
          track-size="2px"
          color="grey"
          label-color="primary"
          thumb-color="primary"
          @update:model-value="updateTopP"
        />
        <span class="text-grey">{{
          $t('src.components.settings.llmProviders.topPDescription')
        }}</span>
      </q-item-section>
    </q-item>

    <!-- 高级设置 -->
    <q-expansion-item
      icon="settings"
      :label="$t('src.components.settings.llmProviders.advancedSettings')"
    >
      <q-card>
        <q-card-section>
          <!-- 思考功能开关 -->
          <q-toggle
            v-model="settings.thinking_enabled"
            :label="$t('src.components.settings.llmProviders.enableInterleavedThinking')"
            class="q-mb-md"
            @update:model-value="updateSettings({ thinking_enabled: $event })"
          >
            <q-tooltip>
              {{ $t('src.components.settings.llmProviders.enableInterleavedThinkingTooltip') }}
            </q-tooltip>
          </q-toggle>

          <!-- 思考预算 -->
          <q-input
            v-if="settings.thinking_enabled"
            v-model="settings.thinking_budget"
            :label="$t('src.components.settings.llmProviders.thinkInputLabel')"
            type="number"
            outlined
            class="q-mb-md"
            :rules="[
              (val) =>
                val >= 1024 || $t('src.components.settings.llmProviders.thinkInputRules.required'),
            ]"
            :hint="$t('src.components.settings.llmProviders.thinkInputRules.hint')"
            @update:model-value="updateSettings({ thinking_budget: Number($event) })"
          >
            <template v-slot:prepend>
              <q-icon name="psychology_alt" />
            </template>
          </q-input>

          <!-- 交错思考开关 -->
          <q-toggle
            v-model="settings.interleaved_thinking"
            :label="$t('src.components.settings.llmProviders.enableInterleavedThinking')"
            class="q-mb-md"
            @update:model-value="updateSettings({ interleaved_thinking: $event })"
          >
            <q-tooltip>
              {{ $t('src.components.settings.llmProviders.enableInterleavedThinkingTooltip') }}
            </q-tooltip>
          </q-toggle>
        </q-card-section>
      </q-card>
    </q-expansion-item>
    <!-- 可用模型管理 -->
    <q-separator class="q-my-lg" />
    <ModelManager v-model="settings.avaliableModels" @update:model-value="updateAvailableModels" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useUiStore } from 'src/stores/ui';
import type { AnthropicSettings } from 'src/types/qwen';
import ModelManager from '../ModelManager.vue';
import type { CategorizedModels } from 'src/types/modelCategories';
import { openExternalLink } from 'src/utils/externalLink';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

const store = useUiStore();
const showApiKey = ref(false);
const original_baseurl = 'https://api.anthropic.com/v1';

// 随机性控制方式
const randomnessControlType = ref<'temperature' | 'topP'>('temperature');

// 随机性控制选项
const randomnessControlOptions = [
  { label: '温度 (Temperature)', value: 'temperature' },
  { label: 'Top P', value: 'topP' },
];

// 当前显示的温度和TopP值
const currentTemperature = ref(0.7);
const currentTopP = ref(0.9);

// 模型选择已移至对话组件中

// 从 store 获取设置
const settings = computed(() => store.perferences?.llm?.anthropic);

// 初始化随机性控制类型和当前值
watch(
  settings,
  (newSettings) => {
    if (newSettings) {
      // 检查哪个参数是有效的（不是undefined且不是NaN）
      const hasValidTemperature =
        newSettings.temperature !== undefined && !isNaN(newSettings.temperature);
      const hasValidTopP = newSettings.topP !== undefined && !isNaN(newSettings.topP);

      // 根据现有设置确定当前使用的控制方式并同步当前值
      if (hasValidTemperature && !hasValidTopP) {
        randomnessControlType.value = 'temperature';
        currentTemperature.value = newSettings.temperature!;
      } else if (hasValidTopP && !hasValidTemperature) {
        randomnessControlType.value = 'topP';
        currentTopP.value = newSettings.topP!;
      } else if (hasValidTemperature && hasValidTopP) {
        // 如果两个都是有效值，默认使用温度
        randomnessControlType.value = 'temperature';
        currentTemperature.value = newSettings.temperature!;
      } else {
        // 如果都不是有效值，默认使用温度
        randomnessControlType.value = 'temperature';
        currentTemperature.value = 0.7;
      }
    }
  },
  { immediate: true },
);

// 更新温度值
const updateTemperature = (value: number) => {
  currentTemperature.value = value;
  updateSettings({ temperature: value });
};

// 更新TopP值
const updateTopP = (value: number) => {
  currentTopP.value = value;
  updateSettings({ topP: value });
};

// 处理随机性控制类型变化
const onRandomnessControlTypeChange = (newType: 'temperature' | 'topP') => {
  if (!settings.value) return;

  // 创建新的设置对象，将不需要的字段设置为NaN
  const currentSettings = { ...settings.value };

  if (newType === 'temperature') {
    // 选择温度，将topP设置为NaN，保留或设置默认温度值
    const temperatureValue = settings.value.temperature ?? 0.7;
    currentSettings.temperature = temperatureValue;
    currentTemperature.value = temperatureValue; // 更新当前显示值
    currentSettings.topP = NaN; // 设置为NaN表示不使用此参数
  } else if (newType === 'topP') {
    // 选择topP，将temperature设置为NaN，保留或设置默认topP值
    const topPValue = settings.value.topP ?? 0.9;
    currentSettings.topP = topPValue;
    currentTopP.value = topPValue; // 更新当前显示值
    currentSettings.temperature = NaN; // 设置为NaN表示不使用此参数
  }

  // 直接更新整个设置对象
  store.updateAnthropicSettings(currentSettings);
};

// 更新设置 - 添加防抖和变化检测
const updateSettings = (newSettings: Partial<AnthropicSettings>) => {
  // 检查是否真的有变化
  const currentSettings = settings.value;
  if (!currentSettings) return;

  // 只有在明确更新 baseUrl 且值为空时，才设置默认值
  if ('baseUrl' in newSettings && !newSettings.baseUrl) {
    newSettings.baseUrl = original_baseurl;
  }

  // 比较新旧值，只有真正变化时才更新
  let hasChanges = false;
  for (const [key, value] of Object.entries(newSettings)) {
    if (currentSettings[key as keyof AnthropicSettings] !== value) {
      hasChanges = true;
      break;
    }
  }

  if (!hasChanges) {
    console.log('[AnthropicOptions] 设置无变化，跳过更新');
    return;
  }

  console.log('[AnthropicOptions] 检测到设置变化，更新:', newSettings);

  store.updateAnthropicSettings(newSettings);
};

// 更新可用模型
const updateAvailableModels = (models: CategorizedModels) => {
  updateSettings({ avaliableModels: models });
};

watch(
  settings,
  (newSettings) => {
    if (!newSettings.apiKey || newSettings.apiKey.trim() === '') {
      settings.value.enabled = false;
    }
  },
  { immediate: true, deep: true },
);
</script>

<style lang="scss" scoped>
.anthropic-settings {
  width: 100%;
}
</style>
