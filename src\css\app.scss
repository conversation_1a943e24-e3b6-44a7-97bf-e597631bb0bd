// app global css in SCSS form
@use 'sass:map';
@use 'sass:math';

// Variables for colors
$primary-dark: #002447;
$primary-darker: #001e3c;
$primary-9: #003569;
$primary-op-xs: #1976d245;
$darker: #171717;
$light-bg-primary-darker: #eaeaea;
$light-border-color: rgba(191, 191, 191, 0.5);
$dark-border-color: rgba(123, 123, 123, 0.3);
$border-placeholder-color: rgba(123, 123, 123, 0);
$white: #ffffff;
$black: #000000;

// Variables for border colors
$border-primary: #1976d2;
$border-secondary: #26a69a;
$border-accent: #9c27b0;
$border-dark: #1d1d1d;
$border-positive: #21ba45;
$border-negative: #c10015;
$border-info: #31ccec;
$border-warning: #f2c037;
$border-orange: #ff7b00;
$border-green: green;

// Variables for border radius
$radius-xs: 0.25rem;
$radius-sm: 0.5rem;
$radius-md: 0.75rem;
$radius-lg: 1rem;
$radius-xl: 1.25rem;

// Variables for spacing (gap)
$gap-xxs: 2px;
$gap-xs: 4px;
$gap-sm: 8px;
$gap-md: 12px;
$gap-lg: 16px;
$gap-xl: 20px;
$gap-xxl: 3rem;

// Mixin for border opacity
@mixin border-opacity($opacity, $light-mode-color: null) {
  border-color: rgba(200, 200, 200, $opacity);

  .body--light & {
    @if $light-mode-color {
      border-color: $light-mode-color;
    } @else {
      border-color: rgba(112, 112, 112, $opacity);
    }
  }
}

// Mixin for border radius
@mixin border-radius($radius, $force: false) {
  @if $force {
    border-radius: $radius !important;
    -webkit-border-radius: $radius !important;
    -moz-border-radius: $radius !important;
    -ms-border-radius: $radius !important;
    -o-border-radius: $radius !important;
  } @else {
    border-radius: $radius;
    -webkit-border-radius: $radius;
    -moz-border-radius: $radius;
    -ms-border-radius: $radius;
    -o-border-radius: $radius;
  }
}

.auto-fill {
  fill: #121212;
  .body--dark & {
    fill: #ededed;
  }
}

/* 优化transition性能 - 只在必要时使用 */
.transition {
  transition:
    transform 0.2s cubic-bezier(0.25, 0.8, 0.25, 1),
    opacity 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  /* 使用GPU加速 */
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* 为抽屉切换时禁用所有transition */
.drawer-transitioning .transition {
  transition: none !important;
}

.unselectable {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
}

.z-unfab {
  z-index: -99;
}

.z-unmax {
  z-index: -9999;
}

// Background colors
.bg-primary-dark {
  background-color: $primary-dark;
}

.bg-primary-darker {
  background-color: $primary-darker;
  .body--light & {
    background-color: $light-bg-primary-darker;
  }
}

.bg-primary-9 {
  background-color: $primary-9;
}

.bg-primary-op-xs {
  background-color: $primary-op-xs;
}

.bg-darker {
  background-color: $darker;
}

.bg-repeat {
  background-repeat: repeat;
}

.bg-repeat-x {
  background-repeat: repeat-x;
}

.bg-repeat-y {
  background-repeat: repeat-y;
}

.bg-contain {
  background-size: contain;
}

.bg-cover {
  background-size: cover;
}

// Borders
.borderless {
  border: 0 solid transparent !important;
}
.border-placehoder {
  border: 1px solid rgba(0, 0, 0, 0);
}

.border-op-none {
  @include border-opacity(0);
}

.border-op-xs {
  @include border-opacity(0.1);
}

.border-op-sm {
  @include border-opacity(0.3);
}

.border-op-md {
  @include border-opacity(0.5);
}

.border-op-lg {
  @include border-opacity(0.7);
}

.border-op-xl {
  @include border-opacity(0.9);
}

.border-op-full {
  @include border-opacity(0); // This was originally 0
}

.border {
  border: 1px solid $dark-border-color;
  .body--light & {
    border: 1px solid $light-border-color;
  }
}

.border-top {
  border-top: 1px solid $dark-border-color;
  .body--light & {
    border-top: 1px solid $light-border-color;
  }
}

.border-bottom {
  border-bottom: 1px solid $dark-border-color;
  .body--light & {
    border-bottom: 1px solid $light-border-color;
  }
}

.border-right {
  border-right: 1px solid $dark-border-color;
  .body--light & {
    border-right: 1px solid $light-border-color;
  }
}

.border-left {
  border-left: 1px solid $dark-border-color;
  .body--light & {
    border-left: 1px solid $light-border-color;
  }
}

.border-placeholder {
  border: 1px solid $border-placeholder-color;
}

.border-bottom-placeholder {
  border-bottom: 1px solid rgba($black, 0);
}

.hover-border {
  border: 1px solid rgba(123, 123, 123, 0);
  &:hover {
    border: 1px solid rgba(123, 123, 123, 0.3);
  }
}

@for $i from 0 through 9 {
  .border-white-op-#{$i} {
    border: 1px solid rgba($white, $i * 0.1);
  }
}

.border-white {
  border: 1px solid rgba($white, 1);
}

.border-xs {
  border-width: 1px;
}

.border-sm {
  border-width: 3px;
}

.border-md {
  border-width: 5px;
}

.border-lg {
  border-width: 7px;
}

.border-xl {
  border-width: 9px;
}

.border-primary {
  border-color: $border-primary !important;
}

.border-secondary {
  border-color: $border-secondary;
}

.border-accent {
  border-color: $border-accent;
}

.border-dark {
  border-color: $border-dark;
}

.border-positive {
  border-color: $border-positive;
}

.border-negative {
  border-color: $border-negative;
}

.border-info {
  border-color: $border-info !important;
}

.border-warning {
  border-color: $border-warning;
}

.border-orange {
  border-color: $border-orange;
}

.border-solid {
  border-style: solid;
}

.border-dotted {
  border-style: dotted;
}

.border-dashed {
  border-style: dashed;
  border-width: 0.3px;
}

.border-green {
  border-color: $border-green;
}

.border-shadow-light {
  box-shadow: inset 0px -1px 0px 0px #3333332e;
}

// Gap utilities
.gap-xs {
  gap: $gap-xs;
}
.gap-xxs {
  gap: $gap-xxs;
}

.gap-sm {
  gap: $gap-sm;
}

.gap-md {
  gap: $gap-md;
}

.gap-lg {
  gap: $gap-lg;
}

.gap-xl {
  gap: $gap-xl;
}

.gap-xxl {
  gap: $gap-xxl;
}
.op-0 {
  opacity: 0;
}
.op-1 {
  opacity: 0.1;
}
.op-2 {
  opacity: 0.2;
}
.op-3 {
  opacity: 0.3;
}
.op-4 {
  opacity: 0.4;
}
.op-5 {
  opacity: 0.5;
}
.op-6 {
  opacity: 0.6;
}
.op-7 {
  opacity: 0.7;
}
.op-8 {
  opacity: 0.8;
}
.op-9 {
  opacity: 0.9;
}
.op-none {
  opacity: 1;
}

// Border radius
.radius-none {
  @include border-radius(0, true);
}

.radius-xs {
  @include border-radius($radius-xs);
}

.radius-xs-f {
  @include border-radius($radius-xs, true);
}

.radius-sm {
  @include border-radius($radius-sm);
}

.radius-sm-f {
  @include border-radius($radius-sm, true);
}

.radius-md {
  @include border-radius($radius-md);
}

.radius-lg {
  @include border-radius($radius-lg);
}

.radius-xl {
  @include border-radius($radius-xl);
}

.radius-full {
  @include border-radius(50%, true);
}

.no-radius {
  @include border-radius(0, true);
}

// Padding
.q-pa-xxs {
  padding: 2px;
}

// Tiptap editor styles
.article,
.tiptap {
  word-wrap: break-word;
  word-break: break-all;

  .doc-title {
    .q-field__control {
      height: unset !important;
    }
  }

  h1,
  .text-h1 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 1.25rem;
  }

  h2,
  .text-h2 {
    font-size: 3rem;
    font-weight: 600;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 1.25rem;
  }

  h3,
  .text-h3 {
    font-size: 2.5rem;
    font-weight: 600;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 1.25rem;
  }

  h4,
  .text-h4 {
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 1rem;
  }

  h5,
  .text-h5 {
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 0.6rem;
  }

  h6,
  .text-h6 {
    font-size: 1.2rem;
    font-weight: 400;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 0.4rem;
  }
}

.tiptap {
  // * {
  //   max-width: 100%;
  // }

  li[data-checked='true'] {
    text-decoration: line-through;
    opacity: 0.3;
  }

  code[class*='language-'],
  pre[class*='language-'] {
    color: unset !important;
    text-shadow: unset !important;
  }

  pre {
    background: #0e0e0e;
    border-radius: 0.3rem;
    color: var(--white);
    font-family: JetBrainsMono, monospace;
    padding: 0.75rem 1rem;
    border: 1px solid #333;
    overflow: auto;

    .body--light & {
      background: #efefef;
      border: 1px solid #ccc;
    }
  }

  hr {
    opacity: 0.1;
    .body--light & {
      opacity: 0.3;
    }
  }
}

// Custom Scrollbar Styles (macOS-like)
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba($white, 0.2);
  border-radius: 10px;
  border: 3px solid transparent;
  background-clip: content-box;
  transition: background-color 0.3s;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba($white, 0.4);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

.body--light {
  &::-webkit-scrollbar-thumb,
  & ::-webkit-scrollbar-thumb {
    background-color: rgba($black, 0.2);
  }

  &::-webkit-scrollbar-thumb:hover,
  & ::-webkit-scrollbar-thumb:hover {
    background-color: rgba($black, 0.4);
  }
}

body.body--dark .shadow-3 {
  box-shadow:
    0 1px 5px rgba(0, 0, 0, 0.2),
    0 2px 2px rgba(0, 0, 0, 0.14),
    0 3px 1px -2px rgba(0, 0, 0, 0.12);
}
body.body--dark .shadow-6 {
  box-shadow:
    0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px rgba(0, 0, 0, 0.14),
    0 1px 18px rgba(0, 0, 0, 0.12);
}
body.body--dark .shadow-12 {
  box-shadow:
    0 7px 8px -4px rgba(0, 0, 0, 0.2),
    0 12px 17px 2px rgba(0, 0, 0, 0.14),
    0 5px 22px 4px rgba(0, 0, 0, 0.12);
}
body.body--dark .shadow-24 {
  box-shadow:
    0 11px 15px -7px rgba(0, 0, 0, 0.2),
    0 24px 38px 3px rgba(0, 0, 0, 0.14),
    0 9px 46px 8px rgba(0, 0, 0, 0.12);
}

.q-stepper--dark {
  box-shadow:
    0 1px 5px rgba(0, 0, 0, 0.2),
    0 2px 2px rgba(0, 0, 0, 0.14),
    0 3px 1px -2px rgba(0, 0, 0, 0.12);
}
body.body--dark .shadow-up-10 {
  box-shadow:
    0 -6px 6px -3px rgba(0, 0, 0, 0.2),
    0 -10px 14px 1px rgba(0, 0, 0, 0.14),
    0 -4px 18px 3px rgba(0, 0, 0, 0.12);
}
body.body--dark .shadow-10 {
  box-shadow:
    0 6px 6px -3px rgba(0, 0, 0, 0.2),
    0 10px 14px 1px rgba(0, 0, 0, 0.14),
    0 4px 18px 3px rgba(0, 0, 0, 0.12);
}

.q-menu--dark {
  box-shadow:
    0 1px 5px rgba(0, 0, 0, 0.2),
    0 2px 2px rgba(0, 0, 0, 0.14),
    0 3px 1px -2px rgba(0, 0, 0, 0.12);
}

.q-menu {
  border: 1px solid rgba(123, 123, 123, 0.3);
  border-radius: 0.35rem;
  -webkit-border-radius: 0.35rem;
  -moz-border-radius: 0.35rem;
  -ms-border-radius: 0.35rem;
  -o-border-radius: 0.35rem;
}
.q-menu .q-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.15rem;
}
.q-menu .q-list .q-item {
  border-radius: 0.25rem;
  -webkit-border-radius: 0.25rem;
  -moz-border-radius: 0.25rem;
  -ms-border-radius: 0.25rem;
  -o-border-radius: 0.25rem;
}

.q-pa-xxs {
  padding: 2px;
}

// tiptap编辑器样式覆盖
.article,
.tiptap {
  word-wrap: break-word;
  word-break: break-all;
}
.article h1,
.article h1,
.tiptap .text-h1,
.tiptap .text-h1 {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.25;
  margin-top: 0.75rem;
  margin-bottom: 1.25rem;
}
.article h2,
.tiptap h2,
.tiptap .text-h2,
.tiptap .text-h2 {
  font-size: 3rem;
  font-weight: 600;
  line-height: 1.25;
  margin-top: 0.75rem;
  margin-bottom: 1.25rem;
}
.article h3,
.tiptap h3,
.tiptap .text-h3,
.tiptap .text-h3 {
  font-size: 2.5rem;
  font-weight: 600;
  line-height: 1.25;
  margin-top: 0.75rem;
  margin-bottom: 1.25rem;
}
.article h4,
.tiptap h4,
.tiptap .text-h4,
.tiptap .text-h4 {
  font-size: 2rem;
  font-weight: 600;
  line-height: 1.25;
  margin-top: 0.75rem;
  margin-bottom: 1rem;
}
.article h5,
.tiptap h5,
.tiptap .text-h5,
.tiptap .text-h5 {
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 1.25;
  margin-top: 0.75rem;
  margin-bottom: 0.6rem;
}
.article h6,
.tiptap h6,
.tiptap .text-h6,
.tiptap .text-h6 {
  font-size: 1.2rem;
  font-weight: 400;
  line-height: 1.25;
  margin-top: 0.75rem;
  margin-bottom: 0.4rem;
}
.editor-container {
  position: relative;
}

.ProseMirror {
  position: relative;
  font-family:
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Android Emoji', 'EmojiSymbols',
    'Twemoji Mozilla', 'sans-serif', 'JetBrainsMono', 'monospace';
  font-size: inherit;
  line-height: inherit;
  outline: none;
  caret-color: #ff6347;
  > * {
    margin-left: 2rem;
    margin-right: 2rem;
  }
  .ProseMirror-widget * {
    margin-top: auto;
  }
  s {
    opacity: 0.2;
  }

  .ProseMirror-widget * {
    margin-top: auto;
  }

  > * + * {
    margin-top: 0.75em;
  }

  // 标题样式使用相对单位
  h1 {
    font-size: 2.5em;
    font-weight: 800;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 1.25rem;
  }

  h2 {
    font-size: 2em;
    font-weight: 600;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 1.25rem;
  }

  h3 {
    font-size: 1.75em;
    font-weight: 600;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 1.25rem;
  }

  h4 {
    font-size: 1.5em;
    font-weight: 600;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 1rem;
  }

  h5 {
    font-size: 1.25em;
    font-weight: 400;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 0.6rem;
  }

  h6 {
    font-size: 1.1em;
    font-weight: 400;
    line-height: 1.25;
    margin-top: 0.75rem;
    margin-bottom: 0.4rem;
  }

  p.is-editor-empty:first-child::before {
    color: #adb5bd;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  /* 代码块样式 */
  pre {
    border-radius: 0.5rem;
    font-family: 'JetBrainsMono', monospace;
    padding: 0.75rem 1rem;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    code {
      background: none;
      display: block;
      font-size: 0.9rem;
      line-height: 1.6;
      padding: 0;
      white-space: pre;
    }
  }

  p {
    code {
      font-family: 'JetBrainsMono', monospace, monospace;
      font-size: 1em;
      padding: 2px 6px;
      margin: 0 4px;
      border: 1px solid #d4d4d4;
      border-radius: 4px;
      background-color: aliceblue;
    }
  }

  /* 暗色主题适配 */
  .body--dark & {
    pre {
      background: #0e0e0e;
    }
    code {
      background: #0e0e0e;
    }
  }

  /* 任务列表样式 */
  ul[data-type='taskList'] {
    list-style: none;
    padding: 0;

    li {
      display: flex;
      align-items: flex-start;
      margin: 0.5rem 0;

      > label {
        flex: 0 0 auto;
        margin-right: 0.5rem;
        user-select: none;
      }

      > div {
        flex: 1 1 auto;
      }
    }
  }

  /* 图片样式 */
  .editor-image {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
    border-radius: 4px;
    cursor: pointer;
    transition: opacity 0.2s;

    &:hover {
      opacity: 0.8;
    }
  }

  /* 文本对齐样式 */
  [style*='text-align: center'] {
    text-align: center;
  }

  [style*='text-align: right'] {
    text-align: right;
  }

  [style*='text-align: justify'] {
    text-align: justify;
  }

  /* 高亮样式 */
  mark {
    background-color: #faf594;
    border-radius: 0.2em;
    padding: 0.1em 0.2em;
  }

  /* 下标和上标样式 */
  sub {
    vertical-align: sub;
    font-size: smaller;
  }

  sup {
    vertical-align: super;
    font-size: smaller;
  }

  /* 下划线样式 */
  u {
    text-decoration: underline;
  }

  table {
    border-collapse: collapse;
    margin: 0 2rem;
    overflow: hidden;
    table-layout: fixed;
    width: 100%;

    td,
    th {
      border: 1px solid rgba(150, 150, 150, 0.22);
      box-sizing: border-box;
      min-width: 1em;
      padding: 3px 5px;
      position: relative;
      vertical-align: top;

      > * {
        margin-bottom: 0;
      }
    }

    th {
      background-color: rgba(100, 100, 100, 0.15);
      font-weight: bold;
      text-align: left;
    }
  }

  .selectedCell:after {
    background: rgba(40, 40, 40, 0.2);
    content: '';
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    pointer-events: none;
    position: absolute;
    z-index: 2;
  }

  .column-resize-handle {
    background-color: rgba(255, 255, 255, 0.3);
  }
}

// 暗色主题适配
.body--dark {
  .ProseMirror {
    table {
      td,
      th {
        border-color: rgba(255, 255, 255, 0.22);
      }

      th {
        background-color: rgba(255, 255, 255, 0.15);
      }
    }

    .selectedCell:after {
      background: rgba(255, 255, 255, 0.2);
    }

    .column-resize-handle {
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
}

/* 数学公式样式 */
.Tiptap-mathematics-editor {
  border: 1px solid $grey-6;
  border-radius: 4px;
  padding: 0.5rem;
  margin: 0.5rem 0;
  background-color: $grey-10;
}

.Tiptap-mathematics-render {
  padding: 0.5rem;
  margin: 0.5rem 0;
  overflow-x: auto;
}

.bubble-menu {
  .q-separator--vertical-inset {
    margin-top: 8px;
    margin-bottom: 8px;
  }
}

.catalog-resize-handle {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  cursor: col-resize;
  background-color: transparent;
  transition: background-color 0.2s;

  &:hover,
  &:active {
    background-color: $primary;
  }
}

/* 目录样式 */
.table-of-contents {
  margin-left: 4px; // 为拖拽手柄留出空间

  > div {
    transition: all 0.2s;

    &:hover {
      transform: translateX(4px);
    }

    &.is-active {
      > a {
        color: $orange-10;
        font-weight: bold;
      }
    }

    &.is-scrolled {
      > a {
        color: $orange-10;
      }
    }

    a {
      text-decoration: none;
      color: $grey-7;
      display: block;
      transition: color 0.2s;

      &:hover {
        color: $secondary;
      }
    }
  }
}
.body--dark {
  a {
    color: $grey-3;
  }
  .bubble-menu {
    background-color: $black;
  }
}

/* 标题样式 */
.heading {
  scroll-margin-top: 2rem;
  position: relative;
}

// 编辑器 - 节点拖拽控制开始
// 全局选中状态样式
::selection {
  background-color: #70cff850;
}

// 确保所有元素的选中状态都可见
* {
  &::selection {
    background-color: #70cff850;
  }
}

// 暗色主题下的全局选中状态
.body--dark {
  ::selection {
    background-color: #4a90e2aa;
  }

  * {
    &::selection {
      background-color: #4a90e2aa;
    }
  }
}

// 修复文本选中状态样式
.ProseMirror {
  // 确保文本选中状态可见
  ::selection {
    background-color: #70cff850 !important;
    color: inherit;
  }

  // 修复包含图片的选中状态
  .ProseMirror-selectednode {
    outline: 2px solid #70cff8;
    outline-offset: 2px;
  }

  // 当编辑器有选中内容时的样式增强
  &.has-selection {
    ::selection {
      background-color: #70cff8 !important;
      color: inherit;
    }

    // 确保图片在选中状态下也有正确的背景
    .image-container {
      &::selection {
        background-color: #70cff850 !important;
      }
    }
  }
}

// 暗色主题下的选中状态
.body--dark .ProseMirror {
  ::selection {
    background-color: #4a90e2aa !important;
  }

  .ProseMirror-selectednode {
    outline-color: #4a90e2;
  }

  // 暗色主题下有选中内容时的样式增强
  &.has-selection {
    ::selection {
      background-color: #4a90e2 !important;
      color: inherit;
    }

    .image-container {
      &::selection {
        background-color: #4a90e2aa !important;
      }
    }
  }
}

.ProseMirror-noderangeselection {
  *::selection {
    background: transparent;
  }

  * {
    caret-color: transparent;
  }
}

.ProseMirror-selectednode,
.ProseMirror-selectednoderange {
  position: relative;
}

.custom-drag-handle {
  &::after {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.25rem;
    height: 2rem;
    content: '⠿';
    font-size: 1rem;
    line-height: 1.25rem;
    font-weight: 700;
    cursor: grab;
    background: #0d0d0d00;
    color: #0d0d0da4;
    border-radius: 0.25rem;
    transform: translateX(-1rem);
    -webkit-transform: translateX(-1rem);
    -moz-transform: translateX(-1rem);
    -ms-transform: translateX(-1rem);
    -o-transform: translateX(-1rem);
  }
}
.body--dark {
  .custom-drag-handle {
    &::after {
      background: #4a4a4a00;
      color: #b8b8b8ea;
    }
  }
}

// 确保拖拽手柄容器正确定位
.ProseMirror > * {
  position: relative;
}

/* Details 组件样式 */
/* Details */
.details {
  display: flex;
  gap: 0.25rem;
  margin: 1.5rem 0;
  padding: 0.5rem;

  summary {
    font-weight: 700;
  }

  > button {
    /* 移除原生样式 */
    border: none;
    background: none;
    outline: none;
    box-shadow: none;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    /* 保留自定义样式 */
    align-items: center;
    border-radius: 4px;
    display: flex;
    font-size: 0.625rem;
    height: 1.25rem;
    justify-content: center;
    line-height: 1;
    margin-top: 0.1rem;
    padding: 0;
    width: 1.25rem;

    &:hover {
      cursor: pointer;
    }

    &::before {
      content: '\25B6';
      color: #adb5bd;
    }
  }

  &.is-open > button::before {
    transform: rotate(90deg);
  }

  > div {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;

    > [data-type='detailsContent'] > :last-child {
      margin-bottom: 0.5rem;
    }
  }

  .details {
    margin: 0.5rem 0;
  }
}

.left-switch .q-item__section--side,
.left-switch .q-item__section--avatar {
  align-items: flex-start !important;
  min-width: unset !important;
  padding: 0 !important;
}

.q-expansion-item .q-item {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

.folder-tree.q-expansion-item .q-item {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.hover-item {
  .hover-show-item {
    opacity: 0;
    transition: all 200ms;
    -webkit-transition: all 200ms;
    -moz-transition: all 200ms;
    -ms-transition: all 200ms;
    -o-transition: all 200ms;
  }
  &:hover {
    .hover-show-item {
      opacity: 1;
    }
  }
}

.radius-helper {
  .q-focus-helper {
    border-radius: 0.25rem !important;
    -webkit-border-radius: 0.25rem !important;
    -moz-border-radius: 0.25rem !important;
    -ms-border-radius: 0.25rem !important;
    -o-border-radius: 0.25rem !important;
  }
}

.hover-highlight {
  background-color: rgba(109, 109, 109, 0);
  &:hover {
    background-color: rgba(109, 109, 109, 0.1);
  }
  .body--dark & {
    &:hover {
      background-color: rgba(109, 109, 109, 0.5);
    }
  }
}

.border-placeholder {
  border: 1px solid rgba(109, 109, 109, 0);
}
.border-none {
  border: none !important;
}
.border-active {
  border: 1px solid $primary;
}
.border-inset {
  border-style: inset;
}

.ink-dark {
  background-color: $ink-dark;
}
.tiptap {
  pre {
    code {
      background-color: transparent;
    }
  }
  li {
    p {
      margin-bottom: 0;
    }
  }
}

// 修改标记样式
[data-track-change] {
  position: relative;
  padding: 0 2px;
  border-radius: 2px;

  &[data-track-change-type='insertion'] {
    background-color: rgba(0, 255, 0, 0.1);
    text-decoration: underline;
    text-decoration-style: wavy;
    text-decoration-color: #00c853;
  }

  &[data-track-change-type='deletion'] {
    background-color: rgba(255, 0, 0, 0.1);
    text-decoration: line-through;
    text-decoration-style: wavy;
    text-decoration-color: #ff1744;
  }

  &[data-track-change-type='formatting'] {
    background-color: rgba(0, 0, 255, 0.1);
    text-decoration: underline;
    text-decoration-style: dotted;
    text-decoration-color: #2979ff;
  }

  &[data-track-change-status='accepted'] {
    background-color: transparent;
    text-decoration: none;
  }

  &[data-track-change-status='rejected'] {
    display: none;
  }

  &:hover::after {
    content: attr(data-track-change-comment);
    position: absolute;
    bottom: 100%;
    left: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.body--dark {
  [data-track-change] {
    &[data-track-change-type='insertion'] {
      background-color: rgba(0, 255, 0, 0.2);
    }

    &[data-track-change-type='deletion'] {
      background-color: rgba(255, 0, 0, 0.2);
    }

    &[data-track-change-type='formatting'] {
      background-color: rgba(0, 0, 255, 0.2);
    }

    &:hover::after {
      background: #1e1e1e;
      border-color: #333;
      color: #fff;
    }
  }
}

.agentwriter-selection-highlight {
  padding: 0 4px;
  border: 1px solid tomato;
  margin: 0 4px;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
}
.agentwriter-range {
  height: 1.2rem;
  position: relative;
}
.agentwriter-range::after {
  content: 'ai processing...';
  margin-left: 4px;
  color: tomato;
  position: absolute;
  top: -70%;
  left: -0.1rem;
  white-space: nowrap;
  z-index: 999;
  font-size: 0.8rem;
  line-height: 1.2;
}

.agentwriter-flag {
  width: 1px;
  height: 1.2rem;
  background-color: tomato;
  position: relative;
  padding-left: 2px;
}
.agentwriter-flag::after {
  content: 'ai processing...';
  color: tomato;
  position: absolute;
  top: -60%;
  left: -2px;
  white-space: nowrap;
  z-index: 999;
  font-size: 0.8rem;
  line-height: 1.2;
}

// 自动补全建议样式
.auto-complete-suggestion {
  opacity: 0.2;
  transition: opacity 0.2s ease;
}

.auto-complete-suggestion.confirmed {
  opacity: 1;
}

.q-card--dark {
  box-shadow:
    0 1px 5px rgba(0, 0, 0, 0.2),
    0 2px 2px rgba(0, 0, 0, 0.14),
    0 3px 1px -2px rgba(0, 0, 0, 0.12);
}

.image-container {
  position: relative;
  display: inline-block;
  max-width: 100%;
  padding: 4px;
  border: 3px solid rgba(0, 0, 0, 0);
  border-radius: 8px;

  // 确保图片容器在选中状态下保持可见
  &.ProseMirror-selectednode {
    border: 3px solid $primary;
    background-color: rgba(25, 118, 210, 0.1);
    // 移除全局的outline样式，只保留border
    outline: none !important;
  }

  // 修复包含图片的文本选中状态
  &::selection {
    background-color: #70cff850 !important;
  }
}

// 暗色主题下的图片容器选中状态
.body--dark .image-container {
  &.ProseMirror-selectednode {
    background-color: rgba(74, 144, 226, 0.2);
    // 移除全局的outline样式，只保留border
    outline: none !important;
  }

  &::selection {
    background-color: #4a90e2aa !important;
  }
}

.image-preview {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
  border-radius: 4px;
  transition: opacity 0.3s ease;

  // 确保图片不会阻止选中状态的显示
  &::selection {
    background-color: transparent;
  }

  // 图片被选中时的样式
  // &.selected-image {
  //   border: 2px solid #007bff;
  //   box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  // }
}

.image-preview.loading {
  opacity: 0.5;
  filter: grayscale(1);
}

.image-preview.error {
  opacity: 0.5;
  filter: grayscale(1) brightness(0.8);
}

.image-preview[src^='data:image/svg+xml'] {
  width: 24px;
  height: 24px;
  opacity: 0.5;
}

.image-delete-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover .image-delete-button {
  opacity: 1;
}

.image-delete-button:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.positive-shadow {
  box-shadow: 0 0 6px 0 rgba(0, 255, 0, 0.5);
}
.negative-shadow {
  box-shadow: 0 0 6px 0 rgba(255, 0, 0, 0.5);
}

.highlight-tree-item {
  border: 1px solid $primary;
  background-color: #1976d242;
}
.body--light {
  .highlight-tree-item {
    border: 1px solid #1976d275;
    background-color: rgb(25 118 210 / 9%);
  }
}
.lowlight-tree-item {
  border: 1px solid #00000000;
  background-color: #1976d214;
}
.body--light {
  .lowlight-tree-item {
    border: 1px solid #00000000;
    background-color: rgb(25 118 210 / 4%);
  }
}
.tool-result {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.8em;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin: 4px 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 260px;
  overflow-y: auto;
}

.input-pr-none {
  .q-field__control {
    padding-right: 0 !important;
  }
}
.input-pl-none {
  .q-field__control {
    padding-left: 0 !important;
  }
}
.input-px-none {
  .q-field__control {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
.input-px-xs {
  .q-field__control {
    padding-left: 4px !important;
    padding-right: 4px !important;
  }
}
.input-pl-xs {
  .q-field__control {
    padding-left: 4px !important;
  }
}
.input-pr-xs {
  .q-field__control {
    padding-right: 4px !important;
  }
}
.dense-input {
  .q-field__control,
  .q-field__marginal {
    height: 32px !important;
    padding: 0 6px;
  }
  .q-field__native {
    padding: 0 !important;
  }
}
.q-dialog-plugin {
  border: 1px solid #333;
}
.q-dialog__title {
  padding-top: 8px;
  padding-bottom: 8px;
  font-weight: 600;
}
