/**
 * 剪贴板工具函数
 * 提供跨平台的剪贴板操作支持，特别针对Qt WebEngine环境优化
 */

import { $t } from 'src/composables/useTrans';

import { Notify } from 'quasar';

/**
 * 尝试使用Qt特定的剪贴板方法
 * @param text 要复制的文本
 */
async function tryQtClipboard(text: string): Promise<boolean> {
  // 在Qt WebEngine环境中，我们可以尝试一些特殊的方法

  // 方法1: 尝试使用Qt的execCommand，但在安全的上下文中
  try {
    // 确保文档有焦点
    if (document.hasFocus && !document.hasFocus()) {
      window.focus();
    }

    // 创建一个可见的文本区域（Qt可能需要可见元素）
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'absolute';
    textArea.style.left = '0px';
    textArea.style.top = '0px';
    textArea.style.width = '1px';
    textArea.style.height = '1px';
    textArea.style.opacity = '0.01'; // 几乎不可见但不是完全透明
    textArea.style.zIndex = '-1000';

    document.body.appendChild(textArea);

    // 确保元素获得焦点
    textArea.focus();
    textArea.select();

    // 等待一小段时间确保选择生效
    await new Promise((resolve) => setTimeout(resolve, 10));

    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);

    console.log('🔄 [Clipboard] Qt特定方法 execCommand 结果:', successful);
    return successful;
  } catch (error) {
    console.error('❌ [Clipboard] Qt特定方法失败:', error);
    return false;
  }
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param options 配置选项
 */
export async function copyToClipboard(
  text: string,
  options: {
    showNotification?: boolean;
    successMessage?: string;
    errorMessage?: string;
  } = {},
): Promise<boolean> {
  const {
    showNotification = true,
    successMessage = $t('src.utils.clipboard.successMessage'),
    errorMessage = $t('src.utils.clipboard.errorMessage'),
  } = options;

  // 方法0: 检查是否在Qt环境中，尝试使用Qt特定的方法
  const isQtEnv = !!(window as unknown as { qtWindow: boolean }).qtWindow;
  if (isQtEnv) {
    console.log('🔄 [Clipboard] 检测到Qt环境，尝试Qt特定的复制方法');

    try {
      // 在Qt环境中，我们可以尝试直接操作系统剪贴板
      // 这里我们仍然使用标准方法，但添加特殊处理
      const qtResult = await tryQtClipboard(text);
      if (qtResult) {
        console.log('✅ [Clipboard] Qt特定方法复制成功');

        if (showNotification) {
          Notify.create({
            type: 'positive',
            message: successMessage,
            color: 'primary',
            timeout: 2000,
          });
        }

        return true;
      }
    } catch (error) {
      console.error('❌ [Clipboard] Qt特定方法失败:', error);
    }
  }

  // 方法1: 尝试使用现代剪贴板API
  if (navigator.clipboard && navigator.clipboard.writeText) {
    console.log('🔄 [Clipboard] 尝试使用现代剪贴板API:', text);

    try {
      await navigator.clipboard.writeText(text);
      console.log('✅ [Clipboard] 现代API复制成功');

      if (showNotification) {
        Notify.create({
          type: 'positive',
          message: successMessage,
          color: 'primary',
          timeout: 2000,
        });
      }

      return true;
    } catch (error) {
      // 现代API失败，尝试备用方法
      console.error('❌ [Clipboard] 现代API复制失败，尝试备用方法:', error);
    }
  } else {
    console.log('⚠️ [Clipboard] 现代剪贴板API不可用，直接使用备用方法');
  }

  // 方法2: 备用方法 - 使用传统的execCommand
  console.log('🔄 [Clipboard] 尝试使用备用方法 (execCommand):', text);

  try {
    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    textArea.style.pointerEvents = 'none';

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    console.log('🔄 [Clipboard] 执行 document.execCommand("copy")');
    // 尝试执行复制命令
    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);

    console.log('📋 [Clipboard] execCommand 结果:', successful);

    if (successful) {
      console.log('✅ [Clipboard] 备用方法复制成功');

      if (showNotification) {
        Notify.create({
          type: 'positive',
          message: successMessage,
          color: 'primary',
          timeout: 2000,
        });
      }

      return true;
    } else {
      throw new Error('execCommand failed');
    }
  } catch (error) {
    console.error('❌ [Clipboard] 备用方法也失败了:', error);

    if (showNotification) {
      console.error('❌ [Clipboard] 所有复制方法都失败了:', error);
      Notify.create({
        type: 'negative',
        message: `${errorMessage}，${$t('src.utils.clipboard.checkBrowserPermission')}`,
        timeout: 3000,
      });
    }

    return false;
  }
}

/**
 * 从剪贴板读取文本
 * @param options 配置选项
 */
export async function readFromClipboard(
  options: {
    showNotification?: boolean;
    errorMessage?: string;
  } = {},
): Promise<string | null> {
  const { showNotification = true, errorMessage = $t('src.utils.clipboard.errorMessage') } =
    options;

  // 只能使用现代剪贴板API读取
  if (navigator.clipboard && navigator.clipboard.readText) {
    try {
      const text = await navigator.clipboard.readText();
      return text;
    } catch (error) {
      if (showNotification) {
        console.error('❌ [Clipboard] 读取剪贴板失败:', error);
        Notify.create({
          type: 'negative',
          message: `${errorMessage}，${$t('src.utils.clipboard.checkBrowserPermission')}`,
          timeout: 3000,
        });
      }

      return null;
    }
  } else {
    if (showNotification) {
      console.error('❌ [Clipboard] 浏览器不支持剪贴板读取功能');
      Notify.create({
        type: 'negative',
        message: $t('src.utils.clipboard.browserNotSupported'),
        timeout: 3000,
      });
    }

    return null;
  }
}

/**
 * 检查剪贴板API是否可用
 */
export function isClipboardSupported(): {
  write: boolean;
  read: boolean;
} {
  return {
    write: !!(navigator.clipboard?.writeText || document.execCommand),
    read: !!navigator.clipboard?.readText,
  };
}

/**
 * 检查是否在安全上下文中（HTTPS或localhost）
 */
export function isSecureContext(): boolean {
  return (
    window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost'
  );
}

/**
 * 获取剪贴板状态信息
 */
export function getClipboardInfo(): {
  supported: { write: boolean; read: boolean };
  secureContext: boolean;
  userAgent: string;
  isQt: boolean;
} {
  const supported = isClipboardSupported();
  const secureContext = isSecureContext();
  const userAgent = navigator.userAgent;
  const isQt =
    userAgent.includes('InkCop-Qt') || !!(window as unknown as { qtWindow: boolean }).qtWindow;

  return {
    supported,
    secureContext,
    userAgent,
    isQt,
  };
}
