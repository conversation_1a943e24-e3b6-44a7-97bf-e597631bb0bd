// Fluent System Icons 类型定义

declare module '*.json' {
  const value: Record<string, number>
  export default value
}

// Fluent Icons 相关类型
export type FluentIconVariant = 'regular' | 'light' | 'filled' | 'resizable'

export interface FluentIconMap {
  [key: string]: string
}

export interface FluentIconOptions {
  variant?: FluentIconVariant
  size?: string | number
  color?: string
}

export interface FluentIconInfo {
  name: string
  unicode: string
  variants: FluentIconVariant[]
  categories?: string[]
}

// 扩展 Quasar 的 QIcon 组件类型
declare module 'quasar' {
  interface QIconProps {
    fluentVariant?: FluentIconVariant
  }
}

// 全局属性类型
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $fluentIcon: {
      get: (name: string, variant?: FluentIconVariant) => string
      createClass: (name: string, variant?: FluentIconVariant) => string
      available: string[]
      render: (iconName: string, variant?: FluentIconVariant) => HTMLElement | null
    }
  }
}

// 常用图标名称类型
export type CommonFluentIconName = 
  | 'add'
  | 'delete' 
  | 'edit'
  | 'save'
  | 'copy'
  | 'cut'
  | 'arrow_left'
  | 'arrow_right'
  | 'arrow_up'
  | 'arrow_down'
  | 'chevron_left'
  | 'chevron_right'
  | 'chevron_up'
  | 'chevron_down'
  | 'home'
  | 'settings'
  | 'search'
  | 'filter'
  | 'more'
  | 'close'
  | 'folder'
  | 'folder_open'
  | 'document'
  | 'chat'
  | 'mail'
  | 'phone'
  | 'checkmark'
  | 'error'
  | 'warning'
  | 'info'

// 图标搜索结果类型
export interface FluentIconSearchResult {
  name: string
  variants: FluentIconVariant[]
  score: number
}

// 图标集配置类型
export interface FluentIconSetConfig {
  defaultVariant: FluentIconVariant
  enableSearch: boolean
  maxSearchResults: number
  preloadVariants: FluentIconVariant[]
}

// 导出所有类型
export * from '../icons/fluent-icons'
export * from '../composables/useFluentIcons'
