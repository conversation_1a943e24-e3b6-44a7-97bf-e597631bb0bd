/**
 * Blob URL到图片ID的映射管理器
 * 用于支持文档间图片复制功能
 */

// 全局blob URL到图片ID的映射
const blobUrlToImageIdMap = new Map<string, number>();

/**
 * 注册blob URL到图片ID的映射
 */
export const registerBlobUrlMapping = (blobUrl: string, imageId: number): void => {
  blobUrlToImageIdMap.set(blobUrl, imageId);
  console.log(`[BlobUrlMapping] 注册映射: ${blobUrl} -> ${imageId}`);
};

/**
 * 从blob URL获取图片ID
 */
export const getImageIdFromBlobUrl = (blobUrl: string): number | null => {
  const imageId = blobUrlToImageIdMap.get(blobUrl);
  return imageId || null;
};

/**
 * 清理无效的blob URL映射
 */
export const cleanupBlobUrlMappings = (): void => {
  const keysToDelete: string[] = [];
  
  for (const [blobUrl] of blobUrlToImageIdMap) {
    // 检查blob URL是否仍然有效
    try {
      fetch(blobUrl, { method: 'HEAD' }).catch(() => {
        keysToDelete.push(blobUrl);
      });
    } catch {
      keysToDelete.push(blobUrl);
    }
  }

  keysToDelete.forEach((key) => {
    blobUrlToImageIdMap.delete(key);
  });
  
  console.log(`[BlobUrlMapping] 清理了 ${keysToDelete.length} 个无效映射`);
};

/**
 * 检查blob URL是否来自当前应用
 */
export const isBlobUrlFromCurrentApp = (blobUrl: string): boolean => {
  return blobUrl.startsWith('blob:') && blobUrl.includes(window.location.origin);
};

/**
 * 获取所有映射的调试信息
 */
export const getBlobUrlMappingDebugInfo = (): Array<{ blobUrl: string; imageId: number }> => {
  return Array.from(blobUrlToImageIdMap.entries()).map(([blobUrl, imageId]) => ({
    blobUrl,
    imageId,
  }));
};
