# 文件夹树排序功能实现

## 功能概述

已为 FolderTree.vue 组件实现了智能排序功能，支持：

1. **文件夹优先**：文件夹始终显示在文档前面
2. **智能排序规则**：按照数字、英文字符、中文拼音的字母顺序排序
3. **中文拼音支持**：使用 `pinyin-pro` 库实现高精度中文拼音转换
4. **实时排序**：新增文件夹或文档时自动应用排序

## 排序规则

### 1. 优先级顺序
1. **文件夹** - 始终在前
2. **文档** - 在文件夹后

### 2. 名称排序规则
按照首字符类型进行分类排序：

1. **数字** (0-9) - 最高优先级
2. **英文字符** (a-z, A-Z) - 中等优先级  
3. **中文字符** - 按拼音字母顺序排序
4. **其他字符** - 最低优先级

### 3. 排序示例

```
文件夹排序示例：
├── 1项目文档/
├── 2备份文件/
├── API文档/          (按拼音 "a" 排序)
├── Backend/
├── Frontend/
├── 测试文档/         (按拼音 "c" 排序)
├── 开发指南/         (按拼音 "k" 排序)
└── 用户手册/         (按拼音 "y" 排序)

文档排序示例：
├── 1.README.md
├── 2.安装指南.md     (按拼音 "a" 排序)
├── API.md
├── Config.md
├── 部署文档.md       (按拼音 "b" 排序)
└── 开发规范.md       (按拼音 "k" 排序)
```

## 技术实现

### 1. 第三方库
使用 `pinyin-pro` 库进行中文拼音转换：
- 准确率高达 99.846%
- 性能优异，支持 TypeScript
- 轻量级，无额外依赖

### 2. 核心文件

#### `src/utils/sortUtils.ts`
排序工具函数集合：

```typescript
// 获取排序键
function getSortKey(text: string): string {
  const firstChar = text.charAt(0);
  
  if (/\d/.test(firstChar)) return `0_${text}`;           // 数字
  if (/[a-zA-Z]/.test(firstChar)) return `1_${text.toLowerCase()}`; // 英文
  if (/[\u4e00-\u9fff]/.test(firstChar)) {               // 中文
    const pinyinResult = pinyin(firstChar, { toneType: 'none' });
    return `2_${pinyinResult.toLowerCase()}_${text}`;
  }
  return `3_${text}`;                                     // 其他
}

// 排序函数
export function sortFolders(folders: Folder[]): Folder[]
export function sortDocuments(documents: Document[]): Document[]
export function sortFoldersAndDocuments(folders, documents)
export function sortFolderTree(folderTree: Folder[]): Folder[]
```

#### `src/stores/doc.ts`
在 Store 层面集成排序：

```typescript
// 加载文件夹树时排序
async loadFolderTree(parentId: number | null = null) {
  const folders = await useSqlite().listFolders(parentId);
  if (parentId === null) {
    this.folderTree = sortFolderTree(folders);
  } else {
    this.updateFolderInTree(parentId, sortFolderTree(folders));
  }
}

// 添加新文件夹时排序
addFolderToTree(folder: Folder, parentId: number | null) {
  // ... 添加逻辑
  this.folderTree = sortFolderTree(this.folderTree);
}

// 添加新文档时排序
addDocumentToTree(document: Document, folderId: number) {
  // ... 添加逻辑
  const { sortedDocuments } = sortFoldersAndDocuments([], folder.documents);
  folder.documents = sortedDocuments;
}
```

#### `src/components/FolderTree.vue`
在组件层面应用排序：

```typescript
const folders = computed(() => {
  // ... 获取文件夹列表
  
  // 应用排序：确保文档也被排序
  return folderList.map(folder => {
    const sortedFolder = { ...folder };
    if (sortedFolder.documents && sortedFolder.documents.length > 0) {
      const { sortedDocuments } = sortFoldersAndDocuments([], sortedFolder.documents);
      sortedFolder.documents = sortedDocuments;
    }
    return sortedFolder;
  });
});
```

## 性能优化

### 1. 缓存机制
- 排序结果在 Store 中缓存
- 只在数据变更时重新排序
- 避免重复计算

### 2. 增量排序
- 新增项目时只对相关部分重新排序
- 不影响整个文件树的性能

### 3. 拼音转换优化
- 只转换首字符，减少计算量
- 使用高性能的 pinyin-pro 库

## 用户体验

### 1. 一致性
- 所有文件夹和文档都遵循相同的排序规则
- 用户可以快速找到目标文件

### 2. 直观性
- 数字文件在最前面，便于版本管理
- 英文文件按字母顺序，符合国际惯例
- 中文文件按拼音排序，符合中文用户习惯

### 3. 实时性
- 新建文件夹或文档时立即应用排序
- 重命名后自动重新排序

## 扩展性

该排序系统具有良好的扩展性，可以轻松添加：
- 自定义排序规则
- 用户偏好设置
- 多语言支持
- 特殊字符处理规则

## 测试建议

1. 创建包含数字、英文、中文的文件夹和文档
2. 验证排序顺序是否正确
3. 测试新增项目的实时排序
4. 验证重命名后的重新排序
5. 测试大量文件的排序性能
