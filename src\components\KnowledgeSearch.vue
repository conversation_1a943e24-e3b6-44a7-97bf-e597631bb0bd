<template>
  <div class="knowledge-search fit column no-wrap" :class="hasSearched ? 'flex-center' : ''">
    <!-- 顶部搜索区域 -->
    <div class="row items-center full-width" :class="hasSearched ? '' : 'q-space'">
      <div class="q-pa-lg full-width">
        <slot name="top" />
        <!-- 搜索输入框 -->
        <div class="row q-gutter-md items-center">
          <q-input
            v-model="searchQuery"
            :placeholder="$t('src.components.KnowledgeSearch.placeholder')"
            outlined
            class="col"
            @keydown.enter="handleSearchEnter"
            @compositionstart="isComposing = true"
            @compositionend="isComposing = false"
          >
            <template #prepend>
              <q-icon name="search" />
            </template>
            <template #append>
              <q-btn
                color="primary"
                icon="search"
                :label="$t('src.components.KnowledgeSearch.search')"
                @click="performSearch"
                :loading="searching"
                :disable="!searchQuery.trim()"
              />
            </template>
          </q-input>
        </div>

        <!-- 搜索选项 -->
        <div class="row q-gutter-md q-mt-md">
          <q-select
            v-model="selectedKnowledgeBase"
            :options="knowledgeBaseOptions"
            :label="$t('src.components.KnowledgeSearch.scope')"
            outlined
            dense
            style="min-width: 200px"
            clearable
          />

          <q-input
            v-model.number="resultLimit"
            type="number"
            :label="$t('src.components.KnowledgeSearch.result_limit')"
            outlined
            dense
            style="width: 120px"
            :min="1"
            :max="100"
          />

          <q-input
            v-model.number="minScore"
            type="number"
            :label="$t('src.components.KnowledgeSearch.min_score')"
            outlined
            dense
            style="width: 140px"
            :min="0"
            :max="1"
            :step="0.01"
            :hint="$t('src.components.KnowledgeSearch.min_score_hint')"
          />
        </div>

        <slot name="more_actions" />
      </div>
    </div>
    <q-space v-if="!hasSearched" />

    <!-- 搜索结果区域 -->
    <div v-if="searchResults.length > 0" class="col full-width scroll q-pa-lg">
      <!-- 搜索统计 -->
      <div class="q-mb-md">
        <q-chip color="primary" text-color="white" icon="search">
          {{ $t('src.components.KnowledgeSearch.found_results', { count: searchResults.length }) }}
        </q-chip>
        <span class="text-caption text-grey-6 q-ml-sm"
          >{{ $t('src.components.KnowledgeSearch.query') }}: "{{ lastSearchQuery }}"
        </span>
      </div>

      <!-- 无结果状态 -->
      <div v-if="searchResults.length === 0" class="text-center q-pa-xl">
        <q-icon name="mdi-file-search-outline" size="80px" color="grey-5" />
        <div class="text-h6 text-grey-6 q-mt-md">
          {{ $t('src.components.KnowledgeSearch.no_result') }}
        </div>
        <div
          class="text-body2 text-grey-5 q-mb-md"
          v-html="$t('src.components.KnowledgeSearch.no_result_tips')"
        ></div>
        <div class="row justify-center q-gutter-sm">
          <q-btn
            color="primary"
            outline
            size="sm"
            :label="$t('src.components.KnowledgeSearch.retry_lower_threshold')"
            @click="
              minScore = 0.01;
              performSearch();
            "
          />
          <q-btn
            color="secondary"
            outline
            size="sm"
            :label="$t('src.components.KnowledgeSearch.debug_diagnose')"
            @click="$router.push('/debug')"
          />
        </div>
      </div>

      <!-- 搜索结果列表 -->
      <q-list v-else-if="searchResults.length > 0" separator>
        <q-item
          v-for="(result, index) in searchResults"
          :key="result.id"
          class="q-pa-md"
          clickable
          @click="openDocument(result)"
        >
          <q-item-section>
            <q-item-label class="text-weight-bold text-primary">
              {{ result.document_title || $t('src.components.KnowledgeSearch.unnamed_document') }}
            </q-item-label>

            <q-item-label caption class="q-mt-xs">
              <q-chip
                v-if="result.metadata?.knowledge_base_name"
                size="sm"
                color="blue-grey-2"
                text-color="blue-grey-8"
                dense
              >
                {{ result.metadata.knowledge_base_name }}
              </q-chip>
            </q-item-label>

            <q-item-label class="q-mt-sm" lines="4">
              {{ result.memory }}
            </q-item-label>

            <q-item-label caption class="q-mt-sm row items-center q-gutter-sm">
              <q-chip size="sm" :color="getScoreColor(result.score)" text-color="white" dense>
                {{ $t('src.components.KnowledgeSearch.' + getScoreLabel(result.score)) }}:
                {{ (result.score * 100).toFixed(1) }}%
              </q-chip>

              <span class="text-grey-6"> #{{ index + 1 }} </span>
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>

    <!-- 文档查看DOM -->
    <q-card
      v-if="showDocumentViewer"
      class="column absolute-full z-max"
      :class="fullDocumentContent || currentDocument.memory ? '' : 'q-pa-xl'"
    >
      <q-card-section class="row items-center q-py-sm border-bottom">
        <q-btn icon="mdi-arrow-left" flat round dense @click="showDocumentViewer = false" />
        <div class="text-h6 q-pl-md">{{ currentDocument?.document_title || '文档详情' }}</div>
      </q-card-section>

      <q-card-section class="col scroll">
        <div v-if="currentDocument">
          <!-- 文档元信息 -->
          <div v-if="!fullDocumentContent && !currentDocument.memory" class="q-mb-md">
            <q-chip color="blue-grey-2" text-color="blue-grey-8" dense>
              {{ $t('src.components.KnowledgeSearch.knowledge_base') }}:
              {{
                currentDocument.metadata?.knowledge_base_name ||
                $t('src.components.KnowledgeSearch.unknown')
              }}
            </q-chip>
            <q-chip color="green-2" text-color="green-8" dense class="q-ml-sm">
              {{ $t('src.components.KnowledgeSearch.similarity') }}:
              {{ (currentDocument.score * 100).toFixed(1) }}%
            </q-chip>
            <q-chip
              v-if="fullDocumentContent !== currentDocument.memory"
              color="primary"
              text-color="white"
              dense
              class="q-ml-sm"
            >
              {{ $t('src.components.KnowledgeSearch.full_document') }}
            </q-chip>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoadingDocument" class="text-center q-pa-md">
            <q-spinner color="primary" size="2em" />
            <div class="q-mt-sm text-grey-6">
              {{ $t('src.components.KnowledgeSearch.loading_full_document') }}
            </div>
          </div>

          <!-- 文档内容 -->
          <MarkdownRenderer
            v-else
            :content="fullDocumentContent || currentDocument.memory"
            class="q-px-sm"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { useKnowledge } from '../composeables/useKnowledge';
import type { KnowledgeSearchResult } from '../env';
import { useKnowledgeStore } from '../stores/knowledge';
import { storeToRefs } from 'pinia';
import MarkdownRenderer from './MarkdownRenderer.vue';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

// 组合式 API
const $q = useQuasar();
const $router = useRouter();
const knowledge = useKnowledge();
const knowledgeStore = useKnowledgeStore();

// 从store获取知识库列表
const { knowledgeBases } = storeToRefs(knowledgeStore);

// 响应式数据
const searchQuery = ref('');
const lastSearchQuery = ref('');
const searchResults = ref<KnowledgeSearchResult[]>([]);
const searching = ref(false);
const hasSearched = ref(false);
const isComposing = ref(false);
const selectedKnowledgeBase = ref<{ label: string; value: number } | null>(null);
const resultLimit = ref(20);
const minScore = ref(0.05); // 降低默认阈值，从0.1改为0.05

// 文档查看相关
const showDocumentViewer = ref(false);
const currentDocument = ref<KnowledgeSearchResult | null>(null);
const fullDocumentContent = ref<string>('');
const isLoadingDocument = ref(false);

// 计算属性
const knowledgeBaseOptions = computed(() => {
  const options = knowledgeBases.value.map((kb) => ({
    label: kb.name,
    value: kb.id,
  }));

  // 添加"所有知识库"选项
  return [{ label: '所有知识库', value: -1 }, ...options];
});

// 方法
const getScoreColor = (score: number): string => {
  if (score >= 0.8) return 'green';
  if (score >= 0.6) return 'orange';
  if (score >= 0.4) return 'amber';
  if (score >= 0.2) return 'deep-orange';
  return 'red';
};

const getScoreLabel = (score: number): string => {
  if (score >= 0.8) return 'base';
  if (score >= 0.6) return 'better';
  if (score >= 0.4) return 'good';
  if (score >= 0.2) return 'bad';
  return 'weak';
};

const handleSearchEnter = (event: KeyboardEvent) => {
  // 如果正在进行中文输入法组合输入，不触发搜索事件
  if (isComposing.value) {
    return;
  }

  // 阻止默认行为并触发搜索
  event.preventDefault();
  void performSearch();
};

const performSearch = async () => {
  if (!searchQuery.value.trim()) return;

  searching.value = true;
  hasSearched.value = true;
  lastSearchQuery.value = searchQuery.value;

  try {
    console.log('🔍 [KnowledgeSearch] 开始搜索:', {
      query: searchQuery.value,
      knowledgeBase: selectedKnowledgeBase.value,
      limit: resultLimit.value,
      minScore: minScore.value,
    });

    let results: KnowledgeSearchResult[] = [];

    if (!selectedKnowledgeBase.value || selectedKnowledgeBase.value.value === -1) {
      // 搜索所有知识库
      results = await knowledge.searchKnowledge(
        searchQuery.value,
        undefined,
        resultLimit.value,
        minScore.value,
      );
    } else {
      // 搜索指定知识库
      results = await knowledge.searchKnowledge(
        searchQuery.value,
        selectedKnowledgeBase.value.value,
        resultLimit.value,
        minScore.value,
      );
    }

    searchResults.value = results;
    console.log('✅ [KnowledgeSearch] 搜索完成，找到', results.length, '个结果');

    // 显示搜索完成通知
    if (results.length > 0) {
      $q.notify({
        type: 'positive',
        message: $t('src.components.KnowledgeSearch.notify_success', { count: results.length }),
        timeout: 2000,
      });
    }
  } catch (error) {
    console.error('❌ [KnowledgeSearch] 搜索失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.KnowledgeSearch.notify_failed'),
      caption:
        error instanceof Error ? error.message : $t('src.components.KnowledgeSearch.unknown'),
      timeout: 5000,
    });
    searchResults.value = [];
  } finally {
    searching.value = false;
  }
};

const openDocument = async (result: KnowledgeSearchResult) => {
  try {
    currentDocument.value = result;
    isLoadingDocument.value = true;
    fullDocumentContent.value = '';
    showDocumentViewer.value = true;

    // 如果有knowledge_document_id，获取完整的知识库文档内容
    if (result.knowledge_document_id) {
      console.log('🔍 [KnowledgeSearch] 获取完整文档内容:', result.knowledge_document_id);

      const fullDocResult = await knowledge.callKnowledgeApi<{ content: string }>(
        'getDocument',
        result.knowledge_document_id,
      );

      if (fullDocResult && fullDocResult.content) {
        fullDocumentContent.value = fullDocResult.content;
        console.log('✅ [KnowledgeSearch] 获取完整文档内容成功');
      } else {
        console.warn('⚠️ [KnowledgeSearch] 未获取到完整文档内容，使用片段内容');
        fullDocumentContent.value = result.memory;
      }
    } else {
      // 没有knowledge_document_id，使用片段内容
      console.log('⚠️ [KnowledgeSearch] 无knowledge_document_id，使用片段内容');
      fullDocumentContent.value = result.memory;
    }
  } catch (error) {
    console.error('❌ [KnowledgeSearch] 获取完整文档内容失败:', error);
    // 出错时使用片段内容
    fullDocumentContent.value = result.memory;

    $q.notify({
      type: 'warning',
      message: $t('src.components.KnowledgeSearch.notify_doc_failed'),
      caption:
        error instanceof Error ? error.message : $t('src.components.KnowledgeSearch.unknown'),
      timeout: 3000,
    });
  } finally {
    isLoadingDocument.value = false;
  }
};

// 生命周期
onMounted(async () => {
  // 确保知识库列表已加载
  if (knowledgeBases.value.length === 0) {
    try {
      await knowledgeStore.loadKnowledgeBases();
    } catch (error) {
      console.error('❌ [KnowledgeSearch] 加载知识库列表失败:', error);
    }
  }

  // 设置文档向量化完成监听
  const unsubscribe = knowledge.onDocumentVectorized((docId: string, chunkCount: number) => {
    console.log('📡 [KnowledgeSearch] 收到文档向量化完成:', docId, chunkCount);

    if (chunkCount > 0) {
      // 显示成功通知
      $q.notify({
        type: 'positive',
        message: $t('src.components.KnowledgeSearch.notify_success_message'),
        caption: $t('src.components.KnowledgeSearch.notify_success_caption', {
          docId,
          chunkCount,
        }),
        timeout: 3000,
      });
    } else {
      // 处理失败
      $q.notify({
        type: 'negative',
        message: $t('src.components.KnowledgeSearch.notify_failed_message'),
        caption: $t('src.components.KnowledgeSearch.notify_failed_caption', { docId }),
        timeout: 5000,
      });
    }

    // 刷新知识库列表数据
    void knowledgeStore.loadKnowledgeBases();
  });

  // 组件卸载时取消监听
  onUnmounted(() => {
    unsubscribe();
  });
});
</script>

<style scoped>
.knowledge-search {
  max-width: 1200px;
  margin: 0 auto;
}

.hover-item {
  transition: all 0.3s ease;
}

.hover-item:hover {
  background-color: rgba(25, 118, 210, 0.04);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
