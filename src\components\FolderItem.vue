<template>
  <div class="folder-item-container q-space">
    <div
      :ref="setFolderRef"
      :data-folder-id="folder.id"
      class="row no-wrap gap-xs items-center q-space hover-item folder-drag-container"
      :class="[
        uiStore.highlightTreeItem === `folder-${folder?.id}`
          ? uiStore.lowlight
            ? 'lowlight-tree-item'
            : 'highlight-tree-item'
          : 'border-placehoder',
        isDragging ? 'dragging' : '',
        isDragOver ? 'drag-over' : '',
      ]"
      @contextmenu.prevent="showContextMenu"
    >
      <div
        class="row no-wrap items-center q-space cursor-pointer hover-highlight q-px-sm full-height"
      >
        <div
          class="row no-wrap items-center q-space full-height"
          @click="$emit('toggle', folder.id)"
        >
          <template v-if="depth > 0">
            <div
              v-for="i in depth"
              :key="i"
              style="flex: 0 0 16px"
              class="column items-center no-wrap full-height hover-show-item"
            >
              <div class="q-space border-left" style="width: 1px"></div>
            </div>
          </template>
          <div class="row no-wrap items-center" style="flex: 0 0 16px; height: 32px">
            <q-icon
              name="mdi-chevron-right"
              size="xs"
              class="cursor-pointer"
              :class="`${expanded ? 'rotate-90' : ''} ${isCreatingContent ? 'text-primary' : ''}`"
              style="transition: transform 200ms"
              :title="
                isCreatingContent
                  ? $t('src.components.FolderItem.creating_content_cannot_fold')
                  : ''
              "
            />
          </div>
          <div
            v-if="!isRenaming"
            class="column justify-center q-pl-sm q-space text-no-wrap"
            :class="isCreatingContent ? 'text-primary' : ''"
            :title="
              isCreatingContent ? $t('src.components.FolderItem.creating_content_cannot_fold') : ''
            "
          >
            {{ folder.name }}
          </div>
          <!-- 重命名输入框 -->
          <q-input
            v-else
            ref="renameInputRef"
            v-model="renameNameLocal"
            dense
            autofocus
            hide-bottom-space
            input-class="q-px-sm"
            class="q-space overflow-hidden"
            @keydown.enter="handleRenameEnter"
            @keydown.esc="cancelRename"
            @compositionstart="isComposing = true"
            @compositionend="isComposing = false"
          >
            <template #append>
              <div class="flex flex-center q-pr-sm">
                <q-btn
                  v-if="renameNameLocal !== folder.name"
                  icon="mdi-check"
                  dense
                  size="sm"
                  round
                  flat
                  @click="confirmRename"
                />
                <q-btn v-else icon="close" dense size="sm" round flat @click="cancelRename" />
              </div>
            </template>
          </q-input>
        </div>
      </div>

      <!-- 右键菜单 -->
      <q-menu ref="contextMenuRef" class="shadow-24" context-menu>
        <q-list dense>
          <q-item clickable v-close-popup @click="showCreateSubfolder">
            <q-item-section side>
              <q-icon name="mdi-folder-plus" />
            </q-item-section>
            <q-item-section class="text-no-wrap">{{
              $t('src.components.FolderItem.new_subfolder')
            }}</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="showCreateDocument">
            <q-item-section side>
              <q-icon name="mdi-file-plus" />
            </q-item-section>
            <q-item-section class="text-no-wrap">{{
              $t('src.components.FolderItem.new_document')
            }}</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="showRename">
            <q-item-section side>
              <q-icon name="mdi-pencil" />
            </q-item-section>
            <q-item-section class="text-no-wrap">{{
              $t('src.components.FolderItem.rename')
            }}</q-item-section>
          </q-item>
          <q-item
            clickable
            v-close-popup
            @click="pasteDocument"
            :disable="
              !uiStore.hasCopiedDocument() ||
              (uiStore.isCutMode() && uiStore.copiedDocument?.parentId === folder.id)
            "
          >
            <q-item-section side>
              <q-icon name="mdi-content-paste" />
            </q-item-section>
            <q-item-section class="text-no-wrap">{{
              $t('src.components.FolderItem.paste')
            }}</q-item-section>
          </q-item>
          <q-separator class="op-5" />
          <q-item clickable v-close-popup @click="addAllToKnowledgeBase">
            <q-item-section side>
              <q-icon name="mdi-brain" />
            </q-item-section>
            <q-item-section class="text-no-wrap">{{
              $t('src.components.FolderItem.add_all_to_knowledge_base')
            }}</q-item-section>
          </q-item>
          <q-separator class="op-5" />
          <q-item clickable v-close-popup @click="deleteFolder">
            <q-item-section side>
              <q-icon name="mdi-delete" color="negative" />
            </q-item-section>
            <q-item-section class="text-no-wrap">{{
              $t('src.components.FolderItem.delete')
            }}</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </div>

    <!-- 新建子文件夹 -->
    <div
      v-if="createSubfolderVisible"
      class="row no-wrap q-pl-sm q-py-none"
      style="height: fit-content; border-left: 1px solid #00000000"
    >
      <template v-if="depth + 1 > 0">
        <div
          v-for="i in depth + 1"
          :key="i"
          style="flex: 0 0 16px"
          class="column items-center no-wrap hover-show-item"
        >
          <div class="q-space border-left" style="width: 1px"></div>
        </div>
      </template>
      <div class="q-py-xs full-width">
        <CreateFolder
          :parentId="folder.id"
          class="q-space"
          @folderCreated="handleSubfolderCreated"
          @cancel="cancelCreateSubfolder"
        />
      </div>
    </div>

    <!-- 新建文档 -->
    <div
      v-if="createDocumentVisible"
      class="row no-wrap q-pl-sm q-py-none"
      style="height: fit-content; border-left: 1px solid #00000000"
    >
      <template v-if="depth + 1 > 0">
        <div
          v-for="i in depth + 1"
          :key="i"
          style="flex: 0 0 16px"
          class="column items-center no-wrap hover-show-item"
        >
          <div class="q-space border-left" style="width: 1px"></div>
        </div>
      </template>
      <div class="q-py-xs full-width">
        <CreateDocument
          class="q-space"
          :parentId="folder.id"
          @documentCreated="handleDocumentCreated"
          @cancel="cancelCreateDocument"
        />
      </div>
    </div>

    <!-- 批量添加到知识库配置组件 -->
    <q-popup-proxy
      v-if="showBatchConfigCard"
      v-model="showBatchConfigCard"
      class="shadow-24"
      :offset="[-40, -10]"
    >
      <BatchKnowledgeBaseConfigCard
        :bolderless="true"
        style="min-width: 26rem"
        @cancel="showBatchConfigCard = false"
        @start-processing="handleStartBatchProcessing"
      />
    </q-popup-proxy>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import type { Folder, Document } from 'src/types/doc';
import { useQuasar, QInput } from 'quasar';
import { useI18n } from 'vue-i18n';
import CreateFolder from './CreateFolder.vue';
import CreateDocument from './CreateDocument.vue';
import { useUiStore } from 'src/stores/ui';
import { useDocStore } from 'src/stores/doc';

import { useDocumentToKnowledge } from 'src/composeables/useDocumentToKnowledge';
import BatchKnowledgeBaseConfigCard from './BatchKnowledgeBaseConfigCard.vue';
import type { ChunkingMethod, ChunkingConfig } from 'src/types/qwen';
import { useSqlite } from 'src/composeables/useSqlite'; // 修复导入路径

const docStore = useDocStore();
const uiStore = useUiStore();
const documentToKnowledge = useDocumentToKnowledge();
const sqlite = useSqlite(); // 创建实例

const props = defineProps<{
  folder: Folder;
  depth: number;
  expanded: boolean;
  isCreatingContent?: boolean;
  folderIndex?: number;
}>();

const emit = defineEmits<{
  toggle: [id: number];
  'set-folder-ref': [folderId: number, el: unknown];
  'folder-updated': [folder: Folder];
  'folder-deleted': [folderId: number];
  'subfolder-created': [folder: Folder];
  'document-created': [document: Document, parentId: number];
  'creating-content-start': [folderId: number];
  'creating-content-end': [folderId: number];
}>();

const $q = useQuasar();
const { t: $t } = useI18n({ useScope: 'global' });

// 内部状态管理
const isRenaming = ref(false);
const renameNameLocal = ref('');
const createSubfolderVisible = ref(false);
const createDocumentVisible = ref(false);
const showBatchConfigCard = ref(false);
const batchProcessing = ref(false);

const contextMenuRef = ref();
const isComposing = ref(false);
const renameInputRef = ref<InstanceType<typeof QInput> | null>(null);

// 拖拽状态
const isDragging = ref(false);
const isDragOver = ref(false);

// 监听重命名状态变化，自动聚焦输入框
watch(
  () => isRenaming.value,
  async (newValue) => {
    if (newValue) {
      await nextTick();
      if (renameInputRef.value) {
        renameInputRef.value.focus();
      }
    }
  },
);

// 重命名相关方法
const showRename = () => {
  isRenaming.value = true;
  renameNameLocal.value = props.folder.name || '';
};

const cancelRename = () => {
  isRenaming.value = false;
  renameNameLocal.value = '';
};

const confirmRename = async () => {
  if (!renameNameLocal.value || renameNameLocal.value === props.folder.name) {
    cancelRename();
    return;
  }

  try {
    await sqlite.updateFolder(props.folder.id, renameNameLocal.value, props.folder.parent_id);
    const updatedFolder = await sqlite.getFolder(props.folder.id);

    // 通过store更新文件夹信息
    docStore.updateFolderInTreeData(updatedFolder);
    emit('folder-updated', updatedFolder);

    isRenaming.value = false;
  } catch (error) {
    console.error('重命名文件夹失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.FolderItem.rename_folder_failed'),
      position: 'top',
    });
  }
};

const handleRenameEnter = (event: KeyboardEvent) => {
  // 如果正在进行中文输入法组合输入，不触发重命名事件
  if (isComposing.value) {
    return;
  }

  // 阻止默认行为并触发重命名
  event.preventDefault();
  void confirmRename();
};

// 新建相关方法
const showCreateSubfolder = () => {
  createSubfolderVisible.value = true;
  emit('toggle', props.folder.id); // 展开文件夹
  emit('creating-content-start', props.folder.id); // 通知开始创建内容
};

const showCreateDocument = () => {
  createDocumentVisible.value = true;
  emit('toggle', props.folder.id); // 展开文件夹
  emit('creating-content-start', props.folder.id); // 通知开始创建内容
};

const cancelCreateSubfolder = () => {
  createSubfolderVisible.value = false;
  emit('creating-content-end', props.folder.id); // 通知结束创建内容
};

const cancelCreateDocument = () => {
  createDocumentVisible.value = false;
  emit('creating-content-end', props.folder.id); // 通知结束创建内容
};

const handleSubfolderCreated = (newFolder: Folder) => {
  // 通过store管理文件夹创建
  void docStore.addFolderToTree(newFolder, props.folder.id);
  emit('subfolder-created', newFolder);
  createSubfolderVisible.value = false;
  emit('creating-content-end', props.folder.id); // 通知结束创建内容
};

const handleDocumentCreated = (newDoc: Document) => {
  // 通过store管理文档创建
  void docStore.addDocumentToTree(newDoc, props.folder.id);
  emit('document-created', newDoc, props.folder.id);
  createDocumentVisible.value = false;
  emit('creating-content-end', props.folder.id); // 通知结束创建内容
};

// 删除方法
const deleteFolder = () => {
  // 显示确认对话框
  $q.dialog({
    title: $t('src.components.FolderItem.confirm_delete_folder_title'),
    message: $t('src.components.FolderItem.confirm_delete_folder_message', {
      name: props.folder.name,
    }),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    try {
      void sqlite.deleteFolder(props.folder.id);
      docStore.removeFolderFromTree(props.folder.id);
      emit('folder-deleted', props.folder.id);
      $q.notify({
        type: 'positive',
        message: $t('src.components.FolderItem.folder_deleted_success', {
          name: props.folder.name,
        }),
        position: 'top',
      });
    } catch (error) {
      console.error('删除文件夹失败:', error);
      $q.notify({
        type: 'negative',
        message: $t('src.components.FolderItem.folder_deleted_failed', {
          name: props.folder.name,
          error: error instanceof Error ? error.message : String(error),
        }),
        position: 'top',
      });
    }
  });
};

const setFolderRef = (el: unknown) => {
  emit('set-folder-ref', props.folder.id, el);
};

// 获取文件夹的所有父级路径
const getFolderParentPath = (folderId: number): number[] => {
  const path: number[] = [];
  const folder = docStore.folderMap.get(folderId);

  if (folder && folder.parent_id && folder.parent_id !== -1) {
    // 递归获取父级路径
    path.push(...getFolderParentPath(folder.parent_id));
    path.push(folder.parent_id);
  }

  return path;
};

// 确保文件夹及其所有父级都展开的函数
const ensureFolderExpanded = async (folderId: number) => {
  try {
    console.log('📂 [文档] 确保文件夹及其父级展开:', folderId);

    // 获取所有需要展开的父级文件夹路径
    const parentPath = getFolderParentPath(folderId);
    console.log('📂 [文档] 父级路径:', parentPath);

    // 从根级开始，逐级展开所有父级文件夹
    for (const parentId of parentPath) {
      console.log('📂 [文档] 展开父级文件夹:', parentId);
      // 这里我们需要通过事件系统来展开父级文件夹
      // 由于我们在子组件中，需要向上传递展开请求
      emit('toggle', parentId);
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // 最后展开目标文件夹本身
    if (!props.expanded) {
      emit('toggle', folderId);

      // 等待展开动画完成
      await new Promise((resolve) => setTimeout(resolve, 150));
      console.log('✅ [文档] 文件夹已展开:', folderId);
    } else {
      console.log('📂 [文档] 文件夹已经是展开状态:', folderId);
    }
  } catch (error) {
    console.error('❌ [文档] 展开文件夹失败:', error);
  }
};

const pasteDocument = async () => {
  const copiedDoc = uiStore.copiedDocument;
  if (!copiedDoc) {
    $q.notify({
      type: 'warning',
      message: $t('src.components.FolderItem.no_document_to_paste'),
      position: 'top',
    });
    return;
  }

  try {
    if (copiedDoc.mode === 'cut') {
      // 剪切模式：移动文档
      console.log('✂️ [文档] 准备移动文档:', copiedDoc.title);

      // 使用docStore的moveDocumentToFolder方法移动文档
      const success = await docStore.moveDocumentToFolder(copiedDoc.id, props.folder.id);

      if (success) {
        // 确保文件夹展开以显示移动的文档
        await ensureFolderExpanded(props.folder.id);

        $q.notify({
          type: 'positive',
          message: $t('src.components.FolderItem.document_moved_to_folder', {
            title: copiedDoc.title,
          }),
          position: 'top',
        });
        console.log('✅ [文档] 文档移动成功:', copiedDoc.title);

        // 清除剪切状态
        uiStore.clearCopiedDocument();
      } else {
        throw new Error('移动文档失败');
      }
    } else {
      // 复制模式：创建新文档
      console.log('📋 [文档] 准备复制文档:', copiedDoc.title);

      // 检查目标文件夹中是否存在同名文档
      const folder = docStore.folderMap.get(props.folder.id);
      let newTitle = copiedDoc.title;

      if (folder && folder.documents) {
        const existingDoc = folder.documents.find((doc) => doc.title === copiedDoc.title);
        if (existingDoc) {
          // 如果存在同名文档，添加副本后缀
          newTitle = `${copiedDoc.title}(${$t('copiedItem')})`;
          console.log('📋 [文档] 检测到同名文档，重命名为:', newTitle);
        }
      }

      // 解析文档内容
      const content = JSON.parse(copiedDoc.content);
      const metadata = copiedDoc.metadata;

      // 创建新文档
      const docId = await sqlite.createDocument(newTitle, content, props.folder.id, metadata);
      const newDoc = await sqlite.getDocument(docId);

      // 更新store中的文件树
      void docStore.addDocumentToTree(newDoc, props.folder.id);
      emit('document-created', newDoc, props.folder.id);

      // 确保文件夹展开以显示新创建的文档
      await ensureFolderExpanded(props.folder.id);

      $q.notify({
        type: 'positive',
        message: $t('src.components.FolderItem.document_copied_to_folder', {
          title: newTitle,
        }),
        position: 'top',
      });
      console.log('✅ [文档] 文档复制成功:', newTitle);

      // 清除复制状态
      uiStore.clearCopiedDocument();
    }
  } catch (error) {
    console.error('❌ [文档] 粘贴文档失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.FolderItem.paste_document_failed'),
      position: 'top',
    });
  }
};

// 从数据库直接获取文件夹下的所有文档ID（递归）
const allDocumentIds = ref<number[]>();
// 批量添加文件夹下所有文档到知识库
const addAllToKnowledgeBase = async () => {
  try {
    console.log('🧠 [文件夹] 准备批量添加文件夹下所有文档到知识库:', props.folder.id);
    allDocumentIds.value = await sqlite.getAllDocumentsInFolder(props.folder.id);

    if (allDocumentIds.value?.length === 0) {
      $q.notify({
        type: 'warning',
        message: $t('src.components.FolderItem.no_documents_in_folder', {
          name: props.folder.name,
        }),
        position: 'top',
      });
      return;
    }

    console.log('📄 [文件夹] 找到文档数量:', allDocumentIds.value.length);

    // 显示批量配置对话框
    showBatchConfigCard.value = true;
  } catch (error) {
    console.error('❌ [文件夹] 批量添加到知识库失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.FolderItem.batch_add_to_knowledge_base_failed', {
        error: error instanceof Error ? error.message : '未知错误',
      }),
      position: 'top',
    });
  }
};

// 处理批量处理开始事件
const handleStartBatchProcessing = async (config: {
  knowledgeBaseId: number;
  chunkingMethod: ChunkingMethod;
  chunkingConfig: ChunkingConfig;
}) => {
  try {
    console.log('🚀 [文件夹] 开始批量处理:', config);

    batchProcessing.value = true;
    showBatchConfigCard.value = false;

    if (allDocumentIds.value?.length === 0) {
      $q.notify({
        type: 'warning',
        message: `文件夹"${props.folder.name}"中没有文档可以添加到知识库`,
        position: 'top',
      });
      return;
    }

    // 筛选出未添加到目标知识库的文档
    console.log('🔍 [文件夹] 筛选未添加到知识库的文档...');
    const { isDocumentInKnowledgeBase } = sqlite;
    const filteredDocumentIds: number[] = [];
    const alreadyInKBDocuments: number[] = [];

    for (const docId of allDocumentIds.value) {
      try {
        const isInKB = await isDocumentInKnowledgeBase(docId, config.knowledgeBaseId);
        if (isInKB) {
          alreadyInKBDocuments.push(docId);
        } else {
          filteredDocumentIds.push(docId);
        }
      } catch (error) {
        console.error(`❌ [文件夹] 检查文档 ${docId} 是否在知识库中失败:`, error);
        // 如果检查失败，默认认为不在知识库中，继续处理
        filteredDocumentIds.push(docId);
      }
    }

    console.log('📊 [文件夹] 文档筛选结果:', {
      总文档数: allDocumentIds.value.length,
      已在知识库中: alreadyInKBDocuments.length,
      需要处理: filteredDocumentIds.length,
      已在知识库的文档ID: alreadyInKBDocuments,
    });

    // 如果有文档已经在知识库中，显示提示
    if (alreadyInKBDocuments.length > 0) {
      $q.notify({
        type: 'info',
        message: `跳过了 ${alreadyInKBDocuments.length} 个已在知识库中的文档`,
        position: 'top',
        timeout: 3000,
      });
    }

    // 如果没有需要处理的文档，提示用户
    if (filteredDocumentIds.length === 0) {
      $q.notify({
        type: 'warning',
        message: $t('src.components.FolderItem.all_documents_already_in_knowledge_base', {
          name: props.folder.name,
        }),
        position: 'top',
      });
      return;
    }

    // 使用筛选后的文档ID进行批量处理
    const batchResult = await documentToKnowledge.processBatchDocuments(
      filteredDocumentIds,
      config,
      {
        onProgress: (current, total, currentTitle) => {
          console.log(`📊 [文件夹] 处理进度: ${current}/${total} - ${currentTitle}`);
        },
        onDocumentCompleted: (result) => {
          console.log(`✅ [文件夹] 文档处理完成:`, result);
        },
        onChunkingProgress: (docId, progress) => {
          console.log(
            `📊 [文件夹] 文档 ${docId} 切割进度: ${progress.stage} (${progress.percentage}%)`,
          );
        },
        onChunkingCompleted: (docId, result) => {
          console.log(`✅ [文件夹] 文档 ${docId} 切割完成，共 ${result.chunkCount} 个块`);
        },
        onVectorizationCompleted: (docId, chunkCount) => {
          console.log(`🎯 [文件夹] 文档 ${docId} 向量化完成，处理了 ${chunkCount} 个块`);
        },
        onError: (docId, error, stage) => {
          console.error(`❌ [文件夹] 文档 ${docId} ${stage}失败:`, error);
        },
      },
    );

    // 显示批量处理完成的总结
    const totalOriginal = allDocumentIds.value.length;
    const skipped = alreadyInKBDocuments.length;
    const processed = batchResult.processedCount;
    const failed = batchResult.errorCount;

    console.log('🎉 [文件夹] 批量处理完成:', {
      原始文档总数: totalOriginal,
      跳过已存在: skipped,
      成功处理: processed,
      处理失败: failed,
    });

    // 显示完成通知
    if (processed > 0) {
      $q.notify({
        type: 'positive',
        message: $t('src.components.FolderItem.batch_processing_completed', {
          processed,
          skipped: skipped > 0 ? skipped : undefined,
          failed: failed > 0 ? failed : undefined,
        }),
        position: 'top',
        timeout: 5000,
      });
    }
  } catch (error) {
    console.error('❌ [文件夹] 批量处理失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.FolderItem.batch_add_to_knowledge_base_failed', {
        error: error instanceof Error ? error.message : '未知错误',
      }),
      position: 'top',
    });
  } finally {
    batchProcessing.value = false;
  }
};

const showContextMenu = (event: MouseEvent) => {
  if (contextMenuRef.value) {
    contextMenuRef.value.show(event);
  }
};
</script>

<style scoped>
.folder-item-container {
  position: relative;
}

.folder-drag-container {
  transition: all 0.2s ease;
}

.folder-drag-container.dragging {
  opacity: 0.5;
  transform: scale(0.95);
  cursor: grabbing;
}

.folder-drag-container.drag-over {
  background-color: rgba(25, 118, 210, 0.1);
  border: 2px dashed rgba(210, 25, 139, 0.3);
}

.folder-drag-container.drag-over-top {
  border-top: 3px solid #1976d2;
}

.folder-drag-container.drag-over-bottom {
  border-bottom: 3px solid #1976d2;
}

.folder-drag-container.drag-over-inside {
  background-color: rgba(25, 118, 210, 0.15);
  border: 2px dashed rgba(210, 25, 133, 0.5);
}

:deep(.body--dark) .folder-drag-container.drag-over {
  background-color: rgba(144, 202, 249, 0.15);
  border-color: rgba(2, 226, 69, 0.943);
}

:deep(.body--dark) .folder-drag-container.drag-over-top,
:deep(.body--dark) .folder-drag-container.drag-over-bottom {
  border-color: #90caf9;
}
</style>
