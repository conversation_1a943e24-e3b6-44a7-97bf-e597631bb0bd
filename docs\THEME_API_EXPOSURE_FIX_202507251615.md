# Qt主题API暴露修复

**创建时间**: 2025-07-25 16:15  
**问题**: `setThemeDirectly` 方法没有正确暴露给前端JavaScript

## 问题分析

### 根本原因
前端通过 `window.qtWindow` 访问的是 `WindowApi` 对象，而不是 `MainWindow` 对象。我们在 `MainWindow` 中定义了 `setThemeDirectly` 方法，但没有在 `WindowApi` 中暴露它。

### WebChannel架构
```
前端 JavaScript
    ↓
window.qtWindow (WindowApi对象)
    ↓
WindowApi::setThemeDirectly() 
    ↓
MainWindow::setThemeDirectly()
```

### 问题表现
- 前端控制台显示 `window.qtWindow` 对象中没有 `setThemeDirectly` 方法
- 主题切换时只有Web部分更新，Qt端UI没有变化
- 控制台报错：`setThemeDirectly is not a function`

## 修复方案

### 1. WindowApi头文件添加方法声明

**qt-src/windowapi.h**:
```cpp
// 主题设置相关的JavaScript可调用方法
Q_INVOKABLE bool hasThemeSetting();
Q_INVOKABLE bool getSavedTheme();
Q_INVOKABLE void saveThemeSetting(bool isDark);
Q_INVOKABLE void setThemeFromVue(bool isDark);
Q_INVOKABLE void onThemeChangedFromFrontend();
Q_INVOKABLE void setThemeDirectly(bool isDark);  // 新增
```

### 2. WindowApi实现文件添加方法实现

**qt-src/windowapi.cpp**:
```cpp
void WindowApi::setThemeFromVue(bool isDark)
{
    if (m_mainWindow)
        m_mainWindow->setThemeFromVue(isDark);
}

void WindowApi::onThemeChangedFromFrontend()
{
    if (m_mainWindow)
        m_mainWindow->onThemeChangedFromFrontend();
}

void WindowApi::setThemeDirectly(bool isDark)  // 新增实现
{
    if (m_mainWindow)
        m_mainWindow->setThemeDirectly(isDark);
}
```

### 3. WebChannel注册机制

Qt端通过以下方式将API暴露给前端：

**mainwindow.cpp setupWebViewCommands()**:
```cpp
void MainWindow::setupWebViewCommands()
{
    // 初始化 QWebChannel
    m_webChannel = new QWebChannel(this);
    m_webChannel->registerObject(QStringLiteral("qtWindow"), m_windowApi);  // 注册WindowApi
    m_webChannel->registerObject(QStringLiteral("databaseApi"), m_databaseApi);
    m_webChannel->registerObject(QStringLiteral("knowledgeApi"), m_knowledgeApi);
    m_webView->page()->setWebChannel(m_webChannel);
}
```

## 修复效果

### 修复前
```javascript
console.log(window.qtWindow.setThemeDirectly); // undefined
```

### 修复后
```javascript
console.log(window.qtWindow.setThemeDirectly); // function setThemeDirectly(isDark) { ... }
```

### 完整的调用链路
1. **前端调用**: `window.qtWindow.setThemeDirectly(true)`
2. **WindowApi转发**: `WindowApi::setThemeDirectly(bool isDark)`
3. **MainWindow执行**: `MainWindow::setThemeDirectly(bool isDark)`
4. **UI更新**: `updateTheme()` 立即更新Qt端界面

## 架构说明

### Qt WebChannel架构
```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   前端 JS       │    │  WindowApi   │    │   MainWindow    │
│                 │    │              │    │                 │
│ window.qtWindow │◄──►│ Q_INVOKABLE  │◄──►│ 实际业务逻辑    │
│                 │    │ 方法暴露     │    │                 │
└─────────────────┘    └──────────────┘    └─────────────────┘
```

### API暴露模式
所有需要前端调用的Qt方法都必须：
1. 在对应的API类（如WindowApi）中声明为 `Q_INVOKABLE`
2. 在API类中实现，通常是转发给MainWindow
3. 通过QWebChannel注册到前端

## 相关文件

- `qt-src/windowapi.h` - WindowApi类声明
- `qt-src/windowapi.cpp` - WindowApi类实现  
- `qt-src/mainwindow.cpp` - MainWindow业务逻辑和WebChannel注册
- `src/env.d.ts` - 前端TypeScript类型定义

## 测试验证

1. **API可用性测试**：
   ```javascript
   console.log(typeof window.qtWindow.setThemeDirectly); // "function"
   ```

2. **功能测试**：
   - 在设置页面切换主题
   - 观察Qt端工具栏是否立即更新颜色

3. **调试日志**：
   ```
   [THEME] 前端直接设置Qt端主题: 暗色
   [THEME] Qt端主题已直接更新完成
   ```

## 经验总结

1. **API暴露规则**：所有前端需要调用的Qt方法都必须通过对应的API类暴露
2. **架构一致性**：保持WindowApi作为前端和MainWindow之间的桥梁
3. **类型安全**：确保前端TypeScript定义与Qt端API保持同步
4. **调试方法**：通过控制台检查API对象的方法可用性

这次修复确保了前端能够直接调用Qt端的主题切换方法，实现了真正的即时主题同步。
