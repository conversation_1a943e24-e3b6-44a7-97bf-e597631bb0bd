/**
 * 文档切割 Web Worker
 * 在后台线程中执行文档切割任务，避免阻塞主线程
 */

import {
  splitMarkdownWithLangChain,
  splitTextWithRecursiveCharacter,
  splitLatexWithLangChain,
  splitDocumentSmart,
  type ChunkingConfig,
  type ChunkingResult,
} from '../utils/knowledgeBase';

// Worker 消息类型定义
export interface ChunkingWorkerMessage {
  id: string;
  type: 'chunk';
  strategy: 'markdown' | 'recursiveCharacter' | 'latex' | 'smart';
  content: string;
  config?: ChunkingConfig;
  enableLogging?: boolean;
}

export interface ChunkingWorkerResponse {
  id: string;
  type: 'success' | 'error' | 'progress';
  result?: ChunkingResult;
  error?: string;
  progress?: {
    stage: string;
    percentage: number;
  };
}

/**
 * 处理文档切割任务
 */
async function handleChunkingTask(message: ChunkingWorkerMessage): Promise<void> {
  const { id, strategy, content, config, enableLogging } = message;

  try {
    // 发送开始进度
    postMessage({
      id,
      type: 'progress',
      progress: {
        stage: '开始切割',
        percentage: 0,
      },
    } as ChunkingWorkerResponse);

    // 发送执行进度
    postMessage({
      id,
      type: 'progress',
      progress: {
        stage: '执行切割',
        percentage: 50,
      },
    } as ChunkingWorkerResponse);

    // 根据策略执行切割
    let result: ChunkingResult;
    switch (strategy) {
      case 'markdown':
        result = await splitMarkdownWithLangChain(content, config, enableLogging);
        break;
      case 'recursiveCharacter':
        result = await splitTextWithRecursiveCharacter(content, config, enableLogging);
        break;
      case 'latex':
        result = await splitLatexWithLangChain(content, config, enableLogging);
        break;
      case 'smart':
        result = await splitDocumentSmart(content, config, 'auto', enableLogging);
        break;
      default:
        throw new Error(`不支持的切割策略: ${strategy as string}`);
    }

    // 发送成功结果
    postMessage({
      id,
      type: 'success',
      result,
    } as ChunkingWorkerResponse);
  } catch (error) {
    console.error('文档切割失败:', error);
    postMessage({
      id,
      type: 'error',
      error: error instanceof Error ? error.message : '未知错误',
    } as ChunkingWorkerResponse);
  }
}

// 监听主线程消息
self.addEventListener('message', (event: MessageEvent<ChunkingWorkerMessage>) => {
  const message = event.data;

  if (message.type === 'chunk') {
    void handleChunkingTask(message);
  } else {
    console.warn('🤷 [ChunkingWorker] 未知消息类型:', message.type);
  }
});
