import type { Editor } from '@tiptap/vue-3';
import { ref, computed } from 'vue';

/**
 * 编辑器基础命令模块
 * 负责处理文本格式化、基础编辑操作等核心编辑器功能
 */

// 当前编辑器实例
export const currentEditor = ref<Editor | null>(null);

// 颜色选择器配置
export const colors = [
  'rgb(255, 0, 0)',     // 红色
  'rgb(255, 125, 0)',   // 橙色
  'rgb(255, 255, 0)',   // 黄色
  'rgb(0, 255, 0)',     // 绿色
  'rgb(0, 0, 255)',     // 蓝色
  'rgb(75, 0, 130)',    // 靛色
  'rgb(148, 0, 211)',   // 紫色
  'rgb(0, 0, 0)',       // 黑色
  'rgb(128, 128, 128)', // 灰色
];

/**
 * 设置当前编辑器实例
 */
export const setCurrentEditor = (editor: Editor | null) => {
  currentEditor.value = editor;
};

/**
 * 获取当前编辑器实例
 */
export const getCurrentEditor = () => currentEditor.value;

/**
 * 检查编辑器是否可用
 */
export const isEditorReady = computed(() => {
  return currentEditor.value && !currentEditor.value.isDestroyed;
});

/**
 * 颜色选择处理
 */
export const onColorSelect = (color: string) => {
  if (!isEditorReady.value) return;
  
  currentEditor.value?.chain().focus().setColor(color).run();
};

/**
 * 检查颜色是否激活
 */
export const isColorActive = (color: string): boolean => {
  if (!isEditorReady.value) return false;
  
  return currentEditor.value?.isActive('textStyle', { color }) ?? false;
};

/**
 * 基础格式化命令
 */
export const editorCommands = {
  // 文本格式化
  bold: () => currentEditor.value?.chain().focus().toggleBold().run(),
  italic: () => currentEditor.value?.chain().focus().toggleItalic().run(),
  underline: () => currentEditor.value?.chain().focus().toggleUnderline().run(),
  strike: () => currentEditor.value?.chain().focus().toggleStrike().run(),
  code: () => currentEditor.value?.chain().focus().toggleCode().run(),
  
  // 标题
  heading1: () => currentEditor.value?.chain().focus().toggleHeading({ level: 1 }).run(),
  heading2: () => currentEditor.value?.chain().focus().toggleHeading({ level: 2 }).run(),
  heading3: () => currentEditor.value?.chain().focus().toggleHeading({ level: 3 }).run(),
  heading4: () => currentEditor.value?.chain().focus().toggleHeading({ level: 4 }).run(),
  heading5: () => currentEditor.value?.chain().focus().toggleHeading({ level: 5 }).run(),
  heading6: () => currentEditor.value?.chain().focus().toggleHeading({ level: 6 }).run(),
  
  // 段落
  paragraph: () => currentEditor.value?.chain().focus().setParagraph().run(),
  
  // 列表
  bulletList: () => currentEditor.value?.chain().focus().toggleBulletList().run(),
  orderedList: () => currentEditor.value?.chain().focus().toggleOrderedList().run(),
  taskList: () => currentEditor.value?.chain().focus().toggleTaskList().run(),
  
  // 引用和代码块
  blockquote: () => currentEditor.value?.chain().focus().toggleBlockquote().run(),
  codeBlock: () => currentEditor.value?.chain().focus().toggleCodeBlock().run(),
  
  // 对齐
  alignLeft: () => currentEditor.value?.chain().focus().setTextAlign('left').run(),
  alignCenter: () => currentEditor.value?.chain().focus().setTextAlign('center').run(),
  alignRight: () => currentEditor.value?.chain().focus().setTextAlign('right').run(),
  alignJustify: () => currentEditor.value?.chain().focus().setTextAlign('justify').run(),
  
  // 缩进 - 需要安装相应的扩展
  // indent: () => currentEditor.value?.chain().focus().indent().run(),
  // outdent: () => currentEditor.value?.chain().focus().outdent().run(),
  
  // 分割线 - 需要HorizontalRule扩展
  // horizontalRule: () => currentEditor.value?.chain().focus().setHorizontalRule().run(),
  
  // 换行 - 需要HardBreak扩展
  // hardBreak: () => currentEditor.value?.chain().focus().setHardBreak().run(),
  
  // 撤销和重做
  undo: () => currentEditor.value?.chain().focus().undo().run(),
  redo: () => currentEditor.value?.chain().focus().redo().run(),
  
  // 选择和删除
  selectAll: () => currentEditor.value?.chain().focus().selectAll().run(),
  deleteSelection: () => currentEditor.value?.chain().focus().deleteSelection().run(),
  
  // 清除格式
  clearFormat: () => currentEditor.value?.chain().focus().clearNodes().unsetAllMarks().run(),
  
  // 表格操作 - 需要Table扩展
  /*
  insertTable: () => currentEditor.value?.chain().focus().insertTable({ 
    rows: 3, 
    cols: 3, 
    withHeaderRow: true 
  }).run(),
  deleteTable: () => currentEditor.value?.chain().focus().deleteTable().run(),
  addColumnBefore: () => currentEditor.value?.chain().focus().addColumnBefore().run(),
  addColumnAfter: () => currentEditor.value?.chain().focus().addColumnAfter().run(),
  deleteColumn: () => currentEditor.value?.chain().focus().deleteColumn().run(),
  addRowBefore: () => currentEditor.value?.chain().focus().addRowBefore().run(),
  addRowAfter: () => currentEditor.value?.chain().focus().addRowAfter().run(),
  deleteRow: () => currentEditor.value?.chain().focus().deleteRow().run(),
  mergeCells: () => currentEditor.value?.chain().focus().mergeCells().run(),
  splitCell: () => currentEditor.value?.chain().focus().splitCell().run(),
  toggleHeaderColumn: () => currentEditor.value?.chain().focus().toggleHeaderColumn().run(),
  toggleHeaderRow: () => currentEditor.value?.chain().focus().toggleHeaderRow().run(),
  toggleHeaderCell: () => currentEditor.value?.chain().focus().toggleHeaderCell().run(),
  */
};

/**
 * 检查命令是否可用
 */
export const canExecuteCommand = (command: string): boolean => {
  if (!isEditorReady.value) return false;
  
  // 检查命令是否在editorCommands中定义
  if (!(command in editorCommands)) return false;
  
  switch (command) {
    case 'undo':
      return currentEditor.value?.can().undo() ?? false;
    case 'redo':
      return currentEditor.value?.can().redo() ?? false;
    // 表格相关命令暂时不可用
    case 'deleteTable':
    case 'addColumnBefore':
    case 'addColumnAfter':
    case 'deleteColumn':
    case 'addRowBefore':
    case 'addRowAfter':
    case 'deleteRow':
    case 'mergeCells':
    case 'splitCell':
    case 'toggleHeaderColumn':
    case 'toggleHeaderRow':
    case 'toggleHeaderCell':
      return false; // 表格扩展未安装
    default:
      return true;
  }
};

/**
 * 检查格式是否激活
 */
export const isFormatActive = (format: string, options?: Record<string, unknown>): boolean => {
  if (!isEditorReady.value) return false;
  
  return currentEditor.value?.isActive(format, options) ?? false;
};

/**
 * 获取当前选中文本
 */
export const getSelectedText = (): string => {
  if (!isEditorReady.value) return '';
  
  const { from, to } = currentEditor.value.state.selection;
  return currentEditor.value.state.doc.textBetween(from, to);
};

/**
 * 插入文本
 */
export const insertText = (text: string) => {
  if (!isEditorReady.value) return;
  
  currentEditor.value?.chain().focus().insertContent(text).run();
};

/**
 * 插入 HTML 内容
 */
export const insertHTML = (html: string) => {
  if (!isEditorReady.value) return;
  
  currentEditor.value?.chain().focus().insertContent(html).run();
};

/**
 * 获取编辑器内容（HTML）
 */
export const getEditorHTML = (): string => {
  if (!isEditorReady.value) return '';
  
  return currentEditor.value?.getHTML() ?? '';
};

/**
 * 获取编辑器内容（纯文本）
 */
export const getEditorText = (): string => {
  if (!isEditorReady.value) return '';
  
  return currentEditor.value?.getText() ?? '';
};

/**
 * 设置编辑器内容
 */
export const setEditorContent = (content: string) => {
  if (!isEditorReady.value) return;
  
  currentEditor.value?.commands.setContent(content);
};

/**
 * 聚焦编辑器
 */
export const focusEditor = () => {
  if (!isEditorReady.value) return;
  
  currentEditor.value?.chain().focus().run();
};

/**
 * 字数统计
 */
export const wordCount = computed(() => {
  if (!isEditorReady.value) return 0;

  const content = getEditorText();
  // 统计中文字符数
  const chineseChars = content.match(/[\u4e00-\u9fa5，。！？；：""''（）【】《》、]/g) || [];
  // 统计英文单词数
  const englishWords = content
    .split(/\s+/)
    .filter((word: string) => /[a-zA-Z]/.test(word)).length;

  // 返回总数：英文单词数 + 中文字符数
  return englishWords + chineseChars.length;
});

/**
 * 执行编辑器命令
 */
export const executeCommand = (command: string) => {
  if (!canExecuteCommand(command)) return;
  
  // 检查命令是否存在于 editorCommands 中
  const commandFn = editorCommands[command as keyof typeof editorCommands];
  if (typeof commandFn === 'function') {
    commandFn();
  }
};

/**
 * 高级文本操作
 */
export const advancedTextOperations = {
  /**
   * 转换为大写
   */
  toUpperCase: () => {
    const selectedText = getSelectedText();
    if (selectedText) {
      currentEditor.value?.chain().focus().deleteSelection().insertContent(selectedText.toUpperCase()).run();
    }
  },
  
  /**
   * 转换为小写
   */
  toLowerCase: () => {
    const selectedText = getSelectedText();
    if (selectedText) {
      currentEditor.value?.chain().focus().deleteSelection().insertContent(selectedText.toLowerCase()).run();
    }
  },
  
  /**
   * 首字母大写
   */
  toCapitalize: () => {
    const selectedText = getSelectedText();
    if (selectedText) {
      const capitalized = selectedText.replace(/\b\w/g, l => l.toUpperCase());
      currentEditor.value?.chain().focus().deleteSelection().insertContent(capitalized).run();
    }
  },
};

// 目录状态管理
export const catalogVisible = ref<Map<string, boolean>>(new Map());

/**
 * 初始化目录状态
 */
export const initCatalogState = (instanceKey: string) => {
  if (!catalogVisible.value.has(instanceKey)) {
    catalogVisible.value.set(instanceKey, false);
  }
};

/**
 * 获取目录状态
 */
export const getCatalogState = (instanceKey: string) => {
  return catalogVisible.value.get(instanceKey) ?? false;
};

/**
 * 切换目录状态
 */
export const toggleCatalog = (instanceKey: string) => {
  const currentState = catalogVisible.value.get(instanceKey) ?? false;
  catalogVisible.value.set(instanceKey, !currentState);
};

export default {
  currentEditor,
  setCurrentEditor,
  getCurrentEditor,
  isEditorReady,
  editorCommands,
  canExecuteCommand,
  isFormatActive,
  getSelectedText,
  insertText,
  insertHTML,
  getEditorHTML,
  getEditorText,
  setEditorContent,
  focusEditor,
  wordCount,
  executeCommand,
  advancedTextOperations,
  onColorSelect,
  isColorActive,
  colors,
  catalogVisible,
  initCatalogState,
  getCatalogState,
  toggleCatalog,
};