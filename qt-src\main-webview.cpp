#include <QApplication>
#include <QWebView>  // 使用WebView替代WebEngine
#include <QStandardPaths>
#include "mainwindow.h"
#include <QCoreApplication>
#include <QIcon>
#include <QLoggingCategory>

// 禁用Qt日志输出以减少噪音
void messageOutput(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    // 只显示警告和错误
    if (type == QtWarningMsg || type == QtCriticalMsg || type == QtFatalMsg) {
        QTextStream(stderr) << msg << Qt::endl;
    }
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    QCoreApplication::setApplicationName("InkCop");
    QCoreApplication::setApplicationVersion("1.0.0");
    QCoreApplication::setOrganizationName("InkCop");
    QCoreApplication::setOrganizationDomain("inkcop.com");
    
    // 设置自定义消息处理器
    qInstallMessageHandler(messageOutput);
    
    // 设置应用程序图标
    QString iconPath = ":/icons/app-icon.png";
    if (QFile::exists(iconPath)) {
        app.setWindowIcon(QIcon(iconPath));
    }
    
    // 注意：WebView版本不需要WebEngine的初始化
    // QtWebEngine::initialize(); // 移除这行
    
    // 创建主窗口
    MainWindow window;
    window.show();
    
    return app.exec();
}
