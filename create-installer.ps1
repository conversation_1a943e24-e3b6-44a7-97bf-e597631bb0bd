# InkCop Multi-Package Builder
# Creates multiple package formats: EXE, MSIX, ZIP

param(
    [string]$Version = "1.0.0",
    [switch]$SkipBuild = $false,
    [switch]$SkipPriGeneration = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "======================================" -ForegroundColor Cyan
Write-Host "InkCop Multi-Package Builder" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "Version: $Version" -ForegroundColor Blue
Write-Host "Skip Build: $SkipBuild" -ForegroundColor Blue
Write-Host "Skip PRI Generation: $SkipPriGeneration" -ForegroundColor Blue

# 检查发行版构建状态
function Get-ReleaseBuildsStatus {
    $installerExePath = "build-installer\bin\Release\InkCop.exe"
    
    if (Test-Path $installerExePath) {
        $fileInfo = Get-Item $installerExePath
        return @{
            Exists = $true;
            Path = $installerExePath;
            Directory = "build-installer\bin\Release";
            Size = [math]::Round($fileInfo.Length / 1MB, 2);
            LastModified = $fileInfo.LastWriteTime
        }
    } else {
        return @{
            Exists = $false;
            Path = $installerExePath;
            Directory = "build-installer\bin\Release"
        }
    }
}

# 交互式包类型选择器
function Show-PackageTypeSelector {
    $packageTypes = @(
        @{ Name = "EXE Installer"; Type = "exe"; Description = "Inno Setup installer package"; Selected = $true },
        @{ Name = "MSIX Package"; Type = "msix"; Description = "Windows Store format package"; Selected = $false },
        @{ Name = "ZIP Portable"; Type = "zip"; Description = "Portable zip package"; Selected = $false }
    )
    
    $currentIndex = 0
    $allSelected = $false
    
    function Show-Menu {
        Clear-Host
        Write-Host "======================================" -ForegroundColor Cyan
        Write-Host "Select Package Types to Build" -ForegroundColor Cyan
        Write-Host "======================================" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Use Up/Down arrows to navigate, SPACE to toggle, 'A' for all, ENTER to confirm" -ForegroundColor Yellow
        Write-Host ""
        
        for ($i = 0; $i -lt $packageTypes.Count; $i++) {
            $pkg = $packageTypes[$i]
            $prefix = if ($i -eq $currentIndex) { ">" } else { " " }
            
            if ($pkg.Selected) {
                $checkbox = "*"
            } else {
                $checkbox = " "
            }
            
            $color = if ($i -eq $currentIndex) { "White" } else { "Gray" }
            
            Write-Host "$prefix [$checkbox] $($pkg.Name)" -ForegroundColor $color
            Write-Host "    $($pkg.Description)" -ForegroundColor DarkGray
            Write-Host ""
        }
        
        Write-Host "Selected packages: " -NoNewline -ForegroundColor Green
        $selected = $packageTypes | Where-Object { $_.Selected }
        if ($selected.Count -gt 0) {
            Write-Host ($selected.Name -join ", ") -ForegroundColor Cyan
        } else {
            Write-Host "None" -ForegroundColor Red
        }
    }
    
    do {
        Show-Menu
        $key = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
        switch ($key.VirtualKeyCode) {
            38 { # Up arrow
                $currentIndex = if ($currentIndex -gt 0) { $currentIndex - 1 } else { $packageTypes.Count - 1 }
            }
            40 { # Down arrow
                $currentIndex = if ($currentIndex -lt $packageTypes.Count - 1) { $currentIndex + 1 } else { 0 }
            }
            32 { # Space
                $packageTypes[$currentIndex].Selected = -not $packageTypes[$currentIndex].Selected
            }
            65 { # 'A' key
                $allSelected = -not $allSelected
                for ($i = 0; $i -lt $packageTypes.Count; $i++) {
                    $packageTypes[$i].Selected = $allSelected
                }
            }
            97 { # 'a' key (lowercase)
                $allSelected = -not $allSelected
                for ($i = 0; $i -lt $packageTypes.Count; $i++) {
                    $packageTypes[$i].Selected = $allSelected
                }
            }
            13 { # Enter
                $selectedTypes = $packageTypes | Where-Object { $_.Selected }
                if ($selectedTypes.Count -eq 0) {
                    Write-Host ""
                    Write-Host "Please select at least one package type!" -ForegroundColor Red
                    Start-Sleep -Seconds 2
                } else {
                    return $selectedTypes.Type
                }
            }
            27 { # Escape
                Write-Host ""
                Write-Host "Operation cancelled by user" -ForegroundColor Yellow
                return $null
            }
        }
    } while ($true)
}

# 构建应用程序
function Invoke-ApplicationBuild {
    if ($SkipBuild) {
        Write-Host "Skipping application build..." -ForegroundColor Yellow
        return $true
    }

    Write-Host ""
    Write-Host "======================================" -ForegroundColor Green
    Write-Host "Building Release Application" -ForegroundColor Green
    Write-Host "======================================" -ForegroundColor Green

    # Step 0: 设置发行版环境
    Write-Host "Step 0: Setting up release environment..." -ForegroundColor Magenta
    if (Test-Path ".env.release") {
        Copy-Item ".env.release" ".env" -Force
        Write-Host "Release .env configuration applied!" -ForegroundColor Green
    } else {
        Write-Host "Warning: .env.release not found, using existing .env" -ForegroundColor Yellow
    }

    # Step 1: 构建前端
    Write-Host "Step 1: Building frontend..." -ForegroundColor Magenta
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    & bun install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Frontend dependency installation failed!" -ForegroundColor Red
        return $false
    }

    Write-Host "Building frontend..." -ForegroundColor Yellow
    & bun run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Frontend build failed!" -ForegroundColor Red
        return $false
    }
    Write-Host "Frontend build completed!" -ForegroundColor Green

    # Step 2: 设置Qt环境
    Write-Host "Step 2: Setting up Qt 6.9.1 MSVC environment..." -ForegroundColor Magenta
    $qtPath = "C:\Qt\6.9.1\msvc2022_64"
    $qwkPath = "third-party/build-qwindowkit/install"

    if (-not (Test-Path $qtPath)) {
        Write-Host "Error: Qt installation not found at: $qtPath" -ForegroundColor Red
        return $false
    }

    # Include QWindowKit in CMAKE_PREFIX_PATH
    $combinedPrefixPath = "$qwkPath;$qtPath"
    $env:CMAKE_PREFIX_PATH = $combinedPrefixPath
    $env:Qt6_DIR = "$qtPath\lib\cmake\Qt6"
    $env:PATH = "$qtPath\bin;$env:PATH"

    Write-Host "Qt environment configured!" -ForegroundColor Green

    # Step 3: 加载Visual Studio环境
    Write-Host "Loading Visual Studio environment..." -ForegroundColor Yellow
    $vsPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
    if (-not (Test-Path $vsPath)) {
        $vsPath = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat"
    }

    if (Test-Path $vsPath) {
        cmd /c "`"$vsPath`" && set" | ForEach-Object {
            if ($_ -match "^([^=]+)=(.*)$") {
                [System.Environment]::SetEnvironmentVariable($matches[1], $matches[2])
            }
        }
        Write-Host "Visual Studio environment loaded!" -ForegroundColor Green
    } else {
        Write-Host "Warning: Visual Studio not found, using system compiler" -ForegroundColor Yellow
    }

    # Step 4: 准备构建目录
    Write-Host "Step 3: Preparing build directory..." -ForegroundColor Magenta
    if (Test-Path "build-installer") {
        Remove-Item "build-installer" -Recurse -Force
    }
    New-Item -ItemType Directory -Path "build-installer" | Out-Null
    Set-Location "build-installer"

    # Step 5: 构建Qt应用程序
    Write-Host "Step 4: Building Qt application..." -ForegroundColor Magenta
    Write-Host "Configuring CMake..." -ForegroundColor Yellow
    $cmakeArgs = @(
        "-G", "Visual Studio 17 2022",
        "-A", "x64",
        "-DCMAKE_BUILD_TYPE=Release",
        "-DCMAKE_PREFIX_PATH=$env:CMAKE_PREFIX_PATH",
        "-DQt6_DIR=$env:Qt6_DIR",
        "-DENABLE_LOCAL_GGUF=ON",
        "-DCMAKE_CXX_FLAGS=/Zm300 /bigobj",
        ".."
    )

    & cmake @cmakeArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "CMake configuration failed!" -ForegroundColor Red
        Set-Location ..
        return $false
    }

    Write-Host "Building project..." -ForegroundColor Yellow
    & cmake --build . --config Release
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed!" -ForegroundColor Red
        Set-Location ..
        return $false
    }

    Write-Host "Build completed successfully!" -ForegroundColor Green

    # Step 6: 部署Qt库
    Write-Host "Deploying Qt libraries..." -ForegroundColor Yellow
    $targetDir = "bin\Release"
    & "$qtPath\bin\windeployqt.exe" "$targetDir\InkCop.exe" --qmldir "..\src" --release
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Qt deployment failed!" -ForegroundColor Red
        Set-Location ..
        return $false
    }

    Write-Host "Qt libraries deployed!" -ForegroundColor Green

    # Step 7: 复制ObjectBox DLL
    $objectboxDllPaths = @(
        "..\third-party\objectbox-windows\lib\objectbox.dll",
        "..\third-party\objectbox-c\lib\objectbox.dll",
        "third-party\objectbox-windows\lib\objectbox.dll",
        "third-party\objectbox-c\lib\objectbox.dll"
    )

    $objectboxCopied = $false
    foreach ($objectboxDll in $objectboxDllPaths) {
        if (Test-Path $objectboxDll) {
            Copy-Item $objectboxDll $targetDir -Force
            Write-Host "ObjectBox DLL copied from: $objectboxDll" -ForegroundColor Green
            $objectboxCopied = $true
            break
        }
    }

    if (-not $objectboxCopied) {
        Write-Host "Warning: ObjectBox DLL not found in any of the expected locations:" -ForegroundColor Yellow
        foreach ($path in $objectboxDllPaths) {
            Write-Host "  - $path" -ForegroundColor Gray
        }
    }

    # Step 8: 复制llama.cpp DLL（如果存在）
    $llamaPath = "..\third-party\llama.cpp\build-cuda\bin\Release"
    if (Test-Path $llamaPath) {
        Write-Host "Copying llama.cpp DLLs..." -ForegroundColor Yellow

        $llamaDlls = @(
            "ggml-base.dll",
            "ggml-cpu.dll",
            "ggml-cuda.dll",
            "ggml.dll",
            "llama.dll"
        )

        $copiedCount = 0
        foreach ($dll in $llamaDlls) {
            $sourceDll = Join-Path $llamaPath $dll
            if (Test-Path $sourceDll) {
                Copy-Item $sourceDll $targetDir -Force
                Write-Host "  Copied $dll" -ForegroundColor Blue
                $copiedCount++
            } else {
                Write-Host "  Warning: $dll not found" -ForegroundColor Yellow
            }
        }

        if ($copiedCount -gt 0) {
            Write-Host "llama.cpp DLLs copied! ($copiedCount/$($llamaDlls.Count) files)" -ForegroundColor Green

            # 复制必要的 CUDA 运行时库
            Write-Host "Copying CUDA runtime libraries..." -ForegroundColor Yellow

            $cudaPaths = @(
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin",
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin",
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.7\bin",
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin",
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.5\bin",
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin",
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.3\bin",
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.2\bin",
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\bin",
                "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.0\bin"
            )

            $cudaDlls = @(
                "cudart64_12.dll",
                "cublas64_12.dll",
                "cublasLt64_12.dll",
                "curand64_10.dll",
                "cusparse64_12.dll"
            )

            $cudaPath = $null
            foreach ($path in $cudaPaths) {
                if (Test-Path $path) {
                    $cudaPath = $path
                    break
                }
            }

            if ($cudaPath) {
                Write-Host "  Found CUDA installation: $cudaPath" -ForegroundColor Blue
                $cudaCopiedCount = 0

                foreach ($dll in $cudaDlls) {
                    $sourceDll = Join-Path $cudaPath $dll
                    if (Test-Path $sourceDll) {
                        try {
                            Copy-Item $sourceDll $targetDir -Force
                            Write-Host "    Copied $dll" -ForegroundColor Blue
                            $cudaCopiedCount++
                        } catch {
                            Write-Host "    Warning: Failed to copy $dll - $($_.Exception.Message)" -ForegroundColor Yellow
                        }
                    } else {
                        Write-Host "    Note: $dll not found (may not be required)" -ForegroundColor Gray
                    }
                }

                if ($cudaCopiedCount -gt 0) {
                    Write-Host "CUDA runtime libraries copied! ($cudaCopiedCount files)" -ForegroundColor Green
                } else {
                    Write-Host "Warning: No CUDA runtime libraries were copied" -ForegroundColor Yellow
                }
            } else {
                Write-Host "Warning: CUDA installation not found" -ForegroundColor Yellow
                Write-Host "  CUDA runtime libraries may be missing from the package" -ForegroundColor Yellow
            }
        } else {
            Write-Host "Warning: No llama.cpp DLLs found in $llamaPath" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Warning: llama.cpp build directory not found: $llamaPath" -ForegroundColor Yellow
        Write-Host "  Make sure to build llama.cpp with CUDA support first" -ForegroundColor Yellow
    }

    # Step 9: 复制QWindowKit DLL
    $qwkDllPath = "..\third-party\qwindowkit\build-qwk\out-amd64-Release\bin"
    if (Test-Path $qwkDllPath) {
        Write-Host "Copying QWindowKit DLLs..." -ForegroundColor Yellow

        $qwkDlls = Get-ChildItem -Path $qwkDllPath -Filter "*.dll"
        $copiedCount = 0

        foreach ($dll in $qwkDlls) {
            try {
                Copy-Item $dll.FullName $targetDir -Force
                Write-Host "  Copied $($dll.Name)" -ForegroundColor Blue
                $copiedCount++
            } catch {
                Write-Host "  Warning: Failed to copy $($dll.Name)" -ForegroundColor Yellow
            }
        }

        if ($copiedCount -gt 0) {
            Write-Host "QWindowKit DLLs copied! ($copiedCount files)" -ForegroundColor Green
        } else {
            Write-Host "Warning: No QWindowKit DLLs found or copied" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Warning: QWindowKit DLL directory not found: $qwkDllPath" -ForegroundColor Yellow
        Write-Host "  Make sure to build QWindowKit first" -ForegroundColor Yellow
    }

    # Step 10: 复制应用程序图标
    Write-Host "Copying application icon..." -ForegroundColor Yellow
    $iconSourcePath = "..\public\icons\favicon.ico"
    if (Test-Path $iconSourcePath) {
        try {
            Copy-Item $iconSourcePath $targetDir -Force
            Write-Host "  Application icon copied successfully" -ForegroundColor Green
        } catch {
            Write-Host "  Warning: Failed to copy application icon - $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  Warning: Application icon not found at $iconSourcePath" -ForegroundColor Yellow
    }

    Set-Location ..
    return $true
}

# 检查Inno Setup是否安装
function Test-InnoSetupInstalled {
    $innoSetupPaths = @(
        "${env:ProgramFiles(x86)}\Inno Setup 6\ISCC.exe",
        "${env:ProgramFiles}\Inno Setup 6\ISCC.exe",
        "${env:ProgramFiles(x86)}\Inno Setup 5\ISCC.exe",
        "${env:ProgramFiles}\Inno Setup 5\ISCC.exe"
    )

    foreach ($path in $innoSetupPaths) {
        if (Test-Path $path) {
            return $path
        }
    }

    # 检查PATH中是否有ISCC.exe
    try {
        $isccPath = (Get-Command "ISCC.exe" -ErrorAction Stop).Source
        return $isccPath
    } catch {
        return $null
    }
}

# 创建Inno Setup脚本
function New-InnoSetupScript {
    param(
        [string]$Version,
        [string]$BuildDirectory
    )

    $scriptContent = @"
[Setup]
AppId={{12345678-1234-1234-1234-123456789012}
AppName=InkCop
AppVersion=$Version
AppPublisher=InkCop
AppPublisherURL=https://github.com/your-repo/inkcop
AppSupportURL=https://github.com/your-repo/inkcop
AppUpdatesURL=https://github.com/your-repo/inkcop
DefaultDirName={localappdata}\InkCop
DefaultGroupName=InkCop
AllowNoIcons=yes
OutputDir=dist-packages
OutputBaseFilename=InkCop_${Version}_x64_Setup
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=lowest
PrivilegesRequiredOverridesAllowed=dialog
; 设置应用程序图标
SetupIconFile=$BuildDirectory\favicon.ico
UninstallDisplayIcon={app}\InkCop.exe

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "$BuildDirectory\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\InkCop"; Filename: "{app}\InkCop.exe"; IconFilename: "{app}\favicon.ico"
Name: "{group}\{cm:UninstallProgram,InkCop}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\InkCop"; Filename: "{app}\InkCop.exe"; IconFilename: "{app}\favicon.ico"; Tasks: desktopicon

[Run]
Filename: "{app}\InkCop.exe"; Description: "{cm:LaunchProgram,InkCop}"; Flags: nowait postinstall skipifsilent
"@

    $scriptPath = "InkCop_Setup.iss"
    $scriptContent | Out-File -FilePath $scriptPath -Encoding UTF8
    return $scriptPath
}

# 构建 EXE 安装包
function Invoke-ExeInstallerBuild {
    param(
        [string]$Version,
        [string]$BuildDirectory
    )

    Write-Host ""
    Write-Host "======================================" -ForegroundColor Green
    Write-Host "Building EXE Installer Package" -ForegroundColor Green
    Write-Host "======================================" -ForegroundColor Green

    # 检查 Inno Setup
    $isccPath = Test-InnoSetupInstalled
    if (-not $isccPath) {
        Write-Host "Inno Setup not found! Please install Inno Setup 6 from https://jrsoftware.org/isinfo.php" -ForegroundColor Red
        return @{ Success = $false; Message = "Inno Setup not installed" }
    }

    Write-Host "Found Inno Setup: $isccPath" -ForegroundColor Green

    # 创建 Inno Setup 脚本
    Write-Host "Creating Inno Setup script..." -ForegroundColor Yellow
    $scriptPath = New-InnoSetupScript $Version $BuildDirectory
    if (-not $scriptPath) {
        return @{ Success = $false; Message = "Failed to create Inno Setup script" }
    }

    # 编译安装包
    Write-Host "Compiling EXE installer..." -ForegroundColor Yellow
    & $isccPath $scriptPath
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Inno Setup compilation failed!" -ForegroundColor Red
        return @{ Success = $false; Message = "Inno Setup compilation failed" }
    }

    # 清理临时文件
    if (Test-Path $scriptPath) {
        Remove-Item $scriptPath -Force
    }

    # 验证输出
    $installerPath = "dist-packages\InkCop_${Version}_x64_Setup.exe"
    if (Test-Path $installerPath) {
        $fileSize = [math]::Round((Get-Item $installerPath).Length / 1MB, 2)
        Write-Host "EXE installer created successfully!" -ForegroundColor Green
        Write-Host "  Path: $installerPath" -ForegroundColor Blue
        Write-Host "  Size: $fileSize MB" -ForegroundColor Blue

        return @{
            Success = $true;
            Path = $installerPath;
            Size = $fileSize;
            Type = "EXE Installer"
        }
    } else {
        return @{ Success = $false; Message = "Installer file not found after compilation" }
    }
}

# 构建 ZIP 便携包
function Invoke-ZipPortableBuild {
    param(
        [string]$Version,
        [string]$BuildDirectory
    )

    Write-Host ""
    Write-Host "======================================" -ForegroundColor Green
    Write-Host "Building ZIP Portable Package" -ForegroundColor Green
    Write-Host "======================================" -ForegroundColor Green

    $zipPath = "dist-packages\InkCop_${Version}_x64_Portable.zip"

    # 创建临时目录
    $tempDir = "dist-packages\zip-temp"
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

    # 复制应用程序文件
    Write-Host "Copying application files..." -ForegroundColor Yellow
    Copy-Item "$BuildDirectory\*" "$tempDir\" -Recurse -Force

    # 创建便携包说明文件
    $readmeContent = @"
InkCop Portable Version $Version
================================

This is a portable version of InkCop that doesn't require installation.

How to use:
1. Extract all files to a folder of your choice
2. Run InkCop.exe to start the application
3. All settings and data will be stored in the application folder

System Requirements:
- Windows 10 version 1903 or later
- Visual C++ Redistributable 2019 or later
- .NET Framework 4.8 or later

For support, visit: https://github.com/your-repo/inkcop

Note: This portable version may require administrator privileges
for first-time setup of certain features.
"@

    $readmeContent | Out-File -FilePath "$tempDir\README.txt" -Encoding UTF8

    # 创建启动脚本（可选）
    $launchScript = @"
@echo off
echo Starting InkCop...
start "" "InkCop.exe"
"@

    $launchScript | Out-File -FilePath "$tempDir\Launch_InkCop.bat" -Encoding ASCII

    # 创建 ZIP 包
    Write-Host "Creating ZIP package..." -ForegroundColor Yellow

    try {
        # 使用 PowerShell 5.0+ 的 Compress-Archive
        if (Get-Command Compress-Archive -ErrorAction SilentlyContinue) {
            Compress-Archive -Path "$tempDir\*" -DestinationPath $zipPath -Force
        } else {
            # 回退到 .NET 方法
            Add-Type -AssemblyName System.IO.Compression.FileSystem
            [System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $zipPath)
        }

        # 清理临时目录
        Remove-Item $tempDir -Recurse -Force

        # 验证输出
        if (Test-Path $zipPath) {
            $fileSize = [math]::Round((Get-Item $zipPath).Length / 1MB, 2)
            Write-Host "ZIP portable package created successfully!" -ForegroundColor Green
            Write-Host "  Path: $zipPath" -ForegroundColor Blue
            Write-Host "  Size: $fileSize MB" -ForegroundColor Blue

            return @{
                Success = $true;
                Path = $zipPath;
                Size = $fileSize;
                Type = "ZIP Portable"
            }
        } else {
            return @{ Success = $false; Message = "ZIP file not found after compression" }
        }
    } catch {
        Write-Host "Failed to create ZIP package: $($_.Exception.Message)" -ForegroundColor Red
        return @{ Success = $false; Message = "ZIP compression failed: $($_.Exception.Message)" }
    }
}

# 生成自签名证书用于 MSIX 包
function New-SelfSignedCertificateForMsix {
    param([string]$Version)

    $certDir = "dist-packages\certificates"
    if (-not (Test-Path $certDir)) {
        New-Item -ItemType Directory -Path $certDir -Force | Out-Null
    }

    $certName = "InkCop_SelfSigned"
    $pfxPath = "$certDir\$certName.pfx"
    $cerPath = "$certDir\$certName.cer"
    $password = "InkCop2025"

    # 检查证书是否已存在且有效
    if (Test-Path $pfxPath) {
        try {
            $existingCert = Get-PfxCertificate -FilePath $pfxPath -Password (ConvertTo-SecureString $password -AsPlainText -Force)
            if ($existingCert.NotAfter -gt (Get-Date).AddDays(30)) {
                Write-Host "Using existing certificate: $pfxPath" -ForegroundColor Green
                return @{
                    Success = $true;
                    PfxPath = $pfxPath;
                    CerPath = $cerPath;
                    Password = $password;
                    Subject = $existingCert.Subject
                }
            }
        } catch {
            Write-Host "Existing certificate is invalid, creating new one..." -ForegroundColor Yellow
        }
    }

    Write-Host "Creating self-signed certificate for MSIX package..." -ForegroundColor Yellow

    try {
        # 创建自签名证书
        $cert = New-SelfSignedCertificate `
            -Type Custom `
            -Subject "CN=InkCop, O=InkCop, C=US" `
            -KeyUsage DigitalSignature `
            -FriendlyName "InkCop Code Signing Certificate" `
            -CertStoreLocation "Cert:\CurrentUser\My" `
            -TextExtension @("*********={text}*******.*******.3", "*********={text}")

        # 导出为PFX文件（包含私钥）
        $securePassword = ConvertTo-SecureString $password -AsPlainText -Force
        Export-PfxCertificate -Cert $cert -FilePath $pfxPath -Password $securePassword | Out-Null

        # 导出为CER文件（公钥）
        Export-Certificate -Cert $cert -FilePath $cerPath | Out-Null

        # 从个人存储中删除证书（可选）
        Remove-Item -Path "Cert:\CurrentUser\My\$($cert.Thumbprint)" -Force

        Write-Host "Certificate created successfully!" -ForegroundColor Green
        Write-Host "  PFX file: $pfxPath" -ForegroundColor Blue
        Write-Host "  CER file: $cerPath" -ForegroundColor Blue
        Write-Host "  Password: $password" -ForegroundColor Blue

        return @{
            Success = $true;
            PfxPath = $pfxPath;
            CerPath = $cerPath;
            Password = $password;
            Subject = $cert.Subject
        }
    } catch {
        Write-Host "Failed to create certificate: $($_.Exception.Message)" -ForegroundColor Red
        return @{
            Success = $false;
            Message = "Certificate creation failed: $($_.Exception.Message)"
        }
    }
}

# 创建 AppxManifest.xml 文件
function New-AppxManifest {
    param(
        [string]$Version,
        [string]$BuildDirectory
    )

    # 转换版本号为 MSIX 格式 (4 位数字)
    $versionParts = $Version.Split('.')
    while ($versionParts.Count -lt 4) {
        $versionParts += "0"
    }
    $msixVersion = $versionParts[0..3] -join '.'

    $manifestContent = @"
<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
         xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
         xmlns:uap3="http://schemas.microsoft.com/appx/manifest/uap/windows10/3"
         xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10"
         xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities">
  <Identity Name="InkCop"
            Version="$msixVersion"
            Publisher="CN=InkCop, O=InkCop, C=US"
            ProcessorArchitecture="x64" />

  <Properties>
    <DisplayName>InkCop</DisplayName>
    <PublisherDisplayName>InkCop</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
    <Description>InkCop - AI-powered writing assistant</Description>
    <uap:SupportedUsers>multiple</uap:SupportedUsers>
  </Properties>

  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.22621.0" />
  </Dependencies>

  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
    <Capability Name="internetClient" />
    <Capability Name="privateNetworkClientServer" />
  </Capabilities>

  <Applications>
    <Application Id="InkCop" Executable="InkCop.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements DisplayName="InkCop"
                          Description="AI-powered writing assistant"
                          BackgroundColor="transparent"
                          Square150x150Logo="Assets\Square150x150Logo.png"
                          Square44x44Logo="Assets\Square44x44Logo.png"
                          AppListEntry="none">
        <uap:DefaultTile Wide310x150Logo="Assets\Wide310x150Logo.png"
                         Square310x310Logo="Assets\Square150x150Logo.png"
                         Square71x71Logo="Assets\Square44x44Logo.png"/>
        <uap:SplashScreen Image="Assets\Square150x150Logo.png" BackgroundColor="transparent"/>
      </uap:VisualElements>
      <Extensions>
        <uap3:Extension Category="windows.appExecutionAlias"
                       Executable="InkCop.exe"
                       EntryPoint="Windows.FullTrustApplication">
          <uap3:AppExecutionAlias>
            <desktop:ExecutionAlias Alias="InkCop.exe"/>
          </uap3:AppExecutionAlias>
        </uap3:Extension>
      </Extensions>
    </Application>
  </Applications>
</Package>
"@

    $manifestPath = "$BuildDirectory\AppxManifest.xml"
    $manifestContent | Out-File -FilePath $manifestPath -Encoding UTF8
    return $manifestPath
}

# 创建应用图标资源
function New-DefaultAppIcons {
    param([string]$BuildDirectory)

    $assetsDir = "$BuildDirectory\Assets"
    if (-not (Test-Path $assetsDir)) {
        New-Item -ItemType Directory -Path $assetsDir -Force | Out-Null
    }

    # MSIX 需要的图标文件
    $iconFiles = @(
        "StoreLogo.png",
        "Square150x150Logo.png",
        "Square44x44Logo.png",
        "Wide310x150Logo.png"
    )

    # 图标源文件路径（按优先级排序）
    $iconSources = @{
        "StoreLogo.png" = @(
            "msix-icons-unplated\StoreLogo.png",
            "public\icons\icon-512x512.png"
        )
        "Square150x150Logo.png" = @(
            "msix-icons-unplated\Square150x150Logo.png",
            "public\icons\icon-512x512.png"
        )
        "Square44x44Logo.png" = @(
            "msix-icons-unplated\Square44x44Logo.png",
            "public\icons\icon-512x512.png"
        )
        "Wide310x150Logo.png" = @(
            "msix-icons-unplated\Wide310x150Logo.png",
            "public\icons\icon-512x512.png"
        )
    }

    # 复制所有 unplated 和 targetsize 图标文件（用于任务栏等透明背景显示）
    Write-Host "  Copying all unplated and targetsize icons for taskbar transparency..." -ForegroundColor Blue
    $unplatedCopiedCount = 0

    # 查找所有 unplated 和 targetsize 图标文件
    $completeIconsDir = "msix-icons-complete"
    if (Test-Path $completeIconsDir) {
        $allUnplatedFiles = Get-ChildItem -Path $completeIconsDir -Filter "*altform-unplated*" -Name
        $allTargetsizeFiles = Get-ChildItem -Path $completeIconsDir -Filter "*targetsize-*" -Name | Where-Object { $_ -notlike "*altform-unplated*" }

        # 复制所有 unplated 图标
        foreach ($unplatedFile in $allUnplatedFiles) {
            $sourcePath = "$completeIconsDir\$unplatedFile"
            $destPath = "$assetsDir\$unplatedFile"

            if (Test-Path $sourcePath) {
                try {
                    Copy-Item $sourcePath $destPath -Force
                    Write-Host "  Copied $unplatedFile" -ForegroundColor Green
                    $unplatedCopiedCount++
                } catch {
                    Write-Host "  Failed to copy $unplatedFile : $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        }

        # 复制所有 targetsize 图标（plated 版本）
        foreach ($targetsizeFile in $allTargetsizeFiles) {
            $sourcePath = "$completeIconsDir\$targetsizeFile"
            $destPath = "$assetsDir\$targetsizeFile"

            if (Test-Path $sourcePath) {
                try {
                    Copy-Item $sourcePath $destPath -Force
                    Write-Host "  Copied $targetsizeFile" -ForegroundColor Green
                    $unplatedCopiedCount++
                } catch {
                    Write-Host "  Failed to copy $targetsizeFile : $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        }
    } else {
        Write-Host "  Complete icons directory not found, using fallback..." -ForegroundColor Yellow

        # 备选方案：使用之前的 unplated 图标
        $fallbackUnplatedIcons = @{
            "Square44x44Logo.targetsize-44_altform-unplated.png" = @(
                "msix-icons-unplated\Square44x44Logo.targetsize-44_altform-unplated.png"
            )
            "Square150x150Logo.targetsize-150_altform-unplated.png" = @(
                "msix-icons-unplated\Square150x150Logo.targetsize-150_altform-unplated.png"
            )
            "StoreLogo.targetsize-50_altform-unplated.png" = @(
                "msix-icons-unplated\StoreLogo.targetsize-50_altform-unplated.png"
            )
            "Wide310x150Logo.targetsize-310_altform-unplated.png" = @(
                "msix-icons-unplated\Wide310x150Logo.targetsize-310_altform-unplated.png"
            )
        }

        foreach ($unplatedFile in $fallbackUnplatedIcons.Keys) {
            $unplatedPath = "$assetsDir\$unplatedFile"
            $unplatedCopied = $false

            foreach ($sourceFile in $fallbackUnplatedIcons[$unplatedFile]) {
                if (Test-Path $sourceFile) {
                    try {
                        Copy-Item $sourceFile $unplatedPath -Force
                        Write-Host "  Copied $unplatedFile from $sourceFile" -ForegroundColor Green
                        $unplatedCopied = $true
                        $unplatedCopiedCount++
                        break
                    } catch {
                        Write-Host "  Failed to copy $sourceFile : $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }
            }

            if (-not $unplatedCopied) {
                Write-Host "  No source found for $unplatedFile" -ForegroundColor Yellow
            }
        }
    }

    $copiedCount = 0
    $totalCount = $iconFiles.Count

    # 复制标准图标
    foreach ($iconFile in $iconFiles) {
        $iconPath = "$assetsDir\$iconFile"
        $iconCopied = $false

        # 尝试从源文件中复制图标
        foreach ($sourceFile in $iconSources[$iconFile]) {
            if (Test-Path $sourceFile) {
                try {
                    Copy-Item $sourceFile $iconPath -Force
                    Write-Host "  Copied $iconFile from $sourceFile" -ForegroundColor Green
                    $iconCopied = $true
                    $copiedCount++
                    break
                } catch {
                    Write-Host "  Failed to copy $sourceFile : $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        }

        # 如果没有找到源文件，创建占位符
        if (-not $iconCopied) {
            Write-Host "  No source found for $iconFile, creating placeholder" -ForegroundColor Red
            "Placeholder icon for $iconFile" | Out-File -FilePath $iconPath -Encoding ASCII
        }
    }



    # 报告复制结果
    Write-Host ""
    Write-Host "Icon Copy Summary:" -ForegroundColor Cyan
    Write-Host "Standard icons: $copiedCount/$totalCount" -ForegroundColor Green
    Write-Host "Unplated/Targetsize icons: $unplatedCopiedCount" -ForegroundColor Green

    if ($copiedCount -eq $totalCount -and $unplatedCopiedCount -gt 0) {
        Write-Host "All app icons copied successfully!" -ForegroundColor Green
        Write-Host "Complete icon set should fix taskbar background issues" -ForegroundColor Blue
    } elseif ($copiedCount -gt 0) {
        Write-Host "Partial success with standard icons" -ForegroundColor Yellow
        if ($unplatedCopiedCount -eq 0) {
            Write-Host "No unplated icons copied - taskbar may still show background" -ForegroundColor Yellow
        }
    } else {
        Write-Host "No real icons found, using placeholders" -ForegroundColor Red
        Write-Host "Run 'create-complete-msix-icons.ps1' first to generate proper icons" -ForegroundColor Yellow
    }
}

# 构建 MSIX 包
function Invoke-MsixPackageBuild {
    param(
        [string]$Version,
        [string]$BuildDirectory,
        [bool]$SkipPriGeneration = $false
    )

    Write-Host ""
    Write-Host "======================================" -ForegroundColor Green
    Write-Host "Building MSIX Package" -ForegroundColor Green
    Write-Host "======================================" -ForegroundColor Green

    # 转换版本号为 MSIX 格式 (4 位数字) - 用于文件名
    $versionParts = $Version.Split('.')
    while ($versionParts.Count -lt 4) {
        $versionParts += "0"
    }
    $msixVersion = $versionParts[0..3] -join '.'

    # 检查 Windows SDK 工具 - 扩展路径搜索
    $sdkVersions = @(
        "10.0.22621.0", "10.0.22000.0", "10.0.20348.0", "10.0.19041.0",
        "10.0.18362.0", "10.0.17763.0", "10.0.17134.0", "10.0.16299.0"
    )

    $makeappxPaths = @()
    $signtoolPaths = @()

    foreach ($version in $sdkVersions) {
        $makeappxPaths += "${env:ProgramFiles(x86)}\Windows Kits\10\bin\$version\x64\makeappx.exe"
        $makeappxPaths += "${env:ProgramFiles}\Windows Kits\10\bin\$version\x64\makeappx.exe"
        $signtoolPaths += "${env:ProgramFiles(x86)}\Windows Kits\10\bin\$version\x64\signtool.exe"
        $signtoolPaths += "${env:ProgramFiles}\Windows Kits\10\bin\$version\x64\signtool.exe"
    }

    # 也检查 App Certification Kit 路径
    $makeappxPaths += "${env:ProgramFiles(x86)}\Windows Kits\10\App Certification Kit\makeappx.exe"
    $makeappxPaths += "${env:ProgramFiles}\Windows Kits\10\App Certification Kit\makeappx.exe"
    $signtoolPaths += "${env:ProgramFiles(x86)}\Windows Kits\10\App Certification Kit\signtool.exe"
    $signtoolPaths += "${env:ProgramFiles}\Windows Kits\10\App Certification Kit\signtool.exe"

    $makeappxPath = $null
    $signtoolPath = $null

    foreach ($path in $makeappxPaths) {
        if (Test-Path $path) {
            $makeappxPath = $path
            break
        }
    }

    foreach ($path in $signtoolPaths) {
        if (Test-Path $path) {
            $signtoolPath = $path
            break
        }
    }

    if (-not $makeappxPath -or -not $signtoolPath) {
        Write-Host "Windows SDK tools not found!" -ForegroundColor Red
        Write-Host ""
        Write-Host "To build MSIX packages, you need to install Windows SDK:" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Option 1: Install Visual Studio with Windows SDK component" -ForegroundColor White
        Write-Host "  - Download Visual Studio Community (free)" -ForegroundColor Gray
        Write-Host "  - During installation, select 'Windows 10/11 SDK'" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Option 2: Install standalone Windows SDK" -ForegroundColor White
        Write-Host "  - Download from: https://developer.microsoft.com/en-us/windows/downloads/windows-sdk/" -ForegroundColor Gray
        Write-Host "  - Make sure to select 'Windows SDK for UWP C++ Apps'" -ForegroundColor Gray
        Write-Host ""
        Write-Host "After installation, the following tools should be available:" -ForegroundColor White
        Write-Host "  - makeappx.exe (for creating MSIX packages)" -ForegroundColor Gray
        Write-Host "  - signtool.exe (for signing packages)" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Skipping MSIX package creation..." -ForegroundColor Yellow
        return @{ Success = $false; Message = "Windows SDK tools not found - please install Windows SDK" }
    }

    Write-Host "Found Windows SDK tools:" -ForegroundColor Green
    Write-Host "  makeappx: $makeappxPath" -ForegroundColor Blue
    Write-Host "  signtool: $signtoolPath" -ForegroundColor Blue

    # 生成自签名证书
    Write-Host "Generating self-signed certificate..." -ForegroundColor Yellow
    $certResult = New-SelfSignedCertificateForMsix $Version
    if (-not $certResult.Success) {
        return @{ Success = $false; Message = $certResult.Message }
    }

    # 创建临时打包目录
    $tempPackageDir = "dist-packages\msix-temp"
    if (Test-Path $tempPackageDir) {
        Remove-Item $tempPackageDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempPackageDir -Force | Out-Null

    # 复制应用程序文件
    Write-Host "Copying application files..." -ForegroundColor Yellow
    Copy-Item "$BuildDirectory\*" "$tempPackageDir\" -Recurse -Force

    # 创建 AppxManifest.xml
    Write-Host "Creating AppxManifest.xml..." -ForegroundColor Yellow
    New-AppxManifest $Version $tempPackageDir | Out-Null

    # 创建增强版图标（优先使用 enhanced 版本）
    $enhancedIconsDir = "msix-icons-enhanced"
    if (Test-Path $enhancedIconsDir) {
        Write-Host "Using enhanced icon set for Windows 11 24H2 compatibility..." -ForegroundColor Green
        Copy-Item "$enhancedIconsDir\*" "$tempPackageDir\Assets\" -Force -Recurse
    } else {
        Write-Host "Creating default icons..." -ForegroundColor Yellow
        New-DefaultAppIcons $tempPackageDir
    }

    # 生成 PRI 文件（用于 unplated 图标支持）
    # 注意：PRI 文件不是必需的，如果生成失败可以跳过
    if ($SkipPriGeneration) {
        Write-Host "Skipping PRI file generation (disabled)" -ForegroundColor Yellow
    } else {
        Write-Host "Generating Package Resource Index (PRI) file..." -ForegroundColor Yellow

        # 查找 makepri.exe
        $makepriPath = $null
    $sdkBinPaths = @(
        "${env:ProgramFiles(x86)}\Windows Kits\10\bin\10.0.26100.0\x64\makepri.exe",
        "${env:ProgramFiles(x86)}\Windows Kits\10\bin\10.0.26100.0\x86\makepri.exe",
        "${env:ProgramFiles(x86)}\Windows Kits\10\bin\10.0.22621.0\x86\makepri.exe",
        "${env:ProgramFiles(x86)}\Windows Kits\10\bin\10.0.22000.0\x86\makepri.exe",
        "${env:ProgramFiles(x86)}\Windows Kits\10\bin\10.0.19041.0\x86\makepri.exe",
        "${env:ProgramFiles(x86)}\Windows Kits\10\bin\10.0.18362.0\x86\makepri.exe"
    )

    foreach ($path in $sdkBinPaths) {
        if (Test-Path $path) {
            $makepriPath = $path
            break
        }
    }

    if ($makepriPath) {
        Write-Host "  Found makepri.exe: $makepriPath" -ForegroundColor Blue

        try {
            # 创建 PRI 配置文件
            $priConfigPath = "$tempPackageDir\priconfig.xml"
            Write-Host "  Creating PRI config..." -ForegroundColor Gray

            $createConfigJob = Start-Job -ScriptBlock {
                param($makepriPath, $priConfigPath)
                & $makepriPath createconfig /cf $priConfigPath /dq en-US
                return $LASTEXITCODE
            } -ArgumentList $makepriPath, $priConfigPath

            if (Wait-Job $createConfigJob -Timeout 30) {
                $createConfigResult = Receive-Job $createConfigJob
                Remove-Job $createConfigJob

                if ($createConfigResult -eq 0 -and (Test-Path $priConfigPath)) {
                    Write-Host "  PRI config created successfully" -ForegroundColor Gray

                    # 生成 PRI 文件
                    Write-Host "  Generating PRI file..." -ForegroundColor Gray

                    $generatePriJob = Start-Job -ScriptBlock {
                        param($makepriPath, $tempPackageDir, $priConfigPath)
                        Set-Location $tempPackageDir
                        $priOutputPath = Join-Path $tempPackageDir "resources.pri"
                        & $makepriPath new /pr . /cf $priConfigPath /of $priOutputPath
                        return $LASTEXITCODE
                    } -ArgumentList $makepriPath, $tempPackageDir, $priConfigPath

                    if (Wait-Job $generatePriJob -Timeout 60) {
                        $generatePriResult = Receive-Job $generatePriJob
                        Remove-Job $generatePriJob

                        if ($generatePriResult -eq 0) {
                            Write-Host "  PRI file generated successfully" -ForegroundColor Green
                        } else {
                            Write-Host "  PRI file generation failed (exit code: $generatePriResult), continuing without it" -ForegroundColor Yellow
                        }
                    } else {
                        Write-Host "  PRI file generation timed out after 60 seconds, continuing without it" -ForegroundColor Yellow
                        Stop-Job $generatePriJob
                        Remove-Job $generatePriJob
                    }
                } else {
                    Write-Host "  PRI config creation failed (exit code: $createConfigResult), continuing without it" -ForegroundColor Yellow
                }
            } else {
                Write-Host "  PRI config creation timed out after 30 seconds, continuing without it" -ForegroundColor Yellow
                Stop-Job $createConfigJob
                Remove-Job $createConfigJob
            }
        } catch {
            Write-Host "  PRI generation failed with error: $($_.Exception.Message)" -ForegroundColor Yellow
            Write-Host "  Continuing without PRI file..." -ForegroundColor Yellow
        }
        } else {
            Write-Host "  makepri.exe not found, continuing without PRI file" -ForegroundColor Yellow
            Write-Host "    This may affect unplated icon display" -ForegroundColor Gray
        }
    }

    # 创建 MSIX 包
    $msixPath = "dist-packages\InkCop_${msixVersion}_x64.msix"
    Write-Host "Creating MSIX package..." -ForegroundColor Yellow

    & $makeappxPath pack /d $tempPackageDir /p $msixPath /overwrite
    if ($LASTEXITCODE -ne 0) {
        Write-Host "MSIX package creation failed!" -ForegroundColor Red
        return @{ Success = $false; Message = "makeappx pack failed" }
    }

    # 签名 MSIX 包
    Write-Host "Signing MSIX package..." -ForegroundColor Yellow
    & $signtoolPath sign /fd SHA256 /a /f $certResult.PfxPath /p $certResult.Password $msixPath
    if ($LASTEXITCODE -ne 0) {
        Write-Host "MSIX package signing failed!" -ForegroundColor Red
        return @{ Success = $false; Message = "signtool sign failed" }
    }

    # 清理临时目录
    Remove-Item $tempPackageDir -Recurse -Force

    # 验证输出
    if (Test-Path $msixPath) {
        $fileSize = [math]::Round((Get-Item $msixPath).Length / 1MB, 2)
        Write-Host "MSIX package created successfully!" -ForegroundColor Green
        Write-Host "  Path: $msixPath" -ForegroundColor Blue
        Write-Host "  Size: $fileSize MB" -ForegroundColor Blue
        Write-Host "  Certificate: $($certResult.CerPath)" -ForegroundColor Blue

        return @{
            Success = $true;
            Path = $msixPath;
            Size = $fileSize;
            Type = "MSIX Package";
            CertificatePath = $certResult.CerPath;
            CertificatePassword = $certResult.Password
        }
    } else {
        return @{ Success = $false; Message = "MSIX file not found after creation" }
    }
}

function Main {
    # 选择要构建的包类型
    Write-Host ""
    $selectedPackageTypes = Show-PackageTypeSelector
    if (-not $selectedPackageTypes) {
        Write-Host "No package types selected, exiting" -ForegroundColor Yellow
        exit 1
    }

    Write-Host ""
    Write-Host "Selected package types: $($selectedPackageTypes -join ', ')" -ForegroundColor Green

    # 构建发行版本
    Write-Host ""
    Write-Host "======================================" -ForegroundColor Cyan
    Write-Host "Preparing Release Build for Packages" -ForegroundColor Cyan
    Write-Host "======================================" -ForegroundColor Cyan

    $buildStatus = Get-ReleaseBuildsStatus
    $selectedBuild = $null

    if ($SkipBuild) {
        Write-Host "Skip build mode - checking for existing release build..." -ForegroundColor Yellow

        if ($buildStatus.Exists) {
            Write-Host ""
            Write-Host "Found existing release build:" -ForegroundColor Green
            Write-Host "  Path: $($buildStatus.Path)" -ForegroundColor Blue
            Write-Host "  Size: $($buildStatus.Size) MB" -ForegroundColor Blue
            Write-Host "  Modified: $($buildStatus.LastModified)" -ForegroundColor Blue
            Write-Host ""
            Write-Host "Use this build for packages? [Y/n]: " -ForegroundColor Yellow -NoNewline
            $confirm = Read-Host

            if ($confirm -eq '' -or $confirm.ToLower() -eq 'y' -or $confirm.ToLower() -eq 'yes') {
                $selectedBuild = @{
                    Path = $buildStatus.Path;
                    Directory = $buildStatus.Directory;
                    Name = "Existing Release Build";
                    Description = "Previously built release version"
                }
                Write-Host "Using existing build for packages" -ForegroundColor Green
            } else {
                Write-Host "User chose not to use existing build, exiting" -ForegroundColor Yellow
                exit 1
            }
        } else {
            Write-Host ""
            Write-Host "No release build found at: $($buildStatus.Path)" -ForegroundColor Red
            Write-Host "Please run without -SkipBuild to build the application first" -ForegroundColor Yellow
            exit 1
        }
    } else {
        Write-Host "Building fresh release version for packages..." -ForegroundColor Green

        if (-not (Invoke-ApplicationBuild)) {
            Write-Host "Release build failed!" -ForegroundColor Red
            exit 1
        }

        # 验证构建结果
        $buildStatus = Get-ReleaseBuildsStatus
        if ($buildStatus.Exists) {
            $selectedBuild = @{
                Path = $buildStatus.Path;
                Directory = $buildStatus.Directory;
                Name = "Fresh Release Build";
                Description = "Newly built release version"
            }
            Write-Host ""
            Write-Host "Release build completed successfully!" -ForegroundColor Green
            Write-Host "  Executable: $($buildStatus.Path)" -ForegroundColor Blue
            Write-Host "  Size: $($buildStatus.Size) MB" -ForegroundColor Blue
        } else {
            Write-Host ""
            Write-Host "Build completed but executable not found at: $($buildStatus.Path)" -ForegroundColor Red
            Write-Host "Please check the build process for errors" -ForegroundColor Yellow
            exit 1
        }
    }

    # 验证选定的构建文件是否存在
    if (-not (Test-Path $selectedBuild.Path)) {
        Write-Host "Error: Selected build not found!" -ForegroundColor Red
        Write-Host "Path: $($selectedBuild.Path)" -ForegroundColor Yellow
        exit 1
    }

    Write-Host ""
    Write-Host "Preparing to package build artifact:" -ForegroundColor Green
    Write-Host "   Name: $($selectedBuild.Name)" -ForegroundColor Blue
    Write-Host "   Path: $($selectedBuild.Path)" -ForegroundColor Blue
    Write-Host ""

    # 创建输出目录
    if (-not (Test-Path "dist-packages")) {
        New-Item -ItemType Directory -Path "dist-packages" | Out-Null
    }

    # 构建选定的包类型
    $buildResults = @()
    $totalPackages = $selectedPackageTypes.Count
    $currentPackage = 0

    foreach ($packageType in $selectedPackageTypes) {
        $currentPackage++
        Write-Host ""
        Write-Host "======================================" -ForegroundColor Magenta
        Write-Host "Building Package $currentPackage of $totalPackages" -ForegroundColor Magenta
        Write-Host "======================================" -ForegroundColor Magenta

        $result = $null
        switch ($packageType) {
            "exe" {
                $result = Invoke-ExeInstallerBuild $Version $selectedBuild.Directory
            }
            "msix" {
                $result = Invoke-MsixPackageBuild $Version $selectedBuild.Directory $SkipPriGeneration
            }
            "zip" {
                $result = Invoke-ZipPortableBuild $Version $selectedBuild.Directory
            }
        }

        if ($result -and $result.Success) {
            $buildResults += $result
            Write-Host ""
            Write-Host "$($result.Type) created successfully!" -ForegroundColor Green
        } else {
            Write-Host ""
            Write-Host "Failed to create $packageType package: $($result.Message)" -ForegroundColor Red
            # 继续构建其他包类型，不退出
        }
    }

    # 显示构建结果汇总
    Write-Host ""
    Write-Host "======================================" -ForegroundColor Cyan
    Write-Host "Build Summary" -ForegroundColor Cyan
    Write-Host "======================================" -ForegroundColor Cyan

    if ($buildResults.Count -gt 0) {
        Write-Host "Successfully created $($buildResults.Count) package(s):" -ForegroundColor Green
        Write-Host ""

        $totalSize = 0
        foreach ($result in $buildResults) {
            Write-Host "[OK] $($result.Type)" -ForegroundColor Green
            Write-Host "  Path: $($result.Path)" -ForegroundColor Blue
            Write-Host "  Size: $($result.Size) MB" -ForegroundColor Blue

            # MSIX 包的特殊信息
            if ($result.Type -eq "MSIX Package" -and $result.CertificatePath) {
                Write-Host "  Certificate: $($result.CertificatePath)" -ForegroundColor Blue
                Write-Host "  Note: Install certificate before installing MSIX package" -ForegroundColor Yellow
            }

            Write-Host ""
            $totalSize += $result.Size
        }

        Write-Host "Total size: $([math]::Round($totalSize, 2)) MB" -ForegroundColor Cyan

        # 检查是否有 MSIX 包，如果有则显示安装说明
        $msixResult = $buildResults | Where-Object { $_.Type -eq "MSIX Package" }
        if ($msixResult) {
            Write-Host ""
            Write-Host "======================================" -ForegroundColor Magenta
            Write-Host "MSIX Package Installation Instructions" -ForegroundColor Magenta
            Write-Host "======================================" -ForegroundColor Magenta
            Write-Host ""
            Write-Host "Before installing the MSIX package, you need to install the certificate:" -ForegroundColor Yellow
            Write-Host ""
            Write-Host "1. Right-click on the certificate file:" -ForegroundColor White
            Write-Host "   $($msixResult.CertificatePath)" -ForegroundColor Blue
            Write-Host ""
            Write-Host "2. Select 'Install Certificate'" -ForegroundColor White
            Write-Host "3. Choose 'Local Machine' and click 'Next'" -ForegroundColor White
            Write-Host "4. Select 'Place all certificates in the following store'" -ForegroundColor White
            Write-Host "5. Click 'Browse' and select 'Trusted Root Certification Authorities'" -ForegroundColor White
            Write-Host "6. Click 'Next' and then 'Finish'" -ForegroundColor White
            Write-Host ""
            Write-Host "After installing the certificate, you can install the MSIX package by:" -ForegroundColor Yellow
            Write-Host "- Double-clicking the .msix file, or" -ForegroundColor White
            Write-Host "- Using PowerShell: Add-AppxPackage -Path '$($msixResult.Path)'" -ForegroundColor White
            Write-Host ""
            Write-Host "Note: This is a self-signed certificate for development/testing purposes." -ForegroundColor Gray
            Write-Host "For production distribution, use a certificate from a trusted CA." -ForegroundColor Gray
        }

        Write-Host ""
        Write-Host "All packages are ready for distribution!" -ForegroundColor Green
    } else {
        Write-Host "No packages were created successfully!" -ForegroundColor Red
        exit 1
    }
}

# 运行主函数
Main
