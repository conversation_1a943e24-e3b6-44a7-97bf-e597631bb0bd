# Pexels 视频播放故障排除

## 常见问题及解决方案

### 1. 206 Partial Content 错误

**问题描述**：
- 控制台显示 206 Partial Content 状态码
- Artplayer 报错但视频实际可以播放

**原因分析**：
- 206 是正常的 HTTP 状态码，用于视频流的分段传输
- Artplayer 可能在处理多分辨率源时出现兼容性问题

**解决方案**：
1. **简化播放器配置**：
   ```javascript
   // 禁用可能导致问题的功能
   const playerConfig = {
     pip: false,           // 禁用画中画
     setting: false,       // 禁用设置菜单
     playbackRate: false,  // 禁用播放速度
     aspectRatio: false,   // 禁用宽高比
   };
   ```

2. **优先使用较小质量**：
   ```javascript
   // 优先选择 SD 质量而不是 HD
   const defaultVideo = 
     mp4Files.find(file => file.quality === 'sd') ||
     mp4Files.find(file => file.quality === 'hd') ||
     mp4Files[0];
   ```

3. **添加跨域支持**：
   ```javascript
   const playerConfig = {
     crossOrigin: 'anonymous',
   };
   ```

### 2. 视频加载失败

**问题描述**：
- 视频无法播放
- 播放器显示错误信息

**解决方案**：
1. **检查视频 URL**：
   ```javascript
   console.log('视频URL:', defaultVideo.link);
   // 在浏览器中直接访问URL验证是否可用
   ```

2. **使用备用质量**：
   ```javascript
   // 自动切换到最小分辨率
   const fallbackVideo = mp4Files
     .sort((a, b) => a.width - b.width)[0];
   ```

3. **延迟重试**：
   ```javascript
   setTimeout(() => {
     artplayerInstance.switchUrl(fallbackVideo.link);
   }, 1000);
   ```

### 3. 多分辨率切换问题

**问题描述**：
- 质量切换菜单不显示
- 切换质量时出错

**解决方案**：
1. **验证视频源格式**：
   ```javascript
   const videoSources = mp4Files.map(file => ({
     html: getQualityLabel(file.quality, file.width, file.height),
     url: file.link,
     // 移除 type: 'mp4' 可能解决兼容性问题
   }));
   ```

2. **条件启用质量切换**：
   ```javascript
   // 只有在有多个不同质量时才启用
   if (videoSources.length > 1) {
     playerConfig.quality = videoSources;
     playerConfig.setting = true;
   }
   ```

### 4. 播放器初始化失败

**问题描述**：
- Artplayer 创建时抛出异常
- 播放器容器未正确初始化

**解决方案**：
1. **添加 try-catch**：
   ```javascript
   try {
     artplayerInstance = new Artplayer(playerConfig);
   } catch (error) {
     console.error('创建播放器失败:', error);
   }
   ```

2. **验证容器元素**：
   ```javascript
   if (!videoPlayerContainer.value) {
     console.error('播放器容器未找到');
     return;
   }
   ```

## 调试技巧

### 1. 启用详细日志

```javascript
// 在 previewVideo 函数中添加详细日志
console.log('🎥 [Debug] 视频数据:', video);
console.log('🎥 [Debug] MP4文件:', mp4Files);
console.log('🎥 [Debug] 默认视频:', defaultVideo);
console.log('🎥 [Debug] 播放器配置:', playerConfig);
```

### 2. 检查网络请求

1. 打开浏览器开发者工具
2. 切换到 Network 标签
3. 播放视频并观察请求状态
4. 206 状态码是正常的，表示分段传输

### 3. 验证视频 URL

```javascript
// 在控制台中测试视频URL
const testVideo = document.createElement('video');
testVideo.src = defaultVideo.link;
testVideo.onloadeddata = () => console.log('视频可以加载');
testVideo.onerror = (e) => console.error('视频加载失败:', e);
```

## 兼容性检查

### 浏览器支持

```javascript
// 检查浏览器对视频格式的支持
const video = document.createElement('video');
const canPlayMP4 = video.canPlayType('video/mp4');
console.log('MP4支持:', canPlayMP4);
```

### 设备性能

```javascript
// 根据设备性能选择合适的质量
const getOptimalQuality = () => {
  const connection = navigator.connection;
  if (connection) {
    if (connection.effectiveType === '4g') return 'hd';
    if (connection.effectiveType === '3g') return 'sd';
  }
  return 'sd'; // 默认使用标清
};
```

## 降级策略

### 1. 渐进式降级

```javascript
const qualityFallback = ['sd', 'hd', 'uhd'];
let currentQualityIndex = 0;

const tryNextQuality = () => {
  if (currentQualityIndex < qualityFallback.length - 1) {
    currentQualityIndex++;
    const quality = qualityFallback[currentQualityIndex];
    const video = mp4Files.find(f => f.quality === quality);
    if (video) {
      artplayerInstance.switchUrl(video.link);
    }
  }
};
```

### 2. 简化播放器

```javascript
// 最简配置
const minimalConfig = {
  container: videoPlayerContainer.value,
  url: defaultVideo.link,
  poster: video.image,
  autoplay: false,
  muted: false,
  loop: false,
};
```

## 性能优化

### 1. 预加载策略

```javascript
const playerConfig = {
  preload: 'metadata', // 只预加载元数据
  // preload: 'none',  // 不预加载（更快的初始化）
};
```

### 2. 内存管理

```javascript
// 确保正确销毁播放器
const closeVideoPreview = () => {
  if (artplayerInstance) {
    try {
      artplayerInstance.destroy();
      artplayerInstance = null;
    } catch (error) {
      console.error('销毁播放器失败:', error);
    }
  }
  videoPreviewDialog.value = false;
};
```

## 监控和报告

### 错误收集

```javascript
const errorReports = [];

artplayerInstance.on('error', (error) => {
  const report = {
    timestamp: new Date().toISOString(),
    error: error,
    videoUrl: defaultVideo.link,
    userAgent: navigator.userAgent,
  };
  errorReports.push(report);
  console.error('错误报告:', report);
});
```

### 性能监控

```javascript
const performanceMetrics = {
  loadStart: 0,
  canPlay: 0,
  firstFrame: 0,
};

artplayerInstance.on('ready', () => {
  performanceMetrics.loadStart = performance.now();
});

// 监控加载时间
artplayerInstance.on('video:canplay', () => {
  performanceMetrics.canPlay = performance.now();
  console.log('加载时间:', performanceMetrics.canPlay - performanceMetrics.loadStart, 'ms');
});
```

## 联系支持

如果问题仍然存在，请提供以下信息：

1. 浏览器版本和操作系统
2. 控制台错误日志
3. 网络请求详情（开发者工具 Network 标签）
4. 视频 URL 和质量信息
5. 重现步骤

这些信息将帮助快速定位和解决问题。
