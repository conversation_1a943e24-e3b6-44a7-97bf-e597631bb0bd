import type { JSONContent } from '@tiptap/vue-3';
import type { Document } from 'src/types/doc';
import { useSqlite } from './useSqlite';
import { useUtils } from './useUtils';
import { docStore } from './useStore';

export function useDocumentActions() {
  // 获取文档
  const onGetDocument = async (doc_id: number) => {
    return await useSqlite().getDocument(doc_id);
  };

  // 打开文档
  const onOpenDocument = (doc: Document, folderId: number) => {
    docStore.setLlmDocumentKey(doc.id);
    if (docStore.splitterWindows.length === 0) {
      docStore.splitterWindows.push({
        id: 0,
        flex: '1',
        documents: [doc],
        folderId: folderId,
      });
      docStore.setActiveDocumentId(0, doc.id);
      docStore.setActiveSplitterWindowId(0);
      return;
    }
    if (!docStore.activeSplitterWindowId) {
      docStore.setActiveSplitterWindowId(0);
    }
    const activeWin = docStore.getActiveWindow();
    // console.log('activeWin', activeWin);
    if (activeWin) {
      const hasThisDoc = activeWin.documents.find((d) => d.id === doc.id);
      if (!hasThisDoc) {
        activeWin.documents.push(doc);
      }
      docStore.setActiveDocumentId(activeWin.id, doc.id);
    }
  };

  // 重命名文档，只修改标题不影响内容
  const onRenameDocument = async (doc_id: number, newTitle: string) => {
    await useSqlite().renameDocument(doc_id, newTitle);
    return await useSqlite().getDocument(doc_id);
  };

  // 删除文档
  const onDeleteDocument = async (doc_id: number) => {
    await useSqlite().deleteDocument(doc_id);
    // 刷新逻辑交由外部
  };

  // 更新文档
  const onUpdateDocument = async (
    doc_id: number,
    title: string,
    content: JSONContent,
    folderId: number,
  ) => {
    const { checkContentSize } = useUtils();

    // 检查内容大小
    const sizeCheck = checkContentSize(content);
    console.log('🔄 [useDocumentActions] 开始更新文档:', {
      doc_id,
      title,
      folderId,
      contentSize: sizeCheck.sizeMB ? `${sizeCheck.sizeMB}MB` : 'unknown',
      isValidSize: sizeCheck.isValid,
    });

    // 如果内容过大，提前警告
    if (!sizeCheck.isValid && sizeCheck.error) {
      console.warn('⚠️ [useDocumentActions] 文档内容过大:', sizeCheck.error);
      // 这里仍然尝试保存，让 useSqlite 处理具体的错误
    }

    // 获取更新前的文档信息
    const beforeDoc = await useSqlite().getDocument(doc_id);
    console.log('📋 [useDocumentActions] 更新前文档信息:', {
      id: beforeDoc.id,
      title: beforeDoc.title,
      updated_at: beforeDoc.updated_at,
    });

    // 获取现有文档的metadata，保持不变
    const metadata = beforeDoc.metadata || '{}';

    await useSqlite().updateDocument(doc_id, title, content, folderId, metadata);

    // 获取更新后的文档信息
    const afterDoc = await useSqlite().getDocument(doc_id);
    console.log('✅ [useDocumentActions] 更新后文档信息:', {
      id: afterDoc.id,
      title: afterDoc.title,
      updated_at: afterDoc.updated_at,
      timeChanged: beforeDoc.updated_at !== afterDoc.updated_at,
    });

    // 检查文档是否被添加到知识库中，如果是则标记需要刷新
    if (beforeDoc.updated_at !== afterDoc.updated_at) {
      // 只有当文档时间戳确实发生变化时才检查知识库关联
      void (async () => {
        try {
          console.log('🔍 [useDocumentActions] 检查文档知识库关联:', doc_id);

          // 获取文档的知识库关联信息
          const associations = await useSqlite().getDocumentKnowledgeAssociations(doc_id);

          if (associations.length > 0) {
            console.log('📋 [useDocumentActions] 文档已关联到知识库，标记需要刷新:', {
              documentId: doc_id,
              associationsCount: associations.length,
              knowledgeBaseIds: associations.map((a) => a.knowledge_base_id),
            });

            // 导入知识库store
            const { useKnowledgeStore } = await import('src/stores/knowledge');
            const knowledgeStore = useKnowledgeStore();

            // 为每个关联的知识库标记需要刷新
            associations.forEach((association) => {
              knowledgeStore.markKnowledgeBaseNeedsRefresh(association.knowledge_base_id);
              console.log('🔄 [useDocumentActions] 已标记知识库需要刷新:', {
                knowledgeBaseId: association.knowledge_base_id,
                knowledgeDocumentId: association.knowledge_document_id,
              });
            });
          } else {
            console.log('📋 [useDocumentActions] 文档未关联到任何知识库，无需刷新');
          }
        } catch (error) {
          console.error('❌ [useDocumentActions] 检查文档知识库关联失败:', error);
          // 不阻断主流程，只记录错误
        }
      })();
    }

    return afterDoc;
  };

  return {
    onGetDocument,
    onOpenDocument,
    onRenameDocument,
    onDeleteDocument,
    onUpdateDocument,
  };
}
