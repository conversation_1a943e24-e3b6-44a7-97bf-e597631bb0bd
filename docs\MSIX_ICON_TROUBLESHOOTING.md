# MSIX 图标透明背景问题解决方案

## 问题描述
使用 `create-installer.ps1` 构建的 MSIX 安装包在安装时，右上角应用图标和任务栏图标显示深橙色背景而不是透明图标。

## 根本原因
这是 **Windows 11 24H2 的已知 bug**，不是打包问题：
- Microsoft 已确认这是 Windows 11 24H2 更新中的回归问题
- 系统无法正确加载 "unplated" 图标（透明背景图标）
- 回退到系统主题色（通常是橙色）作为背景

## 解决方案

### 1. 立即解决方案
运行增强版图标生成脚本：

```powershell
# 创建增强版图标集
.\create-enhanced-msix-icons.ps1

# 然后构建 MSIX
.\create-installer.ps1 -Type MSIX
```

### 2. 手动验证步骤

#### 检查图标文件
```powershell
# 验证图标是否存在
Get-ChildItem msix-icons-enhanced\*altform-unplated* | Select Name, Length

# 检查图像透明度
Add-Type -AssemblyName System.Drawing
$img = [System.Drawing.Image]::FromFile("msix-icons-enhanced\Square44x44Logo.targetsize-44_altform-unplated.png")
Write-Host "Format: $($img.RawFormat) - Size: $($img.Size)"
```

#### 验证 MSIX 包内容
```powershell
# 检查生成的 MSIX 包内容
.\verify-msix-content.ps1 dist-packages\InkCop_*.msix
```

### 3. 临时绕过方法

如果问题仍然存在，可以尝试以下方法：

#### 方法 A：手动安装证书
```powershell
# 安装自签名证书（以管理员身份运行）
$certPath = "dist-packages\certificates\InkCop_SelfSigned.cer"
certutil -addstore -f "Root" $certPath
```

#### 方法 B：使用 EXE 安装器
```powershell
# 改用 EXE 安装器（不受此问题影响）
.\create-installer.ps1 -Type EXE
```

### 4. 长期解决方案

#### 等待 Windows 更新
Microsoft 已修复此问题，等待 Windows Update：
- 检查 Windows Update：设置 → Windows 更新
- 安装所有累积更新
- 重启后测试 MSIX 包

#### 反馈给 Microsoft
通过 Feedback Hub 提交问题：
1. 打开 Feedback Hub
2. 搜索 "MSIX icon transparency"
3. 提交新问题并附上您的应用信息

## 技术细节

### 已实施的增强措施

#### 1. 增强图标集
- ✅ 完整的 unplated 图标（16px-256px）
- ✅ 商店图标 unplated 版本
- ✅ 宽图标 unplated 版本
- ✅ 高分辨率渲染

#### 2. 更新的 AppxManifest.xml
```xml
<uap:VisualElements 
    BackgroundColor="transparent"
    Square44x44Logo="AssetsSquare44x44Logo.png"
    AppListEntry="none">
```

#### 3. 增强的构建流程
- 优先使用增强图标集
- 自动生成 PRI 文件
- 包含所有必要的图标变体

### 验证图标透明度

#### 检查 PNG 透明度
```powershell
# 使用 PowerShell 检查透明度
Add-Type -AssemblyName System.Drawing
$icons = Get-ChildItem msix-icons-enhanced\*.png
foreach ($icon in $icons) {
    $img = [System.Drawing.Image]::FromFile($icon.FullName)
    $bitmap = New-Object System.Drawing.Bitmap($img)
    $isTransparent = $false
    for ($x = 0; $x -lt $bitmap.Width; $x += 10) {
        for ($y = 0; $y -lt $bitmap.Height; $y += 10) {
            $pixel = $bitmap.GetPixel($x, $y)
            if ($pixel.A -lt 255) {
                $isTransparent = $true
                break
            }
        }
        if ($isTransparent) { break }
    }
    Write-Host "$($icon.Name): Transparent=$isTransparent"
    $bitmap.Dispose()
    $img.Dispose()
}
```

## 构建命令参考

### 完整构建流程
```powershell
# 1. 创建增强图标
.\create-enhanced-msix-icons.ps1

# 2. 构建 MSIX
.\create-installer.ps1 -Type MSIX

# 3. 验证包内容
.\verify-msix-content.ps1 dist-packages\InkCop_*.msix
```

### 快速测试
```powershell
# 跳过构建直接使用现有版本
.\create-installer.ps1 -Type MSIX -SkipBuild
```

## 已知限制

1. **Windows 11 24H2 bug**：这是系统级问题，不是打包问题
2. **临时解决方案**：使用 EXE 安装器或等待系统更新
3. **证书要求**：MSIX 需要自签名证书安装
4. **测试环境**：建议在 Windows 10/11 21H2+ 测试以验证兼容性

## 获取帮助

如果遇到问题：
1. 检查 `dist-packages` 目录中的构建日志
2. 运行验证脚本：`.\verify-msix-content.ps1`
3. 查看 Windows 事件查看器中的应用程序日志
4. 在 GitHub issues 中提交问题报告

## 相关文件

- `create-enhanced-msix-icons.ps1` - 增强图标生成
- `verify-msix-content.ps1` - 包验证工具
- `create-installer.ps1` - 主构建脚本
- `msix-icons-enhanced/` - 增强图标目录