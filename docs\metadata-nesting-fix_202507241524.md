# Metadata字段嵌套问题修复记录

**日期**: 2025-07-24 15:24  
**问题**: 数据库中文档的metadata字段每次保存后都会嵌套一层JSON字符串转义

## 问题描述

用户报告数据库中文档的metadata字段出现不断嵌套的问题：
- 初始状态：metadata为空字符串
- 第一次修改保存后：变成`"\"{}\""`
- 多次修改后：不断增加转义层级，如`"\"\\\"{}\\\"\""`

## 根本原因分析

问题出现在**双重JSON序列化**：

1. **第一次序列化**：在`DocumentMetadata.vue`的`saveMetadataIfChanged`函数中
   ```typescript
   const metadataString = stringifyMetadata(metadataObject); // JSON.stringify()
   ```

2. **第二次序列化**：在`useSqlite.ts`的`updateDocument`函数中
   ```typescript
   const metadataJson = safeJsonStringify(metadata); // 再次JSON.stringify()
   ```

这导致每次保存都会对已经是JSON字符串的metadata再次进行序列化，产生嵌套转义。

## 修复方案

### 1. 修复useSqlite.ts中的双重序列化

**文件**: `src/composeables/useSqlite.ts`

**修改前**:
```typescript
// 先序列化 metadata，通常较小
const metadataJson = safeJsonStringify(metadata);
```

**修改后**:
```typescript
// metadata 参数已经是 JSON 字符串，不需要再次序列化
const metadataJson = metadata;
```

**影响的函数**:
- `createDocument` (第159行)
- `updateDocument` (第220行)

### 2. 增强parseMetadata函数处理历史嵌套数据

**文件**: `src/components/tiptap/DocumentMetadata.vue`

**修改前**:
```typescript
const parsed = JSON.parse(metadataString);
return {
  prompt: parsed.prompt || '',
  note: parsed.note || '',
};
```

**修改后**:
```typescript
let parsed = JSON.parse(metadataString);

// 处理嵌套的JSON字符串（由于之前的双重序列化导致的问题）
// 如果解析结果是字符串，继续解析直到得到对象
while (typeof parsed === 'string') {
  parsed = JSON.parse(parsed);
}

// 确保parsed是对象类型
if (typeof parsed === 'object' && parsed !== null) {
  return {
    prompt: parsed.prompt || '',
    note: parsed.note || '',
  };
} else {
  // 如果不是对象，返回默认值
  return { ...defaultMetadata };
}
```

## 调用链分析

所有调用`updateDocument`的地方都传入字符串类型的metadata：

1. **DocumentMetadata.vue**: `stringifyMetadata(metadataObject)` → 字符串
2. **useDocumentActions.ts**: `beforeDoc.metadata || '{}'` → 字符串  
3. **file.ts**: `existingDoc.metadata || '{}'` → 字符串
4. **doc.ts**: `document.metadata || '{}'` → 字符串
5. **FolderTree.vue**: `draggedDoc.metadata` → 字符串

这证实了`updateDocument`函数的metadata参数应该是字符串，不需要再次序列化。

## 测试验证

修复后的行为：
1. **新文档**: metadata正常保存为`{"prompt":"","note":""}`
2. **已有嵌套数据**: 通过增强的`parseMetadata`函数自动解嵌套
3. **后续保存**: 不再产生新的嵌套

## 预防措施

1. **类型安全**: `updateDocument`函数的metadata参数明确定义为`string`类型
2. **单一职责**: 序列化逻辑统一在调用方处理，存储层不再重复序列化
3. **向后兼容**: `parseMetadata`函数能处理历史嵌套数据

## 影响范围

- ✅ 修复新文档metadata保存问题
- ✅ 兼容处理历史嵌套数据
- ✅ 不影响其他功能模块
- ✅ 保持API接口不变
