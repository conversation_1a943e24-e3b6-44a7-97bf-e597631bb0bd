<template>
  <div class="word-count-stats">
    <div v-if="!docId" class="text-grey-6 text-center q-pa-md">
      {{ $t('src.components.tiptap.wordCountStats.selectDocument') }}
    </div>
    
    <div v-else class="q-pa-sm">
      <div class="text-h6 q-mb-md">{{ $t('src.components.tiptap.wordCountStats.title') }}</div>
      
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ wordCount || 0 }}</div>
          <div class="stat-label">{{ $t('src.components.tiptap.wordCountStats.wordCount') }}</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value">{{ characterCount }}</div>
          <div class="stat-label">{{ $t('src.components.tiptap.wordCountStats.characterCount') }}</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value">{{ paragraphCount }}</div>
          <div class="stat-label">{{ $t('src.components.tiptap.wordCountStats.paragraphCount') }}</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value">{{ readingTime }}</div>
          <div class="stat-label">{{ $t('src.components.tiptap.wordCountStats.readingTime') }}</div>
        </div>
      </div>
      
      <q-separator class="q-my-md" />
      
      <div class="additional-info">
        <div class="info-row">
          <span class="info-label">文档ID:</span>
          <span class="info-value">{{ docId }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">活动面板:</span>
          <span class="info-value">{{ activePaneIndex }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">最后更新:</span>
          <span class="info-value">{{ lastUpdated }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  docId?: number | null;
  activePaneIndex?: number;
  wordCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  docId: null,
  activePaneIndex: 0,
  wordCount: 0,
});

const { t: $t } = useI18n({ useScope: 'global' });

const lastUpdated = ref(new Date().toLocaleTimeString());

// 计算字符数（模拟）
const characterCount = computed(() => {
  return Math.round((props.wordCount || 0) * 1.5);
});

// 计算段落数（模拟）
const paragraphCount = computed(() => {
  return Math.max(1, Math.round((props.wordCount || 0) / 50));
});

// 计算阅读时间（基于平均阅读速度）
const readingTime = computed(() => {
  const wordsPerMinute = 200;
  const minutes = Math.ceil((props.wordCount || 0) / wordsPerMinute);
  return Math.max(1, minutes);
});

// 监听字数变化，更新最后更新时间
watch(() => props.wordCount, () => {
  lastUpdated.value = new Date().toLocaleTimeString();
});
</script>

<style scoped>
.word-count-stats {
  height: 100%;
  overflow-y: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.additional-info {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 8px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.info-row:not(:last-child) {
  border-bottom: 1px solid #e0e0e0;
}

.info-label {
  font-weight: 500;
  color: #666;
}

.info-value {
  color: #333;
  font-weight: 500;
}
</style>
