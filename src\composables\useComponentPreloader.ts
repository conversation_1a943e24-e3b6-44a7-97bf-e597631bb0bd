import { ref, computed } from 'vue';
import type { Component } from 'vue';

interface PreloadedComponent {
  name: string;
  component: Component | null;
  isLoading: boolean;
  error: Error | null;
  loadTime: number;
}

/**
 * 组件预加载器
 */
export class ComponentPreloader {
  private preloadedComponents = new Map<string, PreloadedComponent>();
  private loadingPromises = new Map<string, Promise<Component>>();

  /**
   * 预加载组件
   */
  async preloadComponent(name: string, loader: () => Promise<Component>): Promise<Component> {
    // 如果已经加载过，直接返回
    const existing = this.preloadedComponents.get(name);
    if (existing?.component) {
      return existing.component;
    }

    // 如果正在加载，返回现有的 Promise
    const existingPromise = this.loadingPromises.get(name);
    if (await existingPromise) {
      return existingPromise;
    }

    // 开始加载
    const startTime = performance.now();
    this.preloadedComponents.set(name, {
      name,
      component: null,
      isLoading: true,
      error: null,
      loadTime: 0,
    });

    const loadPromise = loader()
      .then((component) => {
        const loadTime = performance.now() - startTime;
        this.preloadedComponents.set(name, {
          name,
          component,
          isLoading: false,
          error: null,
          loadTime,
        });
        this.loadingPromises.delete(name);
        return component;
      })
      .catch((error) => {
        this.preloadedComponents.set(name, {
          name,
          component: null,
          isLoading: false,
          error,
          loadTime: 0,
        });
        this.loadingPromises.delete(name);
        throw error;
      });

    this.loadingPromises.set(name, loadPromise);
    return loadPromise;
  }

  /**
   * 获取预加载的组件
   */
  getComponent(name: string): Component | null {
    return this.preloadedComponents.get(name)?.component || null;
  }

  /**
   * 检查组件是否正在加载
   */
  isLoading(name: string): boolean {
    return this.preloadedComponents.get(name)?.isLoading || false;
  }

  /**
   * 获取组件加载错误
   */
  getError(name: string): Error | null {
    return this.preloadedComponents.get(name)?.error || null;
  }

  /**
   * 获取组件加载时间
   */
  getLoadTime(name: string): number {
    return this.preloadedComponents.get(name)?.loadTime || 0;
  }

  /**
   * 预加载多个组件
   */
  async preloadComponents(
    components: Array<{ name: string; loader: () => Promise<Component> }>,
  ): Promise<void> {
    const promises = components.map(({ name, loader }) =>
      this.preloadComponent(name, loader).catch((error) => {
        console.warn(`Failed to preload component ${name}:`, error);
      }),
    );

    await Promise.allSettled(promises);
  }

  /**
   * 清理预加载的组件
   */
  cleanup(): void {
    this.preloadedComponents.clear();
    this.loadingPromises.clear();
  }

  /**
   * 获取所有预加载状态
   */
  getAllStatus(): PreloadedComponent[] {
    return Array.from(this.preloadedComponents.values());
  }
}

/**
 * 组件预加载组合式函数
 */
export function useComponentPreloader() {
  const preloader = new ComponentPreloader();
  const isPreloading = ref(false);

  /**
   * 预加载编辑器相关组件
   * 注意：移除了SplitterPane和SplitterHandle的动态导入，因为它们已被静态导入
   */
  const preloadEditorComponents = async () => {
    isPreloading.value = true;

    try {
      await preloader.preloadComponents([
        {
          name: 'EditorGroup',
          loader: () => import('../components/EditorGroup.vue'),
        },
        {
          name: 'TipTap',
          loader: () => import('../components/tiptap/TipTap.vue'),
        },
        {
          name: 'EditorToolbar',
          loader: () => import('../components/tiptap/EditorToolbar.vue'),
        },
        // 移除SplitterPane和SplitterHandle，因为它们已被SplitterEditor.vue静态导入
      ]);
    } finally {
      isPreloading.value = false;
    }
  };

  /**
   * 带回退的组件加载
   */
  const loadWithFallback = async <T>(
    loader: () => Promise<T>,
    fallback: T,
    timeout: number = 5000,
  ): Promise<T> => {
    try {
      return await Promise.race([
        loader(),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Component load timeout')), timeout),
        ),
      ]);
    } catch (error) {
      console.warn('Component load failed, using fallback:', error);
      return fallback;
    }
  };

  /**
   * 智能组件加载器
   */
  const smartLoad = async <T>(
    name: string,
    loader: () => Promise<T>,
    options: {
      fallback?: T;
      timeout?: number;
      retries?: number;
    } = {},
  ): Promise<T> => {
    const { fallback, timeout = 5000, retries = 2 } = options;

    let lastError: Error | null = null;

    for (let i = 0; i <= retries; i++) {
      try {
        if (timeout) {
          return await Promise.race([
            loader(),
            new Promise<never>((_, reject) =>
              setTimeout(() => reject(new Error('Load timeout')), timeout),
            ),
          ]);
        } else {
          return await loader();
        }
      } catch (error) {
        lastError = error as Error;
        if (i < retries) {
          // 指数退避重试
          await new Promise((resolve) => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
      }
    }

    if (fallback !== undefined) {
      console.warn(
        `Failed to load ${name} after ${retries + 1} attempts, using fallback:`,
        lastError,
      );
      return fallback;
    }

    throw lastError;
  };

  /**
   * 获取预加载状态
   */
  const getPreloadStatus = computed(() => {
    return preloader.getAllStatus();
  });

  /**
   * 检查是否所有组件都已预加载
   */
  const allComponentsLoaded = computed(() => {
    const status = preloader.getAllStatus();
    return status.length > 0 && status.every((s) => s.component !== null && !s.isLoading);
  });

  return {
    preloader,
    isPreloading,
    preloadEditorComponents,
    loadWithFallback,
    smartLoad,
    getPreloadStatus,
    allComponentsLoaded,
  };
}

/**
 * 全局组件预加载器
 */
const globalPreloader = useComponentPreloader();

export const useGlobalPreloader = () => globalPreloader;
