# Ollama Reasoning Content 支持

## 概述

本功能为 Ollama API 添加了 `reasoning_content` 支持，解决了 Ollama 默认将思考过程和最终回答混合在 `content` 字段中的问题。

## 问题背景

Ollama 目前还不支持 OpenAI 的 `reasoning_content` 字段，而是使用 XML 标签（如 `<think></think>`）来区分思考过程和最终回答。这导致：

1. 思考过程和最终回答混合在一起
2. 与 OpenAI API 的行为不一致
3. 用户界面无法正确分离显示思考内容和回答内容

## 解决方案

### 1. 自动检测 Ollama 提供商

通过检查 `baseUrl` 来判断是否使用 Ollama：

```typescript
const isOllamaProvider = (baseUrl: string): boolean => {
  return (
    baseUrl.includes('localhost:11434') ||
    baseUrl.includes('127.0.0.1:11434') ||
    baseUrl.includes('ollama')
  );
};
```

### 2. 内容处理逻辑

当检测到使用 Ollama 时，使用状态机模式实时处理流式内容：

```typescript
const processOllamaContent = (content: string): { content: string; reasoning_content: string } => {
  let reasoning_content = '';
  let processedContent = '';
  let currentPos = 0;
  let isInThinkingMode = false;

  while (currentPos < content.length) {
    if (!isInThinkingMode) {
      // 查找 <think> 开始标签
      const thinkStartIndex = content.indexOf('<think>', currentPos);

      if (thinkStartIndex === -1) {
        // 没有找到开始标签，剩余内容都是回答
        processedContent += content.substring(currentPos);
        break;
      }

      // 将开始标签之前的内容添加到回答中
      processedContent += content.substring(currentPos, thinkStartIndex);

      // 切换到思考模式，跳过 <think> 标签
      isInThinkingMode = true;
      currentPos = thinkStartIndex + 7; // '<think>'.length = 7
    } else {
      // 在思考模式中，查找 </think> 结束标签
      const thinkEndIndex = content.indexOf('</think>', currentPos);

      if (thinkEndIndex === -1) {
        // 没有找到结束标签，剩余内容都是思考内容
        reasoning_content += content.substring(currentPos);
        break;
      }

      // 将结束标签之前的内容添加到思考内容中
      reasoning_content += content.substring(currentPos, thinkEndIndex);

      // 切换回回答模式，跳过 </think> 标签
      isInThinkingMode = false;
      currentPos = thinkEndIndex + 8; // '</think>'.length = 8
    }
  }

  return {
    content: processedContent.trim(),
    reasoning_content: reasoning_content.trim(),
  };
};
```

**核心优势：**

- ✅ **实时处理**：遇到 `<think>` 立即开始累积思考内容
- ✅ **流式友好**：不需要等待完整标签对出现
- ✅ **状态机模式**：准确跟踪当前处理状态
- ✅ **多段支持**：正确处理多个思考段落

### 3. 流式处理集成

在流式处理过程中，累积完整内容并实时分离：

```typescript
if (isUsingOllama) {
  // Ollama处理逻辑：累积所有内容，然后实时解析
  if (content) {
    ollamaContentBuffer += content;

    // 使用状态机实时处理内容，分离thinking和实际回答
    const processed = processOllamaContent(ollamaContentBuffer);

    // 实时更新reasoning_content（遇到<think>立即开始显示）
    lastMessage.reasoning_content = processed.reasoning_content;

    // 实时更新实际内容（标签外的内容立即显示）
    lastMessage.content = processed.content;
  }
}
```

**流式处理优势：**

- 🚀 **即时响应**：`<think>` 标签后的内容立即显示在思考区域
- 🔄 **实时更新**：每个数据块都会触发界面更新
- 📱 **用户体验**：用户可以实时看到AI的思考过程
- ⚡ **性能优化**：状态机算法效率高，处理速度快

## 支持的格式

### 基本格式

```
<think>这是思考过程</think>这是最终回答
```

### 多段思考

```
<think>第一段思考</think>部分回答<think>第二段思考</think>最终回答
```

### 复杂思考内容

````
<think>
用户问了一个关于编程的问题。
我需要考虑以下几点：
1. 代码的可读性
2. 性能优化
3. 最佳实践
</think>
根据您的问题，我建议使用以下方法：

```javascript
const result = data.map(item => item.value);
````

这种方法简洁且高效。

```

## 实现位置

该功能已集成到以下文件中：

1. **src/composeables/useQwen.ts** - 主要的 LLM 交互逻辑
2. **src/composeables/useFloatAgent.ts** - 浮动助手功能

## 兼容性

- ✅ 完全兼容现有的非 Ollama 提供商（如 Qwen、OpenAI 等）
- ✅ 自动检测 Ollama 提供商，无需手动配置
- ✅ 支持流式和非流式响应
- ✅ 保持与 OpenAI API 的一致性

## 测试验证

功能已通过以下测试用例验证：

1. 基本 thinking 标签处理
2. 多个 thinking 标签处理
3. 没有 thinking 标签的内容
4. 只有 thinking 标签的内容
5. 复杂 thinking 内容处理
6. URL 检测逻辑

所有测试用例均通过验证。

## 使用说明

1. 配置 Ollama 服务器地址（如 `http://localhost:11434/v1/chat/completions`）
2. 系统会自动检测并启用 Ollama 处理逻辑
3. 思考内容将自动分离到 `reasoning_content` 字段
4. 最终回答显示在 `content` 字段
5. 用户界面会正确显示分离的内容

## 注意事项

1. 该功能仅在检测到 Ollama 提供商时启用
2. 处理逻辑基于 `<think></think>` XML 标签
3. 如果 Ollama 未来支持原生 `reasoning_content` 字段，可能需要调整实现
4. 建议使用支持推理的模型（如 DeepSeek R1 系列）以获得最佳体验
```
