# 构建优化指南

## 问题描述

在构建过程中出现了多个Vite警告，提示某些模块既被静态导入又被动态导入，这会影响代码分割的效果：

```
[plugin vite:reporter] 
(!) C:/Users/<USER>/www/inkcop/src/components/tiptap/SplitterPane.vue is dynamically imported by C:/Users/<USER>/www/inkcop/src/composables/useComponentPreloader.ts but also statically imported by C:/Users/<USER>/www/inkcop/src/components/tiptap/SplitterEditor.vue, dynamic import will not move module into another chunk.
```

## 解决方案

### 1. 组件预加载器优化

**文件**: `src/composables/useComponentPreloader.ts`

**问题**: SplitterPane.vue 和 SplitterHandle.vue 既被动态导入（预加载器）又被静态导入（SplitterEditor.vue）

**解决**: 从预加载器中移除这些组件的动态导入，因为它们已经被静态导入，不需要额外的预加载

```typescript
// 修改前
await preloader.preloadComponents([
  { name: 'SplitterPane', loader: () => import('../components/tiptap/SplitterPane.vue') },
  { name: 'SplitterHandle', loader: () => import('../components/tiptap/SplitterHandle.vue') },
]);

// 修改后
// 移除SplitterPane和SplitterHandle，因为它们已被SplitterEditor.vue静态导入
```

### 2. Store中的动态导入优化

**文件**: `src/stores/ui.ts`, `src/stores/llm.ts`

**问题**: useSqlite 既被静态导入又被动态导入

**解决**: 将所有静态导入改为动态导入，确保一致性

#### UI Store 优化

```typescript
// 修改前
import { useSqlite } from 'src/composeables/useSqlite';
const { getAppSettings } = useSqlite();

// 修改后
const { useSqlite } = await import('src/composeables/useSqlite');
const { getAppSettings } = useSqlite();
```

#### LLM Store 优化

```typescript
// 修改前
import { useSqlite } from 'src/composeables/useSqlite';
const { createConversation } = useSqlite();

// 修改后
const getSqliteApi = async () => {
  const { useSqlite } = await import('src/composeables/useSqlite');
  return useSqlite();
};
const { createConversation } = await getSqliteApi();
```

### 3. 字体文件路径修复

**文件**: `src/css/excalidraw.css`

**问题**: Assistant字体文件路径使用相对路径，构建时无法解析

**解决**: 将相对路径改为绝对路径

```css
/* 修改前 */
src: url('./fonts/Assistant/Assistant-Regular.woff2') format('woff2');

/* 修改后 */
src: url('/fonts/Assistant/Assistant-Regular.woff2') format('woff2');
```

## 优化效果

### 1. 减少构建警告
- 消除了所有"dynamic import will not move module into another chunk"警告
- 提高了构建过程的清洁度

### 2. 改善代码分割
- 确保动态导入的模块能够正确分割到独立的chunk中
- 优化了应用的加载性能

### 3. 统一导入策略
- Store中的数据库操作统一使用动态导入
- 避免了循环依赖和初始化顺序问题

### 4. 资源加载优化
- 字体文件能够在构建时正确处理
- 减少了运行时的资源解析开销

## 最佳实践

### 1. 导入策略选择

- **静态导入**: 用于必须在模块初始化时就需要的依赖
- **动态导入**: 用于按需加载、延迟加载或条件加载的模块

### 2. Store中的异步操作

- 数据库操作等异步依赖建议使用动态导入
- 避免在store初始化时就建立所有依赖关系

### 3. 组件预加载

- 只预加载真正需要延迟加载的组件
- 已经被静态导入的组件不需要额外预加载

### 4. 资源路径

- 静态资源使用绝对路径（从public目录开始）
- 确保构建工具能够正确处理资源引用

## 注意事项

1. **向后兼容性**: 这些修改不会影响应用的功能，只是优化了构建过程
2. **性能影响**: 动态导入可能会增加少量的运行时开销，但能够改善初始加载性能
3. **调试**: 动态导入的模块在调试时可能需要额外注意异步加载的时机

## 验证方法

构建应用后检查：

1. 构建警告是否消除
2. 生成的chunk文件是否合理分割
3. 应用功能是否正常工作
4. 字体文件是否正确加载

```bash
npm run build
# 检查构建输出，确认没有相关警告
```
