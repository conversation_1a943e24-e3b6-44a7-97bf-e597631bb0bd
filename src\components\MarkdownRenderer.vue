<template>
  <div
    class="tiptap prose dark:prose-invert max-w-none markdown-content"
    v-html="renderedContent"
  ></div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue';
import { marked } from 'marked';
import hljs from 'highlight.js';
import 'highlight.js/styles/github-dark.css';
import type { Tokens } from 'marked';
import { openExternalLink } from 'src/utils/externalLink';

interface Props {
  content: string;
  animated?: boolean;
  enableCopy?: boolean;
  checkLinkCredibility?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  animated: false,
  enableCopy: false,
  checkLinkCredibility: false,
});

// 自动修复markdown的工具函数
function autoFixMarkdown(markdown: string): string {
  return autoCloseTrailingLink(processKatexInMarkdown(dropMarkdownQuote(markdown)));
}

function autoCloseTrailingLink(markdown: string): string {
  let fixedMarkdown = markdown;

  // Fix unclosed image syntax ![...](...)
  fixedMarkdown = fixedMarkdown.replace(
    /!\[([^\]]*)\]\(([^)]*)$/g,
    (match: string, altText: string, url: string): string => {
      return `![${altText}](${url})`;
    },
  );

  // Fix unclosed link syntax [...](...)
  fixedMarkdown = fixedMarkdown.replace(
    /\[([^\]]*)\]\(([^)]*)$/g,
    (match: string, linkText: string, url: string): string => {
      return `[${linkText}](${url})`;
    },
  );

  // Fix unclosed image syntax ![...]
  fixedMarkdown = fixedMarkdown.replace(
    /!\[([^\]]*)$/g,
    (match: string, altText: string): string => {
      return `![${altText}]`;
    },
  );

  // Fix unclosed link syntax [...]
  fixedMarkdown = fixedMarkdown.replace(
    /\[([^\]]*)$/g,
    (match: string, linkText: string): string => {
      return `[${linkText}]`;
    },
  );

  return fixedMarkdown;
}

function processKatexInMarkdown(markdown: string): string {
  if (!markdown) return markdown;

  return markdown
    .replace(/\\\\\[/g, '$$')
    .replace(/\\\\\]/g, '$$')
    .replace(/\\\\\(/g, '$')
    .replace(/\\\\\)/g, '$')
    .replace(/\\\[/g, '$$')
    .replace(/\\\]/g, '$$')
    .replace(/\\\(/g, '$')
    .replace(/\\\)/g, '$');
}

function dropMarkdownQuote(markdown: string): string {
  if (!markdown) return markdown;
  return markdown
    .replace(/^```markdown\n/gm, '')
    .replace(/^```text\n/gm, '')
    .replace(/^```\n/gm, '')
    .replace(/\n```$/gm, '');
}

// 配置marked选项
marked.use({
  renderer: {
    code(token: Tokens.Code) {
      const validLanguage = hljs.getLanguage(token.lang || '') ? token.lang : 'plaintext';
      const highlighted = hljs.highlight(token.text, {
        language: validLanguage || 'plaintext',
      }).value;
      return `<pre><code class="hljs language-${validLanguage}">${highlighted}</code></pre>`;
    },

    link(token: Tokens.Link) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const text = this.parser.parseInline(token.tokens);
      const isExternal = token.href?.startsWith('http');
      const target = isExternal ? 'target="_blank" rel="noopener noreferrer"' : '';
      const titleAttr = token.title ? `title="${token.title}"` : '';
      return `<a href="${token.href}" ${target} ${titleAttr}>${text}</a>`;
    },

    image(token: Tokens.Image) {
      const titleAttr = token.title ? `title="${token.title}"` : '';
      const altAttr = token.text ? `alt="${token.text}"` : '';

      return `<img src="${token.href}" ${altAttr} ${titleAttr} class="rounded max-w-full h-auto cursor-pointer" onclick="handleImageClick('${token.href}')" />`;
    },
  },
  breaks: true, // 识别换行符
  gfm: true, // 启用GitHub风格的markdown
  silent: false, // 不静默错误
});

// 处理图片点击事件
const handleImageClick = (url: string) => {
  openExternalLink(url);
};

// 在组件挂载时将函数添加到全局作用域
onMounted(() => {
  (window as unknown as { handleImageClick?: (url: string) => void }).handleImageClick =
    handleImageClick;
});

// 在组件卸载时清理全局函数
onUnmounted(() => {
  const globalWindow = window as unknown as { handleImageClick?: (url: string) => void };
  if (globalWindow.handleImageClick === handleImageClick) {
    delete globalWindow.handleImageClick;
  }
});

const renderedContent = computed(() => {
  if (!props.content) return '';

  try {
    const fixedContent = autoFixMarkdown(props.content);
    const parsed = marked.parse(fixedContent);
    return parsed;
  } catch (error) {
    console.error('Markdown parsing error:', error);
    return `<pre>${props.content}</pre>`;
  }
});
</script>

<style scoped>
.markdown-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1rem 0;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.markdown-content :deep(img:hover) {
  transform: scale(1.02);
}

.markdown-content :deep(p) {
  margin: 0.5rem 0;
}

.markdown-content :deep(ol),
.markdown-content :deep(ul) {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.markdown-content :deep(li) {
  margin: 0.25rem 0;
}
</style>
