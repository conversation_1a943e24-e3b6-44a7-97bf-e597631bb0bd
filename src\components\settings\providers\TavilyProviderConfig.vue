<template>
  <div class="tavily-config q-gutter-y-md">
    <q-input
      :model-value="config.apiKey"
      :label="$t('src.components.settings.providers.TavilyProviderConfig.apiKeyLabel')"
      outlined
      dense
      type="password"
      :placeholder="$t('src.components.settings.providers.TavilyProviderConfig.apiKeyPlaceholder')"
      :hint="$t('src.components.settings.providers.TavilyProviderConfig.apiKeyHint')"
      @update:model-value="updateConfig('apiKey', $event)"
    />

    <q-input
      :model-value="config.baseUrl"
      :label="$t('src.components.settings.providers.TavilyProviderConfig.baseUrlLabel')"
      outlined
      dense
      :placeholder="$t('src.components.settings.providers.TavilyProviderConfig.baseUrlPlaceholder')"
      :hint="$t('src.components.settings.providers.TavilyProviderConfig.baseUrlHint')"
      @update:model-value="updateConfig('baseUrl', $event)"
    />

    <q-select
      :model-value="config.searchDepth"
      :options="searchDepthOptions"
      :label="$t('src.components.settings.providers.TavilyProviderConfig.searchDepthLabel')"
      outlined
      dense
      emit-value
      map-options
      :hint="$t('src.components.settings.providers.TavilyProviderConfig.searchDepthHint')"
      @update:model-value="updateConfig('searchDepth', $event)"
    />

    <q-input
      :model-value="config.maxResults"
      :label="$t('src.components.settings.providers.TavilyProviderConfig.maxResultsLabel')"
      outlined
      dense
      type="number"
      :placeholder="
        $t('src.components.settings.providers.TavilyProviderConfig.maxResultsPlaceholder')
      "
      :hint="$t('src.components.settings.providers.TavilyProviderConfig.maxResultsHint')"
      @update:model-value="updateConfig('maxResults', parseInt(String($event)) || 5)"
    />

    <div class="text-subtitle2 q-mt-md q-mb-sm">
      {{ $t('src.components.settings.providers.TavilyProviderConfig.searchOptions') }}
    </div>

    <q-toggle
      :model-value="config.includeAnswer"
      :label="$t('src.components.settings.providers.TavilyProviderConfig.includeAnswerLabel')"
      @update:model-value="updateConfig('includeAnswer', $event)"
    />
    <div class="text-caption text-grey-6 q-ml-md">
      {{ $t('src.components.settings.providers.TavilyProviderConfig.includeAnswerHint') }}
    </div>

    <q-toggle
      :model-value="config.includeRawContent"
      :label="$t('src.components.settings.providers.TavilyProviderConfig.includeRawContentLabel')"
      @update:model-value="updateConfig('includeRawContent', $event)"
    />
    <div class="text-caption text-grey-6 q-ml-md">
      {{ $t('src.components.settings.providers.TavilyProviderConfig.includeRawContentHint') }}
    </div>

    <q-toggle
      :model-value="config.includeImages"
      :label="$t('src.components.settings.providers.TavilyProviderConfig.includeImagesLabel')"
      @update:model-value="updateConfig('includeImages', $event)"
    />
    <div class="text-caption text-grey-6 q-ml-md">
      {{ $t('src.components.settings.providers.TavilyProviderConfig.includeImagesHint') }}
    </div>

    <q-toggle
      :model-value="config.includeImageDescriptions"
      :label="
        $t('src.components.settings.providers.TavilyProviderConfig.includeImageDescriptionsLabel')
      "
      :disable="!config.includeImages"
      @update:model-value="updateConfig('includeImageDescriptions', $event)"
    />
    <div class="text-caption text-grey-6 q-ml-md">
      {{
        $t('src.components.settings.providers.TavilyProviderConfig.includeImageDescriptionsHint')
      }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TavilySettings } from 'src/env.d';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

interface Props {
  config: TavilySettings;
}

interface Emits {
  (e: 'update', config: TavilySettings): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 搜索深度选项
const searchDepthOptions = [
  {
    label: $t('src.components.settings.providers.TavilyProviderConfig.searchDepthOptions.basic'),
    value: 'basic',
  },
  {
    label: $t('src.components.settings.providers.TavilyProviderConfig.searchDepthOptions.advanced'),
    value: 'advanced',
  },
];

// 更新配置
const updateConfig = (key: keyof TavilySettings, value: string | number | boolean) => {
  const updatedConfig = {
    ...props.config,
    [key]: value,
  };
  emit('update', updatedConfig);
};
</script>
