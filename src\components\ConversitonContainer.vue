<template>
  <div class="column no-wrap absolute-full">
    <!-- 对话状态栏 -->
    <div class="row no-wrap items-center gap-sm q-pt-md q-px-sm">
      <!-- 当前对话信息 -->
      <q-btn
        v-if="!llmStore.currentConversation"
        dense
        padding="2px 16px 2px 8px"
        noCaps
        unelevated
        rounded
        icon="mdi-plus"
        :color="$q.dark.isActive ? 'grey-9' : 'white text-grey-10'"
        :label="$t('src.components.ConversitonContainer.label.new_conversation')"
        @click="llmStore.clearCurrentConversation"
      />
      <span v-else class="text-weight-medium op-5">{{ llmStore.currentConversation?.title }}</span>
      <q-space />
      <q-btn
        v-if="llmStore.currentConversation"
        dense
        size="sm"
        round
        flat
        unelevated
        icon="mdi-plus"
        @click="llmStore.clearCurrentConversation"
      />
      <!-- 字体大小调节 -->
      <q-btn dense size="sm" round flat icon="mdi-dots-vertical">
        <q-menu class="shadow-24 radius-sm">
          <q-list dense bordered class="radius-sm">
            <q-item clickable @click="llmStore.fontSize = 1">
              <q-item-section side>
                <q-icon name="mdi-refresh" />
              </q-item-section>
              <q-item-section class="text-no-wrap unselectable">{{
                $t('src.components.ConversitonContainer.menu.reset_font_size')
              }}</q-item-section>
            </q-item>
            <q-item clickable @click="llmStore.adjustFontSize(0.1)">
              <q-item-section side>
                <q-icon name="mdi-plus" />
              </q-item-section>
              <q-item-section class="text-no-wrap unselectable">{{
                $t('src.components.ConversitonContainer.menu.increase_font_size')
              }}</q-item-section>
            </q-item>
            <q-item clickable @click="llmStore.adjustFontSize(-0.1)">
              <q-item-section side>
                <q-icon name="mdi-minus" />
              </q-item-section>
              <q-item-section class="text-no-wrap unselectable">{{
                $t('src.components.ConversitonContainer.menu.decrease_font_size')
              }}</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>

    <!-- 移除相关记忆提示，已替换为知识库功能 -->

    <!-- 消息显示区域 -->
    <q-scroll-area
      v-if="llmStore.messages.length > 0"
      class="q-space q-pa-md"
      ref="scrollArea"
      :style="{ fontSize: llmStore.fontSize + 'rem' }"
      @scroll="handleScroll"
    >
      <q-list class="message-list" :style="messageListStyle">
        <!-- 显示消息历史 -->
        <template v-for="(message, index) in llmStore.messages" :key="index">
          <div
            v-if="
              index === llmStore.messages.length - 1 && llmStore.knowledgeSearchResults.length > 0
            "
            :class="llmStore.showKnowledgeResults ? '' : 'q-mb-sm'"
          >
            <!-- 知识库搜索结果 -->
            <div
              class="row no-wrap items-center cursor-pointer"
              @click="llmStore.showKnowledgeResults = !llmStore.showKnowledgeResults"
              @mouseenter="showExpandMessageKnowledgeIcon = index"
              @mouseleave="showExpandMessageKnowledgeIcon = null"
            >
              <q-icon
                :name="
                  showExpandMessageKnowledgeIcon === index
                    ? llmStore.showKnowledgeResults
                      ? 'mdi-chevron-down'
                      : 'mdi-chevron-right'
                    : 'mdi-database-search'
                "
                class="q-mr-sm"
                :color="llmStore.showKnowledgeResults ? 'primary' : 'grey-6'"
              />
              <span class="op-5">{{
                $t('src.components.ConversitonContainer.knowledge.knowledge_search_results')
              }}</span>
            </div>
            <template v-if="llmStore.showKnowledgeResults">
              <div class="text-caption text-grey-6 q-mt-sm q-mb-xs">
                {{
                  $t(
                    'src.components.ConversitonContainer.knowledge.knowledge_search_results_count',
                    {
                      knowledgeBaseName: llmStore.selectedKnowledgeBase?.name,
                      count: llmStore.knowledgeSearchResults.length,
                    },
                  )
                }}
              </div>
              <div
                v-for="(result, index) in llmStore.knowledgeSearchResults"
                :key="index"
                class="knowledge-item q-mb-xs"
              >
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-subtitle2 text-primary q-mb-xs">
                      <q-icon name="article" class="q-mr-xs" />
                      {{ result.document_title }}
                    </div>
                    <div class="text-body2 q-mb-sm op-5">{{ result.memory }}</div>
                    <div class="text-caption text-grey-6">
                      <q-icon name="trending_up" class="q-mr-xs" size="xs" />
                      {{ $t('src.components.ConversitonContainer.knowledge.relevance') }}:
                      {{ (result.score * 100).toFixed(1) }}%
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </template>
          </div>
          <UserMessage v-if="message.role === 'user'" :message="message" />
          <AssistantMessage v-else-if="llmStore.isAssistantMessage(message)" :message="message">
            <template #toolMessage v-if="toolResult(index)">
              <ToolMessage
                v-if="llmStore.isToolMessage(toolResult(index))"
                :message="toolResult(index)"
              />
            </template>
          </AssistantMessage>
        </template>

        <template v-if="llmStore.waittingLlm">
          <SkeletonWaitting class="q-px-xs" />
        </template>

        <!-- 用户提问卡片 -->
        <q-card
          v-if="llmStore.currentUserQuestion"
          class="q-mt-md"
          flat
          bordered
          :class="$q.dark.isActive ? 'bg-grey-8' : 'bg-orange-1'"
        >
          <q-card-section class="q-pb-sm">
            <div class="row items-center q-mb-sm">
              <q-icon name="mdi-help-circle" color="orange" class="q-mr-sm" />
              <div class="text-weight-medium text-orange-8">
                {{ $t('src.components.ConversitonContainer.ai_question.title') }}
              </div>
              <q-space />
              <div class="text-caption text-grey-6">
                {{ new Date(llmStore.currentUserQuestion.timestamp).toLocaleTimeString() }}
              </div>
            </div>
            <div class="q-mb-md text-body1">
              {{ llmStore.currentUserQuestion.question }}
            </div>
            <q-input
              v-model="llmStore.userAnswer"
              :label="$t('src.components.ConversitonContainer.ai_question.input_placeholder')"
              dense
              outlined
              autogrow
              type="textarea"
              :rows="2"
              @keydown.enter="handleAnswerEnter"
              class="q-mb-sm"
            />
          </q-card-section>
          <q-card-actions align="right" class="q-pt-none">
            <q-btn
              flat
              dense
              :label="$t('src.components.ConversitonContainer.ai_question.cancel_question')"
              color="grey-7"
              @click="handleCancelQuestion"
              :disable="submittingAnswer"
            />
            <q-btn
              dense
              unelevated
              :label="$t('src.components.ConversitonContainer.ai_question.submit_answer')"
              color="primary"
              @click="handleSubmitAnswer"
              :loading="submittingAnswer"
              :disable="!llmStore.userAnswer.trim()"
            />
          </q-card-actions>
        </q-card>
      </q-list>
    </q-scroll-area>
    <!-- 消息输入区域 -->
    <div class="q-pa-sm column">
      <!-- 输入框和控制按钮 -->
      <div
        class="border radius-sm column no-wrap q-px-xs q-pb-xs"
        :class="$q.dark.isActive ? 'bg-grey-9' : 'bg-grey-3'"
      >
        <div class="row q-pt-xs">
          <!-- 相关文档chips - 已移除，统一使用附加内容显示 -->

          <!-- 选中文本chip -->
          <template v-if="llmStore.currentSelectedText">
            <q-chip
              :label="`${getSelectedTextPreview()}`"
              icon="mdi-cursor-text"
              color="primary"
              square
              dense
              removable
              @remove="llmStore.clearSelectedText()"
            >
              <q-tooltip>{{ getSelectedTextTooltip() }}</q-tooltip>
            </q-chip>
          </template>

          <!-- 附加内容chips -->
          <template v-if="llmStore.attachmentState.attachments.length > 0">
            <q-chip
              v-for="attachment in llmStore.attachmentState.attachments"
              :key="attachment.id"
              :label="getAttachmentLabel(attachment)"
              :icon="getAttachmentIcon(attachment)"
              :color="getAttachmentColor(attachment)"
              square
              dense
              removable
              @remove="llmStore.removeAttachment(attachment.id)"
            >
              <q-tooltip>{{ getAttachmentTooltip(attachment) }}</q-tooltip>
            </q-chip>
          </template>
        </div>
        <template v-if="!llmStore.needSettings">
          <q-input
            v-model="llmStore.input"
            :placeholder="getInputPlaceholder()"
            :disable="llmStore.loading"
            borderless
            dense
            autogrow
            type="textarea"
            @keydown.enter="handleInputEnter"
            @input="handleInputChange"
            @compositionstart="isComposing = true"
            @compositionend="isComposing = false"
            class="full-width q-px-sm"
          >
          </q-input>
          <div class="row no-wrap gap-sm items-center">
            <!-- 附加内容按钮 -->
            <q-btn
              v-if="false"
              dense
              size="sm"
              round
              flat
              icon="mdi-plus"
              :color="llmStore.attachmentState.attachments.length > 0 ? 'primary' : void 0"
            >
              <q-tooltip>{{
                $t('src.components.ConversitonContainer.tooltip.add_attachment')
              }}</q-tooltip>
              <q-menu class="shadow-24 radius-sm">
                <q-list dense bordered class="radius-sm">
                  <q-item clickable v-close-popup @click="handleFileUpload">
                    <q-item-section side>
                      <q-icon name="mdi-file-upload" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{
                        $t('src.components.ConversitonContainer.tooltip.upload_file')
                      }}</q-item-label>
                      <q-item-label caption>{{
                        $t('src.components.ConversitonContainer.tooltip.upload_file_hint')
                      }}</q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-item clickable v-close-popup @click="handleImageUpload">
                    <q-item-section side>
                      <q-icon name="mdi-image" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{
                        $t('src.components.ConversitonContainer.tooltip.upload_image')
                      }}</q-item-label>
                      <q-item-label caption>{{
                        $t('src.components.ConversitonContainer.tooltip.upload_image_hint')
                      }}</q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-separator />
                  <q-item clickable v-close-popup @click="handlePasteFromClipboard">
                    <q-item-section side>
                      <q-icon name="mdi-content-paste" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{
                        $t('src.components.ConversitonContainer.tooltip.paste_clipboard')
                      }}</q-item-label>
                      <q-item-label caption>{{
                        $t('src.components.ConversitonContainer.tooltip.paste_clipboard_hint')
                      }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>

            <q-btn
              rounded
              dense
              unelevated
              size="sm"
              padding="2px 12px"
              noCaps
              :color="$q.dark.isActive ? 'grey-8' : 'white text-grey-10'"
            >
              <q-icon :name="conversationMode.icon" size="14px" class="op-5" />
              <span class="q-ml-sm text-body2">{{ conversationMode.label }}</span>
              <q-menu class="shadow-24 radius-sm">
                <q-list dense class="radius-sm" style="max-width: 14rem">
                  <q-item
                    v-for="mode in conversationModes.filter((item) => item.enable)"
                    :key="mode.key"
                    clickable
                    v-close-popup
                    :class="{
                      'text-primary bg-primary-op-xs': mode.key === llmStore.conversationMode,
                    }"
                    @click="toggleConversationMode(mode.key)"
                  >
                    <q-item-section side>
                      <q-icon
                        :name="mode.icon"
                        :color="mode.key === llmStore.conversationMode ? 'primary' : 'grey-6'"
                        size="24px"
                      />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>
                        <strong>{{ mode.label }}</strong>
                      </q-item-label>
                      <q-item-label caption lines="2" class="op-5">{{
                        mode.description
                      }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <!-- 大模型选择按钮 -->
            <div class="row items-center no-wrap cursor-pointer">
              <q-tooltip>{{
                $t('src.components.ConversitonContainer.tooltip.select_model')
              }}</q-tooltip>
              <q-icon size="1rem">
                <q-img
                  :src="
                    getResource(uiStore.perferences?.provider)?.icon[
                      $q.dark.isActive ? 'dark' : 'light'
                    ]
                  "
                  :ratio="1"
                  width="0.8rem"
                  height="0.8rem"
                />
              </q-icon>
              <span class="q-ml-xs">
                {{
                  getCurrentModelName()?.length > 8
                    ? getCurrentModelName()?.slice(0, 12) + '...'
                    : getCurrentModelName()
                }}
              </span>
              <q-menu class="shadow-24 radius-sm">
                <q-list dense class="radius-sm">
                  <template v-for="(option, index) in getModelOptions()" :key="index">
                    <!-- 分割线 -->
                    <div
                      v-if="'type' in option && option.type === 'separator'"
                      :key="`separator-${option.provider}`"
                      class="op-2 bg-grey q-mx-xs"
                      style="flex: 0 0 1px"
                    />
                    <!-- 模型选项 -->
                    <q-item
                      v-else-if="'value' in option"
                      clickable
                      v-close-popup
                      :class="{
                        'text-primary bg-primary-op-xs': option.value === getCurrentModelValue(),
                      }"
                      @click="selectModel(option.provider, option.model)"
                    >
                      <q-item-section side>
                        <q-icon size="1.25rem">
                          <q-img
                            :src="
                              getResource(option.provider)?.icon[
                                $q.dark.isActive ? 'dark' : 'light'
                              ]
                            "
                            :ratio="1"
                          />
                        </q-icon>
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ option.label }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                </q-list>
              </q-menu>
            </div>

            <!-- 提示词选择按钮 -->
            <div class="row items-center no-wrap cursor-pointer">
              <q-tooltip>{{
                $t('src.components.ConversitonContainer.tooltip.select_prompt')
              }}</q-tooltip>
              <q-icon name="fluent-app_recent" size="0.9rem" class="q-mr-xs" />
              <span class="q-ml-xs">
                {{ getCurrentPromptName() }}
              </span>
              <q-menu class="shadow-24 radius-sm">
                <q-list dense class="radius-sm">
                  <template v-for="(prompt, index) in getChatPromptOptions()" :key="index">
                    <q-item
                      clickable
                      v-close-popup
                      :class="{
                        'text-primary bg-primary-op-xs': prompt.name === getCurrentPromptValue(),
                      }"
                      @click="selectPrompt(prompt.name)"
                    >
                      <q-item-section>
                        <q-item-label>{{ prompt.name }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                </q-list>
              </q-menu>
            </div>
            <q-space />
            <q-btn
              v-if="llmStore.currentConversation"
              dense
              size="sm"
              round
              flat
              unelevated
              icon="mdi-plus"
              @click="llmStore.clearCurrentConversation"
            />
            <q-btn
              v-if="
                isKnowledgeOnline &&
                llmStore.knowledgeBases?.length > 0 &&
                isCurrentModelToolCapable &&
                hasEnabledModel
              "
              dense
              size="sm"
              flat
              :color="llmStore.selectedKnowledgeBase ? 'positive' : void 0"
              icon="mdi-database-search"
            >
              <q-tooltip>
                {{
                  llmStore.selectedKnowledgeBase
                    ? $t('src.components.ConversitonContainer.tooltip.selected_knowledge_base') +
                      ': ' +
                      llmStore.selectedKnowledgeBase.name
                    : $t('src.components.ConversitonContainer.tooltip.no_knowledge_base')
                }}
              </q-tooltip>
              <q-menu class="shadow-24 radius-sm border">
                <q-list class="radius-sm" dense>
                  <template v-if="llmStore.knowledgeBases.length > 0">
                    <template v-for="kb in llmStore.knowledgeBases" :key="kb.id">
                      <q-item
                        clickable
                        v-close-popup
                        :class="{
                          'bg-grey-9':
                            $q.dark.isActive && kb.id === llmStore.selectedKnowledgeBase?.id,
                          'bg-grey-2':
                            !$q.dark.isActive && kb.id === llmStore.selectedKnowledgeBase?.id,
                        }"
                        @click="llmStore.selectKnowledgeBase(kb)"
                      >
                        <q-item-section class="text-no-wrap">{{ kb.name }}</q-item-section>
                      </q-item>
                    </template>
                    <q-separator class="op-5 q-my-xxs" />
                  </template>
                  <q-item clickable v-close-popup @click="llmStore.selectKnowledgeBase(null)">
                    <q-item-section class="text-no-wrap">{{
                      $t('src.components.ConversitonContainer.tooltip.no_knowledge_base')
                    }}</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>

            <!-- 中断按钮 -->
            <q-btn
              v-if="llmStore.loading && llmStore.abortController"
              dense
              size="sm"
              round
              flat
              @click="handleAbortConversation"
              :color="llmStore.isAborting ? 'orange' : 'negative'"
              :loading="llmStore.isAborting"
              icon="mdi-stop"
            >
              <q-tooltip>
                {{
                  llmStore.isAborting
                    ? $t('src.components.ConversitonContainer.tooltip.aborting')
                    : $t('src.components.ConversitonContainer.tooltip.abort_conversation')
                }}
              </q-tooltip>
            </q-btn>

            <!-- 发送按钮 -->
            <q-btn
              v-else
              :loading="llmStore.loading"
              :disable="!llmStore.input.trim() || llmStore.loading"
              dense
              size="sm"
              round
              unelevated
              @click="llmStore.send"
              :color="$q.dark.isActive ? 'grey-8' : 'white text-grey-10'"
              icon="mdi-chevron-up"
            >
              <q-tooltip>
                {{
                  llmStore.currentConversation
                    ? $t('src.components.ConversitonContainer.tooltip.send_message')
                    : $t(
                        'src.components.ConversitonContainer.tooltip.send_message_and_create_new_conversation',
                      )
                }}
              </q-tooltip>
            </q-btn>
          </div>
        </template>

        <!-- 需要设置时的提示 -->
        <div v-else class="flex flex-center q-pt-sm" style="height: 6rem">
          <q-btn
            color="primary"
            flat
            noCaps
            icon="mdi-brain"
            :label="$t('src.components.ConversitonContainer.button.llm_settings')"
            @click="goSettings()"
          />
        </div>
        <q-input
          v-if="false && llmStore.currentConversation"
          v-model="llmStore.currentConversation.prompt"
          type="text"
          :label="$t('src.components.ConversitonContainer.tooltip.prompt')"
          dense
          outlined
        />
      </div>
      <div
        v-if="!isCurrentModelToolCapable && hasEnabledModel"
        class="text-grey row items-center gap-sm q-px-sm q-py-xs"
      >
        <q-icon name="info" color="orange" />
        <span class="text-orange">{{
          $t('src.components.ConversitonContainer.warning.no_tool_support')
        }}</span>
      </div>
    </div>
    <template v-if="showHistoryArea && hasEnabledModel">
      <q-space v-if="!llmStore.messages || llmStore.messages.length === 0" />
      <ConversitionHistory
        v-if="llmDocument && llmStore.conversations.length > 0"
        ref="historyRef"
        :conversations="llmStore.conversations"
        @select="llmStore.selectConversation"
        @add="llmStore.createNewConversation(llmStore.input)"
        @delete="llmStore.removeConversation"
        @rename="llmStore.renameConversation"
      />
      <ChatConversationList v-else class="op-5" @select-conversation="onSelectChatConversation" />
    </template>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick, watch } from 'vue';
import AssistantMessage from 'src/components/AssistantMessage.vue';
import UserMessage from 'src/components/UserMessage.vue';
import ToolMessage from 'src/components/ToolMessage.vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import ConversitionHistory from 'src/components/ConversitionHistory.vue';
import ChatConversationList from 'src/components/ChatConversationList.vue';
import SkeletonWaitting from './tiptap/SkeletonWaitting.vue';
import type { Conversation, Attachment, ToolMessage as ToolMessageType } from 'src/types/qwen';
import useConversition from 'src/composeables/useConversition';
import { getResource } from 'src/config/resourceMap';

import { useUiStore } from 'src/stores/ui';
import { useLlmStore } from 'src/stores/llm';
import { useDocStore } from 'src/stores/doc';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });
const docStore = useDocStore();
const uiStore = useUiStore();
const llmStore = useLlmStore();

const router = useRouter();
const $q = useQuasar();
const submittingAnswer = ref(false);
const isComposing = ref(false);
const isCurrentModelToolCapable = computed(() => llmStore.isCurrentModelToolCapable());

const {
  getModelOptions,
  getCurrentModelName,
  getCurrentModelValue,
  conversationModes,
  hasEnabledModel,
} = useConversition();

const availableModels = computed(() => getModelOptions());
const currentProvider = computed(() => uiStore.perferences.provider);
watch(
  [availableModels, currentProvider],
  () => {
    if (!currentProvider.value && availableModels.value?.length > 0) {
      const _providers = availableModels.value.filter((m) => 'value' in m);
      selectModel(_providers[0].provider, _providers[0].model);
    }
  },
  { deep: true },
);

const toolResult = (idx: number): ToolMessageType | null => {
  if (llmStore.messages[idx + 1] && llmStore.messages[idx + 1].role === 'tool') {
    return llmStore.messages[idx + 1] as ToolMessageType;
  }
  return null;
};

// 计算当前对话模式的显示信息
const conversationMode = computed(() => {
  const _conversationModes = conversationModes.filter((item) => item.enable);
  return (
    _conversationModes.find((mode) => mode.key === llmStore.conversationMode) ||
    _conversationModes[0]
  );
});

const toggleConversationMode = (key: string) => {
  const mode = key as 'agent' | 'chat';
  llmStore.toggleConversationMode(mode);
};

/**
 * 选择模型
 * 根据下拉菜单中选择的条目设置供应商和模型
 * 格式：供应商 / 模型名称
 */
const selectModel = (provider: string, model: string) => {
  console.log(`[ConversitonContainer] 选择模型: ${provider} / ${model}`);

  // 设置供应商
  uiStore.setLlmProvider(provider);

  // 设置对应供应商的模型
  if (provider === 'qwen') {
    uiStore.updateQwenSettings({ model });
  } else if (provider === 'ollama') {
    uiStore.updateOllamaSettings({ model });
  } else if (provider === 'minimax') {
    uiStore.updateMiniMaxSettings({ model });
  } else if (provider === 'deepseek') {
    uiStore.updateDeepSeekSettings({ model });
  } else if (provider === 'volces') {
    uiStore.updateVolcesSettings({ model });
  } else if (provider === 'moonshot') {
    uiStore.updateMoonshotSettings({ model });
  } else if (provider === 'anthropic') {
    uiStore.updateAnthropicSettings({ model });
  } else if (provider === 'openai') {
    uiStore.updateOpenAISettings({ model });
  } else if (provider === 'azureOpenai') {
    uiStore.updateAzureOpenAISettings({ model });
  } else if (provider === 'gemini') {
    uiStore.updateGeminiSettings({ model });
  } else if (provider === 'grok') {
    uiStore.updateGrokSettings({ model });
  } else if (provider === 'glm') {
    uiStore.updateGlmSettings({ model });
  }
};

/**
 * 获取当前提示词名称
 */
const getCurrentPromptName = () => {
  const selectedPrompt = uiStore.perferences?.prompt?.chat?.selected || 'default';
  return selectedPrompt;
};

/**
 * 获取当前提示词值
 */
const getCurrentPromptValue = () => {
  return uiStore.perferences?.prompt?.chat?.selected || 'default';
};

/**
 * 获取聊天提示词选项
 */
const getChatPromptOptions = () => {
  const chatPrompts = uiStore.perferences?.prompt?.chat?.list || [];
  return chatPrompts;
};

/**
 * 选择提示词
 */
const selectPrompt = (promptName: string) => {
  console.log(`[ConversitonContainer] 选择提示词: ${promptName}`);
  if (uiStore.perferences?.prompt?.chat) {
    uiStore.perferences.prompt.chat.selected = promptName;
    // 持久化到本地存储
    localStorage.setItem('inkcop_chat_prompt', promptName);
  }
};

// 滚动相关状态
const scrollArea = ref();
const isAutoScrollEnabled = ref(true);
const scrollThreshold = 260; // 距离底部多少像素时认为在底部
let autoScrollInterval: NodeJS.Timeout | null = null; // 自动滚动定时器
let userScrollTimeout: NodeJS.Timeout | null = null;

const props = defineProps<{
  rightDrawerWidth: number;
}>();

// 使用计算属性来优化性能，避免频繁重新渲染
const messageListStyle = computed(() => ({
  width: `${props.rightDrawerWidth - 32}px`,
}));

// 使用缓存优化计算属性
const llmDocument = computed(() => {
  const key = docStore.llmDocumentKey;
  return key ? docStore.getLlmDocumentByKey(key) : null;
});

// 优化历史区域显示逻辑
const showHistoryArea = computed(() => {
  if (llmStore.conversations?.length === 0 && llmStore.chatConversations?.length === 0) {
    return false;
  }
  return llmStore.messages.length === 0;
});

// 缓存知识库在线状态
const isKnowledgeOnline = computed(() => {
  return llmStore.knowledgeServiceStatus.status === 'online';
});
const showExpandMessageKnowledgeIcon = ref(null);

const goSettings = async () => {
  uiStore.app = 'settings';
  await router.push('/settings?settingfor=llm');
};

watch(
  () => llmStore.currentConversation,
  () => {
    if (llmStore.currentConversation && !llmStore.currentConversation.prompt) {
      llmStore.currentConversation.prompt = '';
    }
  },
  { immediate: true },
);

// 自动滚动定时器控制方法 - 优化性能
const startAutoScrollTimer = () => {
  // 清除可能存在的定时器
  stopAutoScrollTimer();

  // 启动新的定时器，减少频率以提高性能
  autoScrollInterval = setInterval(() => {
    if (isAutoScrollEnabled.value && llmStore.loading) {
      // 使用requestAnimationFrame优化滚动性能
      requestAnimationFrame(() => {
        scrollToBottom();
      });
    }
  }, 800); // 增加间隔时间，减少CPU占用
};

const stopAutoScrollTimer = () => {
  if (autoScrollInterval) {
    clearInterval(autoScrollInterval);
    autoScrollInterval = null;
    console.log('🔄 [滚动] 清除自动滚动定时器');
  }
};

// 监听加载状态变化，控制自动滚动定时器
watch(
  () => llmStore.loading,
  (isLoading, wasLoading) => {
    if (isLoading && !wasLoading) {
      // 开始加载时，启动自动滚动定时器
      console.log('🔄 [滚动] 开始加载，启动自动滚动定时器');
      startAutoScrollTimer();
    } else if (!isLoading && wasLoading) {
      // 结束加载时，清除自动滚动定时器
      console.log('🔄 [滚动] 结束加载，停止自动滚动定时器');
      stopAutoScrollTimer();
    }
  },
);
// 处理选择对话（独立对话或文档对话）
const onSelectChatConversation = async (conversation: Conversation) => {
  console.log('🔄 [组件] 切换到对话:', conversation.id);

  // 使用 llm store 选择对话
  await llmStore.selectChatConversation(conversation);

  // 重置滚动状态为启用
  isAutoScrollEnabled.value = true;
  console.log('🔄 [滚动] 切换对话，启用自动滚动');
};

// 获取输入框占位符文本
const getInputPlaceholder = () => {
  if (llmStore.currentConversation) {
    return $t('src.components.ConversitonContainer.input.placeholder_conversation');
  } else if (llmDocument.value) {
    return $t('src.components.ConversitonContainer.input.placeholder_document');
  } else {
    return $t('src.components.ConversitonContainer.input.placeholder_new');
  }
};

// 处理用户答案输入框回车事件
const handleAnswerEnter = (event: KeyboardEvent) => {
  // 如果按下了 Shift + Enter，允许换行，不提交
  if (event.shiftKey) {
    // 不阻止默认行为，允许换行
    return;
  }

  // 阻止默认行为并触发提交
  event.preventDefault();
  handleSubmitAnswer();
};

// 处理提交用户答案
const handleSubmitAnswer = () => {
  if (!llmStore.userAnswer.trim()) {
    $q.notify({
      type: 'warning',
      message: $t('src.components.ConversitonContainer.notification.please_input_answer'),
      position: 'top',
    });
    return;
  }

  submittingAnswer.value = true;
  try {
    const success = llmStore.submitUserAnswer();
    if (success) {
      $q.notify({
        type: 'positive',
        message: $t('src.components.ConversitonContainer.notification.answer_submitted'),
        position: 'top',
      });
    } else {
      $q.notify({
        type: 'negative',
        message: $t('src.components.ConversitonContainer.notification.submit_failed'),
        position: 'top',
      });
    }
  } catch (error) {
    console.error('提交答案失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.ConversitonContainer.notification.submit_failed'),
      position: 'top',
    });
  } finally {
    submittingAnswer.value = false;
  }
};

// 处理取消用户提问
const handleCancelQuestion = () => {
  try {
    const success = llmStore.cancelUserQuestion();
    if (success) {
      $q.notify({
        type: 'info',
        message: $t('src.components.ConversitonContainer.notification.answer_cancelled'),
        position: 'top',
      });
    }
  } catch (error) {
    console.error('取消提问失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.ConversitonContainer.notification.cancel_failed'),
      position: 'top',
    });
  }
};

// 处理输入框回车事件
const handleInputEnter = async (event: KeyboardEvent) => {
  // 如果正在进行中文输入法组合输入，不触发发送事件
  if (isComposing.value) {
    return;
  }

  // 如果按下了 Shift + Enter，允许换行，不提交
  if (event.shiftKey) {
    // 不阻止默认行为，允许换行
    return;
  }

  // 阻止默认行为并触发发送
  event.preventDefault();
  // 前端只负责触发发送，所有业务逻辑交给 LLM Store 处理
  await llmStore.send();
};

// 处理输入变化事件
const handleInputChange = () => {
  // 如果有选择知识库，可以在这里添加实时搜索逻辑
  // 目前暂时不实现实时搜索，避免过多请求
};

// 处理中断对话
const handleAbortConversation = () => {
  try {
    llmStore.abortConversation();
    $q.notify({
      type: 'info',
      message: $t('src.components.ConversitonContainer.notification.conversation_aborted'),
      position: 'top',
    });
  } catch (error) {
    console.error('中断对话失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.ConversitonContainer.notification.abort_failed'),
      position: 'top',
    });
  }
};

// 滚动相关方法
const checkIfNearBottom = () => {
  if (!scrollArea.value) return false;

  try {
    // 使用Quasar QScrollArea的getScrollPosition方法获取当前滚动位置
    const scrollPosition = scrollArea.value.getScrollPosition();
    const scrollTarget = scrollArea.value.getScrollTarget();

    // 获取容器高度和总滚动高度
    const containerHeight = scrollTarget.clientHeight;
    const scrollHeight = scrollTarget.scrollHeight;

    // 计算距离底部的距离
    const distanceFromBottom = scrollHeight - (scrollPosition.top + containerHeight);

    return distanceFromBottom <= scrollThreshold;
  } catch (error) {
    console.warn('检查滚动位置失败:', error);
    return false;
  }
};

const scrollToBottom = (duration: number = 150) => {
  if (!scrollArea.value) return;
  void nextTick(() => {
    try {
      // 根据Quasar文档，获取滚动目标和最大滚动高度
      const scrollTarget = scrollArea.value.getScrollTarget();
      const maxScrollHeight = scrollTarget.scrollHeight - scrollTarget.clientHeight;

      // 使用setScrollPosition方法滚动到底部
      // 参数：axis（轴向），offset（偏移量），duration（动画时长，可选）
      scrollArea.value.setScrollPosition('vertical', maxScrollHeight, duration);
    } catch (error) {
      console.warn('滚动到底部失败:', error);
    }
  });
};

const handleScroll = () => {
  // 清除之前的超时，重新设置
  if (userScrollTimeout) {
    clearTimeout(userScrollTimeout);
  }

  // 设置超时来检测用户滚动行为
  userScrollTimeout = setTimeout(() => {
    // 检查距离底部的位置
    const nearBottom = checkIfNearBottom();

    if (nearBottom) {
      // 用户滚动到底部附近（距离底部<=50px），启用自动滚动
      if (!isAutoScrollEnabled.value) {
        isAutoScrollEnabled.value = true;
        console.log('🔄 [滚动] 用户滚动到底部附近，启用自动滚动');
      }
    } else {
      // 用户滚动离开底部（距离底部>50px），禁用自动滚动
      if (isAutoScrollEnabled.value) {
        isAutoScrollEnabled.value = false;
        console.log('🔄 [滚动] 用户滚动离开底部，禁用自动滚动');
      }
    }
  }, 100);
};

watch(
  () => llmStore.messages,
  async () => {
    await nextTick();
    scrollToBottom(0);
  },
  { immediate: true },
);

// 组件销毁时清理定时器
onUnmounted(() => {
  stopAutoScrollTimer();
});

// ==================== 附加内容UI方法 ====================

/**
 * 获取附加内容的标签文本
 */
const getAttachmentLabel = (attachment: Attachment) => {
  // console.log('🔍 [附加内容] 获取附加内容的标签文本:', attachment);
  return attachment.name;
};

/**
 * 获取附加内容的图标
 */
const getAttachmentIcon = (attachment: Attachment) => {
  switch (attachment.type) {
    case 'document':
      return 'mdi-file-document';
    case 'image':
      return 'mdi-image';
    case 'file':
      return 'mdi-file';
    case 'text_snippet':
      return 'mdi-text-box';
    default:
      return 'mdi-attachment';
  }
};

/**
 * 获取附加内容的颜色
 */
const getAttachmentColor = (attachment: Attachment) => {
  switch (attachment.type) {
    case 'document':
      return 'blue';
    case 'image':
      return 'green';
    case 'file':
      return 'orange';
    case 'text_snippet':
      return 'purple';
    default:
      return 'grey';
  }
};

/**
 * 获取附加内容的提示文本
 */
const getAttachmentTooltip = (attachment: Attachment) => {
  switch (attachment.type) {
    case 'document': {
      return `${$t('src.components.ConversitonContainer.tooltip.document')}: ${attachment.title} (ID: ${attachment.documentId})`;
    }
    case 'image': {
      return `${$t('src.components.ConversitonContainer.tooltip.image')}: ${attachment.fileName}${attachment.size ? ` (${(attachment.size / 1024).toFixed(1)}KB)` : ''}`;
    }
    case 'file': {
      return `${$t('src.components.ConversitonContainer.tooltip.file')}: ${attachment.fileName}${attachment.size ? ` (${(attachment.size / 1024).toFixed(1)}KB)` : ''}`;
    }
    case 'text_snippet': {
      const preview = attachment.content.substring(0, 6);
      return `${$t('src.components.ConversitonContainer.tooltip.text_snippet')}: ${preview}${attachment.content.length > 6 ? '...' : ''}`;
    }
    default: {
      // 这种情况理论上不应该发生，因为所有类型都已处理
      const _exhaustiveCheck: never = attachment;
      return _exhaustiveCheck;
    }
  }
};

// ==================== 选中文本UI方法 ====================

/**
 * 获取选中文本的预览
 */
const getSelectedTextPreview = () => {
  if (!llmStore.currentSelectedText) return '';
  const content = llmStore.currentSelectedText.content;
  return content.length > 6 ? content.substring(0, 6) + '...' : content;
};

/**
 * 获取选中文本的提示
 */
const getSelectedTextTooltip = () => {
  if (!llmStore.currentSelectedText) return '';
  const { content, documentId, position } = llmStore.currentSelectedText;
  let tooltip = `${content}`;
  if (documentId) {
    tooltip += `\n${$t('src.components.ConversitonContainer.tooltip.document_id')}: ${documentId}`;
  }
  if (position) {
    tooltip += `\n${$t('src.components.ConversitonContainer.tooltip.position')}: ${position.from}-${position.to}`;
  }
  return tooltip;
};

// ==================== 附加内容处理方法 ====================

/**
 * 处理文件上传
 */
const handleFileUpload = () => {
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '*/*';
  input.multiple = true;

  input.onchange = (event) => {
    const files = (event.target as HTMLInputElement).files;
    if (files) {
      Array.from(files).forEach((file) => {
        // 暂时只记录文件信息，不解析内容
        const attachmentId = llmStore.addFileAttachment(
          file.name, // 暂时用文件名作为路径
          file.name,
          {
            size: file.size,
            mimeType: file.type,
            extension: file.name.split('.').pop() || '',
          },
        );

        $q.notify({
          type: 'positive',
          message: $t('src.components.ConversitonContainer.notification.file_added', {
            fileName: file.name,
          }),
          position: 'top',
        });

        console.log('📎 [附加内容] 文件已添加:', attachmentId, file.name);
      });
    }
  };

  input.click();
};

/**
 * 处理图片上传
 */
const handleImageUpload = () => {
  // 创建图片输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  input.multiple = true;

  input.onchange = (event) => {
    const files = (event.target as HTMLInputElement).files;
    if (files) {
      Array.from(files).forEach((file) => {
        // 创建临时URL用于预览
        const src = URL.createObjectURL(file);

        // 暂时只记录图片信息，不解析内容
        const attachmentId = llmStore.addImageAttachment(src, file.name, {
          size: file.size,
          mimeType: file.type,
        });

        $q.notify({
          type: 'positive',
          message: $t('src.components.ConversitonContainer.notification.image_added', {
            fileName: file.name,
          }),
          position: 'top',
        });

        console.log('🖼️ [附加内容] 图片已添加:', attachmentId, file.name);
      });
    }
  };

  input.click();
};

/**
 * 处理剪贴板粘贴
 */
const handlePasteFromClipboard = async () => {
  try {
    const clipboardItems = await navigator.clipboard.read();

    for (const clipboardItem of clipboardItems) {
      // 处理图片
      for (const type of clipboardItem.types) {
        if (type.startsWith('image/')) {
          const blob = await clipboardItem.getType(type);
          const src = URL.createObjectURL(blob);
          const fileName = `剪贴板图片_${Date.now()}.${type.split('/')[1]}`;

          const attachmentId = llmStore.addImageAttachment(src, fileName, {
            size: blob.size,
            mimeType: type,
          });

          $q.notify({
            type: 'positive',
            message: $t('src.components.ConversitonContainer.notification.clipboard_image_added'),
            position: 'top',
          });

          console.log('📋🖼️ [附加内容] 剪贴板图片已添加:', attachmentId);
          return;
        }
      }
    }

    // 处理文本
    const text = await navigator.clipboard.readText();
    if (text.trim()) {
      const attachmentId = llmStore.addTextSnippetAttachment(text, { type: 'clipboard' });

      $q.notify({
        type: 'positive',
        message: $t('src.components.ConversitonContainer.notification.clipboard_text_added'),
        position: 'top',
      });

      console.log('📋📝 [附加内容] 剪贴板文本已添加:', attachmentId);
    } else {
      $q.notify({
        type: 'warning',
        message: $t('src.components.ConversitonContainer.notification.clipboard_empty'),
        position: 'top',
      });
    }
  } catch (error) {
    console.error('❌ [附加内容] 读取剪贴板失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.ConversitonContainer.notification.clipboard_read_failed'),
      position: 'top',
    });
  }
};

/**
 * 处理键盘事件
 */
const handleKeyDown = (event: KeyboardEvent) => {
  // Ctrl+V 或 Cmd+V 粘贴剪贴板内容
  if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
    // 检查是否在输入框中，如果是则不拦截
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    ) {
      return;
    }

    event.preventDefault();
    void handlePasteFromClipboard();
  }
};

const focusDocument = computed(() => {
  return docStore.getFocusedDocument();
});

watch(
  focusDocument,
  (newFocusDocument, oldFocusDocument) => {
    if (oldFocusDocument) {
      const idx = llmStore.relatedDocuments.findIndex((doc) => doc.id === oldFocusDocument.id);
      const isAddByUser = llmStore.relatedDocumentsAddByUser.findIndex(
        (doc) => doc.id === oldFocusDocument.id,
      );
      if (idx !== -1 && isAddByUser === -1) {
        llmStore.relatedDocuments.splice(idx, 1);
      }
    }
    if (newFocusDocument) {
      void llmStore.setCurrentDocument(newFocusDocument);
    }
  },
  { immediate: true },
);

onMounted(async () => {
  llmStore.waittingLlm = false;
  // 初始化设置和数据
  await llmStore.initSettings();
  await nextTick();

  // 加载保存的提示词偏好
  const savedPrompt = localStorage.getItem('inkcop_chat_prompt');
  if (savedPrompt && uiStore.perferences?.prompt?.chat) {
    uiStore.perferences.prompt.chat.selected = savedPrompt;
  }

  isAutoScrollEnabled.value = true;

  // 如果当前正在加载，启动自动滚动定时器
  if (llmStore.loading) {
    console.log('🔄 [滚动] 初始化时正在加载，启动自动滚动定时器');
    startAutoScrollTimer();
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  stopAutoScrollTimer();
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown);
});
</script>
