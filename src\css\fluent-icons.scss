// Fluent System Icons for Quasar QIcon Component
// 适配 QIcon 组件的 Fluent System Icons 样式

// 字体定义
@font-face {
  font-family: 'FluentSystemIcons-Regular';
  src:
    url('../fonts/FluentSystemIcons-Regular.woff2') format('woff2'),
    url('../fonts/FluentSystemIcons-Regular.woff') format('woff'),
    url('../fonts/FluentSystemIcons-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'FluentSystemIcons-Light';
  src:
    url('../fonts/FluentSystemIcons-Light.woff2') format('woff2'),
    url('../fonts/FluentSystemIcons-Light.woff') format('woff'),
    url('../fonts/FluentSystemIcons-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'FluentSystemIcons-Filled';
  src:
    url('../fonts/FluentSystemIcons-Filled.woff2') format('woff2'),
    url('../fonts/FluentSystemIcons-Filled.woff') format('woff'),
    url('../fonts/FluentSystemIcons-Filled.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'FluentSystemIcons-Resizable';
  src:
    url('../fonts/FluentSystemIcons-Resizable.woff2') format('woff2'),
    url('../fonts/FluentSystemIcons-Resizable.woff') format('woff'),
    url('../fonts/FluentSystemIcons-Resizable.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

// QIcon 组件基础样式
.q-icon {
  &.fluent-icon {
    font-family: 'FluentSystemIcons-Regular', sans-serif;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    // 确保内部的 span 元素继承颜色
    span.fluent-icon {
      color: inherit;
      font-family: inherit;
      font-size: inherit;
    }

    // 变体支持
    &.fluent-light {
      font-family: 'FluentSystemIcons-Light', sans-serif;
      font-weight: 300;

      span.fluent-icon {
        font-family: inherit;
      }
    }

    &.fluent-filled {
      font-family: 'FluentSystemIcons-Filled', sans-serif;
      font-weight: 900;

      span.fluent-icon {
        font-family: inherit;
      }
    }

    &.fluent-resizable {
      font-family: 'FluentSystemIcons-Resizable', sans-serif;

      span.fluent-icon {
        font-family: inherit;
      }
    }
  }
}

// 为 QIcon 组件创建 Fluent 图标类
// 这些类将通过 JavaScript 动态生成内容
.fluent-icon {
  &::before {
    font-family: inherit;
    font-style: normal;
    font-weight: inherit;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

// 支持不同尺寸的图标
.q-icon.fluent-icon {
  &.text-xs {
    font-size: 12px;
  }
  &.text-sm {
    font-size: 14px;
  }
  &.text-base {
    font-size: 16px;
  }
  &.text-lg {
    font-size: 18px;
  }
  &.text-xl {
    font-size: 20px;
  }
  &.text-2xl {
    font-size: 24px;
  }
  &.text-3xl {
    font-size: 28px;
  }
  &.text-4xl {
    font-size: 32px;
  }
}

// 主题适配
.body--dark .q-icon.fluent-icon {
  // 暗色主题下的图标样式调整
  opacity: 0.87;
}

.body--light .q-icon.fluent-icon {
  // 亮色主题下的图标样式调整
  opacity: 0.87;
}
