# 脚本依赖复制修正记录

**日期**: 2025-01-26 15:24  
**修正内容**: win-prod.ps1、create-installer.ps1 脚本依赖复制问题

## 问题分析

### 发现的问题
1. **QWindowKit DLL 路径不一致**
   - `win-dev.ps1`: `third-party\qwindowkit\build-qwk\out-amd64-Release\bin` ✅
   - `win-prod.ps1`: `third-party\qwindowkit\build\out-amd64-Release\bin` ❌
   - `create-installer.ps1`: `third-party\qwindowkit\build\out-amd64-Release\bin` ❌

2. **依赖复制完整性差异**
   - `win-prod.ps1` 缺少 CUDA 运行时库复制
   - `win-prod.ps1` 缺少多路径 ObjectBox DLL 检查
   - `create-installer.ps1` 已有完善的依赖复制，只需修正路径

## 修正内容

### 1. win-prod.ps1 修正

#### QWindowKit 路径修正
```powershell
# 修正前
$qwkDllPath = "..\third-party\qwindowkit\build\out-amd64-Release\bin"

# 修正后  
$qwkDllPath = "..\third-party\qwindowkit\build-qwk\out-amd64-Release\bin"
```

#### ObjectBox DLL 多路径检查
```powershell
# 修正前：单一路径
$objectboxDll = "..\third-party\objectbox-windows\lib\objectbox.dll"

# 修正后：多路径检查
$objectboxDllPaths = @(
    "..\third-party\objectbox-windows\lib\objectbox.dll",
    "..\third-party\objectbox-c\lib\objectbox.dll", 
    "third-party\objectbox-windows\lib\objectbox.dll",
    "third-party\objectbox-c\lib\objectbox.dll"
)
```

#### 添加 CUDA 运行时库复制
- 支持 CUDA 12.0 到 12.9 版本检测
- 复制必要的 CUDA DLL：
  - `cudart64_12.dll`
  - `cublas64_12.dll`
  - `cublasLt64_12.dll`
  - `curand64_10.dll`
  - `cusparse64_12.dll`

### 2. create-installer.ps1 修正

#### QWindowKit 路径修正
```powershell
# 修正前
$qwkDllPath = "..\third-party\qwindowkit\build\out-amd64-Release\bin"

# 修正后
$qwkDllPath = "..\third-party\qwindowkit\build-qwk\out-amd64-Release\bin"
```

## 修正后的依赖复制统一标准

### 所有脚本现在都包含：

1. **ObjectBox DLL**
   - 多路径检查，确保找到正确的 DLL
   - 支持不同的构建配置

2. **llama.cpp DLL**
   - 5个核心 DLL 文件
   - CUDA 运行时库支持
   - 版本兼容性检查

3. **QWindowKit DLL**
   - 统一路径：`third-party\qwindowkit\build-qwk\out-amd64-Release\bin`
   - 自动复制所有相关 DLL

## 验证结果

- ✅ 所有脚本使用相同的 QWindowKit 路径
- ✅ win-prod.ps1 现在包含完整的依赖复制逻辑
- ✅ create-installer.ps1 路径已修正
- ✅ 代码检查无错误

## 影响

这次修正确保了：
1. 生产构建和安装包构建使用相同的依赖复制逻辑
2. 所有必要的 DLL 都能被正确找到和复制
3. CUDA 支持在所有构建类型中都可用
4. 构建过程更加稳定和可靠
