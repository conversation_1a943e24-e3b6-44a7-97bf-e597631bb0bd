# 多层次语义切割功能指南

## 概述

本文档介绍了InkCop知识库系统中新实现的多层次语义切割功能，这是一个智能的文本分割系统，能够根据文档类型和语义内容进行高质量的文本切割。

## 功能特点

### 🎯 多层次切割架构

1. **第一层：文档结构切割**

   - 自动检测文档类型（Markdown、代码、学术论文等）
   - 基于文档特有的结构进行初步切割
   - 支持标题、章节、函数等结构单元

2. **第二层：段落级切割**

   - 识别自然段落边界
   - 保持段落的完整性
   - 控制chunk的合理大小

3. **第三层：语义相似度切割**
   - 使用向量相似度计算
   - 确保语义相关的内容保持在同一chunk中
   - 避免语义断裂

### 🤖 智能文档类型检测

系统能够自动识别以下文档类型：

- **Markdown文档**：检测标题、代码块、链接等特征
- **代码文档**：识别函数定义、类定义、导入语句等
- **学术论文**：检测摘要、引言、方法、结论等章节
- **手册/教程**：识别步骤、指南等结构
- **普通文本**：使用通用的段落切割策略

## 使用方法

### 1. 基本使用

在添加文档到知识库时，系统会自动使用多层次语义切割：

```cpp
// 添加文档（自动使用语义切割）
QString result = knowledgeApi->addDocumentToKnowledgeBase(
    kbId,
    "文档标题",
    "文档内容",
    "markdown"  // 指定文档类型，或留空自动检测
);
```

### 2. 测试切割效果

使用测试接口查看切割效果：

```cpp
// 测试语义切割
QString testResult = knowledgeApi->testSemanticSplit(
    "要测试的文本内容",
    "markdown"  // 文档类型
);
```

### 3. 重新生成chunks

对现有文档重新应用语义切割：

```cpp
// 重新生成文档的chunks
QString result = knowledgeApi->regenerateDocumentChunks(docId);
```

## 配置参数

### 切割参数

- **targetSize**: 目标chunk大小（默认400字符）
- **semanticThreshold**: 语义相似度阈值（默认0.7）
- **maxChunkSize**: 段落级最大chunk大小（默认800字符）
- **overlapSize**: chunk重叠大小（默认50字符）

### 文档类型配置

支持的文档类型：

- `markdown`: Markdown文档
- `code`: 程序代码
- `academic`: 学术论文
- `manual`: 手册/教程
- `text`: 普通文本（默认）

## 切割策略详解

### Markdown文档切割

```markdown
# 主标题

这里是主标题下的内容...

## 子标题1

这里是子标题1的内容...

## 子标题2

这里是子标题2的内容...
```

切割结果：

- Chunk1: 主标题及其内容
- Chunk2: 子标题1及其内容
- Chunk3: 子标题2及其内容

### 代码文档切割

```javascript
function getData() {
  // 函数实现
  return data;
}

class MyClass {
  constructor() {
    // 构造函数
  }
}
```

切割结果：

- Chunk1: getData函数完整定义
- Chunk2: MyClass类完整定义

### 语义相似度切割

系统会计算相邻文本段的语义相似度：

- 相似度 > 0.7：合并到同一chunk
- 相似度 < 0.7：分割为不同chunk

## 性能优化

### 降级机制

如果语义切割过程中出现异常，系统会自动降级到简单的规则切割，确保系统稳定性。

### 缓存机制

向量计算结果会被缓存，提高重复处理的效率。

## 最佳实践

### 1. 文档类型选择

- 明确指定文档类型可以获得更好的切割效果
- 让系统自动检测适用于混合内容

### 2. 内容准备

- 确保文档结构清晰（使用标题、段落分隔等）
- 避免过长的单一段落
- 代码文档建议保持良好的格式化

### 3. 参数调优

- 对于技术文档，可以适当降低语义阈值（0.6-0.7）
- 对于叙述性文档，可以提高语义阈值（0.7-0.8）
- 根据具体需求调整chunk大小

## 监控和调试

### 日志输出

系统会输出详细的切割过程日志：

```
🎯 [KnowledgeApi] 开始多层次语义切割，文档类型:markdown，长度:2048
📋 [KnowledgeApi] 自动检测文档类型:markdown
🏗️ [KnowledgeApi] 结构化切割得到3个部分
📄 [KnowledgeApi] 段落切割得到5个段落块
🧠 [KnowledgeApi] 语义切割得到8个语义块
🔗 [KnowledgeApi] 添加重叠完成，重叠大小:50
🎉 [KnowledgeApi] 多层次切割完成，总计8个chunks
```

### 测试接口返回格式

```json
{
  "success": true,
  "original_length": 2048,
  "document_type": "markdown",
  "total_chunks": 8,
  "chunks": [
    {
      "index": 1,
      "length": 245,
      "content_preview": "# 主标题\n这里是主标题的内容...",
      "word_count": 42
    }
  ],
  "statistics": {
    "average_chunk_length": 256.0,
    "min_chunk_length": 180,
    "max_chunk_length": 320
  }
}
```

## 未来增强

### 计划中的功能

1. **更多文档类型支持**：PDF、HTML、XML等
2. **自定义切割规则**：用户定义的切割策略
3. **机器学习优化**：基于使用反馈的自动优化
4. **并行处理**：提高大文档的处理速度

### 性能目标

- 处理速度：1000字符/秒
- 内存使用：< 100MB for 10MB文档
- 准确率：> 90%的语义完整性保持

## 故障排除

### 常见问题

1. **切割结果过多/过少**

   - 调整目标chunk大小
   - 检查文档类型检测是否正确

2. **语义切割不准确**

   - 调整语义相似度阈值
   - 检查文档内容质量

3. **处理速度慢**
   - 检查文档大小
   - 考虑预处理优化

### 错误代码

- `SEMANTIC_SPLIT_FAILED`: 语义切割失败，已降级
- `DOCUMENT_TYPE_DETECTION_FAILED`: 文档类型检测失败
- `EMBEDDING_GENERATION_FAILED`: 向量生成失败

## 联系支持

如有问题或建议，请联系开发团队。
