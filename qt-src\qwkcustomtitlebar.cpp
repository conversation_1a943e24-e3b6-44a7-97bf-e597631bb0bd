#include "qwkcustomtitlebar.h"
#include <QIcon>
#include <QStyle>
#include <QApplication>
#include <QDesktopServices>
#include <QUrl>
#include <QDebug>
#include <QPixmap>
#include <QFile>

QWKCustomTitleBar::QWKCustomTitleBar(QWidget *parent)
    : QWidget(parent), m_layout(nullptr), m_iconLabel(nullptr), m_titleLabel(nullptr), m_spacer(nullptr), m_minimizeButton(nullptr), m_maximizeButton(nullptr), m_closeButton(nullptr), m_leftDrawerBtn(nullptr), m_rightDrawerBtn(nullptr), m_themeBtn(nullptr), m_reloadBtn(nullptr), m_devToolsBtn(nullptr), m_toggleModeBtn(nullptr), m_micaTestBtn(nullptr), m_isDarkTheme(false), m_isMaximized(false), m_isLeftDrawerOpened(false), m_isRightDrawerOpened(false), m_barHeight(40), m_micaEffectEnabled(false)
{
    setupUI();
    setupConnections();

    // 设置对象名称用于样式表
    setObjectName("qwkCustomTitleBar");

    // 安装事件过滤器
    installEventFilter(this);

    // 应用默认主题
    applyTheme();

    // 初始化抽屉图标
    updateDrawerButtonIcons();

    // 初始化工具按钮图标
    updateToolButtonIcons();
}

QWKCustomTitleBar::~QWKCustomTitleBar()
{
}

void QWKCustomTitleBar::setupUI()
{
    setFixedHeight(m_barHeight);
    setMouseTracking(true);

    m_layout = new QHBoxLayout(this);
    m_layout->setContentsMargins(16, 0, 0, 0);
    m_layout->setSpacing(0);

    // 应用图标
    m_iconLabel = new QLabel(this);
    m_iconLabel->setFixedSize(16, 16);
    m_iconLabel->setScaledContents(true);
    m_layout->addWidget(m_iconLabel);

    // 标题标签
    m_titleLabel = new QLabel("inkCop", this);
    m_titleLabel->setObjectName("titleLabel");
    m_titleLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_layout->addWidget(m_titleLabel, 1);
    m_layout->addSpacing(10);

    // 创建工具按钮
    m_leftDrawerBtn = createToolButton("drawer", "toggle left drawer");
    m_leftDrawerBtn->setObjectName("leftDrawerBtn");
    m_leftDrawerBtn->setAttribute(Qt::WA_TransparentForMouseEvents, false);
    m_layout->addWidget(m_leftDrawerBtn);

    m_rightDrawerBtn = createToolButton("drawer", "toggle right drawer");
    m_rightDrawerBtn->setObjectName("rightDrawerBtn");
    m_rightDrawerBtn->setAttribute(Qt::WA_TransparentForMouseEvents, false);
    m_layout->addWidget(m_rightDrawerBtn);

// 开发模式专用按钮 - 只在开发模式下显示
#ifdef DEV_MODE
    m_reloadBtn = createToolButton("refresh", "reload (F5)");
    m_reloadBtn->setObjectName("reloadBtn");
    m_reloadBtn->setAttribute(Qt::WA_TransparentForMouseEvents, false);
    m_layout->addWidget(m_reloadBtn);

    m_devToolsBtn = createToolButton("code", "developer tools (F12)");
    m_devToolsBtn->setObjectName("devToolsBtn");
    m_devToolsBtn->setAttribute(Qt::WA_TransparentForMouseEvents, false);
    m_layout->addWidget(m_devToolsBtn);

    m_toggleModeBtn = createToolButton("exchange", "toggle mode (Ctrl+T)");
    m_toggleModeBtn->setObjectName("toggleModeBtn");
    m_toggleModeBtn->setAttribute(Qt::WA_TransparentForMouseEvents, false);
    m_layout->addWidget(m_toggleModeBtn);

    m_micaTestBtn = createToolButton("star", "test mica effect");
    m_micaTestBtn->setObjectName("micaTestBtn");
    m_micaTestBtn->setAttribute(Qt::WA_TransparentForMouseEvents, false);
    m_micaTestBtn->setVisible(false); // 隐藏Mica效果切换按钮
    m_layout->addWidget(m_micaTestBtn);
#else
    // 生产模式下这些按钮为空指针
    m_reloadBtn = nullptr;
    m_devToolsBtn = nullptr;
    m_toggleModeBtn = nullptr;
    m_micaTestBtn = nullptr;
#endif

    // 创建主题切换按钮
    m_themeBtn = createToolButton("theme-light-dark", "切换主题");
    m_themeBtn->setObjectName("themeBtn");
    m_themeBtn->setAttribute(Qt::WA_TransparentForMouseEvents, false);
    m_themeBtn->setVisible(false); // 隐藏主题切换按钮，主题控制现在在前端设置中完成
    m_layout->addWidget(m_themeBtn);

    // 弹性空间
    m_spacer = new QWidget(this);
    m_spacer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    m_layout->addWidget(m_spacer);

    // 窗口控制按钮容器
    QWidget *buttonContainer = new QWidget(this);
    buttonContainer->setFixedHeight(m_barHeight);
    QHBoxLayout *buttonLayout = new QHBoxLayout(buttonContainer);
    buttonLayout->setContentsMargins(0, 0, 0, 0);
    buttonLayout->setSpacing(0);

    // 最小化按钮
    m_minimizeButton = new QPushButton(this);
    m_minimizeButton->setObjectName("minimizeButton");
    m_minimizeButton->setFixedSize(46, m_barHeight);
    m_minimizeButton->setToolTip("最小化");
    buttonLayout->addWidget(m_minimizeButton);

    // 最大化/还原按钮
    m_maximizeButton = new QPushButton(this);
    m_maximizeButton->setObjectName("maximizeButton");
    m_maximizeButton->setFixedSize(46, m_barHeight);
    m_maximizeButton->setToolTip("最大化");
    buttonLayout->addWidget(m_maximizeButton);

    // 关闭按钮
    m_closeButton = new QPushButton(this);
    m_closeButton->setObjectName("closeButton");
    m_closeButton->setFixedSize(46, m_barHeight);
    m_closeButton->setToolTip("关闭");
    buttonLayout->addWidget(m_closeButton);

    m_layout->addWidget(buttonContainer);

    // 加载默认应用图标
    loadDefaultIcon();
}

void QWKCustomTitleBar::setupConnections()
{
    connect(m_minimizeButton, &QPushButton::clicked, this, &QWKCustomTitleBar::onMinimizeClicked);
    connect(m_maximizeButton, &QPushButton::clicked, this, &QWKCustomTitleBar::onMaximizeClicked);
    connect(m_closeButton, &QPushButton::clicked, this, &QWKCustomTitleBar::onCloseClicked);

    // 为关闭按钮添加hover事件处理
    m_closeButton->installEventFilter(this);

    // 工具按钮连接
    connect(m_leftDrawerBtn, &QPushButton::clicked, this, &QWKCustomTitleBar::onLeftDrawerClicked);
    connect(m_rightDrawerBtn, &QPushButton::clicked, this, &QWKCustomTitleBar::onRightDrawerClicked);
    connect(m_themeBtn, &QPushButton::clicked, this, &QWKCustomTitleBar::onThemeToggleClicked);

    // 开发模式专用按钮连接 - 只在按钮存在时连接
    if (m_reloadBtn)
    {
        connect(m_reloadBtn, &QPushButton::clicked, this, &QWKCustomTitleBar::onReloadClicked);
    }
    if (m_devToolsBtn)
    {
        connect(m_devToolsBtn, &QPushButton::clicked, this, &QWKCustomTitleBar::onDevToolsClicked);
    }
    if (m_toggleModeBtn)
    {
        connect(m_toggleModeBtn, &QPushButton::clicked, this, &QWKCustomTitleBar::onModeToggleClicked);
    }
    if (m_micaTestBtn)
    {
        connect(m_micaTestBtn, &QPushButton::clicked, this, &QWKCustomTitleBar::onMicaTestClicked);
    }
}

void QWKCustomTitleBar::setTitle(const QString &title)
{
    m_titleLabel->setText(title);
}

void QWKCustomTitleBar::setWindowIcon(const QIcon &icon)
{
    if (!icon.isNull())
    {
        QPixmap pixmap = icon.pixmap(16, 16);
        m_iconLabel->setPixmap(pixmap);
    }
}

void QWKCustomTitleBar::updateMaximizeButton(bool isMaximized)
{
    m_isMaximized = isMaximized;
    if (isMaximized)
    {
        m_maximizeButton->setToolTip("还原");
    }
    else
    {
        m_maximizeButton->setToolTip("最大化");
    }
    updateButtonIcons();
}

void QWKCustomTitleBar::updateTheme(bool isDarkTheme)
{
    m_isDarkTheme = isDarkTheme;
    applyTheme();
}

void QWKCustomTitleBar::updateLeftDrawerState(bool isOpened)
{
    m_isLeftDrawerOpened = isOpened;
    updateDrawerButtonIcons();
}

void QWKCustomTitleBar::updateRightDrawerState(bool isOpened)
{
    m_isRightDrawerOpened = isOpened;
    updateDrawerButtonIcons();
}

void QWKCustomTitleBar::setBarHeight(int height)
{
    m_barHeight = height;
    setFixedHeight(height);

    if (m_minimizeButton)
        m_minimizeButton->setFixedHeight(height);
    if (m_maximizeButton)
        m_maximizeButton->setFixedHeight(height);
    if (m_closeButton)
        m_closeButton->setFixedHeight(height);
}

void QWKCustomTitleBar::setMicaEffectEnabled(bool enabled)
{
    if (m_micaEffectEnabled != enabled)
    {
        m_micaEffectEnabled = enabled;
        qDebug() << "QWKCustomTitleBar: Mica effect enabled changed to:" << enabled;
        // 重新应用主题以更新背景
        applyTheme();
    }
}

bool QWKCustomTitleBar::isMicaEffectEnabled() const
{
    return m_micaEffectEnabled;
}

void QWKCustomTitleBar::applyTheme()
{
    // 根据是否使用Mica效果选择背景色和边框色
    QString bgColor;
    QString borderColor;

    if (m_micaEffectEnabled)
    {
        // 使用透明背景和边框让Mica效果显示
        bgColor = "transparent";
        borderColor = "transparent"; // 边框也要透明
        qDebug() << "QWKCustomTitleBar: Using transparent background for Mica effect";
    }
    else
    {
        // 使用实色背景
        bgColor = m_isDarkTheme ? "#1e1e1e" : "#ffffff";
        borderColor = m_isDarkTheme ? "#333333" : "#e0e0e0";
    }
    QString textColor = m_isDarkTheme ? "#ffffff" : "#333333";
    QString buttonHoverColor = m_isDarkTheme ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.15)";
    QString buttonPressedColor = m_isDarkTheme ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.25)";
    QString closeHoverColor = "#dc3545";
    QString closePressedColor = "#c82333";

    QString styleSheet = QString(
                             "QWKCustomTitleBar {"
                             "    background-color: %1;"
                             "    border-bottom: 1px solid %2;"
                             "}"

                             "QLabel#titleLabel {"
                             "    color: %3;"
                             "    font-size: 14px;"
                             "    font-weight: 500;"
                             "    padding-left: 8px;"
                             "    background-color: transparent;"
                             "}"

                             "QPushButton {"
                             "    background-color: transparent;"
                             "    border: none;"
                             "    border-radius: 0px;"
                             "    margin: 0px;"
                             "    padding: 0px;"
                             "}"

                             "QPushButton:hover {"
                             "    background-color: %4;"
                             "}"

                             "QPushButton:pressed {"
                             "    background-color: %5;"
                             "}"

                             "QPushButton#closeButton:hover {"
                             "    background-color: %6;"
                             "}"

                             "QPushButton#closeButton:pressed {"
                             "    background-color: %7;"
                             "}"

                             "QToolTip {"
                             "    background-color: #ffffff;"
                             "    color: #333333;"
                             "    padding: 2px;"
                             "}")
                             .arg(bgColor)
                             .arg(borderColor)
                             .arg(textColor)
                             .arg(buttonHoverColor)
                             .arg(buttonPressedColor)
                             .arg(closeHoverColor)
                             .arg(closePressedColor);

    setStyleSheet(styleSheet);

    // 更新工具按钮主题
    applyToolButtonTheme(m_leftDrawerBtn, m_isDarkTheme);
    applyToolButtonTheme(m_rightDrawerBtn, m_isDarkTheme);
    applyToolButtonTheme(m_themeBtn, m_isDarkTheme);

    // 更新主题按钮图标
    if (m_themeBtn)
    {
        // 亮色主题时显示太阳图标，暗色主题时显示月亮图标
        m_themeBtn->setText(m_isDarkTheme ? "🌣" : "☪︎");
    }

    // 只为存在的开发模式按钮应用主题
    if (m_reloadBtn)
    {
        applyToolButtonTheme(m_reloadBtn, m_isDarkTheme);
    }
    if (m_devToolsBtn)
    {
        applyToolButtonTheme(m_devToolsBtn, m_isDarkTheme);
    }
    if (m_toggleModeBtn)
    {
        applyToolButtonTheme(m_toggleModeBtn, m_isDarkTheme);
    }
    if (m_micaTestBtn)
    {
        applyToolButtonTheme(m_micaTestBtn, m_isDarkTheme);
    }

    updateButtonIcons();
    updateDrawerButtonIcons();
    updateToolButtonIcons();

    // 强制刷新UI以确保主题立即生效
    update();
    repaint();

    // 强制刷新所有子控件
    const QList<QWidget *> children = findChildren<QWidget *>();
    for (QWidget *child : children)
    {
        if (child)
        {
            child->update();
            child->repaint();
        }
    }
}

void QWKCustomTitleBar::updateButtonIcons()
{
    // 根据主题选择对应的 SVG 图标
    QString iconSuffix = m_isDarkTheme ? "_white.svg" : ".svg";

    // 最小化按钮图标
    QString minimizeIconPath = ":/icons/ui/Minimize" + iconSuffix;
    QIcon minimizeIcon(minimizeIconPath);
    m_minimizeButton->setIcon(minimizeIcon);
    m_minimizeButton->setIconSize(QSize(16, 16));

    // 最大化/还原按钮图标
    QString maximizeIconPath;
    if (m_isMaximized)
    {
        maximizeIconPath = ":/icons/ui/Restore" + iconSuffix;
    }
    else
    {
        maximizeIconPath = ":/icons/ui/Maximize" + iconSuffix;
    }
    QIcon maximizeIcon(maximizeIconPath);
    m_maximizeButton->setIcon(maximizeIcon);
    m_maximizeButton->setIconSize(QSize(16, 16));

    // 关闭按钮图标
    QString closeIconPath = ":/icons/ui/Close" + iconSuffix;
    QIcon closeIcon(closeIconPath);
    m_closeButton->setIcon(closeIcon);
    m_closeButton->setIconSize(QSize(16, 16));
}

void QWKCustomTitleBar::updateDrawerButtonIcons()
{
    // 根据主题选择图标后缀
    QString themeSuffix = m_isDarkTheme ? "_dark" : "_light";

    // 左侧抽屉图标 - 根据Qt内部状态
    QString leftDrawerState = m_isLeftDrawerOpened ? "opened" : "closed";
    QString leftDrawerIconPath = QString(":/icons/ui/left_drawer_%1%2.svg").arg(leftDrawerState).arg(themeSuffix);

    if (QFile::exists(leftDrawerIconPath))
    {
        m_leftDrawerBtn->setIcon(QIcon(leftDrawerIconPath));
        m_leftDrawerBtn->setIconSize(QSize(16, 16));
        m_leftDrawerBtn->setText(""); // 清除备用文字
    }
    else
    {
        // 使用Unicode符号作为备用
        m_leftDrawerBtn->setText("◧");
        m_leftDrawerBtn->setIcon(QIcon()); // 清除图标
    }

    // 右侧抽屉图标 - 根据Qt内部状态
    QString rightDrawerState = m_isRightDrawerOpened ? "opened" : "closed";
    QString rightDrawerIconPath = QString(":/icons/ui/right_drawer_%1%2.svg").arg(rightDrawerState).arg(themeSuffix);

    if (QFile::exists(rightDrawerIconPath))
    {
        m_rightDrawerBtn->setIcon(QIcon(rightDrawerIconPath));
        m_rightDrawerBtn->setIconSize(QSize(16, 16));
        m_rightDrawerBtn->setText(""); // 清除备用文字
    }
    else
    {
        // 使用Unicode符号作为备用
        m_rightDrawerBtn->setText("◨");
        m_rightDrawerBtn->setIcon(QIcon()); // 清除图标
    }
}

void QWKCustomTitleBar::syncDrawerIconsFromFrontend()
{
    // 从前端获取真实的抽屉状态并更新图标
    emit requestDrawerStateSync();
}

void QWKCustomTitleBar::updateToolButtonIcons()
{
    // 根据主题选择图标后缀
    QString themeSuffix = m_isDarkTheme ? "_dark" : "_light";

    // 更新刷新按钮图标
    if (m_reloadBtn)
    {
        QString refreshIconPath = QString(":/icons/ui/refresh%1.svg").arg(themeSuffix);
        if (QFile::exists(refreshIconPath))
        {
            m_reloadBtn->setIcon(QIcon(refreshIconPath));
            m_reloadBtn->setIconSize(QSize(16, 16));
            m_reloadBtn->setText("");
        }
        else
        {
            m_reloadBtn->setText("⟳");
            m_reloadBtn->setIcon(QIcon());
        }
    }

    // 更新开发工具按钮图标
    if (m_devToolsBtn)
    {
        QString codeIconPath = QString(":/icons/ui/code%1.svg").arg(themeSuffix);
        if (QFile::exists(codeIconPath))
        {
            m_devToolsBtn->setIcon(QIcon(codeIconPath));
            m_devToolsBtn->setIconSize(QSize(16, 16));
            m_devToolsBtn->setText("");
        }
        else
        {
            m_devToolsBtn->setText("⚙");
            m_devToolsBtn->setIcon(QIcon());
        }
    }

    // 更新模式切换按钮图标
    if (m_toggleModeBtn)
    {
        QString exchangeIconPath = QString(":/icons/ui/exchange%1.svg").arg(themeSuffix);
        if (QFile::exists(exchangeIconPath))
        {
            m_toggleModeBtn->setIcon(QIcon(exchangeIconPath));
            m_toggleModeBtn->setIconSize(QSize(16, 16));
            m_toggleModeBtn->setText("");
        }
        else
        {
            m_toggleModeBtn->setText("⇄");
            m_toggleModeBtn->setIcon(QIcon());
        }
    }

    // 更新Mica测试按钮图标
    if (m_micaTestBtn)
    {
        QString starIconPath = QString(":/icons/ui/star%1.svg").arg(themeSuffix);
        if (QFile::exists(starIconPath))
        {
            m_micaTestBtn->setIcon(QIcon(starIconPath));
            m_micaTestBtn->setIconSize(QSize(16, 16));
            m_micaTestBtn->setText("");
        }
        else
        {
            m_micaTestBtn->setText("★");
            m_micaTestBtn->setIcon(QIcon());
        }
    }

    // 更新主题按钮图标
    if (m_themeBtn)
    {
        QString themeIconPath = QString(":/icons/ui/theme-light-dark%1.svg").arg(themeSuffix);
        if (QFile::exists(themeIconPath))
        {
            m_themeBtn->setIcon(QIcon(themeIconPath));
            m_themeBtn->setIconSize(QSize(16, 16));
            m_themeBtn->setText("");
        }
        else
        {
            m_themeBtn->setText(m_isDarkTheme ? "🌣" : "☪︎");
            m_themeBtn->setIcon(QIcon());
        }
    }
}

bool QWKCustomTitleBar::eventFilter(QObject *obj, QEvent *event)
{
    if (obj == this)
    {
        if (event->type() == QEvent::MouseButtonDblClick)
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            if (mouseEvent->button() == Qt::LeftButton)
            {
                // 双击标题栏最大化/还原窗口
                emit maximizeClicked();
                return true;
            }
        }
    }
    else if (obj == m_closeButton)
    {
        if (event->type() == QEvent::Enter)
        {
            // 鼠标进入关闭按钮时，始终使用白色图标
            QIcon closeIcon(":/icons/ui/Close_white.svg");
            m_closeButton->setIcon(closeIcon);
            m_closeButton->setIconSize(QSize(16, 16));
            return false; // 继续传递事件
        }
        else if (event->type() == QEvent::Leave)
        {
            // 鼠标离开关闭按钮时，恢复原来的图标
            QString iconSuffix = m_isDarkTheme ? "_white.svg" : ".svg";
            QString closeIconPath = ":/icons/ui/Close" + iconSuffix;
            QIcon closeIcon(closeIconPath);
            m_closeButton->setIcon(closeIcon);
            m_closeButton->setIconSize(QSize(16, 16));
            return false; // 继续传递事件
        }
    }
    return QWidget::eventFilter(obj, event);
}

void QWKCustomTitleBar::mouseDoubleClickEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        emit maximizeClicked();
        event->accept();
    }
    else
    {
        QWidget::mouseDoubleClickEvent(event);
    }
}

void QWKCustomTitleBar::onMinimizeClicked()
{
    emit minimizeClicked();
}

void QWKCustomTitleBar::onMaximizeClicked()
{
    emit maximizeClicked();
}

void QWKCustomTitleBar::onCloseClicked()
{
    emit closeClicked();
}

void QWKCustomTitleBar::loadDefaultIcon()
{
    // 尝试从多个位置加载高质量图标，优先使用高分辨率图标
    QStringList iconPaths = {
        // 优先使用高分辨率图标
        ":/icons/icon-256x256.png",
        ":/icons/icon-128x128.png",
        ":/icons/icon-512x512.png",
        ":/logo.png",
        ":/icons/favicon-96x96.png",
        ":/icons/favicon-64x64.png",
        ":/icons/favicon-32x32.png",
        // 备用路径
        QApplication::applicationDirPath() + "/webapp/icons/icon-256x256.png",
        QApplication::applicationDirPath() + "/../dist/spa/icons/icon-256x256.png",
        QApplication::applicationDirPath() + "/../../dist/spa/icons/icon-256x256.png"};

    QPixmap iconPixmap;
    bool iconLoaded = false;
    QString loadedPath;

    for (const QString &path : iconPaths)
    {
        if (QFile::exists(path) || path.startsWith(":/"))
        {
            iconPixmap.load(path);
            if (!iconPixmap.isNull())
            {
                loadedPath = path;
                iconLoaded = true;
                break;
            }
        }
    }

    if (iconLoaded)
    {
        updateAppIcon(iconPixmap);
    }
}

void QWKCustomTitleBar::updateAppIcon(const QPixmap &icon)
{
    if (!icon.isNull())
    {
        // 使用高质量缩放算法，确保图标清晰
        QPixmap scaledIcon = icon.scaled(
            16, 16,
            Qt::KeepAspectRatio,
            Qt::SmoothTransformation);

        // 如果原图标分辨率很高，使用更精细的缩放处理
        if (icon.width() > 32 || icon.height() > 32)
        {
            // 对于高分辨率图标，先缩放到中等尺寸，再缩放到目标尺寸
            QPixmap intermediateIcon = icon.scaled(
                32, 32,
                Qt::KeepAspectRatio,
                Qt::SmoothTransformation);
            scaledIcon = intermediateIcon.scaled(
                16, 16,
                Qt::KeepAspectRatio,
                Qt::SmoothTransformation);
        }

        m_iconLabel->setPixmap(scaledIcon);
    }
}

QPushButton *QWKCustomTitleBar::createToolButton(const QString &iconPath, const QString &tooltip)
{
    QPushButton *button = new QPushButton(this);
    button->setFixedSize(32, 32);
    button->setToolTip(tooltip);
    button->setCursor(Qt::PointingHandCursor);
    button->setFocusPolicy(Qt::NoFocus);
    button->setAttribute(Qt::WA_Hover, true);
    button->setEnabled(true); // 确保按钮是启用的

    // 图标将通过专门的更新方法设置
    // 为抽屉按钮设置临时显示
    if (!iconPath.isEmpty() && iconPath.contains("drawer"))
    {
        button->setText("☰");
    }

    return button;
}

void QWKCustomTitleBar::applyToolButtonTheme(QPushButton *button, bool isDarkTheme)
{
    if (!button)
        return;

    QString bgColor = "transparent";
    QString hoverBgColor = isDarkTheme ? "rgba(255, 255, 255, 0.15)" : "rgba(0, 0, 0, 0.1)";
    QString pressBgColor = isDarkTheme ? "rgba(255, 255, 255, 0.25)" : "rgba(0, 0, 0, 0.2)";
    QString textColor = isDarkTheme ? "#ffffff" : "#333333";

    QString style = QString(
                        "QPushButton {"
                        "    border: none;"
                        "    border-radius: 4px;"
                        "    background-color: %1;"
                        "    color: %2;"
                        "    font-weight: bold;"
                        "    font-size: 14px;"
                        "    margin: 2px 2px;"
                        "    padding: 4px;"
                        "}"
                        "QPushButton:hover {"
                        "    background-color: %3;"
                        "}"
                        "QPushButton:pressed {"
                        "    background-color: %4;"
                        "}"
                        "QToolTip {"
                        "    background-color: #ffffff;"
                        "    color: #333333;"
                        "    padding: 2px;"
                        "}")
                        .arg(bgColor)
                        .arg(textColor)
                        .arg(hoverBgColor)
                        .arg(pressBgColor);

    button->setStyleSheet(style);

    // 确保鼠标指针样式不被样式表覆盖
    button->setCursor(Qt::PointingHandCursor);

    // 强制刷新按钮以确保样式立即生效
    button->update();
    button->repaint();
}

// 槽函数实现
void QWKCustomTitleBar::onLeftDrawerClicked()
{
    emit leftDrawerToggled();
}

void QWKCustomTitleBar::onRightDrawerClicked()
{
    emit rightDrawerToggled();
}

void QWKCustomTitleBar::onThemeToggleClicked()
{
    emit themeToggleRequested();
}

void QWKCustomTitleBar::onReloadClicked()
{
    emit reloadRequested();
}

void QWKCustomTitleBar::onDevToolsClicked()
{
    emit devToolsRequested();
}

void QWKCustomTitleBar::onModeToggleClicked()
{
    emit modeToggleRequested();
}

void QWKCustomTitleBar::onMicaTestClicked()
{
    emit micaTestRequested();
}