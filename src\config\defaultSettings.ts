/**
 * 默认设置配置
 * 集中管理所有设置的默认值，便于维护和管理
 */

import { $t } from 'src/composables/useTrans';

import type {
  AppSettings,
  LlmSettings,
  AutoComplete,
  FloatAgent,
  SearchEngineSettings,
  ResourceProviderSettings,
  TavilySettings,
  PexelsSettings,
  AutoCompletePrompt,
  FloatAgentPrompt,
  ChatPrompt,
} from 'src/env.d';
import type {
  QwenSettings,
  OllamaSettings,
  MiniMaxSettings,
  DeepSeekSettings,
  VolcesSettings,
  MoonshotSettings,
  AnthropicSettings,
  KnowledgeBaseSettings,
  GrokSettings,
} from 'src/types/qwen';
import type { OpenAISettings } from 'src/types/openai';
import type { AzureOpenAISettings } from 'src/types/azureOpenai';
import type { GeminiSettings } from 'src/types/gemini';
import type { GlmSettings } from 'src/types/glm';
import { ModelType } from 'src/types/modelCategories';

const QWEN_BASEURL =
  process.env.QWEN_BASEURL || 'https://dashscope.aliyuncs.com/compatible-mode/v1';
const QWEN_APIKEY = '';
const MINIMAX_BASEURL = 'https://api.minimax.chat/v1/text/chatcompletion_v2';
const MINIMAX_APIKEY = '';
const DEEPSEEK_BASEURL = 'https://api.deepseek.com/v1';
const DEEPSEEK_APIKEY = '';
const VOLCES_BASEURL = 'https://ark.cn-beijing.volces.com/api/v3';
const VOLCES_APIKEY = '';
const MOONSHOT_BASEURL = 'https://api.moonshot.cn/v1';
const MOONSHOT_APIKEY = '';
const ANTHROPIC_BASEURL = 'https://api.anthropic.com/v1';
const ANTHROPIC_APIKEY = '';
const OPENAI_BASEURL = 'https://api.openai.com/v1';
const OPENAI_APIKEY = '';
const AZURE_OPENAI_BASEURL = ''; // 需要用户填写，格式: https://YOUR_RESOURCE_NAME.openai.azure.com
const AZURE_OPENAI_APIKEY = '';
const GEMINI_BASEURL = 'https://generativelanguage.googleapis.com/v1beta';
const GEMINI_APIKEY = '';
const GROK_BASEURL = 'https://api.x.ai/v1';
const GROK_APIKEY = '';
const GLM_BASEURL = 'https://open.bigmodel.cn/api/paas/v4';
const GLM_APIKEY = '';
const OLLAMA_BASEURL = 'http://localhost:11434/v1';
const OLLAMA_APIKEY = 'ollama';

const TAVILY_APIKEY = '';
const PEXELS_APIKEY = '';

// 知识库嵌入式模型配置
const EMBEDDING_BASEURL =
  process.env.QWEN_BASEURL || 'https://dashscope.aliyuncs.com/compatible-mode/v1';
const EMBEDDING_APIKEY = '';
const EMBEDDING_MODEL = process.env.EMBEDDING_MODEL || 'text-embedding-v4';

/**
 * 应用基础设置
 */

export const DEFAULT_APP_BASE_SETTINGS = {
  language: 'en-US',
  theme: 'dark',
} as const;

/**
 * 编辑器默认设置
 */
export const DEFAULT_EDITOR_SETTINGS = {
  fontSize: 1,
  lineHeight: 1.5,
  enableAutoComplete: true,
  enableEmbeddTitle: true,
  enableToolbar: true,
  enableAutoSave: true,
} as const;

/**
 * 自动补全默认设置
 */
export const DEFAULT_AUTOCOMPLETE_SETTINGS: AutoComplete = {
  enabled: false,
  base_url: `${VOLCES_BASEURL}/chat/completions`,
  api_key: VOLCES_APIKEY,
  body: {
    model: 'doubao-seed-1-6-flash-250615',
    prompt: '',
    temperature: 0.7,
    maxTokens: 50,
    frequency_penalty: 1.2,
    presence_penalty: 1.2,
  },
  // 自動補全配置參數 (來自 useAutoComplete.ts 的 defaultConfig)
  autoCompleteTriggerLength: 5, // 觸發自動補全的最小字符數
  autoCompleteMaxSuggestions: 1, // 最大建議數量
  autoCompleteDebounceTime: 500, // 防抖時間（毫秒）
};

/**
 * 自动补全默认设置
 */
export const DEFAULT_FLOATAGENT_SETTINGS: FloatAgent = {
  enabled: false,
  base_url: `${VOLCES_BASEURL}/chat/completions`,
  api_key: VOLCES_APIKEY,
  body: {
    model: 'doubao-seed-1-6-flash-250615',
    prompt: '',
    temperature: 0.7,
    maxTokens: 50,
    frequency_penalty: 1.2,
    presence_penalty: 1.2,
  },
  triggerLength: 5, // 觸發自動補全的最小字符數
};

/**
 * Qwen LLM 默认设置
 */
export const DEFAULT_QWEN_SETTINGS: QwenSettings = {
  baseUrl: QWEN_BASEURL,
  apiKey: QWEN_APIKEY,
  helpUrl: 'https://help.aliyun.com/zh/dashscope/developer-reference/api-key-management',
  model: 'qwen3-235b-a22b',
  temperature: 0.7,
  topP: 0.8,
  stream: true,
  parallel_tool_calls: true,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: [
      'Qwen3-235B-A22B-Thinking-2507',
      'qwen3-235b-a22b-instruct-2507',
      'qwen-max',
      'qwen-plus',
      'qwen-turbo',
      'qwen-long',
    ],
    [ModelType.REASONING]: [
      'Qwen3-235B-A22B-Thinking-2507',
      'qwen-max',
      'qwen-plus',
      'qwen-turbo',
      'qwen-long',
    ],
    [ModelType.EMBEDDING]: ['text-embedding-v4'],
    [ModelType.TOOLS_CALLS]: [
      'qwen3-235b-a22b',
      'qwen-max',
      'qwen-plus',
      'qwen-turbo',
      'qwen-long',
    ],
  },
  enabled: false,
};

/**
 * Ollama 默认设置
 */
export const DEFAULT_OLLAMA_SETTINGS: OllamaSettings = {
  baseUrl: OLLAMA_BASEURL,
  apiKey: OLLAMA_APIKEY,
  helpUrl: 'https://ollama.com/download',
  model: '',
  temperature: 0.7,
  topP: 0.8,
  stream: true,
  parallel_tool_calls: true,
  avaliableModels: {},
  enabled: false,
};

/**
 * MiniMax 默认设置
 */
export const DEFAULT_MINIMAX_SETTINGS: MiniMaxSettings = {
  baseUrl: MINIMAX_BASEURL,
  apiKey: MINIMAX_APIKEY,
  helpUrl: 'https://platform.minimaxi.com/user-center/basic-information/interface-key',
  model: 'MiniMax-M1',
  temperature: 0.7,
  topP: 0.8,
  stream: true,
  parallel_tool_calls: false,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: ['MiniMax-M1', 'MiniMax-Text-01'],
    [ModelType.TOOLS_CALLS]: ['MiniMax-M1', 'MiniMax-Text-01'],
  },
  enabled: false,
};

/**
 * DeepSeek 默认设置
 */
export const DEFAULT_DEEPSEEK_SETTINGS: DeepSeekSettings = {
  baseUrl: DEEPSEEK_BASEURL,
  apiKey: DEEPSEEK_APIKEY,
  helpUrl: 'https://platform.deepseek.com/api_keys',
  model: 'deepseek-chat',
  maxTokens: 4096,
  temperature: 0.7,
  topP: 0.8,
  stream: true,
  parallel_tool_calls: true,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: ['deepseek-chat'],
    [ModelType.REASONING]: ['deepseek-reasoner'],
    [ModelType.TOOLS_CALLS]: ['deepseek-chat'],
  },
  enabled: false,
};

/**
 * Volces 默认设置
 */
export const DEFAULT_VOLCES_SETTINGS: VolcesSettings = {
  baseUrl: VOLCES_BASEURL,
  apiKey: VOLCES_APIKEY,
  helpUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey',
  model: 'doubao-seed-1-6-flash-250615',
  temperature: 0.7,
  topP: 0.8,
  stream: true,
  parallel_tool_calls: false,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: [
      'doubao-seed-1.6-250615',
      'doubao-seed-1-6-thinking-250615',
      'doubao-seed-1-6-flash-250615',
    ],
    [ModelType.REASONING]: [
      'doubao-seed-1.6-250615',
      'doubao-seed-1-6-thinking-250615',
      'doubao-seed-1-6-flash-250615',
    ],
    [ModelType.MULTIMODAL]: [
      'doubao-seed-1.6-250615',
      'doubao-seed-1-6-thinking-250615',
      'doubao-seed-1-6-flash-250615',
    ],
    [ModelType.EMBEDDING]: ['doubao-embedding-large-text-240915', 'doubao-embedding-text-240715'],
    [ModelType.TOOLS_CALLS]: [
      'doubao-seed-1.6-250615',
      'doubao-seed-1-6-thinking-250615',
      'doubao-seed-1-6-flash-250615',
    ],
  },
  enabled: false,
};

/**
 * Moonshot 默认设置
 */
export const DEFAULT_MOONSHOT_SETTINGS: MoonshotSettings = {
  baseUrl: MOONSHOT_BASEURL,
  apiKey: MOONSHOT_APIKEY,
  helpUrl: 'https://platform.moonshot.cn/console/api-keys',
  model: 'kimi-latest',
  temperature: 0.7,
  topP: 0.8,
  stream: true,
  parallel_tool_calls: true,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: ['kimi-latest', 'kimi-k2-0711-preview', 'kimi-thinking-preview'],
    [ModelType.REASONING]: ['kimi-latest', 'kimi-k2-0711-preview', 'kimi-thinking-preview'],
    [ModelType.IMAGE_UNDERSTANDING]: ['kimi-latest', 'kimi-thinking-preview'],
    [ModelType.TOOLS_CALLS]: ['kimi-latest', 'kimi-k2-0711-preview', 'kimi-thinking-preview'],
  },
  enabled: false,
};

/**
 * Anthropic 默认设置
 */
export const DEFAULT_ANTHROPIC_SETTINGS: AnthropicSettings = {
  baseUrl: ANTHROPIC_BASEURL,
  apiKey: ANTHROPIC_APIKEY,
  helpUrl: 'https://console.anthropic.com/settings/keys',
  model: 'claude-4-sonnet',
  temperature: 0.7,
  maxTokens: 4096,
  topP: 0.8,
  stream: true,
  thinking_enabled: false,
  thinking_budget: 5000,
  interleaved_thinking: false,
  parallel_tool_calls: false,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: ['claude-4-sonnet', 'claude-4-opus', 'claude-3-7-sonnet'],
    [ModelType.REASONING]: ['claude-4-opus', 'claude-4-sonnet', 'claude-3-7-sonnet'],
    [ModelType.MULTIMODAL]: ['claude-4-sonnet', 'claude-4-opus', 'claude-3-7-sonnet'],
    [ModelType.IMAGE_UNDERSTANDING]: ['claude-4-opus', 'claude-4-sonnet', 'claude-3-7-sonnet'],
    [ModelType.TOOLS_CALLS]: ['claude-4-sonnet', 'claude-4-opus', 'claude-3-7-sonnet'],
  },
  enabled: false,
};

/**
 * OpenAI 默认设置
 */
export const DEFAULT_OPENAI_SETTINGS: OpenAISettings = {
  baseUrl: OPENAI_BASEURL,
  apiKey: OPENAI_APIKEY,
  helpUrl: 'https://platform.openai.com/api-keys',
  model: 'gpt-4o-mini',
  temperature: 0.7,
  maxTokens: 4096,
  topP: 0.8,
  stream: true,
  frequencyPenalty: 0,
  presencePenalty: 0,
  parallel_tool_calls: true,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    [ModelType.REASONING]: ['o1-preview', 'o1-mini', 'o3-mini'],
    [ModelType.MULTIMODAL]: ['gpt-4o', 'gpt-4-turbo', 'gpt-4-vision-preview'],
    [ModelType.IMAGE_GENERATION]: ['dall-e-3', 'dall-e-2'],
    [ModelType.TEXT_TO_SPEECH]: ['tts-1', 'tts-1-hd'],
    [ModelType.SPEECH_TO_TEXT]: ['whisper-1'],
    [ModelType.EMBEDDING]: [
      'text-embedding-3-large',
      'text-embedding-3-small',
      'text-embedding-ada-002',
    ],
    [ModelType.TOOLS_CALLS]: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
  },
  enabled: false,
};

/**
 * Azure OpenAI 默认设置
 */
export const DEFAULT_AZURE_OPENAI_SETTINGS: AzureOpenAISettings = {
  baseUrl: AZURE_OPENAI_BASEURL,
  apiKey: AZURE_OPENAI_APIKEY,
  helpUrl:
    'https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/OpenAI',
  deploymentName: '', // 需要用户填写
  apiVersion: '2024-06-01', // 默认使用稳定版本
  model: 'gpt-4', // 用于显示
  temperature: 0.7,
  maxTokens: 4096,
  topP: 0.8,
  stream: true,
  frequencyPenalty: 0,
  presencePenalty: 0,
  parallel_tool_calls: true,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    [ModelType.REASONING]: ['o1-preview', 'o1-mini', 'o3-mini'],
    [ModelType.MULTIMODAL]: ['gpt-4o', 'gpt-4-turbo', 'gpt-4-vision-preview'],
    [ModelType.IMAGE_GENERATION]: ['dall-e-3', 'dall-e-2'],
    [ModelType.TEXT_TO_SPEECH]: ['tts-1', 'tts-1-hd'],
    [ModelType.SPEECH_TO_TEXT]: ['whisper-1'],
    [ModelType.EMBEDDING]: [
      'text-embedding-3-large',
      'text-embedding-3-small',
      'text-embedding-ada-002',
    ],
    [ModelType.TOOLS_CALLS]: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
  },
  enabled: false,
  authType: 'apiKey',
};

/**
 * Google Gemini 默认设置
 */
export const DEFAULT_GEMINI_SETTINGS: GeminiSettings = {
  baseUrl: GEMINI_BASEURL,
  apiKey: GEMINI_APIKEY,
  helpUrl: 'https://aistudio.google.com/app/apikey',
  model: 'gemini-2.5-flash',
  temperature: 0.7,
  maxTokens: 4096,
  topP: 0.8,
  topK: 40,
  candidateCount: 1,
  stream: true,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: ['gemini-2.5-pro', 'gemini-2.5-flash'],
    [ModelType.REASONING]: ['gemini-2.5-pro', 'gemini-2.5-flash'],
    [ModelType.MULTIMODAL]: ['gemini-2.5-pro', 'gemini-2.5-flash'],
    [ModelType.EMBEDDING]: ['gemini-embedding-001'],
    [ModelType.IMAGE_UNDERSTANDING]: ['gemini-2.5-pro', 'gemini-2.5-flash'],
    [ModelType.VIDEO_UNDERSTANDING]: ['gemini-2.5-pro', 'gemini-2.5-flash'],
    [ModelType.AUDIO_UNDERSTANDING]: ['gemini-2.5-pro', 'gemini-2.5-flash'],
    [ModelType.TOOLS_CALLS]: ['gemini-2.5-pro', 'gemini-2.5-flash'],
  },
  enabled: false,
};

/**
 * Grok 默认设置
 */
export const DEFAULT_GROK_SETTINGS: GrokSettings = {
  baseUrl: GROK_BASEURL,
  apiKey: GROK_APIKEY,
  helpUrl: 'https://console.x.ai/',
  model: 'grok-beta',
  temperature: 0.7,
  maxTokens: 4096,
  topP: 0.8,
  stream: true,
  frequencyPenalty: 0,
  presencePenalty: 0,
  parallel_tool_calls: true,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: ['grok-4-0709', 'grok-3-mini', 'grok-3-fast', 'grok-3-mini-fast'],
    [ModelType.REASONING]: ['grok-4-0709', 'grok-3-mini', 'grok-3-fast', 'grok-3-mini-fast'],
    [ModelType.IMAGE_UNDERSTANDING]: ['grok-4-0709', 'grok-2-vision-1212'],
    [ModelType.TOOLS_CALLS]: ['grok-4-0709', 'grok-3-mini', 'grok-3-fast', 'grok-3-mini-fast'],
  },
  enabled: false,
};

/**
 * GLM 默认设置
 */
export const DEFAULT_GLM_SETTINGS: GlmSettings = {
  baseUrl: GLM_BASEURL,
  apiKey: GLM_APIKEY,
  helpUrl: 'https://open.bigmodel.cn/usercenter/apikeys',
  model: 'glm-4-plus',
  temperature: 0.7,
  maxTokens: 4096,
  topP: 0.9,
  stream: true,
  avaliableModels: {
    [ModelType.TEXT_GENERATION]: ['glm-4-plus', 'glm-4-air', 'glm-4-flash', 'glm-4-flashx'],
    [ModelType.REASONING]: ['glm-z1-air', 'glm-4-plus', 'glm-4-long'],
    [ModelType.MULTIMODAL]: ['glm-4v-plus-0111', 'glm-4v-plus', 'glm-4v'],
    [ModelType.IMAGE_UNDERSTANDING]: ['glm-4v-plus-0111', 'glm-4v-plus'],
    [ModelType.VIDEO_UNDERSTANDING]: ['glm-4v-plus-0111'],
    [ModelType.EMBEDDING]: ['embedding-3', 'embedding-2'],
    [ModelType.TOOLS_CALLS]: ['glm-4-plus', 'glm-4-air', 'glm-4-flash'],
  },
  enabled: false,
};

/**
 * Tavily搜索默认设置
 */
export const DEFAULT_TAVILY_SETTINGS: TavilySettings = {
  apiKey: TAVILY_APIKEY,
  baseUrl: 'https://api.tavily.com/search',
  searchDepth: 'basic' as 'basic' | 'advanced',
  includeAnswer: true,
  includeRawContent: false,
  maxResults: 5,
  includeImages: false,
  includeImageDescriptions: false,
};

/**
 * Pexels搜索默认设置
 */
export const DEFAULT_PEXELS_SETTINGS: PexelsSettings = {
  apiKey: PEXELS_APIKEY,
  baseUrl: 'https://api.pexels.com',
  maxResults: 6,
};

/**
 * 搜索引擎默认设置
 */
export const DEFAULT_SEARCH_ENGINE_SETTINGS: SearchEngineSettings = {
  providers: [
    {
      id: 'tavily',
      name: 'Tavily',
      key: 'tavily',
      description: 'Tavily Search',
      config: DEFAULT_TAVILY_SETTINGS,
    },
  ],
  defaultProvider: 'tavily',
};

/**
 * 资源服务商默认设置
 */
export const DEFAULT_RESOURCE_PROVIDER_SETTINGS: ResourceProviderSettings = {
  providers: [
    {
      id: 'pexels',
      name: 'Pexels',
      key: 'pexels',
      description: 'Pexels Media Browser',
      config: DEFAULT_PEXELS_SETTINGS,
    },
  ],
  defaultProvider: 'pexels',
};

/**
 * 知识库默认设置
 */
export const DEFAULT_KNOWLEDGEBASE_SETTINGS: KnowledgeBaseSettings = {
  baseUrl: EMBEDDING_BASEURL,
  apiKey: EMBEDDING_APIKEY,
  model: EMBEDDING_MODEL,
  chunkSize: 800,
  chunkOverlap: 200,
  semanticThreshold: 0.7,
  searchLimit: 10,
  defaultChunkStrategy: 'recursiveCharacter',
  enableMultiSemanticSplit: true,
  enableHierarchicalSplit: true,
  // 本地GGUF模型设置
  embeddingMode: 'cloud',
  localModelPath: '',
  localGpuLayers: 20,
  localContextSize: 2048,
  localUseGpu: true,
  selectedGpuDevice: -1, // 默认值：-1表示未选择设备，等待GPU检测后设置
};

/**
 * LLM 默认设置
 */
export const DEFAULT_LLM_SETTINGS: LlmSettings = {
  deepseek: DEFAULT_DEEPSEEK_SETTINGS,
  ollama: DEFAULT_OLLAMA_SETTINGS,
  qwen: DEFAULT_QWEN_SETTINGS,
  minimax: DEFAULT_MINIMAX_SETTINGS,
  volces: DEFAULT_VOLCES_SETTINGS,
  moonshot: DEFAULT_MOONSHOT_SETTINGS,
  anthropic: DEFAULT_ANTHROPIC_SETTINGS,
  openai: DEFAULT_OPENAI_SETTINGS,
  azureOpenai: DEFAULT_AZURE_OPENAI_SETTINGS,
  gemini: DEFAULT_GEMINI_SETTINGS,
  grok: DEFAULT_GROK_SETTINGS,
  glm: DEFAULT_GLM_SETTINGS,
};

export const FLOATAGENT_DEFAULT_PROMPT: FloatAgentPrompt[] = [
  {
    name: $t('default'),
    prompt: `你是一个专业的AI助手，擅长撰写文章。请遵循以下原则：\n
  1. 你的职责是为用户生成文章片段或改写片段，不是生成完整的文章，因此不需要格式化文本内容；\n
  2. 如果用户要求改写且没有具体的字数要求，那么生成文本字数与被改写内容字数差异不要超过50字；\n
  3. 返回最终的纯文本内容，不需要格式化，不需要任何解释或建议；\n
  4. 返回内容中不要包含任何换行符；\n
  5. 检查生成内容与原始内容拼接后，是否通顺，标点是否正确`,
  },
];

export const AUTOCOMPLETE_DEFAULT_PROMPT: AutoCompletePrompt[] = [
  {
    name: $t('default'),
    prompt: `请根据光标位置和前后文内容，生成一个适合插入到光标位置的文本建议。\n
要求：\n
1. 根据光标位置前后内容，判断用户是否需要续写，如果不需要，返回空字符串\n2. 与整体文风、语种保持一致\n3. 返回的内容必须是可直接插入到文档中的文本，不包含任何解释、建议或提示\n4. 根据前后文内容，正确处理返回内容首尾的标点符号，确保返回内容与原文拼接后，整体标点正确，例如，光标前有标点，光标后第一个字符不是标点，那么返回内容应该是以标点开头，不以标点结尾，反之，如果光标前有标点，光标后没有标点，那么返回内容应该是以标点结尾，不以标点开头\n5. 绝对不要返回任何不应该出现在文档中的内容（如"建议"、"提示"等字样）\n
请直接返回建议的文本内容：`,
  },
];

export const CHAT_DEFAULT_PROMPT: ChatPrompt[] = [
  {
    name: $t('default'),
    prompt: {
      responsibilities: [
        '内容创作助手：提供改写、续写、润色等文字创作服务',
        '作品管理助手：提供文件创建、修改、删除、移动等管理能力',
        '资源辅助助手：查找联网内容，文档，图片等资源，为用户提供内容创作时的可用素材',
        '灵感激发助手：提供创意激发、头脑风暴等创新能力支持',
      ],
      emphasize: `## 重要提醒：\n- 你的第一职责是协助用户对文档进行修改，而不是与用户对话，因此永远优选调用工具对当前文档，或者检索合适的文档进行操作，只有当所有工具都无法有效满足时，再在对话中输出内容要求用户手动操作\n`,
    },
  },
];

/**
 * 应用默认设置
 */
export const DEFAULT_APP_SETTINGS: AppSettings = {
  base: DEFAULT_APP_BASE_SETTINGS,
  editor: DEFAULT_EDITOR_SETTINGS,
  llm: DEFAULT_LLM_SETTINGS,
  autoComplete: DEFAULT_AUTOCOMPLETE_SETTINGS,
  floatAgent: DEFAULT_FLOATAGENT_SETTINGS,
  prompt: {
    autoComplete: {
      selected: $t('default'),
      list: AUTOCOMPLETE_DEFAULT_PROMPT,
    },
    floatAgent: {
      selected: $t('default'),
      list: FLOATAGENT_DEFAULT_PROMPT,
    },
    chat: {
      selected: $t('default'),
      list: CHAT_DEFAULT_PROMPT,
    },
  },
  provider: '',
  knowledgeBase: DEFAULT_KNOWLEDGEBASE_SETTINGS,
  searchEngine: DEFAULT_SEARCH_ENGINE_SETTINGS,
  resourceProvider: DEFAULT_RESOURCE_PROVIDER_SETTINGS,
};

/**
 * 深度合并设置对象
 * @param target 目标对象（默认设置）
 * @param source 源对象（用户设置）
 * @returns 合并后的设置对象
 */
export function deepMergeSettings<T>(target: T, source: Partial<T> | null | undefined): T {
  if (!source) return { ...target };

  const result = { ...target };

  for (const key of Object.keys(source) as Array<keyof T>) {
    const sourceValue = source[key];
    const targetValue = target[key];

    if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
      // 如果是对象且不是数组，递归合并
      (result as Record<keyof T, unknown>)[key] = deepMergeSettings(
        (targetValue as Record<string, unknown>) || {},
        sourceValue as Record<string, unknown>,
      );
    } else if (sourceValue !== undefined && sourceValue !== null) {
      // 如果是基本类型或数组，且不为 undefined 或 null，直接覆盖
      (result as Record<keyof T, unknown>)[key] = sourceValue;
    }
  }

  return result;
}

/**
 * 验证设置对象的完整性
 * @param settings 设置对象
 * @param defaultSettings 默认设置对象
 * @returns 验证并补全后的设置对象
 */
export function validateAndCompleteSettings<T>(
  settings: Partial<T> | null | undefined,
  defaultSettings: T,
): T {
  return deepMergeSettings(defaultSettings, settings);
}

/**
 * 获取完整的应用设置（包含默认值）
 * @param userSettings 用户设置
 * @returns 完整的应用设置
 */
export function getCompleteAppSettings(userSettings?: Partial<AppSettings> | null): AppSettings {
  return validateAndCompleteSettings(userSettings, DEFAULT_APP_SETTINGS);
}

/**
 * 获取完整的 LLM 设置（包含默认值）
 * @param userSettings 用户 LLM 设置
 * @returns 完整的 LLM 设置
 */
export function getCompleteLlmSettings(userSettings?: Partial<LlmSettings> | null): LlmSettings {
  return validateAndCompleteSettings(userSettings, DEFAULT_LLM_SETTINGS);
}

/**
 * 获取完整的搜索引擎设置（包含默认值）
 * @param userSettings 用户搜索引擎设置
 * @returns 完整的搜索引擎设置
 */
export function getCompleteSearchEngineSettings(
  userSettings?: Partial<SearchEngineSettings> | null,
): SearchEngineSettings {
  return validateAndCompleteSettings(userSettings, DEFAULT_SEARCH_ENGINE_SETTINGS);
}

/**
 * 获取完整的资源服务商设置（包含默认值）
 * @param userSettings 用户资源服务商设置
 * @returns 完整的资源服务商设置
 */
export function getCompleteResourceProviderSettings(
  userSettings?: Partial<ResourceProviderSettings> | null,
): ResourceProviderSettings {
  return validateAndCompleteSettings(userSettings, DEFAULT_RESOURCE_PROVIDER_SETTINGS);
}

/**
 * 获取完整的知识库设置（包含默认值）
 * @param userSettings 用户知识库设置
 * @returns 完整的知识库设置
 */
export function getCompleteKnowledgeBaseSettings(
  userSettings?: Partial<KnowledgeBaseSettings> | null,
): KnowledgeBaseSettings {
  return validateAndCompleteSettings(userSettings, DEFAULT_KNOWLEDGEBASE_SETTINGS);
}

/**
 * 检查设置是否为空或无效
 * @param settings 设置对象
 * @returns 是否为空或无效
 */
export function isSettingsEmpty(settings: unknown): boolean {
  return (
    !settings ||
    (typeof settings === 'object' && Object.keys(settings).length === 0) ||
    settings === null ||
    settings === undefined
  );
}

/**
 * 关键字黑名单
 */

export const blacklistKeywords = ['大纪元'];

export const exclude_domains = ['epochtimes.com'];

/**
 * 嵌入式大模型名称关键字
 */

export const embeddingModelKeywords = [
  'embedding',
  'snowflake',
  'embed',
  'bge-m3',
  'all-minilm',
  'bge-large',
  'paraphrase-multilingual',
];
