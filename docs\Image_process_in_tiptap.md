# TipTap 图片处理系统设计文档

## 概述

本文档详细描述了 TipTap 编辑器中图片处理系统的完整设计逻辑，包括自定义扩展、数据管理、剪贴板处理等核心功能。

## 1. 自定义 TipTap 扩展设计逻辑

### 1.1 ImageWithId 扩展核心设计

```typescript
// 位置: src/components/tiptap/tiptap.ts
const ImageWithId = Image.extend({
  name: 'image',

  addAttributes() {
    return {
      // 核心设计：src 不序列化到 JSON
      src: {
        default: null,
        renderHTML: false, // 不渲染到 HTML
        parseHTML: false, // 不从 HTML 解析
      },
      // 唯一标识符 - 保存到数据库的关键属性
      'data-image-id': {
        default: null,
        renderHTML: (attributes) => {
          if (!attributes['data-image-id']) return false;
          return { 'data-image-id': attributes['data-image-id'] };
        },
      },
      alt: { default: null },
      title: { default: null },
      width: { default: null },
      height: { default: null },
    };
  },
});
```

### 1.2 解析优先级设计

```typescript
parseHTML() {
  return [
    {
      // 优先级1：解析有 data-image-id 的图片节点
      tag: 'img[data-image-id]',
      getAttrs: (element) => ({
        'data-image-id': element.getAttribute('data-image-id'),
        alt: element.getAttribute('alt'),
        // 不解析 src 属性
      }),
    },
    {
      // 优先级2：向后兼容 image:// 格式
      tag: 'img[src^="image://"]',
      getAttrs: (element) => {
        const imageId = element.getAttribute('src')?.replace('image://', '');
        return { 'data-image-id': imageId };
      },
    },
    {
      // 优先级3：处理普通图片（粘贴场景）
      tag: 'img[src]',
      getAttrs: (element) => {
        const src = element.getAttribute('src');
        // 过滤临时 URL
        if (src?.startsWith('blob:') || src?.startsWith('data:')) {
          return false;
        }
        return { src }; // 临时保存，后续处理
      },
    },
  ];
}
```

### 1.3 NodeView 动态加载设计

```typescript
addNodeView() {
  return ({ HTMLAttributes }) => {
    const img = document.createElement('img');
    const imageId = HTMLAttributes['data-image-id'];

    if (imageId) {
      // 核心逻辑：总是从后端动态获取图片
      loadImageFromBackend();
    }

    async function loadImageFromBackend() {
      try {
        const { imageManager } = await import('src/utils/imageManager');
        const blobUrl = await imageManager.getImageAsBlob(Number(imageId));

        if (blobUrl) {
          img.src = blobUrl;
        }
      } catch (error) {
        // 显示错误占位符
        img.src = 'data:image/svg+xml;base64,...';
      }
    }

    return { dom: img };
  };
}
```

## 2. 图片数据获取、存储、删除、防误删逻辑

### 2.1 数据存储架构

#### 数据库表结构

```sql
-- 图片主表
CREATE TABLE images (
    id INTEGER PRIMARY KEY,
    file_path TEXT NOT NULL,
    original_url TEXT,
    mime_type TEXT NOT NULL,
    file_size INTEGER,
    width INTEGER,
    height INTEGER,
    hash_sha256 TEXT UNIQUE,  -- 去重关键字段
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 图片引用表（引用计数）
CREATE TABLE image_references (
    id INTEGER PRIMARY KEY,
    image_id INTEGER NOT NULL,
    document_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (image_id) REFERENCES images(id),
    UNIQUE(image_id, document_id)
);
```

#### JSON 存储格式

```json
{
  "type": "image",
  "attrs": {
    "data-image-id": "123",
    "alt": "图片描述",
    "width": "800",
    "height": "600"
    // 注意：不包含 src 属性
  }
}
```

### 2.2 图片获取逻辑

```typescript
// 位置: src/utils/imageManager.ts
class ImageManager {
  /**
   * 获取图片作为 Blob URL
   */
  async getImageAsBlob(imageId: number): Promise<string | null> {
    return new Promise((resolve) => {
      window.databaseApi.getImageAsBlob(imageId, (result) => {
        if (result.success && result.blob_data) {
          // 创建 Blob URL 用于显示
          const blob = this.base64ToBlob(result.blob_data, result.mime_type);
          const blobUrl = URL.createObjectURL(blob);
          resolve(blobUrl);
        } else {
          resolve(null);
        }
      });
    });
  }
}
```

### 2.3 图片存储逻辑

```typescript
/**
 * 保存图片的完整流程
 */
async saveImageFromData(imageData: string, mimeType: string, originalUrl?: string) {
  // 1. 计算哈希值检查重复
  // 2. 如果重复，返回现有图片ID
  // 3. 如果不重复，保存新图片文件
  // 4. 记录到数据库
  // 5. 返回图片ID和本地路径
}

/**
 * 添加图片引用
 */
async addImageReference(imageId: number, documentId: number) {
  // 在 image_references 表中添加引用记录
  // 支持多文档引用同一图片
}
```

### 2.4 图片删除与防误删逻辑

#### 智能删除机制

```typescript
/**
 * 删除图片的安全流程
 */
async deleteImage(imageId: number, documentId: number) {
  // 1. 只删除当前文档的引用
  await this.removeImageReference(imageId, documentId);

  // 2. 检查是否还有其他引用
  const references = await this.getImageReferences(imageId);

  // 3. 如果没有引用，才删除实际文件
  if (references.length === 0) {
    await this.deleteImageFile(imageId);
  }
}

/**
 * 防误删机制
 */
async deleteUnreferencedImages() {
  // 定期清理无引用的图片文件
  // 只在确认无任何引用时才删除
}
```

#### 编辑器删除处理

```typescript
// 位置: src/components/tiptap/imageHandler.ts
setupImageDeleteHandler() {
  this.editor.on('update', ({ transaction }) => {
    // 检测被删除的图片节点
    const deletedImages = this.findDeletedImages(transaction);

    if (deletedImages.length > 0) {
      // 异步处理删除，避免阻塞编辑器
      this.handleImageDeletes(deletedImages);
    }
  });
}

private async handleImageDeletes(imageIds: number[]) {
  for (const imageId of imageIds) {
    try {
      // 使用安全删除机制
      await imageManager.removeImageReference(imageId, this.documentId);
      await imageManager.deleteUnreferencedImages();
    } catch (error) {
      console.error('删除图片引用失败:', error);
    }
  }
}
```

## 3. 剪贴板内容粘贴逻辑

### 3.1 粘贴处理器架构

```typescript
// 位置: src/components/tiptap/imageHandler.ts
setupPasteHandler() {
  this.editor.view.dom.addEventListener('paste', (event) => {
    const clipboardData = event.clipboardData;
    if (!clipboardData) return;

    // 处理优先级
    if (this.hasImageFiles(clipboardData)) {
      // 1. 图片文件粘贴
      this.handleImageFilesPaste(clipboardData);
    } else if (this.hasHtmlContent(clipboardData)) {
      // 2. HTML 内容粘贴（包含图片）
      this.handleHtmlContentPaste(clipboardData);
    }
  });
}
```

### 3.2 网页内容粘贴处理逻辑

#### 3.2.1 HTML 内容解析

```typescript
private async handleHtmlContentPaste(clipboardData: DataTransfer) {
  const htmlContent = clipboardData.getData('text/html');
  if (!htmlContent) return;

  // 创建临时DOM解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;

  // 查找所有图片元素
  const imgElements = tempDiv.querySelectorAll('img');

  for (const img of imgElements) {
    const src = img.getAttribute('src');
    if (src && src.startsWith('http')) {
      // 处理网络图片
      await this.processNetworkImage(src, img);
    } else if (src && src.startsWith('data:')) {
      // 处理base64图片
      await this.processBase64Image(src, img);
    }
  }
}
```

#### 3.2.2 网络图片处理

```typescript
private async processNetworkImage(imageUrl: string, imgElement: HTMLImageElement) {
  try {
    // 使用图片管理器处理网络图片
    const result = await imageManager.processImageUrl(imageUrl, this.documentId);

    if (result.success && result.imageId) {
      // 替换为图片ID引用
      imgElement.setAttribute('data-image-id', result.imageId.toString());
      imgElement.removeAttribute('src');

      console.log(`网络图片处理成功: ${imageUrl} -> ID: ${result.imageId}`);
    }
  } catch (error) {
    console.error('处理网络图片失败:', error);
  }
}
```

#### 3.2.3 Base64 图片处理

```typescript
private async processBase64Image(dataUrl: string, imgElement: HTMLImageElement) {
  try {
    // 解析base64数据
    const [header, data] = dataUrl.split(',');
    const mimeMatch = header.match(/data:([^;]+)/);
    const mimeType = mimeMatch ? mimeMatch[1] : 'image/png';

    // 保存图片
    const result = await imageManager.saveImageFromData(data, mimeType, dataUrl);

    if (result.success && result.imageId) {
      // 添加引用
      await imageManager.addImageReference(result.imageId, this.documentId);

      // 替换为图片ID引用
      imgElement.setAttribute('data-image-id', result.imageId.toString());
      imgElement.removeAttribute('src');

      console.log(`Base64图片处理成功: ID: ${result.imageId}`);
    }
  } catch (error) {
    console.error('处理Base64图片失败:', error);
  }
}
```

### 3.3 文档间内容粘贴处理逻辑

#### 3.3.1 Blob URL 检测与处理

```typescript
private async processBlobImagesInContent() {
  const editorDom = this.editor.view.dom;
  const imgElements = editorDom.querySelectorAll('img[src^="blob:"]');

  if (imgElements.length === 0) return;

  console.log(`发现 ${imgElements.length} 个blob图片，开始处理...`);

  for (const imgElement of imgElements) {
    const blobUrl = imgElement.getAttribute('src');
    const imageId = imgElement.getAttribute('data-image-id');

    if (imageId) {
      // 如果已有图片ID，直接添加引用
      await this.addImageReferenceForExistingImage(parseInt(imageId));
    } else if (blobUrl) {
      // 如果是新的blob URL，需要保存图片
      await this.processBlobImage(blobUrl, imgElement);
    }
  }
}
```

#### 3.3.2 现有图片引用处理

```typescript
private async addImageReferenceForExistingImage(imageId: number) {
  try {
    // 检查图片是否存在
    const imageExists = await imageManager.checkImageExists(imageId);

    if (imageExists) {
      // 添加到当前文档的引用
      await imageManager.addImageReference(imageId, this.documentId);
      console.log(`添加现有图片引用: ID ${imageId} -> 文档 ${this.documentId}`);
    } else {
      console.warn(`图片ID ${imageId} 不存在，无法添加引用`);
    }
  } catch (error) {
    console.error('添加图片引用失败:', error);
  }
}
```

#### 3.3.3 Blob 图片保存处理

```typescript
private async processBlobImage(blobUrl: string, imgElement: HTMLImageElement) {
  try {
    // 从blob URL获取图片数据
    const response = await fetch(blobUrl);
    const blob = await response.blob();
    const base64Data = await imageManager.blobToBase64(blob);

    // 保存图片
    const result = await imageManager.saveImageFromData(
      base64Data,
      blob.type,
      'copied-image'
    );

    if (result.success && result.imageId) {
      // 添加引用
      await imageManager.addImageReference(result.imageId, this.documentId);

      // 更新图片元素
      imgElement.setAttribute('data-image-id', result.imageId.toString());
      imgElement.removeAttribute('src');

      console.log(`Blob图片处理成功: ID: ${result.imageId}`);
    }
  } catch (error) {
    console.error('处理Blob图片失败:', error);
  }
}
```

### 3.4 图片文件直接粘贴处理

```typescript
private async handleImageFilesPaste(clipboardData: DataTransfer) {
  const files = Array.from(clipboardData.files).filter(file =>
    file.type.startsWith('image/')
  );

  if (files.length === 0) return;

  // 阻止默认粘贴行为
  event.preventDefault();

  // 处理图片文件
  await this.handleImageFiles(files);
}

private isImageFile(file: File): boolean {
  return file.type.startsWith('image/') && [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ].includes(file.type);
}
```

## 4. 系统架构总结

### 4.1 核心设计原则

1. **数据持久性**: 图片节点JSON中不保存临时URL，只保存永久的图片ID
2. **引用计数**: 支持多文档引用同一图片，防止误删
3. **去重机制**: 基于SHA256哈希，相同图片只存储一份
4. **动态加载**: 每次打开文档时动态获取有效的图片地址
5. **向后兼容**: 支持旧格式的图片引用

### 4.2 数据流向图

```
用户操作 → 图片处理器 → 图片管理器 → Qt后端 → 数据库/文件系统
    ↓           ↓           ↓         ↓         ↓
  拖拽/粘贴 → 文件处理 → 保存/引用 → API调用 → 持久化存储
    ↓           ↓           ↓         ↓         ↓
  编辑器显示 ← NodeView ← Blob URL ← 数据获取 ← 数据库查询
```

### 4.3 关键优势

1. **存储效率**: 图片去重，节省磁盘空间
2. **数据安全**: 引用计数防止误删，多重备份机制
3. **性能优化**: 动态加载，按需获取图片数据
4. **用户体验**: 无缝的拖拽、粘贴、复制体验
5. **系统稳定**: 完善的错误处理和降级机制

### 4.4 扩展性考虑

- 支持更多图片格式
- 图片压缩和优化
- 云存储集成
- 图片版本管理
- 批量操作优化

这套图片处理系统为 TipTap 编辑器提供了完整、高效、安全的图片管理解决方案。
