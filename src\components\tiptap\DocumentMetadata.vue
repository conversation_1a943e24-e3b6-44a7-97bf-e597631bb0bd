<template>
  <div class="document-metadata">
    <div v-if="!documentData" class="text-grey-6 text-center q-pa-md">
      {{ $t('src.components.tiptap.documentMetadata.selectDocument') }}
    </div>

    <div v-else-if="loading" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" />
      <div class="q-mt-sm">{{ $t('src.components.tiptap.documentMetadata.loading') }}</div>
    </div>

    <div v-else-if="documentData" class="row gap-md q-pa-sm">
      <!-- 元数据编辑区域 -->
      <div class="q-mb-md q-space q-px-md q-pt-sm">
        <!-- Prompt字段 -->
        <q-input
          v-model="editablePrompt"
          :label="$t('src.components.tiptap.documentMetadata.prompt')"
          type="textarea"
          outlined
          dense
          rows="3"
          class="q-mb-md"
          :hint="$t('src.components.tiptap.documentMetadata.promptHint')"
          @blur="onPromptBlur"
        />

        <!-- Note字段 -->
        <q-input
          v-model="editableNote"
          :label="$t('src.components.tiptap.documentMetadata.note')"
          type="textarea"
          outlined
          dense
          rows="3"
          class="q-mb-md"
          :hint="$t('src.components.tiptap.documentMetadata.noteHint')"
          @blur="onNoteBlur"
        />

        <!-- 保存状态指示器 -->
        <div v-if="saving" class="row items-center q-gutter-sm text-grey-6">
          <q-spinner size="16px" />
          <span class="text-caption">{{
            $t('src.components.tiptap.documentMetadata.saving')
          }}</span>
        </div>
      </div>

      <!-- 只读信息 -->
      <q-list class="q-px-md q-pt-sm">
        <q-item>
          <q-item-section>
            <q-item-label caption class="op-5"
              >{{ $t('src.components.tiptap.documentMetadata.title') }}:</q-item-label
            >
            <q-item-label>{{
              documentData.title || $t('src.components.tiptap.documentMetadata.noTitle')
            }}</q-item-label>
          </q-item-section>
        </q-item>
        <q-item>
          <q-item-section>
            <q-item-label caption class="op-5"
              >{{ $t('src.components.tiptap.documentMetadata.createdAt') }}:</q-item-label
            >
            <q-item-label>{{ formatDate(documentData.created_at) }}</q-item-label>
          </q-item-section>
        </q-item>
        <q-item>
          <q-item-section>
            <q-item-label caption class="op-5"
              >{{ $t('src.components.tiptap.documentMetadata.updatedAt') }}:</q-item-label
            >
            <q-item-label>{{ formatDate(documentData.updated_at) }}</q-item-label>
          </q-item-section>
        </q-item>
        <q-item>
          <q-item-section>
            <q-item-label caption class="op-5"
              >{{ $t('src.components.tiptap.documentMetadata.documentId') }}:</q-item-label
            >
            <q-item-label>{{ documentData.id }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>

    <div v-else class="text-negative text-center q-pa-md">
      {{ $t('src.components.tiptap.documentMetadata.loadFailed') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onBeforeUnmount } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useDocStore } from 'src/stores/doc';
import { useSqlite } from 'src/composeables/useSqlite';
import type { Document } from 'src/types/doc';

const $q = useQuasar();
const { t: $t } = useI18n({ useScope: 'global' });
const docStore = useDocStore();

// 定义metadata数据结构
interface MetadataObject {
  prompt: string;
  note: string;
}

// 默认metadata数据
const defaultMetadata: MetadataObject = {
  prompt: '',
  note: '',
};

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const editablePrompt = ref('');
const editableNote = ref('');
const originalPrompt = ref('');
const originalNote = ref('');

// 获取当前聚焦的文档
const focusedDocument = computed(() => docStore.getFocusedDocument());

// 当前文档数据（从聚焦文档获取）
const documentData = computed(() => focusedDocument.value);

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return $t('src.components.tiptap.documentMetadata.unknown');
  try {
    const date = new Date(dateString);
    return date.toLocaleString($t('src.components.tiptap.documentMetadata.locale'), {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  } catch (error) {
    throw new Error(`${$t('src.components.tiptap.documentMetadata.dateError')}: ${error}`);
  }
};

// 解析metadata字符串为对象
const parseMetadata = (metadataString: string | null | undefined): MetadataObject => {
  if (!metadataString) {
    return { ...defaultMetadata };
  }

  try {
    let parsed = JSON.parse(metadataString);

    // 处理嵌套的JSON字符串（由于之前的双重序列化导致的问题）
    // 如果解析结果是字符串，继续解析直到得到对象
    while (typeof parsed === 'string') {
      parsed = JSON.parse(parsed);
    }

    // 确保parsed是对象类型
    if (typeof parsed === 'object' && parsed !== null) {
      return {
        prompt: parsed.prompt || '',
        note: parsed.note || '',
      };
    } else {
      // 如果不是对象，返回默认值
      return { ...defaultMetadata };
    }
  } catch (error) {
    console.warn($t('src.components.tiptap.documentMetadata.parseMetadataFailed'), error);
    return { ...defaultMetadata };
  }
};

// 将metadata对象转换为JSON字符串
const stringifyMetadata = (metadata: MetadataObject): string => {
  return JSON.stringify(metadata);
};

// 初始化元数据编辑字段
const initializeMetadata = (document: Document | null) => {
  const metadata = parseMetadata(document?.metadata);

  editablePrompt.value = metadata.prompt;
  editableNote.value = metadata.note;
  originalPrompt.value = metadata.prompt;
  originalNote.value = metadata.note;
};

// 检查是否有变化
const hasChanges = (): boolean => {
  return editablePrompt.value !== originalPrompt.value || editableNote.value !== originalNote.value;
};

// 保存元数据（如果有变化）
const saveMetadataIfChanged = async (): Promise<void> => {
  if (!hasChanges()) {
    return; // 没有变化，不需要保存
  }

  const currentDoc = documentData.value;
  if (!currentDoc) return;

  saving.value = true;
  try {
    // 构建metadata对象
    const metadataObject: MetadataObject = {
      prompt: editablePrompt.value,
      note: editableNote.value,
    };

    // 转换为JSON字符串
    const metadataString = stringifyMetadata(metadataObject);

    // 使用useSqlite的updateDocument方法更新文档
    // 需要获取当前文档的content，因为updateDocument需要完整的参数
    const currentContent = docStore.getEditorContent(currentDoc.id);

    await useSqlite().updateDocument(
      currentDoc.id,
      currentDoc.title || '',
      currentContent || { type: 'doc', content: [] },
      currentDoc.folder_id || null,
      metadataString,
    );

    // 更新成功后，更新本地状态
    originalPrompt.value = editablePrompt.value;
    originalNote.value = editableNote.value;

    $q.notify({
      type: 'positive',
      message: $t('src.components.tiptap.documentMetadata.metadataSaved'),
      position: 'top',
    });
  } catch (error) {
    console.error($t('src.components.tiptap.documentMetadata.saveMetadataFailed'), error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.tiptap.documentMetadata.saveMetadataFailed'),
      position: 'top',
    });
  } finally {
    saving.value = false;
  }
};

// Prompt字段失去焦点时的处理
const onPromptBlur = () => {
  void saveMetadataIfChanged();
};

// Note字段失去焦点时的处理
const onNoteBlur = () => {
  void saveMetadataIfChanged();
};

// 监听聚焦文档变化
watch(
  focusedDocument,
  async (newDoc, oldDoc) => {
    // 如果有旧文档且有未保存的变化，先保存
    if (oldDoc && hasChanges()) {
      await saveMetadataIfChanged();
    }

    if (newDoc) {
      initializeMetadata(newDoc);
    } else {
      // 清空所有字段
      editablePrompt.value = '';
      editableNote.value = '';
      originalPrompt.value = '';
      originalNote.value = '';
    }
  },
  { immediate: true },
);

// 组件销毁前保存未保存的变化
onBeforeUnmount(async () => {
  if (hasChanges()) {
    await saveMetadataIfChanged();
  }
});
</script>

<style scoped>
.document-metadata {
  height: 100%;
  overflow-y: auto;
}

.info-section {
  border-top: 1px solid #e0e0e0;
  padding-top: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.info-value {
  color: #333;
  text-align: right;
  word-break: break-all;
}
</style>
