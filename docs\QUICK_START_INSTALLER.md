# InkCop Windows Installer - Quick Start Guide

## 🚀 Quick Start (Recommended)

### Option 1: Interactive Menu (Easiest)

1. Double-click `build-installer.bat`
2. Choose your installer type from the menu
3. Follow the prompts

### Option 2: PowerShell Command (Fast)

```powershell
# Build signed MSIX package (recommended)
.\build-installer.ps1 -Type MSIX

# Build unsigned MSIX package (requires Developer Mode)
.\build-installer.ps1 -Type MSIX -SkipSigning

# Build traditional installer
.\build-installer.ps1 -Type InnoSetup

# Build all types
.\build-installer.ps1 -Type All
```

## 📋 Prerequisites Check

Before building installers, ensure you have:

### For MSIX Packages (Recommended)

- ✅ **Windows SDK** installed
- ✅ `makeappx.exe` in PATH

**Quick Install:**

```powershell
# Check if makeappx is available
makeappx /?
```

If not found, download Windows SDK from: https://developer.microsoft.com/windows/downloads/windows-sdk/

### For Traditional Installers

- ✅ **Inno Setup** installed
- ✅ `iscc.exe` in PATH

**Quick Install:**
Download from: https://jrsoftware.org/isinfo.php

## 🎯 Choose Your Installer Type

| Type           | Best For            | Pros                                       | Cons                 |
| -------------- | ------------------- | ------------------------------------------ | -------------------- |
| **MSIX**       | Modern deployment   | Store-ready, clean uninstall, auto-updates | Requires Windows SDK |
| **Inno Setup** | Direct distribution | Custom directory, familiar to users        | Requires Inno Setup  |
| **Portable**   | No-install usage    | No dependencies, USB-friendly              | Manual updates       |

## ⚡ One-Command Build

### Build Signed MSIX (Most Popular)

```powershell
.\build-installer.ps1 -Type MSIX
```

### Build Unsigned MSIX (Developer Mode)

```powershell
.\build-installer.ps1 -Type MSIX -SkipSigning
```

### Build Traditional Installer

```powershell
.\build-installer.ps1 -Type InnoSetup
```

### Build Everything

```powershell
.\build-installer.ps1 -Type All
```

## 📁 Output Files

After building, check the `dist-packages` folder:

### MSIX Package

- `InkCop_1.0.0_x64.msix` - Unsigned package (if -SkipSigning used)
- `InkCop_1.0.0_x64_Signed.msix` - Signed package (default)
- `InkCop-SelfSigned.cer` - Self-signed certificate
- `install-all.ps1` - One-click installer
- `install-certificate.ps1` - Certificate installer
- `install-signed.ps1` - Signed package installer

### Traditional Installer

- `InkCop_1.0.0_x64_Setup.exe` - Installer executable

### Portable Package

- `InkCop_1.0.0_x64_Portable.zip` - Portable ZIP file

## 🔧 Common Issues & Solutions

### ❌ "makeappx not found"

**Solution:** Install Windows SDK

```powershell
# Download from: https://developer.microsoft.com/windows/downloads/windows-sdk/
```

### ❌ "iscc not found"

**Solution:** Install Inno Setup

```powershell
# Download from: https://jrsoftware.org/isinfo.php
```

### ❌ "Build failed"

**Solution:** Check application build first

```powershell
.\prod-build.ps1
```

### ❌ "MSIX won't install"

**Solution:** Enable Developer Mode

1. Windows Settings → Update & Security → For developers
2. Enable "Developer mode" or "Sideload apps"

## 🎨 Customization

### Change Version

```powershell
.\build-installer.ps1 -Type MSIX -Version "2.0.0"
```

### Skip Application Build

```powershell
.\build-installer.ps1 -Type MSIX -SkipBuild
```

### Custom Publisher Name

Edit the scripts and change:

```powershell
$PublisherName = "Your Company Name"
```

## 📦 Distribution Guide

### MSIX Package

**For End Users:**

1. Provide the `.msix` file and `install.ps1`
2. Users run: `.\install.ps1`

**For Enterprise:**

1. Sign the package with your certificate
2. Deploy via Microsoft Intune or SCCM

### Traditional Installer

**For End Users:**

1. Provide the `Setup.exe` file
2. Users run as Administrator

### Portable Package

**For End Users:**

1. Provide the `.zip` file
2. Users extract and run `InkCop.exe`

## 🔐 Code Signing (Production)

For production releases, sign your installers:

```powershell
# Sign MSIX
signtool sign /fd SHA256 /a "InkCop_1.0.0_x64.msix"

# Sign EXE
signtool sign /fd SHA256 /a "InkCop_1.0.0_x64_Setup.exe"
```

## 📞 Need Help?

1. **Check the logs** - Scripts show detailed error messages
2. **Read INSTALLER_README.md** - Comprehensive documentation
3. **Test prerequisites** - Ensure all tools are installed
4. **Verify base build** - Run `prod-build.ps1` first

## 🎉 Success Checklist

After running the build scripts, you should have:

- ✅ No error messages in the console
- ✅ Files created in `dist-packages` folder
- ✅ Installer files are the expected size (>10MB typically)
- ✅ Test installation works on a clean system

## 🚀 Next Steps

1. **Test your installer** on a clean Windows system
2. **Create installation instructions** for your users
3. **Consider code signing** for production releases
4. **Set up automated builds** for continuous deployment

---

**Happy Building! 🎯**

For detailed documentation, see `INSTALLER_README.md`
