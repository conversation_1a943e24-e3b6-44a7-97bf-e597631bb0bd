<template>
  <div class="pexels-config q-gutter-y-md">
    <q-input
      :model-value="config.apiKey"
      :label="$t('src.components.settings.providers.PexelsProviderConfig.apiKeyLabel')"
      outlined
      dense
      type="password"
      :placeholder="$t('src.components.settings.providers.PexelsProviderConfig.apiKeyPlaceholder')"
      :hint="$t('src.components.settings.providers.PexelsProviderConfig.apiKeyHint')"
      @update:model-value="updateConfig('apiKey', $event)"
    />

    <q-input
      :model-value="config.baseUrl"
      :label="$t('src.components.settings.providers.PexelsProviderConfig.baseUrlLabel')"
      outlined
      dense
      :placeholder="$t('src.components.settings.providers.PexelsProviderConfig.baseUrlPlaceholder')"
      :hint="$t('src.components.settings.providers.PexelsProviderConfig.baseUrlHint')"
      @update:model-value="updateConfig('baseUrl', $event)"
    />

    <q-input
      :model-value="config.maxResults"
      :label="$t('src.components.settings.providers.PexelsProviderConfig.maxResultsLabel')"
      outlined
      dense
      type="number"
      :placeholder="
        $t('src.components.settings.providers.PexelsProviderConfig.maxResultsPlaceholder')
      "
      :hint="$t('src.components.settings.providers.PexelsProviderConfig.maxResultsHint')"
      @update:model-value="updateConfig('maxResults', parseInt(String($event)) || 6)"
    />
  </div>
</template>

<script setup lang="ts">
import type { PexelsSettings } from 'src/env.d';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

interface Props {
  config: PexelsSettings;
}

interface Emits {
  (e: 'update', config: PexelsSettings): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 更新配置
const updateConfig = (key: keyof PexelsSettings, value: string | number | boolean) => {
  const updatedConfig = {
    ...props.config,
    [key]: value,
  };
  emit('update', updatedConfig);
};
</script>
