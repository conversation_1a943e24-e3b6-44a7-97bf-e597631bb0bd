<template>
  <node-view-wrapper class="agent-writer-panel q-mb-md">
    <q-card
      bordered
      class="shadow-24 radius-sm relative-position z-fab"
      :class="$q.dark.mode ? 'bg-grey-10' : 'bg-grey-1'"
    >
      <q-card-section class="row items-center q-pa-xs border-bottom">
        <q-space />
        <q-btn v-if="!hasLlmResult" flat dense size="sm" round icon="mdi-close" @click="cancel" />
        <q-btn
          v-else
          :color="$q.dark.mode ? 'grey-9 text-grey-3' : 'grey-3 text-grey-9'"
          dense
          size="sm"
          padding="xs sm"
          class="radius-xs"
          icon="mdi-checkbox-marked-outline"
          :label="`${$q.platform.is.mac ? '⌘' : 'Ctrl'} + Enter | 接受`"
          @click="handleFinalConfirm"
        />
      </q-card-section>
      <q-card-section class="q-pa-xs">
        <div
          v-if="llmResult.reasoning_content || llmResult.content"
          class="q-pa-md column no-wrap gap-sm"
        >
          <div>思考：</div>
          <div
            v-if="llmResult.reasoning_content"
            class="scroll-y op-3 column no-wrap gap-xs"
            style="max-height: 200px; line-height: 1.2"
          >
            {{ llmResult.reasoning_content }}
          </div>
          <div v-if="llmResult.content">{{ llmResult.content }}</div>
        </div>
        <q-input
          v-else
          ref="inputRef"
          v-model="message"
          borderless
          autofocus
          autogrow
          type="text"
          input-style="padding: 8px 8px;"
          placeholder="✨ 请输入提示词"
        />
      </q-card-section>
      <q-card-section class="row no-wrap gap-xs q-pa-xs border-top">
        <q-btn
          flat
          dense
          size="sm"
          icon="mdi-code-braces"
          label="提示词"
          @click="togglePromptWindow"
        />
        <q-space />
        <q-btn
          v-if="isGenerating || hasLlmResult"
          flat
          dense
          unelevated
          size="sm"
          icon="mdi-stop"
          @click="cancel"
        />
        <q-btn
          v-if="!hasLlmResult"
          dense
          size="sm"
          icon="auto_awesome"
          unelevated
          :color="$q.dark.mode ? 'grey-9 text-grey-3' : 'grey-3 text-grey-9'"
          padding="2px 8px"
          label="生成"
          @click="handleGenerate(message)"
        />
        <q-btn
          v-else
          dense
          size="sm"
          icon="auto_awesome"
          unelevated
          :color="$q.dark.mode ? 'grey-9 text-grey-3' : 'grey-3 text-grey-9'"
          padding="2px 8px"
          label="重新生成"
          @click="reGenerate(message)"
        />
      </q-card-section>
    </q-card>
    <q-card
      class="absolute"
      :style="{ top: promptWindowYPosition + 'px', left: '10px', right: '10px', bottom: '10px' }"
      bordered
    >
      <div style="height: 1.2rem; line-height: 1.2rem" class="q-pa-xs text-caption op-5">
        Prompt:
      </div>
      <q-card-section class="q-pa-xs scroll-y" style="max-height: 100px">
        <q-input
          ref="promptInputRef"
          v-model="prompt"
          borderless
          autofocus
          autogrow
          input-style="padding: 8px 8px;"
          type="text"
        />
      </q-card-section>
    </q-card>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { NodeViewWrapper, type NodeViewProps } from '@tiptap/vue-3';
import { ref, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';
import { useQuasar } from 'quasar';
// import type { Editor } from '@tiptap/core';
import { useFloatAgent } from 'src/composeables/useFloatAgent';

const $q = useQuasar();

const message = ref('');
const prompt = ref('');

const promptWindowYPosition = ref(0);
const togglePromptWindow = () => {
  if (promptWindowYPosition.value === 0) {
    if (inputRef.value) {
      inputRef.value.blur();
    }
    if (promptInputRef.value) {
      promptInputRef.value.focus();
    }
    promptWindowYPosition.value = -120;
  } else {
    if (promptInputRef.value) {
      promptInputRef.value.blur();
    }
    if (inputRef.value) {
      inputRef.value.focus();
    }
    promptWindowYPosition.value = 0;
  }
};

const props = defineProps<NodeViewProps>();
const attrs = props.node.attrs;
const {
  selectedText,
  currentText,
  prevNodesText,
  nextNodesText,
  selectedNodePos,
  selectedNodeSize,
  currentNodePos,
  originalInsertPos,
} = attrs;

console.log('AgentWriterView attrs', attrs);

const context = computed(() => {
  const parts = [];

  if (currentText) {
    parts.push(`当前段落的文本是：${currentText}；`);
  }

  if (selectedText) {
    parts.push(`用户需要改写的文字内容是：${selectedText}；`);
  } else if (
    originalInsertPos !== undefined &&
    currentText &&
    currentNodePos !== undefined &&
    !selectedText
  ) {
    // 计算光标在当前文本中的相对位置，使用原始插入位置
    // 由于 Tiptap 文档从位置 1 开始，而 slice 从 0 开始，需要减 1 来补偿
    const relativePos = originalInsertPos - currentNodePos - 1;
    if (relativePos >= 0 && relativePos <= currentText.length) {
      // 将文本按相对光标位置分成两部分
      const beforeText = currentText.slice(0, relativePos);
      const afterText = currentText.slice(relativePos);
      parts.push(
        `用户需要你在内容片段之间添加内容，目前的光标位置在"${beforeText}"和"${afterText}"之间，请按照用户要求生成插入到光标位置的内容；`,
      );
    }
  }

  if (prevNodesText.length > 0) {
    parts.push(`前${prevNodesText.length}个段落的文本是：${prevNodesText.join('\n')}；`);
  }

  if (nextNodesText.length > 0) {
    parts.push(`后${nextNodesText.length}个段落的文本是：${nextNodesText.join('\n')}；`);
  }

  return parts.length > 0 ? parts.join('\n') : '没有上下文信息';
});

const { generate, isGenerating, abort } = useFloatAgent();
const llmResult = ref({
  content: '',
  reasoning_content: '',
});
const hasLlmResult = computed(() => {
  return !!llmResult.value.content;
});

const inputRef = ref(null);
const promptInputRef = ref(null);

const handleGenerate = async (_message: string) => {
  // 使用节点属性中的 insertPos，而不是当前的光标位置
  console.log('handleGenerate', props);
  const insertPos = props.node.attrs.insertPos;
  if (!props.editor || insertPos === undefined) return;

  await generate(_message, prompt.value, context.value, llmResult.value);
  handleAccept();
};

const reGenerate = async (_message: string) => {
  reset();
  await generate(_message, prompt.value, context.value, llmResult.value);
  handleAccept();
};

const llmResponsed = ref(false);
const markInsertArea = ref<[number, number] | null>(null);
const markDeleteArea = ref<[number, number] | null>(null);

const handleAccept = () => {
  if (!props.editor || !llmResult.value.content) return;

  // 使用节点属性中的 insertPos
  const insertPos = props.node.attrs.insertPos;
  if (insertPos === undefined) return;

  // agentWriter 节点插入后，所有后续位置都要 +1
  const offset = 1;

  props.editor
    .chain()
    .focus()
    .command(({ tr }) => {
      if (selectedText && selectedNodePos !== undefined && selectedNodeSize !== undefined) {
        // 记录删除区域
        markDeleteArea.value = [
          selectedNodePos + offset,
          selectedNodePos + selectedNodeSize + offset,
        ];

        // 1. 先对原选区做删除标记（区间+1）
        tr.addMark(
          markDeleteArea.value[0],
          markDeleteArea.value[1],
          props.editor.schema.marks.trackChange.create({
            type: 'deletion',
            author: 'AI Assistant',
            timestamp: Date.now(),
            status: 'pending',
            comment: 'AI 建议删除此内容',
          }),
        );

        // 2. 再插入新内容（带插入标记），插入到原选区结束点+1
        const insertAt = selectedNodePos + selectedNodeSize + offset;
        const newContent = props.editor.schema.text(llmResult.value.content);
        tr.insert(insertAt, newContent);

        // 记录插入区域
        markInsertArea.value = [insertAt, insertAt + llmResult.value.content.length];

        tr.addMark(
          markInsertArea.value[0],
          markInsertArea.value[1],
          props.editor.schema.marks.trackChange.create({
            type: 'insertion',
            author: 'AI Assistant',
            timestamp: Date.now(),
            status: 'pending',
            comment: 'AI 建议添加此内容',
          }),
        );
      } else {
        // 没有选中文本，直接插入到 insertPos
        const newContent = props.editor.schema.text(llmResult.value.content);
        tr.insert(insertPos, newContent);

        // 记录插入区域
        markInsertArea.value = [insertPos, insertPos + llmResult.value.content.length];

        tr.addMark(
          markInsertArea.value[0],
          markInsertArea.value[1],
          props.editor.schema.marks.trackChange.create({
            type: 'insertion',
            author: 'AI Assistant',
            timestamp: Date.now(),
            status: 'pending',
            comment: 'AI 建议添加此内容',
          }),
        );
      }

      // 更新 insertPos 属性，但不删除 agentWriter 节点
      props.updateAttributes({ insertPos });
      return true;
    })
    .run();
  llmResponsed.value = true;
};

const handleFinalConfirm = () => {
  if (!props.editor) return;
  const pos = props.getPos();
  const originalInsertPos = props.node.attrs.originalInsertPos;

  props.editor
    .chain()
    .focus()
    .command(({ tr }) => {
      // 1. 处理待删除的内容（只在有选中文本时执行）
      if (selectedText && selectedNodePos !== undefined && selectedNodeSize !== undefined) {
        const toDelete: { from: number; to: number }[] = [];
        tr.doc.descendants((node, nodePos) => {
          node.marks.forEach((mark) => {
            if (
              mark.type.name === 'trackChange' &&
              mark.attrs.status === 'pending' &&
              mark.attrs.type === 'deletion'
            ) {
              toDelete.push({ from: nodePos, to: nodePos + node.nodeSize });
            }
          });
        });
        // 从后向前删除，避免位置偏移
        toDelete.reverse().forEach(({ from, to }) => {
          tr.delete(from, to);
        });
      }

      // 2. 移除所有待插入内容的标记
      tr.doc.descendants((node, nodePos) => {
        node.marks.forEach((mark) => {
          if (
            mark.type.name === 'trackChange' &&
            mark.attrs.status === 'pending' &&
            mark.attrs.type === 'insertion'
          ) {
            tr.removeMark(nodePos, nodePos + node.nodeSize, mark.type);
          }
        });
      });

      // 3. 删除 agentWriter 节点
      tr.delete(pos, pos + props.node.nodeSize);

      // 4. 不再插入新内容，accept 时已插入

      return true;
    })
    .run();

  // 只恢复光标，不恢复选区
  if (originalInsertPos !== undefined && originalInsertPos !== null) {
    props.editor.commands.setTextSelection(originalInsertPos);
  }
};

const reset = () => {
  // 如果有标记的区域，需要恢复原始状态
  if (props.editor && (markInsertArea.value || markDeleteArea.value)) {
    props.editor
      .chain()
      .focus()
      .command(({ tr }) => {
        // 如果有插入区域，删除插入的内容
        if (markInsertArea.value) {
          tr.delete(markInsertArea.value[0], markInsertArea.value[1]);
        }

        // 如果有删除区域，恢复删除标记
        if (markDeleteArea.value) {
          tr.removeMark(
            markDeleteArea.value[0],
            markDeleteArea.value[1],
            props.editor.schema.marks.trackChange,
          );
        }

        return true;
      })
      .run();
  }

  // 重置标记区域
  markInsertArea.value = null;
  markDeleteArea.value = null;
};

const cancel = () => {
  llmResult.value = {
    content: '',
    reasoning_content: '',
  };
  // 如果正在生成，先中止生成过程
  if (isGenerating.value) {
    abort();
    // 中止后直接返回，等待生成过程完全停止
    return;
  }

  // 只有在非生成状态下才执行重置和移除节点
  reset();
  removeNode();
};
const removeNode = () => {
  if (!props.editor) return;

  const pos = props.getPos();
  const originalInsertPos = props.node.attrs.originalInsertPos;
  const selectedNodePos = props.node.attrs.selectedNodePos;
  const selectedNodeSize = props.node.attrs.selectedNodeSize;

  props.editor
    .chain()
    .focus()
    .command(({ tr }) => {
      tr.delete(pos, pos + props.node.nodeSize);
      return true;
    })
    .run();

  // 优先恢复选区，否则恢复光标
  if (
    selectedNodePos !== undefined &&
    selectedNodePos !== null &&
    selectedNodeSize !== undefined &&
    selectedNodeSize !== null
  ) {
    props.editor.commands.setTextSelection({
      from: selectedNodePos,
      to: selectedNodePos + selectedNodeSize,
    });
  } else if (originalInsertPos !== undefined && originalInsertPos !== null) {
    props.editor.commands.setTextSelection(originalInsertPos);
  }
};
const handleKeyDown = (event: KeyboardEvent) => {
  // Ctrl + Enter 触发接受
  if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
    event.preventDefault();
    if (!message.value) {
      return;
    } else {
      if (!llmResponsed.value) {
        void handleGenerate(message.value);
      } else {
        handleFinalConfirm();
      }
    }
  }
  // Esc 删除节点
  else if (event.key === 'Escape') {
    event.preventDefault();
    cancel();
  }
};

// 聚焦输入框的函数
const focusInput = async () => {
  await nextTick();
  if (inputRef.value && typeof inputRef.value.focus === 'function') {
    inputRef.value.focus();
  }
};

onMounted(() => {
  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown);
  // 确保编辑器失去焦点，这样不会触发额外的节点插入
  if (props.editor) {
    props.editor.commands.blur();
  }
  // 组件挂载后聚焦输入框
  setTimeout(() => {
    void focusInput();
  }, 100);
});

onBeforeUnmount(() => {
  // 移除键盘事件监听
  window.removeEventListener('keydown', handleKeyDown);
});
</script>
