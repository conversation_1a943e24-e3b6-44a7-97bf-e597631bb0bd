# Pexels 媒体浏览器 - 快速开始

## 🚀 快速开始

### 1. 配置 Pexels API

首先需要获取 Pexels API Key：

1. 访问 [Pexels API](https://www.pexels.com/api/)
2. 注册账户并创建应用
3. 获取 API Key

### 2. 在应用中配置

在应用设置中配置 Pexels API：

```typescript
// 通过 UI Store 配置
uiStore.perferences.llm.pexels = {
  apiKey: "YOUR_PEXELS_API_KEY",
  baseUrl: "https://api.pexels.com",
  maxResults: 12
}
```

### 3. 使用组件

#### 基本使用

```vue
<template>
  <div class="media-container">
    <PexelsMediaBrowser />
  </div>
</template>

<script setup lang="ts">
import PexelsMediaBrowser from 'src/components/PexelsMediaBrowser.vue';
</script>
```

#### 带事件处理的使用

```vue
<template>
  <div class="media-container">
    <PexelsMediaBrowser @media-selected="handleMediaSelected" />
  </div>
</template>

<script setup lang="ts">
import PexelsMediaBrowser from 'src/components/PexelsMediaBrowser.vue';
import type { PexelsPhoto, PexelsVideo } from 'src/env.d';

const handleMediaSelected = (media: PexelsPhoto | PexelsVideo, type: 'photo' | 'video') => {
  console.log('选择的媒体:', media, '类型:', type);
  // 处理选择的媒体
};
</script>
```

## 📱 功能特性

### ✅ 已实现功能

- ✅ 图片搜索和预览
- ✅ 视频搜索和预览  
- ✅ 响应式网格布局（6列 → 4列 → 3列 → 2列 → 1列）
- ✅ 完整分页功能（每页12条）
- ✅ 搜索框和媒体类型切换
- ✅ 图片预览（hevue-img-preview）
- ✅ 视频播放（artplayer）
- ✅ 媒体选择事件
- ✅ 加载状态和错误处理
- ✅ 深色主题支持

### 🎯 核心配置

```typescript
// 默认配置
{
  apiKey: '',                    // Pexels API Key
  baseUrl: 'https://api.pexels.com',  // API 基础地址
  maxResults: 12                 // 每页结果数
}
```

## 🔧 测试和调试

### 测试页面

访问 `/pexels-test` 路由查看组件演示：

```
http://localhost:9000/#/pexels-test
```

### 集成示例

查看完整的集成示例：

```vue
import PexelsIntegrationExample from 'src/components/examples/PexelsIntegrationExample.vue';
```

## 📋 API 端点

### 图片搜索
```
GET https://api.pexels.com/v1/search
参数:
- query: 搜索关键词
- per_page: 每页结果数 (默认12)
- page: 页码
```

### 视频搜索
```
GET https://api.pexels.com/videos/search
参数:
- query: 搜索关键词
- per_page: 每页结果数 (默认12)
- page: 页码
```

## 🎨 样式定制

### 网格布局

```scss
// 自定义网格列数
.photos-grid,
.videos-grid {
  grid-template-columns: repeat(6, 1fr); // 桌面端6列
}

@media (max-width: 1400px) {
  .photos-grid,
  .videos-grid {
    grid-template-columns: repeat(4, 1fr); // 平板端4列
  }
}
```

### 主题适配

```scss
// 深色主题
.body--dark .search-section {
  border-color: rgba(255, 255, 255, 0.12);
}
```

## 🔍 故障排除

### 常见问题

1. **API Key 错误**
   ```
   错误: Pexels API credentials are not configured
   解决: 检查 uiStore.perferences.llm.pexels.apiKey 是否正确设置
   ```

2. **网络请求失败**
   ```
   错误: API request failed: 401 Unauthorized
   解决: 验证 API Key 是否有效
   ```

3. **视频播放问题**
   ```
   错误: 视频无法播放
   解决: 检查浏览器是否支持视频格式，确认网络连接
   ```

4. **图片预览问题**
   ```
   错误: 图片预览不显示
   解决: 确认 hevue-img-preview 已正确安装和导入
   ```

### 调试技巧

1. **开启控制台日志**
   ```javascript
   // 组件内部有详细的日志输出
   console.log('🖼️ [Pexels Search] 开始搜索:', options.query);
   console.log('🎬 [Pexels Video Search] 搜索成功，视频数:', data.videos?.length);
   ```

2. **检查网络请求**
   ```javascript
   // 在浏览器开发者工具的 Network 标签中查看 API 请求
   ```

3. **验证配置**
   ```javascript
   // 在控制台中检查配置
   console.log(uiStore.perferences.llm.pexels);
   ```

## 📚 相关文档

- [Pexels API 文档](https://www.pexels.com/api/documentation/)
- [hevue-img-preview 文档](https://heyongsheng.github.io/guide/getting-started.html)
- [artplayer 文档](https://artplayer.org/document/)
- [完整功能文档](./PEXELS_MEDIA_BROWSER.md)

## 🎯 下一步

1. 在应用设置中配置 Pexels API Key
2. 访问测试页面验证功能
3. 根据需要集成到你的组件中
4. 自定义样式和布局

## 💡 提示

- 建议在生产环境中设置合理的请求频率限制
- 可以根据需要调整每页显示的媒体数量
- 支持键盘导航和无障碍访问
- 所有媒体内容遵循 Pexels License
