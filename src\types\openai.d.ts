/**
 * OpenAI API 类型定义
 * 基于 OpenAI Chat Completions API 文档
 * @see https://platform.openai.com/docs/api-reference/chat
 */

import type { CategorizedModels } from './modelCategories';

export interface OpenAISettings {
  baseUrl: string;
  apiKey: string;
  helpUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  logitBias?: Record<string, number>;
  seed?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  response_format?: ResponseFormat;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
}

// ================ 基础类型定义 ================

/** 消息角色类型 */
type MessageRole = 'system' | 'user' | 'assistant' | 'tool';

/** 完成原因 */
type FinishReason = 'stop' | 'length' | 'tool_calls' | 'content_filter';

/** 工具类型 */
type ToolType = 'function';

/** 工具选择策略 */
type ToolChoice = 'auto' | 'none' | { type: 'function'; function: { name: string } };

/** 响应格式类型 */
type ResponseFormat = { type: 'text' } | { type: 'json_object' };

// ================ 消息相关类型定义 ================

/** 基础消息接口 */
interface BaseMessage {
  role: MessageRole;
}

/** 系统消息 */
interface SystemMessage extends BaseMessage {
  role: 'system';
  content: string;
}

/** 用户消息 */
interface UserMessage extends BaseMessage {
  role: 'user';
  content:
    | string
    | Array<{ type: 'text'; text: string } | { type: 'image_url'; image_url: { url: string } }>;
}

/** 助手消息 */
interface AssistantMessage extends BaseMessage {
  role: 'assistant';
  content?: string | null;
  tool_calls?: ToolCall[];
}

/** 工具消息 */
interface ToolMessage extends BaseMessage {
  role: 'tool';
  content: string;
  tool_call_id: string;
}

/** 消息类型联合 */
type Message = SystemMessage | UserMessage | AssistantMessage | ToolMessage;

// ================ 工具调用相关类型定义 ================

/** JSON Schema 类型定义 */
interface JSONSchema {
  type?: string;
  properties?: Record<string, JSONSchema>;
  required?: string[];
  items?: JSONSchema;
  additionalProperties?: boolean | JSONSchema;
  description?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  enum?: any[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

/** 函数定义 */
interface FunctionDefinition {
  name: string;
  description?: string;
  parameters: JSONSchema;
}

/** 工具定义 */
interface Tool {
  type: ToolType;
  function: FunctionDefinition;
}

/** 函数调用 */
interface FunctionCall {
  name: string;
  arguments: string;
}

/** 工具调用 */
interface ToolCall {
  id: string;
  type: ToolType;
  function: FunctionCall;
}

// ================ 请求参数类型定义 ================

/** 聊天请求参数 */
interface ChatCompletionRequest {
  /** 模型名称（必选） */
  model: string;

  /** 消息列表（必选） */
  messages: Message[];

  /** 是否流式输出（可选，默认false） */
  stream?: boolean;

  /** 流式输出选项（可选） */
  stream_options?: {
    include_usage?: boolean;
  };

  /** 采样温度（可选，范围：0-2） */
  temperature?: number;

  /** 核采样概率（可选，范围：0-1） */
  top_p?: number;

  /** 生成数量（可选，默认1） */
  n?: number;

  /** 停止序列（可选） */
  stop?: string | string[] | null;

  /** 最大令牌数（可选） */
  max_tokens?: number | null;

  /** 存在惩罚（可选，范围：-2.0到2.0） */
  presence_penalty?: number;

  /** 频率惩罚（可选，范围：-2.0到2.0） */
  frequency_penalty?: number;

  /** Logit偏差（可选） */
  logit_bias?: Record<string, number> | null;

  /** 用户标识（可选） */
  user?: string;

  /** 响应格式（可选） */
  response_format?: ResponseFormat;

  /** 随机种子（可选） */
  seed?: number | null;

  /** 工具列表（可选） */
  tools?: Tool[];

  /** 工具选择策略（可选） */
  tool_choice?: ToolChoice;

  /** 是否并行调用工具（可选） */
  parallel_tool_calls?: boolean;
}

// ================ 响应类型定义 ================

/** Token 使用统计 */
interface Usage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

/** 选择项 */
interface Choice {
  index: number;
  message?: {
    role: 'assistant';
    content?: string | null;
    tool_calls?: ToolCall[];
  };
  delta?: {
    role?: 'assistant';
    content?: string | null;
    tool_calls?: Array<{
      index: number;
      id?: string;
      type?: 'function';
      function?: {
        name?: string;
        arguments?: string;
      };
    }>;
  };
  finish_reason: FinishReason | null;
}

/** 聊天完成响应 */
interface ChatCompletionResponse {
  id: string;
  object: 'chat.completion' | 'chat.completion.chunk';
  created: number;
  model: string;
  system_fingerprint?: string;
  choices: Choice[];
  usage?: Usage;
}

// ================ 导出类型 ================

export type {
  MessageRole,
  FinishReason,
  ToolType,
  ToolChoice,
  ResponseFormat,
  BaseMessage,
  SystemMessage,
  UserMessage,
  AssistantMessage,
  ToolMessage,
  Message,
  Tool,
  ToolCall,
  FunctionCall,
  FunctionDefinition,
  ChatCompletionRequest,
  ChatCompletionResponse,
  Choice,
  Usage,
};
