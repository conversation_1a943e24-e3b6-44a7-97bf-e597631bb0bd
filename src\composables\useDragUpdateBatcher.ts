import { ref } from 'vue';

interface FlexUpdate {
  flex: string;
}

interface PaneSizeUpdate {
  leftIndex: number;
  rightIndex: number;
  leftFlex: string;
  rightFlex: string;
}

/**
 * 拖拽更新批处理器
 * 用于批量处理拖拽时的面板大小更新，减少DOM操作频率
 */
export class DragUpdateBatcher {
  private pendingUpdates = new Map<number, FlexUpdate>();
  private rafId: number | null = null;
  private updateCallback: ((updates: Map<number, FlexUpdate>) => void) | null = null;

  constructor(callback: (updates: Map<number, FlexUpdate>) => void) {
    this.updateCallback = callback;
  }

  /**
   * 调度单个面板的更新
   */
  scheduleUpdate(paneIndex: number, flexValue: string) {
    this.pendingUpdates.set(paneIndex, { flex: flexValue });

    if (!this.rafId) {
      this.rafId = requestAnimationFrame(() => this.flushUpdates());
    }
  }

  /**
   * 调度面板对的更新（拖拽时通常是两个相邻面板）
   */
  schedulePairUpdate(leftIndex: number, rightIndex: number, leftFlex: string, rightFlex: string) {
    this.pendingUpdates.set(leftIndex, { flex: leftFlex });
    this.pendingUpdates.set(rightIndex, { flex: rightFlex });

    if (!this.rafId) {
      this.rafId = requestAnimationFrame(() => this.flushUpdates());
    }
  }

  /**
   * 立即刷新所有待处理的更新
   */
  private flushUpdates() {
    if (this.updateCallback && this.pendingUpdates.size > 0) {
      // 创建更新的副本，避免在回调中修改原始Map
      const updates = new Map(this.pendingUpdates);
      this.updateCallback(updates);
    }

    this.pendingUpdates.clear();
    this.rafId = null;
  }

  /**
   * 取消待处理的更新
   */
  cancel() {
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
    this.pendingUpdates.clear();
  }

  /**
   * 销毁批处理器
   */
  destroy() {
    this.cancel();
    this.updateCallback = null;
  }
}

/**
 * 拖拽更新批处理器的组合式函数
 */
export function useDragUpdateBatcher() {
  const batcher = ref<DragUpdateBatcher | null>(null);

  /**
   * 创建批处理器
   */
  const createBatcher = (callback: (updates: Map<number, FlexUpdate>) => void) => {
    if (batcher.value) {
      batcher.value.destroy();
    }
    batcher.value = new DragUpdateBatcher(callback);
    return batcher.value;
  };

  /**
   * 销毁批处理器
   */
  const destroyBatcher = () => {
    if (batcher.value) {
      batcher.value.destroy();
      batcher.value = null;
    }
  };

  return {
    batcher,
    createBatcher,
    destroyBatcher,
  };
}

/**
 * 拖拽性能监控器
 */
export class DragPerformanceMonitor {
  private startTime = 0;
  private frameCount = 0;
  private isMonitoring = false;

  start() {
    this.startTime = performance.now();
    this.frameCount = 0;
    this.isMonitoring = true;
  }

  frame() {
    if (this.isMonitoring) {
      this.frameCount++;
    }
  }

  end() {
    if (!this.isMonitoring) return;

    const duration = performance.now() - this.startTime;
    const fps = this.frameCount / (duration / 1000);

    if (fps < 30) {
      console.warn(
        `⚠️ [Splitter Drag] Performance Warning: ${fps.toFixed(1)} FPS, duration ${duration.toFixed(2)}ms`,
      );
    } else if (fps > 50) {
      console.log(
        `✅ [Splitter Drag] Good performance: ${fps.toFixed(1)} FPS, duration ${duration.toFixed(2)}ms`,
      );
    }

    this.isMonitoring = false;
  }
}

export type { FlexUpdate, PaneSizeUpdate };
