# Pexels 图片拖拽功能

## 功能概述

Pexels 媒体浏览器现在支持将图片拖拽到 TipTap 编辑器中，实现快速插入图片的功能。

## 实现细节

### 拖拽数据格式

当拖拽图片时，会设置以下数据：

```javascript
// 主要数据 - 图片URL (landscape 尺寸)
event.dataTransfer.setData('text/uri-list', photo.src.landscape);
event.dataTransfer.setData('text/plain', photo.src.landscape);

// 拖拽效果
event.dataTransfer.effectAllowed = 'copy';
```

### 图片尺寸选择

拖拽时使用 `photo.src.landscape` 尺寸，这是一个适中的尺寸，适合在编辑器中显示：

- **tiny**: 280x200 (太小)
- **small**: 130x130 (太小)  
- **medium**: 350x233 (较小)
- **large**: 940x627 (较大)
- **landscape**: 640x427 ✅ (推荐，适中)
- **original**: 原始尺寸 (可能很大)

## 用户体验

### 视觉反馈

1. **鼠标样式**：
   - 默认：`cursor: grab`
   - 拖拽时：`cursor: grabbing`

2. **拖拽预览**：
   - 使用 `photo.src.small` 作为拖拽时的预览图片
   - 预览图片大小：50x50 像素

3. **悬停效果**：
   - 图片项向上移动 2px
   - 添加阴影效果

### 交互逻辑

1. **点击预览**：点击预览按钮打开图片预览
2. **拖拽插入**：拖拽图片到编辑器插入图片
3. **选择功能**：点击加号按钮选择图片

## TipTap 编辑器集成

### 拖拽事件处理

TipTap 编辑器需要处理以下拖拽事件：

```javascript
// 在 TipTap 编辑器中处理拖拽
editor.on('drop', (view, event, slice, moved) => {
  const url = event.dataTransfer?.getData('text/uri-list');
  if (url && url.startsWith('http')) {
    // 插入图片节点
    const { schema } = view.state;
    const node = schema.nodes.image.create({ src: url });
    const transaction = view.state.tr.insert(view.posAtCoords({
      left: event.clientX,
      top: event.clientY
    })?.pos || 0, node);
    view.dispatch(transaction);
    return true; // 阻止默认处理
  }
  return false;
});
```

### 图片节点创建

```javascript
// 创建图片节点
const imageNode = {
  type: 'image',
  attrs: {
    src: draggedImageUrl,
    alt: 'Pexels image',
    title: 'Image from Pexels'
  }
};
```

## 测试方法

### 基本测试

1. 打开 Pexels 媒体浏览器
2. 搜索图片（例如："nature"）
3. 将鼠标悬停在图片上，确认光标变为 `grab`
4. 按住鼠标左键开始拖拽
5. 拖拽到 TipTap 编辑器区域
6. 释放鼠标，图片应该插入到编辑器中

### 控制台日志

拖拽过程中会输出以下日志：

```
🖼️ [Drag Start] 开始拖拽图片: https://images.pexels.com/photos/.../landscape.jpg
🖼️ [Drag End] 拖拽结束
```

### 数据验证

可以在浏览器开发者工具中验证拖拽数据：

```javascript
// 在 dragstart 事件中
console.log('URI List:', event.dataTransfer.getData('text/uri-list'));
console.log('Plain Text:', event.dataTransfer.getData('text/plain'));
```

## 故障排除

### 常见问题

1. **拖拽不工作**
   - 检查 `draggable="true"` 属性是否正确设置
   - 确认 `handleDragStart` 函数是否正确绑定

2. **图片无法插入编辑器**
   - 检查 TipTap 编辑器是否正确处理 drop 事件
   - 验证图片 URL 是否有效

3. **拖拽预览不显示**
   - 检查网络连接，确保预览图片能够加载
   - 验证 `photo.src.small` URL 是否有效

### 调试技巧

1. **启用拖拽日志**：
   ```javascript
   // 在拖拽函数中添加更多日志
   console.log('Drag data:', {
     uriList: event.dataTransfer.getData('text/uri-list'),
     plainText: event.dataTransfer.getData('text/plain')
   });
   ```

2. **检查拖拽数据**：
   ```javascript
   // 在目标元素的 drop 事件中
   event.dataTransfer.types.forEach(type => {
     console.log(`${type}:`, event.dataTransfer.getData(type));
   });
   ```

## 扩展功能

### 可能的改进

1. **多图片拖拽**：支持同时拖拽多张图片
2. **拖拽反馈**：显示拖拽目标区域高亮
3. **尺寸选择**：允许用户选择拖拽的图片尺寸
4. **元数据传递**：传递图片的作者信息等元数据

### 自定义配置

```javascript
// 可配置的拖拽选项
const dragOptions = {
  imageSize: 'landscape', // 拖拽时使用的图片尺寸
  previewSize: 'small',   // 拖拽预览使用的图片尺寸
  effectAllowed: 'copy',  // 拖拽效果
  includeMetadata: true   // 是否包含元数据
};
```

## 兼容性

- ✅ Chrome 88+
- ✅ Firefox 85+
- ✅ Safari 14+
- ✅ Edge 88+

## 相关文档

- [HTML Drag and Drop API](https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API)
- [TipTap 拖拽处理](https://tiptap.dev/guide/custom-extensions#drag-and-drop)
- [Pexels API 图片尺寸](https://www.pexels.com/api/documentation/#photos-search)
