import type { Document } from 'src/env';

export interface SplitterWindow {
  id: number;
  flex?: string;
  documents?: Document[]; // 面板中的文档列表
  activeDocumentId?: number | null; // 当前活动文档ID
  folderId?: number; // 文档所属文件夹ID
}

// 面板布局状态
export interface SplitterLayout {
  windows: SplitterWindow[];
  activeWindowId: string | null;
  rootWindowId: string | null; // 根面板ID
}

// 面板操作选项
export interface SplitOptions {
  position: 'left' | 'right'; // 分割位置
  width: number; // 新面板的初始宽度
}
