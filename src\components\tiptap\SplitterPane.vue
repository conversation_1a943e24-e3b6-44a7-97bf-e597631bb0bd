<template>
  <div
    class="split-pane column no-wrap border-inset"
    :style="{ flex: pane.flex }"
    @click="handlePaneClick"
    :class="isActive ? 'border-active' : 'border-placeholder'"
  >
    <EditorGroup
      :documents="pane.documents"
      :paneIndex="paneIndex"
      :winId="pane.id"
      :folderId="pane.folderId"
      @onReady="handleReady"
      @onRemoveDoc="handleRemoveDoc"
      @onDocumentChange="handleDocumentChange"
      @setPaneIndex="handleSetPaneIndex"
    />
  </div>
</template>

<script setup lang="ts">
import type { SplitterWindow } from 'src/types/splitterWindow';
import EditorGroup from '../EditorGroup.vue';

interface Props {
  pane: SplitterWindow;
  paneIndex: number;
  isActive: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'ready', paneId: number, docId: number): void;
  (e: 'removeDoc', docId: number): void;
  (e: 'documentChange', docId: number): void;
  (e: 'paneClick', index: number): void;
}>();

const handlePaneClick = () => {
  emit('paneClick', props.paneIndex);
};

const handleReady = (docId: number) => {
  emit('ready', props.pane.id, docId);
};

const handleRemoveDoc = (docId: number) => {
  emit('removeDoc', docId);
};

const handleDocumentChange = (docId: number) => {
  emit('documentChange', docId);
};

const handleSetPaneIndex = (paneIndex: number) => {
  emit('paneClick', paneIndex);
};
</script>

<style scoped>
.split-pane {
  min-width: 0;
  height: calc(100% - 2px);
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化重绘性能 */
  contain: layout style;
  /* 优化合成层 */
  will-change: auto;
}
</style>
