import { useUiStore } from 'src/stores/ui';
import { useDocStore } from 'src/stores/doc';
import { useLlmStore } from 'src/stores/llm';
import { wrapStoreWithDebug } from 'src/utils/storeDebugger';

// 定义 store 类型
type UiStoreType = ReturnType<typeof useUiStore>;
type DocStoreType = ReturnType<typeof useDocStore>;
type LlmStoreType = ReturnType<typeof useLlmStore>;

// 在开发环境下包装 store 函数以便调试
const wrappedUseUiStore =
  process.env.NODE_ENV === 'development' ? wrapStoreWithDebug('uiStore', useUiStore) : useUiStore;

const wrappedUseDocStore =
  process.env.NODE_ENV === 'development'
    ? wrapStoreWithDebug('docStore', useDocStore)
    : useDocStore;

const wrappedUseLlmStore =
  process.env.NODE_ENV === 'development'
    ? wrapStoreWithDebug('llmStore', useLlmStore)
    : useLlmStore;

// 单例存储
const _uiStore: UiStoreType | null = null;
const _docStore: DocStoreType | null = null;
const _llmStore: LlmStoreType | null = null;

// 创建缓存对象
const uiStoreCache = { value: _uiStore };
const docStoreCache = { value: _docStore };
const llmStoreCache = { value: _llmStore };

// 创建具有正确类型的 store getter
function createTypedStoreGetter<T extends object>(
  name: string,
  useStore: () => T,
  cache: { value: T | null },
): T {
  const handler: ProxyHandler<T> = {
    get(target, prop) {
      if (!cache.value) {
        try {
          cache.value = useStore();
        } catch (error) {
          console.error(`Failed to initialize ${name} store:`, error);
          if (process.env.NODE_ENV === 'development') {
            console.error('Stack trace:', new Error().stack);
          }
          throw error;
        }
      }
      return cache.value[prop as keyof T];
    },
    set(target, prop, value) {
      if (!cache.value) {
        try {
          cache.value = useStore();
        } catch (error) {
          console.error(`Failed to initialize ${name} store:`, error);
          if (process.env.NODE_ENV === 'development') {
            console.error('Stack trace:', new Error().stack);
          }
          throw error;
        }
      }
      cache.value[prop as keyof T] = value;
      return true;
    },
    has(target, prop) {
      if (!cache.value) {
        cache.value = useStore();
      }
      return prop in cache.value;
    },
    ownKeys() {
      if (!cache.value) {
        cache.value = useStore();
      }
      return Object.keys(cache.value) as (string | symbol)[];
    },
    getOwnPropertyDescriptor(target, prop) {
      if (!cache.value) {
        cache.value = useStore();
      }
      return Object.getOwnPropertyDescriptor(cache.value, prop);
    },
  };

  return new Proxy({} as T, handler);
}

// 导出具有正确类型的 store 代理
export const uiStore: UiStoreType = createTypedStoreGetter('ui', wrappedUseUiStore, uiStoreCache);
export const docStore: DocStoreType = createTypedStoreGetter(
  'doc',
  wrappedUseDocStore,
  docStoreCache,
);
export const llmStore: LlmStoreType = createTypedStoreGetter(
  'llm',
  wrappedUseLlmStore,
  llmStoreCache,
);

// 保留函数式调用方式作为备选
export const getUiStore = (): UiStoreType => {
  if (!uiStoreCache.value) {
    uiStoreCache.value = wrappedUseUiStore();
  }
  return uiStoreCache.value;
};

export const getDocStore = (): DocStoreType => {
  if (!docStoreCache.value) {
    docStoreCache.value = wrappedUseDocStore();
  }
  return docStoreCache.value;
};

export const getLlmStore = (): LlmStoreType => {
  if (!llmStoreCache.value) {
    llmStoreCache.value = wrappedUseLlmStore();
  }
  return llmStoreCache.value;
};

// 用于调试的工具函数
export const resetStores = () => {
  uiStoreCache.value = null;
  docStoreCache.value = null;
  llmStoreCache.value = null;
};

// 导出类型供其他地方使用
export type { UiStoreType, DocStoreType, LlmStoreType };
