#include "settings.h"
#include <QDebug>
#include <QStyleHints>
#include <QGuiApplication>
#include <QPalette>
#include <QApplication>
#include <QFile>
#include <QTextStream>

const QString THEME_KEY = "theme/isDark";
const QString WINDOW_GEOMETRY_KEY = "window/geometry";
const QString WINDOW_STATE_KEY = "window/isMaximized";

Settings::Settings()
    : m_settings(QSettings::IniFormat, QSettings::UserScope, "YourCompany", "InkCop")
{
}

void Settings::saveTheme(bool isDark)
{
    m_settings.setValue(THEME_KEY, isDark);
}

bool Settings::getSavedTheme() const
{
    // 如果键不存在，toBool() 会返回 false，此时探测系统主题
    if (!hasThemeSetting())
    {
        qDebug() << "没有找到保存的主题设置，探测系统主题...";
        return detectSystemTheme();
    }

    bool isDark = m_settings.value(THEME_KEY, false).toBool();

    // 如果主题为暗色，修改 index.html
    if (isDark)
    {
        QFile file("index.html");
        if (file.open(QIODevice::ReadWrite | QIODevice::Text))
        {
            QTextStream in(&file);
            QString content = in.readAll();

            // 检查 body 标签是否已经有 body--dark class
            if (!content.contains("class=\"body--dark\"") && !content.contains("class='body--dark'"))
            {
                // 在 body 标签中添加 body--dark class
                content.replace("<body", "<body class=\"body--dark\"");

                // 将文件指针移到开始位置
                file.seek(0);
                // 写入修改后的内容
                QTextStream out(&file);
                out << content;
                // 截断文件到当前位置
                file.resize(file.pos());
            }
            file.close();
        }
    }

    return isDark;
}

bool Settings::hasThemeSetting() const
{
    return m_settings.contains(THEME_KEY);
}

void Settings::syncSettings()
{
    m_settings.sync(); // 强制同步设置文件，确保文件状态是最新的
}

bool Settings::detectSystemTheme() const
{
    // 优先使用 QStyleHints 检测系统主题
    QStyleHints *styleHints = QGuiApplication::styleHints();
    if (styleHints)
    {
        // 检查是否支持暗色主题检测
        if (styleHints->colorScheme() != Qt::ColorScheme::Unknown)
        {
            bool isDark = (styleHints->colorScheme() == Qt::ColorScheme::Dark);
            qDebug() << "使用 QStyleHints 检测系统主题:" << (isDark ? "暗色" : "亮色");
            return isDark;
        }
    }

    // 如果 QStyleHints 不可用或不支持，回退到调色板检测方法
    qDebug() << "QStyleHints 不可用，使用调色板检测方法";

    // 方法1: 检测系统调色板
    QPalette defaultPalette = QApplication::palette();
    QColor windowColor = defaultPalette.color(QPalette::Window);
    QColor textColor = defaultPalette.color(QPalette::WindowText);
    QColor baseColor = defaultPalette.color(QPalette::Base);
    QColor buttonColor = defaultPalette.color(QPalette::Button);

    // 计算各种颜色的亮度
    int windowLightness = windowColor.lightness();
    int textLightness = textColor.lightness();
    int baseLightness = baseColor.lightness();
    int buttonLightness = buttonColor.lightness();

    // 综合判断方法：
    // 1. 背景色亮度较低
    // 2. 文本色亮度高于背景色（暗背景+亮文字）
    // 3. 整体色调偏暗
    double avgBackgroundLightness = (windowLightness + baseLightness + buttonLightness) / 3.0;
    bool isDarkByBrightness = avgBackgroundLightness < 128;
    bool isDarkByContrast = textLightness > windowLightness && (textLightness - windowLightness) > 100;
    bool isDarkByAverage = avgBackgroundLightness < 160 && textLightness > 180;

    // 如果满足任一条件，则认为是暗色主题
    bool isDark = isDarkByBrightness || isDarkByContrast || isDarkByAverage;

    qDebug() << "系统主题检测详细信息:";
    qDebug() << "  窗口颜色:" << windowColor.name() << "亮度:" << windowLightness;
    qDebug() << "  文本颜色:" << textColor.name() << "亮度:" << textLightness;
    qDebug() << "  基础颜色:" << baseColor.name() << "亮度:" << baseLightness;
    qDebug() << "  按钮颜色:" << buttonColor.name() << "亮度:" << buttonLightness;
    qDebug() << "  平均背景亮度:" << avgBackgroundLightness;
    qDebug() << "  按亮度判断暗色:" << isDarkByBrightness;
    qDebug() << "  按对比度判断暗色:" << isDarkByContrast;
    qDebug() << "  按综合判断暗色:" << isDarkByAverage;
    qDebug() << "  最终检测结果: 暗色主题 =" << isDark;

    // 额外的强制检测
    if (!isDark && windowLightness < 80)
    {
        isDark = true;
        qDebug() << "强制设置为暗色主题（窗口亮度极低）";
    }
    return isDark;
}

// 窗口几何设置
void Settings::saveWindowGeometry(const QRect &geometry)
{
    m_settings.setValue(WINDOW_GEOMETRY_KEY, geometry);
}

QRect Settings::getSavedWindowGeometry() const
{
    return m_settings.value(WINDOW_GEOMETRY_KEY, QRect()).toRect();
}

bool Settings::hasWindowGeometry() const
{
    return m_settings.contains(WINDOW_GEOMETRY_KEY);
}

void Settings::saveWindowState(bool isMaximized)
{
    m_settings.setValue(WINDOW_STATE_KEY, isMaximized);
}

bool Settings::getSavedWindowState() const
{
    return m_settings.value(WINDOW_STATE_KEY, false).toBool();
}

bool Settings::hasWindowState() const
{
    return m_settings.contains(WINDOW_STATE_KEY);
}