# Mica效果与自定义标题栏背景冲突修复

## 问题描述

在启用QWindowKit的Mica效果后，发现自定义标题栏的实色背景会完全覆盖Mica的半透明毛玻璃效果，导致Mica效果无法正常显示。

## 问题原因

1. **Mica效果已启用**：在`mainwindow.cpp`中设置了`m_windowAgent->setWindowAttribute("mica", true)`
2. **标题栏有实色背景**：在`QWKCustomTitleBar::applyTheme()`中设置了`background-color: #1e1e1e`（深色）或`#ffffff`（浅色）
3. **层级冲突**：标题栏的实色背景位于Mica效果之上，完全遮挡了毛玻璃效果

## 解决方案

### 1. 添加Mica效果状态管理

在`QWKCustomTitleBar`类中添加了Mica效果状态管理：

```cpp
// 头文件中添加
private:
    bool m_micaEffectEnabled;

public:
    void setMicaEffectEnabled(bool enabled);
    bool isMicaEffectEnabled() const;
```

### 2. 动态背景色选择

修改`applyTheme()`方法，根据Mica效果状态动态选择背景色：

```cpp
void QWKCustomTitleBar::applyTheme()
{
    // 根据是否使用Mica效果选择背景色
    QString bgColor;
    if (m_micaEffectEnabled)
    {
        // 使用透明背景让Mica效果显示
        bgColor = "transparent";
    }
    else
    {
        // 使用实色背景
        bgColor = m_isDarkTheme ? "#1e1e1e" : "#ffffff";
    }
    // ... 其余样式设置
}
```

### 3. 主窗口集成

在`MainWindow::setupUI()`中同时启用Mica效果和通知标题栏：

```cpp
#ifdef Q_OS_WIN
    // 启用Mica效果并通知标题栏
    m_windowAgent->setWindowAttribute("mica", true);
    m_qwkTitleBar->setMicaEffectEnabled(true);
#endif
```

## 技术细节

### Mica效果工作原理
- Mica是Windows 11的新视觉效果，提供半透明的毛玻璃背景
- 需要窗口背景为透明才能正确显示
- 任何实色背景都会覆盖Mica效果

### 透明背景的影响
- 使用`background-color: transparent`让Mica效果透过
- 按钮悬停效果仍然正常工作（使用半透明颜色）
- 文字颜色根据主题自动调整

### 兼容性考虑
- 只在Windows平台启用Mica效果
- 非Windows平台或不支持Mica时使用实色背景
- 向后兼容现有的主题系统

## 使用方法

### 启用Mica效果
```cpp
// 在MainWindow中
m_windowAgent->setWindowAttribute("mica", true);
m_qwkTitleBar->setMicaEffectEnabled(true);
```

### 禁用Mica效果
```cpp
// 恢复实色背景
m_windowAgent->setWindowAttribute("mica", false);
m_qwkTitleBar->setMicaEffectEnabled(false);
```

### 检查当前状态
```cpp
bool micaEnabled = m_qwkTitleBar->isMicaEffectEnabled();
```

## 效果对比

### 修复前
- 标题栏显示实色背景（#1e1e1e 或 #ffffff）
- Mica效果被完全遮挡
- 窗口看起来像普通的实色窗口

### 修复后
- 标题栏背景透明，Mica效果可见
- 保持半透明毛玻璃质感
- 按钮交互效果正常
- 文字清晰可读

## 注意事项

1. **Windows版本要求**：Mica效果需要Windows 11
2. **性能影响**：Mica效果可能对性能有轻微影响
3. **主题一致性**：确保透明背景下的文字对比度足够
4. **测试建议**：在不同Windows版本和主题下测试效果

## 相关文件

- `qt-src/qwkcustomtitlebar.h` - 添加Mica状态管理接口
- `qt-src/qwkcustomtitlebar.cpp` - 实现动态背景选择逻辑
- `qt-src/mainwindow.cpp` - 集成Mica效果启用

## 未来改进

1. 可以添加用户设置选项来开关Mica效果
2. 支持其他Windows视觉效果（Acrylic等）
3. 根据系统主题自动调整效果强度
