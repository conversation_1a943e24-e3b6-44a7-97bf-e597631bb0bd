<template>
  <node-view-wrapper class="overflow-hidden radius-sm">
    <q-responsive :ratio="4 / 3">
      <q-card bordered class="fit column radius-sm overflow-hidden">
        <q-bar dark class="transparent border-bottom">
          <q-space />
          <q-btn dense flat icon="mdi-pencil" @click="openEditor" />
        </q-bar>
        <div ref="excalidrawDom" class="q-space" />
      </q-card>
      <q-dialog v-model="showEditor" full-height full-width @show="onDialogShow">
        <q-card bordered class="fit column">
          <q-bar
            class="q-px-xs"
            :style="`background-color: ${$q.dark.isActive ? '#121212' : '#fff'}`"
          >
            <q-space />
            <q-btn dense flat icon="close" @click="saveAndCloseEditor" />
          </q-bar>
          <div ref="excalidrawEditor" class="q-space" />
        </q-card>
      </q-dialog>
    </q-responsive>
  </node-view-wrapper>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { NodeViewWrapper } from '@tiptap/vue-3';
import type { NodeViewProps } from '@tiptap/core';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { Excalidraw } from '@excalidraw/excalidraw';
import { useQuasar } from 'quasar';
// import { useI18n } from 'vue-i18n';

const props = defineProps<NodeViewProps>();
const $q = useQuasar();
const showEditor = ref<boolean>(false);

let root = null;
let app = null;
let editorRoot = null;
let editorApp = null;

const STATE = ref({
  // 默认的 appState 配置
  viewBackgroundColor: '#fff', // 画布背景颜色
  currentItemStrokeColor: '#000000', // 当前绘画工具颜色
  currentItemBackgroundColor: '#ffffff', // 当前工具填充颜色
  activeTool: 'selection', // 默认工具为选择工具
  zoom: 1, // 缩放比例
});
const excalidrawDom = ref<HTMLDivElement | null>(null);
const excalidrawEditor = ref<HTMLDivElement | null>(null);
const isEditorInitializing = ref<boolean>(false);

// 初始化只读模式的 Excalidraw
const initializeExcalidraw = () => {
  try {
    if (!excalidrawDom.value) {
      console.error('Excalidraw container not found');
      return;
    }

    // 从节点属性中获取保存的数据
    const savedData = props.node.attrs.data;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let initialData: any;

    if (savedData && typeof savedData === 'string') {
      try {
        const parsedData = JSON.parse(savedData);

        // 验证数据结构
        if (parsedData && typeof parsedData === 'object') {
          initialData = {
            elements: Array.isArray(parsedData.elements) ? parsedData.elements : [],
            appState: parsedData.appState || STATE.value,
          };
          console.log('Loaded Excalidraw data from node:', {
            elementsCount: initialData.elements.length,
            hasAppState: !!initialData.appState,
            timestamp: parsedData.timestamp,
          });
        } else {
          throw new Error('Invalid data structure');
        }
      } catch (parseError) {
        console.warn('Failed to parse saved Excalidraw data:', parseError);
        initialData = { elements: [], appState: STATE.value };
      }
    } else {
      initialData = { elements: [], appState: STATE.value };
    }

    // 获取当前主题模式
    const isDarkMode = $q.dark.isActive;

    // 创建只读模式的 Excalidraw 组件
    const excalidrawElement = React.createElement(Excalidraw, {
      initialData: initialData,
      excalidrawAPI: handleExcalidrawAPI,
      langCode: 'zh-CN',
      // 主题设置：跟随 Quasar 应用的明暗模式
      theme: isDarkMode ? 'dark' : 'light',
      // 只读模式配置
      viewModeEnabled: true, // 启用只读模式
      zenModeEnabled: true, // 启用禅模式，隐藏工具栏
      gridModeEnabled: false,
      // 禁用所有 UI 功能
      UIOptions: {
        canvasActions: {
          clearCanvas: false,
          loadScene: false,
          saveToActiveFile: false,
          export: false,
          saveAsImage: false,
        },
      },
    });

    // 创建并渲染 React 根节点
    root = createRoot(excalidrawDom.value);
    root.render(excalidrawElement);

    // 设置焦点监听
    setupFocusListeners();

    console.log('Excalidraw readonly component initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Excalidraw:', error);
  }
};

// 初始化编辑模式的 Excalidraw
const initializeEditor = () => {
  try {
    if (!excalidrawEditor.value) {
      console.error('Excalidraw editor container not found');
      return;
    }

    // 防止重复初始化
    if (isEditorInitializing.value || editorRoot) {
      console.log('Editor is already initializing or initialized, skipping');
      return;
    }

    isEditorInitializing.value = true;
    console.log('Starting editor initialization');

    // 从节点属性中获取保存的数据
    const savedData = props.node.attrs.data;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let initialData: any;

    if (savedData && typeof savedData === 'string') {
      try {
        const parsedData = JSON.parse(savedData);
        if (parsedData && typeof parsedData === 'object') {
          initialData = {
            elements: Array.isArray(parsedData.elements) ? parsedData.elements : [],
            appState: parsedData.appState || STATE.value,
          };
        } else {
          throw new Error('Invalid data structure');
        }
      } catch (parseError) {
        console.warn('Failed to parse saved Excalidraw data:', parseError);
        initialData = { elements: [], appState: STATE.value };
      }
    } else {
      initialData = { elements: [], appState: STATE.value };
    }

    // 获取当前主题模式
    const isDarkMode = $q.dark.isActive;

    // 创建编辑模式的 Excalidraw 组件
    const excalidrawElement = React.createElement(Excalidraw, {
      initialData: initialData,
      onChange: handleDrawingChange,
      excalidrawAPI: handleEditorAPI,
      langCode: 'zh-CN',
      // 主题设置：跟随 Quasar 应用的明暗模式
      theme: isDarkMode ? 'dark' : 'light',
      // 编辑模式配置
      viewModeEnabled: false, // 禁用只读模式
      zenModeEnabled: false, // 禁用禅模式，显示工具栏
      gridModeEnabled: false,
      // 启用编辑功能
      UIOptions: {
        canvasActions: {
          clearCanvas: true,
          loadScene: false,
          saveToActiveFile: false,
          export: false,
          saveAsImage: true,
        },
      },
    });

    // 创建并渲染编辑器 React 根节点
    editorRoot = createRoot(excalidrawEditor.value);
    editorRoot.render(excalidrawElement);

    console.log('Excalidraw editor initialized successfully');

    // 重置初始化状态
    isEditorInitializing.value = false;
  } catch (error) {
    console.error('Failed to initialize Excalidraw editor:', error);
    // 重置初始化状态，允许重试
    isEditorInitializing.value = false;
  }
};

// 处理绘图变化，保存到节点属性
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleDrawingChange = (elements: any[], newAppState: any) => {
  try {
    // 验证输入参数
    if (!elements || !newAppState) {
      console.warn('Invalid parameters in handleDrawingChange:', { elements, newAppState });
      return;
    }

    // 安全地获取当前状态
    let state = newAppState;
    if (app && typeof app.getAppState === 'function') {
      try {
        state = app.getAppState();
      } catch (apiError) {
        console.warn('Failed to get app state from API, using newAppState:', apiError);
        state = newAppState;
      }
    }

    // 安全地处理状态对象
    let cleanAppState = {};
    if (state && typeof state === 'object') {
      // 创建状态副本，排除不需要的属性
      cleanAppState = { ...state };

      // 删除不需要序列化的属性
      if ('collaborators' in cleanAppState) {
        delete cleanAppState.collaborators;
      }
      if ('isLoading' in cleanAppState) {
        delete cleanAppState.isLoading;
      }
    }

    // 验证数据
    const validElements = Array.isArray(elements) ? elements : [];
    const validAppState = Object.keys(cleanAppState).length > 0 ? cleanAppState : STATE.value;

    // 创建要保存的数据
    const dataToSave = {
      elements: validElements,
      appState: validAppState,
      timestamp: Date.now(),
    };

    // 保存到节点属性
    const serializedData = JSON.stringify(dataToSave);
    props.updateAttributes({ data: serializedData });
  } catch (error) {
    console.error('Failed to save Excalidraw data:', error);
  }
};

// 处理只读模式 Excalidraw API
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleExcalidrawAPI = (api: any) => {
  app = api;
};

// 处理编辑模式 Excalidraw API
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleEditorAPI = (api: any) => {
  editorApp = api;
};

// 打开编辑器
const openEditor = () => {
  console.log('Opening editor dialog');
  showEditor.value = true;
  // 初始化由 watch 监听器处理，避免重复调用
};

// 弹框显示完成后的回调
const onDialogShow = () => {
  console.log('Dialog show event triggered');
  // 弹框显示动画完成后，初始化编辑器
  setTimeout(() => {
    if (excalidrawEditor.value && !editorRoot) {
      console.log('Dialog fully shown, initializing editor');
      initializeEditor();
    }
  }, 200); // 适中的延迟时间
};

// 保存编辑器内容并关闭
const saveAndCloseEditor = () => {
  if (editorApp) {
    try {
      // 获取编辑器中的数据
      const elements = editorApp.getSceneElements();
      const appState = editorApp.getAppState();

      // 保存数据到节点属性
      const dataToSave = {
        elements: elements,
        appState: {
          viewBackgroundColor: appState.viewBackgroundColor,
          currentItemStrokeColor: appState.currentItemStrokeColor,
          currentItemBackgroundColor: appState.currentItemBackgroundColor,
          zoom: appState.zoom,
        },
        timestamp: Date.now(),
      };

      // 更新节点属性
      props.updateAttributes({
        data: JSON.stringify(dataToSave),
      });

      console.log('Excalidraw data saved:', {
        elementsCount: elements.length,
        timestamp: dataToSave.timestamp,
      });

      // 关闭编辑器
      closeEditor();

      // 刷新只读视图
      refreshReadonlyView();
    } catch (error) {
      console.error('Failed to save Excalidraw data:', error);
    }
  }
};

// 关闭编辑器
const closeEditor = () => {
  // 清理编辑器 React 根节点
  if (editorRoot) {
    try {
      editorRoot.unmount();
      editorRoot = null;
    } catch (error) {
      console.warn('Error unmounting Excalidraw editor:', error);
    }
  }

  // 清理编辑器 API 引用
  editorApp = null;

  // 重置初始化状态
  isEditorInitializing.value = false;

  // 关闭弹框
  showEditor.value = false;
};

// 刷新只读视图
const refreshReadonlyView = () => {
  if (root && app) {
    try {
      // 重新初始化只读视图以显示最新数据
      root.unmount();
      root = null;
      app = null;

      // 重新初始化
      void nextTick(() => {
        initializeExcalidraw();
      });
    } catch (error) {
      console.warn('Error refreshing readonly view:', error);
    }
  }
};

// 改进的焦点监听器 - 解决重新聚焦后 Delete 键失效问题
const setupFocusListeners = () => {
  if (!excalidrawDom.value) return;

  const excalidrawContainer = excalidrawDom.value;

  // 监听容器的焦点事件
  const handleFocusIn = (event: FocusEvent) => {
    console.log('Excalidraw focus in:', event.target);

    // 确保 Excalidraw 获得焦点时能正确处理键盘事件
    if (app) {
      setTimeout(() => {
        try {
          // 尝试多种方法来重新激活 Excalidraw 的键盘事件处理

          // 方法1: 如果有 refresh 方法，调用它
          if (typeof app.refresh === 'function') {
            app.refresh();
            console.log('Excalidraw refreshed after focus');
          }

          // 方法2: 强制重新设置焦点到画布
          const canvas = excalidrawContainer.querySelector('canvas');
          if (canvas) {
            canvas.focus();
            console.log('Canvas focused');
          }

          // 方法3: 触发一个空的状态更新来重新激活事件监听器
          if (typeof app.updateScene === 'function') {
            app.updateScene({
              appState: {
                // 触发一个微小的状态变化
                lastPointerDownTarget: null,
              },
            });
            console.log('Excalidraw state updated after focus');
          }
        } catch (error) {
          console.warn('Failed to refresh Excalidraw after focus:', error);
        }
      }, 100); // 增加延迟时间
    }
  };

  const handleFocusOut = (event: FocusEvent) => {
    console.log('Excalidraw focus out:', event.target);
  };

  // 监听键盘事件，用于调试
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Delete' || event.key === 'Backspace') {
      console.log('Key event in Excalidraw container:', {
        key: event.key,
        target: event.target,
        currentTarget: event.currentTarget,
        activeElement: document.activeElement,
      });
    }
  };

  // 监听鼠标点击事件，确保在用户交互时重新激活键盘事件
  const handleMouseDown = () => {
    console.log('Mouse down in Excalidraw');

    // 延迟执行，确保 Excalidraw 内部状态已更新
    setTimeout(() => {
      if (app) {
        try {
          // 确保画布获得焦点
          const canvas = excalidrawContainer.querySelector('canvas');
          if (canvas && canvas !== document.activeElement) {
            canvas.focus();
            console.log('Canvas re-focused after mouse interaction');
          }
        } catch (error) {
          console.warn('Failed to focus canvas after mouse interaction:', error);
        }
      }
    }, 10);
  };

  // 添加事件监听器
  excalidrawContainer.addEventListener('focusin', handleFocusIn, true);
  excalidrawContainer.addEventListener('focusout', handleFocusOut, true);
  excalidrawContainer.addEventListener('keydown', handleKeyDown, true);
  excalidrawContainer.addEventListener('mousedown', handleMouseDown, true);

  // 存储清理函数
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (excalidrawContainer as any)._focusCleanup = () => {
    excalidrawContainer.removeEventListener('focusin', handleFocusIn, true);
    excalidrawContainer.removeEventListener('focusout', handleFocusOut, true);
    excalidrawContainer.removeEventListener('keydown', handleKeyDown, true);
    excalidrawContainer.removeEventListener('mousedown', handleMouseDown, true);
  };
};

// 监听主题变化，动态更新 Excalidraw 主题
watch(
  () => $q.dark.isActive,
  (isDarkMode) => {
    if (app && typeof app.updateScene === 'function') {
      try {
        // 更新 Excalidraw 主题
        app.updateScene({
          appState: {
            theme: isDarkMode ? 'dark' : 'light',
          },
        });
      } catch (error) {
        console.warn('Failed to update Excalidraw theme:', error);
      }
    }
  },
);

// 监听编辑器弹框状态 - 仅用于日志记录
watch(
  () => showEditor.value,
  (isOpen) => {
    if (isOpen) {
      console.log('Editor dialog opened');
    } else {
      console.log('Editor dialog closed');
    }
  },
);

onMounted(() => {
  // 使用 nextTick 和 setTimeout 确保 DOM 完全准备好
  void nextTick(() => {
    setTimeout(() => {
      initializeExcalidraw();
    }, 100);
  });
});

onUnmounted(() => {
  // 清理焦点监听器
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  if (excalidrawDom.value && (excalidrawDom.value as any)._focusCleanup) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (excalidrawDom.value as any)._focusCleanup();
  }

  // 清理只读模式 React 根节点
  if (root) {
    try {
      root.unmount();
      root = null;
    } catch (error) {
      console.warn('Error unmounting Excalidraw:', error);
    }
  }

  // 清理编辑器 React 根节点
  if (editorRoot) {
    try {
      editorRoot.unmount();
      editorRoot = null;
    } catch (error) {
      console.warn('Error unmounting Excalidraw editor:', error);
    }
  }

  // 清理 API 引用
  app = null;
  editorApp = null;
});
</script>

<style scoped>
/* 隐藏不需要的 UI 元素 */
:deep(.excalidraw) {
  /* .App-menu_top {
    display: flex;
    flex-direction: row;
    justify-content: end;
  } */
  /* 隐藏左上角主菜单按钮 - 通过多种选择器确保完全隐藏 */
  .App-menu_top__left .dropdown-menu-button {
    display: none !important;
  }

  /* 隐藏右上角资源库按钮 */
  /* .layer-ui__wrapper__top-right {
    display: none !important;
  } */
}
</style>
