<template>
  <div class="column no-wrap absolute-full overflow-hidden q-pa-md">
    <div class="row items-center justify-between q-mb-md">
      <div class="text-h4">Fluent Icons 图标库</div>
      <div class="text-subtitle1">总计: {{ displayedIcons.length }} 个图标</div>
    </div>

    <!-- 搜索和筛选 -->
    <q-card class="q-mb-md">
      <q-card-section class="q-pa-sm">
        <div class="row items-center gap-md">
          <div class="col-md-6 col-12">
            <q-input
              v-model="searchQuery"
              placeholder="搜索图标名称..."
              clearable
              outlined
              @input="handleSearch"
              @clear="searchQuery = ''"
              dense
            >
              <template v-slot:prepend>
                <q-icon name="fluent-search" />
              </template>
            </q-input>
          </div>
          <div class="q-space">
            <q-select
              v-model="selectedVariant"
              :options="variantOptions"
              label="图标变体"
              dense
              outlined
              emit-value
              map-options
            />
          </div>
          <div class="q-space">
            <q-select
              v-model="iconsPerPage"
              :options="[50, 100, 200, 500]"
              label="每页显示"
              dense
              outlined
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 图标网格 -->
    <div class="icon-grid q-space scroll-y">
      <div
        v-for="iconName in paginatedIcons"
        :key="iconName"
        class="icon-item"
        @click="copyIconName(iconName)"
      >
        <div class="icon-container">
          <q-icon :name="`fluent-${iconName}`" size="24px" class="icon" />
          <div class="icon-text">
            <div class="icon-name">{{ iconName }}</div>
            <div class="icon-code">fluent-{{ iconName }}</div>
          </div>
        </div>
        <q-tooltip class="bg-primary"> 点击复制: fluent-{{ iconName }} </q-tooltip>
      </div>
    </div>

    <!-- 分页 -->
    <div class="row flex-center q-mt-lg" v-if="totalPages > 1">
      <q-pagination
        v-model="currentPage"
        :max="totalPages"
        :max-pages="6"
        boundary-numbers
        direction-links
        outline
        color="primary"
        active-design="unelevated"
        active-color="primary"
        active-text-color="white"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useFluentIcons, type FluentIconVariant } from '../composables/useFluentIcons';
import { copyToClipboard } from '../utils/clipboard';

// 使用 Quasar 和 Fluent Icons
const $q = useQuasar();
const { availableIcons, searchIcons } = useFluentIcons();

// 响应式数据
const searchQuery = ref('');
const selectedVariant = ref<FluentIconVariant>('regular');
const iconsPerPage = ref(100);
const currentPage = ref(1);
const copiedText = ref('');

// 变体选项
const variantOptions = [
  { label: 'Regular', value: 'regular' },
  { label: 'Light', value: 'light' },
  { label: 'Filled', value: 'filled' },
  { label: 'Resizable', value: 'resizable' },
];

// 计算属性
const displayedIcons = computed(() => {
  if (searchQuery.value.trim()) {
    return searchIcons(searchQuery.value);
  }
  return availableIcons;
});

const totalPages = computed(() => {
  return Math.ceil(displayedIcons.value.length / iconsPerPage.value);
});

const paginatedIcons = computed(() => {
  const start = (currentPage.value - 1) * iconsPerPage.value;
  const end = start + iconsPerPage.value;
  return displayedIcons.value.slice(start, end);
});

// 监听搜索变化，重置页码
watch(searchQuery, () => {
  currentPage.value = 1;
});

watch(iconsPerPage, () => {
  currentPage.value = 1;
});

// 方法
const handleSearch = () => {
  currentPage.value = 1;
};

const copyIconName = async (iconName: string) => {
  const textToCopy = `fluent-${iconName}`;
  console.log('textToCopy', textToCopy);

  try {
    // 使用 Qt 应用专用的剪贴板工具
    const success = await copyToClipboard(textToCopy, {
      showNotification: false, // 我们自己处理通知
      successMessage: '复制成功!',
      errorMessage: '复制失败',
    });

    if (success) {
      copiedText.value = textToCopy;
      // 显示自定义通知
      $q.notify({
        type: 'positive',
        message: '复制成功!',
        caption: textToCopy,
        position: 'top',
        timeout: 2000,
        actions: [{ icon: 'fluent-dismiss', color: 'white', round: true, handler: () => {} }],
      });
    } else {
      throw new Error('剪贴板操作失败');
    }
  } catch (err) {
    console.error('复制失败:', err);
    $q.notify({
      type: 'negative',
      message: '复制失败',
      caption: '请手动复制图标名称',
      position: 'top',
      timeout: 3000,
    });
  }
};
</script>

<style scoped>
.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 8px;
}

.icon-item {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.icon-item:hover {
  border-color: var(--q-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.icon-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon {
  flex-shrink: 0;
  color: var(--q-info);
}

.icon-text {
  flex: 1;
  min-width: 0;
}

.icon-name {
  font-weight: 500;
  font-size: 14px;
  color: var(--q-dark);
  margin-bottom: 2px;
  word-break: break-all;
}

.icon-code {
  font-family: 'JetBrainsMono', 'Courier New', monospace;
  font-size: 12px;
  color: var(--q-secondary);
  opacity: 0.8;
  word-break: break-all;
}

/* 暗色主题适配 */
.body--dark .icon-item {
  background: var(--q-dark-page);
  border-color: rgba(255, 255, 255, 0.12);
}

.body--dark .icon-item:hover {
  border-color: var(--q-primary);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.body--dark .icon-name {
  color: white;
}

.body--dark .icon-code {
  color: var(--q-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .icon-grid {
    grid-template-columns: 1fr;
  }

  .icon-container {
    gap: 8px;
  }

  .icon-item {
    padding: 8px;
  }
}

/* 分页样式优化 */
:deep(.q-pagination) {
  .q-btn {
    min-width: 40px;
    height: 40px;
  }
}
</style>
