<template>
  <div class="ollama-settings">
    <div class="row items-center">
      <div class="column">
        <div class="text-h6">{{ $t('ollama') }} {{ $t('settings.title') }}</div>
        <div class="text-caption text-grey-6">
          {{ $t('configuration') }} {{ $t('ollama') }}
          {{ $t('src.components.settings.llmProviders.titleSuffix') }}
        </div>
      </div>
      <q-space />
      <!-- 连接测试按钮 -->
      <div class="row q-gutter-sm">
        <q-btn
          color="secondary"
          :label="$t('src.components.settings.llmProviders.testConnection')"
          dense
          flat
          :loading="testing"
          @click="testConnection"
        />
        <q-toggle
          v-model="settings.enabled"
          color="positive"
          :label="$t('src.components.settings.llmProviders.enabled')"
        />
      </div>
    </div>

    <!-- 连接状态显示 -->
    <q-banner
      v-if="connectionStatus"
      :class="connectionStatus.success ? 'bg-positive' : 'bg-negative'"
      text-color="white"
      class="q-mt-md radius-sm"
    >
      <template v-slot:avatar>
        <q-icon :name="connectionStatus.success ? 'check_circle' : 'error'" color="white" />
      </template>
      {{ connectionStatus.message }}
      <template v-slot:action>
        <q-btn
          color="grey-2"
          dense
          padding="xs md"
          :label="$t('iKnownIt')"
          class="text-grey-10"
          @click.stop="connectionStatus = null"
        />
      </template>
    </q-banner>

    <q-form class="q-gutter-md q-mt-lg">
      <!-- 基础 URL -->
      <q-input
        v-model="settings.baseUrl"
        :label="$t('src.components.settings.llmProviders.baseUrl')"
        :hint="`${$t('ollama')} ${$t('src.components.settings.llmProviders.baseUrlRules.hint')}`"
        outlined
        :rules="[
          (val) => !!val || $t('src.components.settings.llmProviders.baseUrlRules.required'),
          (val) =>
            val.startsWith('http') || $t('src.components.settings.llmProviders.baseUrlRules.http'),
        ]"
      >
        <template v-slot:prepend>
          <q-icon name="link" />
        </template>
      </q-input>

      <!-- API Key -->
      <q-input
        v-model="settings.apiKey"
        :label="$t('src.components.settings.llmProviders.apiKey')"
        :hint="`${$t('ollama')} ${$t('src.components.settings.llmProviders.apiKeyRules.hint')}`"
        disable
        outlined
        :rules="[(val) => !!val || $t('src.components.settings.llmProviders.apiKeyRules.required')]"
      >
        <template v-slot:prepend>
          <q-icon name="key" />
        </template>
        <q-tooltip>
          {{ $t('dont_touch_ollama_api_key') }}
        </q-tooltip>
      </q-input>

      <!-- 模型选择已移至对话组件中 -->
      <!-- 随机性控制方式选择 -->
      <q-select
        v-model="randomnessControlType"
        :options="randomnessControlOptions"
        :label="$t('src.components.settings.llmProviders.randomnessType')"
        outlined
        emit-value
        map-options
        class="q-mb-md"
        @update:model-value="onRandomnessControlTypeChange"
      >
        <template v-slot:prepend>
          <q-icon name="settings" />
        </template>
      </q-select>

      <!-- 温度设置 -->
      <q-item v-if="randomnessControlType === 'temperature'" class="q-pa-none q-mb-md">
        <q-item-section avatar>
          <q-icon name="thermostat" />
        </q-item-section>
        <q-item-section>
          <q-slider
            v-model="currentTemperature"
            :min="0"
            :max="2"
            :step="0.1"
            label
            :label-always="true"
            :label-value="`${$t('src.components.settings.llmProviders.temperature')}: ${currentTemperature}`"
            track-size="2px"
            color="grey"
            label-color="primary"
            thumb-color="primary"
            @update:model-value="updateTemperature"
          />
          <span class="text-grey">{{
            $t('src.components.settings.llmProviders.temperatureDescription')
          }}</span>
        </q-item-section>
      </q-item>

      <!-- Top P 设置 -->
      <q-item v-if="randomnessControlType === 'topP'" class="q-pa-none q-mb-md">
        <q-item-section avatar>
          <q-icon name="tune" />
        </q-item-section>
        <q-item-section>
          <q-slider
            v-model="currentTopP"
            :min="0"
            :max="1"
            :step="0.1"
            label
            :label-always="true"
            :label-value="`${$t('src.components.settings.llmProviders.topP')}: ${currentTopP}`"
            track-size="2px"
            color="grey"
            label-color="primary"
            thumb-color="primary"
            @update:model-value="updateTopP"
          />
          <span class="text-grey">{{
            $t('src.components.settings.llmProviders.topPDescription')
          }}</span>
        </q-item-section>
      </q-item>
    </q-form>

    <!-- 可用模型管理 -->
    <q-separator class="q-my-lg" />
    <ModelManager v-model="settings.avaliableModels" @update:model-value="updateAvailableModels" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useUiStore } from 'src/stores/ui';
import type { OllamaSettings } from 'src/types/qwen';
import ModelManager from '../ModelManager.vue';
import type { CategorizedModels } from 'src/types/modelCategories';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

const store = useUiStore();
const testing = ref(false);
const connectionStatus = ref<{ success: boolean; message: string } | null>(null);

// 随机性控制方式
const randomnessControlType = ref<'temperature' | 'topP'>('temperature');

// 随机性控制选项
const randomnessControlOptions = [
  { label: $t('src.components.settings.llmProviders.temperature'), value: 'temperature' },
  { label: $t('src.components.settings.llmProviders.topP'), value: 'topP' },
];

// 当前显示的温度和TopP值
const currentTemperature = ref(0.7);
const currentTopP = ref(0.9);

// 防止递归更新的标志
const isUpdatingSettings = ref(false);

// 动态模型选项
const modelOptions = ref<{ label: string; value: string }[]>([]);
const loadingModels = ref(false);

// 获取Ollama模型列表
const fetchOllamaModels = async (baseUrl?: string) => {
  loadingModels.value = true;

  try {
    // 使用提供的baseUrl或当前设置的baseUrl
    let ollamaBaseUrl = baseUrl || settings.value.baseUrl;

    // 从OpenAI兼容的URL中提取Ollama基础URL
    if (ollamaBaseUrl.includes('/v1/chat/completions')) {
      ollamaBaseUrl = ollamaBaseUrl.replace('/v1/chat/completions', '');
    } else if (ollamaBaseUrl.includes('/chat/completions')) {
      ollamaBaseUrl = ollamaBaseUrl.replace('/chat/completions', '');
    }

    // 确保URL格式正确
    if (ollamaBaseUrl.endsWith('/')) {
      ollamaBaseUrl = ollamaBaseUrl.slice(0, -1);
    }
    if (ollamaBaseUrl.endsWith('/v1')) {
      ollamaBaseUrl = ollamaBaseUrl.slice(0, -3);
    }

    console.log('[OllamaOptions] 获取模型列表，URL:', `${ollamaBaseUrl}/api/tags`);

    const response = await fetch(`${ollamaBaseUrl}/api/tags`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();

      if (data.models && Array.isArray(data.models)) {
        modelOptions.value = data.models.map((model: { name: string; size?: string }) => ({
          label: `${model.name} (${model.size || 'Unknown size'})`,
          value: model.name,
        }));

        console.log('[OllamaOptions] 成功获取模型列表:', modelOptions.value.length, '个模型');
      } else {
        console.warn('[OllamaOptions] 响应中没有找到模型列表');
        modelOptions.value = [];
      }
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('[OllamaOptions] 获取模型列表失败:', error);
    modelOptions.value = [];

    // 如果获取失败，提供一些常见的默认选项
    modelOptions.value = [{ label: '请先启动Ollama服务并下载模型', value: '' }];
  } finally {
    loadingModels.value = false;
  }
  return modelOptions.value;
};

// 从 store 获取设置
const settings = computed(() => store.perferences?.llm?.ollama);

// 更新温度值
const updateTemperature = (value: number) => {
  currentTemperature.value = value;
  updateSettings({ temperature: value });
};

// 更新TopP值
const updateTopP = (value: number) => {
  currentTopP.value = value;
  updateSettings({ topP: value });
};

// 处理随机性控制类型变化
const onRandomnessControlTypeChange = (newType: 'temperature' | 'topP') => {
  if (!settings.value || isUpdatingSettings.value) return;

  // 设置更新标志，防止递归
  isUpdatingSettings.value = true;

  try {
    // 创建新的设置对象，将不需要的字段设置为NaN
    const currentSettings = { ...settings.value };

    if (newType === 'temperature') {
      // 选择温度，将topP设置为NaN，保留或设置默认温度值
      const temperatureValue = settings.value.temperature ?? 0.7;
      currentSettings.temperature = temperatureValue;
      currentTemperature.value = temperatureValue; // 更新当前显示值
      currentSettings.topP = NaN; // 设置为NaN表示不使用此参数
    } else if (newType === 'topP') {
      // 选择topP，将temperature设置为NaN，保留或设置默认topP值
      const topPValue = settings.value.topP ?? 0.9;
      currentSettings.topP = topPValue;
      currentTopP.value = topPValue; // 更新当前显示值
      currentSettings.temperature = NaN; // 设置为NaN表示不使用此参数
    }

    // 直接更新整个设置对象
    store.updateOllamaSettings(currentSettings);
  } finally {
    // 重置更新标志
    setTimeout(() => {
      isUpdatingSettings.value = false;
    }, 100);
  }
};

// 更新设置
const updateSettings = (newSettings: Partial<OllamaSettings>) => {
  const currentSettings = settings.value;
  if (!currentSettings) return;

  const updatedSettings = {
    ...currentSettings,
    ...newSettings,
  };

  store.updateOllamaSettings(updatedSettings);
};

// 测试连接
const testConnection = async () => {
  testing.value = true;
  connectionStatus.value = null;

  // 从OpenAI兼容的URL中提取Ollama基础URL
  let baseUrl = settings.value.baseUrl;

  // 确保URL以正确的格式结尾
  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.slice(0, -1);
  }
  if (baseUrl.endsWith('/v1')) {
    baseUrl = baseUrl.slice(0, -3);
  }

  try {
    const response = await fetch(`${baseUrl}/api/tags`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();

      // 测试OpenAI兼容API端点
      try {
        const chatResponse = await fetch(`${settings.value.baseUrl}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${settings.value.apiKey}`,
          },
          body: JSON.stringify({
            model: settings.value.model,
            messages: [{ role: 'user', content: 'test' }],
            max_tokens: 1,
            stream: false,
          }),
        });

        if (chatResponse.ok || chatResponse.status === 400) {
          // 400错误通常表示API端点存在但参数有问题，这是正常的
          connectionStatus.value = {
            success: true,
            message: `连接成功！发现 ${data.models?.length || 0} 个可用模型，OpenAI兼容API正常`,
          };

          // 连接成功后刷新模型列表
          await fetchOllamaModels();
        } else {
          connectionStatus.value = {
            success: true,
            message: `Ollama服务正常（发现 ${data.models?.length || 0} 个模型），但OpenAI兼容API可能未启用`,
          };

          // 即使OpenAI兼容API有问题，也刷新模型列表
          await fetchOllamaModels();
        }
      } catch {
        // 即使OpenAI兼容API测试失败，如果基础API正常，也刷新模型列表
        connectionStatus.value = {
          success: true,
          message: `Ollama服务正常（发现 ${data.models?.length || 0} 个模型），但OpenAI兼容API测试失败`,
        };

        await fetchOllamaModels();
      }
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('[OllamaOptions] 连接测试失败:', error);
    console.error('[OllamaOptions] 测试的URL:', `${baseUrl}/api/tags`);

    connectionStatus.value = {
      success: false,
      message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}。请确保Ollama服务正在运行在 ${baseUrl}`,
    };
  } finally {
    testing.value = false;
  }
};

watch(
  settings,
  async (cur, prv) => {
    if (!cur || !prv || isUpdatingSettings.value) return;

    // 比较新旧值，只有真正变化时才更新
    let hasChanges = false;
    for (const [key, value] of Object.entries(cur)) {
      if (prv[key as keyof OllamaSettings] !== value) {
        hasChanges = true;
        break;
      }
    }

    if (!hasChanges) {
      console.log('[OllamaOptions] 设置无变化，跳过更新');
      return;
    }

    console.log('[OllamaOptions] 检测到设置变化，更新:', cur);

    // 初始化随机性控制类型和当前值（只在非更新状态下执行）
    if (!isUpdatingSettings.value) {
      const hasValidTemperature = cur.temperature !== undefined && !isNaN(cur.temperature);
      const hasValidTopP = cur.topP !== undefined && !isNaN(cur.topP);

      if (hasValidTemperature && !hasValidTopP) {
        randomnessControlType.value = 'temperature';
        currentTemperature.value = cur.temperature!;
      } else if (hasValidTopP && !hasValidTemperature) {
        randomnessControlType.value = 'topP';
        currentTopP.value = cur.topP!;
      } else if (hasValidTemperature && hasValidTopP) {
        randomnessControlType.value = 'temperature';
        currentTemperature.value = cur.temperature!;
      } else {
        randomnessControlType.value = 'temperature';
        currentTemperature.value = 0.7;
      }
    }

    // baseUrl变化，重新获取模型列表
    if (cur.baseUrl !== prv.baseUrl && cur.baseUrl.trim() !== '') {
      await fetchOllamaModels(cur.baseUrl);
      if (!loadingModels.value) {
        return;
      }
    }

    // 只在需要时设置 parallel_tool_calls，避免递归更新
    if (cur.parallel_tool_calls !== true && !isUpdatingSettings.value) {
      const updatedSettings = { ...cur, parallel_tool_calls: true };
      store.updateOllamaSettings(updatedSettings);
    }
  },
  { deep: true },
);

onMounted(async () => {
  console.log('[OllamaOptions] 组件挂载，当前Ollama配置:', settings.value);

  // 组件挂载时立即获取模型列表并更新设置到后端
  if (settings.value.baseUrl && settings.value.baseUrl.trim() !== '') {
    console.log('[OllamaOptions] 组件挂载时获取模型列表');

    try {
      // 获取模型列表
      await fetchOllamaModels();

      // 如果成功获取到模型列表，立即更新设置到后端
      if (modelOptions.value.length > 0 && modelOptions.value[0].value !== '') {
        console.log('[OllamaOptions] 获取到模型列表，立即更新设置到后端');

        // 更新设置（不再更新avaliableModels，因为它现在是分类结构）
        const updatedSettings = {
          ...settings.value,
        };

        // 如果当前没有选择模型，选择第一个可用模型
        if (!updatedSettings.model || updatedSettings.model.trim() === '') {
          updatedSettings.model = modelOptions.value[0].value;
          settings.value.model = modelOptions.value[0].value;
        }

        // 更新到 store 和后端
        store.updateOllamaSettings(updatedSettings);

        console.log('[OllamaOptions] 设置已更新到后端，可用模型数量:', modelOptions.value.length);
      }
    } catch (error) {
      console.error('[OllamaOptions] 组件挂载时获取模型列表失败:', error);
    }
  }
});

// 更新可用模型
const updateAvailableModels = (models: CategorizedModels) => {
  updateSettings({ avaliableModels: models });
};
</script>

<style lang="scss" scoped>
.ollama-settings {
  width: 100%;
}
</style>
