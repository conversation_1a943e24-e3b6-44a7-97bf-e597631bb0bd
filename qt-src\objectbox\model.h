#ifndef OBJECTBOX_MODEL_H
#define OBJECTBOX_MODEL_H

#include "objectbox.hpp"
#include "knowledge_generated.h"

namespace obx
{

    // 为ObjectBox生成模型定义
    inline obx::Model createKnowledgeModel()
    {
        obx::ModelBuilder builder;

        // 知识库实体
        builder.entity<InkCop::Knowledge::KnowledgeBase>("KnowledgeBase")
            .idProperty(&InkCop::Knowledge::KnowledgeBase::id)
            .property(&InkCop::Knowledge::KnowledgeBase::name, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeBase::description, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeBase::user_id, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeBase::settings, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeBase::created_at, obx::PropertyType::Long)
            .property(&InkCop::Knowledge::KnowledgeBase::updated_at, obx::PropertyType::Long);

        // 知识库文档实体
        builder.entity<InkCop::Knowledge::KnowledgeDocument>("KnowledgeDocument")
            .idProperty(&InkCop::Knowledge::KnowledgeDocument::id)
            .property(&InkCop::Knowledge::KnowledgeDocument::kb_id, obx::PropertyType::Long)
            .property(&InkCop::Knowledge::KnowledgeDocument::title, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeDocument::content, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeDocument::document_type, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeDocument::metadata, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeDocument::created_at, obx::PropertyType::Long)
            .property(&InkCop::Knowledge::KnowledgeDocument::updated_at, obx::PropertyType::Long);

        // 知识库块实体
        auto chunkEntity = builder.entity<InkCop::Knowledge::KnowledgeChunk>("KnowledgeChunk")
                               .idProperty(&InkCop::Knowledge::KnowledgeChunk::id)
                               .property(&InkCop::Knowledge::KnowledgeChunk::knowledge_document_id, obx::PropertyType::Long)
                               .property(&InkCop::Knowledge::KnowledgeChunk::chunk_index, obx::PropertyType::Int)
                               .property(&InkCop::Knowledge::KnowledgeChunk::content, obx::PropertyType::String)
                               .property(&InkCop::Knowledge::KnowledgeChunk::embedding, obx::PropertyType::FloatVector)
                               .property(&InkCop::Knowledge::KnowledgeChunk::metadata, obx::PropertyType::String)
                               .property(&InkCop::Knowledge::KnowledgeChunk::created_at, obx::PropertyType::Long)
                               .property(&InkCop::Knowledge::KnowledgeChunk::is_vectorized, obx::PropertyType::Bool);

        // 为embedding字段添加HNSW索引
        // 注意：维度需要根据实际使用的embedding模型动态配置
        // 常见维度：text-embedding-v1(1536), text-embedding-v2(1536), text-embedding-v3(1536)
        // 本地GGUF模型维度取决于具体模型，简单TF-IDF为100维
        // 这里使用1536作为默认值，适配大多数OpenAI兼容的embedding模型
        chunkEntity.hnswIndex(&InkCop::Knowledge::KnowledgeChunk::embedding)
            .dimensions(1536) // 使用1536维适配主流embedding模型
            .distanceType(obx::VectorDistanceType::Cosine)
            .neighborsPerNode(30)
            .indexingSearchCount(200);

        // 知识库查询实体
        builder.entity<InkCop::Knowledge::KnowledgeQuery>("KnowledgeQuery")
            .idProperty(&InkCop::Knowledge::KnowledgeQuery::id)
            .property(&InkCop::Knowledge::KnowledgeQuery::knowledge_base_id, obx::PropertyType::Long)
            .property(&InkCop::Knowledge::KnowledgeQuery::query_text, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeQuery::query_embedding, obx::PropertyType::FloatVector)
            .property(&InkCop::Knowledge::KnowledgeQuery::results, obx::PropertyType::String)
            .property(&InkCop::Knowledge::KnowledgeQuery::created_at, obx::PropertyType::Long);

        return builder.build();
    }

} // namespace obx

#endif // OBJECTBOX_MODEL_H