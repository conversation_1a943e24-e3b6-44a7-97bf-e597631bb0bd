// useFluentIcons 组合式函数
// 提供便捷的 Fluent Icons 使用方法

import { computed, ref, type Ref } from 'vue'
import { 
  getFluentIcon, 
  createFluentIconClass, 
  type FluentIconVariant,
  availableFluentIcons,
  searchFluentIcons,
  commonFluentIcons
} from '../icons/fluent-icons'

export interface UseFluentIconsOptions {
  defaultVariant?: FluentIconVariant
  enableSearch?: boolean
}

export function useFluentIcons(options: UseFluentIconsOptions = {}) {
  const { defaultVariant = 'regular', enableSearch = true } = options
  
  // 响应式状态
  const currentVariant = ref<FluentIconVariant>(defaultVariant)
  const searchQuery = ref('')
  
  // 计算属性
  const filteredIcons = computed(() => {
    if (!enableSearch || !searchQuery.value.trim()) {
      return availableFluentIcons.slice(0, 100) // 限制显示数量以提高性能
    }
    return searchFluentIcons(searchQuery.value)
  })
  
  const iconCount = computed(() => availableFluentIcons.length)
  
  // 方法
  const getIcon = (name: string, variant?: FluentIconVariant) => {
    return getFluentIcon(name, variant || currentVariant.value)
  }
  
  const getIconClass = (name: string, variant?: FluentIconVariant) => {
    return createFluentIconClass(name, variant || currentVariant.value)
  }
  
  const isIconAvailable = (name: string) => {
    return availableFluentIcons.includes(name)
  }
  
  const getIconVariants = (name: string) => {
    const variants: FluentIconVariant[] = []
    
    if (getFluentIcon(name, 'regular')) variants.push('regular')
    if (getFluentIcon(name, 'light')) variants.push('light')
    if (getFluentIcon(name, 'filled')) variants.push('filled')
    if (getFluentIcon(name, 'resizable')) variants.push('resizable')
    
    return variants
  }
  
  const searchIcons = (query: string) => {
    searchQuery.value = query
    return filteredIcons.value
  }
  
  const clearSearch = () => {
    searchQuery.value = ''
  }
  
  const setVariant = (variant: FluentIconVariant) => {
    currentVariant.value = variant
  }
  
  // 预设图标组合
  const getCommonIcon = (key: keyof typeof commonFluentIcons) => {
    const iconName = commonFluentIcons[key]
    return getIcon(iconName)
  }
  
  const getCommonIconClass = (key: keyof typeof commonFluentIcons) => {
    const iconName = commonFluentIcons[key]
    return getIconClass(iconName)
  }
  
  // 批量获取图标
  const getIcons = (names: string[], variant?: FluentIconVariant) => {
    return names.reduce((acc, name) => {
      acc[name] = getIcon(name, variant)
      return acc
    }, {} as Record<string, string>)
  }
  
  // 获取图标的 CSS 内容
  const getIconContent = (name: string, variant?: FluentIconVariant) => {
    const unicode = getIcon(name, variant)
    return unicode ? `"${unicode}"` : ''
  }
  
  // 创建图标样式对象
  const createIconStyle = (name: string, variant?: FluentIconVariant, size?: string) => {
    const unicode = getIcon(name, variant)
    if (!unicode) return {}
    
    const baseStyle = {
      fontFamily: `FluentSystemIcons-${variant || currentVariant.value}`,
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontVariant: 'normal',
      textTransform: 'none',
      lineHeight: '1',
      WebkitFontSmoothing: 'antialiased',
      MozOsxFontSmoothing: 'grayscale'
    }
    
    if (size) {
      baseStyle['fontSize'] = size
    }
    
    return baseStyle
  }
  
  return {
    // 响应式状态
    currentVariant: readonly(currentVariant),
    searchQuery: readonly(searchQuery),
    filteredIcons,
    iconCount,
    
    // 基础方法
    getIcon,
    getIconClass,
    getIconContent,
    createIconStyle,
    isIconAvailable,
    getIconVariants,
    
    // 搜索功能
    searchIcons,
    clearSearch,
    
    // 变体管理
    setVariant,
    
    // 预设图标
    getCommonIcon,
    getCommonIconClass,
    commonIcons: commonFluentIcons,
    
    // 批量操作
    getIcons,
    
    // 常量
    availableIcons: availableFluentIcons,
    variants: ['regular', 'light', 'filled', 'resizable'] as FluentIconVariant[]
  }
}

// 只读版本的 ref
function readonly<T>(ref: Ref<T>) {
  return computed(() => ref.value)
}

// 导出类型
export type { FluentIconVariant }
