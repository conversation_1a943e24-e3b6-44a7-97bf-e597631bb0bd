<template>
  <div class="image-gallery">
    <div v-for="image in images" :key="image.id" class="image-container">
      <img :src="image.src.medium" :alt="image.photographer" @click="showImage(image.src.large)" />
      <div class="image-info">
        <span
          class="text-primary cursor-pointer"
          @click="openExternalLink(image.photographer_url)"
          style="text-decoration: underline; color: white"
        >
          {{ image.photographer }}
        </span>
      </div>
    </div>
    <q-dialog v-model="dialogVisible">
      <q-card>
        <q-img :src="selectedImage" />
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { PexelsPhoto } from '../llm/tools/pexels';
import { openExternalLink } from 'src/utils/externalLink';

defineProps<{ images: PexelsPhoto[] }>();

const dialogVisible = ref(false);
const selectedImage = ref('');

const showImage = (src: string) => {
  selectedImage.value = src;
  dialogVisible.value = true;
};
</script>

<style scoped>
.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-container {
  position: relative;
  cursor: pointer;
}

.image-container img {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
}

.image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 5px;
  font-size: 12px;
  text-align: center;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
</style>
