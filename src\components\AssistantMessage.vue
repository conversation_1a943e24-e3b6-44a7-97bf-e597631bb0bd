<template>
  <q-item class="q-py-none q-px-xs">
    <q-item-section class="column no-wrap">
      <template v-if="message.reasoning_content?.trim()">
        <div
          class="row no-wrap items-center cursor-pointer"
          :class="expandThinking ? '' : 'q-mb-sm'"
          @click="expandThinking = !expandThinking"
          @mouseenter="showExpandIcon = true"
          @mouseleave="showExpandIcon = false"
        >
          <q-icon
            :name="
              showExpandIcon
                ? expandThinking
                  ? 'mdi-chevron-down'
                  : 'mdi-chevron-right'
                : 'mdi-brain'
            "
            class="q-mr-sm"
            :color="expandThinking ? 'primary' : 'grey-6'"
          />

          <span class="op-5">{{
            isThinking
              ? $t('src.components.AssistantMessage.thinking')
              : $t('src.components.AssistantMessage.thinkingProcess')
          }}</span>
        </div>
        <MarkdownRenderer
          v-if="expandThinking && message.reasoning_content"
          class="q-px-md op-5"
          :content="message.reasoning_content"
        />
      </template>

      <!-- 工具调用显示 -->
      <div
        v-if="message.tool_calls?.length > 0"
        class="row no-wrap items-center cursor-pointer"
        :class="expandToolResult ? '' : 'q-mb-sm'"
        @click="expandToolResult = !expandToolResult"
        @mouseenter="showToolIcon = true"
        @mouseleave="showToolIcon = false"
      >
        <q-icon
          :name="
            showToolIcon
              ? expandToolResult
                ? 'mdi-chevron-down'
                : 'mdi-chevron-right'
              : 'construction'
          "
          class="q-mr-sm"
          :color="expandToolResult ? 'primary' : 'grey-6'"
        />

        <span class="op-5"
          >{{ $t('src.components.AssistantMessage.toolCall') }}:{{
            message.tool_calls?.map((i) => i.function?.name)?.join(', ')
          }}</span
        >
      </div>
      <slot v-if="expandToolResult" name="toolMessage" />

      <div v-if="message.content" class="column no-wrap">
        <MarkdownRenderer class="q-pa-xs" :content="message.content" />
        <CreatekKnowlegeDocCard
          v-if="showCreateKnowledgeDocCard"
          :content="message.content"
          :default-title="generateDefaultTitle(message.content)"
          :bolderless="false"
          class="q-px-xs"
          @cancel="showCreateKnowledgeDocCard = false"
          @saved="onKnowledgeDocSaved"
        />
        <div class="row no-wrap gap-sm items-center q-px-md q-pb-lg">
          <q-btn
            flat
            dense
            size="sm"
            icon="mdi-brain"
            @click="showSaveToKnowledgeDialog"
            :disable="!message.content || !uiStore.isKnowledgeBaseConfigured"
          >
            <q-tooltip>
              {{
                uiStore.isKnowledgeBaseConfigured
                  ? $t('src.components.AssistantMessage.saveToKnowledge')
                  : $t('src.components.AssistantMessage.saveToKnowledgeHint')
              }}
            </q-tooltip>
          </q-btn>
          <q-btn flat dense size="sm" icon="mdi-content-copy" @click="onCopy(message.content)" />
        </div>
      </div>
    </q-item-section>
  </q-item>
</template>

<script setup lang="ts">
import type { AssistantMessage } from 'src/types/qwen';
import MarkdownRenderer from 'src/components/MarkdownRenderer.vue';
import { computed, ref, watch } from 'vue';
import { copyToClipboard } from 'src/utils/clipboard';
import { useKnowledgeStore } from '../stores/knowledge';
import { useUiStore } from 'src/stores/ui';
import CreatekKnowlegeDocCard from './CreatekKnowlegeDocCard.vue';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

const props = defineProps<{
  message: AssistantMessage;
}>();

const knowledgeStore = useKnowledgeStore();
const uiStore = useUiStore();
const expandThinking = ref(false);
const expandToolResult = ref(false);
const isThinking = computed(
  () => props.message.reasoning_content && !props.message.content && !props.message.tool_calls,
);
const hasAutoToggled = ref(false); // 记录是否已经自动切换过状态
const showExpandIcon = ref(true);
const showToolIcon = ref(true);
const showCreateKnowledgeDocCard = ref<boolean>(false);

const onCopy = async (content: string) => {
  await copyToClipboard(content, {
    showNotification: true,
    successMessage: $t('copySuccess'),
    errorMessage: $t('copyFailed'),
  });
};

/**
 * 显示保存到知识库对话框
 */
const showSaveToKnowledgeDialog = () => {
  showCreateKnowledgeDocCard.value = true;
};

/**
 * 生成默认文档标题
 */
const generateDefaultTitle = (content: string): string => {
  // 取内容前30个字符作为默认标题
  const title = content.substring(0, 30).replace(/\n/g, ' ').trim();
  return title
    ? `${$t('src.components.AssistantMessage.aiAnswer')}_${title}...`
    : $t('src.components.AssistantMessage.aiAnswer');
};

/**
 * 知识库文档保存成功回调
 */
const onKnowledgeDocSaved = async (knowledgeBaseId: number, documentId: number, title: string) => {
  showCreateKnowledgeDocCard.value = false;

  try {
    // 刷新知识库列表数据，更新文档数量统计
    await knowledgeStore.refreshKnowledgeBases();
    console.log('✅ [AssistantMessage] 文档已保存到知识库，数据已刷新:', {
      knowledgeBaseId,
      documentId,
      title,
    });
  } catch (error) {
    console.error('❌ [AssistantMessage] 刷新知识库数据失败:', error);
  }
};

// 监听 reasoning_content 的变化，自动控制展开状态
watch(
  () => props.message.content,
  (newContent) => {
    // 如果已经自动切换过，则不再自动修改状态
    if (hasAutoToggled.value) return;

    if (newContent) {
      // 当内容存在时，立刻展开
      expandThinking.value = true;

      // 当字数超过50时，自动收起
      if (newContent.length > 50) {
        expandThinking.value = false;
        hasAutoToggled.value = true; // 标记已经完成自动切换
      }
    }
  },
  { immediate: true },
);
</script>
