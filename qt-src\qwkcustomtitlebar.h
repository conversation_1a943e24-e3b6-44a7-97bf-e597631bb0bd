#ifndef QWKCUSTOMTITLEBAR_H
#define QWKCUSTOMTITLEBAR_H

#include <QWidget>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QMouseEvent>
#include <QEvent>
#include <QTimer>

class QWKCustomTitleBar : public QWidget
{
    Q_OBJECT

public:
    explicit QWKCustomTitleBar(QWidget *parent = nullptr);
    ~QWKCustomTitleBar();

    // 设置标题
    void setTitle(const QString &title);

    // 设置窗口图标
    void setWindowIcon(const QIcon &icon);

    // 更新最大化按钮状态
    void updateMaximizeButton(bool isMaximized);

    // 更新主题
    void updateTheme(bool isDarkTheme);

    // 更新抽屉状态
    void updateLeftDrawerState(bool isOpened);
    void updateRightDrawerState(bool isOpened);

    // 从前端同步抽屉图标状态
    void syncDrawerIconsFromFrontend();

    // 设置高度
    void setBarHeight(int height);

    // 设置是否使用Mica效果
    void setMicaEffectEnabled(bool enabled);

    // 获取当前Mica效果状态
    bool isMicaEffectEnabled() const;

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;

signals:
    void minimizeClicked();
    void maximizeClicked();
    void closeClicked();

    // 抽屉控制信号
    void leftDrawerToggled();
    void rightDrawerToggled();

    // 工具按钮信号
    void reloadRequested();
    void devToolsRequested();
    void modeToggleRequested();
    void themeToggleRequested();

    // 抽屉状态同步信号
    void requestDrawerStateSync();

    // Mica测试信号
    void micaTestRequested();

private slots:
    void onMinimizeClicked();
    void onMaximizeClicked();
    void onCloseClicked();

    // 工具按钮槽函数
    void onLeftDrawerClicked();
    void onRightDrawerClicked();
    void onThemeToggleClicked();
    void onReloadClicked();
    void onDevToolsClicked();
    void onModeToggleClicked();
    void onMicaTestClicked();

private:
    void setupUI();
    void setupConnections();
    void applyTheme();
    void updateButtonIcons();
    void updateDrawerButtonIcons();
    void updateToolButtonIcons();
    void loadDefaultIcon();
    void updateAppIcon(const QPixmap &icon);
    QPushButton *createToolButton(const QString &iconPath, const QString &tooltip);
    void applyToolButtonTheme(QPushButton *button, bool isDarkTheme);

    QHBoxLayout *m_layout;
    QLabel *m_iconLabel;
    QLabel *m_titleLabel;
    QWidget *m_spacer;

    QPushButton *m_minimizeButton;
    QPushButton *m_maximizeButton;
    QPushButton *m_closeButton;

    // 工具按钮
    QPushButton *m_leftDrawerBtn;
    QPushButton *m_rightDrawerBtn;
    QPushButton *m_themeBtn;

    // 开发模式按钮
    QPushButton *m_reloadBtn;
    QPushButton *m_devToolsBtn;
    QPushButton *m_toggleModeBtn;
    QPushButton *m_micaTestBtn;

    bool m_isDarkTheme;
    bool m_isMaximized;
    bool m_isLeftDrawerOpened;
    bool m_isRightDrawerOpened;
    int m_barHeight;
    bool m_micaEffectEnabled;
};

#endif // QWKCUSTOMTITLEBAR_H