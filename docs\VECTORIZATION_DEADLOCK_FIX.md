# 向量化卡死问题修正报告

## 问题分析

根据终端输出，系统在 `llama_decode` 阶段卡死，原因可能包括：

### 1. 嵌套锁问题
**问题**: 在 `generateEmbedding()` 中同时持有推理锁和读写锁
**修正**: 移除嵌套锁，在推理锁内部进行简单的状态检查

### 2. 批处理逻辑错误
**问题**: `BatchVectorizationWorker` 中即使已生成向量仍调用 `processDocumentVectorization`
**修正**: 修改批处理逻辑，避免重复处理

### 3. 信号量设置不当
**问题**: 信号量设置为2但实际只支持1个并发推理
**修正**: 将信号量改为1，确保严格的串行处理

## 具体修正

### 修正1: 消除嵌套锁
```cpp
// 原来的问题代码
{
    QMutexLocker inferenceLocker(&m_inferenceMutex);
    QReadLocker locker(&m_rwLock);  // 嵌套锁！
    // ...
}

// 修正后的代码
{
    QMutexLocker inferenceLocker(&m_inferenceMutex);
    // 在推理锁内部快速检查，避免嵌套锁
    if (!m_modelLoaded || !m_model || !m_context) {
        // 错误处理
    }
    // ...
}
```

### 修正2: 临时禁用批处理
```cpp
// 暂时禁用批处理以避免复杂性
bool batchEnabled = false; // 临时禁用批处理
```

### 修正3: 信号量改为1
```cpp
LocalGGUFEmbedding::LocalGGUFEmbedding(QObject *parent)
    : QObject(parent), m_inferenceSemaphore(1) // 改为1个并发
```

### 修正4: 增强调试信息
```cpp
// 添加推理前的状态检查
logDebug(QString("Model status - loaded: %1, context: %2")
             .arg(m_modelLoaded ? "true" : "false")
             .arg(m_context ? "valid" : "null"));
```

## 预期效果

修正后应该能够：
1. **避免死锁** - 消除嵌套锁和批处理复杂性
2. **串行推理** - 确保一次只有一个推理进行
3. **更好调试** - 提供详细的状态信息
4. **稳定运行** - 回退到经过验证的单文档处理逻辑

## 测试建议

1. **重新构建**: 运行 `.\win-dev.ps1` 重新构建
2. **单文档测试**: 添加一个简短的文档测试向量化
3. **观察日志**: 查看详细的调试输出
4. **监控时间**: 确认推理不再卡死

## 后续优化

问题解决后可以考虑：
1. 重新启用批处理（修正逻辑后）
2. 适当增加并发数量
3. 实现更高效的向量化策略

当前修正采用保守策略，优先确保稳定性。