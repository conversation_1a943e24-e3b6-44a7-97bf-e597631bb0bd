/**
 * TipTap编辑器图片处理系统
 * 重构版本：统一处理拖拽、粘贴、删除等图片操作
 */

import type { Editor } from '@tiptap/core';
import { imageManager } from 'src/utils/imageManager';

/**
 * 图片处理器类
 */
export class EditorImageHandler {
  private editor: Editor;
  private documentId: number;
  private processingImages = new Set<string>(); // 正在处理的图片URL
  private isProcessingContent = false; // 标记是否正在处理内容
  private isInsertingImage = false; // 标记是否正在插入图片
  private recentlyCreatedImages = new Set<number>(); // 记录最近创建的图片ID，用于拖拽保护

  constructor(editor: Editor, documentId: number) {
    this.editor = editor;
    this.documentId = documentId;
  }

  /**
   * 初始化图片处理器
   */
  init() {
    this.setupDragAndDrop();
    this.setupPasteHandler();
    this.setupImageDeleteHandler();
    this.setupContentChangeHandler();
  }

  /**
   * 设置拖拽处理
   */
  private setupDragAndDrop() {
    const editorElement = this.editor.view.dom;

    editorElement.addEventListener('dragover', (e) => {
      e.preventDefault();
    });

    editorElement.addEventListener('drop', (e) => {
      const dataTransfer = e.dataTransfer;
      if (!dataTransfer) return;

      // 检查是否包含图片相关内容
      const files = Array.from(dataTransfer.files || []);
      const imageFiles = files.filter((file) => file.type.startsWith('image/'));
      const htmlData = dataTransfer.getData('text/html');
      const urlData = dataTransfer.getData('text/uri-list') || dataTransfer.getData('text/plain');

      // 检查是否为内部图片拖拽（编辑器内拖拽）
      const isInternalDrag = this.checkInternalImageDrag(dataTransfer);

      // 如果包含图片内容，阻止默认行为并自定义处理
      const hasImageContent =
        imageFiles.length > 0 ||
        (htmlData && htmlData.includes('<img')) ||
        (urlData && this.isImageUrl(urlData.trim())) ||
        isInternalDrag;

      if (hasImageContent) {
        e.preventDefault();
        e.stopPropagation();

        // 处理内部图片拖拽（优先级最高）
        if (isInternalDrag) {
          void this.handleInternalImageDrag(e, dataTransfer);
          return;
        }

        // 处理文件拖拽
        if (imageFiles.length > 0) {
          void this.handleImageFiles(imageFiles);
          return;
        }

        // 处理HTML内容拖拽（包含图片）
        if (htmlData && htmlData.includes('<img')) {
          void this.handleDraggedHtmlContent(htmlData);
          return;
        }

        // 处理URL拖拽
        if (urlData && this.isImageUrl(urlData.trim())) {
          void this.handleDraggedImageUrl(urlData.trim());
          return;
        }
      }

      // 对于非图片内容，让TipTap处理默认行为
    });
  }

  /**
   * 设置粘贴处理
   */
  private setupPasteHandler() {
    this.editor.on('paste', ({ event }) => {
      console.log('[ImageHandler] 粘贴事件触发');
      const clipboardData = event.clipboardData;
      if (!clipboardData) {
        console.log('[ImageHandler] 没有剪贴板数据');
        return;
      }

      // 处理粘贴的文件
      const files = Array.from(clipboardData.files);
      const imageFiles = files.filter((file) => file.type.startsWith('image/'));

      if (imageFiles.length > 0) {
        console.log('[ImageHandler] 检测到图片文件，数量:', imageFiles.length);
        event.preventDefault();
        void this.handleImageFiles(imageFiles);
        return;
      }

      // 处理粘贴的HTML内容中的图片
      const htmlData = clipboardData.getData('text/html');
      if (htmlData) {
        console.log('[ImageHandler] 检测到HTML数据，准备延迟处理图片');
        console.log('[ImageHandler] HTML内容预览:', htmlData.substring(0, 200) + '...');
        setTimeout(() => {
          console.log('[ImageHandler] 开始延迟处理图片内容');
          void this.processImagesInContent();
        }, 100); // 延迟处理，确保内容已插入
      } else {
        console.log('[ImageHandler] 没有HTML数据');
      }
    });
  }

  /**
   * 设置内容变化处理器
   */
  private setupContentChangeHandler() {
    // 监听编辑器内容变化，自动处理新插入的网络图片
    let processingTimeout: NodeJS.Timeout | null = null;

    this.editor.on('update', ({ transaction }) => {
      // 如果正在插入图片或处理内容，跳过自动处理
      if (this.isInsertingImage || this.isProcessingContent) {
        return;
      }

      // 检查是否有新插入的内容
      let hasNewContent = false;

      transaction.steps.forEach((step: unknown) => {
        const stepWithType = step as { stepType?: string };
        // 只处理插入操作，不处理删除操作
        if (stepWithType.stepType === 'replace' || stepWithType.stepType === 'replaceAround') {
          hasNewContent = true;
        }
      });

      // 只有在有新内容插入时才处理
      if (hasNewContent) {
        // 清除之前的定时器
        if (processingTimeout) {
          clearTimeout(processingTimeout);
        }

        // 延迟处理，避免在事务中修改内容，并防止频繁触发
        processingTimeout = setTimeout(() => {
          console.log('检测到新内容插入，开始处理图片...');
          void this.processImagesInContent();
        }, 300);
      }
    });
  }

  /**
   * 设置图片删除处理
   */
  private setupImageDeleteHandler() {
    // 记录上一次的图片ID集合
    let previousImageIds = new Set<number>();

    // 获取当前文档中的所有图片ID
    const getCurrentImageIds = (): Set<number> => {
      const imageIds = new Set<number>();
      this.editor.state.doc.descendants((node) => {
        if (node.type.name === 'image' && node.attrs['data-image-id']) {
          const imageId = parseInt(node.attrs['data-image-id']);
          if (!isNaN(imageId)) {
            imageIds.add(imageId);
          }
        }
      });
      return imageIds;
    };

    // 初始化当前图片ID集合
    previousImageIds = getCurrentImageIds();

    this.editor.on('update', () => {
      // 如果正在处理内容，跳过删除检测
      if (this.isProcessingContent) {
        return;
      }

      // 获取更新后的图片ID集合
      const currentImageIds = getCurrentImageIds();

      // 找出被删除的图片ID
      const deletedImages: number[] = [];
      previousImageIds.forEach((imageId) => {
        if (!currentImageIds.has(imageId)) {
          deletedImages.push(imageId);
        }
      });

      // 更新记录
      previousImageIds = currentImageIds;

      // 处理删除的图片
      if (deletedImages.length > 0) {
        console.log('检测到删除的图片:', deletedImages);
        this.handleImageDeletes(deletedImages);
      }
    });
  }

  /**
   * 处理图片文件
   */
  private async handleImageFiles(files: File[]) {
    console.log(`开始处理 ${files.length} 个图片文件`);

    for (const file of files) {
      try {
        const result = await imageManager.processImageFile(file, this.documentId);

        if (result.success && result.imageId && result.localSrc) {
          // 设置插入标记，防止内容变化处理器触发
          this.isInsertingImage = true;

          // 插入图片到编辑器（不设置src，让NodeView动态加载）
          this.editor
            .chain()
            .focus()
            .insertContent({
              type: 'image',
              attrs: {
                'data-image-id': result.imageId,
                alt: file.name,
              },
            })
            .run();

          // 延迟清除标记
          setTimeout(() => {
            this.isInsertingImage = false;
          }, 100);

          console.log(`图片文件处理成功: ${file.name} -> ID: ${result.imageId}`);
        } else {
          console.error(`图片文件处理失败: ${file.name}`, result.error);
        }
      } catch (error) {
        console.error(`处理图片文件时发生错误: ${file.name}`, error);
      }
    }
  }

  /**
   * 处理内容中的所有图片（包括HTTP图片和blob图片）
   */
  private async processImagesInContent() {
    console.log('[ImageHandler] processImagesInContent 被调用');
    // 设置处理标记，防止删除检测器误触发
    this.isProcessingContent = true;

    try {
      // 处理HTTP图片
      console.log('[ImageHandler] 开始处理HTTP图片');
      await this.processHttpImagesInContent();

      // 处理blob图片（文档间复制的情况）
      console.log('[ImageHandler] 开始处理blob图片');
      await this.processBlobImagesInContent();

      // 处理data:image格式的图片（复制粘贴的情况）
      console.log('[ImageHandler] 开始处理data:image格式图片');
      this.processDataImagesInContent();
    } finally {
      // 清除处理标记
      this.isProcessingContent = false;
      console.log('[ImageHandler] 图片内容处理完成');
    }
  }

  /**
   * 处理内容中的HTTP图片
   */
  private async processHttpImagesInContent() {
    const editorDom = this.editor.view.dom;
    const imgElements = editorDom.querySelectorAll('img[src^="http"]');

    if (imgElements.length === 0) return;

    console.log(`发现 ${imgElements.length} 个HTTP图片，开始处理...`);

    for (const imgElement of imgElements) {
      const httpUrl = imgElement.getAttribute('src');
      if (!httpUrl || this.processingImages.has(httpUrl)) continue;

      this.processingImages.add(httpUrl);

      try {
        const result = await imageManager.processImageUrl(httpUrl, this.documentId);

        if (result.success && result.imageId && result.localSrc) {
          // 更新图片元素（只设置data-image-id，让NodeView处理src）
          imgElement.setAttribute('data-image-id', result.imageId.toString());
          imgElement.removeAttribute('src'); // 移除原始src

          // 更新编辑器内容
          this.updateImageInEditor(httpUrl, result.imageId);

          console.log(`HTTP图片处理成功: ${httpUrl} -> ID: ${result.imageId}`);
        } else {
          console.error(`HTTP图片处理失败: ${httpUrl}`, result.error);
        }
      } catch (error) {
        console.error(`处理HTTP图片时发生错误: ${httpUrl}`, error);
      } finally {
        this.processingImages.delete(httpUrl);
      }
    }
  }

  /**
   * 处理内容中的blob图片（文档间复制的情况）
   */
  private async processBlobImagesInContent() {
    const editorDom = this.editor.view.dom;
    const imgElements = editorDom.querySelectorAll('img[src^="blob:"]');

    if (imgElements.length === 0) return;

    console.log(`发现 ${imgElements.length} 个blob图片，开始处理...`);

    for (const imgElement of imgElements) {
      const blobUrl = imgElement.getAttribute('src');
      if (!blobUrl) continue;

      console.log('处理blob图片:', blobUrl);

      // 检查是否已经有data-image-id
      const existingImageId = imgElement.getAttribute('data-image-id');
      if (existingImageId) {
        console.log('图片已有ID，跳过处理:', existingImageId);
        continue;
      }

      // 尝试从blob URL映射中获取图片ID
      let imageId: number | null = null;

      try {
        const { getImageIdFromBlobUrl } = await import('src/utils/blobUrlMapping');
        imageId = getImageIdFromBlobUrl(blobUrl);
        console.log('从blob URL映射中获取图片ID:', imageId);
      } catch (error) {
        console.warn('获取blob URL映射失败:', error);
      }

      // 如果映射中没有找到，尝试从DOM中找到对应的图片ID
      if (!imageId) {
        console.log('尝试从DOM中查找图片ID');
        const allImgElements = document.querySelectorAll('img[src="' + blobUrl + '"]');

        for (const otherImgElement of allImgElements) {
          const dataImageId = otherImgElement.getAttribute('data-image-id');
          if (dataImageId && otherImgElement !== imgElement) {
            imageId = parseInt(dataImageId);
            console.log('从其他图片元素找到ID:', imageId);
            break;
          }
        }
      }

      if (imageId) {
        // 添加图片引用到当前文档
        try {
          // 使用新的主动关联方法（简化版本，不等待结果）
          imageManager.associateImageWithDocument(imageId, this.documentId);

          // 更新图片元素
          imgElement.setAttribute('data-image-id', imageId.toString());
          imgElement.removeAttribute('src'); // 移除blob src

          // 更新编辑器内容
          this.updateImageInEditor(blobUrl, imageId);

          console.log(`blob图片处理成功: ${blobUrl} -> ID: ${imageId}`);
        } catch (error) {
          console.error(`处理blob图片时发生错误: ${blobUrl}`, error);
        }
      } else {
        console.log('无法找到blob图片的ID:', blobUrl);
      }
    }
  }

  /**
   * 处理内容中的data:image格式图片（复制粘贴的情况）
   */
  private processDataImagesInContent() {
    const editorDom = this.editor.view.dom;
    const imgElements = editorDom.querySelectorAll('img[src^="data:image/"]');

    if (imgElements.length === 0) {
      console.log('[ImageHandler] 没有发现data:image格式的图片');
      return;
    }

    console.log(`[ImageHandler] 发现 ${imgElements.length} 个data:image格式图片，开始处理...`);

    for (const imgElement of imgElements) {
      const dataSrc = imgElement.getAttribute('src');
      const dataImageId = imgElement.getAttribute('data-image-id');

      console.log('[ImageHandler] 处理data:image图片:', {
        src: dataSrc?.substring(0, 50) + '...',
        dataImageId,
      });

      if (dataImageId) {
        const imageId = parseInt(dataImageId);
        if (!isNaN(imageId)) {
          console.log(
            `[ImageHandler] 主动关联图片 ${imageId} 与文档 ${this.documentId} (来自data:image)`,
          );

          try {
            // 使用新的主动关联方法（简化版本，不等待结果）
            imageManager.associateImageWithDocument(imageId, this.documentId);

            console.log(
              `[ImageHandler] 图片关联事件已触发: ${imageId} -> 文档 ${this.documentId} (data:image)`,
            );
            // 移除data src，让NodeView处理
            imgElement.removeAttribute('src');
          } catch (error) {
            console.error(`[ImageHandler] 处理data:image图片时发生错误:`, error);
          }
        }
      }
    }
  }

  /**
   * 更新编辑器中的图片节点
   */
  private updateImageInEditor(oldSrc: string, imageId: number) {
    const { tr } = this.editor.state;
    let updated = false;

    this.editor.state.doc.descendants((node, pos) => {
      if (node.type.name === 'image' && node.attrs.src === oldSrc) {
        tr.setNodeMarkup(pos, undefined, {
          ...node.attrs,
          'data-image-id': imageId,
          // 移除src属性，让NodeView动态加载
          src: undefined,
        });
        updated = true;
      }
    });

    if (updated) {
      this.editor.view.dispatch(tr);
    }
  }

  /**
   * 处理图片删除
   */
  private handleImageDeletes(imageIds: number[]) {
    console.log(`处理图片删除: ${imageIds.join(', ')}`);

    for (const imageId of imageIds) {
      // 检查是否为最近创建的图片（可能是拖拽操作）
      if (this.recentlyCreatedImages.has(imageId)) {
        console.log(`图片 ${imageId} 是最近创建的，可能是拖拽操作，延迟删除检查`);

        // 延迟删除检查，给拖拽操作足够时间完成
        setTimeout(() => {
          if (!this.recentlyCreatedImages.has(imageId)) {
            // 如果图片已经不在保护列表中，说明保护期已过，可以安全删除
            void this.safeDeleteImage(imageId);
          } else {
            console.log(`图片 ${imageId} 仍在保护期内，跳过删除`);
          }
        }, 5000); // 延长到5秒，确保拖拽完成
      } else {
        // 不在保护期内的图片，使用更安全的删除检查
        void this.safeDeleteImage(imageId);
      }
    }
  }

  /**
   * 安全删除图片（带引用检查）
   */
  private async safeDeleteImage(imageId: number) {
    try {
      // 重新检查图片引用情况
      const references = await imageManager.getImageReferences(imageId);
      console.log(`安全删除检查: 图片 ${imageId} 当前引用数量: ${references.length}`);

      if (references.length === 0) {
        // 确实无引用，可以安全删除
        await imageManager.handleImageDelete(imageId, this.documentId);
        console.log(`安全删除图片: ${imageId}`);
      } else {
        console.log(
          `图片 ${imageId} 仍有引用，跳过删除:`,
          references.map((r) => r.document_title),
        );
      }
    } catch (error) {
      console.error(`安全删除图片失败: ${imageId}`, error);
    }
  }

  /**
   * 转换文档内容中的图片引用
   */
  async convertImagesInContent(content: unknown): Promise<unknown> {
    if (!content || typeof content !== 'object') {
      return content;
    }

    // 如果是数组，递归处理每个元素
    if (Array.isArray(content)) {
      const processedArray = await Promise.all(
        content.map((item) => this.convertImagesInContent(item)),
      );
      return processedArray;
    }

    // 如果是图片节点，转换src
    const imageContentObj = content as {
      type?: string;
      attrs?: { src?: string; [key: string]: unknown };
    };
    if (
      imageContentObj.type === 'image' &&
      imageContentObj.attrs &&
      typeof imageContentObj.attrs.src === 'string'
    ) {
      const src = imageContentObj.attrs.src;

      // 处理image://id格式的图片
      if (src.startsWith('image://')) {
        const imageId = parseInt(src.replace('image://', ''));
        if (!isNaN(imageId)) {
          // 主动建立图片与文档的关联
          console.log(`主动关联图片 ${imageId} 与文档 ${this.documentId}`);
          imageManager.associateImageWithDocument(imageId, this.documentId);

          // 不设置src，只设置data-image-id，让NodeView动态加载
          return {
            ...imageContentObj,
            attrs: {
              ...imageContentObj.attrs,
              'data-image-id': imageId,
              // 移除src属性
              src: undefined,
            },
          };
        }
      }

      // 处理其他格式的图片（如blob、data等），也移除src
      if (src.startsWith('blob:') || src.startsWith('data:')) {
        // 如果有data-image-id，保留它并建立关联
        if (imageContentObj.attrs['data-image-id']) {
          const dataImageId = imageContentObj.attrs['data-image-id'];
          const imageId =
            typeof dataImageId === 'number'
              ? dataImageId
              : typeof dataImageId === 'string'
                ? parseInt(dataImageId)
                : NaN;
          if (!isNaN(imageId)) {
            // 主动建立图片与文档的关联
            console.log(`主动关联图片 ${imageId} 与文档 ${this.documentId} (来自data/blob图片)`);
            imageManager.associateImageWithDocument(imageId, this.documentId);
          }

          return {
            ...imageContentObj,
            attrs: {
              ...imageContentObj.attrs,
              src: undefined,
            },
          };
        }
      }
    }

    // 递归处理对象的所有属性
    const result: Record<string, unknown> = {};
    const contentObj = content as Record<string, unknown>;
    for (const key in contentObj) {
      if (Object.prototype.hasOwnProperty.call(contentObj, key)) {
        result[key] = await this.convertImagesInContent(contentObj[key]);
      }
    }

    return result;
  }

  /**
   * 处理拖拽的HTML内容
   */
  private async handleDraggedHtmlContent(htmlContent: string) {
    console.log('处理拖拽的HTML内容...');

    // 创建临时DOM解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // 查找所有图片元素
    const imgElements = tempDiv.querySelectorAll('img');

    if (imgElements.length === 0) {
      console.log('HTML内容中没有图片元素');
      return;
    }

    console.log(`发现 ${imgElements.length} 个图片元素，开始处理...`);

    // 处理每个图片元素
    for (const img of imgElements) {
      const src = img.getAttribute('src');
      if (src && src.startsWith('http')) {
        console.log('处理网络图片:', src);
        // 直接处理网络图片并插入到编辑器
        await this.handleDraggedImageUrl(src);
      } else if (src && src.startsWith('data:')) {
        console.log('处理base64图片');
        // 处理base64图片并插入到编辑器
        await this.handleDraggedBase64Image(src);
      } else {
        console.log('跳过非网络/base64图片:', src);
      }
    }
  }

  /**
   * 处理拖拽的图片URL
   */
  private async handleDraggedImageUrl(imageUrl: string) {
    console.log('处理拖拽的图片URL:', imageUrl);

    try {
      const result = await imageManager.processImageUrl(imageUrl, this.documentId);

      if (result.success && result.imageId) {
        // 设置插入标记，防止内容变化处理器触发
        this.isInsertingImage = true;

        // 插入图片到编辑器
        this.editor
          .chain()
          .focus()
          .insertContent({
            type: 'image',
            attrs: {
              'data-image-id': result.imageId,
              alt: imageUrl.split('/').pop() || '拖拽的图片',
            },
          })
          .run();

        // 延迟清除标记
        setTimeout(() => {
          this.isInsertingImage = false;
        }, 100);

        console.log(`拖拽图片URL处理成功: ${imageUrl} -> ID: ${result.imageId}`);
      } else {
        console.error(`拖拽图片URL处理失败: ${imageUrl}`, result.error);
      }
    } catch (error) {
      console.error('处理拖拽图片URL失败:', error);
    }
  }

  /**
   * 处理拖拽的Base64图片
   */
  private async handleDraggedBase64Image(dataUrl: string) {
    console.log('处理拖拽的Base64图片');

    try {
      // 解析base64数据
      const [header, data] = dataUrl.split(',');
      const mimeMatch = header.match(/data:([^;]+)/);
      const mimeType = mimeMatch ? mimeMatch[1] : 'image/png';

      // 保存图片
      const result = await imageManager.saveImageFromData(data, mimeType, dataUrl);

      if (result.success && result.imageId) {
        // 使用新的主动关联方法（简化版本，不等待结果）
        imageManager.associateImageWithDocument(result.imageId, this.documentId);

        // 设置插入标记，防止内容变化处理器触发
        this.isInsertingImage = true;

        // 插入图片到编辑器
        this.editor
          .chain()
          .focus()
          .insertContent({
            type: 'image',
            attrs: {
              'data-image-id': result.imageId,
              alt: '拖拽的图片',
            },
          })
          .run();

        // 延迟清除标记
        setTimeout(() => {
          this.isInsertingImage = false;
        }, 100);

        console.log(`拖拽Base64图片处理成功: ID: ${result.imageId}`);
      } else {
        console.error('拖拽Base64图片处理失败:', result.error);
      }
    } catch (error) {
      console.error('处理拖拽Base64图片失败:', error);
    }
  }

  /**
   * 检查是否为内部图片拖拽
   */
  private checkInternalImageDrag(dataTransfer: DataTransfer): boolean {
    const htmlData = dataTransfer.getData('text/html');
    if (!htmlData) return false;

    // 检查HTML内容是否包含来自当前编辑器的图片
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlData;
    const imgElements = tempDiv.querySelectorAll('img[data-image-id]');

    return imgElements.length > 0;
  }

  /**
   * 处理内部图片拖拽（编辑器内拖拽）
   */
  private handleInternalImageDrag(event: DragEvent, dataTransfer: DataTransfer) {
    console.log('[ImageHandler] 处理内部图片拖拽');

    try {
      // 获取拖拽的HTML内容
      const htmlData = dataTransfer.getData('text/html');
      if (!htmlData) return;

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlData;
      const imgElements = tempDiv.querySelectorAll('img[data-image-id]');

      // 收集所有被拖拽的图片ID
      const draggedImageIds: number[] = [];
      for (const img of imgElements) {
        const imageId = img.getAttribute('data-image-id');
        if (imageId) {
          const imageIdNum = parseInt(imageId);
          draggedImageIds.push(imageIdNum);
          
          // 保护内部拖拽的图片不被删除
          this.recentlyCreatedImages.add(imageIdNum);
          setTimeout(() => {
            this.recentlyCreatedImages.delete(imageIdNum);
          }, 5000); // 延长保护期到5秒
          
          console.log(`[ImageHandler] 保护内部拖拽图片: ${imageIdNum}`);
        }
      }

      // 允许TipTap处理默认的拖拽行为，实现位置移动
      // 不再阻止默认行为，让TipTap正常处理拖拽
      console.log('[ImageHandler] 允许TipTap处理默认拖拽行为，图片ID:', draggedImageIds);
    } catch (error) {
      console.error('[ImageHandler] 处理内部图片拖拽失败:', error);
    }
  }

  /**
   * 检查URL是否为图片
   */
  private isImageUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();
      return (
        /\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)(\?.*)?$/i.test(pathname) ||
        url.includes('image') ||
        url.includes('photo') ||
        url.includes('picture')
      );
    } catch {
      return false;
    }
  }

  /**
   * 清理资源
   */
  destroy() {
    this.processingImages.clear();
  }
}

/**
 * 创建图片处理器
 */
export function createImageHandler(editor: Editor, documentId: number): EditorImageHandler {
  return new EditorImageHandler(editor, documentId);
}
