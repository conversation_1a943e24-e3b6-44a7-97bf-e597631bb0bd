import type { z } from 'zod';

export interface ToolMetadata {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: { [key: string]: { type: string; description: string } };
    required: string[];
  };
}

export abstract class BaseTool<T extends z.ZodTypeAny> {
  protected abstract _name: string;
  protected abstract _description: string;
  protected abstract _schema: T;

  abstract _call(args: z.infer<T>): Promise<unknown>;

  get name(): string {
    return this._name;
  }

  get description(): string {
    return this._description;
  }

  get schema(): T {
    return this._schema;
  }

  get metadata(): ToolMetadata {
    return {
      name: this._name,
      description: this._description,
      parameters: this._schema.parse({}).parameters,
    };
  }
}
