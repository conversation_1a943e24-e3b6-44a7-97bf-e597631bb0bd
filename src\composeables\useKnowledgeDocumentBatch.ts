import { ref, onUnmounted } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useKnowledge } from './useKnowledge';
import { useKnowledgeStore } from 'src/stores/knowledge';
import type { ChunkingMethod, ChunkingConfig } from 'src/types/qwen';
import type { ChunkingResult } from '../utils/knowledgeBase';

/**
 * 批量知识库文档处理的hook
 * 抽离自KnowledgeBaseManager.vue的逻辑
 */
export function useKnowledgeDocumentBatch() {
  const $q = useQuasar();
  const { t: $t } = useI18n({ useScope: 'global' });
  const knowledge = useKnowledge();
  const knowledgeStore = useKnowledgeStore();

  // 待处理的文档数据
  const pendingDocuments = ref<
    Map<
      string,
      {
        documentId: number;
        data: {
          title: string;
          content: string;
          chunkingMethod: ChunkingMethod;
          chunkingConfig: ChunkingConfig;
        };
      }
    >
  >(new Map());

  // 处理中的文档
  const processingDocuments = ref<Set<number>>(new Set());

  // 最近完成的文档
  const recentlyCompletedDocs = ref<Set<number>>(new Set());

  // 向量化进度
  const vectorizationProgress = ref<Map<number, { completed: number; total: number }>>(new Map());

  // 完成的chunks跟踪器
  const completedChunksTracker = ref<Map<number, number>>(new Map());

  /**
   * 创建知识库文档（仅文档，不包含chunks）
   */
  const createKnowledgeDocumentOnly = async (
    knowledgeBaseId: number,
    title: string,
    content: string,
    chunkingMethod: ChunkingMethod,
    chunkingConfig: ChunkingConfig,
  ) => {
    console.log('🚀 [useKnowledgeDocumentBatch] 开始创建知识库文档:', {
      knowledgeBaseId,
      title,
      contentLength: content.length,
    });

    try {
      // 第一步：创建知识库文档（不包含chunks）
      const result = await knowledge.createKnowledgeDocumentOnly(
        knowledgeBaseId,
        title,
        content,
        'markdown', // documentType
      );

      // callKnowledgeApi 已经处理了 success 检查，直接返回 data 字段内容
      if (!result || !result.id) {
        throw new Error($t('src.composeables.useKnowledgeDocumentBatch.create_document_failed'));
      }

      // 保持文档ID为数字格式，与数据库字段类型一致
      const documentId = typeof result.id === 'string' ? parseInt(result.id) : result.id;

      console.log('✅ [useKnowledgeDocumentBatch] 知识库文档创建成功，ID:', documentId);

      // 存储待处理的文档数据
      const documentKey = `${knowledgeBaseId}-${documentId}`;
      pendingDocuments.value.set(documentKey, {
        documentId,
        data: {
          title,
          content,
          chunkingMethod,
          chunkingConfig,
        },
      });

      return documentId;
    } catch (error) {
      console.error('💥 [useKnowledgeDocumentBatch] 创建文档失败:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : $t('src.composeables.useKnowledgeDocumentBatch.create_document_failed');

      $q.notify({
        type: 'negative',
        message: errorMessage,
      });

      throw error;
    }
  };

  /**
   * 处理切割完成事件
   */
  const handleChunkingCompleted = async (
    knowledgeBaseId: number,
    eventData: {
      documentData: {
        title: string;
        content: string;
        chunkingMethod: ChunkingMethod;
        chunkingConfig: ChunkingConfig;
      };
      chunkingResult: ChunkingResult;
    },
  ) => {
    console.log('🧩 [useKnowledgeDocumentBatch] 切割完成，准备提交切割结果:', {
      chunkCount: eventData.chunkingResult.chunkCount,
      title: eventData.documentData.title,
    });

    try {
      // 查找对应的文档ID
      const documentKey = Array.from(pendingDocuments.value.keys()).find((key) => {
        const pending = pendingDocuments.value.get(key);
        return pending?.data.title === eventData.documentData.title;
      });

      if (!documentKey) {
        console.error('💥 [useKnowledgeDocumentBatch] 找不到对应的待处理文档');
        return;
      }

      const pendingDoc = pendingDocuments.value.get(documentKey);
      if (!pendingDoc) return;

      // 第三步：提交切割结果到Qt后端
      console.log('📤 [useKnowledgeDocumentBatch] 提交切割结果到Qt后端...');

      // 调用Qt后端接口提交切割结果
      // eventData.chunkingResult.chunks 已经是 DocumentChunk[] 格式，直接使用
      const submitResult = await knowledge.submitChunkingResults(
        pendingDoc.documentId,
        eventData.chunkingResult.chunks,
      );
      const success = submitResult.success;

      if (success) {
        console.log('✅ [useKnowledgeDocumentBatch] 切割结果提交成功');
        processingDocuments.value.add(pendingDoc.documentId);
      } else {
        $q.notify({
          type: 'negative',
          message: $t('src.composeables.useKnowledgeDocumentBatch.submit_chunking_result_failed'),
        });
      }

      // 从待处理列表中移除
      pendingDocuments.value.delete(documentKey);
    } catch (error) {
      console.error('💥 [useKnowledgeDocumentBatch] 提交切割结果失败:', error);
      $q.notify({
        type: 'negative',
        message: $t('src.composeables.useKnowledgeDocumentBatch.submit_chunking_result_failed'),
      });
    }
  };

  /**
   * 处理切割失败事件
   */
  const handleChunkingFailed = (eventData: { documentData: { title: string }; error: string }) => {
    console.error('💥 [useKnowledgeDocumentBatch] 切割失败:', eventData.error);

    // 查找并清理待处理的文档
    const documentKey = Array.from(pendingDocuments.value.keys()).find((key) => {
      const pending = pendingDocuments.value.get(key);
      return pending?.data.title === eventData.documentData.title;
    });

    if (documentKey) {
      pendingDocuments.value.delete(documentKey);
    }

    $q.notify({
      type: 'negative',
      message: $t('src.composeables.useKnowledgeDocumentBatch.chunking_failed'),
      caption: eventData.error,
    });
  };

  /**
   * 设置事件监听器
   */
  const setupEventListeners = () => {
    // 设置文档向量化完成监听
    const unsubscribeVectorized = knowledge.onDocumentVectorized(
      (docId: string, chunkCount: number) => {
        console.log('📡 [useKnowledgeDocumentBatch] 收到文档向量化完成:', docId, chunkCount);

        const docIdNum = parseInt(docId);

        // 确保清理所有状态数据
        processingDocuments.value.delete(docIdNum);
        vectorizationProgress.value.delete(docIdNum);
        completedChunksTracker.value.delete(docIdNum);

        if (chunkCount > 0) {
          // 如果还没有在完成列表中，添加进去
          if (!recentlyCompletedDocs.value.has(docIdNum)) {
            recentlyCompletedDocs.value.add(docIdNum);

            // 3秒后移除完成状态
            setTimeout(() => {
              recentlyCompletedDocs.value.delete(docIdNum);
            }, 3000);
          }

          $q.notify({
            type: 'positive',
            message: $t(
              'src.composeables.useKnowledgeDocumentBatch.document_vectorized_successfully',
            ),
            caption: $t('src.composeables.useKnowledgeDocumentBatch.document_vectorized_caption', {
              docId,
              chunkCount,
            }),
            timeout: 3000,
          });

          // 延迟刷新数据，确保数据库已更新
          setTimeout(() => {
            void knowledgeStore.refreshKnowledgeBases();
          }, 1000);
        } else {
          $q.notify({
            type: 'negative',
            message: $t('src.composeables.useKnowledgeDocumentBatch.document_vectorization_failed'),
            caption: $t('src.composeables.useKnowledgeDocumentBatch.document_id') + ': ' + docId,
            timeout: 5000,
          });
        }
      },
    );

    // 设置向量化进度监听
    const unsubscribeProgress = knowledge.onVectorizationProgress(
      (kbId: string, docId: string, completed: number, total: number) => {
        console.log('📡 [useKnowledgeDocumentBatch] 收到向量化进度:', {
          kbId,
          docId,
          completed,
          total,
        });

        const docIdNum = parseInt(docId);
        const previousCompleted = completedChunksTracker.value.get(docIdNum) || 0;

        // 计算新增的已完成chunk数量
        const newCompletedChunks = completed - previousCompleted;

        if (newCompletedChunks > 0) {
          console.log('📊 [useKnowledgeDocumentBatch] 向量化进度更新:', {
            kbId,
            docId: docIdNum,
            newCompleted: newCompletedChunks,
          });
        }

        // 更新跟踪器
        completedChunksTracker.value.set(docIdNum, completed);

        // 如果向量化完成，清理进度数据并标记为完成
        if (completed === total && total > 0) {
          console.log('📡 [useKnowledgeDocumentBatch] 向量化进度完成，清理数据:', docIdNum);
          vectorizationProgress.value.delete(docIdNum);
          processingDocuments.value.delete(docIdNum);
          recentlyCompletedDocs.value.add(docIdNum);

          // 清理跟踪器
          completedChunksTracker.value.delete(docIdNum);

          // 3秒后移除完成状态
          setTimeout(() => {
            recentlyCompletedDocs.value.delete(docIdNum);
          }, 3000);
        }
      },
    );

    // 返回清理函数
    return () => {
      unsubscribeVectorized();
      unsubscribeProgress();
    };
  };

  // 组件卸载时清理
  let cleanupListeners: (() => void) | null = null;

  const initialize = () => {
    if (!cleanupListeners) {
      cleanupListeners = setupEventListeners();
    }
  };

  const cleanup = () => {
    if (cleanupListeners) {
      cleanupListeners();
      cleanupListeners = null;
    }

    // 清理所有状态
    pendingDocuments.value.clear();
    processingDocuments.value.clear();
    recentlyCompletedDocs.value.clear();
    vectorizationProgress.value.clear();
    completedChunksTracker.value.clear();
  };

  onUnmounted(() => {
    cleanup();
  });

  return {
    // 状态
    pendingDocuments,
    processingDocuments,
    recentlyCompletedDocs,
    vectorizationProgress,

    // 方法
    createKnowledgeDocumentOnly,
    handleChunkingCompleted,
    handleChunkingFailed,
    initialize,
    cleanup,
  };
}
