/**
 * 知识库文档重新向量化工具
 * 用于检查和管理知识库文档的重新向量化需求
 */

import { useSqlite } from '../composeables/useSqlite';

export interface RevectorizationCheckResult {
  needsRevectorization: boolean;
  originalDocumentId?: number;
  originalUpdatedAt?: string;
  knowledgeUpdatedAt?: string;
  reason?: string;
}

/**
 * 检查单个知识库文档是否需要重新向量化
 */
export async function checkKnowledgeDocumentRevectorization(
  knowledgeDocumentId: number,
  knowledgeDocuments: Array<{ id: number; updated_at: string | number }>,
): Promise<RevectorizationCheckResult> {
  try {
    const { checkKnowledgeDocumentNeedsRevectorization } = useSqlite();
    const result = await checkKnowledgeDocumentNeedsRevectorization(
      knowledgeDocumentId,
      knowledgeDocuments,
    );

    console.log('🔍 [重新向量化检查] 知识库文档', knowledgeDocumentId, '检查结果:', result);

    return result;
  } catch (error) {
    console.error('❌ [重新向量化检查] 检查失败:', error);

    return {
      needsRevectorization: false,
      reason: `检查失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
}

/**
 * 批量检查多个知识库文档的重新向量化需求
 */
export async function batchCheckKnowledgeDocumentsRevectorization(
  knowledgeDocumentIds: number[],
  knowledgeDocuments: Array<{ id: number; updated_at: string | number }>,
): Promise<Map<number, RevectorizationCheckResult>> {
  const results = new Map<number, RevectorizationCheckResult>();

  if (knowledgeDocumentIds.length === 0) {
    console.log('🔍 [批量重新向量化检查] 没有文档需要检查');
    return results;
  }

  console.log('🔍 [批量重新向量化检查] 开始检查', knowledgeDocumentIds.length, '个知识库文档');

  // 使用 Promise.allSettled 来并行处理，提高性能
  const promises = knowledgeDocumentIds.map(async (docId) => {
    try {
      const result = await checkKnowledgeDocumentRevectorization(docId, knowledgeDocuments);
      return { docId, result };
    } catch (error) {
      console.error('❌ [批量重新向量化检查] 文档', docId, '检查失败:', error);
      return {
        docId,
        result: {
          needsRevectorization: false,
          reason: `检查失败: ${error instanceof Error ? error.message : '未知错误'}`,
        } as RevectorizationCheckResult,
      };
    }
  });

  const settledResults = await Promise.allSettled(promises);

  // 处理结果
  settledResults.forEach((settledResult) => {
    if (settledResult.status === 'fulfilled') {
      const { docId, result } = settledResult.value;
      results.set(docId, result);
    } else {
      console.error('❌ [批量重新向量化检查] Promise 失败:', settledResult.reason);
    }
  });

  const needsRevectorization = Array.from(results.values()).filter(
    (r) => r.needsRevectorization,
  ).length;
  console.log(
    '📊 [批量重新向量化检查] 完成，共',
    results.size,
    '个文档，其中',
    needsRevectorization,
    '个需要重新向量化',
  );

  return results;
}

/**
 * 为知识库文档列表添加重新向量化状态
 */
export async function enrichKnowledgeDocumentsWithRevectorizationStatus<
  T extends { id: number; updated_at: string | number },
>(
  knowledgeDocuments: T[],
): Promise<Array<T & { needsRevectorization?: boolean; revectorizationReason?: string }>> {
  if (knowledgeDocuments.length === 0) {
    return [];
  }

  const documentIds = knowledgeDocuments.map((doc) => doc.id);
  const revectorizationResults = await batchCheckKnowledgeDocumentsRevectorization(
    documentIds,
    knowledgeDocuments,
  );

  return knowledgeDocuments.map((doc) => {
    const revectorizationResult = revectorizationResults.get(doc.id);
    return {
      ...doc,
      needsRevectorization: revectorizationResult?.needsRevectorization || false,
      revectorizationReason: revectorizationResult?.reason,
    };
  });
}

/**
 * 获取需要重新向量化的知识库文档列表
 */
export function getDocumentsNeedingRevectorization<
  T extends { id: number; needsRevectorization?: boolean },
>(enrichedDocuments: T[]): T[] {
  return enrichedDocuments.filter((doc) => doc.needsRevectorization === true);
}

/**
 * 统计重新向量化状态
 */
export function getRevectorizationStats<T extends { needsRevectorization?: boolean }>(
  enrichedDocuments: T[],
): {
  total: number;
  needsRevectorization: number;
  upToDate: number;
  percentage: number;
} {
  const total = enrichedDocuments.length;
  const needsRevectorization = enrichedDocuments.filter(
    (doc) => doc.needsRevectorization === true,
  ).length;
  const upToDate = total - needsRevectorization;
  const percentage = total > 0 ? Math.round((needsRevectorization / total) * 100) : 0;

  return {
    total,
    needsRevectorization,
    upToDate,
    percentage,
  };
}
