/**
 * Azure OpenAI API 类型定义
 * 基于 Azure OpenAI Chat Completions API 文档
 * @see https://learn.microsoft.com/en-us/azure/ai-services/openai/reference
 */

import type { Tool, ToolChoice, ResponseFormat } from './openai';
import type { CategorizedModels } from './modelCategories';

export interface AzureOpenAISettings {
  baseUrl: string; // 格式: https://YOUR_RESOURCE_NAME.openai.azure.com
  apiKey: string;
  helpUrl: string;
  deploymentName: string; // Azure 部署名称
  apiVersion: string; // API 版本，如 2024-06-01 或 2025-04-01-preview
  model: string; // 模型名称（用于显示）
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  logitBias?: Record<string, number>;
  seed?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  response_format?: ResponseFormat;
  avaliableModels?: CategorizedModels;
  enabled?: boolean;
  // Azure 特有配置
  resourceName?: string; // Azure 资源名称
  authType?: 'apiKey' | 'azureAD'; // 认证方式
}

// Azure OpenAI 使用与 OpenAI 相同的消息格式和响应格式
// 因此可以重用 OpenAI 的类型定义

// 导出从 OpenAI 类型重用的类型
export type {
  Message,
  Tool,
  ToolCall,
  ToolChoice,
  ResponseFormat,
  ChatCompletionRequest as AzureChatCompletionRequest,
  ChatCompletionResponse as AzureChatCompletionResponse,
} from './openai';
