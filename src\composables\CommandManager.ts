import { ref, computed, shallowRef, type Ref, type ComputedRef } from 'vue'
import type { Editor } from '@tiptap/vue-3'
import { MemoryManager } from './MemoryManager'
import { PerformanceMonitor } from './PerformanceMonitor'

// Command types
export interface Command {
  id: string
  name: string
  description?: string
  icon?: string
  shortcut?: string
  category: CommandCategory
  execute: (params?: unknown) => Promise<void> | void
  canExecute?: () => boolean
  isActive?: () => boolean
  priority?: number
}

export enum CommandCategory {
  EDITOR = 'editor',
  FORMAT = 'format',
  INSERT = 'insert',
  DOCUMENT = 'document',
  SEARCH = 'search',
  VIEW = 'view',
  TOOLS = 'tools',
  SYSTEM = 'system'
}

export interface CommandPalette {
  isOpen: Ref<boolean>
  searchQuery: Ref<string>
  selectedIndex: Ref<number>
  filteredCommands: ComputedRef<Command[]>
  recentCommands: Ref<string[]>
}

export interface CommandHistory {
  undo: () => void
  redo: () => void
  canUndo: () => boolean
  canRedo: () => boolean
  clear: () => void
}

export class CommandManager {
  private static instance: CommandManager | null = null
  private commands = shallowRef<Map<string, Command>>(new Map())
  private commandHistory: string[] = []
  private historyLimit = 100
  private executionQueue: Promise<void> = Promise.resolve()
  private isExecuting = ref(false)
  private recentCommands = ref<string[]>([])
  private recentCommandsLimit = 10
  private memoryManager = new MemoryManager()
  private performanceMonitor = new PerformanceMonitor()
  
  // Command palette state
  private paletteOpen = ref(false)
  private paletteQuery = ref('')
  private paletteSelectedIndex = ref(0)
  
  // Editor instance reference
  private editorRef = shallowRef<Editor | null>(null)
  
  // Keyboard shortcuts map
  private shortcuts = new Map<string, string>()
  
  // Command execution callbacks
  private beforeExecuteCallbacks: Array<(command: Command) => void> = []
  private afterExecuteCallbacks: Array<(command: Command, error?: Error) => void> = []

  private constructor() {
    this.initializeDefaultCommands()
    this.setupKeyboardHandlers()
  }

  static getInstance(): CommandManager {
    if (!CommandManager.instance) {
      CommandManager.instance = new CommandManager()
    }
    return CommandManager.instance
  }

  // Register a new command
  registerCommand(command: Command): void {
    this.performanceMonitor.startTiming(`register-command-${command.id}`)
    
    const commandsMap = new Map(this.commands.value)
    commandsMap.set(command.id, command)
    this.commands.value = commandsMap
    
    // Register shortcut if provided
    if (command.shortcut) {
      this.shortcuts.set(command.shortcut, command.id)
    }
    
    this.performanceMonitor.endTiming(`register-command-${command.id}`)
    console.log(`Command registered: ${command.id}`)
  }

  // Unregister a command
  unregisterCommand(commandId: string): void {
    const commandsMap = new Map(this.commands.value)
    const command = commandsMap.get(commandId)
    
    if (command) {
      commandsMap.delete(commandId)
      this.commands.value = commandsMap
      
      // Remove shortcut
      if (command.shortcut) {
        this.shortcuts.delete(command.shortcut)
      }
      
      console.log(`Command unregistered: ${commandId}`)
    }
  }

  // Execute a command by ID
  async executeCommand(commandId: string, params?: unknown): Promise<void> {
    const command = this.commands.value.get(commandId)
    
    if (!command) {
      console.error(`Command not found: ${commandId}`)
      return
    }
    
    // Check if command can be executed
    if (command.canExecute && !command.canExecute()) {
      console.warn(`Command cannot be executed: ${commandId}`)
      return
    }
    
    // Add to execution queue to ensure serial execution
    this.executionQueue = this.executionQueue.then(async () => {
      this.isExecuting.value = true
      this.performanceMonitor.startTiming(`execute-command-${commandId}`)
      
      try {
        // Call before execute callbacks
        this.beforeExecuteCallbacks.forEach(callback => callback(command))
        
        // Execute the command
        await command.execute(params)
        
        // Update command history
        this.addToHistory(commandId)
        
        // Update recent commands
        this.updateRecentCommands(commandId)
        
        // Call after execute callbacks
        this.afterExecuteCallbacks.forEach(callback => callback(command))
        
        console.log(`Command executed: ${commandId}`)
      } catch (error) {
        console.error(`Command execution failed: ${commandId}`, error)
        
        // Call after execute callbacks with error
        this.afterExecuteCallbacks.forEach(callback => 
          callback(command, error as Error)
        )
        
        throw error
      } finally {
        this.performanceMonitor.endTiming(`execute-command-${commandId}`)
        this.isExecuting.value = false
      }
    })
    
    return this.executionQueue
  }

  // Get all commands
  getAllCommands(): Command[] {
    return Array.from(this.commands.value.values())
  }

  // Get commands by category
  getCommandsByCategory(category: CommandCategory): Command[] {
    return this.getAllCommands().filter(cmd => cmd.category === category)
  }

  // Get command by ID
  getCommand(commandId: string): Command | undefined {
    return this.commands.value.get(commandId)
  }

  // Search commands
  searchCommands(query: string): Command[] {
    const normalizedQuery = query.toLowerCase()
    return this.getAllCommands().filter(cmd => 
      cmd.name.toLowerCase().includes(normalizedQuery) ||
      cmd.description?.toLowerCase().includes(normalizedQuery) ||
      cmd.id.toLowerCase().includes(normalizedQuery)
    )
  }

  // Command palette API
  getCommandPalette(): CommandPalette {
    const filteredCommands = computed(() => {
      if (!this.paletteQuery.value) {
        // Show recent commands when no query
        const recentCmds = this.recentCommands.value
          .map(id => this.commands.value.get(id))
          .filter((cmd): cmd is Command => Boolean(cmd))
        
        const otherCmds = this.getAllCommands()
          .filter(cmd => !this.recentCommands.value.includes(cmd.id))
          .sort((a, b) => (b.priority || 0) - (a.priority || 0))
        
        return [...recentCmds, ...otherCmds]
      }
      
      return this.searchCommands(this.paletteQuery.value)
        .sort((a, b) => {
          // Prioritize exact matches
          const aExact = a.name.toLowerCase() === this.paletteQuery.value.toLowerCase()
          const bExact = b.name.toLowerCase() === this.paletteQuery.value.toLowerCase()
          if (aExact && !bExact) return -1
          if (!aExact && bExact) return 1
          
          // Then by priority
          return (b.priority || 0) - (a.priority || 0)
        })
    })
    
    return {
      isOpen: this.paletteOpen,
      searchQuery: this.paletteQuery,
      selectedIndex: this.paletteSelectedIndex,
      filteredCommands,
      recentCommands: this.recentCommands
    }
  }

  // Open command palette
  openCommandPalette(): void {
    this.paletteOpen.value = true
    this.paletteQuery.value = ''
    this.paletteSelectedIndex.value = 0
  }

  // Close command palette
  closeCommandPalette(): void {
    this.paletteOpen.value = false
    this.paletteQuery.value = ''
    this.paletteSelectedIndex.value = 0
  }

  // Set editor instance
  setEditor(editor: Editor | null): void {
    this.editorRef.value = editor
  }

  // Get editor instance
  getEditor(): Editor | null {
    return this.editorRef.value
  }

  // Command history API
  getCommandHistory(): CommandHistory {
    return {
      undo: () => this.undoCommand(),
      redo: () => this.redoCommand(),
      canUndo: () => this.canUndo(),
      canRedo: () => this.canRedo(),
      clear: () => this.clearHistory()
    }
  }

  // Register callbacks
  onBeforeExecute(callback: (command: Command) => void): () => void {
    this.beforeExecuteCallbacks.push(callback)
    return () => {
      const index = this.beforeExecuteCallbacks.indexOf(callback)
      if (index > -1) {
        this.beforeExecuteCallbacks.splice(index, 1)
      }
    }
  }

  onAfterExecute(callback: (command: Command, error?: Error) => void): () => void {
    this.afterExecuteCallbacks.push(callback)
    return () => {
      const index = this.afterExecuteCallbacks.indexOf(callback)
      if (index > -1) {
        this.afterExecuteCallbacks.splice(index, 1)
      }
    }
  }

  // Private methods
  private initializeDefaultCommands(): void {
    // Editor commands
    this.registerCommand({
      id: 'editor.undo',
      name: 'Undo',
      description: 'Undo last action',
      icon: 'undo',
      shortcut: 'Ctrl+Z',
      category: CommandCategory.EDITOR,
      execute: () => {
        this.editorRef.value?.chain().undo().run()
      },
      canExecute: () => !!this.editorRef.value?.can().undo(),
      priority: 100
    })
    
    this.registerCommand({
      id: 'editor.redo',
      name: 'Redo',
      description: 'Redo last action',
      icon: 'redo',
      shortcut: 'Ctrl+Y',
      category: CommandCategory.EDITOR,
      execute: () => {
        this.editorRef.value?.chain().redo().run()
      },
      canExecute: () => !!this.editorRef.value?.can().redo(),
      priority: 99
    })
    
    // Format commands
    this.registerCommand({
      id: 'format.bold',
      name: 'Bold',
      description: 'Toggle bold formatting',
      icon: 'format_bold',
      shortcut: 'Ctrl+B',
      category: CommandCategory.FORMAT,
      execute: () => {
        this.editorRef.value?.chain().toggleBold().run()
      },
      isActive: () => !!this.editorRef.value?.isActive('bold'),
      priority: 90
    })
    
    this.registerCommand({
      id: 'format.italic',
      name: 'Italic',
      description: 'Toggle italic formatting',
      icon: 'format_italic',
      shortcut: 'Ctrl+I',
      category: CommandCategory.FORMAT,
      execute: () => {
        this.editorRef.value?.chain().toggleItalic().run()
      },
      isActive: () => !!this.editorRef.value?.isActive('italic'),
      priority: 89
    })
    
    // System commands
    this.registerCommand({
      id: 'system.commandPalette',
      name: 'Command Palette',
      description: 'Open command palette',
      icon: 'terminal',
      shortcut: 'Ctrl+Shift+P',
      category: CommandCategory.SYSTEM,
      execute: () => this.openCommandPalette(),
      priority: 200
    })
  }

  private setupKeyboardHandlers(): void {
    if (typeof window === 'undefined') return
    
    window.addEventListener('keydown', (event) => {
      // Build shortcut string
      const parts: string[] = []
      if (event.ctrlKey || event.metaKey) parts.push('Ctrl')
      if (event.shiftKey) parts.push('Shift')
      if (event.altKey) parts.push('Alt')
      
      // Add key
      if (event.key.length === 1) {
        parts.push(event.key.toUpperCase())
      } else {
        // Handle special keys
        const keyMap: Record<string, string> = {
          'Enter': 'Enter',
          'Escape': 'Esc',
          'ArrowUp': 'Up',
          'ArrowDown': 'Down',
          'ArrowLeft': 'Left',
          'ArrowRight': 'Right',
          ' ': 'Space'
        }
        const mappedKey = keyMap[event.key]
        if (mappedKey) {
          parts.push(mappedKey)
        }
      }
      
      const shortcut = parts.join('+')
      const commandId = this.shortcuts.get(shortcut)
      
      if (commandId) {
        event.preventDefault()
        this.executeCommand(commandId).catch(console.error)
      }
    })
  }

  private addToHistory(commandId: string): void {
    this.commandHistory.push(commandId)
    
    // Limit history size
    if (this.commandHistory.length > this.historyLimit) {
      this.commandHistory.shift()
    }
  }

  private updateRecentCommands(commandId: string): void {
    const recent = [...this.recentCommands.value]
    
    // Remove if already exists
    const existingIndex = recent.indexOf(commandId)
    if (existingIndex > -1) {
      recent.splice(existingIndex, 1)
    }
    
    // Add to beginning
    recent.unshift(commandId)
    
    // Limit size
    if (recent.length > this.recentCommandsLimit) {
      recent.pop()
    }
    
    this.recentCommands.value = recent
  }

  private undoCommand(): void {
    // This is for command history, not editor undo
    console.log('Command history undo not implemented')
  }

  private redoCommand(): void {
    // This is for command history, not editor redo
    console.log('Command history redo not implemented')
  }

  private canUndo(): boolean {
    return this.commandHistory.length > 0
  }

  private canRedo(): boolean {
    return false // Not implemented
  }

  private clearHistory(): void {
    this.commandHistory = []
  }

  // Cleanup
  destroy(): void {
    this.commands.value.clear()
    this.shortcuts.clear()
    this.commandHistory = []
    this.beforeExecuteCallbacks = []
    this.afterExecuteCallbacks = []
    this.editorRef.value = null
    CommandManager.instance = null
  }
}

// Export singleton instance getter
export function useCommandManager() {
  return CommandManager.getInstance()
}