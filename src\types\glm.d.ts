import type { CategorizedModels } from './modelCategories';

export interface GlmSettings {
  /**
   * GLM API 端点
   * @default "https://open.bigmodel.cn/api/paas/v4"
   */
  baseUrl: string;

  /**
   * GLM API 密钥
   */
  apiKey: string;
  helpUrl: string;

  /**
   * 要使用的模型
   */
  model: string;

  /**
   * 生成文本的随机性
   * @default 0.7
   * @min 0
   * @max 1
   */
  temperature?: number;

  /**
   * 生成文本的最大 token 数
   * @default 1024
   * @min 1
   */
  maxTokens: number;

  /**
   * 是否使用流式输出
   * @default true
   */
  stream: boolean;

  /**
   * 累积概率采样
   * @default 0.9
   * @min 0
   * @max 1
   */
  topP?: number;

  /**
   * 可用的模型列表
   */
  avaliableModels: CategorizedModels;

  /**
   * 是否启用
   * @default false
   */
  enabled: boolean;
}

export interface GlmMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  tool_call_id?: string;
  tool_calls?: GlmToolCall[];
}

export interface GlmToolCall {
  id: string;
  type: 'function' | 'web_search' | 'retrieval';
  function?: {
    name: string;
    arguments: string;
  };
}

export interface GlmTool {
  type: 'function' | 'web_search' | 'retrieval';
  function?: {
    name: string;
    description: string;
    parameters: Record<string, unknown>;
  };
  enable?: boolean;
  search_engine?: string;
  search_intent?: string;
  count?: number;
  search_domain_filter?: string;
  search_recency_filter?: string;
  content_size?: string;
  result_sequence?: string;
  search_result?: boolean;
  require_search?: boolean;
  search_prompt?: string;
}

export interface GlmResponse {
  id: string;
  created: number;
  model: string;
  choices: GlmChoice[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  web_search?: Array<{
    icon: string;
    title: string;
    link: string;
    media: string;
    publish_date: string;
    content: string;
    refer?: string;
  }>;
}

export interface GlmChoice {
  index: number;
  finish_reason: 'stop' | 'tool_calls' | 'length' | 'sensitive' | 'network_error';
  message?: {
    role: string;
    content: string;
    tool_calls?: GlmToolCall[];
  };
  delta?: {
    role?: string;
    content?: string;
    tool_calls?: GlmToolCall[];
  };
}

export interface GlmStreamResponse {
  id: string;
  created: number;
  model?: string;
  choices: GlmChoice[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  web_search?: Array<{
    icon: string;
    title: string;
    link: string;
    media: string;
    publish_date: string;
    content: string;
  }>;
}

export interface GlmRequestParams {
  model: string;
  messages: GlmMessage[];
  request_id?: string;
  temperature?: number;
  top_p?: number;
  max_tokens?: number;
  response_format?: {
    type: 'text' | 'json_object';
  };
  stop?: string[];
  tools?: GlmTool[];
  tool_choice?: 'auto' | 'none';
  stream?: boolean;
}
