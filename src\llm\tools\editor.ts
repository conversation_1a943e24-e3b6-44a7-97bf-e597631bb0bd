import type { Editor as VueEditor } from '@tiptap/vue-3';
import { useDocStore } from 'src/stores/doc';
import { convertMarkdownToTiptap } from 'src/utils/tiptap';
import { $t } from 'src/composables/useTrans';

interface SearchMatch {
  nodePos: number;
  matchStart: number;
  matchEnd: number;
  matchText: string;
  nodeText: string;
  context: {
    before: string;
    after: string;
    full: string;
  };
  occurrence: number;
}

interface SearchResult {
  success: boolean;
  matches: SearchMatch[];
  totalMatches: number;
  message: string;
}
interface OperationResult {
  success: boolean;
  message: string;
  operation?: string;
  searchText?: string;
  newContent?: string;
  matchInfo?: {
    position: number;
    originalText: string;
    context: string;
  };
  verification?: {
    verified: boolean;
    message: string;
    details?: string;
  };
}

/**
 * 在文档中搜索文本并返回所有匹配结果
 */
function findAllMatches(editor: VueEditor, searchText: string): SearchResult {
  const results: SearchMatch[] = [];
  let occurrenceIndex = 0;

  try {
    editor.state.doc.descendants((node, pos) => {
      if (node.isText && node.textContent) {
        const nodeText = node.textContent;
        let index = 0;

        while ((index = nodeText.indexOf(searchText, index)) !== -1) {
          const contextStart = Math.max(0, index - 20);
          const contextEnd = Math.min(nodeText.length, index + searchText.length + 20);

          results.push({
            nodePos: pos,
            matchStart: pos + index, // 直接使用pos + index，不需要额外的+1
            matchEnd: pos + index + searchText.length,
            matchText: searchText,
            nodeText: nodeText,
            context: {
              before: nodeText.substring(contextStart, index),
              after: nodeText.substring(index + searchText.length, contextEnd),
              full: nodeText.substring(contextStart, contextEnd),
            },
            occurrence: occurrenceIndex++,
          });

          index += 1; // 继续搜索可能重叠的结果
        }
      }
      return true;
    });

    return {
      success: true,
      matches: results,
      totalMatches: results.length,
      message:
        results.length > 0
          ? $t('src.llm.tools.editor.findAllMatches.foundMatches', { count: results.length })
          : $t('src.llm.tools.editor.findAllMatches.noMatches', { searchText }),
    };
  } catch (error) {
    return {
      success: false,
      matches: [],
      totalMatches: 0,
      message: $t('src.llm.tools.editor.findAllMatches.error', { error: error.message }),
    };
  }
}

/**
 * 获取当前活动的编辑器实例
 */
function getActiveEditor(): VueEditor | null {
  const docStore = useDocStore();
  const activeWindow = docStore.getActiveWindow();

  if (!activeWindow || !activeWindow.activeDocumentId) {
    return null;
  }

  const instanceKey = docStore.generateEditorInstanceKey(
    activeWindow.id,
    activeWindow.activeDocumentId,
  );

  return docStore.getEditorInstanceByKey(instanceKey) || null;
}

/**
 * 根据文档ID获取编辑器实例
 * 如果同一文档在多个窗口中打开，返回第一个找到的实例
 */
function getEditorByDocumentId(docId: number): VueEditor | null {
  const docStore = useDocStore();
  const window = docStore.getWindowContainingDocument(docId);

  if (!window) {
    return null;
  }

  const instanceKey = docStore.generateEditorInstanceKey(window.id, docId);
  return docStore.getEditorInstanceByKey(instanceKey) || null;
}

/**
 * 获取编辑器实例（优先使用指定文档ID，否则使用活动编辑器）
 */
function getEditor(documentId?: number): VueEditor | null {
  if (documentId) {
    return getEditorByDocumentId(documentId);
  }
  return getActiveEditor();
}

/**
 * 验证操作结果
 */
function verifyOperation(
  editor: VueEditor,
  operationType: 'replace' | 'insert' | 'delete',
  params: {
    originalText?: string;
    newText?: string;
    position: number;
  },
): { verified: boolean; message: string; details?: string } {
  try {
    const currentContent = editor.getText();

    switch (operationType) {
      case 'replace': {
        const { originalText, newText, position } = params;
        if (!originalText || !newText) {
          return {
            verified: false,
            message: $t('src.llm.tools.editor.verifyOperation.missingParams'),
          };
        }

        // 检查原文本是否还存在于指定位置
        const textAtPosition = currentContent.substring(position, position + originalText.length);
        const hasOriginalText = textAtPosition === originalText;

        // 检查新文本是否存在于文档中
        const hasNewText = currentContent.includes(newText);

        if (hasOriginalText && hasNewText) {
          return {
            verified: true,
            message: $t('src.llm.tools.editor.verifyOperation.replaceAddedTrackChange'),
            details: $t('src.llm.tools.editor.verifyOperation.replaceAddedTrackChangeDetails', {
              originalText,
              newText,
            }),
          };
        } else if (hasNewText && !hasOriginalText) {
          return {
            verified: true,
            message: $t('src.llm.tools.editor.verifyOperation.replaceCompleted'),
            details: $t('src.llm.tools.editor.verifyOperation.replaceCompletedDetails', {
              originalText,
              newText,
            }),
          };
        } else {
          return {
            verified: false,
            message: $t('src.llm.tools.editor.verifyOperation.replaceFailed'),
            details: $t('src.llm.tools.editor.verifyOperation.replaceFailedDetails', {
              newText,
            }),
          };
        }
      }

      case 'insert': {
        const { newText } = params;
        if (!newText) {
          return {
            verified: false,
            message: $t('src.llm.tools.editor.verifyOperation.missingInsertText'),
          };
        }

        const hasNewText = currentContent.includes(newText);
        if (hasNewText) {
          return {
            verified: true,
            message: $t('src.llm.tools.editor.verifyOperation.insertCompleted'),
            details: $t('src.llm.tools.editor.verifyOperation.insertCompletedDetails', {
              newText,
            }),
          };
        } else {
          return {
            verified: false,
            message: $t('src.llm.tools.editor.verifyOperation.insertFailed'),
            details: $t('src.llm.tools.editor.verifyOperation.insertFailedDetails', {
              newText,
            }),
          };
        }
      }

      case 'delete': {
        const { originalText } = params;
        if (!originalText) {
          return {
            verified: false,
            message: $t('src.llm.tools.editor.verifyOperation.missingDeleteText'),
          };
        }

        const hasOriginalText = currentContent.includes(originalText);
        if (hasOriginalText) {
          return {
            verified: true,
            message: $t('src.llm.tools.editor.verifyOperation.deleteAddedTrackChange'),
            details: $t('src.llm.tools.editor.verifyOperation.deleteAddedTrackChangeDetails', {
              originalText,
            }),
          };
        } else {
          return {
            verified: true,
            message: $t('src.llm.tools.editor.verifyOperation.deleteCompleted'),
            details: $t('src.llm.tools.editor.verifyOperation.deleteCompletedDetails', {
              originalText,
            }),
          };
        }
      }

      default:
        return {
          verified: false,
          message: $t('src.llm.tools.editor.verifyOperation.unknownOperation'),
        };
    }
  } catch (error) {
    return {
      verified: false,
      message: $t('src.llm.tools.editor.verifyOperation.error'),
      details: error.message,
    };
  }
}

/**
 * 搜索并替换文本
 */
export function searchAndReplace(params: {
  searchText: string;
  replaceText: string;
  occurrence?: number;
  caseSensitive?: boolean;
  documentId?: number;
}): OperationResult {
  const { searchText, replaceText, occurrence = 0, documentId } = params;

  const editor = getEditor(documentId);
  if (!editor) {
    return {
      success: false,
      message: documentId
        ? $t('src.llm.tools.editor.searchAndReplace.noEditorInstance', { documentId })
        : $t('src.llm.tools.editor.searchAndReplace.noActiveEditor'),
    };
  }

  try {
    const searchResult = findAllMatches(editor, searchText);

    if (!searchResult.success) {
      return {
        success: false,
        message: searchResult.message,
      };
    }

    if (searchResult.totalMatches === 0) {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndReplace.noMatches', { searchText }),
        operation: 'replace',
        searchText: searchText,
      };
    }

    if (occurrence >= searchResult.totalMatches) {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndReplace.occurrenceOutOfRange', {
          occurrence,
          totalMatches: searchResult.totalMatches,
        }),
        operation: 'replace',
        searchText: searchText,
      };
    }

    if (searchResult.totalMatches > 1 && occurrence === undefined) {
      const matchList = searchResult.matches
        .map(
          (match, index) =>
            `[${index}] "${match.context.before}【${match.matchText}】${match.context.after}"`,
        )
        .join('\n');

      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndReplace.multipleMatches', {
          totalMatches: searchResult.totalMatches,
          matchList,
        }),
        operation: 'replace',
        searchText: searchText,
      };
    }

    const targetMatch = searchResult.matches[occurrence];

    // 执行替换 - 使用TrackChange标记
    const success = editor
      .chain()
      .focus()
      .command(({ tr }) => {
        // 1. 先对原文本做删除标记
        tr.addMark(
          targetMatch.matchStart,
          targetMatch.matchEnd,
          editor.schema.marks.trackChange.create({
            type: 'deletion',
            author: 'AI Assistant',
            timestamp: Date.now(),
            status: 'pending',
            comment: $t('src.llm.tools.editor.searchAndReplace.deleteComment', {
              targetMatchText: targetMatch.matchText,
            }),
          }),
        );

        // 2. 在原文本后插入新内容（带插入标记）
        const insertAt = targetMatch.matchEnd;
        const newContent = editor.schema.text(replaceText);
        tr.insert(insertAt, newContent);

        // 3. 为新插入的内容添加插入标记
        tr.addMark(
          insertAt,
          insertAt + replaceText.length,
          editor.schema.marks.trackChange.create({
            type: 'insertion',
            author: 'AI Assistant',
            timestamp: Date.now(),
            status: 'pending',
            comment: $t('src.llm.tools.editor.searchAndReplace.insertComment', {
              replaceText,
            }),
          }),
        );

        return true;
      })
      .run();

    if (success) {
      // 清除文本选择
      setTimeout(() => {
        if (editor.view && editor.view.hasFocus()) {
          editor.commands.setTextSelection(
            targetMatch.matchEnd + replaceText.length - searchText.length,
          );
        }
      }, 50);

      // 验证操作结果
      const verificationResult = verifyOperation(editor, 'replace', {
        originalText: searchText,
        newText: replaceText,
        position: targetMatch.matchStart,
      });

      return {
        success: true,
        message: $t('src.llm.tools.editor.searchAndReplace.replaceCompleted', {
          searchText,
          replaceText,
        }),
        operation: 'replace',
        searchText: searchText,
        newContent: replaceText,
        matchInfo: {
          position: targetMatch.matchStart,
          originalText: targetMatch.matchText,
          context: targetMatch.context.full,
        },
        verification: verificationResult,
      };
    } else {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndReplace.replaceFailed'),
      };
    }
  } catch (error) {
    return {
      success: false,
      message: $t('src.llm.tools.editor.searchAndReplace.error', { error: error.message }),
    };
  }
}

/**
 * 搜索并在指定位置插入文本
 */
export function searchAndInsert(params: {
  searchText: string;
  insertText: string;
  position: 'before' | 'after' | number;
  occurrence?: number;
  caseSensitive?: boolean;
  documentId?: number;
}): OperationResult {
  const { searchText, insertText, position, occurrence = 0, documentId } = params;

  const editor = getEditor(documentId);
  if (!editor) {
    return {
      success: false,
      message: documentId
        ? $t('src.llm.tools.editor.searchAndInsert.noEditorInstance', { documentId })
        : $t('src.llm.tools.editor.searchAndInsert.noActiveEditor'),
    };
  }

  try {
    const searchResult = findAllMatches(editor, searchText);

    if (!searchResult.success) {
      return {
        success: false,
        message: searchResult.message,
      };
    }

    if (searchResult.totalMatches === 0) {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndInsert.noMatches', { searchText }),
        operation: 'insert',
        searchText: searchText,
      };
    }

    if (occurrence >= searchResult.totalMatches) {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndInsert.occurrenceOutOfRange', {
          occurrence,
          totalMatches: searchResult.totalMatches,
        }),
        operation: 'insert',
        searchText: searchText,
      };
    }

    if (searchResult.totalMatches > 1 && occurrence === undefined) {
      const matchList = searchResult.matches
        .map(
          (match, index) =>
            `[${index}] "${match.context.before}【${match.matchText}】${match.context.after}"`,
        )
        .join('\n');

      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndInsert.multipleMatches', {
          totalMatches: searchResult.totalMatches,
          matchList,
        }),
        operation: 'insert',
        searchText: searchText,
      };
    }

    const targetMatch = searchResult.matches[occurrence];
    let insertPos: number;

    if (position === 'before') {
      insertPos = targetMatch.matchStart;
    } else if (position === 'after') {
      insertPos = targetMatch.matchEnd;
    } else if (typeof position === 'number') {
      // 字符索引位置
      if (position < 0 || position > searchText.length) {
        return {
          success: false,
          message: $t('src.llm.tools.editor.searchAndInsert.positionOutOfRange', {
            position,
            searchTextLength: searchText.length,
          }),
        };
      }
      insertPos = targetMatch.matchStart + position;
    } else {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndInsert.invalidPosition'),
      };
    }

    // 执行插入 - 使用TrackChange标记
    const success = editor
      .chain()
      .focus()
      .command(({ tr }) => {
        // 插入新内容
        const newContent = editor.schema.text(insertText);
        tr.insert(insertPos, newContent);

        // 为新插入的内容添加插入标记
        tr.addMark(
          insertPos,
          insertPos + insertText.length,
          editor.schema.marks.trackChange.create({
            type: 'insertion',
            author: 'AI Assistant',
            timestamp: Date.now(),
            status: 'pending',
            comment: $t('src.llm.tools.editor.searchAndInsert.insertComment', {
              insertText,
            }),
          }),
        );

        return true;
      })
      .run();

    if (success) {
      // 清除文本选择，光标定位到插入内容之后
      setTimeout(() => {
        if (editor.view && editor.view.hasFocus()) {
          editor.commands.setTextSelection(insertPos + insertText.length);
        }
      }, 50);

      const positionDesc =
        position === 'before'
          ? $t('src.llm.tools.editor.searchAndInsert.before')
          : position === 'after'
            ? $t('src.llm.tools.editor.searchAndInsert.after')
            : $t('src.llm.tools.editor.searchAndInsert.positionDesc', { position });

      // 验证操作结果
      const verificationResult = verifyOperation(editor, 'insert', {
        newText: insertText,
        position: insertPos,
      });

      return {
        success: true,
        message: $t('src.llm.tools.editor.searchAndInsert.insertCompleted', {
          searchText,
          positionDesc,
          insertText,
        }),
        operation: 'insert',
        searchText: searchText,
        newContent: insertText,
        matchInfo: {
          position: insertPos,
          originalText: targetMatch.matchText,
          context: targetMatch.context.full,
        },
        verification: verificationResult,
      };
    } else {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndInsert.insertFailed'),
      };
    }
  } catch (error) {
    return {
      success: false,
      message: $t('src.llm.tools.editor.searchAndInsert.error', { error: error.message }),
    };
  }
}

/**
 * 搜索并删除文本
 */
export function searchAndDelete(params: {
  searchText: string;
  occurrence?: number;
  caseSensitive?: boolean;
  documentId?: number;
}): OperationResult {
  const { searchText, occurrence = 0, documentId } = params;

  const editor = getEditor(documentId);
  if (!editor) {
    return {
      success: false,
      message: documentId
        ? $t('src.llm.tools.editor.searchAndDelete.noEditorInstance', { documentId })
        : $t('src.llm.tools.editor.searchAndDelete.noActiveEditor'),
    };
  }

  try {
    const searchResult = findAllMatches(editor, searchText);

    if (!searchResult.success) {
      return {
        success: false,
        message: searchResult.message,
      };
    }

    if (searchResult.totalMatches === 0) {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndDelete.noMatches', { searchText }),
        operation: 'delete',
        searchText: searchText,
      };
    }

    if (occurrence >= searchResult.totalMatches) {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndDelete.occurrenceOutOfRange', {
          occurrence,
          totalMatches: searchResult.totalMatches,
        }),
        operation: 'delete',
        searchText: searchText,
      };
    }

    if (searchResult.totalMatches > 1 && occurrence === undefined) {
      const matchList = searchResult.matches
        .map(
          (match, index) =>
            `[${index}] "${match.context.before}【${match.matchText}】${match.context.after}"`,
        )
        .join('\n');

      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndDelete.multipleMatches', {
          totalMatches: searchResult.totalMatches,
          matchList,
        }),
        operation: 'delete',
        searchText: searchText,
      };
    }

    const targetMatch = searchResult.matches[occurrence];

    // 执行删除 - 使用TrackChange标记
    const success = editor
      .chain()
      .focus()
      .command(({ tr }) => {
        // 对要删除的文本添加删除标记
        tr.addMark(
          targetMatch.matchStart,
          targetMatch.matchEnd,
          editor.schema.marks.trackChange.create({
            type: 'deletion',
            author: 'AI Assistant',
            timestamp: Date.now(),
            status: 'pending',
            comment: $t('src.llm.tools.editor.searchAndDelete.deleteComment', {
              targetMatchText: targetMatch.matchText,
            }),
          }),
        );

        return true;
      })
      .run();

    if (success) {
      // 清除文本选择，光标定位到删除位置
      setTimeout(() => {
        if (editor.view && editor.view.hasFocus()) {
          editor.commands.setTextSelection(targetMatch.matchStart);
        }
      }, 50);

      // 验证操作结果
      const verificationResult = verifyOperation(editor, 'delete', {
        originalText: targetMatch.matchText,
        position: targetMatch.matchStart,
      });

      return {
        success: true,
        message: $t('src.llm.tools.editor.searchAndDelete.deleteCompleted', { searchText }),
        operation: 'delete',
        searchText: searchText,
        matchInfo: {
          position: targetMatch.matchStart,
          originalText: targetMatch.matchText,
          context: targetMatch.context.full,
        },
        verification: verificationResult,
      };
    } else {
      return {
        success: false,
        message: $t('src.llm.tools.editor.searchAndDelete.deleteFailed'),
      };
    }
  } catch (error) {
    return {
      success: false,
      message: $t('src.llm.tools.editor.searchAndDelete.error', { error: error.message }),
    };
  }
}

/**
 * 搜索文本（仅查找，不执行操作）
 */
export function searchText(params: {
  searchText: string;
  caseSensitive?: boolean;
  documentId?: number;
}): SearchResult & { matches: Array<SearchMatch & { preview: string }> } {
  const { searchText, documentId } = params;

  const editor = getEditor(documentId);
  if (!editor) {
    return {
      success: false,
      matches: [],
      totalMatches: 0,
      message: documentId
        ? $t('src.llm.tools.editor.common.noEditorInstance', { documentId })
        : $t('src.llm.tools.editor.common.noActiveEditor'),
    };
  }

  try {
    const searchResult = findAllMatches(editor, searchText);

    // 为每个匹配添加预览信息
    const enhancedMatches = searchResult.matches.map((match, index) => ({
      ...match,
      preview: `[${index}] "${match.context.before}【${match.matchText}】${match.context.after}"`,
    }));

    return {
      ...searchResult,
      matches: enhancedMatches,
    };
  } catch (error) {
    return {
      success: false,
      matches: [],
      totalMatches: 0,
      message: $t('src.llm.tools.editor.searchText.error', { error: error.message }),
    };
  }
}

/**
 * 获取当前文档信息
 */
export function getDocumentInfo(
  params: {
    documentId?: number;
  } = {},
): {
  success: boolean;
  message: string;
  documentInfo?: {
    hasActiveDocument: boolean;
    documentId?: number;
    windowId?: number;
    contentLength: number;
    content: string;
    effectiveContent: string;
    hasPendingChanges: boolean;
    pendingChangesCount: number;
  };
} {
  const { documentId } = params;
  const docStore = useDocStore();
  const editor = getEditor(documentId);

  if (!editor) {
    return {
      success: false,
      message: documentId
        ? $t('src.llm.tools.editor.common.noEditorInstance', { documentId })
        : $t('src.llm.tools.editor.common.noActiveEditor'),
    };
  }

  // 获取文档所在的窗口信息
  let windowId: number | undefined;
  let targetDocumentId: number | undefined;

  if (documentId) {
    const window = docStore.getWindowContainingDocument(documentId);
    windowId = window?.id;
    targetDocumentId = documentId;
  } else {
    const activeWindow = docStore.getActiveWindow();
    windowId = activeWindow?.id;
    targetDocumentId = activeWindow?.activeDocumentId;
  }

  if (!windowId || !targetDocumentId) {
    return {
      success: false,
      message: $t('src.llm.tools.editor.getDocumentInfo.cannotDetermineWindow'),
    };
  }

  try {
    const rawContent = editor.getText();

    // 获取待处理的修改信息
    const pendingResult = hasPendingChanges();

    // 构建有效内容（排除待删除的内容）
    let effectiveContent = '';
    const pendingDeletions = new Set<number>();

    if (pendingResult.success && pendingResult.changes.length > 0) {
      // 收集所有待删除的位置范围
      pendingResult.changes.forEach((change) => {
        if (change.type === 'deletion') {
          for (let i = change.from; i < change.to; i++) {
            pendingDeletions.add(i);
          }
        }
      });
    }

    // 构建排除待删除内容的有效文本
    if (pendingDeletions.size > 0) {
      editor.state.doc.descendants((node, nodePos) => {
        if (node.isText) {
          const nodeText = node.textContent || '';
          let nodeEffectiveText = '';

          for (let i = 0; i < nodeText.length; i++) {
            const globalPos = nodePos + i;
            if (!pendingDeletions.has(globalPos)) {
              nodeEffectiveText += nodeText[i];
            }
          }
          effectiveContent += nodeEffectiveText;
        }
        return true;
      });
    } else {
      effectiveContent = rawContent;
    }

    return {
      success: true,
      message: $t('src.llm.tools.editor.getDocumentInfo.success'),
      documentInfo: {
        hasActiveDocument: true,
        documentId: targetDocumentId,
        windowId: windowId,
        contentLength: rawContent.length,
        content: rawContent,
        effectiveContent: effectiveContent,
        hasPendingChanges: pendingResult.success && pendingResult.hasPending,
        pendingChangesCount: pendingResult.success ? pendingResult.changes.length : 0,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: $t('src.llm.tools.editor.getDocumentInfo.error', { error: error.message }),
    };
  }
}

/**
 * 检测文档中是否有待处理的TrackChange标记
 */
export function hasPendingChanges(
  params: {
    documentId?: number;
  } = {},
): {
  success: boolean;
  hasPending: boolean;
  changes: Array<{
    type: 'insertion' | 'deletion';
    from: number;
    to: number;
    text: string;
    comment: string;
  }>;
  message: string;
} {
  const { documentId } = params;
  const editor = getEditor(documentId);
  if (!editor) {
    return {
      success: false,
      hasPending: false,
      changes: [],
      message: documentId
        ? $t('src.llm.tools.editor.common.noEditorInstance', { documentId })
        : $t('src.llm.tools.editor.common.noActiveEditor'),
    };
  }

  const changes: Array<{
    type: 'insertion' | 'deletion';
    from: number;
    to: number;
    text: string;
    comment: string;
  }> = [];

  try {
    editor.state.doc.descendants((node, pos) => {
      if (node.isText && node.marks) {
        node.marks.forEach((mark) => {
          if (
            mark.type.name === 'trackChange' &&
            mark.attrs.status === 'pending' &&
            mark.attrs.author === 'AI Assistant'
          ) {
            changes.push({
              type: mark.attrs.type,
              from: pos,
              to: pos + node.nodeSize,
              text: node.textContent || '',
              comment: mark.attrs.comment || '',
            });
          }
        });
      }
      return true;
    });

    return {
      success: true,
      hasPending: changes.length > 0,
      changes,
      message:
        changes.length > 0
          ? $t('src.llm.tools.editor.hasPendingChanges.found', { count: changes.length })
          : $t('src.llm.tools.editor.hasPendingChanges.none'),
    };
  } catch (error) {
    return {
      success: false,
      hasPending: false,
      changes: [],
      message: $t('src.llm.tools.editor.hasPendingChanges.error', { error: error.message }),
    };
  }
}

/**
 * 接受所有AI修改
 */
export function acceptAllChanges(
  params: {
    documentId?: number;
  } = {},
): OperationResult {
  const { documentId } = params;
  const editor = getEditor(documentId);
  if (!editor) {
    return {
      success: false,
      message: documentId
        ? $t('src.llm.tools.editor.acceptAllChanges.noEditorInstance', { documentId })
        : $t('src.llm.tools.editor.acceptAllChanges.noActiveEditor'),
    };
  }

  try {
    let changeCount = 0;

    const success = editor
      .chain()
      .focus()
      .command(({ tr }) => {
        const toDelete: { from: number; to: number }[] = [];

        // 1. 处理所有待删除的内容
        tr.doc.descendants((node, nodePos) => {
          node.marks.forEach((mark) => {
            if (
              mark.type.name === 'trackChange' &&
              mark.attrs.status === 'pending' &&
              mark.attrs.author === 'AI Assistant' &&
              mark.attrs.type === 'deletion'
            ) {
              toDelete.push({ from: nodePos, to: nodePos + node.nodeSize });
              changeCount++;
            }
          });
        });

        // 从后向前删除，避免位置偏移
        toDelete.reverse().forEach(({ from, to }) => {
          tr.delete(from, to);
        });

        // 2. 移除所有AI的插入标记（保留内容）
        tr.doc.descendants((node, nodePos) => {
          node.marks.forEach((mark) => {
            if (
              mark.type.name === 'trackChange' &&
              mark.attrs.status === 'pending' &&
              mark.attrs.author === 'AI Assistant' &&
              mark.attrs.type === 'insertion'
            ) {
              tr.removeMark(nodePos, nodePos + node.nodeSize, mark.type);
              changeCount++;
            }
          });
        });

        return true;
      })
      .run();

    if (success) {
      // 清除文本选择
      setTimeout(() => {
        if (editor.view && editor.view.hasFocus()) {
          editor.commands.blur();
          setTimeout(() => editor.commands.focus(), 10);
        }
      }, 50);

      return {
        success: true,
        message: $t('src.llm.tools.editor.acceptAllChanges.success', { count: changeCount }),
      };
    } else {
      return {
        success: false,
        message: $t('src.llm.tools.editor.acceptAllChanges.failed'),
      };
    }
  } catch (error) {
    return {
      success: false,
      message: $t('src.llm.tools.editor.acceptAllChanges.error', { error: error.message }),
    };
  }
}

/**
 * 拒绝所有AI修改
 */
export function rejectAllChanges(
  params: {
    documentId?: number;
  } = {},
): OperationResult {
  const { documentId } = params;
  const editor = getEditor(documentId);
  if (!editor) {
    return {
      success: false,
      message: documentId
        ? $t('src.llm.tools.editor.rejectAllChanges.noEditorInstance', { documentId })
        : $t('src.llm.tools.editor.rejectAllChanges.noActiveEditor'),
    };
  }

  try {
    let changeCount = 0;

    const success = editor
      .chain()
      .focus()
      .command(({ tr }) => {
        const toDelete: { from: number; to: number }[] = [];

        // 1. 删除所有AI插入的内容
        tr.doc.descendants((node, nodePos) => {
          node.marks.forEach((mark) => {
            if (
              mark.type.name === 'trackChange' &&
              mark.attrs.status === 'pending' &&
              mark.attrs.author === 'AI Assistant' &&
              mark.attrs.type === 'insertion'
            ) {
              toDelete.push({ from: nodePos, to: nodePos + node.nodeSize });
              changeCount++;
            }
          });
        });

        // 从后向前删除，避免位置偏移
        toDelete.reverse().forEach(({ from, to }) => {
          tr.delete(from, to);
        });

        // 2. 移除所有AI的删除标记（保留原内容）
        tr.doc.descendants((node, nodePos) => {
          node.marks.forEach((mark) => {
            if (
              mark.type.name === 'trackChange' &&
              mark.attrs.status === 'pending' &&
              mark.attrs.author === 'AI Assistant' &&
              mark.attrs.type === 'deletion'
            ) {
              tr.removeMark(nodePos, nodePos + node.nodeSize, mark.type);
              changeCount++;
            }
          });
        });

        return true;
      })
      .run();

    if (success) {
      // 清除文本选择
      setTimeout(() => {
        if (editor.view && editor.view.hasFocus()) {
          editor.commands.blur();
          setTimeout(() => editor.commands.focus(), 10);
        }
      }, 50);

      return {
        success: true,
        message: $t('src.llm.tools.editor.rejectAllChanges.success', { count: changeCount }),
      };
    } else {
      return {
        success: false,
        message: $t('src.llm.tools.editor.rejectAllChanges.failed'),
      };
    }
  } catch (error) {
    return {
      success: false,
      message: $t('src.llm.tools.editor.rejectAllChanges.error', { error: error.message }),
    };
  }
}
/**
 * 格式化并重设文档内容
 * 读取当前文档内容，让AI修正格式、补充标题等，然后重新设置文档内容
 */
export function formatAndResetDocument(params: {
  formattedContent: string;
  documentId?: number;
}): OperationResult {
  const { formattedContent, documentId } = params;

  const editor = getEditor(documentId);
  if (!editor) {
    return {
      success: false,
      message: documentId
        ? $t('src.llm.tools.editor.formatAndResetDocument.noEditorInstance', { documentId })
        : $t('src.llm.tools.editor.formatAndResetDocument.noActiveEditor'),
    };
  }

  if (!formattedContent || formattedContent.trim().length === 0) {
    return {
      success: false,
      message: $t('src.llm.tools.editor.formatAndResetDocument.emptyContent'),
    };
  }

  try {
    // 将Markdown格式的内容转换为TipTap JSON格式
    const tiptapContent = convertMarkdownToTiptap(formattedContent);

    // 获取当前内容长度，用于验证
    const originalContent = editor.getText();
    const originalLength = originalContent.length;

    // 使用setContent方法重设整个文档内容
    const success = editor
      .chain()
      .focus()
      .command(({ tr }) => {
        // 清空当前文档内容
        tr.delete(0, tr.doc.content.size);
        // 插入新的格式化内容
        tr.insert(0, editor.schema.nodeFromJSON(tiptapContent));
        return true;
      })
      .run();

    if (success) {
      // 获取新内容进行验证
      const newContent = editor.getText();
      const newLength = newContent.length;

      // 清除文本选择，光标定位到文档开头
      setTimeout(() => {
        if (editor.view && editor.view.hasFocus()) {
          editor.commands.setTextSelection(0);
        }
      }, 50);

      return {
        success: true,
        message: $t('src.llm.tools.editor.formatAndResetDocument.success', {
          originalLength,
          newLength,
        }),
        operation: 'format_document',
        newContent: formattedContent,
        verification: {
          verified: true,
          message: $t('src.llm.tools.editor.formatAndResetDocument.verified'),
          details: $t('src.llm.tools.editor.formatAndResetDocument.details', {
            originalLength,
            newLength,
          }),
        },
      };
    } else {
      return {
        success: false,
        message: $t('src.llm.tools.editor.formatAndResetDocument.failed'),
      };
    }
  } catch (error) {
    return {
      success: false,
      message: $t('src.llm.tools.editor.formatAndResetDocument.error', { error: error.message }),
    };
  }
}
// 导出所有工具函数（保持向后兼容）
export const editorTools = {
  searchAndReplace,
  searchAndInsert,
  searchAndDelete,
  searchText,
  getDocumentInfo,
  hasPendingChanges,
  acceptAllChanges,
  rejectAllChanges,
  formatAndResetDocument,
};

// 编辑器工具映射表
export const editorToolMappings = {
  search_text: searchText,
  search_and_replace: searchAndReplace,
  search_and_insert: searchAndInsert,
  search_and_delete: searchAndDelete,
  get_document_info: getDocumentInfo,
  has_pending_changes: hasPendingChanges,
  accept_all_changes: acceptAllChanges,
  reject_all_changes: rejectAllChanges,
  format_and_reset_document: formatAndResetDocument,
} as const;

// 导出给大模型使用的工具数组
export const tools = [
  {
    type: 'function' as const,
    function: {
      name: 'search_text',
      description:
        '在指定文档中搜索文本，查看所有匹配结果。用于在执行修改前确认目标文本的位置和数量。',
      parameters: {
        type: 'object',
        properties: {
          searchText: {
            type: 'string',
            description: '要搜索的文本内容',
          },
          caseSensitive: {
            type: 'boolean',
            description: '是否区分大小写，默认为true',
            default: true,
          },
          documentId: {
            type: 'integer',
            description: '目标文档ID，如果不指定则使用当前活动文档',
          },
        },
        required: ['searchText'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'search_and_replace',
      description:
        '搜索并替换指定文档中的文本。如果找到多个匹配，会显示所有匹配供选择。执行后会在编辑器中显示修改预览，用户可以选择接受或拒绝。',
      parameters: {
        type: 'object',
        properties: {
          searchText: {
            type: 'string',
            description: '要被替换的文本',
          },
          replaceText: {
            type: 'string',
            description: '替换后的新文本',
          },
          occurrence: {
            type: 'integer',
            description:
              '指定要替换第几个匹配结果（从0开始），如果有多个匹配且未指定，会返回所有匹配供选择',
            default: 0,
          },
          caseSensitive: {
            type: 'boolean',
            description: '是否区分大小写，默认为true',
            default: true,
          },
          documentId: {
            type: 'integer',
            description: '目标文档ID，如果不指定则使用当前活动文档',
          },
        },
        required: ['searchText', 'replaceText'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'search_and_insert',
      description:
        '在指定文档的指定文本前面或后面插入新内容。如果找到多个匹配，会显示所有匹配供选择。执行后会在编辑器中显示修改预览。',
      parameters: {
        type: 'object',
        properties: {
          searchText: {
            type: 'string',
            description: '用于定位的目标文本',
          },
          insertText: {
            type: 'string',
            description: '要插入的新文本内容',
          },
          position: {
            type: 'string',
            enum: ['before', 'after'],
            description: "插入位置：'before'表示在目标文本前插入，'after'表示在目标文本后插入",
            default: 'after',
          },
          occurrence: {
            type: 'integer',
            description:
              '指定要在第几个匹配结果处插入（从0开始），如果有多个匹配且未指定，会返回所有匹配供选择',
            default: 0,
          },
          caseSensitive: {
            type: 'boolean',
            description: '是否区分大小写，默认为true',
            default: true,
          },
          documentId: {
            type: 'integer',
            description: '目标文档ID，如果不指定则使用当前活动文档',
          },
        },
        required: ['searchText', 'insertText', 'position'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'search_and_delete',
      description:
        '搜索并删除指定文档中的指定文本。如果找到多个匹配，会显示所有匹配供选择。执行后会在编辑器中显示修改预览。',
      parameters: {
        type: 'object',
        properties: {
          searchText: {
            type: 'string',
            description: '要删除的文本内容',
          },
          occurrence: {
            type: 'integer',
            description:
              '指定要删除第几个匹配结果（从0开始），如果有多个匹配且未指定，会返回所有匹配供选择',
            default: 0,
          },
          caseSensitive: {
            type: 'boolean',
            description: '是否区分大小写，默认为true',
            default: true,
          },
          documentId: {
            type: 'integer',
            description: '目标文档ID，如果不指定则使用当前活动文档',
          },
        },
        required: ['searchText'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'get_document_info',
      description:
        '获取指定文档的基本信息，包括文档ID、内容长度、内容预览等。用于了解文档编辑环境。',
      parameters: {
        type: 'object',
        properties: {
          documentId: {
            type: 'integer',
            description: '目标文档ID，如果不指定则使用当前活动文档',
          },
        },
        required: [],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'has_pending_changes',
      description: '检查指定文档是否有待处理的AI修改。返回所有待处理修改的详细信息。',
      parameters: {
        type: 'object',
        properties: {
          documentId: {
            type: 'integer',
            description: '目标文档ID，如果不指定则使用当前活动文档',
          },
        },
        required: [],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'accept_all_changes',
      description:
        '接受指定文档中所有待处理的AI修改。这将删除所有标记为删除的内容，保留所有新增内容，并移除所有修改标记。',
      parameters: {
        type: 'object',
        properties: {
          documentId: {
            type: 'integer',
            description: '目标文档ID，如果不指定则使用当前活动文档',
          },
        },
        required: [],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'reject_all_changes',
      description:
        '拒绝指定文档中所有待处理的AI修改。这将删除所有新增内容，保留所有原始内容，并移除所有修改标记。',
      parameters: {
        type: 'object',
        properties: {
          documentId: {
            type: 'integer',
            description: '目标文档ID，如果不指定则使用当前活动文档',
          },
        },
        required: [],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'format_and_reset_document',
      description:
        '格式化并重设文档内容。AI读取原始内容后，对格式进行修正，补充正确的标题及其级别，完善文档样式等Markdown格式，然后使用convertMarkdownToTiptap工具将Markdown内容转为Tiptap JSON格式并重新设置文档内容。这个工具会完全替换文档内容，所以请确保格式化的内容包含了原文档的所有重要信息。',
      parameters: {
        type: 'object',
        properties: {
          formattedContent: {
            type: 'string',
            description:
              '格式化后的Markdown内容，包含正确的标题层级、列表格式、代码块等。应该包含原文档的所有内容，只是格式得到了优化。',
          },
          documentId: {
            type: 'integer',
            description: '目标文档ID，如果不指定则使用当前活动文档',
          },
        },
        required: ['formattedContent'],
      },
    },
  },
];
