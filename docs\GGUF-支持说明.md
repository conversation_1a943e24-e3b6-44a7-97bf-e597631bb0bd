# InkCop 本地GGUF模型支持

## 概述

InkCop支持本地GGUF格式的嵌入式模型，通过集成llama.cpp库实现。这允许您在本地运行嵌入式模型，无需依赖云端API，并支持NVIDIA GPU加速。

## 系统要求

### 基本要求
- Windows 10/11 (64位)
- 至少8GB RAM
- 2GB可用磁盘空间

### GPU加速要求（可选）
- NVIDIA GPU (GTX 1060或更高)
- CUDA 11.0或更高版本
- 最新的NVIDIA驱动程序

## 构建步骤

### 方法1：自动构建脚本（推荐）

1. 确保已安装以下工具：
   - Visual Studio 2022 Build Tools 或 Visual Studio 2022
   - CMake 3.16或更高版本
   - Git

2. 运行构建脚本：
   ```cmd
   build-with-gguf.bat
   ```

3. 脚本将自动：
   - 检查系统环境
   - 克隆并构建llama.cpp
   - 配置并构建InkCop

### 方法2：手动构建

1. 克隆llama.cpp：
   ```cmd
   mkdir third-party
   cd third-party
   git clone https://github.com/ggerganov/llama.cpp.git
   ```

2. 构建llama.cpp（支持CUDA）：
   ```cmd
   cd llama.cpp
   mkdir build
   cd build
   cmake .. -DLLAMA_CUDA=ON -DLLAMA_CUDA_F16=ON -DLLAMA_STATIC=ON -DCMAKE_BUILD_TYPE=Release
   cmake --build . --config Release --parallel
   ```

3. 构建InkCop：
   ```cmd
   cd ../../..
   mkdir build
   cd build
   cmake .. -DENABLE_LOCAL_GGUF=ON -DCMAKE_BUILD_TYPE=Release
   cmake --build . --config Release --parallel
   ```

## 使用说明

### 1. 启动应用程序
运行构建完成的 `build/bin/InkCop.exe`

### 2. 配置知识库设置
1. 打开设置页面
2. 选择"知识库设置"
3. 在"嵌入模式"中选择"本地模型"或"自动选择"

### 3. 检测GPU能力
点击"检测GPU能力"按钮，系统会检测您的NVIDIA A4000 GPU并显示：
- GPU设备信息
- 支持的最大层数
- 推荐的GPU层数设置

### 4. 选择GGUF模型文件
1. 点击"选择模型文件"
2. 选择您下载的GGUF格式嵌入式模型文件
3. 推荐的模型：
   - `bge-large-zh-v1.5.gguf` (中文嵌入模型)
   - `all-MiniLM-L6-v2.gguf` (英文嵌入模型)

### 5. 配置GPU设置
- **GPU层数**：建议设置为20-30层（根据您的显存调整）
- **上下文大小**：建议设置为2048
- **启用GPU加速**：确保已勾选

### 6. 加载和测试模型
1. 点击"加载模型"按钮
2. 等待模型加载完成
3. 点击"测试本地模型"验证功能

## 故障排除

### GPU检测失败
如果GPU检测显示不可用，请检查：

1. **CUDA运行时**：确保已安装CUDA 11.0+
2. **NVIDIA驱动**：更新到最新版本
3. **编译选项**：确保使用 `-DENABLE_LOCAL_GGUF=ON` 编译
4. **llama.cpp CUDA支持**：确保llama.cpp编译时启用了CUDA

### 性能优化建议

#### 针对NVIDIA A4000 (16GB显存)
- **GPU层数**：可以设置为30-40层
- **上下文大小**：可以设置为4096或更高
- **批处理大小**：建议512-1024

#### 内存使用优化
- 关闭不必要的应用程序
- 监控系统内存使用情况
- 根据模型大小调整设置

## 支持的模型格式

### 推荐的嵌入式模型
1. **BGE系列** (中文优化)
   - `bge-large-zh-v1.5.gguf`
   - `bge-base-zh-v1.5.gguf`

2. **Sentence Transformers**
   - `all-MiniLM-L6-v2.gguf`
   - `all-mpnet-base-v2.gguf`

3. **多语言模型**
   - `multilingual-e5-large.gguf`
   - `paraphrase-multilingual-MiniLM-L12-v2.gguf`

### 模型下载建议
- 使用Hugging Face模型库
- 确保下载的是GGUF格式
- 验证模型是否为嵌入式模型（不是生成式模型）

## 技术细节

### 编译选项
```cmake
-DENABLE_LOCAL_GGUF=ON          # 启用GGUF支持
-DLLAMA_CUDA=ON                 # 启用CUDA支持
-DLLAMA_CUDA_F16=ON             # 启用半精度浮点
-DLLAMA_STATIC=ON               # 静态链接
```

### 运行时检查
应用程序会在运行时检查：
- CUDA驱动可用性
- GPU内存容量
- 模型兼容性
- 性能配置

## 更新日志

### v1.0.0
- 首次发布本地GGUF支持
- 支持NVIDIA GPU加速
- 集成llama.cpp库
- 自动GPU检测和配置

## 联系支持

如果您在使用过程中遇到问题，请：
1. 检查本文档的故障排除部分
2. 查看应用程序日志输出
3. 提供详细的错误信息和系统配置