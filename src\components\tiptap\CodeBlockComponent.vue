<template>
  <node-view-wrapper>
    <q-card
      flat
      bordered
      class="radius-sm overflow-hidden"
      :class="$q.dark.isActive ? 'bg-grey-9' : 'bg-grey-1'"
    >
      <q-bar class="transparent q-pl-sm q-pr-xs">
        <span class="text-grey-6 cursor-pointer unselectable" spellcheck="false"
          >{{ selectedLanguage }}
          <q-menu>
            <q-list dense>
              <q-item
                v-for="language in languages"
                :key="language"
                :value="language"
                clickable
                @click="selectedLanguage = language"
              >
                {{ language }}
              </q-item>
            </q-list>
          </q-menu>
        </span>
        <q-space />
        <q-btn flat dense icon="mdi-content-copy" @click="copyCode" />
      </q-bar>
      <q-card-section class="q-pa-none inner-code">
        <pre><code><node-view-content /></code></pre>
      </q-card-section>
    </q-card>
  </node-view-wrapper>
</template>

<script lang="ts" setup>
import { NodeViewContent, NodeViewWrapper } from '@tiptap/vue-3';
import { computed, onMounted, ref } from 'vue';
import type { NodeViewProps } from '@tiptap/vue-3';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { copyToClipboard } from 'src/utils/clipboard';

const $q = useQuasar();
const { t: $t } = useI18n({ useScope: 'global' });

const props = defineProps<NodeViewProps>();

const languages = ref<string[]>(props.extension.options.lowlight.listLanguages());

const selectedLanguage = computed({
  get: () => props.node.attrs.language,
  set: (language: string | null) => {
    props.updateAttributes({ language });
  },
});
onMounted(() => {
  if (!selectedLanguage.value) {
    selectedLanguage.value = 'javascript';
  }
});

const copyCode = async () => {
  await copyToClipboard(props.node.textContent, {
    showNotification: true,
    successMessage: $t('src.components.tiptap.codeBlock.copied'),
    errorMessage: $t('src.components.tiptap.codeBlock.copyFailed'),
  });
};
</script>
<style lang="scss">
.inner-code {
  pre {
    margin: 0 !important;
    border: 0px solid rgba(123, 123, 123, 0) !important;
    border-radius: 0px !important;
  }
}
</style>
