<template>
  <q-dialog
    v-model="show"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
  >
    <q-card class="column no-wrap">
      <!-- 标题栏 -->
      <q-card-section class="row items-center q-pa-md bg-primary text-white">
        <div class="text-h6">{{ $t('src.components.UploadDocumentDialog.upload_document') }}</div>
        <q-space />
        <q-btn flat round dense icon="close" @click="closeDialog" :disable="loading" />
      </q-card-section>

      <!-- 内容区域 -->
      <q-card-section class="col q-pa-md">
        <div class="row q-gutter-md">
          <!-- 左侧：文件上传区域 -->
          <div class="col-12 col-md-6">
            <div class="text-h6 q-mb-md">
              {{ $t('src.components.UploadDocumentDialog.file_upload') }}
            </div>

            <!-- 文件上传区域 -->
            <div
              class="file-upload-area"
              :class="{
                'file-upload-area--dragover': isDragOver,
                'file-upload-area--has-file': !!selectedFile,
              }"
              @drop="handleDrop"
              @dragover="handleDragOver"
              @dragleave="handleDragLeave"
              @click="triggerFileInput"
            >
              <input
                ref="fileInput"
                type="file"
                accept=".txt,.md,.markdown,.docx,.pdf,.html,.htm,.json,.xml,.csv,.rtf"
                @change="handleFileSelect"
                class="hidden"
              />

              <div v-if="!selectedFile" class="text-center q-pa-xl">
                <q-icon name="cloud_upload" size="64px" class="text-grey-6 q-mb-md" />
                <div class="text-h6 text-grey-8 q-mb-sm">
                  {{ $t('src.components.UploadDocumentDialog.drag_file_here') }}
                </div>
                <div class="text-body2 text-grey-6">
                  {{ $t('src.components.UploadDocumentDialog.supported_formats') }}
                </div>
              </div>

              <div v-else class="text-center q-pa-xl">
                <q-icon name="description" size="64px" class="text-primary q-mb-md" />
                <div class="text-h6 text-grey-8 q-mb-sm">
                  {{ selectedFile.name }}
                </div>
                <div class="text-body2 text-grey-6 q-mb-md">
                  {{ formatFileSize(selectedFile.size) }}
                </div>
                <q-btn
                  flat
                  color="primary"
                  :label="$t('src.components.UploadDocumentDialog.reselect')"
                  @click="triggerFileInput"
                />
              </div>
            </div>

            <!-- 文件验证信息 -->
            <div v-if="fileValidation" class="q-mt-md">
              <q-banner
                :class="
                  fileValidation.valid
                    ? 'text-positive bg-positive-1'
                    : 'text-negative bg-negative-1'
                "
                inline-actions
              >
                <q-icon :name="fileValidation.valid ? 'check_circle' : 'error'" />
                {{ fileValidation.message }}
              </q-banner>
            </div>
          </div>

          <!-- 右侧：文档信息配置 -->
          <div class="col-12 col-md-6">
            <div class="text-h6 q-mb-md">
              {{ $t('src.components.UploadDocumentDialog.document_info') }}
            </div>

            <q-form ref="formRef" @submit.prevent="handleSubmit">
              <!-- 文档标题 -->
              <q-input
                v-model="formData.title"
                :label="$t('src.components.UploadDocumentDialog.document_title')"
                outlined
                dense
                class="q-mb-md"
                :rules="[
                  (val) =>
                    !!val?.trim() ||
                    $t('src.components.UploadDocumentDialog.please_input_document_title'),
                ]"
                :loading="loading"
              />

              <!-- 文档描述 -->
              <q-input
                v-model="formData.description"
                :label="$t('src.components.UploadDocumentDialog.document_description')"
                outlined
                dense
                type="textarea"
                rows="3"
                class="q-mb-md"
                :loading="loading"
              />

              <!-- 切割策略选择器 -->
              <div class="q-mb-md">
                <ChunkingStrategySelector
                  v-model="chunkingStrategy"
                  :disabled="loading"
                  @validation-change="onChunkingValidationChange"
                />
              </div>
            </q-form>
          </div>
        </div>
      </q-card-section>

      <!-- 底部操作区域 -->
      <q-card-actions class="q-pa-md">
        <q-space />
        <q-btn
          flat
          :label="$t('src.components.UploadDocumentDialog.cancel')"
          @click="closeDialog"
          :disable="loading"
        />
        <q-btn
          color="primary"
          :label="$t('src.components.UploadDocumentDialog.upload')"
          @click="handleSubmit"
          :loading="loading"
          :disable="!canSubmit"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { useQuasar, QForm } from 'quasar';
import type { ChunkingMethod, ChunkingConfig } from '../types/qwen';
import ChunkingStrategySelector from './ChunkingStrategySelector.vue';

import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

// Props
interface Props {
  modelValue: boolean;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (
    e: 'upload',
    data: {
      file: File;
      title: string;
      description: string;
      chunkingMethod: ChunkingMethod;
      chunkingConfig: ChunkingConfig;
    },
  ): void;
}

const emit = defineEmits<Emits>();

// Composables
const $q = useQuasar();

// 响应式数据
const show = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

const fileInput = ref<HTMLInputElement>();
const formRef = ref<QForm>();

const selectedFile = ref<File | null>(null);
const isDragOver = ref(false);
const isChunkingValid = ref(true);

const formData = ref({
  title: '',
  description: '',
});

const chunkingStrategy = ref<{
  method: ChunkingMethod;
  config: ChunkingConfig;
}>({
  method: 'markdown',
  config: {
    chunkSize: 800,
    chunkOverlap: 200,
  },
});

// 文件验证
const fileValidation = ref<{
  valid: boolean;
  message: string;
} | null>(null);

// 计算属性
const canSubmit = computed(() => {
  return (
    selectedFile.value &&
    formData.value.title.trim() &&
    fileValidation.value?.valid &&
    isChunkingValid.value &&
    !props.loading
  );
});

// 支持的文件类型
const supportedTypes = [
  'text/plain',
  'text/markdown',
  'text/html',
  'application/json',
  'application/xml',
  'text/xml',
  'text/csv',
  'application/rtf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/pdf',
];

const supportedExtensions = [
  '.txt',
  '.md',
  '.markdown',
  '.html',
  '.htm',
  '.json',
  '.xml',
  '.csv',
  '.rtf',
  '.docx',
  '.pdf',
];

// 方法
const validateFile = (file: File): { valid: boolean; message: string } => {
  // 检查文件大小 (最大 50MB)
  const maxSize = 50 * 1024 * 1024; // 50MB
  if (file.size > maxSize) {
    return {
      valid: false,
      message: $t('src.components.UploadDocumentDialog.file_size_too_large'),
    };
  }

  // 检查文件类型
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
  const isTypeSupported =
    supportedTypes.includes(file.type) || supportedExtensions.includes(fileExtension);

  if (!isTypeSupported) {
    return {
      valid: false,
      message: $t('src.components.UploadDocumentDialog.unsupported_file_format'),
    };
  }

  return {
    valid: true,
    message: $t('src.components.UploadDocumentDialog.file_validation_passed'),
  };
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const triggerFileInput = () => {
  if (props.loading) return;
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    setSelectedFile(file);
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = false;

  if (props.loading) return;

  const file = event.dataTransfer?.files[0];
  if (file) {
    setSelectedFile(file);
  }
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (!props.loading) {
    isDragOver.value = true;
  }
};

const handleDragLeave = () => {
  isDragOver.value = false;
};

const setSelectedFile = (file: File) => {
  selectedFile.value = file;

  // 验证文件
  fileValidation.value = validateFile(file);

  // 自动填充标题
  if (!formData.value.title) {
    const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, '');
    formData.value.title = nameWithoutExtension;
  }
};

const onChunkingValidationChange = (isValid: boolean) => {
  isChunkingValid.value = isValid;
};

const handleSubmit = async () => {
  if (!canSubmit.value) return;

  try {
    // 验证表单
    const isFormValid = await formRef.value?.validate();
    if (!isFormValid) return;

    if (!selectedFile.value) {
      $q.notify({
        type: 'negative',
        message: $t('src.components.UploadDocumentDialog.please_select_file'),
      });
      return;
    }

    // 发送上传事件
    emit('upload', {
      file: selectedFile.value,
      title: formData.value.title.trim(),
      description: formData.value.description.trim(),
      chunkingMethod: chunkingStrategy.value.method,
      chunkingConfig: chunkingStrategy.value.config,
    });
  } catch (error) {
    console.error($t('src.components.UploadDocumentDialog.submit_failed'), error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.UploadDocumentDialog.submit_failed'),
    });
  }
};

const closeDialog = () => {
  if (props.loading) return;
  show.value = false;
};

const resetForm = () => {
  selectedFile.value = null;
  fileValidation.value = null;
  formData.value = {
    title: '',
    description: '',
  };
  chunkingStrategy.value = {
    method: 'markdown',
    config: {
      chunkSize: 800,
      chunkOverlap: 200,
    },
  };
  isChunkingValid.value = true;

  // 重置文件输入
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// 监听对话框开关，重置表单
watch(show, (newValue) => {
  if (newValue) {
    void nextTick(() => {
      resetForm();
    });
  }
});
</script>

<style scoped>
.file-upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-upload-area:hover {
  border-color: #1976d2;
  background-color: #f5f5f5;
}

.file-upload-area--dragover {
  border-color: #1976d2;
  background-color: #e3f2fd;
}

.file-upload-area--has-file {
  border-color: #1976d2;
  background-color: #f5f5f5;
}

.q-dark .file-upload-area {
  border-color: #555;
}

.q-dark .file-upload-area:hover {
  border-color: #1976d2;
  background-color: #333;
}

.q-dark .file-upload-area--dragover {
  border-color: #1976d2;
  background-color: #1e3a8a;
}

.q-dark .file-upload-area--has-file {
  border-color: #1976d2;
  background-color: #333;
}

.hidden {
  display: none;
}
</style>
