declare module '@tiptap-pro/extension-table-of-contents' {
  import type { Extension } from '@tiptap/core';

  export interface Anchor {
    id: string;
    textContent: string;
    level: number;
    originalLevel: number;
    isActive: boolean;
    isScrolledOver: boolean;
  }

  export interface TableOfContentsOptions {
    HTMLAttributes?: Record<string, unknown>;
    onUpdate?: (anchors: Anchor[]) => void;
    scrollParent?: () => HTMLElement | Window;
    anchorTypes?: string[];
    getLevel?: (anchor: Anchor) => number;
    getId?: (content: string) => string;
    getIndex?: (anchor: Anchor, previousAnchors: Anchor[], level: number) => number;
  }

  export interface TableOfContentsStorage {
    items: Anchor[];
  }

  const TableOfContents: Extension<TableOfContentsOptions, TableOfContentsStorage>;
  export default TableOfContents;
}

declare module '@tiptap-pro/extension-details' {
  import type { Node } from '@tiptap/core';
  export interface DetailsOptions {
    persist?: boolean;
    openClassName?: string;
    HTMLAttributes?: Record<string, unknown>;
  }
  const Details: Node<DetailsOptions> & {
    configure: (options: DetailsOptions) => Node<DetailsOptions>;
  };
  export default Details;
}

declare module '@tiptap-pro/extension-details-summary' {
  import type { Node } from '@tiptap/core';
  const DetailsSummary: Node & {
    configure: (options?: Record<string, unknown>) => Node;
  };
  export default DetailsSummary;
}

declare module '@tiptap-pro/extension-details-content' {
  import type { Node } from '@tiptap/core';
  const DetailsContent: Node & {
    configure: (options?: Record<string, unknown>) => Node;
  };
  export default DetailsContent;
}

declare module '@tiptap-pro/extension-unique-id' {
  import type { Extension } from '@tiptap/core';
  export interface UniqueIDOptions {
    attributeName?: string;
    types?: string[];
  }
  const UniqueID: Extension<UniqueIDOptions> & {
    configure: (options: UniqueIDOptions) => Extension<UniqueIDOptions>;
  };
  export default UniqueID;
}

declare module '@tiptap-pro/extension-file-handler' {
  import type { Extension, Editor } from '@tiptap/core';
  export interface FileHandlerOptions {
    allowedMimeTypes?: string[];
    onDrop?: (editor: Editor, files: File[], pos: number) => void;
  }
  const FileHandler: Extension<FileHandlerOptions> & {
    configure: (options: FileHandlerOptions) => Extension<FileHandlerOptions>;
  };
  export default FileHandler;
}
