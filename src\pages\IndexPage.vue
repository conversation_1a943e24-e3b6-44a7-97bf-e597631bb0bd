<template>
  <q-layout view="lHr LpR lFr" container class="absolute-full">
    <q-drawer
      v-if="!leftHiddenItems.includes(uiStore.app)"
      v-model="uiStore.leftDrawerOpen"
      side="left"
      :width="leftDrawerWidth"
      class="border-right drawer-optimized"
      :class="$q.dark.isActive ? 'bg-grey-10' : 'bg-white'"
      :duration="200"
      :breakpoint="0"
    >
      <!-- drawer content -->
      <div class="fit drawer-content">
        <KeepAlive>
          <DocManager v-show="uiStore.app === 'inkcop'" class="fit" />
        </KeepAlive>
        <KeepAlive>
          <SettingPannel v-show="uiStore.app === 'settings'" class="fit" />
        </KeepAlive>
        <KeepAlive>
          <KnowledgeBaseManager v-show="uiStore.app === 'knowledge'" class="fit" />
        </KeepAlive>
      </div>
      <!-- 左侧拖拽手柄 -->
      <div
        class="resize-handle resize-handle-left z-max"
        @mousedown="startResize($event, 'left')"
      ></div>
    </q-drawer>

    <q-drawer
      v-if="!rightHiddenItems.includes(uiStore.app)"
      v-model="uiStore.rightDrawerOpen"
      side="right"
      class="border-left drawer-optimized"
      :class="$q.dark.isActive ? 'bg-grey-10' : 'bg-white'"
      :width="rightDrawerWidth"
      :duration="200"
      :breakpoint="0"
    >
      <!-- drawer content -->
      <div class="fit drawer-content">
        <KeepAlive>
          <ConversitonContainer :rightDrawerWidth="rightDrawerWidth" class="fit" />
        </KeepAlive>
      </div>
      <!-- 右侧拖拽手柄 -->
      <div
        class="resize-handle resize-handle-right"
        @mousedown="startResize($event, 'right')"
      ></div>
    </q-drawer>

    <q-page-container>
      <q-page>
        <div class="absolute-full relative-position ink">
          <div class="absolute-full flex flex-center">
            <div class="flag" />
          </div>
          <router-view />
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, onUnmounted, onBeforeMount, watch, nextTick } from 'vue';
import { useQuasar } from 'quasar';
import SettingPannel from 'src/components/settings/SettingPannel.vue';
import ConversitonContainer from 'src/components/ConversitonContainer.vue';
import KnowledgeBaseManager from 'src/components/KnowledgeBaseManager.vue';
import DocManager from 'src/components/DocManager.vue';
import { useUiStore } from 'src/stores/ui';
const uiStore = useUiStore();
const $q = useQuasar();

const leftDrawerWidth = ref(Number(localStorage.getItem('leftDrawerWidth')) || 240);
const rightDrawerWidth = ref(Number(localStorage.getItem('rightDrawerWidth')) || 540);

onBeforeMount(() => {
  uiStore.leftDrawerOpen = true;
  // 从localStorage恢复抽屉状态
  const savedLeftDrawerOpen = localStorage.getItem('leftDrawerOpen');
  const savedRightDrawerOpen = localStorage.getItem('rightDrawerOpen');

  if (savedLeftDrawerOpen !== null) {
    uiStore.leftDrawerOpen = savedLeftDrawerOpen === 'true';
  }
  if (savedRightDrawerOpen !== null) {
    uiStore.rightDrawerOpen = savedRightDrawerOpen === 'true';
  }
});

// 监听抽屉状态变化，优化切换性能
watch(
  () => [uiStore.leftDrawerOpen, uiStore.rightDrawerOpen],
  () => {
    // 在抽屉切换时添加性能优化类
    document.body.classList.add('drawer-transitioning');

    // 切换完成后移除优化类
    void nextTick(() => {
      setTimeout(() => {
        document.body.classList.remove('drawer-transitioning');
      }, 250); // 稍微延长以确保动画完成
    });
  },
  { flush: 'post' },
);

// 拖拽状态
const isResizing = ref(false);
const currentDrawer = ref<'left' | 'right' | null>(null);
const startX = ref(0);
const startWidth = ref(0);
let animationFrameId: number | null = null;
let pendingWidth: number | null = null;

// 使用 requestAnimationFrame 优化拖拽性能
const updateDrawerWidth = () => {
  if (pendingWidth === null || !currentDrawer.value) return;

  if (currentDrawer.value === 'left') {
    leftDrawerWidth.value = pendingWidth;
  } else {
    rightDrawerWidth.value = pendingWidth;
  }

  pendingWidth = null;
  animationFrameId = null;
};

// 开始拖拽调整大小
const startResize = (event: MouseEvent, drawer: 'left' | 'right') => {
  isResizing.value = true;
  currentDrawer.value = drawer;
  startX.value = event.clientX;
  startWidth.value = drawer === 'left' ? leftDrawerWidth.value : rightDrawerWidth.value;

  // 添加body类来优化拖拽性能
  document.body.classList.add('resizing');

  // 强制设置整个文档的鼠标样式
  document.documentElement.style.cursor = 'col-resize';
  document.body.style.cursor = 'col-resize';

  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);
  event.preventDefault();
};

// 处理拖拽过程 - 使用节流优化性能
const handleResize = (event: MouseEvent) => {
  if (!isResizing.value || !currentDrawer.value) return;

  const deltaX = event.clientX - startX.value;
  let newWidth: number;

  if (currentDrawer.value === 'left') {
    newWidth = startWidth.value + deltaX;
  } else {
    newWidth = startWidth.value - deltaX;
  }

  // 限制最小和最大宽度
  if (currentDrawer.value === 'left') {
    newWidth = Math.max(100, Math.min(740, newWidth));
  } else {
    newWidth = Math.max(200, Math.min(960, newWidth));
  }

  // 使用 requestAnimationFrame 节流更新
  pendingWidth = newWidth;
  if (animationFrameId === null) {
    animationFrameId = requestAnimationFrame(updateDrawerWidth);
  }
};

// 停止拖拽
const stopResize = () => {
  // 确保最后一次更新完成
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId);
    updateDrawerWidth();
  }

  // 保存到localStorage
  if (currentDrawer.value === 'left') {
    localStorage.setItem('leftDrawerWidth', leftDrawerWidth.value.toString());
  } else if (currentDrawer.value === 'right') {
    localStorage.setItem('rightDrawerWidth', rightDrawerWidth.value.toString());
  }

  // 移除body类
  document.body.classList.remove('resizing');

  // 恢复鼠标样式
  document.documentElement.style.cursor = '';
  document.body.style.cursor = '';

  isResizing.value = false;
  currentDrawer.value = null;
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
};

onUnmounted(() => {
  if (isResizing.value) {
    stopResize();
  }
});
const leftHiddenItems = ['icons'];
const rightHiddenItems = ['icons'];
</script>

<style scoped>
/* 抽屉性能优化 */
.drawer-optimized {
  /* 使用GPU加速 */
  will-change: transform;
  /* 优化渲染层 */
  transform: translateZ(0);
  /* 禁用不必要的transition */
  transition: transform 0.2s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

.drawer-content {
  /* 优化内容渲染 */
  contain: layout style paint;
  /* 避免重排 */
  overflow: hidden;
}

/* 抽屉打开/关闭时的优化 */
.q-drawer--mobile.q-drawer--on-top {
  /* 使用transform而不是width变化 */
  transition: transform 0.2s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

.resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: transparent;
  cursor: col-resize;
  z-index: 1000;
  transition: background-color 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  /* GPU加速 */
  will-change: background-color;
  transform: translateZ(0);
}

.resize-handle:hover {
  background-color: #027be3;
}

.resize-handle-right {
  left: 0;
}

.resize-handle-left {
  right: 0;
}

/* 拖拽时的样式 - 禁用transition以提高性能 */
.resize-handle:active,
.resize-handle.resizing {
  background-color: #027be3;
  transition: none;
}

/* 拖拽时禁用页面选择，提高性能 */
body.resizing {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  cursor: col-resize !important;
  pointer-events: none;
}

body.resizing *,
body.resizing *:hover,
body.resizing *:active,
body.resizing *:focus {
  cursor: col-resize !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  pointer-events: auto;
}

/* 确保拖拽手柄在拖拽时保持可见和可操作 */
body.resizing .resize-handle {
  cursor: col-resize !important;
  pointer-events: auto !important;
}

/* 优化KeepAlive组件性能 */
.fit {
  /* 避免不必要的重排 */
  contain: layout;
}
</style>
