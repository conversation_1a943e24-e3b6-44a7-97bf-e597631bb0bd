# TipTap 查找替换功能集成文档

## 概述

本文档描述了如何在 InkCop 项目中集成 TipTap 编辑器的查找替换功能。我们使用了第三方扩展 `@sereneinserenade/tiptap-search-and-replace` 来实现这个功能。

## 安装的依赖

```bash
npm install @sereneinserenade/tiptap-search-and-replace
```

## 集成的组件和文件

### 1. 编辑器工厂 (`src/utils/editorFactory.ts`)

- 导入了 `SearchAndReplace` 扩展
- 在 `createStandardEditor` 和 `getStandardEditorExtensions` 函数中配置了扩展
- 配置选项：
  ```typescript
  SearchAndReplace.configure({
    searchResultClass: 'bg-deep-orange', // 搜索结果高亮样式类
  })
  ```

### 2. 查找替换面板组件 (`src/components/tiptap/SearchReplacePanel.vue`)

这是一个独立的 Vue 组件，提供了完整的查找替换界面：

**功能特性：**
- 查找输入框，支持实时搜索
- 替换输入框
- 上一个/下一个搜索结果导航
- 单个替换和全部替换功能
- 区分大小写选项
- 正则表达式支持选项
- 搜索结果计数显示
- 键盘快捷键支持（Escape 关闭面板）

**Props：**
- `editor`: TipTap 编辑器实例
- `visible`: 控制面板显示/隐藏

**Events：**
- `close`: 关闭面板事件

### 3. 工具栏命令 (`src/components/tiptap/useCommands.ts`)

- 添加了查找替换面板的状态管理
- 新增了 `searchReplace` 工具栏按钮
- 提供了以下状态管理函数：
  - `initSearchReplacePanelState(instanceKey)`
  - `getSearchReplacePanelState(instanceKey)`
  - `toggleSearchReplacePanel(instanceKey)`

### 4. 主编辑器组件 (`src/components/tiptap/TipTap.vue`)

- 集成了 `SearchReplacePanel` 组件
- 添加了 Ctrl+F 键盘快捷键支持
- 在编辑器的 `handleKeyDown` 中处理快捷键

## 使用方法

### 1. 通过工具栏按钮

在编辑器工具栏中点击查找替换按钮（🔍 图标）来打开查找替换面板。

### 2. 通过键盘快捷键

- **Ctrl+F** (Windows/Linux) 或 **Cmd+F** (Mac)：打开/关闭查找替换面板
- **Escape**：关闭查找替换面板
- **Enter**：查找下一个
- **Shift+Enter**：查找上一个

### 3. 查找替换操作

1. 在查找框中输入要搜索的文本
2. 可选：勾选"区分大小写"或"正则表达式"选项
3. 使用导航按钮或键盘快捷键浏览搜索结果
4. 在替换框中输入替换文本
5. 点击"替换"进行单个替换，或"全部替换"进行批量替换

## 样式配置

搜索结果的高亮样式通过 `bg-deep-orange` CSS 类实现：

```css
.bg-deep-orange {
  background-color: #ff5722 !important;
  color: white !important;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
}

/* 暗色模式支持 */
.body--dark .bg-deep-orange {
  background-color: #ff7043 !important;
  color: #000 !important;
}
```

## API 命令

SearchAndReplace 扩展提供了以下命令（通过 `editor.commands` 访问）：

- `setSearchTerm(term: string)`: 设置搜索词
- `setReplaceTerm(term: string)`: 设置替换词
- `replace()`: 替换当前匹配项
- `replaceAll()`: 替换所有匹配项

## 注意事项

1. 查找替换面板是浮动定位的，位于编辑器右上角
2. 面板状态按编辑器实例进行管理，支持多个编辑器实例
3. 搜索结果会实时更新，无需手动刷新
4. 支持正则表达式搜索，但需要用户手动启用
5. 区分大小写选项默认关闭

## 扩展和自定义

如果需要扩展功能，可以：

1. 修改 `SearchReplacePanel.vue` 组件添加新的 UI 元素
2. 在 `editorFactory.ts` 中调整 SearchAndReplace 扩展的配置
3. 在 `useCommands.ts` 中添加新的工具栏按钮或命令
4. 通过 CSS 自定义搜索结果的高亮样式

## 故障排除

如果遇到问题：

1. 确保 `@sereneinserenade/tiptap-search-and-replace` 包已正确安装
2. 检查编辑器实例是否正确传递给 SearchReplacePanel 组件
3. 验证 SearchAndReplace 扩展是否在编辑器配置中正确加载
4. 查看浏览器控制台是否有相关错误信息
