import { ref, computed } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { Notify } from 'quasar';
import { useI18n } from 'vue-i18n';

// 自动补全建议的接口定义
export interface AutoCompleteSuggestion {
  id: string;
  text: string;
}

// 自动补全配置接口
export interface AutoCompleteConfig {
  enabled: boolean;
  triggerLength: number; // 触发自动补全的最小字符数
  maxSuggestions: number; // 最大建议数量
  debounceTime: number; // 防抖时间（毫秒）
  temperature: number; // AI生成温度
  maxTokens: number; // 最大生成token数
}

// 從編輯器設置獲取默認配置的輔助函數
const getDefaultConfigFromEditorSettings = (): AutoCompleteConfig => {
  const uiStore = useUiStore();
  const autoCompleteSettings = uiStore.perferences?.autoComplete;

  return {
    enabled: autoCompleteSettings?.enabled ?? false,
    triggerLength: autoCompleteSettings?.autoCompleteTriggerLength ?? 10,
    maxSuggestions: autoCompleteSettings?.autoCompleteMaxSuggestions ?? 3,
    debounceTime: autoCompleteSettings?.autoCompleteDebounceTime ?? 500,
    temperature: autoCompleteSettings?.body?.temperature ?? 0.7,
    maxTokens: autoCompleteSettings?.body?.maxTokens ?? 50,
  };
};

export const useAutoComplete = () => {
  const { t: $t } = useI18n({ useScope: 'global' });
  const uiStore = useUiStore();
  const autoComplete = uiStore.perferences?.autoComplete;

  const suggestions = ref<AutoCompleteSuggestion>({
    id: '',
    text: '',
  });
  /**
   * 清除建议
   */
  const clearSuggestions = () => {
    suggestions.value = {
      id: '',
      text: '',
    };
  };

  const autoCompletePrompt = uiStore.perferences?.prompt.autoComplete.list.find(
    (prompt) =>
      prompt.name === (uiStore.perferences?.prompt.autoComplete.selected || $t('default')),
  );
  // API配置
  const baseURL = autoComplete.base_url;
  const apiKey = autoComplete.api_key;
  if (!baseURL || !apiKey) {
    Notify.create({
      message: 'AutoComplete baseURL or apiKey is not set',
      color: 'negative',
      position: 'top',
      timeout: 3000,
    });
    return;
  }
  const llmOptions = {
    model: autoComplete.body.model,
    temperature: autoComplete.body.temperature,
    max_tokens: autoComplete.body.maxTokens,
    stream: false, // 自動完成不使用流式輸出
  } as {
    model: string;
    temperature: number;
    max_tokens: number;
    stream: boolean;
    enable_thinking?: boolean;
    thinking_budget?: number;
    thinking?: boolean;
  };
  const isOllama = apiKey?.includes('ollama') || baseURL.includes('11434');
  if (isOllama) {
    llmOptions.thinking = false;
  } else if (autoComplete.body.model.includes('qwen')) {
    llmOptions.enable_thinking = false; // 自動完成不使用思考
    llmOptions.thinking_budget = 0; // 自動完成不使用思考
  }

  // 响应式状态
  const isLoading = ref(false);
  const isEnabled = ref(false); // 默认禁用
  const config = ref<AutoCompleteConfig>({ ...getDefaultConfigFromEditorSettings() });

  // 防抖定时器
  let debounceTimer: NodeJS.Timeout | null = null;
  // 中止控制器
  let abortController: AbortController | null = null;

  // 计算属性
  const hasSuggestions = computed(() => suggestions.value.text.length > 0);
  const canTrigger = computed(() => isEnabled.value && config.value.enabled);

  /**
   * 生成建议（整合自useTabkey）
   */
  const generateSuggestions = async (
    beforeCursor: string,
    afterCursor: string,
    currentParagraph: string,
    fullText: string,
  ) => {
    if (!autoComplete?.enabled) {
      throw new Error('AutoComplete is not enabled');
    }
    const prompt = `你是一位专业的文档写作助手。请根据用户当前编辑的文档内容、光标所在位置，为用户提供输入建议。

文档全文：${fullText}
当前段落：${currentParagraph}
光标前文本：${beforeCursor}
光标后文本：${afterCursor}

${autoCompletePrompt?.prompt || ''}`;

    // 创建新的中止控制器
    abortController = new AbortController();
    const url = isOllama ? `${baseURL}/api/generate` : `${baseURL}/chat/completions`;

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          ...llmOptions,
          messages: [{ role: 'system', content: prompt }],
        }),
        signal: abortController.signal, // 添加中止信号
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[AutoComplete] response data:', data);
      if (data) {
        let content: string = isOllama ? data.response : data.choices[0].message.content;
        // console.log('[AutoComplete] 生成建议完成，建议内容:', content);

        // 处理LLM返回的内容，删除<think>标签及其内部内容
        content = content.replace(/<think>[\s\S]*?(?:<\/think>|$)/gi, '');
        if (!content.trim()) {
          return null;
        }

        return {
          id: data.id,
          text: content.trim(),
        };
      }
      return null;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('[AutoComplete] 请求被中止');
        return null;
      }
      throw error;
    } finally {
      abortController = null;
    }
  };

  /**
   * 中止自动补全
   */
  const abortAutoComplete = () => {
    console.log('[AutoComplete] 中止自动补全');

    // 中止正在进行的API请求
    if (abortController) {
      console.log('[AutoComplete] 中止API请求');
      abortController.abort();
      abortController = null;
    }

    // 清除防抖定时器
    if (debounceTimer) {
      console.log('[AutoComplete] 清除防抖定时器');
      clearTimeout(debounceTimer);
      debounceTimer = null;
    }

    // 清除建议
    clearSuggestions();

    // 重置加载状态
    isLoading.value = false;
  };

  /**
   * 触发自动补全（带防抖）
   */
  const triggerAutoComplete = (
    currentText: string,
    cursorPosition: number,
    context?: {
      beforeText?: string;
      afterText?: string;
      currentParagraph?: string;
      fullText?: string;
      documentTitle?: string;
      documentType?: string;
    },
  ) => {
    if (!autoComplete?.enabled) {
      throw new Error('AutoComplete is not enabled');
    }
    if (!canTrigger.value) {
      console.log('[AutoComplete] 无法触发，清除建议');
      clearSuggestions();
      return;
    }

    // 清除之前的定时器
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // 设置加载状态
    isLoading.value = true;

    // 设置防抖
    debounceTimer = setTimeout(() => {
      void (async () => {
        // 使用从Tiptap获取的精确信息，如果没有则使用备用方案
        const textBeforeCursor = context?.beforeText || currentText.slice(0, cursorPosition);
        const textAfterCursor = context?.afterText || currentText.slice(cursorPosition);
        const currentParagraph = context?.currentParagraph || '';
        const fullText = context?.fullText || currentText;
        // 文本长度不足，跳过生成
        if (textBeforeCursor.length < config.value.triggerLength) {
          isLoading.value = false;
          return;
        }

        try {
          const newSuggestions = await generateSuggestions(
            textBeforeCursor,
            textAfterCursor,
            currentParagraph,
            fullText,
          );

          isLoading.value = false;

          if (newSuggestions) {
            suggestions.value = newSuggestions;
          }
        } catch (error) {
          console.error('[AutoComplete] 生成建议失败:', error);
          isLoading.value = false;
          clearSuggestions();
        }
      })();
    }, config.value.debounceTime);
  };

  /**
   * 更新配置
   */
  const updateConfig = (newConfig: Partial<AutoCompleteConfig>) => {
    // console.log('[AutoComplete] 更新配置:', newConfig);
    config.value = { ...config.value, ...newConfig };
  };

  /**
   * 启用/禁用自动补全
   */
  const toggleEnabled = (enabled?: boolean) => {
    const newEnabled = enabled !== undefined ? enabled : !isEnabled.value;
    // console.log('[AutoComplete] 切换启用状态:', isEnabled.value, '->', newEnabled);
    isEnabled.value = newEnabled;
    if (!isEnabled.value) {
      clearSuggestions();
    }
  };

  /**
   * 获取建议的显示文本（用于预览）
   */
  const getSuggestionPreview = (suggestion: AutoCompleteSuggestion): string => {
    return suggestion.text.length > 100 ? suggestion.text.slice(0, 100) + '...' : suggestion.text;
  };

  return {
    // 状态
    suggestions,
    isLoading,
    isEnabled,
    config,
    hasSuggestions,
    canTrigger,

    // 方法
    triggerAutoComplete,
    clearSuggestions,
    updateConfig,
    toggleEnabled,
    getSuggestionPreview,
    abortAutoComplete,
    generateSuggestions, // 导出生成建议方法，以便其他地方可能需要使用
  };
};
