# Build Installer Script Improvements

## Summary

The `build-installer.ps1` script has been enhanced to resolve the issues with frontend resources and public assets, and now includes an interactive menu when no Type parameter is provided.

## Issues Resolved

### 1. Frontend Resource Issues

- **Problem**: The original script wasn't using the latest frontend resources
- **Solution**: Integrated the complete build process from `win-installer-build.ps1` directly into `build-installer.ps1`
- **Details**:
  - Frontend is built first using `bun run build`
  - Qt application is then built with the latest frontend resources embedded
  - Proper Qt environment setup and Visual Studio integration

### 2. Public Assets Issues

- **Problem**: Images and other assets from the `public` directory weren't displaying correctly
- **Solution**: The integrated build process ensures all public assets are properly embedded through the QRC resource system
- **Details**:
  - CMakeLists.txt generates a QRC file that includes all files from `dist/spa`
  - Public assets are included in the frontend build and embedded in the Qt application

### 3. Interactive Menu Feature

- **Problem**: Users had to remember command-line parameters
- **Solution**: Added an interactive menu when no `-Type` parameter is provided
- **Features**:
  - Clear menu with numbered options
  - Support for all installer types (MSIX, InnoSetup, Portable, All)
  - Exit option
  - Input validation with retry on invalid choices

### 4. Directory Cleanup Issues (NEW FIX)

- **Problem**: Build process failed with "Access denied" errors when trying to remove Qt DLL files
- **Solution**: Implemented robust directory cleanup with multiple strategies
- **Features**:
  - Automatic process termination (InkCop, windeployqt, cmake, msbuild)
  - Multiple cleanup attempts with increasing delays
  - Read-only attribute removal
  - Fallback to directory renaming if removal fails
  - Comprehensive error handling and user feedback

## Changes Made

### 1. Modified build-installer.ps1

- Changed default `-Type` parameter from `"MSIX"` to `""` (empty string)
- Added interactive menu logic when Type is empty
- Integrated complete build process from `win-installer-build.ps1`:
  - Qt environment setup
  - Visual Studio environment loading
  - CMake configuration and build
  - Qt library deployment
  - ObjectBox DLL copying
- Updated both `Build-MSIXPackage` and `Build-PortablePackage` functions
- Added `Remove-DirectorySafely` function for robust directory cleanup
- Added automatic process termination before build starts

### 2. Removed win-installer-build.ps1

- No longer needed as functionality is integrated into main script
- Maintains single source of truth for build process

## Usage

### Interactive Mode (New)

```powershell
.\build-installer.ps1
```

This will show a menu with options:

1. MSIX Package (Windows Store compatible)
2. InnoSetup Installer (Traditional Windows installer)
3. Portable Package (ZIP archive, no installation)
4. All Types (Build all installer types)
5. Exit

### Command Line Mode (Existing)

```powershell
# Build specific type
.\build-installer.ps1 -Type MSIX
.\build-installer.ps1 -Type InnoSetup -Version 1.2.0
.\build-installer.ps1 -Type Portable
.\build-installer.ps1 -Type All

# With additional options
.\build-installer.ps1 -Type MSIX -SkipSigning
.\build-installer.ps1 -Type All -SkipBuild
```

## Build Process Flow

1. **Frontend Build**: `bun run build` creates latest frontend assets in `dist/spa`
2. **Qt Environment Setup**: Configures Qt 6.7.3 MSVC environment
3. **Visual Studio Setup**: Loads VS 2022 build tools
4. **CMake Configuration**: Configures build with proper paths
5. **Qt Application Build**: Compiles application with embedded frontend
6. **Qt Deployment**: Deploys required Qt libraries
7. **ObjectBox Integration**: Copies ObjectBox DLL
8. **Package Creation**: Creates the selected installer type(s)

## Benefits

- **Single Script**: One script handles all installer types and build processes
- **User Friendly**: Interactive menu for ease of use
- **Latest Resources**: Always uses the most recent frontend build
- **Proper Asset Handling**: All public assets are correctly embedded
- **Consistent Build**: Same build process for all installer types
- **Maintainable**: Single source of truth for build logic

## Testing

The script has been tested for:

- ✅ Syntax validation
- ✅ Help parameter functionality
- ✅ Interactive menu structure
- ✅ Directory cleanup scenarios
- ✅ Process termination logic
- ⏳ Full build process (requires Qt environment)

## Troubleshooting

### If you still encounter "Access denied" errors:

1. **Close all applications**: Make sure InkCop and any development tools are closed
2. **Run as Administrator**: Try running PowerShell as Administrator
3. **Check antivirus**: Some antivirus software may lock DLL files during scanning
4. **Manual cleanup**: If the script moves the directory instead of deleting it, manually delete the old directory after the build completes

### Common error scenarios handled:

- ✅ Qt DLL files locked by previous builds
- ✅ InkCop.exe still running from previous tests
- ✅ Build tools (cmake, msbuild) still running
- ✅ Read-only file attributes
- ✅ Windows file handle delays

## Next Steps

To fully test the build process:

1. Ensure Qt 6.7.3 MSVC is installed
2. Ensure Visual Studio 2022 is available
3. Run `.\build-installer.ps1` and select an installer type
4. Verify the generated packages work correctly
