<template>
  <div class="fit column gap-sm no-wrap q-pb-sm">
    <!-- 头部工具栏 -->
    <div class="gap-xs row q-pa-xs items-center transparent border-bottom">
      <q-btn flat dense size="0.7rem" icon="arrow_back" @click="$emit('go-back')" />
      <q-space />
      <span v-if="knowledgeBase">
        {{ knowledgeBase.name }}
        <q-chip
          v-if="processingDocuments.size > 0"
          color="primary"
          text-color="white"
          :label="`处理中 ${processingDocuments.size}`"
          size="sm"
          class="q-ml-sm"
        >
          <q-spinner-dots size="12px" class="q-mr-xs" />
        </q-chip>
      </span>
      <q-space />
      <!-- 操作按钮 -->
      <q-btn flat dense size="0.7rem" icon="add">
        <q-menu class="shadow-24">
          <q-list style="min-width: 100px; max-width: 240px" dense>
            <q-item clickable v-close-popup @click="$emit('create-document')">
              <q-item-section side top>
                <q-icon name="edit_note" />
              </q-item-section>
              <q-item-section>{{
                $t('src.components.KnowledgeBaseDetailView.create_document')
              }}</q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="goFileManager()">
              <q-item-section side top>
                <q-icon name="mdi-playlist-plus" />
              </q-item-section>
              <q-item-section>
                <q-item-label>
                  {{
                    $t('src.components.KnowledgeBaseDetailView.add_document_from_document_library')
                  }}
                </q-item-label>
                <q-item-label caption :lines="2" class="text-deep-orange">
                  {{
                    $t(
                      'src.components.KnowledgeBaseDetailView.add_document_from_document_library_tip',
                    )
                  }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
      <q-btn flat dense size="0.7rem" icon="mdi-dots-vertical">
        <q-menu class="shadow-24 radius-sm">
          <q-list dense>
            <q-item clickable v-close-popup @click="$emit('refresh')">
              <q-item-section side><q-icon name="mdi-refresh" size="18px" /></q-item-section>
              <q-item-section>{{
                $t('src.components.KnowledgeBaseDetailView.refresh')
              }}</q-item-section>
            </q-item>
            <q-separator />
            <q-item clickable v-close-popup @click="handleRevectorizeKnowledgeBase">
              <q-item-section side><q-icon name="mdi-replay" size="18px" /></q-item-section>
              <q-item-section>{{
                $t('src.components.KnowledgeBaseDetailView.revectorization_knowledge_base')
              }}</q-item-section>
            </q-item>
            <q-separator />
            <q-item clickable v-close-popup @click="$emit('settings')">
              <q-item-section side><q-icon name="mdi-cog" size="18px" /></q-item-section>
              <q-item-section>{{
                $t('src.components.KnowledgeBaseDetailView.settings')
              }}</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>

    <!-- 加载状态 -->
    <div v-if="detailLoading && !knowledgeBase" class="text-center q-pa-xs q-space">
      <q-spinner size="40px" color="primary" />
      <div class="q-mt-md">{{ $t('src.components.KnowledgeBaseDetailView.loading') }}</div>
    </div>
    <q-list v-else-if="knowledgeBase" class="q-px-sm">
      <q-item>
        <q-item-section>
          <q-item-label>
            <div class="row items-center gap-md">
              <q-icon name="storage" size="lg" />
              <div class="text-h6">{{ knowledgeBase.name }}</div>
              <div class="row items-center"></div>
            </div>
          </q-item-label>
          <q-item-label>
            <q-chip color="primary" dense square clickable text-color="white" icon="description">
              {{ knowledgeBase.document_count }}
              {{ $t('src.components.KnowledgeBaseDetailView.documents') }}
            </q-chip>
            <q-chip
              v-if="stats"
              dense
              clickable
              square
              color="secondary"
              text-color="white"
              icon="mdi-blur"
            >
              <template
                v-if="
                  stats.total_chunks_count && stats.total_chunks_count > stats.vectorized_chunks
                "
              >
                {{ stats.vectorized_chunks || 0 }} / {{ stats.total_chunks_count || 0 }}
              </template>
              <template v-else>
                {{ stats.total_chunks_count || 0 }}
              </template>
              {{ $t('src.components.KnowledgeBaseDetailView.knowledge_chunks') }}
            </q-chip>
            <q-chip
              v-if="stats?.total_characters"
              dense
              square
              color="accent"
              text-color="white"
              icon="text_fields"
            >
              {{ formatSize(stats.total_characters) }}
            </q-chip>
          </q-item-label>
          <q-item-label caption class="q-px-sm">
            <span v-if="knowledgeBase.description">{{ knowledgeBase.description }}</span>
            <span v-if="knowledgeBase.created_at" class="q-ml-xs"
              >{{ $t('src.components.KnowledgeBaseDetailView.created_time') }}:
              {{ formatDate(knowledgeBase.created_at) }}</span
            >
          </q-item-label>
        </q-item-section>
      </q-item>
      <q-separator spaced inset class="op-5" />
      <q-item-label header>{{
        $t('src.components.KnowledgeBaseDetailView.knowledge_base_documents')
      }}</q-item-label>
      <!-- 空状态 -->
      <div v-if="knowledgeDocuments.length === 0 && !detailLoading" class="text-center q-pa-xl">
        <q-icon name="description" size="60px" color="grey-5" />
        <div class="text-h6 text-grey-6 q-mt-md">
          {{ $t('src.components.KnowledgeBaseDetailView.no_documents') }}
        </div>
        <div class="text-body2 text-grey-5">
          {{ $t('src.components.KnowledgeBaseDetailView.click_add_document') }}
        </div>
      </div>
      <template v-else>
        <q-item
          v-for="doc in knowledgeDocuments"
          :key="doc.id"
          clickable
          @click="$emit('open-document', doc.id)"
          class="q-pa-md radius-xs hover-item"
          :data-doc-id="doc.id"
          :data-completed-time="recentlyCompletedDocs.has(doc.id) ? Date.now() : undefined"
        >
          <q-item-section side top>
            <!-- 准备处理状态 -->
            <template v-if="preparingDocuments.has(doc.id)">
              <q-spinner-dots color="primary" size="36px" class="text-primary">
                <q-tooltip>{{
                  $t('src.components.KnowledgeBaseDetailView.preparing_document')
                }}</q-tooltip>
              </q-spinner-dots>
            </template>
            <!-- 向量化进度显示 -->
            <template v-else-if="vectorizationProgress.has(doc.id)">
              <q-circular-progress
                show-value
                font-size="8px"
                class="text-positive"
                :value="getVectorizationProgressPercent(doc.id)"
                size="36px"
                :thickness="0.05"
                color="positive"
                track-color="grey"
              >
                <span> {{ getVectorizationProgressPercent(doc.id).toFixed(1) }}% </span>
              </q-circular-progress>
            </template>
            <!-- 最近完成状态 -->
            <q-icon
              v-else-if="recentlyCompletedDocs.has(doc.id)"
              name="mdi-checkbox-marked-circle"
              color="positive"
              size="40px"
            />
            <!-- 默认文档图标 -->
            <q-avatar
              v-else
              :color="getDocumentColor(doc.file_type)"
              text-color="white"
              :icon="getDocumentIcon(doc.file_type)"
              size="40px"
            >
              <!-- 重新向量化状态指示器 -->
              <q-badge v-if="needsRevectorization(doc.id)" floating color="warning" rounded>
                <q-icon name="mdi-refresh" size="12px" />
                <q-tooltip class="bg-warning text-black" :delay="500">
                  <div class="text-weight-bold">需要重新向量化</div>
                </q-tooltip>
              </q-badge>
            </q-avatar>
          </q-item-section>

          <q-item-section>
            <q-item-label class="text-weight-bold">{{ doc.title }}</q-item-label>
            <q-item-label caption>
              <q-chip
                v-if="doc.file_type"
                :color="getDocumentColor(doc.file_type)"
                text-color="white"
                size="sm"
                dense
                square
                :label="doc.file_type"
              />
              <span v-else class="text-grey-6">{{
                $t('src.components.KnowledgeBaseDetailView.editor_document')
              }}</span>
            </q-item-label>
            <q-item-label caption>
              <span v-if="processingDocuments.has(doc.id)">
                {{ $t('src.components.KnowledgeBaseDetailView.processing_vectorization') }}
                {{ vectorizationProgress.get(doc.id)?.completed }} /
                {{ vectorizationProgress.get(doc.id)?.total }} ...
              </span>
              <span v-else-if="recentlyCompletedDocs.has(doc.id)">
                {{ $t('src.components.KnowledgeBaseDetailView.vectorization_completed') }} ✓ |
                {{ $t('src.components.KnowledgeBaseDetailView.chunk_count') }}:
                {{ getDocumentChunkCount(doc.id) }} |
                {{ $t('src.components.KnowledgeBaseDetailView.add_time') }}:
                {{ formatDate(doc.created_at) }}
              </span>
              <span v-else class="row items-center gap-xs">
                <q-icon name="mdi-blur" />
                <template
                  v-if="
                    doc.total_chunk_count &&
                    doc.total_chunk_count > (doc.vectorized_chunk_count || doc.chunk_count || 0)
                  "
                >
                  {{ doc.vectorized_chunk_count || doc.chunk_count || 0 }} /
                  {{ doc.total_chunk_count }} |
                </template>
                <template v-else> {{ doc.chunk_count || 0 }} | </template>
                <q-icon name="mdi-timer" />{{ formatDate(doc.created_at) }}
              </span>
            </q-item-label>
          </q-item-section>

          <q-item-section side top class="hover-show-item">
            <div class="row q-gutter-sm">
              <q-btn flat dense round size="sm" icon="mdi-dots-vertical" @click.stop>
                <q-menu>
                  <q-list style="min-width: 100px" dense>
                    <q-item
                      clickable
                      v-close-popup
                      :class="needsRevectorization(doc.id) ? 'text-deep-orange' : void 0"
                      @click.stop="handleRevectorizeDocument(doc)"
                    >
                      <q-item-section side
                        ><q-icon
                          name="mdi-replay"
                          :color="needsRevectorization(doc.id) ? 'deep-orange' : void 0"
                      /></q-item-section>
                      <q-item-section>{{
                        $t('src.components.KnowledgeBaseDetailView.revectorization')
                      }}</q-item-section>
                    </q-item>
                    <q-item
                      v-if="needsRevectorization(doc.id)"
                      clickable
                      v-close-popup
                      @click.stop="ignoreRevectorize(doc)"
                    >
                      <q-item-section side><q-icon name="mdi-priority-low" /></q-item-section>
                      <q-item-section>{{
                        $t('src.components.KnowledgeBaseDetailView.ignoreRevectorize')
                      }}</q-item-section>
                    </q-item>
                    <q-separator class="op-5" />
                    <q-item clickable v-close-popup @click.stop="$emit('delete-document', doc)">
                      <q-item-section side><q-icon name="remove" /></q-item-section>
                      <q-item-section>{{
                        $t('src.components.KnowledgeBaseDetailView.remove')
                      }}</q-item-section>
                      <q-tooltip>{{
                        $t('src.components.KnowledgeBaseDetailView.delete_document')
                      }}</q-tooltip>
                    </q-item>
                  </q-list>
                </q-menu>
              </q-btn>
            </div>
          </q-item-section>
        </q-item>
      </template>
    </q-list>

    <!-- 错误状态 -->
    <div v-else class="text-center q-pa-xl">
      <q-icon name="error" size="60px" color="negative" />
      <div class="text-h6 text-negative q-mt-md">
        {{ $t('src.components.KnowledgeBaseDetailView.load_failed') }}
      </div>
      <div class="text-body2 text-grey-6">
        {{ $t('src.components.KnowledgeBaseDetailView.cannot_load_knowledge_base_information') }}
      </div>
      <q-btn color="primary" class="q-mt-md" @click="$emit('refresh')">{{
        $t('src.components.KnowledgeBaseDetailView.retry')
      }}</q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import type { KnowledgeBase, KnowledgeDocument } from '../env';
import type { KnowledgeBaseStats } from '../composeables/useKnowledge';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { uiStore } from 'src/composeables/useStore';
import { useDocumentChunking } from 'src/composeables/useDocumentChunking';
import { useUiStore } from '../stores/ui';
import type { ChunkingMethod, ChunkingConfig, KnowledgeBaseSettings } from '../types/qwen';
import {
  batchCheckKnowledgeDocumentsRevectorization,
  type RevectorizationCheckResult,
} from '../utils/knowledgeDocumentRevectorization';

const router = useRouter();

// Composables
const $q = useQuasar();
const { t: $t } = useI18n();
const documentChunking = useDocumentChunking();
const store = useUiStore();

// 本地状态管理
const preparingDocuments = ref(new Set<number>());

// 重新向量化状态管理
const revectorizationStatus = ref(new Map<number, RevectorizationCheckResult>());
const loadingRevectorizationStatus = ref(false);

// Props
const props = defineProps<{
  knowledgeBase: KnowledgeBase;
  knowledgeDocuments: KnowledgeDocument[];
  stats: KnowledgeBaseStats | null;
  detailLoading: boolean;
  processingDocuments: Set<number>;
  recentlyCompletedDocs: Set<number>;
  vectorizationProgress: Map<number, { completed: number; total: number }>;
}>();

// Emits
const $emit = defineEmits<{
  'go-back': [];
  'create-document': [];
  refresh: [];
  settings: [];
  'open-document': [docId: number | string | null | undefined];
  'delete-document': [doc: KnowledgeDocument];
}>();

const goFileManager = async () => {
  uiStore.app = 'inkcop';
  await router.push('/doc');
  $q.notify({
    type: 'positive',
    position: 'top',
    message: $t('src.components.KnowledgeBaseDetailView.add_document_from_document_library_tip'),
    timeout: 3000,
  });
};

// 方法
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN');
};

const formatSize = (size: number): string => {
  if (size < 1024) return `${size}B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}K`;
  if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)}M`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(1)}G`;
};

const getDocumentIcon = (fileType?: string): string => {
  if (!fileType) return 'description';
  const type = fileType.toLowerCase();
  switch (type) {
    case 'pdf':
      return 'picture_as_pdf';
    case 'doc':
    case 'docx':
      return 'description';
    case 'txt':
      return 'text_snippet';
    case 'md':
    case 'markdown':
      return 'mdi-file-document';
    case 'html':
      return 'language';
    case 'json':
      return 'data_object';
    case 'xml':
      return 'code';
    case 'csv':
      return 'table_chart';
    case 'rtf':
      return 'text_fields';
    default:
      return 'description';
  }
};

const getDocumentColor = (fileType?: string): string => {
  if (!fileType) return 'blue';
  const type = fileType.toLowerCase();
  switch (type) {
    case 'pdf':
      return 'red';
    case 'doc':
    case 'docx':
      return 'blue';
    case 'txt':
      return 'grey';
    case 'md':
    case 'markdown':
      return 'purple';
    case 'html':
      return 'orange';
    case 'json':
      return 'green';
    case 'xml':
      return 'teal';
    case 'csv':
      return 'cyan';
    case 'rtf':
      return 'brown';
    default:
      return 'blue';
  }
};

// 获取文档的chunk_count，优先使用数据库中的最新数据
const getDocumentChunkCount = (docId: number): number => {
  const doc = props.knowledgeDocuments.find((d) => d.id === docId);
  return doc?.chunk_count || 0;
};

// 获取向量化进度百分比
const getVectorizationProgressPercent = (docId: number): number => {
  const progress = props.vectorizationProgress.get(docId);
  if (!progress || progress.total === 0) return 0;
  return (progress.completed / progress.total) * 100;
};

/**
 * 获取知识库配置
 * 优先使用知识库自身的配置，如果没有则使用全局配置
 */
const getKnowledgeBaseConfig = (
  knowledgeBase: KnowledgeBase | null,
): { chunkingMethod: ChunkingMethod; chunkingConfig: ChunkingConfig } => {
  let kbSettings: Partial<KnowledgeBaseSettings> = {};

  // 1. 尝试解析知识库自身的配置
  if (knowledgeBase?.settings) {
    try {
      const parsedSettings = JSON.parse(knowledgeBase.settings);
      if (parsedSettings && typeof parsedSettings === 'object') {
        kbSettings = parsedSettings;
      }
    } catch (error) {
      console.warn('⚠️ [知识库配置] 解析知识库配置失败，使用全局配置:', error);
    }
  }

  // 2. 如果知识库没有配置，使用全局配置
  if (Object.keys(kbSettings).length === 0) {
    kbSettings = store.perferences?.knowledgeBase || {};
  }

  // 3. 构建切割方法和配置
  const chunkingMethod: ChunkingMethod = kbSettings.defaultChunkingMethod || 'markdown';
  const chunkingConfig: ChunkingConfig = {
    chunkSize: kbSettings.chunkSize || 800,
    chunkOverlap: kbSettings.chunkOverlap || 200,
    semanticThreshold: kbSettings.semanticThreshold || 0.7,
    enableMultiSemantic: kbSettings.enableMultiSemanticSplit !== false,
    enableHierarchical: kbSettings.enableHierarchicalSplit !== false,
  };

  return { chunkingMethod, chunkingConfig };
};

/**
 * 处理重新向量化整个知识库
 */
const handleRevectorizeKnowledgeBase = () => {
  if (!props.knowledgeBase || !props.knowledgeDocuments) {
    console.warn('⚠️ [知识库重新向量化] 知识库或文档数据不可用');
    return;
  }

  // 显示确认对话框
  $q.dialog({
    title: $t('src.components.KnowledgeBaseDetailView.revectorization_knowledge_base'),
    message: $t('src.components.KnowledgeBaseDetailView.revectorize_knowledge_base_confirm', {
      name: props.knowledgeBase.name,
      count: props.knowledgeDocuments.length,
    }),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // 显示开始处理的通知
    $q.notify({
      type: 'info',
      message: $t('src.components.KnowledgeBaseDetailView.revectorize_knowledge_base_started', {
        name: props.knowledgeBase?.name,
        count: props.knowledgeDocuments?.length,
      }),
      position: 'top',
    });

    // 获取知识库配置
    const { chunkingMethod, chunkingConfig } = getKnowledgeBaseConfig(props.knowledgeBase);

    // 逐个处理所有文档
    void (async () => {
      let processedCount = 0;
      let errorCount = 0;

      for (const doc of props.knowledgeDocuments || []) {
        try {
          // 添加到准备处理状态
          preparingDocuments.value.add(doc.id);

          await documentChunking.revectorizeKnowledgeDocument(
            doc.id,
            chunkingMethod,
            chunkingConfig,
            {
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              onProgress: (docId: number, progress: { stage: string; percentage: number }) => {
                // 第一次收到进度时，移除准备状态
                if (preparingDocuments.value.has(docId)) {
                  preparingDocuments.value.delete(docId);
                }
              },
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              onVectorizationCompleted: (docId: number, chunkCount: number) => {
                // 确保移除准备状态
                preparingDocuments.value.delete(docId);
              },
              onError: (docId: number, error: string, stage: 'chunking' | 'vectorization') => {
                console.error(`❌ [知识库重新向量化] 文档 ${doc.title} 失败:`, { error, stage });

                // 确保移除准备状态
                preparingDocuments.value.delete(docId);
                errorCount++;
              },
            },
          );

          processedCount++;
        } catch (error) {
          console.error(`❌ [知识库重新向量化] 文档 ${doc.title} 处理失败:`, error);

          // 确保移除准备状态
          preparingDocuments.value.delete(doc.id);
          errorCount++;
        }
      }

      // 显示完成通知
      $q.notify({
        type: errorCount === 0 ? 'positive' : 'warning',
        message: $t('src.components.KnowledgeBaseDetailView.revectorize_knowledge_base_completed', {
          name: props.knowledgeBase?.name,
          processed: processedCount,
          total: props.knowledgeDocuments?.length,
          errors: errorCount,
        }),
        position: 'top',
        timeout: 8000,
      });

      // 刷新知识库数据
      $emit('refresh');
    })();
  });
};

/**
 * 处理重新向量化文档
 */
const handleRevectorizeDocument = (doc: KnowledgeDocument) => {
  // 显示确认对话框
  $q.dialog({
    title: $t('src.components.KnowledgeBaseDetailView.revectorization'),
    message: $t('src.components.KnowledgeBaseDetailView.revectorize_confirm', {
      title: doc.title,
    }),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // 显示开始处理的通知
    $q.notify({
      type: 'info',
      message: $t('src.components.KnowledgeBaseDetailView.revectorize_started', {
        title: doc.title,
      }),
      position: 'top',
    });

    // 使用前端方法进行重新向量化
    void (async () => {
      try {
        // 添加到准备处理状态
        preparingDocuments.value.add(doc.id);

        // 首先检查是否有关联的原始文档
        const { useSqlite } = await import('src/composeables/useSqlite');
        const sqlite = useSqlite();

        try {
          const originalDocResult = await sqlite.getOriginalDocumentByKnowledgeDocumentId(doc.id);

          if (originalDocResult.success && originalDocResult.content) {
            // 解析原始文档内容并转换为Markdown
            const { convertTiptapToMarkdown } = await import('src/utils/tiptap');
            let markdownContent = '';

            if (originalDocResult.content) {
              try {
                const jsonContent =
                  typeof originalDocResult.content === 'string'
                    ? JSON.parse(originalDocResult.content)
                    : originalDocResult.content;
                markdownContent = convertTiptapToMarkdown(jsonContent);
              } catch (parseError) {
                console.error('❌ [知识库详情] 解析原始文档内容失败:', parseError);
                markdownContent = originalDocResult.content; // 直接使用原始内容
              }
            }

            // 更新知识库文档内容，然后使用前端切片逻辑重新向量化
            if (markdownContent.trim()) {
              const { useKnowledge } = await import('src/composeables/useKnowledge');
              const knowledge = useKnowledge();

              // 只更新文档内容，不触发Qt后端的向量化（现在updateKnowledgeDoc不会触发向量化）
              await knowledge.updateKnowledgeDoc(
                doc.id,
                originalDocResult.title || doc.title, // 使用原始文档标题或保持原标题
                markdownContent,
                'upload', // sourceType
                '', // filePath
                '', // fileType
                false, // updateVector - 这个参数现在被忽略，Qt后端不会触发向量化
                'markdown', // documentType
              );
            }
          }
        } catch (error) {
          console.warn('⚠️ [知识库详情] 检查原始文档失败，继续使用知识库文档自身内容:', error);
        }

        // 获取知识库配置
        const { chunkingMethod, chunkingConfig } = getKnowledgeBaseConfig(props.knowledgeBase);

        await documentChunking.revectorizeKnowledgeDocument(
          doc.id,
          chunkingMethod,
          chunkingConfig,
          {
            onProgress: (docId: number, progress: { stage: string; percentage: number }) => {
              // 第一次收到进度时，移除准备状态
              if (preparingDocuments.value.has(docId)) {
                preparingDocuments.value.delete(docId);
              }

              // 如果是删除完成阶段，立即刷新统计数据
              if (progress.stage.includes('删除完成')) {
                $emit('refresh');
              }
            },
            onChunkingCompleted: (docId, result) => {
              $q.notify({
                type: 'positive',
                message: $t('src.components.KnowledgeBaseDetailView.chunking_completed', {
                  title: doc.title,
                  chunks: result.chunkCount,
                }),
                position: 'top',
              });
            },
            onVectorizationCompleted: (docId, chunkCount) => {
              // 确保移除准备状态
              preparingDocuments.value.delete(docId);

              $q.notify({
                type: 'positive',
                message: `文档"${doc.title}"重新向量化完成，共处理 ${chunkCount} 个文本块`,
                position: 'top',
                timeout: 5000,
              });

              // 刷新知识库数据和重新向量化状态
              $emit('refresh');

              // 重新加载重新向量化状态
              void loadRevectorizationStatus();
            },
            onError: (docId, error, stage) => {
              console.error(`❌ [知识库详情] 重新向量化失败:`, { docId, error, stage });

              // 确保移除准备状态
              preparingDocuments.value.delete(docId);

              $q.notify({
                type: 'negative',
                message: $t('src.components.KnowledgeBaseDetailView.revectorize_error', {
                  error,
                }),
                position: 'top',
              });
            },
          },
        );
      } catch (error) {
        console.error('❌ [知识库详情] 启动重新向量化失败:', error);

        // 确保移除准备状态
        preparingDocuments.value.delete(doc.id);

        $q.notify({
          type: 'negative',
          message: $t('src.components.KnowledgeBaseDetailView.revectorize_error', {
            error: error instanceof Error ? error.message : '未知错误',
          }),
          position: 'top',
        });
      }
    })();
  });
};

const ignoreRevectorize = async (doc: KnowledgeDocument) => {
  try {
    // 显示确认对话框
    const confirmed = await new Promise<boolean>((resolve) => {
      $q.dialog({
        title: $t('src.components.KnowledgeBaseDetailView.ignoreRevectorizeConfirm.title'),
        message: $t('src.components.KnowledgeBaseDetailView.ignoreRevectorizeConfirm.message', {
          title: doc.title,
        }),
        cancel: true,
        persistent: true,
      })
        .onOk(() => resolve(true))
        .onCancel(() => resolve(false));
    });

    if (!confirmed) {
      return;
    }

    // 调用知识库API更新文档的更新时间
    const { useKnowledge } = await import('src/composeables/useKnowledge');
    const knowledge = useKnowledge();

    // 更新知识库文档的更新时间到当前时间
    // 通过调用updateKnowledgeDoc并传递相同的内容来更新时间戳
    await knowledge.updateKnowledgeDoc(
      doc.id,
      doc.title, // 保持原标题
      doc.content, // 保持原内容
      'updated', // sourceType
      '', // filePath
      '', // fileType
      false, // updateVector - 不触发向量化
      'markdown', // 使用默认文档类型
    );

    $q.notify({
      type: 'positive',
      message: $t('src.components.KnowledgeBaseDetailView.ignoreRevectorizeSuccess', {
        title: doc.title,
      }),
      position: 'top',
      timeout: 3000,
    });

    // 刷新知识库数据和重新向量化状态
    $emit('refresh');

    // 重新加载重新向量化状态
    void loadRevectorizationStatus();
  } catch (error) {
    console.error('❌ [知识库详情] 忽略重新向量化失败:', error);

    $q.notify({
      type: 'negative',
      message: $t('src.components.KnowledgeBaseDetailView.ignoreRevectorizeError', {
        error: error instanceof Error ? error.message : '未知错误',
      }),
      position: 'top',
    });
  }
};

/**
 * 加载知识库文档的重新向量化状态
 */
const loadRevectorizationStatus = async () => {
  if (!props.knowledgeDocuments || props.knowledgeDocuments.length === 0) {
    return;
  }

  loadingRevectorizationStatus.value = true;
  try {
    // API调用检查重新向量化状态
    const documentIds = props.knowledgeDocuments.map((doc) => doc.id);
    const results = await batchCheckKnowledgeDocumentsRevectorization(
      documentIds,
      props.knowledgeDocuments,
    );

    revectorizationStatus.value = results;
  } catch (error) {
    console.error('❌ [知识库详情] 加载重新向量化状态失败:', error);

    // 显示错误通知
    $q.notify({
      type: 'negative',
      message: '检查重新向量化状态失败',
      caption: error instanceof Error ? error.message : '未知错误',
      position: 'top',
      timeout: 3000,
    });

    // 清空状态以避免显示错误的信息
    revectorizationStatus.value.clear();
  } finally {
    loadingRevectorizationStatus.value = false;
  }
};

/**
 * 检查文档是否需要重新向量化
 */
const needsRevectorization = computed(() => (docId: number) => {
  const status = revectorizationStatus.value.get(docId);
  return status?.needsRevectorization || false;
});

// 监听知识库文档变化，当有数据时加载重新向量化状态
watch(
  () => props.knowledgeDocuments,
  (newDocuments) => {
    if (newDocuments && newDocuments.length > 0) {
      setTimeout(() => {
        void loadRevectorizationStatus();
      }, 100);
    }
  },
  { immediate: true }, // 立即执行一次，检查初始值
);
</script>

<style scoped>
.hover-item:hover .hover-show-item {
  opacity: 1;
}

.hover-show-item {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.hover-item:hover {
  background-color: var(--q-primary-lighten-4);
}
</style>
