<template>
  <div class="autocomplete-settings q-pa-xl">
    <div class="text-h6 q-mb-xl">{{ $t('src.components.settings.AutoCompleteSetting.title') }}</div>

    <!-- 启用状态控制 -->
    <div class="q-mb-md">
      <q-toggle
        :model-value="settings.enabled"
        :disable="!store.hasEnabledLlmProviders"
        :label="$t('src.components.settings.AutoCompleteSetting.enableAutoComplete')"
        @update:model-value="updateSettings({ enabled: $event })"
      />
      <div class="text-caption text-grey-6 q-mt-xs">
        <span v-if="!store.hasEnabledLlmProviders" class="text-warning">
          {{ $t('src.components.settings.AutoCompleteSetting.needLlmProvider') }}
        </span>
        <span v-else>{{ $t('src.components.settings.AutoCompleteSetting.enableHint') }}</span>
      </div>
    </div>

    <q-form v-if="settings.enabled" @submit.prevent class="q-gutter-sm">
      <!-- 供应商选择 -->
      <q-select
        v-model="selectedProvider"
        :options="availableProviders"
        :label="$t('src.components.settings.AutoCompleteSetting.selectProvider')"
        outlined
        emit-value
        map-options
        class="q-mb-md"
        :rules="[(val) => !!val || $t('src.components.settings.common.selectRequired')]"
        :hint="$t('src.components.settings.AutoCompleteSetting.providerHint')"
        @update:model-value="onProviderChange"
      >
        <template v-slot:prepend>
          <q-icon name="business" />
        </template>
      </q-select>

      <!-- 模型选择 -->
      <q-select
        v-model="selectedModel"
        :options="availableModels"
        :label="$t('src.components.settings.AutoCompleteSetting.selectModel')"
        outlined
        emit-value
        map-options
        class="q-mb-md"
        :disable="!selectedProvider"
        :rules="[(val) => !!val || $t('src.components.settings.common.selectRequired')]"
        :hint="$t('src.components.settings.AutoCompleteSetting.modelHint')"
        @update:model-value="onModelChange"
      >
        <template v-slot:prepend>
          <q-icon name="psychology" />
        </template>
      </q-select>

      <div v-if="selectedProvider && selectedModel" class="text-caption text-grey-6 q-mb-md">
        {{
          $t('src.components.settings.AutoCompleteSetting.selectedInfo', {
            provider: selectedProvider,
            model: selectedModel,
          })
        }}
        <br />
        {{ $t('src.components.settings.AutoCompleteSetting.apiConfigHint') }}
      </div>

      <!-- 提示词输入 -->
      <q-input
        v-model="settings.body.prompt"
        type="textarea"
        :label="$t('src.components.settings.AutoCompleteSetting.globalPrompt')"
        outlined
        rows="3"
        class="q-mb-md"
        :hint="$t('src.components.settings.AutoCompleteSetting.promptHint')"
        @update:model-value="updateSettings({ body: { ...settings.body, prompt: String($event) } })"
      >
        <template v-slot:prepend>
          <q-icon name="edit_note" />
        </template>
      </q-input>

      <!-- 最大 Token 数设置 -->
      <q-input
        v-model.number="settings.body.maxTokens"
        type="number"
        :label="$t('src.components.settings.AutoCompleteSetting.maxTokens')"
        outlined
        class="q-mb-md"
        :rules="[(val) => val > 0 || $t('src.components.settings.common.invalidNumber')]"
        :hint="$t('src.components.settings.AutoCompleteSetting.maxTokensHint')"
        @update:model-value="
          updateSettings({ body: { ...settings.body, maxTokens: Number($event) } })
        "
      >
        <template v-slot:prepend>
          <q-icon name="format_list_numbered" />
        </template>
      </q-input>

      <!-- 温度设置 -->
      <q-item class="q-pa-none q-mb-md">
        <q-item-section avatar>
          <q-icon name="thermostat" />
        </q-item-section>
        <q-item-section>
          <q-slider
            v-model="settings.body.temperature"
            :min="-2"
            :max="2"
            :step="0.1"
            label
            :label-always="true"
            :label-value="
              $t('src.components.settings.AutoCompleteSetting.temperature') +
              ': ' +
              settings.body.temperature
            "
            track-size="2px"
            color="grey"
            label-color="primary"
            thumb-color="primary"
            :hint="$t('src.components.settings.AutoCompleteSetting.temperatureHint')"
            @update:model-value="
              updateSettings({ body: { ...settings.body, temperature: Number($event) } })
            "
          />
        </q-item-section>
      </q-item>

      <!-- Frequency Penalty 设置 -->
      <q-item class="q-pa-none q-mb-md">
        <q-item-section avatar>
          <q-icon name="tune" />
        </q-item-section>
        <q-item-section>
          <q-slider
            v-model="settings.body.frequency_penalty"
            :min="-2"
            :max="2"
            :step="0.1"
            label
            :label-always="true"
            :label-value="
              $t('src.components.settings.AutoCompleteSetting.frequencyPenalty') +
              ': ' +
              settings.body.frequency_penalty
            "
            track-size="2px"
            color="grey"
            label-color="primary"
            thumb-color="primary"
            @update:model-value="
              updateSettings({ body: { ...settings.body, frequency_penalty: Number($event) } })
            "
          />
          <span class="text-grey">{{
            $t('src.components.settings.AutoCompleteSetting.frequencyPenaltyHint')
          }}</span>
        </q-item-section>
      </q-item>

      <!-- Presence Penalty 设置 -->
      <q-item class="q-pa-none q-mb-md">
        <q-item-section avatar>
          <q-icon name="psychology_alt" />
        </q-item-section>
        <q-item-section>
          <q-slider
            v-model="settings.body.presence_penalty"
            :min="0"
            :max="2"
            :step="0.1"
            label
            :label-always="true"
            :label-value="
              $t('src.components.settings.AutoCompleteSetting.presencePenalty') +
              ': ' +
              settings.body.presence_penalty
            "
            track-size="2px"
            color="grey"
            label-color="primary"
            thumb-color="primary"
            @update:model-value="
              updateSettings({ body: { ...settings.body, presence_penalty: Number($event) } })
            "
          />
          <span class="text-grey">{{
            $t('src.components.settings.AutoCompleteSetting.presencePenaltyHint')
          }}</span>
        </q-item-section>
      </q-item>

      <!-- 分隔線 -->
      <q-separator class="q-my-md" />

      <!-- 自動補全高級設置標題 -->
      <div class="text-subtitle1 q-mb-md">
        {{ $t('src.components.settings.AutoCompleteSetting.advancedSettings') }}
      </div>

      <!-- 觸發長度設置 -->
      <q-input
        v-model.number="settings.autoCompleteTriggerLength"
        type="number"
        :label="$t('src.components.settings.AutoCompleteSetting.triggerLength')"
        outlined
        class="q-mb-md"
        :rules="[(val) => val > 0 || $t('src.components.settings.common.invalidNumber')]"
        :hint="$t('src.components.settings.AutoCompleteSetting.triggerLengthHint')"
        @update:model-value="updateSettings({ autoCompleteTriggerLength: Number($event) })"
      >
        <template v-slot:prepend>
          <q-icon name="text_fields" />
        </template>
      </q-input>

      <!-- 最大建議數量設置 暫時關閉，自動完成只需要一個建議 -->
      <q-input
        v-if="false"
        v-model.number="settings.autoCompleteMaxSuggestions"
        type="number"
        :label="$t('src.components.settings.AutoCompleteSetting.maxSuggestions')"
        outlined
        class="q-mb-md"
        :rules="[(val) => val > 0 || $t('src.components.settings.common.invalidNumber')]"
        :hint="$t('src.components.settings.AutoCompleteSetting.maxSuggestionsHint')"
        @update:model-value="updateSettings({ autoCompleteMaxSuggestions: Number($event) })"
      >
        <template v-slot:prepend>
          <q-icon name="list" />
        </template>
      </q-input>

      <!-- 防抖時間設置 -->
      <q-input
        v-model.number="settings.autoCompleteDebounceTime"
        type="number"
        :label="$t('src.components.settings.AutoCompleteSetting.debounceTime')"
        outlined
        class="q-mb-md"
        :rules="[(val) => val > 0 || $t('src.components.settings.common.invalidNumber')]"
        :hint="$t('src.components.settings.AutoCompleteSetting.debounceTimeHint')"
        @update:model-value="updateSettings({ autoCompleteDebounceTime: Number($event) })"
      >
        <template v-slot:prepend>
          <q-icon name="timer" />
        </template>
        <template v-slot:append>
          <q-icon name="timer" class="cursor-pointer">
            <q-tooltip>{{
              $t('src.components.settings.AutoCompleteSetting.milliseconds')
            }}</q-tooltip>
          </q-icon>
        </template>
      </q-input>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { useI18n } from 'vue-i18n';
import type { AutoComplete } from 'src/env.d';
import { DEFAULT_AUTOCOMPLETE_SETTINGS } from 'src/config/defaultSettings';
import { useSqlite } from 'src/composeables/useSqlite';
import {
  AUTOCOMPLETE_MODEL_TYPES,
  hasModelsOfType,
  getModelsByType,
  type CategorizedModels,
} from 'src/types/modelCategories';

const { t: $t } = useI18n({ useScope: 'global' });
const store = useUiStore();
const { setAppSettings } = useSqlite();

// 选择的供应商和模型
const selectedProvider = ref('');
const selectedModel = ref('');

// 从 store 获取设置
const settings = computed(() => store.perferences?.autoComplete || DEFAULT_AUTOCOMPLETE_SETTINGS);

// 获取有文本生成、推理模型的供应商
const availableProviders = computed(() => {
  const providers: Array<{ label: string; value: string }> = [];

  const llmSettings = store.perferences?.llm;
  if (!llmSettings) return providers;

  Object.entries(llmSettings).forEach(([providerKey, providerSettings]) => {
    if (
      providerSettings &&
      typeof providerSettings === 'object' &&
      'enabled' in providerSettings &&
      'avaliableModels' in providerSettings &&
      providerSettings.enabled &&
      providerSettings.avaliableModels
    ) {
      const categorizedModels = providerSettings.avaliableModels as CategorizedModels;

      // 检查是否有自动补全需要的模型类型
      if (hasModelsOfType(categorizedModels, AUTOCOMPLETE_MODEL_TYPES)) {
        providers.push({
          label: providerKey,
          value: providerKey,
        });
      }
    }
  });

  return providers;
});

// 获取选中供应商的自动补全模型
const availableModels = computed(() => {
  if (!selectedProvider.value) return [];

  const llmSettings = store.perferences?.llm;
  const providerSettings = llmSettings?.[selectedProvider.value as keyof typeof llmSettings];

  if (
    providerSettings &&
    typeof providerSettings === 'object' &&
    'avaliableModels' in providerSettings &&
    providerSettings.avaliableModels
  ) {
    const categorizedModels = providerSettings.avaliableModels as CategorizedModels;
    const autocompleteModels = getModelsByType(categorizedModels, AUTOCOMPLETE_MODEL_TYPES);
    const equalModels = Array.from(new Set(autocompleteModels));

    return equalModels.map((model) => ({
      label: model,
      value: model,
    }));
  }

  return [];
});

// 处理供应商变化
const onProviderChange = (provider: string) => {
  selectedProvider.value = provider;
  selectedModel.value = ''; // 重置模型选择

  // 自动选择第一个可用模型
  const models = availableModels.value;
  if (models.length > 0) {
    selectedModel.value = models[0].value;
    onModelChange(models[0].value);
  }
};

// 处理模型变化
const onModelChange = (model: string) => {
  selectedModel.value = model;

  // 从LLM设置中获取配置信息
  const llmSettings = store.perferences?.llm;
  const providerSettings = llmSettings?.[selectedProvider.value as keyof typeof llmSettings];

  if (
    providerSettings &&
    typeof providerSettings === 'object' &&
    'baseUrl' in providerSettings &&
    'apiKey' in providerSettings
  ) {
    const baseUrl = providerSettings.baseUrl;
    const apiKey = providerSettings.apiKey;

    // 更新自动补全设置
    updateSettings({
      body: {
        ...settings.value.body,
        model: model,
      },
      base_url: baseUrl,
      api_key: apiKey,
    });
  }
};
// 更新设置方法
const updateSettings = (newSettings: Partial<AutoComplete>) => {
  // console.log('[AutoCompleteSetting] 更新AutoComplete设置:', newSettings);

  // 更新autoComplete设置到UI Store
  store.updateAutoCompleteSettings(newSettings);

  // 同步更新数据库中的应用设置
  void saveAppSettingsToDatabase();
};
// 保存应用设置到数据库
const saveAppSettingsToDatabase = async () => {
  try {
    // console.log('[AutoCompleteSetting] 保存应用设置到数据库');
    await setAppSettings(store.perferences);
    // console.log('[AutoCompleteSetting] 应用设置已保存到数据库');
  } catch (error) {
    console.error('[AutoCompleteSetting] 保存应用设置到数据库失败:', error);
  }
};

// 组件挂载时初始化选择
onMounted(() => {
  // console.log('[AutoComplete] 组件挂载，初始化供应商和模型选择');

  setTimeout(() => {
    // 首先触发自动调整，确保启用状态正确
    store.autoAdjustAutoComplete();

    // 优先检查settings中是否有合法的model
    const model = settings.value.body.model;
    let found = false;
    if (model) {
      // 遍历所有可用供应商，查找该模型是否属于其中之一
      const llmSettings = store.perferences?.llm;
      if (llmSettings) {
        for (const provider of availableProviders.value) {
          const providerSettings = llmSettings[provider.value as keyof typeof llmSettings];
          if (
            providerSettings &&
            typeof providerSettings === 'object' &&
            'avaliableModels' in providerSettings &&
            providerSettings.avaliableModels
          ) {
            const categorizedModels = providerSettings.avaliableModels as CategorizedModels;
            const models = getModelsByType(categorizedModels, AUTOCOMPLETE_MODEL_TYPES);
            if (models.includes(model)) {
              selectedProvider.value = provider.value;
              selectedModel.value = model;
              onProviderChange(provider.value);
              // onProviderChange会自动设置selectedModel和触发onModelChange
              found = true;
              break;
            }
          }
        }
      }
    }
    // 如果没有找到，按原逻辑选择第一个可用供应商
    if (!found && availableProviders.value.length > 0) {
      selectedProvider.value = availableProviders.value[0].value;
      onProviderChange(selectedProvider.value);
    }
  }, 100);
});
</script>

<style lang="scss" scoped>
.autocomplete-settings {
  width: 100%;
}
</style>
