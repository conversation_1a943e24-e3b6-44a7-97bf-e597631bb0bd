import type { JSONContent } from '@tiptap/vue-3';
import { createConverterEditor } from './editorFactory';

/**
 * 将HTML表格转换为Markdown表格格式
 * @param html 包含HTML表格的字符串
 * @returns 转换后的Markdown格式字符串
 */
export const convertHtmlTableToMarkdown = (html: string): string => {
  // 使用正则表达式匹配表格
  return html.replace(/<table[^>]*>([\s\S]*?)<\/table>/gi, (tableMatch, tableContent) => {
    const rows: string[][] = [];

    // 提取所有行
    const rowMatches = tableContent.match(/<tr[^>]*>([\s\S]*?)<\/tr>/gi);
    if (!rowMatches) return tableMatch;

    rowMatches.forEach((rowMatch: string) => {
      const cells: string[] = [];
      // 匹配td和th标签
      const cellMatches = rowMatch.match(/<(td|th)[^>]*>([\s\S]*?)<\/\1>/gi);
      if (cellMatches) {
        cellMatches.forEach((cellMatch: string) => {
          // 提取单元格内容，移除HTML标签和多余空白
          let cellContent = cellMatch.replace(/<[^>]*>/g, '').trim();
          // 移除换行符和多余空格
          cellContent = cellContent.replace(/\s+/g, ' ').trim();
          cells.push(cellContent || ' ');
        });
      }
      if (cells.length > 0) {
        rows.push(cells);
      }
    });

    if (rows.length === 0) return tableMatch;

    // 构建Markdown表格
    let markdownTable = '';

    // 添加表头
    if (rows.length > 0) {
      markdownTable += '| ' + rows[0].join(' | ') + ' |\n';
      // 添加分隔行
      markdownTable += '| ' + rows[0].map(() => '---').join(' | ') + ' |\n';

      // 添加数据行
      for (let i = 1; i < rows.length; i++) {
        markdownTable += '| ' + rows[i].join(' | ') + ' |\n';
      }
    }

    return markdownTable;
  });
};

/**
 * 检测文本是否为Markdown格式
 * @param content 要检测的文本内容
 * @returns 是否为Markdown格式
 */
export function isMarkdownContent(content: string): boolean {
  if (!content || content.trim().length === 0) {
    return false;
  }

  // 检测常见的Markdown语法
  const markdownPatterns = [
    /^#{1,6}\s+/m, // 标题 # ## ### 等
    /\*\*.*?\*\*/, // 粗体 **text**
    /\*.*?\*/, // 斜体 *text*
    /__.*?__/, // 粗体 __text__
    /_.*?_/, // 斜体 _text_
    /`.*?`/, // 行内代码 `code`
    /```[\s\S]*?```/, // 代码块 ```code```
    /^\s*[-*+]\s+/m, // 无序列表 - * +
    /^\s*\d+\.\s+/m, // 有序列表 1. 2. 3.
    /^\s*>\s+/m, // 引用 >
    /\[.*?\]\(.*?\)/, // 链接 [text](url)
    /!\[.*?\]\(.*?\)/, // 图片 ![alt](url)
    /^\s*\|.*\|.*\|/m, // 表格 |col1|col2|
    /^\s*---+\s*$/m, // 分割线 ---
  ];

  // 如果匹配到多个Markdown语法，则认为是Markdown
  let matchCount = 0;
  for (const pattern of markdownPatterns) {
    if (pattern.test(content)) {
      matchCount++;
      if (matchCount >= 2) {
        return true;
      }
    }
  }

  // 如果只匹配到一个语法，但内容较短且明显是Markdown，也认为是Markdown
  if (matchCount === 1 && content.length < 200) {
    return true;
  }

  return false;
}

/**
 * 将Markdown内容转换为TipTap JSON格式
 * @param markdownContent Markdown格式的文本内容
 * @returns TipTap JSON格式的内容
 */
export function convertMarkdownToTiptap(markdownContent: string): JSONContent {
  if (!markdownContent || markdownContent.trim().length === 0) {
    return {
      type: 'doc',
      content: [],
    };
  }

  try {
    // 使用标准编辑器实例来转换Markdown
    const tempEditor = createConverterEditor();

    // 使用setContent方法将Markdown转换为TipTap JSON
    tempEditor.commands.setContent(markdownContent);
    const jsonContent = tempEditor.getJSON();

    // 销毁临时编辑器
    tempEditor.destroy();

    return jsonContent;
  } catch (error) {
    console.warn('Markdown转换失败，使用纯文本格式:', error);
    // 如果转换失败，回退到纯文本格式
    return {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: markdownContent,
            },
          ],
        },
      ],
    };
  }
}

/**
 * 将 TipTap JSON 转换为 Markdown
 * @param jsonContent TipTap JSON 内容
 * @returns Markdown 内容
 */
export function convertTiptapToMarkdown(jsonContent: JSONContent): string {
  if (!jsonContent || !jsonContent.content || jsonContent.content.length === 0) {
    return '';
  }

  try {
    // 使用标准编辑器实例来转换 TipTap JSON
    const tempEditor = createConverterEditor();

    // 使用setContent方法将 TipTap JSON 转换为 Markdown
    tempEditor.commands.setContent(jsonContent);
    let markdownContent = tempEditor.storage.markdown.getMarkdown();

    // 转换HTML表格为Markdown表格
    markdownContent = convertHtmlTableToMarkdown(markdownContent);

    // 销毁临时编辑器
    tempEditor.destroy();

    return markdownContent;
  } catch {
    let text = '';

    const extractText = (node: Record<string, unknown>) => {
      if (node.type === 'text') {
        text += (node.text as string) || '';
      } else if (node.content && Array.isArray(node.content)) {
        node.content.forEach((childNode: Record<string, unknown>) => extractText(childNode));
      }

      // 在段落、标题等块级元素后添加换行
      if (['paragraph', 'heading', 'codeBlock', 'blockquote'].includes(node.type as string)) {
        text += '\n';
      }
    };

    if (jsonContent && typeof jsonContent === 'object' && 'content' in jsonContent) {
      const contentObj = jsonContent as Record<string, unknown>;
      if (Array.isArray(contentObj.content)) {
        contentObj.content.forEach((node: Record<string, unknown>) => extractText(node));
      }
    } else if (typeof jsonContent === 'string') {
      text = jsonContent;
    }

    return text.trim();
  }
}

/**
 * 将文本内容转换为TipTap JSON格式
 * 自动检测是否为Markdown格式，如果是则转换，否则作为纯文本处理
 * @param content 要转换的文本内容
 * @returns TipTap JSON格式的内容
 */
export function convertContentToTiptap(content: string): JSONContent {
  if (!content || content.trim().length === 0) {
    return {
      type: 'doc',
      content: [],
    };
  }

  // 检查是否为Markdown格式
  if (isMarkdownContent(content)) {
    return convertMarkdownToTiptap(content);
  } else {
    // 非Markdown内容，作为纯文本处理
    return convertTextToTiptap(content);
  }
}

/**
 * 将纯文本转换为TipTap JSON格式
 * @param textContent 纯文本内容
 * @returns TipTap JSON格式的内容
 */
export function convertTextToTiptap(textContent: string): JSONContent {
  if (!textContent || textContent.trim().length === 0) {
    return {
      type: 'doc',
      content: [],
    };
  }

  return {
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: [
          {
            type: 'text',
            text: textContent,
          },
        ],
      },
    ],
  };
}
