<template>
  <q-list class="column gap-xs q-pa-xl">
    <q-item>
      <q-item-section avatar>
        {{ $t('src.components.settings.EditorSettings.fontSize') }}
      </q-item-section>
      <q-item-section>
        <q-slider
          v-model="perferences.editor.fontSize"
          :min="1"
          :max="3"
          :step="0.1"
          label
          :label-value="
            perferences.editor.fontSize + ` ${$t('src.components.settings.common.times')}`
          "
          label-always
        />
      </q-item-section>
    </q-item>

    <q-item>
      <q-item-section avatar>
        {{ $t('src.components.settings.EditorSettings.lineHeight') }}
      </q-item-section>
      <q-item-section>
        <q-slider
          v-model="perferences.editor.lineHeight"
          :min="1"
          :max="3"
          :step="0.25"
          label
          :label-value="
            perferences.editor.lineHeight + ` ${$t('src.components.settings.common.times')}`
          "
          label-always
        />
      </q-item-section>
    </q-item>

    <q-item>
      <q-item-section avatar>
        {{ $t('src.components.settings.EditorSettings.autoComplete') }}
      </q-item-section>
      <q-item-section>
        <q-toggle v-model="perferences.editor.enableAutoComplete" color="primary" />
      </q-item-section>
    </q-item>

    <q-item>
      <q-item-section avatar>
        {{ $t('src.components.settings.EditorSettings.autoSave') }}
      </q-item-section>
      <q-item-section>
        <q-toggle v-model="perferences.editor.enableAutoSave" color="primary" />
      </q-item-section>
    </q-item>

    <q-item>
      <q-item-section avatar>
        {{ $t('src.components.settings.EditorSettings.enableToolbar') }}
      </q-item-section>
      <q-item-section>
        <q-toggle v-model="perferences.editor.enableToolbar" color="primary" />
      </q-item-section>
    </q-item>

    <q-item>
      <q-item-section avatar>
        {{ $t('src.components.settings.EditorSettings.enableEmbeddedTitle') }}
      </q-item-section>
      <q-item-section>
        <q-toggle v-model="perferences.editor.enableEmbeddTitle" color="primary" />
      </q-item-section>
    </q-item>
  </q-list>
</template>
<script setup lang="ts">
import { useUiStore } from 'src/stores/ui';
const uiStore = useUiStore();
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

const { perferences } = uiStore;
</script>
