import { ref } from 'vue';
import { defineBoot } from '#q-app/wrappers';
import type { UiStoreType, WindowApi, DatabaseApi, KnowledgeApi } from 'src/env.d';
import { useUiStore } from 'src/stores/ui';

const uiStore = useUiStore();

declare global {
  interface Window {
    uiStore: UiStoreType;
    qtWindow: WindowApi;
    databaseApi: DatabaseApi;
    knowledgeApi: KnowledgeApi;
    qt?: {
      webChannelTransport: {
        send: (data: string) => void;
        onmessage: (payload: Record<string, unknown>) => void;
      };
    };
    qtInitialized: Promise<void>;
  }
  interface $ink {
    isQtApp: boolean;
  }
}

const $ink = ref({
  isQtApp: false,
});

// 初始化主题（简化版本，避免循环依赖）
const initTheme = async () => {
  try {
    // 直接从Qt端获取主题设置，避免在初始化阶段调用loadSettings
    if (window.qtWindow?.getSavedTheme !== undefined) {
      const isDarkTheme = await window.qtWindow.getSavedTheme();
      uiStore.setTheme(isDarkTheme);
      console.log('[qt-integration] 从Qt端初始化主题:', isDarkTheme ? '暗色' : '亮色');
    } else {
      console.log('[qt-integration] Qt主题API不可用，使用默认主题');
      uiStore.setTheme(false);
    }
  } catch (error) {
    console.error('[qt-integration] 主题初始化失败:', error);
    // 如果失败，使用默认主题
    uiStore.setTheme(false);
  }
};

// 初始化应用设置（延迟执行，避免循环依赖）
const initAppSettings = async () => {
  try {
    // 1. 加载应用设置（包含编辑器设置和LLM设置）
    await uiStore.loadSettings();

    // 2. 同步数据库中的主题设置到前端
    const themeFromDatabase = uiStore.perferences.base.theme;
    const isDarkTheme = themeFromDatabase === 'dark';
    uiStore.setTheme(isDarkTheme);
    console.log('[qt-integration] 从数据库同步主题设置:', themeFromDatabase);

    // 3. 检查是否需要从独立的LLM设置表中迁移数据
    await uiStore.migrateLlmSettings();

    // 4. 同步语言设置到i18n
    await initLanguageSettings();

    // 5. 启动设置监听，自动保存变更
    uiStore.watchSettings();
  } catch (error) {
    console.error('[Qt Integration] 应用设置初始化失败:', error);
    // 即使初始化失败，也要启动设置监听，确保后续的设置变更能被保存
    try {
      uiStore.watchSettings();
    } catch (watchError) {
      console.error('[Qt Integration] 设置监听启动失败:', watchError);
    }
  }
};

// 初始化语言设置
const initLanguageSettings = async () => {
  try {
    // 确保设置已经从数据库加载
    const savedLanguage = uiStore.perferences.base.language;
    if (savedLanguage) {
      // 立即更新 i18n 实例的语言设置
      try {
        const { i18nInstance } = await import('src/boot/i18n');
        if (i18nInstance && i18nInstance.global) {
          const globalInstance = i18nInstance.global as unknown as {
            locale: { value: string };
          };
          if (globalInstance.locale) {
            const currentLocale = globalInstance.locale.value;
            if (savedLanguage !== currentLocale) {
              globalInstance.locale.value = savedLanguage;
            } else {
              console.log('[Qt Integration] i18n语言设置已经是正确的:', savedLanguage);
            }
          }
        }
      } catch (i18nError) {
        console.warn('[Qt Integration] 无法更新i18n语言设置:', i18nError);
      }
    }
  } catch (error) {
    console.error('[Qt Integration] 语言设置初始化失败:', error);
  }
};

export default defineBoot(() => {
  // 将uiStore暴露到全局作用域，以便Qt应用能够调用
  if (typeof window !== 'undefined') {
    // 使用双重类型断言来安全地设置全局属性
    window.uiStore = uiStore as unknown as UiStoreType;
    window.EXCALIDRAW_ASSET_PATH = '/';
    // console.log('uiStore已暴露到全局作用域');
  }

  // 设置一个全局 Promise，以便应用的其他部分可以等待 Qt API 初始化完成
  let resolveQtInitialized: () => void;
  window.qtInitialized = new Promise((resolve) => {
    resolveQtInitialized = resolve;
  });

  // 检查是否在Qt环境中运行
  if (typeof window !== 'undefined' && window.qt && window.qt.webChannelTransport) {
    try {
      // 动态加载QWebChannel
      const script = document.createElement('script');
      script.src = 'qrc:///qtwebchannel/qwebchannel.js';
      document.head.appendChild(script);

      // 等待QWebChannel加载完成
      script.onload = () => {
        if (window.QWebChannel && window.qt) {
          new window.QWebChannel(window.qt.webChannelTransport, (channel) => {
            // 将所有Qt对象暴露到全局
            window.qtWindow = channel.objects.qtWindow;
            window.databaseApi = channel.objects.databaseApi;
            window.knowledgeApi = channel.objects.knowledgeApi;
            // console.log(window.databaseApi);

            // 只初始化主题，不加载应用设置（避免循环依赖）
            const themePromise = window.qtWindow ? initTheme() : Promise.resolve();

            themePromise
              .then(() => {
                if (window.qtWindow) {
                  console.log('✅ Qt "qtWindow" API 连接成功');
                }
                if (window.databaseApi) {
                  console.log('✅ Qt "databaseApi" API 连接成功');
                }

                if (window.knowledgeApi) {
                  console.log('✅ Qt "knowledgeApi" API 连接成功');

                  // 添加调试功能到全局作用域
                  (
                    window as unknown as { debugKnowledge: (kbId?: string) => void }
                  ).debugKnowledge = (kbId?: string) => {
                    if (window.knowledgeApi?.debugObjectBoxData) {
                      const result = window.knowledgeApi.debugObjectBoxData(kbId || '');
                      if (typeof result === 'string') {
                        try {
                          const parsed = JSON.parse(result);
                          return parsed;
                        } catch {
                          return result;
                        }
                      } else {
                        return result;
                      }
                    } else {
                      console.log('❌ debugObjectBoxData 方法不可用');
                    }
                  };
                }
                $ink.value.isQtApp = true;

                // 先resolve Promise，避免循环依赖
                resolveQtInitialized();

                // 然后异步加载应用设置
                setTimeout(() => {
                  void initAppSettings();
                }, 0);
              })
              .catch((error) => {
                console.error('初始化失败:', error);
                resolveQtInitialized();

                // 即使主题初始化失败，也尝试加载应用设置
                setTimeout(() => {
                  void initAppSettings();
                }, 0);
              });
          });
        }
      };

      script.onerror = () => {
        console.warn('无法加载 QWebChannel，某些功能可能不可用');
        resolveQtInitialized();
      };
    } catch (error) {
      console.warn('Qt WebChannel 初始化失败:', error);
      resolveQtInitialized();
    }
  } else {
    // 非Qt环境，直接resolve Promise
    console.log('非Qt环境，跳过Qt API初始化');
    resolveQtInitialized();

    // 在非Qt环境下也要加载应用设置
    setTimeout(() => {
      void initAppSettings();
    }, 0);
  }

  // 添加快捷键支持
  document.addEventListener('keydown', (event: KeyboardEvent) => {
    if (!window.qtWindow) return;

    // F5 或 Ctrl+R - 重新加载
    if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
      event.preventDefault();
      window.qtWindow.reloadPage();
    }
    // F12 - 开发者工具
    else if (event.key === 'F12') {
      event.preventDefault();
      window.qtWindow.openDevTools();
    }
    // Ctrl+T - 切换模式
    else if (event.ctrlKey && event.key === 't') {
      event.preventDefault();
      window.qtWindow.toggleMode();
    }
  });
});

export { $ink };
