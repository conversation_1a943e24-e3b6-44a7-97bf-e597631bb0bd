# InkCop API 密钥配置指南

## 📋 概述

InkCop 需要配置外部 API 密钥才能使用 AI 功能和搜索功能。本指南将帮助您获取并配置所需的 API 密钥。

## 🔑 需要的 API 密钥

### 1. 通义千问 (Qwen) API 密钥 - **必需**
- **用途**: AI 对话、文本生成、自动补全
- **获取方式**: 
  1. 访问 [阿里云控制台](https://dashscope.console.aliyun.com/)
  2. 开通通义千问服务
  3. 创建 API 密钥
- **格式**: `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

### 2. Tavily API 密钥 - **可选**
- **用途**: 网络搜索功能
- **获取方式**:
  1. 访问 [Tavily 官网](https://tavily.com)
  2. 注册账户并获取 API 密钥
- **格式**: `tvly-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

### 3. Pexels API 密钥 - **可选**
- **用途**: 图片搜索和媒体浏览
- **获取方式**:
  1. 访问 [Pexels API](https://www.pexels.com/api/)
  2. 注册开发者账户
  3. 创建应用并获取 API 密钥
- **格式**: 长字符串密钥

## ⚙️ 配置方法

### 方法1: 通过应用设置界面配置（推荐）

1. **启动 InkCop 应用**
2. **打开设置**
   - 点击右上角的设置图标
   - 或使用快捷键 `Ctrl + ,`
3. **配置 LLM 设置**
   - 在左侧菜单选择 "LLM 设置"
   - 选择对应的服务提供商
4. **输入 API 密钥**
   - **通义千问**: 在 "通义千问" 选项卡中输入 API Key
   - **Tavily**: 在 "Tavily" 选项卡中输入 API Key
   - **Pexels**: 在 "Pexels" 选项卡中输入 API Key
5. **测试连接**
   - 点击 "测试连接" 按钮验证密钥是否有效
6. **保存设置**
   - 设置会自动保存到本地数据库

### 方法2: 通过环境变量配置（开发者）

如果您是开发者或需要批量部署，可以通过环境变量配置：

```bash
# Windows (PowerShell)
$env:QWEN_KEY="your-qwen-api-key"
$env:TAVILY_API_KEY="your-tavily-api-key"
$env:PEXELS_KEY="your-pexels-api-key"

# Linux/macOS
export QWEN_KEY="your-qwen-api-key"
export TAVILY_API_KEY="your-tavily-api-key"
export PEXELS_KEY="your-pexels-api-key"
```

## 🔒 安全注意事项

1. **保护您的 API 密钥**
   - 不要在公共场所或截图中暴露 API 密钥
   - 不要将密钥提交到版本控制系统

2. **定期轮换密钥**
   - 建议定期更换 API 密钥以提高安全性

3. **监控使用量**
   - 定期检查 API 使用量，避免意外的高额费用

## 🚀 功能说明

### 通义千问 (必需)
- **AI 对话**: 与 AI 助手进行智能对话
- **文本生成**: 自动生成文章、摘要等内容
- **自动补全**: 写作时的智能建议
- **知识库搜索**: 基于向量的语义搜索

### Tavily (可选)
- **网络搜索**: 获取最新的网络信息
- **内容增强**: 为文档添加实时信息
- **研究辅助**: 快速收集相关资料

### Pexels (可选)
- **图片搜索**: 搜索高质量的免费图片
- **媒体浏览**: 浏览和下载图片资源
- **内容配图**: 为文档添加合适的配图

## ❓ 常见问题

### Q: 为什么我的 API 密钥无效？
A: 请检查：
- 密钥是否正确复制（注意空格和特殊字符）
- 账户是否有足够的余额或配额
- 服务是否已正确开通

### Q: 可以只配置部分 API 密钥吗？
A: 可以。通义千问是必需的，其他服务是可选的。未配置的服务对应功能将不可用。

### Q: 设置保存在哪里？
A: 设置保存在本地数据库中，位于用户数据目录：
- Windows: `%APPDATA%\inkCop\user\`
- Linux: `~/.local/share/inkCop/user/`
- macOS: `~/Library/Application Support/inkCop/user/`

### Q: 如何重置设置？
A: 可以通过设置界面重新输入密钥，或删除用户数据目录重置所有设置。

## 📞 技术支持

如果您在配置过程中遇到问题，请：
1. 查看应用日志获取详细错误信息
2. 确认网络连接正常
3. 联系技术支持团队

---

**注意**: 本应用不会收集或上传您的 API 密钥，所有密钥都安全存储在本地。
