# 关闭按钮hover白色图标修正记录

**日期**: 2025-01-26 15:24  
**修正内容**: 关闭按钮在鼠标hover时始终使用白色图标

## 问题描述

用户要求关闭按钮在鼠标悬停（hover）时始终使用 `Close_white.svg` 白色图标，而不是根据当前主题来选择图标颜色。

## 原有逻辑

在 `QWKCustomTitleBar::updateButtonIcons()` 方法中，关闭按钮图标根据主题选择：

```cpp
// 关闭按钮图标
QString closeIconPath = ":/icons/ui/Close" + iconSuffix;
QIcon closeIcon(closeIconPath);
m_closeButton->setIcon(closeIcon);
```

其中 `iconSuffix` 的值：
- 暗色主题：`"_white.svg"` → `Close_white.svg`
- 亮色主题：`".svg"` → `Close.svg`（黑色图标）

## 修正方案

通过事件过滤器（Event Filter）来监听关闭按钮的鼠标进入和离开事件，在hover状态时强制使用白色图标。

### 修改文件

**文件**: `qt-src/qwkcustomtitlebar.cpp`

### 具体修改

#### 1. 添加事件过滤器安装

在 `setupConnections()` 方法中添加：

```cpp
void QWKCustomTitleBar::setupConnections()
{
    connect(m_minimizeButton, &QPushButton::clicked, this, &QWKCustomTitleBar::onMinimizeClicked);
    connect(m_maximizeButton, &QPushButton::clicked, this, &QWKCustomTitleBar::onMaximizeClicked);
    connect(m_closeButton, &QPushButton::clicked, this, &QWKCustomTitleBar::onCloseClicked);
    
    // 为关闭按钮添加hover事件处理
    m_closeButton->installEventFilter(this);
    
    // ... 其他连接
}
```

#### 2. 实现hover状态处理

在 `eventFilter()` 方法中添加关闭按钮的hover处理：

```cpp
bool QWKCustomTitleBar::eventFilter(QObject *obj, QEvent *event)
{
    // ... 原有的双击处理逻辑
    
    else if (obj == m_closeButton)
    {
        if (event->type() == QEvent::Enter)
        {
            // 鼠标进入关闭按钮时，始终使用白色图标
            QIcon closeIcon(":/icons/ui/Close_white.svg");
            m_closeButton->setIcon(closeIcon);
            m_closeButton->setIconSize(QSize(16, 16));
            return false; // 继续传递事件
        }
        else if (event->type() == QEvent::Leave)
        {
            // 鼠标离开关闭按钮时，恢复原来的图标
            QString iconSuffix = m_isDarkTheme ? "_white.svg" : ".svg";
            QString closeIconPath = ":/icons/ui/Close" + iconSuffix;
            QIcon closeIcon(closeIconPath);
            m_closeButton->setIcon(closeIcon);
            m_closeButton->setIconSize(QSize(16, 16));
            return false; // 继续传递事件
        }
    }
    
    return QWidget::eventFilter(obj, event);
}
```

## 实现效果

### 行为变化

1. **鼠标未悬停时**：
   - 亮色主题：显示黑色关闭图标 (`Close.svg`)
   - 暗色主题：显示白色关闭图标 (`Close_white.svg`)

2. **鼠标悬停时**：
   - **所有主题**：始终显示白色关闭图标 (`Close_white.svg`)

3. **鼠标离开时**：
   - 恢复到对应主题的默认图标

### 技术特点

- ✅ 使用事件过滤器，不影响按钮的其他功能
- ✅ 保持原有的主题切换逻辑
- ✅ hover状态变化流畅，无闪烁
- ✅ 兼容现有的样式表hover背景色变化

## 验证

- ✅ 代码编译无错误
- ✅ 事件过滤器正确安装
- ✅ hover进入/离开事件正确处理
- ✅ 图标路径和尺寸设置正确

## 注意事项

1. 事件过滤器返回 `false` 以确保hover的背景色变化等其他效果正常工作
2. 图标尺寸保持一致（16x16）
3. 使用资源路径 `":/icons/ui/Close_white.svg"` 确保图标正确加载
