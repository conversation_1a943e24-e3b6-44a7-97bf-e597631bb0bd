# 任务队列管理系统

基于 IndexedDB 的持久化任务队列系统，用于管理 Worker 任务的执行和状态恢复。

## 功能特性

- ✅ **持久化存储**: 使用 IndexedDB 存储任务队列，应用重启后自动恢复
- ✅ **任务状态管理**: 支持 pending、running、completed、failed 四种状态
- ✅ **自动重试**: 支持任务失败后的自动重试机制
- ✅ **优先级调度**: 支持任务优先级，优先处理高优先级任务
- ✅ **并发控制**: 支持最大并发任务数限制
- ✅ **崩溃恢复**: 应用重启后自动恢复中断的任务
- ✅ **进度监控**: 实时监控任务执行进度和队列状态

## 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   应用层接口     │    │   Worker管理器    │    │   队列管理器     │
│                │    │                 │    │                │
│ splitInBackground│───▶│chunkingWorker   │───▶│ TaskQueueManager│
│                │    │ Manager         │    │                │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   IndexedDB     │
                                               │   (持久化存储)   │
                                               └─────────────────┘
```

## 核心组件

### 1. TaskQueueManager (任务队列管理器)

负责任务的持久化存储、状态管理和执行调度。

```typescript
import { taskQueueManager, TaskType, TaskStatus } from './utils/taskQueue';

// 初始化队列管理器
await taskQueueManager.initialize();

// 添加任务
const taskId = await taskQueueManager.addTask({
  type: TaskType.CHUNKING,
  status: TaskStatus.PENDING,
  priority: 1,
  maxRetries: 3,
  payload: {
    strategy: 'markdown',
    content: '文档内容...',
    config: { chunkSize: 1000, chunkOverlap: 200 },
  },
});

// 获取任务状态
const task = await taskQueueManager.getTask(taskId);

// 获取队列统计
const stats = await taskQueueManager.getTaskStats();
```

### 2. ChunkingWorkerManager (切割任务管理器)

专门用于管理文档切割任务的高级接口。

```typescript
import { chunkingWorkerManager } from './utils/chunkingWorkerManager';

// 提交切割任务
const taskId = await chunkingWorkerManager.submitChunkingTask(
  'markdown',
  '# 文档内容...',
  { chunkSize: 1000, chunkOverlap: 200 },
  true,
  {
    onProgress: (task) => console.log('进度:', task.progress),
    onCompletion: (task, result) => console.log('完成:', result),
    onError: (task, error) => console.error('错误:', error),
  }
);
```

### 3. splitInBackground (高级接口)

最简单的使用方式，提供与原有接口兼容的高级封装。

```typescript
import { splitInBackground } from './utils/knowledgeBase';

const taskId = await splitInBackground(
  'smart',
  '文档内容...',
  { chunkSize: 800, chunkOverlap: 150 },
  true,
  {
    onProgress: (task) => console.log('进度:', task.status),
    onCompletion: (task, result) => console.log('完成:', result.chunks.length),
    onError: (task, error) => console.error('错误:', error),
  }
);
```

## 任务状态流转

```
PENDING ──────▶ RUNNING ──────▶ COMPLETED
   │               │                 ▲
   │               ▼                 │
   │            FAILED ──────────────┘
   │               │        (重试)
   │               ▼
   └──────────▶ FAILED (最终失败)
```

## 数据库结构

### tasks 表

| 字段 | 类型 | 说明 |
|------|------|------|
| id | string | 任务唯一标识符 |
| type | TaskType | 任务类型 (CHUNKING, VECTORIZATION, etc.) |
| status | TaskStatus | 任务状态 (PENDING, RUNNING, COMPLETED, FAILED) |
| priority | number | 优先级 (数字越小优先级越高) |
| createdAt | number | 创建时间戳 |
| updatedAt | number | 更新时间戳 |
| startedAt | number? | 开始执行时间戳 |
| completedAt | number? | 完成时间戳 |
| error | string? | 错误信息 |
| retryCount | number | 当前重试次数 |
| maxRetries | number | 最大重试次数 |
| payload | any | 任务数据 |
| result | any? | 任务结果 |

## 配置选项

### 队列管理器配置

```typescript
class TaskQueueManager {
  private readonly PROCESSING_INTERVAL = 1000; // 队列检查间隔 (毫秒)
  private readonly MAX_CONCURRENT_TASKS = 3;   // 最大并发任务数
}
```

### 任务重试配置

```typescript
await taskQueueManager.addTask({
  // ... 其他配置
  maxRetries: 3,  // 最大重试次数
  priority: 1,    // 优先级 (1-10, 数字越小优先级越高)
});
```

## 使用示例

### 基本使用

```typescript
// 1. 初始化系统
await taskQueueManager.initialize();

// 2. 提交任务
const taskId = await splitInBackground(
  'markdown',
  '# 我的文档\n\n这是文档内容...',
  { chunkSize: 1000, chunkOverlap: 200 }
);

// 3. 监控进度 (可选)
const task = await taskQueueManager.getTask(taskId);
console.log('任务状态:', task?.status);
```

### 批量处理

```typescript
const documents = [
  { title: '文档1', content: '内容1...' },
  { title: '文档2', content: '内容2...' },
  { title: '文档3', content: '内容3...' },
];

// 批量提交
const taskIds = await Promise.all(
  documents.map(doc => 
    splitInBackground('smart', doc.content, { chunkSize: 500 })
  )
);

// 监控整体进度
setInterval(async () => {
  const stats = await taskQueueManager.getTaskStats();
  console.log(`进度: ${stats.completed}/${stats.total} 完成`);
}, 1000);
```

### 错误处理

```typescript
try {
  const taskId = await splitInBackground(
    'markdown',
    content,
    config,
    true,
    {
      onError: (task, error) => {
        console.error(`任务 ${task.id} 失败:`, error);
        // 可以在这里实现自定义错误处理逻辑
      }
    }
  );
} catch (error) {
  console.error('提交任务失败:', error);
}
```

## 性能优化建议

1. **合理设置并发数**: 根据设备性能调整 `MAX_CONCURRENT_TASKS`
2. **优化任务大小**: 避免单个任务过大，建议文档大小控制在 1MB 以内
3. **定期清理**: 系统会自动清理已完成的任务，但可以手动调用清理方法
4. **监控队列状态**: 定期检查队列统计，避免任务积压

## 故障排除

### 常见问题

1. **任务一直处于 PENDING 状态**
   - 检查队列管理器是否正确初始化
   - 确认没有达到最大并发限制

2. **应用重启后任务丢失**
   - 确认 IndexedDB 权限正常
   - 检查浏览器存储空间是否充足

3. **任务执行失败**
   - 查看任务的 error 字段获取详细错误信息
   - 检查任务 payload 数据是否正确

### 调试工具

```typescript
// 获取详细的队列状态
const stats = await taskQueueManager.getTaskStats();
console.log('队列统计:', stats);

// 获取所有任务 (仅用于调试)
const allTasks = await taskQueueManager.getAllTasks();
console.log('所有任务:', allTasks);

// 手动清理已完成的任务
await taskQueueManager.cleanupCompletedTasks();
```

## 测试

系统提供了完整的测试工具：

```typescript
import { runAllTests } from './utils/taskQueueTest';

// 运行所有测试
await runAllTests();

// 或在浏览器控制台中
taskQueueTest.runAllTests();
```
