import type { Editor } from '@tiptap/vue-3';
import type { Editor as CoreEditor } from '@tiptap/core';
import { ref, computed } from 'vue';
import { Dialog, useQuasar } from 'quasar';
import { Mark } from '@tiptap/core';
import { useSqlite } from 'src/composeables/useSqlite';
import { useDocumentSave } from 'src/composeables/useDocumentSave';
import { downloadImageWithQt, downloadMultipleImagesWithQt } from 'src/utils/qtImageDownload';

import { useUiStore } from 'src/stores/ui';
import { useDocStore } from 'src/stores/doc';

// 保存状态类型
export type SaveStatus = 'saved' | 'saving' | 'pending' | 'error';

// 保存状态管理 - 使用 Map 存储每个文档的保存状态
export const saveStatusMap = ref<Map<number, SaveStatus>>(new Map());
export const lastSaveTimeMap = ref<Map<number, number>>(new Map());
export const saveTimerMap = ref<Map<number, NodeJS.Timeout | null>>(new Map());

// 保存策略配置
const DEBOUNCE_DELAY = 2000; // 2秒防抖延迟
const MAX_UNSAVED_TIME = 10000; // 最长10秒必须保存一次

// 获取文档保存状态
export const getSaveStatus = (docId: number): SaveStatus => {
  return saveStatusMap.value.get(docId) ?? 'saved';
};

// 设置文档保存状态
export const setSaveStatus = (docId: number, status: SaveStatus) => {
  saveStatusMap.value.set(docId, status);
};

// 清理文档保存相关数据
export const cleanupSaveData = (docId: number) => {
  const timer = saveTimerMap.value.get(docId);
  if (timer) {
    clearTimeout(timer);
    saveTimerMap.value.delete(docId);
  }
  saveStatusMap.value.delete(docId);
  lastSaveTimeMap.value.delete(docId);
};

// 定义按钮项的类型
export type ToolbarItem = {
  key: string;
  label?: (...args: boolean[]) => string;
  icon?: string;
  type: 'button' | 'menu' | 'separator::vertical' | 'separator::horizontal' | 'space';
  description?: string;
  tooltip?: string;
  disabled?: () => boolean;
  visible?: () => boolean;
  class?: () => Record<string, boolean>;
  handler?: () => void;
  children?: string[];
  slashCommand?: (payload: { editor: Editor; range: { from: number; to: number } }) => void;
};

// 修改 catalogVisible 的类型定义，使用 Map 来存储每个编辑器的目录状态
export const catalogVisible = ref<Map<string, boolean>>(new Map());

// 初始化目录状态
export const initCatalogState = (instanceKey: string) => {
  if (!catalogVisible.value.has(instanceKey)) {
    catalogVisible.value.set(instanceKey, false);
  }
};

// 获取目录状态
export const getCatalogState = (instanceKey: string) => {
  return catalogVisible.value.get(instanceKey) ?? false;
};

// 切换目录状态
export const toggleCatalog = (instanceKey: string) => {
  const currentState = catalogVisible.value.get(instanceKey) ?? false;
  catalogVisible.value.set(instanceKey, !currentState);
};

//快照部分
// 修改 snapshotViewerVisible 的类型定义，使用 Map 来存储每个编辑器的快照查看器状态
export const snapshotViewerVisible = ref<Map<string, boolean>>(new Map());

// 查找替换面板状态管理
export const searchReplacePanelVisible = ref<Map<string, boolean>>(new Map());

// 初始化快照查看器状态
export const initSnapshotViewerState = (instanceKey: string) => {
  if (!snapshotViewerVisible.value.has(instanceKey)) {
    snapshotViewerVisible.value.set(instanceKey, false);
  }
};

// 获取快照查看器状态
export const getSnapshotViewerState = (instanceKey: string) => {
  return snapshotViewerVisible.value.get(instanceKey) ?? false;
};

// 切换快照查看器状态
export const toggleSnapshotViewer = (instanceKey: string) => {
  const currentState = snapshotViewerVisible.value.get(instanceKey) ?? false;
  snapshotViewerVisible.value.set(instanceKey, !currentState);
};

// 初始化查找替换面板状态
export const initSearchReplacePanelState = (instanceKey: string) => {
  if (!searchReplacePanelVisible.value.has(instanceKey)) {
    searchReplacePanelVisible.value.set(instanceKey, false);
  }
};

// 获取查找替换面板状态
export const getSearchReplacePanelState = (instanceKey: string) => {
  return searchReplacePanelVisible.value.get(instanceKey) ?? false;
};

// 切换查找替换面板状态
export const toggleSearchReplacePanel = (instanceKey: string) => {
  const currentState = searchReplacePanelVisible.value.get(instanceKey) ?? false;
  searchReplacePanelVisible.value.set(instanceKey, !currentState);
};

// 打开查找替换面板（不切换状态，总是打开）
export const openSearchReplacePanel = (instanceKey: string) => {
  searchReplacePanelVisible.value.set(instanceKey, true);
};

// 自定义下标扩展
export const Subscript = Mark.create({
  name: 'subscript',
  parseHTML() {
    return [{ tag: 'sub' }, { style: 'vertical-align=sub' }];
  },
  renderHTML() {
    return ['sub', 0];
  },
});

// 自定义上标扩展
export const Superscript = Mark.create({
  name: 'superscript',
  parseHTML() {
    return [{ tag: 'sup' }, { style: 'vertical-align=super' }];
  },
  renderHTML() {
    return ['sup', 0];
  },
});

const activeWinId = computed(() => {
  const docStore = useDocStore();
  const activeWin = docStore.getActiveWindow();
  if (!activeWin) {
    return 0;
  }
  return activeWin.id;
});
export const { saveImage, createSnapshot } = useSqlite();

export default function useCommands(docId: number) {
  const uiStore = useUiStore();
  const docStore = useDocStore();
  // 获取当前编辑器实例
  const instanceKey = computed(() => docStore.generateEditorInstanceKey(activeWinId.value, docId));
  const currentEditor = computed(() => docStore.getEditorInstanceByKey(instanceKey.value));
  const { perferences } = uiStore;
  const { queueSave } = useDocumentSave();
  const $q = useQuasar();

  // 立即保存函数
  const immediatelySave = (title?: string, folderId?: number) => {
    if (!currentEditor.value) return;

    setSaveStatus(docId, 'saving');

    try {
      const jsonContent = currentEditor.value.getJSON();
      queueSave(docId, title || '', jsonContent, folderId || 0);
      lastSaveTimeMap.value.set(docId, Date.now());

      // 使用短暂延迟来显示保存状态
      setTimeout(() => {
        if (getSaveStatus(docId) === 'saving') {
          setSaveStatus(docId, 'saved');
        }
      }, 500);
    } catch (error) {
      console.error('保存失败:', error);
      setSaveStatus(docId, 'error');
    }
  };

  // 智能保存函数（带防抖）
  const smartWriteDocToDb = (title?: string, folderId?: number) => {
    // 更新状态为待保存
    if (getSaveStatus(docId) !== 'error') {
      setSaveStatus(docId, 'pending');
    }

    // 清除之前的定时器
    const existingTimer = saveTimerMap.value.get(docId);
    if (existingTimer) {
      clearTimeout(existingTimer);
      saveTimerMap.value.delete(docId);
    }

    const lastSaveTime = lastSaveTimeMap.value.get(docId) ?? 0;
    const timeSinceLastSave = Date.now() - lastSaveTime;

    // 如果超过最大未保存时间，立即保存
    if (timeSinceLastSave >= MAX_UNSAVED_TIME) {
      immediatelySave(title, folderId);
    } else {
      // 否则使用防抖
      const timer = setTimeout(() => {
        immediatelySave(title, folderId);
      }, DEBOUNCE_DELAY);
      saveTimerMap.value.set(docId, timer);
    }
  };

  // 计算字数
  const wordCount = computed(() => {
    if (!currentEditor.value) return 0;

    const content = currentEditor.value.getText();
    // 统计中文字符数
    const chineseChars = content.match(/[\u4e00-\u9fa5，。！？；：""''（）【】《》、]/g) || [];
    // 统计英文单词数
    const englishWords = content
      .split(/\s+/)
      .filter((word: string) => /[a-zA-Z]/.test(word)).length;

    // 返回总数：英文单词数 + 中文字符数
    return englishWords + chineseChars.length;
  });

  // 所有按钮和分割线的配置对象
  const toolbarItems: Record<string, ToolbarItem> = {
    space: {
      key: 'space',
      type: 'space',
    },
    agentWriter: {
      key: 'inkcop',
      label: () => 'InkCop',
      icon: 'auto_awesome',
      type: 'button',
      description: 'InkCop 智能体',
      tooltip: 'AI 续/改 写',
      disabled: () => false,
      visible: () => true,
      handler: () => currentEditor.value?.commands.insertAgentWriter(),
      slashCommand: ({ editor }) => {
        editor.commands.insertAgentWriter();
      },
    },

    undo: {
      key: 'undo',
      label: () => '撤销',
      icon: 'undo',
      type: 'button',
      description: '撤销',
      tooltip: '撤销',
      disabled: () => !currentEditor.value?.can().undo(),
      visible: () => true,
      handler: () => currentEditor.value?.chain().focus().undo().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).undo().run();
      },
    },
    redo: {
      key: 'redo',
      label: () => '重做',
      icon: 'redo',
      type: 'button',
      description: '重做',
      tooltip: '重做',
      disabled: () => !currentEditor.value?.can().redo(),
      visible: () => true,
      handler: () => currentEditor.value?.chain().focus().redo().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).redo().run();
      },
    },
    separatorx: {
      key: 'separatorx',
      type: 'separator::vertical',
      visible: () => true,
    },
    separatory: {
      key: 'separatory',
      type: 'separator::horizontal',
      visible: () => true,
    },
    emoji: {
      key: 'emoji',
      label: () => '表情',
      icon: 'emoji_emotions',
      type: 'button',
      description: '插入表情',
      tooltip: '插入表情',
      visible: () => true,
      handler: () => {
        if (!currentEditor.value) return;

        const editor = currentEditor.value;
        const { from } = editor.state.selection;

        // 检查光标左侧是否存在文字
        const textBefore = editor.state.doc.textBetween(Math.max(0, from - 1), from);
        const hasTextBefore = textBefore.trim().length > 0;

        // 如果左侧存在文字，插入空格+冒号，否则只插入冒号
        const contentToInsert = hasTextBefore ? ' :' : ':';

        editor.chain().focus().insertContent(contentToInsert).run();
      },
      slashCommand: ({ editor, range }) => {
        // 先删除slash命令的范围
        editor.chain().focus().deleteRange(range).run();

        // 获取删除后的光标位置
        const { from } = editor.state.selection;

        // 检查光标左侧是否存在文字
        const textBefore = editor.state.doc.textBetween(Math.max(0, from - 1), from);
        const hasTextBefore = textBefore.trim().length > 0;

        // 如果左侧存在文字，插入空格+冒号，否则只插入冒号
        const contentToInsert = hasTextBefore ? ' :' : ':';

        editor.chain().focus().insertContent(contentToInsert).run();
      },
    },
    blockquote: {
      key: 'blockquote',
      label: () => '引用',
      icon: 'format_quote',
      type: 'button',
      description: '引用',
      tooltip: '引用',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('blockquote') }),
      handler: () => currentEditor.value?.chain().focus().toggleBlockquote().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleBlockquote().run();
      },
    },
    codeBlock: {
      key: 'codeBlock',
      label: () => '代码块',
      icon: 'code',
      type: 'button',
      description: '代码块',
      tooltip: '代码块',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('codeBlock') }),
      handler: () => currentEditor.value?.chain().focus().toggleCodeBlock().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleCodeBlock().run();
      },
    },
    mermaid: {
      key: 'mermaid',
      label: () => 'Mermaid图表',
      icon: 'mdi-chart-timeline-variant',
      type: 'button',
      description: 'Mermaid图表',
      tooltip: '插入Mermaid图表',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('mermaid') }),
      handler: () => currentEditor.value?.chain().focus().insertMermaid().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).insertMermaid().run();
      },
    },
    excalidraw: {
      key: 'excalidraw',
      label: () => 'Excalidraw绘图',
      icon: 'mdi-draw',
      type: 'button',
      description: 'Excalidraw绘图',
      tooltip: '插入Excalidraw绘图',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('excalidraw') }),
      handler: () => currentEditor.value?.chain().focus().insertExcalidraw().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).insertExcalidraw().run();
      },
    },
    details: {
      key: 'details',
      label: () => '可折叠段落',
      icon: 'mdi-format-vertical-align-center',
      type: 'button',
      description: '可折叠段落',
      tooltip: '可折叠段落',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('details') }),
      handler: () => currentEditor.value?.chain().focus().setDetails().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).setDetails().run();
      },
    },
    heading: {
      key: 'heading',
      label: () => '标题',
      icon: 'title',
      type: 'menu',
      description: '标题',
      tooltip: '标题',
      visible: () => true,
      children: ['heading1', 'heading2', 'heading3', 'heading4', 'heading5', 'heading6'],
    },
    heading1: {
      key: 'heading1',
      label: () => 'H1',
      description: '插入 H1 标题',
      icon: 'mdi-format-title',
      type: 'button',
      visible: () => true,
      class: () => ({
        'text-h1': true,
        'text-primary': currentEditor.value?.isActive('heading', { level: 1 }),
      }),
      handler: () => currentEditor.value?.chain().focus().toggleHeading({ level: 1 }).run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleHeading({ level: 1 }).run();
      },
    },
    heading2: {
      key: 'heading2',
      label: () => 'H2',
      description: '插入 H2 标题',
      icon: 'mdi-format-title',
      type: 'button',
      visible: () => true,
      class: () => ({
        'text-h2': true,
        'text-primary': currentEditor.value?.isActive('heading', { level: 2 }),
      }),
      handler: () => currentEditor.value?.chain().focus().toggleHeading({ level: 2 }).run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleHeading({ level: 2 }).run();
      },
    },
    heading3: {
      key: 'heading3',
      label: () => 'H3',
      description: '插入 H3 标题',
      icon: 'mdi-format-title',
      type: 'button',
      visible: () => true,
      class: () => ({
        'text-h3': true,
        'text-primary': currentEditor.value?.isActive('heading', { level: 3 }),
      }),
      handler: () => currentEditor.value?.chain().focus().toggleHeading({ level: 3 }).run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleHeading({ level: 3 }).run();
      },
    },
    heading4: {
      key: 'heading4',
      label: () => 'H4',
      description: '插入 H4 标题',
      icon: 'mdi-format-title',
      type: 'button',
      visible: () => true,
      class: () => ({
        'text-h4': true,
        'text-primary': currentEditor.value?.isActive('heading', { level: 4 }),
      }),
      handler: () => currentEditor.value?.chain().focus().toggleHeading({ level: 4 }).run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleHeading({ level: 4 }).run();
      },
    },
    heading5: {
      key: 'heading5',
      label: () => 'H5',
      description: '插入 H5 标题',
      icon: 'mdi-format-title',
      type: 'button',
      visible: () => true,
      class: () => ({
        'text-h5': true,
        'text-primary': currentEditor.value?.isActive('heading', { level: 5 }),
      }),
      handler: () => currentEditor.value?.chain().focus().toggleHeading({ level: 5 }).run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleHeading({ level: 5 }).run();
      },
    },
    heading6: {
      key: 'heading6',
      label: () => 'H6',
      description: '插入 H6 标题',
      icon: 'mdi-format-title',
      type: 'button',
      visible: () => true,
      class: () => ({
        'text-h6': true,
        'text-primary': currentEditor.value?.isActive('heading', { level: 6 }),
      }),
      handler: () => currentEditor.value?.chain().focus().toggleHeading({ level: 6 }).run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleHeading({ level: 6 }).run();
      },
    },
    color: {
      key: 'color',
      label: () => '文字颜色',
      icon: 'mdi-palette',
      type: 'menu',
      description: '文字颜色',
      tooltip: '文字颜色',
      visible: () => true,
      children: [
        'colorPrimary',
        'colorSecondary',
        'colorAccent',
        'colorPositive',
        'colorNegative',
        'colorInfo',
        'colorWarning',
        'colorTransparent',
      ],
    },
    colorPrimary: {
      key: 'colorPrimary',
      label: () => '主要',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-primary': isColorActive('#1976D2') }),
      handler: () => onColorSelect('#1976D2'),
    },
    colorSecondary: {
      key: 'colorSecondary',
      label: () => '次要',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-secondary': isColorActive('#26A69A') }),
      handler: () => onColorSelect('#26A69A'),
    },
    colorAccent: {
      key: 'colorAccent',
      label: () => '强调',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-accent': isColorActive('#9C27B0') }),
      handler: () => onColorSelect('#9C27B0'),
    },
    colorPositive: {
      key: 'colorPositive',
      label: () => '成功',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-positive': isColorActive('#21BA45') }),
      handler: () => onColorSelect('#21BA45'),
    },
    colorNegative: {
      key: 'colorNegative',
      label: () => '错误',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-negative': isColorActive('#C10015') }),
      handler: () => onColorSelect('#C10015'),
    },
    colorInfo: {
      key: 'colorInfo',
      label: () => '信息',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-info': isColorActive('#31CCEC') }),
      handler: () => onColorSelect('#31CCEC'),
    },
    colorWarning: {
      key: 'colorWarning',
      label: () => '警告',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-warning': isColorActive('#F2C037') }),
      handler: () => onColorSelect('#F2C037'),
    },
    colorTransparent: {
      key: 'colorTransparent',
      label: () => '取消颜色',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-grey': isColorActive('transparent') }),
      handler: () => onColorSelect('transparent'),
    },
    // 文本样式按钮
    bold: {
      key: 'bold',
      label: () => '加粗',
      icon: 'format_bold',
      type: 'button',
      description: '加粗',
      tooltip: '加粗',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('bold') }),
      handler: () => currentEditor.value?.chain().focus().toggleBold().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleBold().run();
      },
    },
    italic: {
      key: 'italic',
      label: () => '斜体',
      icon: 'format_italic',
      type: 'button',
      description: '斜体',
      tooltip: '斜体',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('italic') }),
      handler: () => currentEditor.value?.chain().focus().toggleItalic().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleItalic().run();
      },
    },
    underline: {
      key: 'underline',
      label: () => '下划线',
      icon: 'format_underlined',
      type: 'button',
      description: '下划线',
      tooltip: '下划线',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('underline') }),
      handler: () => currentEditor.value?.chain().focus().toggleUnderline().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleUnderline().run();
      },
    },
    strike: {
      key: 'strike',
      label: () => '删除线',
      icon: 'strikethrough_s',
      type: 'button',
      description: '删除线',
      tooltip: '删除线',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('strike') }),
      handler: () => currentEditor.value?.chain().focus().toggleStrike().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleStrike().run();
      },
    },
    code: {
      key: 'code',
      label: () => '行内代码',
      icon: 'code',
      type: 'button',
      description: '行内代码',
      tooltip: '行内代码',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('code') }),
      handler: () => currentEditor.value?.chain().focus().toggleCode().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleCode().run();
      },
    },
    highlight: {
      key: 'highlight',
      label: () => '高亮',
      icon: 'format_color_text',
      type: 'button',
      description: '高亮',
      tooltip: '高亮',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('highlight') }),
      handler: () => currentEditor.value?.chain().focus().toggleHighlight().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleHighlight().run();
      },
    },
    subscript: {
      key: 'subscript',
      label: () => '下标',
      icon: 'subscript',
      type: 'button',
      description: '下标',
      tooltip: '下标',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('subscript') }),
      handler: () =>
        currentEditor.value?.isActive('subscript')
          ? currentEditor.value?.chain().focus().unsetMark('subscript').run()
          : currentEditor.value?.chain().focus().setMark('subscript').run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).setMark('subscript').run();
      },
    },
    superscript: {
      key: 'superscript',
      label: () => '上标',
      icon: 'superscript',
      type: 'button',
      description: '上标',
      tooltip: '上标',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('superscript') }),
      handler: () =>
        currentEditor.value?.isActive('superscript')
          ? currentEditor.value?.chain().focus().unsetMark('superscript').run()
          : currentEditor.value?.chain().focus().setMark('superscript').run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).setMark('superscript').run();
      },
    },

    styleDrawer: {
      key: 'styleDrawer',
      label: () => '样式抽屉',
      icon: 'mdi-menu-down',
      type: 'menu',
      description: '样式抽屉',
      tooltip: '样式抽屉',
      visible: () => true,
      children: ['strike', 'code', 'highlight', 'subscript', 'superscript'],
    },

    // 文本对齐
    align: {
      key: 'align',
      label: () => '对齐方式',
      icon: 'format_align_left',
      type: 'menu',
      description: '对齐方式',
      tooltip: '对齐方式',
      visible: () => true,
      children: ['alignLeft', 'alignCenter', 'alignRight', 'alignJustify'],
    },
    alignLeft: {
      key: 'alignLeft',
      label: () => '左对齐',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive({ textAlign: 'left' }) }),
      handler: () => currentEditor.value?.chain().focus().setTextAlign('left').run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).setTextAlign('left').run();
      },
    },
    alignCenter: {
      key: 'alignCenter',
      label: () => '居中',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive({ textAlign: 'center' }) }),
      handler: () => currentEditor.value?.chain().focus().setTextAlign('center').run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).setTextAlign('center').run();
      },
    },
    alignRight: {
      key: 'alignRight',
      label: () => '右对齐',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive({ textAlign: 'right' }) }),
      handler: () => currentEditor.value?.chain().focus().setTextAlign('right').run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).setTextAlign('right').run();
      },
    },
    alignJustify: {
      key: 'alignJustify',
      label: () => '两端对齐',
      type: 'button',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive({ textAlign: 'justify' }) }),
      handler: () => currentEditor.value?.chain().focus().setTextAlign('justify').run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).setTextAlign('justify').run();
      },
    },

    // 列表
    bulletList: {
      key: 'bulletList',
      label: () => '无序列表',
      icon: 'format_list_bulleted',
      type: 'button',
      description: '无序列表',
      tooltip: '无序列表',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('bulletList') }),
      handler: () => currentEditor.value?.chain().focus().toggleBulletList().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleBulletList().run();
      },
    },
    orderedList: {
      key: 'orderedList',
      label: () => '有序列表',
      icon: 'format_list_numbered',
      type: 'button',
      description: '有序列表',
      tooltip: '有序列表',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('orderedList') }),
      handler: () => currentEditor.value?.chain().focus().toggleOrderedList().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleOrderedList().run();
      },
    },
    taskList: {
      key: 'taskList',
      label: () => '任务列表',
      icon: 'add_task',
      type: 'button',
      description: '任务列表',
      tooltip: '任务列表',
      visible: () => true,
      class: () => ({ 'text-primary': currentEditor.value?.isActive('taskList') }),
      handler: () => currentEditor.value?.chain().focus().toggleTaskList().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleTaskList().run();
      },
    },

    // 表格
    table: {
      key: 'table',
      label: () => '表格',
      icon: 'mdi-table',
      type: 'menu',
      description: '表格',
      tooltip: '表格',
      visible: () => true,
      children: [
        'insertTable',
        'addColumnBefore',
        'addColumnAfter',
        'deleteColumn',
        'addRowBefore',
        'addRowAfter',
        'deleteRow',
        'deleteTable',
        'toggleHeaderCell',
        'mergeCells',
        'splitCell',
      ],
    },
    insertTable: {
      key: 'insertTable',
      label: () => '插入表格',
      icon: 'mdi-table-plus',
      type: 'button',
      visible: () => true,
      handler: () => {
        console.log('插入表格 - 编辑器状态:', {
          editor: currentEditor.value,
          isEditable: currentEditor.value?.isEditable,
          canInsertTable: currentEditor.value?.can().insertTable(),
        });

        if (!currentEditor.value?.isEditable) {
          console.warn('编辑器当前不可编辑');
          return;
        }

        if (!currentEditor.value?.can().insertTable()) {
          console.warn('当前无法插入表格');
          return;
        }

        currentEditor.value
          ?.chain()
          .focus()
          .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
          .run();
      },
      slashCommand: ({ editor, range }) => {
        console.log('插入表格 - slash命令 - 编辑器状态:', {
          editor,
          isEditable: editor.isEditable,
          canInsertTable: editor.can().insertTable(),
        });

        if (!editor.isEditable) {
          console.warn('编辑器当前不可编辑');
          return;
        }

        if (!editor.can().insertTable()) {
          console.warn('当前无法插入表格');
          return;
        }

        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
          .run();
      },
    },
    addColumnBefore: {
      key: 'addColumnBefore',
      label: () => '在左侧插入列',
      icon: 'mdi-table-column-plus-before',
      type: 'button',
      visible: () => currentEditor.value?.can().addColumnBefore(),
      handler: () => currentEditor.value?.chain().focus().addColumnBefore().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).addColumnBefore().run();
      },
    },
    addColumnAfter: {
      key: 'addColumnAfter',
      label: () => '在右侧插入列',
      icon: 'mdi-table-column-plus-after',
      type: 'button',
      visible: () => currentEditor.value?.can().addColumnAfter(),
      handler: () => currentEditor.value?.chain().focus().addColumnAfter().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).addColumnAfter().run();
      },
    },
    deleteColumn: {
      key: 'deleteColumn',
      label: () => '删除列',
      icon: 'mdi-table-column-remove',
      type: 'button',
      visible: () => currentEditor.value?.can().deleteColumn(),
      handler: () => currentEditor.value?.chain().focus().deleteColumn().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).deleteColumn().run();
      },
    },
    addRowBefore: {
      key: 'addRowBefore',
      label: () => '在上方插入行',
      icon: 'mdi-table-row-plus-before',
      type: 'button',
      visible: () => currentEditor.value?.can().addRowBefore(),
      handler: () => currentEditor.value?.chain().focus().addRowBefore().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).addRowBefore().run();
      },
    },
    addRowAfter: {
      key: 'addRowAfter',
      label: () => '在下方插入行',
      icon: 'mdi-table-row-plus-after',
      type: 'button',
      visible: () => currentEditor.value?.can().addRowAfter(),
      handler: () => currentEditor.value?.chain().focus().addRowAfter().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).addRowAfter().run();
      },
    },
    deleteRow: {
      key: 'deleteRow',
      label: () => '删除行',
      icon: 'mdi-table-row-remove',
      type: 'button',
      visible: () => currentEditor.value?.can().deleteRow(),
      handler: () => currentEditor.value?.chain().focus().deleteRow().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).deleteRow().run();
      },
    },
    deleteTable: {
      key: 'deleteTable',
      label: () => '删除表格',
      icon: 'mdi-table-remove',
      type: 'button',
      visible: () => currentEditor.value?.can().deleteTable(),
      handler: () => currentEditor.value?.chain().focus().deleteTable().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).deleteTable().run();
      },
    },
    toggleHeaderCell: {
      key: 'toggleHeaderCell',
      label: () => '切换表头单元格',
      icon: 'mdi-table-headers-eye',
      type: 'button',
      visible: () => currentEditor.value?.can().toggleHeaderCell(),
      handler: () => currentEditor.value?.chain().focus().toggleHeaderCell().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleHeaderCell().run();
      },
    },
    mergeCells: {
      key: 'mergeCells',
      label: () => '合并单元格',
      icon: 'mdi-table-merge-cells',
      type: 'button',
      visible: () => currentEditor.value?.can().mergeCells(),
      handler: () => currentEditor.value?.chain().focus().mergeCells().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).mergeCells().run();
      },
    },
    splitCell: {
      key: 'splitCell',
      label: () => '拆分单元格',
      icon: 'mdi-table-split-cell',
      type: 'button',
      visible: () => currentEditor.value?.can().splitCell(),
      handler: () => currentEditor.value?.chain().focus().splitCell().run(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).splitCell().run();
      },
    },

    // 图片
    image: {
      key: 'image',
      label: () => '图片',
      icon: 'image',
      type: 'menu',
      description: '图片',
      tooltip: '图片',
      visible: () => true,
      children: ['uploadImage', 'insertImageUrl'],
    },
    uploadImage: {
      key: 'uploadImage',
      label: () => '上传图片',
      type: 'button',
      icon: 'mdi-image-plus',
      visible: () => true,
      handler: () => handleImageUpload(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).run();
        handleImageUpload();
      },
    },
    insertImageUrl: {
      key: 'insertImageUrl',
      label: () => '插入图片链接',
      type: 'button',
      icon: 'mdi-link',
      visible: () => true,
      handler: () => handleImageUrl(),
      slashCommand: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).run();
        handleImageUrl();
      },
    },

    // 更多操作
    more: {
      key: 'more',
      label: () => '更多',
      icon: 'more_vert',
      type: 'menu',
      description: '更多操作',
      tooltip: null,
      visible: () => true,
      children: [
        'clearFormat',
        'toggleAutoComplete',
        'toggleCatalog',
        'toggleSnapshotViewer',
        'toggleEmbeddTitle',
      ],
    },
    clearFormat: {
      key: 'clearFormat',
      label: () => '清除格式',
      description: '清除格式',
      tooltip: '清除格式',
      type: 'button',
      icon: 'mdi-format-clear',
      visible: () => true,
      handler: () => currentEditor.value?.chain().focus().clearNodes().unsetAllMarks().run(),
    },
    toggleCatalog: {
      key: 'toggleCatalog',
      label: () => {
        const isVisible = getCatalogState(instanceKey.value);
        return isVisible ? '隐藏目录' : '显示目录';
      },
      icon: 'mdi-format-list-bulleted',
      type: 'button',
      description: '显示目录',
      tooltip: '显示目录',
      visible: () => true,
      class: () => ({ 'text-primary': getCatalogState(instanceKey.value) }),
      handler: () => {
        toggleCatalog(instanceKey.value);
      },
    },
    addSnapshot: {
      key: 'addSnapshot',
      label: () => '添加快照',
      type: 'button',
      icon: 'mdi-camera-iris',
      visible: () => true,
      handler: () => {
        void createSnapshot(docId, new Date().toISOString());
      },
    },
    toggleSnapshotViewer: {
      key: 'toggleSnapshotViewer',
      label: () => {
        const isVisible = getSnapshotViewerState(instanceKey.value);
        return isVisible ? '隐藏快照列表' : '显示快照列表';
      },
      type: 'button',
      icon: 'mdi-camera-iris',
      visible: () => true,
      class: () => ({ 'text-primary': getSnapshotViewerState(instanceKey.value) }),
      handler: () => {
        toggleSnapshotViewer(instanceKey.value);
      },
    },
    // 自动补全
    toggleAutoComplete: {
      key: 'toggleAutoComplete',
      label: () => {
        const isEnabled = perferences.editor.enableAutoComplete;
        return isEnabled ? '禁用自动补全' : '启用自动补全';
      },
      icon: 'mdi-auto-fix',
      type: 'button',
      description: '自动补全',
      tooltip: '自动补全',
      visible: () => true,
      class: () => ({ 'text-primary': perferences.editor.enableAutoComplete }),
      handler: () => {
        perferences.editor.enableAutoComplete = !perferences.editor.enableAutoComplete;
        console.log(
          '[useCommands] 切换自动补全状态:',
          perferences.editor.enableAutoComplete ? '启用' : '禁用',
        );
      },
    },
    // 查找替换按钮
    searchReplace: {
      key: 'searchReplace',
      label: () => '查找替换',
      icon: 'find_replace',
      type: 'button',
      description: '查找和替换文本',
      tooltip: '查找替换 (Ctrl+F)',
      visible: () => true,
      handler: () => {
        toggleSearchReplacePanel(instanceKey.value);
      },
    },
    // 保存按钮
    save: {
      key: 'save',
      label: () => '保存',
      icon: 'save',
      type: 'button',
      description: '立即保存文档',
      tooltip: '立即保存文档 (Ctrl+S)',
      visible: () => true,
      class: () => ({
        'text-negative': getSaveStatus(docId) === 'error',
        'text-primary': getSaveStatus(docId) === 'saving',
        'text-grey-7': getSaveStatus(docId) === 'saved',
      }),
      handler: () => immediatelySave(),
    },
    toggleEmbeddTitle: {
      key: 'toggleEmbeddTitle',
      label: () => `${uiStore.perferences.editor.enableEmbeddTitle ? '隐藏' : '显示'}嵌入标题`,
      icon: 'mdi-format-title',
      type: 'button',
      description: '嵌入标题',
      tooltip: '嵌入标题',
      visible: () => true,
      class: () => ({ 'text-primary': uiStore.perferences.editor.enableEmbeddTitle }),
      handler: () => {
        uiStore.perferences.editor.enableEmbeddTitle =
          !uiStore.perferences.editor.enableEmbeddTitle;
      },
    },
  };

  interface SlashType {
    group: string;
    displayName: string;
    description: string;
    icon: string;
    items: string[];
  }

  const getToolbarItemsByType = (toolbarType: string) => {
    let res: string[] | SlashType[];
    if (!toolbarType || toolbarType === 'classic') {
      res = [
        'undo',
        'redo',
        'toggleCatalog',
        'separatorx',
        'agentWriter',
        'toggleAutoComplete',
        'separatorx',
        'emoji',
        'blockquote',
        'codeBlock',
        'details',
        'separatorx',
        'heading',
        'color',
        'bold',
        'italic',
        'underline',
        'styleDrawer',
        'align',
        'separatorx',
        'bulletList',
        'orderedList',
        'taskList',
        'separatorx',
        'table',
        'image',
        'mermaid',
        'excalidraw',
        'space',
        'searchReplace',
        'save',
        'more',
      ];
    } else if (toolbarType === 'bubble') {
      res = [
        'agentWriter',
        'separatorx',
        'heading',
        'color',
        'bold',
        'italic',
        'underline',
        'strike',
        'code',
        'highlight',
        'subscript',
        'superscript',
        'align',
        'separatorx',
        'bulletList',
        'orderedList',
        'taskList',
        'table',
        'separatorx',
        'space',
        'more',
      ];
    } else if (toolbarType === 'slash') {
      res = [
        {
          group: 'InkCop',
          displayName: 'InkCop',
          description: 'InkCop 智能体',
          icon: 'auto_awesome',
          items: ['agentWriter'],
        },
        {
          group: 'title',
          displayName: '标题',
          description: '插入标题内容',
          icon: 'mdi-format-title',
          items: ['heading1', 'heading2', 'heading3', 'heading4', 'heading5', 'heading6'],
        },
        {
          group: 'list',
          displayName: '列表',
          description: '插入列表内容',
          icon: 'mdi-format-list-bulleted',
          items: ['bulletList', 'orderedList', 'taskList'],
        },
        {
          group: 'table',
          displayName: '表格',
          description: '插入表格内容',
          icon: 'mdi-table',
          items: ['insertTable'],
        },
        {
          group: 'code',
          displayName: '代码',
          description: '插入代码和图表',
          icon: 'mdi-code-braces',
          items: ['codeBlock', 'mermaid', 'excalidraw'],
        },
        {
          group: 'image',
          displayName: '图片',
          description: '插入图片内容',
          icon: 'mdi-image',
          items: ['uploadImage', 'insertImageUrl'],
        },
      ];
    }
    if (!uiStore.perferences.autoComplete.enabled) {
      res =
        toolbarType === 'slash' ? res : (res as string[]).filter((i) => i !== 'toggleAutoComplete');
    }
    return res;
  };

  // 处理图片上传
  const handleImageUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';

    input.onchange = () => {
      const file = input.files?.[0];
      if (file) {
        try {
          // 读取文件为 base64
          const reader = new FileReader();
          reader.onload = async (e) => {
            const base64 = e.target?.result as string;
            if (base64) {
              // 保存图片到数据库
              const imageId = await saveImage(docId, base64, file.type);
              // 在编辑器中插入图片，使用图片ID作为引用
              currentEditor.value
                ?.chain()
                .focus()
                .insertContent({
                  type: 'image',
                  attrs: {
                    src: `image://${imageId}`,
                    'data-image-id': imageId,
                    alt: file.name,
                  },
                })
                .run();
            }
          };
          reader.readAsDataURL(file);
        } catch (error) {
          console.error('图片上传失败:', error);
        }
      }
    };

    input.click();
  };

  const dropImages = async (files: File[], pos: number) => {
    for (const file of files) {
      try {
        const result = await saveImageWithQt(file);

        if (result.success && result.imageId) {
          // 在编辑器中插入图片，使用图片ID作为引用
          currentEditor.value
            ?.chain()
            .focus()
            .insertContentAt(pos, {
              type: 'image',
              attrs: {
                src: `image://${result.imageId}`,
                'data-image-id': result.imageId,
                alt: file.name,
              },
            })
            .run();
        } else {
          showErrorNotification('图片保存失败', result.error || '未知错误');
        }
      } catch (error) {
        console.error('拖拽图片保存失败:', error);
        showErrorNotification('图片保存失败', error instanceof Error ? error.message : '未知错误');
      }
    }
  };

  // 处理图片URL
  const handleImageUrl = () => {
    const processImageUrl = async (url: string) => {
      try {
        // 统一使用Qt后端下载图片
        await downloadImageWithQtUtil(url);
      } catch (error) {
        console.error('下载图片失败:', error);
        // 显示用户友好的错误提示
        $q.notify({
          type: 'negative',
          message: '图片下载失败',
          caption: error instanceof Error ? error.message : '请检查图片URL是否正确',
          position: 'top',
        });
      }
    };

    // 使用Qt工具函数下载图片
    const downloadImageWithQtUtil = async (url: string) => {
      console.log('使用Qt后端下载图片:', url);

      // 显示加载提示
      const loading = $q.loading.show({
        message: '正在下载图片...',
        boxClass: 'bg-grey-2 text-grey-9',
        spinnerColor: 'primary',
      });

      try {
        // 使用Qt工具函数下载图片
        const result = await downloadImageWithQt({
          docId: docId,
          imageUrl: url,
          timeout: 30000,
        });

        if (result.success && result.imageId && result.localSrc) {
          console.log('Qt后端下载图片成功，图片ID:', result.imageId);

          // 在编辑器中插入图片，使用图片ID作为引用
          currentEditor.value
            ?.chain()
            .focus()
            .insertContent({
              type: 'image',
              attrs: {
                src: result.localSrc,
                'data-image-id': result.imageId,
                alt: url.split('/').pop() || '图片',
              },
            })
            .run();

          // 显示成功提示
          $q.notify({
            type: 'positive',
            message: '图片插入成功',
            position: 'top',
            timeout: 2000,
          });
        } else {
          throw new Error(result.error || '下载失败');
        }
      } finally {
        loading();
      }
    };

    Dialog.create({
      title: '插入图片',
      message: '请输入图片URL',
      class: 'shadow-24 border q-pa-xs',
      prompt: {
        model: '',
        type: 'url',
        dense: true,
        outlined: true,
        placeholder: '请输入图片URL',
      },
      cancel: true,
      persistent: true,
    }).onOk((url) => {
      if (url) {
        void processImageUrl(url);
      }
    });
  };

  // 颜色选择器相关
  const colors = [
    { name: 'primary', value: '#1976D2', label: () => '主要' },
    { name: 'secondary', value: '#26A69A', label: () => '次要' },
    { name: 'accent', value: '#9C27B0', label: () => '强调' },
    { name: 'positive', value: '#21BA45', label: () => '成功' },
    { name: 'negative', value: '#C10015', label: () => '错误' },
    { name: 'info', value: '#31CCEC', label: () => '信息' },
    { name: 'warning', value: '#F2C037', label: () => '警告' },
    { name: 'transparent', value: 'transparent', label: () => '取消颜色' },
  ];

  // 处理颜色选择
  const onColorSelect = (color: string) => {
    if (color === 'transparent') {
      currentEditor.value?.chain().focus().unsetColor().run();
    } else {
      currentEditor.value?.chain().focus().setColor(color).run();
    }
  };

  // 检查颜色是否激活
  const isColorActive = (color: string) => {
    if (color === 'transparent') {
      return !currentEditor.value?.isActive('textStyle');
    }
    return currentEditor.value?.isActive('textStyle', { color });
  };

  const getSlashMenuItems = computed(() => {
    const slashItems = getToolbarItemsByType('slash');
    if (Array.isArray(slashItems) && slashItems.length > 0 && typeof slashItems[0] === 'object') {
      return (slashItems as Array<{ items: string[] }>).flatMap((item) => {
        return item.items.map((i: string) => toolbarItems[i]);
      });
    }
    return [];
  });

  // ==================== 图片处理相关函数 ====================

  // 判断是否为需要下载的HTTP图片URL（排除base64和blob等）
  const isImageUrl = (url: string): boolean => {
    try {
      // 排除base64图片
      if (url.startsWith('data:image/')) {
        return false;
      }

      // 排除blob URL
      if (url.startsWith('blob:')) {
        return false;
      }

      // 只处理HTTP/HTTPS图片URL
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        return false;
      }

      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();
      return (
        /\.(jpg|jpeg|png|gif|webp|bmp|svg)(\?.*)?$/i.test(pathname) ||
        /\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i.test(url)
      );
    } catch {
      return false;
    }
  };

  // 显示加载提示的通用函数
  const showLoadingDialog = (message: string) => {
    return $q.loading.show({
      message,
      boxClass: 'bg-grey-2 text-grey-9',
      spinnerColor: 'primary',
    });
  };

  // 显示成功通知的通用函数
  const showSuccessNotification = (message: string, timeout = 2000) => {
    $q.notify({
      type: 'positive',
      message,
      position: 'top',
      timeout,
    });
  };

  // 显示错误通知的通用函数
  const showErrorNotification = (message: string, caption?: string) => {
    $q.notify({
      type: 'negative',
      message,
      caption,
      position: 'top',
    });
  };

  // 在编辑器中插入图片的通用函数
  const insertImageInEditor = (editor: CoreEditor, imageId: number, alt: string, pos?: number) => {
    console.log('插入图片到编辑器，图片ID:', imageId);

    // 设置图片属性，包括占位符src以避免序列化错误
    const imageAttrs = {
      'data-image-id': imageId,
      alt,
      // 提供占位符src，避免复制时出现null错误
      src: `image://${imageId}`,
    };

    if (pos !== undefined) {
      // 在指定位置插入
      editor
        .chain()
        .focus()
        .insertContentAt(pos, {
          type: 'image',
          attrs: imageAttrs,
        })
        .run();
    } else {
      // 在当前光标位置插入
      editor
        .chain()
        .focus()
        .insertContent({
          type: 'image',
          attrs: imageAttrs,
        })
        .run();
    }
  };

  // 处理拖拽或选择的图片文件
  const attachImage = async (currentEditor: CoreEditor, file: File, pos: number) => {
    try {
      const result = await saveImageWithQt(file);

      if (result.success && result.imageId) {
        // 在编辑器中插入图片，使用blob地址
        insertImageInEditor(currentEditor, result.imageId, file.name, pos);

        // 插入图片后立即保存
        immediatelySave();
      } else {
        showErrorNotification('图片保存失败', result.error || '未知错误');
      }
    } catch (error) {
      console.error('保存图片失败:', error);
      showErrorNotification('图片保存失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  // 使用Qt后端保存图片文件的通用函数
  const saveImageWithQt = async (
    file: File,
  ): Promise<{ success: boolean; imageId?: number; error?: string }> => {
    // 检查是否在Qt环境中
    if (typeof window === 'undefined' || !window.databaseApi) {
      return {
        success: false,
        error: 'Qt环境不可用，无法保存图片',
      };
    }

    return new Promise((resolve) => {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        const base64 = e.target?.result as string;
        if (base64) {
          console.log('使用Qt后端保存图片文件');

          // 使用Qt后端保存图片
          window.databaseApi.saveImage(
            docId,
            base64,
            file.type,
            (result: { success: boolean; id?: number; error?: string }) => {
              if (result.success && result.id) {
                console.log('Qt后端保存图片成功，图片ID:', result.id);
                resolve({
                  success: true,
                  imageId: result.id,
                });
              } else {
                console.error('Qt后端保存图片失败:', result.error);
                resolve({
                  success: false,
                  error: result.error || '保存失败',
                });
              }
            },
          );
        } else {
          resolve({
            success: false,
            error: '无法读取图片文件',
          });
        }
      };
      fileReader.readAsDataURL(file);
    });
  };

  // 处理粘贴的图片文件
  const handlePastedImage = async (file: File, isSnapshot = false) => {
    if (isSnapshot) return;

    const loading = showLoadingDialog('正在保存图片...');

    try {
      const result = await saveImageWithQt(file);

      if (result.success && result.imageId) {
        // 在编辑器当前光标位置插入图片
        if (currentEditor.value) {
          insertImageInEditor(currentEditor.value, result.imageId, file.name || '粘贴的图片');
        }

        // 插入图片后立即保存
        immediatelySave();

        // 显示成功提示
        showSuccessNotification('图片粘贴成功');
      } else {
        showErrorNotification('图片粘贴失败', result.error || '保存图片时发生错误');
      }
    } catch (error) {
      console.error('粘贴图片失败:', error);
      showErrorNotification(
        '图片粘贴失败',
        error instanceof Error ? error.message : '处理图片时发生错误',
      );
    } finally {
      loading();
    }
  };

  // 将base64图片转换为blob URL
  const convertBase64ToBlob = (base64Url: string): string | null => {
    try {
      // 检查是否为base64格式
      if (!base64Url.startsWith('data:image/')) {
        return null;
      }

      // 提取MIME类型和base64数据
      const [header, data] = base64Url.split(',');
      if (!header || !data) {
        return null;
      }

      const mimeMatch = header.match(/data:([^;]+)/);
      if (!mimeMatch) {
        return null;
      }

      const mimeType = mimeMatch[1];

      // 将base64转换为二进制数据
      const binaryString = atob(data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // 创建Blob对象
      const blob = new Blob([bytes], { type: mimeType });

      // 创建blob URL
      const blobUrl = URL.createObjectURL(blob);
      return blobUrl;
    } catch (error) {
      console.error('转换base64为blob失败:', error);
      return null;
    }
  };

  // 从Qt后端获取图片的本地文件URL
  const getImageAsFileUrl = async (imageId: number): Promise<string | null> => {
    try {
      // 导入blob URL映射函数
      const { registerBlobUrlMapping } = await import('src/utils/blobUrlMapping');

      // 检查是否在Qt环境中
      if (typeof window === 'undefined' || !window.databaseApi) {
        console.error('Qt环境不可用，无法获取图片');
        return null;
      }

      // 使用Qt API获取图片数据
      const result = await new Promise<{
        success: boolean;
        file_url?: string;
        data?: string;
        mime_type?: string;
        error?: string;
      }>((resolve) => {
        window.databaseApi.getImage(
          imageId,
          (result: {
            success: boolean;
            file_url?: string;
            data?: string;
            mime_type?: string;
            error?: string;
          }) => {
            resolve(result);
          },
        );
      });

      if (result.success) {
        // 检查是否在开发环境（localhost）
        const isDevelopment =
          window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

        console.log(
          `[getImageAsFileUrl] 图片ID: ${imageId}, 环境: ${isDevelopment ? '开发' : '生产'}, hostname: ${window.location.hostname}`,
        );
        console.log(`[getImageAsFileUrl] Qt返回数据:`, {
          hasFileUrl: !!result.file_url,
          hasData: !!result.data,
          hasMimeType: !!result.mime_type,
          fileUrl: result.file_url?.substring(0, 50) + '...',
          dataLength: result.data?.length || 0,
        });

        // 在开发环境下，优先使用blob URL避免CORS问题
        if (isDevelopment && result.data && result.mime_type) {
          console.log(`[getImageAsFileUrl] 开发环境下使用blob URL显示图片 ID ${imageId}`);
          const base64Url = `data:${result.mime_type};base64,${result.data}`;
          const blobUrl = convertBase64ToBlob(base64Url);
          if (blobUrl) {
            registerBlobUrlMapping(blobUrl, imageId);
            console.log(`[getImageAsFileUrl] 成功创建blob URL: ${blobUrl.substring(0, 50)}...`);
            return blobUrl;
          } else {
            console.error(`[getImageAsFileUrl] 创建blob URL失败`);
          }
        }

        // 在生产环境下，优先返回本地文件URL
        if (result.file_url && !isDevelopment) {
          console.log(
            `[getImageAsFileUrl] 生产环境下使用file URL显示图片 ID ${imageId}: ${result.file_url}`,
          );
          return result.file_url;
        }

        // 降级处理：如果没有base64数据但有file_url，仍然尝试使用file_url
        if (result.file_url) {
          console.log(
            `[getImageAsFileUrl] 降级使用file URL显示图片 ID ${imageId}: ${result.file_url}`,
          );
          return result.file_url;
        }

        // 最后的降级：使用blob URL（向后兼容）
        if (result.data && result.mime_type) {
          console.log(`[getImageAsFileUrl] 最后降级使用blob URL显示图片 ID ${imageId}`);
          const base64Url = `data:${result.mime_type};base64,${result.data}`;
          const blobUrl = convertBase64ToBlob(base64Url);
          if (blobUrl) {
            registerBlobUrlMapping(blobUrl, imageId);
            return blobUrl;
          }
        }

        console.error(`[getImageAsFileUrl] 无法为图片 ID ${imageId} 生成任何可用的URL`);
      }

      return null;
    } catch (error) {
      console.error(`获取图片失败 ID ${imageId}:`, error);
      return null;
    }
  };

  // 递归处理文档内容，将所有base64图片转换为blob URL，image://id格式转换为本地文件URL
  const convertImagesInContent = async (content: unknown): Promise<unknown> => {
    if (!content || typeof content !== 'object') {
      return content;
    }

    // 如果是数组，递归处理每个元素
    if (Array.isArray(content)) {
      const processedArray = await Promise.all(content.map((item) => convertImagesInContent(item)));
      return processedArray;
    }

    // 如果是图片节点，检查并转换src
    if (
      typeof content === 'object' &&
      content !== null &&
      'type' in content &&
      content.type === 'image' &&
      'attrs' in content &&
      typeof content.attrs === 'object' &&
      content.attrs !== null &&
      'src' in content.attrs &&
      typeof content.attrs.src === 'string'
    ) {
      const src = content.attrs.src;

      // 处理base64图片
      if (src.startsWith('data:image/')) {
        const blobUrl = convertBase64ToBlob(src);
        if (blobUrl) {
          return {
            ...content,
            attrs: {
              ...content.attrs,
              src: blobUrl,
            },
          };
        }
      }

      // 处理image://id格式的图片
      if (src.startsWith('image://')) {
        const imageId = parseInt(src.replace('image://', ''));
        if (!isNaN(imageId)) {
          const fileUrl = await getImageAsFileUrl(imageId);
          if (fileUrl) {
            return {
              ...content,
              attrs: {
                ...content.attrs,
                src: fileUrl,
                'data-image-id': imageId, // 保留图片ID用于删除等操作
              },
            };
          }
        }
      }

      // 处理file://格式的图片（向后兼容旧数据）
      if (src.startsWith('file://')) {
        console.log('[convertImagesInContent] 发现file://格式图片，尝试转换为blob URL:', src);

        // 尝试从file路径中提取图片ID
        // 路径格式通常为: file:///C:/Users/<USER>/images/2025-07-02/1234567890.jpg
        const pathMatch = src.match(/\/(\d+)\.(?:jpg|jpeg|png|gif|webp)$/i);
        if (pathMatch) {
          const imageId = parseInt(pathMatch[1]);
          if (!isNaN(imageId)) {
            console.log('[convertImagesInContent] 从file路径提取到图片ID:', imageId);
            const blobUrl = await getImageAsFileUrl(imageId);
            if (blobUrl) {
              console.log('[convertImagesInContent] 成功转换file://为blob URL:', blobUrl);
              return {
                ...content,
                attrs: {
                  ...content.attrs,
                  src: blobUrl,
                  'data-image-id': imageId, // 保留图片ID用于删除等操作
                },
              };
            }
          }
        }

        console.warn('[convertImagesInContent] 无法从file路径提取图片ID，保持原样:', src);
      }
    }

    // 递归处理对象的所有属性
    const result: Record<string, unknown> = {};
    for (const key in content) {
      if (Object.prototype.hasOwnProperty.call(content, key)) {
        result[key] = await convertImagesInContent((content as Record<string, unknown>)[key]);
      }
    }

    return result;
  };

  // 保持向后兼容的同步版本（仅处理base64）
  const convertBase64ImagesInContent = (content: unknown): unknown => {
    if (!content || typeof content !== 'object') {
      return content;
    }

    // 如果是数组，递归处理每个元素
    if (Array.isArray(content)) {
      return content.map((item) => convertBase64ImagesInContent(item));
    }

    // 如果是图片节点，检查并转换src
    if (
      typeof content === 'object' &&
      content !== null &&
      'type' in content &&
      content.type === 'image' &&
      'attrs' in content &&
      typeof content.attrs === 'object' &&
      content.attrs !== null &&
      'src' in content.attrs &&
      typeof content.attrs.src === 'string'
    ) {
      const src = content.attrs.src;
      if (src.startsWith('data:image/')) {
        const blobUrl = convertBase64ToBlob(src);
        if (blobUrl) {
          console.log('文档加载时转换base64图片为blob URL');
          return {
            ...content,
            attrs: {
              ...content.attrs,
              src: blobUrl,
            },
          };
        }
      }
    }

    // 递归处理对象的所有属性
    const result: Record<string, unknown> = {};
    for (const key in content) {
      if (Object.prototype.hasOwnProperty.call(content, key)) {
        result[key] = convertBase64ImagesInContent((content as Record<string, unknown>)[key]);
      }
    }

    return result;
  };

  // 处理粘贴的图片URL
  const handlePastedImageUrl = async (imageUrl: string, isSnapshot = false) => {
    if (isSnapshot) return;

    // 检查是否为base64图片
    if (imageUrl.startsWith('data:image/')) {
      console.log('检测到base64图片，需要保存到后端:', imageUrl.substring(0, 50) + '...');

      try {
        // 将base64图片保存到后端
        const { imageManager } = await import('src/utils/imageManager');

        // 提取MIME类型和base64数据
        const [header, data] = imageUrl.split(',');
        const mimeMatch = header.match(/data:([^;]+)/);
        const mimeType = mimeMatch ? mimeMatch[1] : 'image/png';

        const result = await imageManager.saveImageFromData(data, mimeType, imageUrl);

        if (result.success && result.imageId && docId !== undefined) {
          // 使用新的主动关联方法（简化版本，不等待结果）
          imageManager.associateImageWithDocument(result.imageId, docId);

          // 使用图片ID插入图片
          if (currentEditor.value) {
            insertImageInEditor(currentEditor.value, result.imageId, '粘贴的图片');
          }

          // 插入图片后立即保存
          immediatelySave();
          showSuccessNotification('图片粘贴成功');
          return;
        } else {
          throw new Error(result.error || '保存图片失败');
        }
      } catch (error) {
        console.error('保存base64图片失败:', error);
        showErrorNotification(
          '图片粘贴失败',
          error instanceof Error ? error.message : '保存图片失败',
        );
        return;
      }
    }

    // 处理blob URL（文档间复制的情况）
    if (imageUrl.startsWith('blob:')) {
      console.log('检测到blob URL，尝试文档间复制处理:', imageUrl);
      const handled = await handleCrossDocumentImagePaste(imageUrl);
      if (!handled) {
        showErrorNotification('图片粘贴失败', '无法处理该图片，可能来自其他应用');
      }
      return;
    }

    // 只对http/https开头的图片URL使用Qt后端下载
    if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
      console.log('非HTTP图片URL，跳过处理:', imageUrl);
      showErrorNotification('图片粘贴失败', '不支持的图片URL格式');
      return;
    }

    // 检查是否在Qt环境中
    if (typeof window === 'undefined' || !window.databaseApi) {
      console.error('Qt环境不可用，无法下载粘贴的图片URL');
      return;
    }

    const loading = showLoadingDialog('正在下载图片...');

    try {
      console.log('使用Qt后端下载粘贴的HTTP图片URL:', imageUrl);

      // 使用Qt工具函数下载图片
      const result = await downloadImageWithQt({
        docId,
        imageUrl,
        timeout: 30000,
      });

      if (result.success && result.imageId && result.localSrc) {
        console.log('Qt后端下载粘贴图片成功，图片ID:', result.imageId);

        // 在编辑器当前光标位置插入图片
        if (currentEditor.value) {
          insertImageInEditor(
            currentEditor.value,
            result.imageId,
            imageUrl.split('/').pop() || '粘贴的图片',
          );
        }

        // 插入图片后立即保存
        immediatelySave();

        // 显示成功提示
        showSuccessNotification('图片粘贴成功');
      } else {
        console.error('Qt后端下载粘贴图片失败:', result.error);
        showErrorNotification('图片粘贴失败', result.error || '下载图片时发生错误');
        throw new Error(result.error || '下载失败');
      }
    } catch (error) {
      console.error('粘贴图片URL失败:', error);
      showErrorNotification(
        '图片粘贴失败',
        error instanceof Error ? error.message : '处理图片URL时发生错误',
      );
    } finally {
      loading();
    }
  };

  // 用于防抖的处理队列
  const processingHttpImages = ref(new Set<string>());
  let httpImageProcessTimeout: NodeJS.Timeout | null = null;

  // 检查并处理编辑器中的HTTP图片链接
  const checkAndProcessHttpImages = (currentEditor: CoreEditor, isSnapshot = false) => {
    if (isSnapshot) return;

    // 检查是否在Qt环境中
    if (typeof window === 'undefined' || !window.databaseApi) {
      return;
    }

    // 防抖处理：清除之前的定时器，设置新的定时器
    if (httpImageProcessTimeout) {
      clearTimeout(httpImageProcessTimeout);
    }

    httpImageProcessTimeout = setTimeout(() => {
      void (async () => {
        try {
          // 获取编辑器DOM中的所有img标签
          const editorDom = currentEditor.view.dom;
          const imgElements = editorDom.querySelectorAll('img[src^="http"]');

          if (imgElements.length === 0) {
            return;
          }

          console.log(`发现 ${imgElements.length} 个HTTP图片链接，开始处理...`);

          // 收集需要处理的图片URL，去重并过滤已在处理中的
          const urlsToProcess: string[] = [];

          for (const imgElement of imgElements) {
            const httpUrl = imgElement.getAttribute('src');
            if (!httpUrl || !httpUrl.startsWith('http')) {
              continue;
            }

            // 检查是否已经在处理中，避免重复处理
            if (processingHttpImages.value.has(httpUrl)) {
              console.log('图片已在处理中，跳过:', httpUrl);
              continue;
            }

            // 去重处理
            if (!urlsToProcess.includes(httpUrl)) {
              urlsToProcess.push(httpUrl);
            }
          }

          if (urlsToProcess.length === 0) {
            console.log('没有需要处理的新图片');
            return;
          }

          // 标记所有URL为处理中
          urlsToProcess.forEach((url) => processingHttpImages.value.add(url));

          try {
            // 使用批量下载功能
            const downloadOptions = urlsToProcess.map((url) => ({
              docId,
              imageUrl: url,
              timeout: 30000,
            }));

            console.log(`开始批量下载 ${downloadOptions.length} 个图片...`);
            const results = await downloadMultipleImagesWithQt(downloadOptions, 3, 300);

            let successCount = 0;

            // 处理下载结果，使用异步处理以获取blob URL
            for (let index = 0; index < results.length; index++) {
              const result = results[index];
              const httpUrl = urlsToProcess[index];

              if (result.success && result.imageId) {
                console.log('Qt后端下载HTTP图片成功，图片ID:', result.imageId, 'URL:', httpUrl);

                // 获取图片的文件URL
                const fileUrl = await getImageAsFileUrl(result.imageId);
                if (fileUrl) {
                  // 在编辑器JSON中更新对应的节点
                  currentEditor.state.doc.descendants((node, pos) => {
                    if (node.type.name === 'image' && node.attrs.src === httpUrl) {
                      const tr = currentEditor.state.tr;
                      tr.setNodeMarkup(pos, undefined, {
                        ...node.attrs,
                        src: fileUrl,
                        'data-image-id': result.imageId,
                      });
                      currentEditor.view.dispatch(tr);
                      return false; // 停止遍历
                    }
                    return true;
                  });

                  successCount++;
                } else {
                  console.error(`无法获取图片文件URL，图片ID: ${result.imageId}`);
                }
              } else {
                console.error(`Qt后端下载HTTP图片失败 ${httpUrl}:`, result.error);
              }
            }

            console.log(`批量下载完成: ${successCount}/${results.length} 成功`);

            // 更新成功计数
            if (successCount > 0) {
              // 如果有图片被成功处理，保存文档并显示提示
              console.log(`成功处理 ${successCount}/${urlsToProcess.length} 个HTTP图片`);

              // 处理完成后保存文档
              immediatelySave();

              // 显示成功提示
              showSuccessNotification(`已将 ${successCount} 个网络图片保存到本地`, 3000);
            }
          } catch (error) {
            console.error('批量下载HTTP图片时发生错误:', error);
          } finally {
            // 移除所有处理标记
            urlsToProcess.forEach((url) => processingHttpImages.value.delete(url));
          }
        } catch (error) {
          console.error('检查HTTP图片时发生错误:', error);
        }
      })();
    }, 500); // 500ms防抖延迟
  };

  // 处理文档间图片复制的特殊逻辑
  const handleCrossDocumentImagePaste = async (imageUrl: string): Promise<boolean> => {
    // 导入独立的blob URL映射管理器
    const { getImageIdFromBlobUrl, isBlobUrlFromCurrentApp, registerBlobUrlMapping } = await import(
      'src/utils/blobUrlMapping'
    );
    console.log('处理文档间图片复制:', imageUrl);

    // 对于data:image/格式的base64图片，直接返回false让handlePastedImageUrl处理
    if (imageUrl.startsWith('data:image/')) {
      console.log('检测到base64图片，交由handlePastedImageUrl处理');
      return false;
    }

    // 检查是否是来自当前应用的blob URL
    const isFromCurrentApp = isBlobUrlFromCurrentApp(imageUrl);
    console.log('blob URL检查结果:', {
      imageUrl,
      isFromCurrentApp,
      windowOrigin: window.location.origin,
      startsWithBlob: imageUrl.startsWith('blob:'),
      includesOrigin: imageUrl.includes(window.location.origin),
    });

    if (isFromCurrentApp) {
      console.log('检测到来自当前应用的blob URL');

      let imageId: number | null = null;

      // 尝试从映射中获取图片ID
      imageId = getImageIdFromBlobUrl(imageUrl);
      console.log('从blob映射中获取的图片ID:', imageId);

      // 如果还没有找到图片ID，尝试从DOM中查找
      if (!imageId) {
        console.log('尝试从DOM中查找图片ID');

        // 查找所有具有相同src的图片元素
        const imgElements = document.querySelectorAll(`img[src="${CSS.escape(imageUrl)}"]`);
        console.log('找到的图片元素数量:', imgElements.length);

        for (const imgElement of imgElements) {
          const dataImageId = imgElement.getAttribute('data-image-id');
          console.log('图片元素的data-image-id:', dataImageId);

          if (dataImageId) {
            imageId = parseInt(dataImageId);
            // 重新注册映射
            registerBlobUrlMapping(imageUrl, imageId);
            console.log('重新注册blob映射，图片ID:', imageId);
            break;
          }
        }

        // 如果还是没找到，尝试查找具有data-image-id但没有src的图片元素
        if (!imageId) {
          console.log('尝试查找没有src的图片元素');
          const imgElementsWithoutSrc = document.querySelectorAll('img[data-image-id]:not([src])');
          console.log('找到没有src的图片元素数量:', imgElementsWithoutSrc.length);

          // 这里可以根据其他属性（如alt、title等）来匹配
          // 暂时跳过这种情况
        }
      }

      if (imageId) {
        console.log('找到图片ID，准备插入图片:', imageId);

        // 使用图片ID重新插入图片（不设置src，让NodeView动态加载）
        if (currentEditor.value) {
          insertImageInEditor(currentEditor.value, imageId, '复制的图片');
          console.log('图片已插入到编辑器');
        }

        // 立即主动关联图片与文档，不依赖文档保存
        try {
          if (docId !== undefined) {
            const { imageManager } = await import('src/utils/imageManager');
            // 使用新的主动关联方法（简化版本，不等待结果）
            imageManager.associateImageWithDocument(imageId, docId);

            console.log('图片关联事件已触发，现在保存文档');

            // 触发关联后，保存文档
            immediatelySave();

            showSuccessNotification('图片复制成功');
            return true;
          } else {
            throw new Error('文档ID无效');
          }
        } catch (error) {
          console.error('图片关联失败:', error);
          showErrorNotification('图片复制失败', '无法建立图片关联');

          // 如果关联失败，从编辑器中移除图片
          if (currentEditor.value) {
            const { tr } = currentEditor.value.state;
            let imageRemoved = false;

            currentEditor.value.state.doc.descendants((node, pos) => {
              if (node.type.name === 'image' && node.attrs['data-image-id'] === imageId) {
                tr.delete(pos, pos + node.nodeSize);
                imageRemoved = true;
                return false; // 停止遍历
              }
            });

            if (imageRemoved) {
              currentEditor.value.view.dispatch(tr);
              console.log('已从编辑器中移除失败的图片');
            }
          }

          return false;
        }
      } else {
        console.log('无法获取图片ID，将尝试其他处理方式');
      }
    }

    return false;
  };

  /**
   * 统一处理粘贴内容中的图片（由后端处理所有图片下载）
   */
  const processImagesInPastedContent = async (htmlContent: string): Promise<string> => {
    console.log('[统一图片处理] 开始处理粘贴内容中的图片');

    // 查找所有图片标签
    const imgMatches = Array.from(htmlContent.matchAll(/<img[^>]*src=["']([^"']+)["'][^>]*>/gi));

    if (imgMatches.length === 0) {
      console.log('[统一图片处理] 没有找到图片，返回原内容');
      return htmlContent;
    }

    console.log(`[统一图片处理] 找到 ${imgMatches.length} 个图片，开始处理`);

    let processedContent = htmlContent;

    for (const match of imgMatches) {
      const fullImgTag = match[0];
      const imageUrl = match[1];

      console.log(`[统一图片处理] 处理图片: ${imageUrl}`);

      // 跳过已经是我们系统的图片（有data-image-id属性）
      if (fullImgTag.includes('data-image-id')) {
        console.log(`[统一图片处理] 跳过系统内部图片: ${imageUrl}`);
        continue;
      }

      // 跳过data:image格式（这些由其他处理器处理）
      if (imageUrl.startsWith('data:image/')) {
        console.log(`[统一图片处理] 跳过base64图片: ${imageUrl.substring(0, 50)}...`);
        continue;
      }

      // 跳过blob URL（这些由其他处理器处理）
      if (imageUrl.startsWith('blob:')) {
        console.log(`[统一图片处理] 跳过blob图片: ${imageUrl}`);
        continue;
      }

      // 检查是否是文档间复制的图片
      const crossDocHandled = await handleCrossDocumentImagePaste(imageUrl);
      if (crossDocHandled) {
        console.log(`[统一图片处理] 文档间复制处理完成: ${imageUrl}`);
        // 移除这个图片标签，因为已经通过文档间复制处理了
        processedContent = processedContent.replace(fullImgTag, '');
        continue;
      }

      // 使用Qt后端下载网络图片
      try {
        console.log(`[统一图片处理] 使用Qt后端下载图片: ${imageUrl}`);

        const result = await downloadImageWithQt({
          docId,
          imageUrl,
          timeout: 30000,
        });

        if (result.success && result.imageId) {
          console.log(`[统一图片处理] 图片下载成功，ID: ${result.imageId}`);

          // 替换原图片标签为我们的图片标签
          const newImgTag = `<img data-image-id="${result.imageId}" alt="${imageUrl.split('/').pop() || '图片'}" />`;
          processedContent = processedContent.replace(fullImgTag, newImgTag);

          console.log(`[统一图片处理] 图片标签已替换: ${imageUrl} -> ID: ${result.imageId}`);
        } else {
          console.error(`[统一图片处理] 图片下载失败: ${imageUrl}`, result.error);
          // 保留原图片标签，但添加错误标记
          const errorImgTag = `<img src="${imageUrl}" alt="图片加载失败" style="border: 2px solid red;" />`;
          processedContent = processedContent.replace(fullImgTag, errorImgTag);
        }
      } catch (error) {
        console.error(`[统一图片处理] 处理图片时发生错误: ${imageUrl}`, error);
        // 保留原图片标签，但添加错误标记
        const errorImgTag = `<img src="${imageUrl}" alt="图片处理错误" style="border: 2px solid red;" />`;
        processedContent = processedContent.replace(fullImgTag, errorImgTag);
      }
    }

    console.log('[统一图片处理] 图片处理完成');
    return processedContent;
  };

  /**
   * 延迟处理粘贴后的图片（处理CORS图片）
   */
  const processImagesAfterPaste = async (): Promise<void> => {
    console.log('[延迟图片处理] 开始扫描编辑器中的图片');

    if (!currentEditor.value) {
      console.log('[延迟图片处理] 编辑器不可用');
      return;
    }

    const editorDom = currentEditor.value.view.dom;

    // 扫描所有图片元素，不仅仅是http开头的
    const allImgElements = editorDom.querySelectorAll('img');
    console.log(`[延迟图片处理] 找到总共 ${allImgElements.length} 个图片元素`);

    // 先打印所有图片元素的详细信息
    console.log('[延迟图片处理] 所有图片元素详情:');
    Array.from(allImgElements).forEach((img, index) => {
      const src = img.getAttribute('src');
      const dataImageId = img.getAttribute('data-image-id');
      const alt = img.getAttribute('alt');
      console.log(`[延迟图片处理] 图片 ${index + 1}:`, {
        src: src ? src.substring(0, 100) + (src.length > 100 ? '...' : '') : 'null',
        dataImageId,
        alt,
        hasDataImageId: img.hasAttribute('data-image-id'),
        startsWithHttp: src ? src.startsWith('http') : false,
        startsWithBlob: src ? src.startsWith('blob:') : false,
        startsWithData: src ? src.startsWith('data:') : false,
      });
    });

    // 过滤出需要处理的图片（网络图片或blob URL）
    const imagesToProcess = Array.from(allImgElements).filter((img) => {
      const src = img.getAttribute('src');
      if (!src) return false;

      // 检查是否是网络图片、blob URL且未处理过
      const isNetworkImage = src.startsWith('http://') || src.startsWith('https://');
      const isBlobUrl = src.startsWith('blob:');
      const isProcessed = img.hasAttribute('data-image-id');

      console.log(
        `[延迟图片处理] 图片: ${src.substring(0, 50)}... 网络图片: ${isNetworkImage}, blob URL: ${isBlobUrl}, 已处理: ${isProcessed}`,
      );

      return (isNetworkImage || isBlobUrl) && !isProcessed;
    });

    if (imagesToProcess.length === 0) {
      console.log('[延迟图片处理] 没有找到需要处理的图片');
      return;
    }

    console.log(`[延迟图片处理] 找到 ${imagesToProcess.length} 个需要处理的图片`);

    let hasProcessedImages = false;

    for (const imgElement of imagesToProcess) {
      const imageUrl = imgElement.getAttribute('src');
      if (!imageUrl) continue;

      // 跳过已经处理过的图片
      if (imgElement.hasAttribute('data-image-id')) {
        console.log(`[延迟图片处理] 跳过已处理的图片: ${imageUrl}`);
        continue;
      }

      console.log(`[延迟图片处理] 处理图片: ${imageUrl}`);

      try {
        // 对于blob URL，优先尝试文档间复制处理
        if (imageUrl.startsWith('blob:')) {
          console.log(`[延迟图片处理] 检测到blob URL，尝试文档间复制处理: ${imageUrl}`);
          console.log(`[延迟图片处理] 当前window.location.origin: ${window.location.origin}`);

          const crossDocHandled = await handleCrossDocumentImagePaste(imageUrl);
          if (crossDocHandled) {
            console.log(`[延迟图片处理] blob URL文档间复制处理完成: ${imageUrl}`);
            // 移除这个图片元素，因为已经通过文档间复制处理了
            imgElement.remove();
            hasProcessedImages = true;
            continue;
          } else {
            console.log(`[延迟图片处理] blob URL无法通过文档间复制处理，跳过: ${imageUrl}`);
            continue;
          }
        }

        // 对于HTTP/HTTPS URL，检查是否是文档间复制的图片
        const crossDocHandled = await handleCrossDocumentImagePaste(imageUrl);
        if (crossDocHandled) {
          console.log(`[延迟图片处理] 文档间复制处理完成: ${imageUrl}`);
          // 移除这个图片元素，因为已经通过文档间复制处理了
          imgElement.remove();
          hasProcessedImages = true;
          continue;
        }

        // 使用Qt后端下载网络图片
        console.log(`[延迟图片处理] 使用Qt后端下载图片: ${imageUrl}`);

        const result = await downloadImageWithQt({
          docId,
          imageUrl,
          timeout: 30000,
        });

        console.log(`[延迟图片处理] 下载结果详情:`, {
          success: result.success,
          imageId: result.imageId,
          localSrc: result.localSrc,
          error: result.error,
          fullResult: result,
        });

        if (result.success && result.imageId) {
          console.log(`[延迟图片处理] 图片下载成功，ID: ${result.imageId}`);

          // 获取图片的blob URL
          try {
            const fileUrl = await getImageAsFileUrl(result.imageId);
            if (fileUrl) {
              // 更新图片元素
              imgElement.setAttribute('data-image-id', result.imageId.toString());
              imgElement.setAttribute('src', fileUrl);
              imgElement.setAttribute('alt', imageUrl.split('/').pop() || '图片');

              // 更新编辑器内部状态
              if (currentEditor.value) {
                const { tr } = currentEditor.value.state;
                let updated = false;

                currentEditor.value.state.doc.descendants((node, pos) => {
                  if (node.type.name === 'image' && node.attrs.src === imageUrl) {
                    tr.setNodeMarkup(pos, undefined, {
                      ...node.attrs,
                      'data-image-id': result.imageId,
                      src: undefined, // 移除src，让NodeView动态加载
                    });
                    updated = true;
                  }
                });

                if (updated) {
                  currentEditor.value.view.dispatch(tr);
                  console.log(
                    `[延迟图片处理] 编辑器状态已更新: ${imageUrl} -> ID: ${result.imageId}`,
                  );
                }
              }

              console.log(
                `[延迟图片处理] 图片元素已更新: ${imageUrl} -> ID: ${result.imageId}, src: ${fileUrl}`,
              );
            } else {
              console.error(`[延迟图片处理] 无法获取图片文件URL，图片ID: ${result.imageId}`);
              // 设置错误样式
              imgElement.style.border = '2px solid red';
              imgElement.setAttribute('alt', '图片加载失败');
            }
          } catch (error) {
            console.error(`[延迟图片处理] 获取图片文件URL时发生错误:`, error);
            // 设置错误样式
            imgElement.style.border = '2px solid red';
            imgElement.setAttribute('alt', '图片加载失败');
          }

          hasProcessedImages = true;
        } else {
          console.error(`[延迟图片处理] 图片下载失败: ${imageUrl}`, result.error);
          console.error(
            `[延迟图片处理] 失败详情: success=${result.success}, imageId=${result.imageId}`,
          );
          // 添加错误样式
          imgElement.style.border = '2px solid red';
          imgElement.setAttribute('alt', '图片加载失败');
        }
      } catch (error) {
        console.error(`[延迟图片处理] 处理图片时发生错误: ${imageUrl}`, error);
        // 添加错误样式
        imgElement.style.border = '2px solid red';
        imgElement.setAttribute('alt', '图片处理错误');
      }
    }

    if (hasProcessedImages) {
      console.log('[延迟图片处理] 图片处理完成，保存文档');
      immediatelySave();
      showSuccessNotification('图片处理完成');
    }

    console.log('[延迟图片处理] 处理完成');
  };

  // 处理粘贴事件
  const handlePaste = (_view: unknown, event: ClipboardEvent, isSnapshot = false) => {
    if (isSnapshot) return false;

    // 处理粘贴图片
    const clipboardData = event.clipboardData;
    if (!clipboardData) {
      console.log('没有剪贴板数据');
      return false;
    }

    console.log('剪贴板数据:', clipboardData);
    console.log('剪贴板类型:', clipboardData.types);

    // 优先检查图片文件
    const items = Array.from(clipboardData.items);
    const imageItem = items.find((item) => item.type.startsWith('image/'));

    if (imageItem) {
      console.log('处理图片文件粘贴');
      event.preventDefault();

      const file = imageItem.getAsFile();
      if (file) {
        console.log('获取到图片文件:', file);
        void handlePastedImage(file, isSnapshot);
      }
      return true;
    }

    // 检查剪贴板类型，决定是否需要延迟处理
    const htmlData = clipboardData.getData('text/html');
    const hasUriList = clipboardData.types.includes('text/uri-list');
    const hasTextPlain = clipboardData.getData('text/plain');

    console.log('[延迟图片处理] 剪贴板类型:', clipboardData.types);
    console.log('[延迟图片处理] HTML数据:', htmlData ? htmlData.substring(0, 200) + '...' : '无');
    console.log('[延迟图片处理] URI列表:', hasUriList);

    // 检查是否可能包含图片内容（更宽泛的检测）
    const mightContainImages =
      htmlData &&
      (htmlData.includes('<img') ||
        htmlData.includes('image') ||
        htmlData.includes('src=') ||
        // 检查是否有可能的图片相关内容
        /\.(jpg|jpeg|png|gif|webp|svg)/i.test(htmlData) ||
        // 检查是否有base64图片
        htmlData.includes('data:image') ||
        // 检查是否有可能的网络图片URL
        /https?:\/\/[^\s<>"]+\.(jpg|jpeg|png|gif|webp|svg)/i.test(htmlData));

    // 检查URI列表是否包含图片
    const uriListMightContainImages =
      hasUriList &&
      (() => {
        const uriList = clipboardData.getData('text/uri-list');
        if (uriList) {
          const urls = uriList.split('\n').filter((url) => url.trim() && !url.startsWith('#'));
          return urls.some((url) => isImageUrl(url.trim()));
        }
        return false;
      })();

    // 如果可能包含图片，设置延迟处理
    if (mightContainImages || uriListMightContainImages || hasUriList) {
      console.log('[延迟图片处理] 检测到可能包含图片的内容，设置延迟处理');
      console.log(
        '[延迟图片处理] 触发原因: HTML图片=' +
          mightContainImages +
          ', URI图片=' +
          uriListMightContainImages +
          ', 有URI=' +
          hasUriList,
      );

      // 延迟处理，让TipTap的默认粘贴先完成
      setTimeout(() => {
        console.log('[延迟图片处理] 开始延迟处理图片');
        void processImagesAfterPaste();
      }, 1000); // 延迟1000ms确保内容已插入并且CORS错误已发生
    } else if (hasTextPlain || htmlData) {
      // 即使没有检测到图片，也设置一个通用的延迟处理，以防遗漏
      console.log('[延迟图片处理] 设置通用延迟处理以防遗漏图片');
      setTimeout(() => {
        void processImagesAfterPaste();
      }, 1500); // 更长的延迟确保所有内容都已处理
    }

    // 检查URI列表 - 但对于可能的CORS图片，让延迟处理来处理
    if (hasUriList) {
      const uriList = clipboardData.getData('text/uri-list');
      if (uriList) {
        const urls = uriList.split('\n').filter((url) => url.trim() && !url.startsWith('#'));

        for (const url of urls) {
          const trimmedUrl = url.trim();
          if (isImageUrl(trimmedUrl)) {
            console.log('[URI处理] 发现图片URL:', trimmedUrl);

            // 如果已经设置了延迟处理，就不阻止默认粘贴，让延迟处理来处理CORS情况
            if (mightContainImages || uriListMightContainImages || hasUriList) {
              console.log('[URI处理] 已设置延迟处理，不阻止默认粘贴以处理可能的CORS图片');
              // 不阻止默认粘贴，让延迟处理来处理
              break;
            } else {
              // 没有延迟处理的情况下，使用原来的逻辑
              console.log('[URI处理] 直接处理图片URL');
              event.preventDefault();

              void (async () => {
                const handled = await handleCrossDocumentImagePaste(trimmedUrl);
                if (!handled) {
                  void handlePastedImageUrl(trimmedUrl, isSnapshot);
                }
              })();
              return true;
            }
          }
        }
      }
    }

    // 检查纯文本URL
    const textData = clipboardData.getData('text/plain');
    if (textData && textData.trim()) {
      const trimmedText = textData.trim();

      if (trimmedText.length >= 10 && isImageUrl(trimmedText)) {
        console.log('处理文本图片URL粘贴');
        event.preventDefault();

        void (async () => {
          const handled = await handleCrossDocumentImagePaste(trimmedText);
          if (!handled) {
            void handlePastedImageUrl(trimmedText, isSnapshot);
          }
        })();
        return true;
      }
    }

    console.log('没有检测到图片相关内容，执行默认粘贴');
    return false;
  };

  return {
    dropImages,
    currentEditor,
    toolbarItems,
    getToolbarItemsByType,
    handleImageUpload,
    handleImageUrl,
    onColorSelect,
    isColorActive,
    colors,
    getSlashMenuItems,
    wordCount,
    immediatelySave,
    smartWriteDocToDb,
    // 图片处理相关函数
    isImageUrl,
    convertBase64ToBlob,
    convertBase64ImagesInContent,
    convertImagesInContent,
    getImageAsFileUrl,
    registerBlobUrlMapping: async (blobUrl: string, imageId: number) => {
      const { registerBlobUrlMapping } = await import('src/utils/blobUrlMapping');
      return registerBlobUrlMapping(blobUrl, imageId);
    },
    getImageIdFromBlobUrl: async (blobUrl: string) => {
      const { getImageIdFromBlobUrl } = await import('src/utils/blobUrlMapping');
      return getImageIdFromBlobUrl(blobUrl);
    },
    cleanupBlobUrlMappings: async () => {
      const { cleanupBlobUrlMappings } = await import('src/utils/blobUrlMapping');
      return cleanupBlobUrlMappings();
    },
    showLoadingDialog,
    showSuccessNotification,
    showErrorNotification,
    insertImageInEditor,
    saveImageWithQt,
    attachImage,
    handlePastedImage,
    handlePastedImageUrl,
    checkAndProcessHttpImages,
    downloadImageWithQt,
    handleCrossDocumentImagePaste,
    processImagesInPastedContent,
    processImagesAfterPaste,
    handlePaste,
  };
}
