import { pinyin } from 'pinyin-pro';
import type { Document, Folder } from 'src/types/doc';

/**
 * 排序模式枚举
 */
export enum SortMode {
  ALPHABETICAL = 'alphabetical', // 字典排序（默认）
  CUSTOM = 'custom', // 自定义排序（按 sort_order）
}

/**
 * 获取字符串的排序键
 * 按照数字、英文字符、中文首字拼音的字母顺序排序
 */
function getSortKey(text: string): string {
  if (!text) return '';

  // 获取第一个字符
  const firstChar = text.charAt(0);

  // 数字排在最前面
  if (/\d/.test(firstChar)) {
    return `0_${text}`;
  }

  // 英文字符排在中间
  if (/[a-zA-Z]/.test(firstChar)) {
    return `1_${text.toLowerCase()}`;
  }

  // 中文字符转拼音排在最后
  if (/[\u4e00-\u9fff]/.test(firstChar)) {
    try {
      // 使用 pinyin-pro 转换为拼音，不带音调
      const pinyinResult = pinyin(firstChar, { toneType: 'none' });
      return `2_${pinyinResult.toLowerCase()}_${text}`;
    } catch (error) {
      console.warn('拼音转换失败:', error);
      return `2_${text}`;
    }
  }

  // 其他字符排在最后
  return `3_${text}`;
}

/**
 * 比较两个字符串的排序顺序
 */
function compareStrings(a: string, b: string): number {
  const keyA = getSortKey(a);
  const keyB = getSortKey(b);
  return keyA.localeCompare(keyB, 'zh-CN');
}

/**
 * 按 sort_order 排序文件夹数组
 */
function sortFoldersByOrder(folders: Folder[]): Folder[] {
  // 过滤掉无效的文件夹
  const validFolders = folders.filter(
    (folder) => folder && typeof folder.id === 'number' && folder.id > 0,
  );

  return [...validFolders].sort((a, b) => {
    const orderA = a.sort_order ?? Number.MAX_SAFE_INTEGER;
    const orderB = b.sort_order ?? Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });
}

/**
 * 按 sort_order 排序文档数组
 */
function sortDocumentsByOrder(documents: Document[]): Document[] {
  // 过滤掉无效的文档
  const validDocuments = documents.filter(
    (document) => document && typeof document.id === 'number' && document.id > 0,
  );

  return [...validDocuments].sort((a, b) => {
    const orderA = a.sort_order ?? Number.MAX_SAFE_INTEGER;
    const orderB = b.sort_order ?? Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });
}

/**
 * 按字典顺序排序文件夹数组
 * 按照名称的首字符进行排序：数字 -> 英文 -> 中文拼音
 */
function sortFoldersAlphabetically(folders: Folder[]): Folder[] {
  // 过滤掉无效的文件夹
  const validFolders = folders.filter(
    (folder) => folder && typeof folder.id === 'number' && folder.id > 0,
  );

  return [...validFolders].sort((a, b) => {
    const nameA = a.name || '';
    const nameB = b.name || '';
    return compareStrings(nameA, nameB);
  });
}

/**
 * 按字典顺序排序文档数组
 * 按照标题的首字符进行排序：数字 -> 英文 -> 中文拼音
 */
function sortDocumentsAlphabetically(documents: Document[]): Document[] {
  // 过滤掉无效的文档
  const validDocuments = documents.filter(
    (document) => document && typeof document.id === 'number' && document.id > 0,
  );

  return [...validDocuments].sort((a, b) => {
    const titleA = a.title || '';
    const titleB = b.title || '';
    return compareStrings(titleA, titleB);
  });
}

/**
 * 排序文件夹数组（支持排序模式）
 * @param folders 文件夹数组
 * @param mode 排序模式，默认为字典排序
 */
export function sortFolders(folders: Folder[], mode: SortMode = SortMode.ALPHABETICAL): Folder[] {
  switch (mode) {
    case SortMode.CUSTOM:
      return sortFoldersByOrder(folders);
    case SortMode.ALPHABETICAL:
    default:
      return sortFoldersAlphabetically(folders);
  }
}

/**
 * 排序文档数组（支持排序模式）
 * @param documents 文档数组
 * @param mode 排序模式，默认为字典排序
 */
export function sortDocuments(
  documents: Document[],
  mode: SortMode = SortMode.ALPHABETICAL,
): Document[] {
  switch (mode) {
    case SortMode.CUSTOM:
      return sortDocumentsByOrder(documents);
    case SortMode.ALPHABETICAL:
    default:
      return sortDocumentsAlphabetically(documents);
  }
}

/**
 * 排序混合数组（文件夹和文档）
 * 文件夹在前，文档在后，各自内部按照指定模式排序
 * @param folders 文件夹数组
 * @param documents 文档数组
 * @param mode 排序模式，默认为字典排序
 */
export function sortFoldersAndDocuments(
  folders: Folder[],
  documents: Document[],
  mode: SortMode = SortMode.ALPHABETICAL,
): { sortedFolders: Folder[]; sortedDocuments: Document[] } {
  return {
    sortedFolders: sortFolders(folders, mode),
    sortedDocuments: sortDocuments(documents, mode),
  };
}

/**
 * 为文件夹递归排序所有子内容
 * @param folder 文件夹对象
 * @param mode 排序模式，默认为字典排序
 */
export function sortFolderRecursively(
  folder: Folder,
  mode: SortMode = SortMode.ALPHABETICAL,
): Folder {
  const sortedFolder = { ...folder };

  // 排序子文件夹
  if (sortedFolder.children && sortedFolder.children.length > 0) {
    sortedFolder.children = sortFolders(sortedFolder.children, mode).map((child) =>
      sortFolderRecursively(child, mode),
    );
  }

  // 排序文档
  if (sortedFolder.documents && sortedFolder.documents.length > 0) {
    sortedFolder.documents = sortDocuments(sortedFolder.documents, mode);
  }

  return sortedFolder;
}

/**
 * 排序整个文件夹树
 * @param folderTree 文件夹树数组
 * @param mode 排序模式，默认为字典排序
 */
export function sortFolderTree(
  folderTree: Folder[],
  mode: SortMode = SortMode.ALPHABETICAL,
): Folder[] {
  return sortFolders(folderTree, mode).map((folder) => sortFolderRecursively(folder, mode));
}
