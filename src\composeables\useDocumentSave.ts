import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useDocumentActions } from './useDocumentActions';
import type { JSONContent } from '@tiptap/vue-3';

export function useDocumentSave() {
  const worker = ref<Worker | null>(null);
  const { onUpdateDocument } = useDocumentActions();
  const isSaving = ref(false);

  // 初始化 Worker
  onMounted(() => {
    worker.value = new Worker(new URL('../workers/documentWorker.ts', import.meta.url), {
      type: 'module',
    });

    // 监听 Worker 消息
    worker.value.addEventListener('message', (event) => {
      const { type, payload } = event.data;

      if (type === 'SAVE_DOCUMENT') {
        isSaving.value = true;
        void (async () => {
          try {
            await onUpdateDocument(payload.docId, payload.title, payload.content, payload.folderId);
          } catch (error) {
            console.error('保存文档失败:', error);
          } finally {
            isSaving.value = false;
          }
        })();
      }
    });
  });

  // 清理 Worker
  onBeforeUnmount(() => {
    if (worker.value) {
      worker.value.terminate();
      worker.value = null;
    }
  });

  // 将保存任务加入队列
  const queueSave = (docId: number, title: string, content: JSONContent, folderId: number) => {
    if (!worker.value) return;

    worker.value.postMessage({
      type: 'QUEUE_SAVE',
      payload: {
        docId,
        title,
        content,
        folderId,
      },
    });
  };

  return {
    queueSave,
    isSaving,
  };
}
