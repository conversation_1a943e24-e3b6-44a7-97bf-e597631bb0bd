/**
 * Qt图片下载工具函数
 * 提供统一的图片下载接口，支持URL图片下载并转换为本地引用
 */

// 定义Qt API返回结果的类型
interface QtImageDownloadResult {
  success: boolean;
  id?: number;
  error?: string;
}

// 定义下载结果的类型
export interface ImageDownloadResult {
  success: boolean;
  imageId?: number;
  localSrc?: string;
  error?: string;
}

// 定义下载选项的类型
export interface ImageDownloadOptions {
  docId: number;
  imageUrl: string;
  timeout?: number; // 下载超时时间（毫秒），默认30秒
}

/**
 * 使用Qt后端下载图片并获取本地引用
 * @param options 下载选项
 * @returns Promise<ImageDownloadResult> 下载结果
 */
export async function downloadImageWithQt(
  options: ImageDownloadOptions,
): Promise<ImageDownloadResult> {
  const { docId, imageUrl, timeout = 30000 } = options;

  // 检查Qt API是否可用
  if (typeof window === 'undefined' || !window.databaseApi) {
    return {
      success: false,
      error: 'Qt API unavailable',
    };
  }

  // 验证输入参数
  if (!imageUrl || !imageUrl.trim()) {
    return {
      success: false,
      error: 'Image URL cannot be empty',
    };
  }

  if (!docId || docId <= 0) {
    return {
      success: false,
      error: 'Document ID is invalid',
    };
  }

  try {
    console.log('Start using Qt backend to download image:', imageUrl);

    // 创建Promise来处理Qt API的回调
    const result = await new Promise<QtImageDownloadResult>((resolve, reject) => {
      // 设置超时处理
      const timeoutId = setTimeout(() => {
        reject(new Error(`Download timeout (${timeout}ms)`));
      }, timeout);

      // 调用Qt API下载图片
      window.databaseApi.downloadAndSaveImage(docId, imageUrl, (result: QtImageDownloadResult) => {
        clearTimeout(timeoutId);
        resolve(result);
      });
    });

    // 处理下载结果
    if (result.success && result.id) {
      const localSrc = `image://${result.id}`;
      console.log(
        'Qt backend download image successfully, image ID:',
        result.id,
        'local reference:',
        localSrc,
      );

      return {
        success: true,
        imageId: result.id,
        localSrc,
      };
    } else {
      const errorMsg = result.error || 'Download failed, unknown error';
      console.error('Qt backend download image failed:', errorMsg);

      return {
        success: false,
        error: errorMsg,
      };
    }
  } catch (error) {
    const errorMsg =
      error instanceof Error ? error.message : 'Unknown error occurred during download';
    console.error('Qt image download exception:', errorMsg);

    return {
      success: false,
      error: errorMsg,
    };
  }
}

/**
 * 批量下载多个图片
 * @param options 批量下载选项数组
 * @param concurrency 并发数量，默认为3
 * @param delayBetweenRequests 请求间延迟（毫秒），默认300ms
 * @returns Promise<ImageDownloadResult[]> 批量下载结果
 */
export async function downloadMultipleImagesWithQt(
  options: ImageDownloadOptions[],
  concurrency: number = 3,
  delayBetweenRequests: number = 300,
): Promise<ImageDownloadResult[]> {
  if (!options || options.length === 0) {
    return [];
  }

  console.log(`Start batch downloading ${options.length} images, concurrency: ${concurrency}`);

  const results: ImageDownloadResult[] = [];
  const chunks: ImageDownloadOptions[][] = [];

  // 将任务分组，控制并发数
  for (let i = 0; i < options.length; i += concurrency) {
    chunks.push(options.slice(i, i + concurrency));
  }

  // 逐组处理
  for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
    const chunk = chunks[chunkIndex];

    // 并发处理当前组的任务
    const chunkPromises = chunk.map(async (option) => {
      try {
        return await downloadImageWithQt(option);
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Download failed',
        };
      }
    });

    // 等待当前组完成
    const chunkResults = await Promise.all(chunkPromises);
    results.push(...chunkResults);

    // 如果不是最后一组，添加延迟
    if (chunkIndex < chunks.length - 1 && delayBetweenRequests > 0) {
      await new Promise((resolve) => setTimeout(resolve, delayBetweenRequests));
    }
  }

  const successCount = results.filter((r) => r.success).length;
  console.log(`Batch download completed: ${successCount}/${results.length} successful`);

  return results;
}

/**
 * 检查URL是否为HTTP/HTTPS图片链接
 * @param url 要检查的URL
 * @returns boolean 是否为HTTP图片链接
 */
export function isHttpImageUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }

  const trimmedUrl = url.trim().toLowerCase();

  // 检查是否以http或https开头
  if (!trimmedUrl.startsWith('http://') && !trimmedUrl.startsWith('https://')) {
    return false;
  }

  // 检查是否为常见图片格式
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
  const hasImageExtension = imageExtensions.some((ext) => trimmedUrl.includes(ext));

  // 如果有明确的图片扩展名，直接返回true
  if (hasImageExtension) {
    return true;
  }

  // 对于没有明确扩展名的URL，可能是动态图片链接，也认为是图片
  // 这里可以根据实际需求调整判断逻辑
  return true;
}

/**
 * 从HTML内容中提取所有HTTP图片URL
 * @param htmlContent HTML内容
 * @returns string[] HTTP图片URL数组
 */
export function extractHttpImageUrls(htmlContent: string): string[] {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return [];
  }

  const urls: string[] = [];
  const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
  let match;

  while ((match = imgRegex.exec(htmlContent)) !== null) {
    const url = match[1];
    if (isHttpImageUrl(url)) {
      urls.push(url);
    }
  }

  return [...new Set(urls)]; // 去重
}

/**
 * 生成本地图片引用
 * @param imageId 图片ID
 * @returns string 本地图片引用
 */
export function generateLocalImageSrc(imageId: number): string {
  return `image://${imageId}`;
}
