# Embedding维度自动检测与配置系统

## 概述

本文档描述了InkCop知识库系统中实现的embedding维度自动检测与配置功能。该系统能够在用户配置embedding模型时自动检测向量维度，并将其保存到配置中，确保HNSW索引配置的准确性。

## 功能特性

### 🔍 自动维度检测
- **触发时机**：当用户修改embedding模型、API地址或API密钥时
- **检测方式**：发送测试请求到embedding API，解析返回的向量维度
- **支持模式**：云端API模式和本地GGUF模式

### 💾 配置持久化
- **自动保存**：检测到的维度自动保存到知识库配置中
- **只读显示**：在界面中显示维度信息，但用户不能手动修改
- **配置同步**：确保配置中的维度与实际使用的模型匹配

### 🔧 HNSW索引优化
- **配置验证**：提供工具验证HNSW索引配置与实际维度的匹配性
- **性能优化**：确保向量搜索获得最佳性能
- **错误预防**：避免维度不匹配导致的索引失效

## 实现架构

### 前端组件 (KnowledgeBaseSettings.vue)

#### 响应式状态
```typescript
// 维度检测状态
const detectingDimension = ref(false);

// 计算属性：显示embedding维度
const embeddingDimensionDisplay = computed(() => {
  const dimension = settings.value?.embeddingDimension;
  if (dimension && dimension > 0) {
    return `${dimension}维`;
  }
  return '未检测';
});
```

#### 自动检测触发器
```typescript
// 监听关键配置变化
watch(
  () => [settings.value?.baseUrl, settings.value?.apiKey, settings.value?.model, settings.value?.embeddingMode],
  ([newBaseUrl, newApiKey, newModel, newMode], [oldBaseUrl, oldApiKey, oldModel, oldMode]) => {
    if (newMode === 'cloud') {
      const hasConfigChange = 
        newBaseUrl !== oldBaseUrl || 
        newApiKey !== oldApiKey || 
        newModel !== oldModel;

      if (hasConfigChange && newBaseUrl && newApiKey && newModel) {
        setTimeout(() => {
          void detectEmbeddingDimension();
        }, 1000);
      }
    }
  },
  { deep: true }
);
```

#### 维度检测方法
```typescript
const detectEmbeddingDimension = async () => {
  // 1. 验证配置完整性
  // 2. 发送测试请求到embedding API
  // 3. 解析响应中的向量维度
  // 4. 保存维度到配置中
  // 5. 更新UI状态
};
```

### 后端API (KnowledgeApi)

#### 配置读取方法
```cpp
int KnowledgeApi::getEmbeddingDimensionFromKnowledgeBaseConfig()
{
    // 从数据库中读取知识库配置
    // 提取embeddingDimension字段
    // 返回保存的维度值
}
```

#### 配置验证方法
```cpp
QString KnowledgeApi::validateHNSWConfiguration()
{
    // 检测当前embedding模型的实际维度
    // 读取配置中保存的维度
    // 比较HNSW索引配置的维度
    // 返回详细的验证报告
}
```

### 数据类型扩展

#### KnowledgeBaseSettings接口
```typescript
export interface KnowledgeBaseSettings {
  baseUrl: string;
  apiKey: string;
  model: string;
  embeddingDimension?: number; // 新增：自动检测的向量维度
  // ... 其他字段
}
```

## 使用流程

### 1. 用户配置embedding模型

1. 用户在知识库设置中选择供应商和模型
2. 系统自动填充baseUrl和apiKey
3. 触发自动维度检测

### 2. 自动维度检测

1. 系统发送测试请求到embedding API
2. 解析返回的向量数据，获取维度信息
3. 将维度保存到知识库配置中
4. 在界面中显示检测结果

### 3. 配置验证

1. 用户可以手动触发配置验证
2. 系统比较实际维度、配置维度和HNSW索引维度
3. 提供详细的验证报告和优化建议

## 支持的Embedding模型

### 云端API模型
- **OpenAI兼容模型**：text-embedding-ada-002 (1536维)
- **阿里云模型**：text-embedding-v1/v2/v3/v4 (1536维)
- **其他兼容模型**：根据实际API响应检测

### 本地GGUF模型
- **自动检测**：从模型推理结果中获取维度
- **动态适配**：支持不同维度的本地模型

### 回退方案
- **简单TF-IDF**：100维（内置实现）

## 界面展示

### 维度显示字段
```vue
<q-input
  v-model="embeddingDimensionDisplay"
  label="向量维度"
  outlined
  readonly
  hint="自动检测的embedding模型向量维度，用于HNSW索引配置"
  :loading="detectingDimension"
>
  <template v-slot:prepend>
    <q-icon name="straighten" />
  </template>
  <template v-slot:append>
    <q-btn
      flat
      round
      dense
      icon="refresh"
      tooltip="重新检测维度"
      @click="detectEmbeddingDimension"
    />
  </template>
</q-input>
```

### 状态指示
- **未检测**：显示"未检测"
- **检测中**：显示加载动画
- **已检测**：显示"1536维"等具体数值
- **检测失败**：显示错误信息

## 配置验证报告

### 验证结果示例
```json
{
  "success": true,
  "actual_dimension": 1536,
  "hnsw_configured_dimension": 1536,
  "saved_dimension": 1536,
  "dimension_match": true,
  "config_hnsw_match": true,
  "message": "✅ 配置完美！实际维度(1536)与HNSW索引维度匹配",
  "recommendation": "配置已优化，无需调整",
  "embedding_mode": "cloud",
  "embedding_model": "text-embedding-v4"
}
```

### 常见问题诊断

#### 1. 维度不匹配
- **问题**：实际维度与HNSW配置不匹配
- **影响**：搜索性能下降或索引失效
- **解决**：更新model.h中的HNSW维度配置

#### 2. 配置过期
- **问题**：保存的维度与当前模型不匹配
- **影响**：可能使用了错误的模型配置
- **解决**：重新测试embedding配置

#### 3. 检测失败
- **问题**：无法连接到embedding API
- **影响**：无法获取准确的维度信息
- **解决**：检查网络连接和API配置

## 最佳实践

### 1. 配置流程
1. **选择模型**：在知识库设置中选择合适的embedding模型
2. **等待检测**：等待系统自动检测维度（约1-2秒）
3. **验证结果**：查看维度显示是否正确
4. **测试连接**：使用"测试连接"功能验证配置

### 2. 维护建议
1. **定期验证**：定期运行配置验证工具
2. **模型更换**：更换embedding模型时注意维度变化
3. **性能监控**：监控向量搜索性能，确保配置正确

### 3. 故障排除
1. **检查日志**：查看控制台日志了解检测过程
2. **手动刷新**：使用刷新按钮重新检测维度
3. **配置重置**：必要时重新配置embedding模型

## 技术优势

### 1. 自动化程度高
- 无需用户手动配置维度
- 自动触发检测和保存
- 减少配置错误

### 2. 配置一致性
- 确保前端配置与后端索引匹配
- 避免维度不匹配导致的问题
- 提供完整的验证机制

### 3. 用户体验优化
- 实时反馈检测状态
- 清晰的错误提示
- 智能的配置建议

## 总结

embedding维度自动检测与配置系统显著提升了InkCop知识库的配置体验和系统可靠性。通过自动化的维度检测、持久化配置和智能验证，确保了HNSW向量搜索的最佳性能，同时降低了用户配置的复杂度和出错概率。
