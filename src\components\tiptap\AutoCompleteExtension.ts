import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { DecorationSet, Decoration } from '@tiptap/pm/view';
import { TextSelection } from '@tiptap/pm/state';
import { useAutoComplete } from '../../composeables/useAutoComplete';
import { watch } from 'vue';

export interface AutoCompleteOptions {
  enabled: boolean;
  triggerLength: number;
  debounceTime: number;
  docId?: number;
  documentTitle?: string;
  onSuggestionSelected?: (text: string, from: number, to: number) => void;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    autoComplete: {
      /**
       * 触发自动补全
       */
      triggerAutoComplete: () => ReturnType;
      /**
       * 清除自动补全建议
       */
      clearAutoComplete: () => ReturnType;
      /**
       * 确认自动补全建议
       */
      confirmAutoComplete: () => ReturnType;
      /**
       * 更新自动补全选项
       */
      updateAutoCompleteOptions: (options: Partial<AutoCompleteOptions>) => ReturnType;
    };
  }
}

// 状态定义
type AutoCompleteStatus =
  | 'initialization'
  | 'ready'
  | 'processing'
  | 'hasSuggestion'
  | 'drapedSuggestion';
let autoCompleteStatus: AutoCompleteStatus = 'initialization';
let isEditorInitialized = false; // 新增：标记编辑器是否已完成初始化

export const AutoComplete = Extension.create<AutoCompleteOptions>({
  name: 'autoComplete',

  addOptions() {
    return {
      enabled: false, // 默认禁用
      triggerLength: 10,
      debounceTime: 500,
      docId: undefined,
      documentTitle: '',
      onSuggestionSelected: undefined,
    };
  },

  addCommands() {
    return {
      triggerAutoComplete:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta('autoComplete', { type: 'trigger' });
          }
          return true;
        },
      clearAutoComplete:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta('autoComplete', { type: 'clear' });
          }
          return true;
        },
      confirmAutoComplete:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta('autoComplete', { type: 'confirm' });
          }
          return true;
        },
      updateAutoCompleteOptions:
        (options: Partial<AutoCompleteOptions>) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta('autoComplete', { type: 'updateOptions', options });
          }
          return true;
        },
    };
  },

  addProseMirrorPlugins() {
    const {
      triggerAutoComplete,
      clearSuggestions,
      suggestions,
      updateConfig,
      toggleEnabled,
      abortAutoComplete,
    } = useAutoComplete();
    let currentSuggestion: { text: string; from: number; to: number } | null = null;
    let lastCursorPosition = 0; // 跟踪上次光标位置
    let rejectTimestamp = 0; // 记录拒绝建议的时间戳
    let isScrolling = false; // 跟踪滚动状态
    let scrollTimeout: NodeJS.Timeout | null = null; // 滚动超时定时器

    // 保存扩展选项的引用
    const extensionOptions = this.options;

    // 同步配置到useAutoComplete
    updateConfig({
      enabled: this.options.enabled,
      triggerLength: this.options.triggerLength,
      debounceTime: this.options.debounceTime,
    });

    // 设置启用状态
    toggleEnabled(this.options.enabled);

    return [
      new Plugin({
        key: new PluginKey('autoComplete'),
        state: {
          init() {
            return DecorationSet.empty;
          },
          apply(tr) {
            const autoCompleteMeta = tr.getMeta('autoComplete');

            // 处理清除建议
            if (autoCompleteMeta?.type === 'clear' || autoCompleteMeta?.type === 'refresh') {
              return DecorationSet.empty;
            }

            // 处理确认建议
            if (autoCompleteMeta?.type === 'confirm' && currentSuggestion) {
              return DecorationSet.empty;
            }

            // 处理更新选项
            if (autoCompleteMeta?.type === 'updateOptions') {
              const { options } = autoCompleteMeta;
              // 同步更新扩展内部的options
              Object.assign(extensionOptions, options);
              updateConfig({
                enabled: options.enabled,
                triggerLength: options.triggerLength,
                debounceTime: options.debounceTime,
              });
              // 设置启用状态
              toggleEnabled(options.enabled);
              return DecorationSet.empty;
            }

            // 显示建议装饰
            if (autoCompleteMeta?.type === 'showSuggestion' && currentSuggestion) {
              const { from } = currentSuggestion;
              const decoration = Decoration.widget(from, () => {
                const span = document.createElement('span');
                span.className = 'auto-complete-suggestion';
                span.textContent = currentSuggestion.text;
                return span;
              });
              return DecorationSet.create(tr.doc, [decoration]);
            }

            return DecorationSet.empty;
          },
        },
        props: {
          decorations(state) {
            return this.getState(state);
          },
        },
        view: (editorView) => {
          // 滚动事件处理
          const handleScroll = () => {
            isScrolling = true;

            // 如果正在滚动，清除当前建议
            if (currentSuggestion) {
              clearSuggestion();
            }

            // 中止正在进行的自动补全请求
            if (autoCompleteStatus === 'processing') {
              abortAutoComplete();
              autoCompleteStatus = 'ready';
            }

            // 清除之前的超时
            if (scrollTimeout) {
              clearTimeout(scrollTimeout);
            }

            // 设置超时，滚动停止后恢复自动补全
            scrollTimeout = setTimeout(() => {
              isScrolling = false;
            }, 150); // 滚动停止150ms后恢复
          };

          // 监听滚动事件
          const scrollContainer =
            editorView.dom.closest('.q-scroll-area') || editorView.dom.parentElement;
          if (scrollContainer) {
            scrollContainer.addEventListener('scroll', handleScroll, { passive: true });
          }

          // 显示建议内容（不插入实际内容）
          const showSuggestion = (text: string, from: number) => {
            const to = from + text.length;
            currentSuggestion = { text, from, to };
            autoCompleteStatus = 'hasSuggestion';

            // 通过装饰显示建议文本
            const tr = editorView.state.tr.setMeta('autoComplete', {
              type: 'showSuggestion',
            });
            editorView.dispatch(tr);
          };

          // 清除建议内容
          const clearSuggestion = () => {
            if (currentSuggestion) {
              currentSuggestion = null;
              clearSuggestions();
              autoCompleteStatus = 'ready';

              // 清除装饰
              const tr = editorView.state.tr.setMeta('autoComplete', { type: 'clear' });
              editorView.dispatch(tr);
            }
          };

          // 拒绝建议内容（设置状态为drapedSuggestion）
          const rejectSuggestion = () => {
            if (currentSuggestion) {
              currentSuggestion = null;
              clearSuggestions();
              autoCompleteStatus = 'drapedSuggestion';
              lastCursorPosition = editorView.state.selection.from; // 记录拒绝时的光标位置
              rejectTimestamp = Date.now(); // 记录拒绝时间

              // 清除装饰
              const tr = editorView.state.tr.setMeta('autoComplete', { type: 'clear' });
              editorView.dispatch(tr);
            }
          };

          // 确认建议内容（真正插入到文档中）
          const confirmSuggestion = () => {
            if (currentSuggestion) {
              const { text, from } = currentSuggestion;

              // 真正插入建议文本到文档中
              let tr = editorView.state.tr.insertText(text, from);
              tr = tr.setSelection(TextSelection.create(tr.doc, from + text.length));
              editorView.dispatch(tr);

              // 清理建议状态
              currentSuggestion = null;
              clearSuggestions();
              autoCompleteStatus = 'ready';

              // 清除装饰
              const clearTr = editorView.state.tr.setMeta('autoComplete', { type: 'clear' });
              editorView.dispatch(clearTr);
            }
          };

          // 监听建议变化
          const unwatchSuggestions = watch(suggestions, (newSuggestion) => {
            // 只有在编辑器初始化完成且没有当前建议时才处理
            if (isEditorInitialized && newSuggestion && newSuggestion.text && !currentSuggestion) {
              const { state } = editorView;
              const { from } = state.selection;
              showSuggestion(newSuggestion.text, from);
            }
          });

          // 监听编辑器变化
          const handleUpdate = () => {
            // 处理初始化状态
            if (autoCompleteStatus === 'initialization') {
              autoCompleteStatus = 'ready';
              // 延迟标记编辑器为已初始化，确保内容加载完成
              setTimeout(() => {
                isEditorInitialized = true;
              }, 1000); // 延迟1秒确保内容加载完成
              return;
            }

            // 如果编辑器未初始化完成，不处理自动补全
            if (!isEditorInitialized) {
              return;
            }

            // 如果正在滚动，不处理自动补全
            if (isScrolling) {
              return;
            }

            const { state } = editorView;
            const { from } = state.selection;

            if (currentSuggestion) {
              const { from: sugFrom, to: sugTo } = currentSuggestion;
              // 如果光标移出建议区域，清除建议
              if (from < sugFrom || from > sugTo) {
                clearSuggestion();
              }
            } else {
              // 检查是否存在AgentWriter节点
              let hasAgentWriterNode = false;

              state.doc.descendants((node) => {
                if (node.type.name === 'agentWriter') {
                  hasAgentWriterNode = true;
                  return false; // 停止遍历，找到第一个AgentWriter节点即可
                }
              });

              // 检查是否存在选择文本
              const { selection } = state;
              const hasTextSelection = !selection.empty && selection.from !== selection.to;

              // 如果存在AgentWriter节点或存在选择文本，禁用自动补全
              if (hasAgentWriterNode || hasTextSelection) return;

              // 如果当前是drapedSuggestion状态，检查是否需要重置
              if (autoCompleteStatus === 'drapedSuggestion') {
                const currentTime = Date.now();
                const timeSinceReject = currentTime - rejectTimestamp;

                // 条件1：光标位置改变（输入内容或移动光标都会导致位置变化）
                const hasCursorMoved = from !== lastCursorPosition;

                // 条件2：时间间隔（至少1秒后才允许重新触发）
                const hasTimePassed = timeSinceReject > 1000;

                // 满足条件1且满足条件2时重置状态
                if (hasCursorMoved && hasTimePassed) {
                  autoCompleteStatus = 'ready';
                }
              }

              // 只有ready时才允许触发AI建议，drapedSuggestion状态不触发
              if (autoCompleteStatus !== 'ready') return;

              // 检查是否启用了自动补全
              if (!extensionOptions.enabled) {
                return;
              }

              // 通过Tiptap API获取精确的上下文信息
              const $from = state.doc.resolve(from);
              const currentParagraphPos = $from.before($from.depth);
              const currentParagraphNode = state.doc.nodeAt(currentParagraphPos);
              const currentParagraph = currentParagraphNode?.textContent || '';

              // 获取当前段落内的光标前后文本
              const paragraphStart = currentParagraphPos + 1; // +1 跳过段落节点本身
              const paragraphEnd = currentParagraphPos + (currentParagraphNode?.nodeSize || 0);
              const textBeforeCursor = state.doc.textBetween(paragraphStart, from);
              const textAfterCursor = state.doc.textBetween(from, paragraphEnd);

              // 获取文档全文
              const fullText = state.doc.textBetween(0, state.doc.content.size);

              if (textBeforeCursor.length >= extensionOptions.triggerLength) {
                autoCompleteStatus = 'processing';
                triggerAutoComplete(textBeforeCursor, from, {
                  beforeText: textBeforeCursor,
                  afterText: textAfterCursor,
                  currentParagraph,
                  fullText,
                  documentTitle: extensionOptions.documentTitle,
                  documentType: 'document',
                });
              }
            }

            // 更新光标位置跟踪
            lastCursorPosition = from;
          };

          // 监听键盘事件
          const handleKeyDown = (event: KeyboardEvent) => {
            // 处理Ctrl+K快捷键，中止正在生成的自动补全
            if (event.key === 'k' && (event.ctrlKey || event.metaKey)) {
              if (autoCompleteStatus === 'processing') {
                console.log('[AutoComplete] 检测到Ctrl+K，中止正在生成的自动补全');
                abortAutoComplete();
                autoCompleteStatus = 'ready';
                // 不阻止事件传播，让其他处理器也能处理Ctrl+K
              }
              // 无论是否中止了自动补全，都不阻止事件传播
              return;
            }

            if (event.key === 'Tab' && currentSuggestion) {
              event.preventDefault();
              confirmSuggestion();
            } else if (event.key === 'Escape' && currentSuggestion) {
              event.preventDefault();
              rejectSuggestion();
            }
          };

          // 监听点击事件
          const handleClick = () => {
            if (currentSuggestion) {
              const { from, to } = currentSuggestion;
              const selFrom = editorView.state.selection.from;
              if (selFrom < from || selFrom > to) {
                clearSuggestion();
              }
            }
          };

          editorView.dom.addEventListener('keydown', handleKeyDown);
          editorView.dom.addEventListener('click', handleClick);

          return {
            update: handleUpdate,
            destroy: () => {
              unwatchSuggestions();
              editorView.dom.removeEventListener('keydown', handleKeyDown);
              editorView.dom.removeEventListener('click', handleClick);

              // 清理滚动事件监听器
              if (scrollContainer) {
                scrollContainer.removeEventListener('scroll', handleScroll);
              }

              // 清理滚动超时
              if (scrollTimeout) {
                clearTimeout(scrollTimeout);
              }
            },
          };
        },
      }),
    ];
  },
});
