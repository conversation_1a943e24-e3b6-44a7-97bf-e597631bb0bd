import { computed, type Ref } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { useQwen } from './useQwen';
import { useOllama } from './useOllama';
import { useMiniMax } from './useMiniMax';
import { useDeepSeek } from './useDeepSeek';
import { useVolces } from './useVolces';
import { useMoonshot } from './useMoonshot';
import { useAnthropic } from './useAnthropic';
import { useOpenAI } from './useOpenAI';
import { useAzureOpenAI } from './useAzureOpenAI';
import { useGemini } from './useGemini';
import { useGrok } from './useGrok';
import { useGlm } from './useGlm';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import type { ConversationRole } from 'src/services/promptService';
import { getAllModels } from 'src/types/modelCategories';

/**
 * LLM 路由器 - 根据当前选择的供应商调用相应的 hook
 */
export const useLlmRouter = () => {
  const uiStore = useUiStore();

  // 获取当前LLM供应商
  const currentProvider = computed(() => uiStore.currentLlmProvider);

  // 初始化各个供应商的hooks
  const qwenHook = useQwen();
  const ollamaHook = useOllama();
  const miniMaxHook = useMiniMax();
  const deepSeekHook = useDeepSeek();
  const volcesHook = useVolces();
  const moonshotHook = useMoonshot();
  const anthropicHook = useAnthropic();
  const openaiHook = useOpenAI();
  const azureOpenaiHook = useAzureOpenAI();
  const geminiHook = useGemini();
  const grokHook = useGrok();
  const glmHook = useGlm();

  // 获取当前供应商的设置
  const currentSettings = computed(() => {
    const provider = currentProvider.value;
    if (provider === 'qwen') {
      return qwenHook.qwenSettings.value;
    } else if (provider === 'ollama') {
      return ollamaHook.ollamaSettings.value;
    } else if (provider === 'minimax') {
      return miniMaxHook.currentSettings.value;
    } else if (provider === 'deepseek') {
      return deepSeekHook.deepSeekSettings.value;
    } else if (provider === 'volces') {
      return volcesHook.volcesSettings.value;
    } else if (provider === 'moonshot') {
      return moonshotHook.moonshotSettings.value;
    } else if (provider === 'anthropic') {
      return anthropicHook.anthropicSettings.value;
    } else if (provider === 'openai') {
      return openaiHook.openaiSettings.value;
    } else if (provider === 'azureOpenai') {
      return azureOpenaiHook.azureOpenaiSettings.value;
    } else if (provider === 'gemini') {
      return geminiHook.geminiSettings.value;
    } else if (provider === 'grok') {
      return grokHook.grokSettings.value;
    } else if (provider === 'glm') {
      return glmHook.glmSettings.value;
    }
    return null;
  });

  // 检查是否需要配置
  // 1. 首先检查是否启用
  // 2. 除了 ollama 之外的所有供应商中，存在一个配置了 apikey，就认为不需要设置
  // 3. 对于 ollama，只要存在可用模型，就返回 false
  const needSettings = computed(() => {
    // 检查 Qwen 设置
    const qwenSettings = qwenHook.qwenSettings.value;
    if (qwenSettings.enabled && qwenSettings?.apiKey && qwenSettings.apiKey.trim() !== '') {
      return false;
    }

    // 检查 MiniMax 设置
    const miniMaxSettings = miniMaxHook.currentSettings.value;
    if (
      miniMaxSettings.enabled &&
      miniMaxSettings?.apiKey &&
      miniMaxSettings.apiKey.trim() !== ''
    ) {
      return false;
    }

    // 检查 DeepSeek 设置
    const deepSeekSettings = deepSeekHook.deepSeekSettings.value;
    if (
      deepSeekSettings.enabled &&
      deepSeekSettings?.apiKey &&
      deepSeekSettings.apiKey.trim() !== ''
    ) {
      return false;
    }

    // 检查 Volces 设置
    const volcesSettings = volcesHook.volcesSettings.value;
    if (volcesSettings.enabled && volcesSettings?.apiKey && volcesSettings.apiKey.trim() !== '') {
      return false;
    }

    // 检查 Moonshot 设置
    const moonshotSettings = moonshotHook.moonshotSettings.value;
    if (
      moonshotSettings.enabled &&
      moonshotSettings?.apiKey &&
      moonshotSettings.apiKey.trim() !== ''
    ) {
      return false;
    }

    // 检查 Anthropic 设置
    const anthropicSettings = anthropicHook.anthropicSettings.value;
    if (
      anthropicSettings.enabled &&
      anthropicSettings?.apiKey &&
      anthropicSettings.apiKey.trim() !== ''
    ) {
      return false;
    }

    // 检查 OpenAI 设置
    const openaiSettings = openaiHook.openaiSettings.value;
    if (openaiSettings.enabled && openaiSettings?.apiKey && openaiSettings.apiKey.trim() !== '') {
      return false;
    }

    // 检查 Azure OpenAI 设置
    const azureOpenaiSettings = azureOpenaiHook.azureOpenaiSettings.value;
    if (
      azureOpenaiSettings.enabled &&
      azureOpenaiSettings?.apiKey &&
      azureOpenaiSettings.apiKey.trim() !== '' &&
      azureOpenaiSettings.deploymentName &&
      azureOpenaiSettings.deploymentName.trim() !== ''
    ) {
      return false;
    }

    // 检查 Gemini 设置
    const geminiSettings = geminiHook.geminiSettings.value;
    if (geminiSettings.enabled && geminiSettings?.apiKey && geminiSettings.apiKey.trim() !== '') {
      return false;
    }

    // 检查 Grok 设置
    const grokSettings = grokHook.grokSettings.value;
    if (grokSettings.enabled && grokSettings?.apiKey && grokSettings.apiKey.trim() !== '') {
      return false;
    }

    // 检查 GLM 设置
    const glmSettings = glmHook.glmSettings.value;
    if (glmSettings.enabled && glmSettings?.apiKey && glmSettings.apiKey.trim() !== '') {
      return false;
    }

    // 检查 Ollama 设置 - 只要存在可用模型，就认为不需要设置
    const ollamaSettings = ollamaHook.ollamaSettings.value;
    if (ollamaSettings.enabled && ollamaSettings?.avaliableModels) {
      // 检查是否有非空的模型
      const categorizedModels = ollamaSettings.avaliableModels;
      const allModels = getAllModels(categorizedModels);
      const hasValidModels = allModels.some((model) => model && model.trim() !== '');
      if (hasValidModels) {
        return false;
      }
    }

    // Ollama之外，如果没有任何供应商配置了 API Key 或可用模型，则需要设置
    return true;
  });

  // 统一的消息发送接口 - 接收简化的参数，直接操作响应式消息数组
  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const provider = currentProvider.value;

    console.log(`[LLM Router] 使用供应商: ${provider}`);

    if (provider === 'qwen') {
      await qwenHook.sendMessage(params, loading);
    } else if (provider === 'ollama') {
      await ollamaHook.sendMessage(params, loading);
    } else if (provider === 'minimax') {
      await miniMaxHook.sendMessage(params, loading);
    } else if (provider === 'deepseek') {
      await deepSeekHook.sendMessage(params, loading);
    } else if (provider === 'volces') {
      await volcesHook.sendMessage(params, loading);
    } else if (provider === 'moonshot') {
      await moonshotHook.sendMessage(params, loading);
    } else if (provider === 'anthropic') {
      await anthropicHook.sendMessage(params, loading);
    } else if (provider === 'openai') {
      await openaiHook.sendMessage(params, loading);
    } else if (provider === 'azureOpenai') {
      await azureOpenaiHook.sendMessage(params, loading);
    } else if (provider === 'gemini') {
      await geminiHook.sendMessage(params, loading);
    } else if (provider === 'grok') {
      await grokHook.sendMessage(params, loading);
    } else if (provider === 'glm') {
      await glmHook.sendMessage(params, loading);
    } else {
      console.error(`[LLM Router] 不支持的供应商: ${provider}`);
      throw new Error(`不支持的LLM供应商: ${provider}`);
    }
  };

  // 获取可用角色（从qwen hook获取，因为这是通用的）
  const getAvailableRoles = () => qwenHook.getAvailableRoles();

  // 获取角色建议（从qwen hook获取，因为这是通用的）
  const getRoleSuggestions = (role: ConversationRole) => qwenHook.getRoleSuggestions(role);

  return {
    currentProvider,
    currentSettings,
    needSettings,
    sendMessage,
    getAvailableRoles,
    getRoleSuggestions,
    // 导出各个供应商的hooks以便直接访问
    qwenHook,
    ollamaHook,
    miniMaxHook,
    deepSeekHook,
    volcesHook,
    moonshotHook,
    anthropicHook,
    openaiHook,
    azureOpenaiHook,
    geminiHook,
    grokHook,
    glmHook,
  };
};
