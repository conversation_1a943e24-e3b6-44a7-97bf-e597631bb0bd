import type { KnowledgeSearchResult, KnowledgeBase } from 'src/env';

/**
 * 知识库服务类 - 解决循环依赖问题
 */
export class KnowledgeService {
  private selectedKnowledgeBase: KnowledgeBase | null = null;
  private knowledgeContext: {
    knowledgeBase: KnowledgeBase;
    searchQuery: string;
    results: KnowledgeSearchResult[];
  } | null = null;

  /**
   * 设置选中的知识库
   */
  setSelectedKnowledgeBase(kb: KnowledgeBase | null): void {
    console.log('🔍 [KnowledgeService] 设置选中的知识库:', kb?.name || '无');
    this.selectedKnowledgeBase = kb;
    if (!kb) {
      this.clearKnowledgeContext();
    }
  }

  /**
   * 获取选中的知识库
   */
  getSelectedKnowledgeBase(): KnowledgeBase | null {
    return this.selectedKnowledgeBase;
  }

  /**
   * 搜索知识库内容
   */
  searchKnowledgeContent(
    query: string,
    knowledgeBaseId: string,
    limit: number = 5,
  ): Promise<KnowledgeSearchResult[]> {
    try {
      if (!window.knowledgeApi) {
        console.warn('知识库API不可用');
        return Promise.resolve([]);
      }

      const response = window.knowledgeApi.searchKnowledgeBase(knowledgeBaseId, query, limit);
      const results = JSON.parse(response);

      if (results.success) {
        return Promise.resolve(results.results || []);
      } else {
        console.error('知识库搜索失败:', results.error);
        return Promise.resolve([]);
      }
    } catch (error) {
      console.error('知识库搜索异常:', error);
      return Promise.resolve([]);
    }
  }

  /**
   * 设置知识库上下文
   */
  setKnowledgeContext(
    knowledgeBase: KnowledgeBase,
    results: KnowledgeSearchResult[],
    searchQuery: string,
  ): void {
    console.log(
      '🔍 [KnowledgeService] 设置知识库上下文:',
      knowledgeBase.name,
      '结果数:',
      results.length,
    );
    this.knowledgeContext = {
      knowledgeBase,
      results,
      searchQuery,
    };
  }

  /**
   * 获取知识库上下文
   */
  getKnowledgeContext(): typeof this.knowledgeContext {
    // 移除冗余的调试信息
    return this.knowledgeContext;
  }

  /**
   * 清空知识库上下文
   */
  clearKnowledgeContext(): void {
    this.knowledgeContext = null;
  }
}

// 导出单例实例
export const knowledgeService = new KnowledgeService();
