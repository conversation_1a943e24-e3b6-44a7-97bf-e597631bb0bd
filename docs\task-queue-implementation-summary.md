# 任务队列系统实现总结

## 概述

本次实现了一个基于 IndexedDB 的持久化任务队列管理系统，用于管理 Worker 事务，确保在应用重启后能够恢复待处理队列。

## 实现的文件

### 1. 核心队列管理器
- **`src/utils/taskQueue.ts`** - 主要的队列管理器，包含：
  - `TaskQueueManager` 类：负责任务的持久化存储和执行调度
  - IndexedDB 数据库操作（使用 Dexie）
  - 任务状态管理（PENDING, RUNNING, COMPLETED, FAILED）
  - 自动重试机制
  - 并发控制（最大3个并发任务）
  - 崩溃恢复功能

### 2. Worker 管理器集成
- **`src/utils/chunkingWorkerManager.ts`** - 修改现有的 Worker 管理器：
  - 集成队列系统
  - 异步任务提交接口
  - 保持向后兼容性
  - 添加初始化和恢复功能

### 3. Worker 增强
- **`src/workers/chunkingWorker.ts`** - 增强 Worker 功能：
  - 支持队列状态查询
  - 初始化消息处理
  - 扩展消息类型定义

### 4. 高级接口适配
- **`src/utils/knowledgeBase.ts`** - 修改 `splitInBackground` 函数：
  - 适配新的异步接口
  - 回调函数类型转换
  - 保持 API 兼容性

### 5. 测试和示例
- **`src/utils/taskQueueTest.ts`** - 完整的测试套件
- **`src/utils/queueUsageExample.ts`** - 使用示例和最佳实践

## 核心特性

### 1. 持久化存储
```typescript
// 使用 Dexie (IndexedDB) 存储任务
class TaskQueueDB extends Dexie {
  tasks!: Table<BaseTask>;
  
  constructor() {
    super('TaskQueueDB');
    this.version(1).stores({
      tasks: '&id, type, status, priority, createdAt, updatedAt',
    });
  }
}
```

### 2. 任务状态管理
```typescript
export enum TaskStatus {
  PENDING = 'pending',    // 待处理
  RUNNING = 'running',    // 运行中
  COMPLETED = 'completed', // 已完成
  FAILED = 'failed',      // 失败
}
```

### 3. 自动恢复机制
```typescript
// 应用重启时自动恢复中断的任务
private async recoverInterruptedTasks(): Promise<void> {
  const runningTasks = await this.db.tasks
    .where('status')
    .equals(TaskStatus.RUNNING)
    .toArray();

  for (const task of runningTasks) {
    await this.db.tasks.update(task.id, {
      status: TaskStatus.PENDING,
      updatedAt: Date.now(),
    });
  }
}
```

### 4. 并发控制
```typescript
private readonly MAX_CONCURRENT_TASKS = 3;
private runningTasks = new Set<string>();

// 限制同时运行的任务数量
if (this.runningTasks.size >= this.MAX_CONCURRENT_TASKS) {
  return; // 已达到最大并发数
}
```

### 5. 重试机制
```typescript
// 任务失败后自动重试
if (task.retryCount < task.maxRetries) {
  await this.updateTask(taskId, {
    status: TaskStatus.PENDING,
    retryCount: task.retryCount + 1,
    error: error.message,
  });
} else {
  await this.updateTask(taskId, {
    status: TaskStatus.FAILED,
    error: error.message,
  });
}
```

## 使用方式

### 1. 基础队列操作
```typescript
import { taskQueueManager, TaskType, TaskStatus } from './utils/taskQueue';

// 初始化
await taskQueueManager.initialize();

// 添加任务
const taskId = await taskQueueManager.addTask({
  type: TaskType.CHUNKING,
  status: TaskStatus.PENDING,
  priority: 1,
  maxRetries: 3,
  payload: { /* 任务数据 */ },
});

// 监控状态
const stats = await taskQueueManager.getTaskStats();
```

### 2. Worker 管理器接口
```typescript
import { chunkingWorkerManager } from './utils/chunkingWorkerManager';

const taskId = await chunkingWorkerManager.submitChunkingTask(
  'markdown',
  content,
  config,
  enableLogging,
  {
    onProgress: (task) => console.log('进度:', task.progress),
    onCompletion: (task, result) => console.log('完成:', result),
    onError: (task, error) => console.error('错误:', error),
  }
);
```

### 3. 高级接口（保持兼容）
```typescript
import { splitInBackground } from './utils/knowledgeBase';

const taskId = await splitInBackground(
  'smart',
  content,
  config,
  enableLogging,
  callbacks
);
```

## 数据库结构

### tasks 表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | string | 任务唯一标识符 |
| type | TaskType | 任务类型 |
| status | TaskStatus | 任务状态 |
| priority | number | 优先级（数字越小优先级越高）|
| createdAt | number | 创建时间戳 |
| updatedAt | number | 更新时间戳 |
| startedAt | number? | 开始执行时间戳 |
| completedAt | number? | 完成时间戳 |
| error | string? | 错误信息 |
| retryCount | number | 当前重试次数 |
| maxRetries | number | 最大重试次数 |
| payload | any | 任务数据 |
| result | any? | 任务结果 |

## 配置参数

```typescript
class TaskQueueManager {
  private readonly PROCESSING_INTERVAL = 1000; // 队列检查间隔 (ms)
  private readonly MAX_CONCURRENT_TASKS = 3;   // 最大并发任务数
}
```

## 测试验证

### 运行测试
```typescript
// 在浏览器控制台中运行
taskQueueTest.runAllTests();

// 或运行单个测试
taskQueueTest.testBasicQueue();
taskQueueTest.testWorkerManagerIntegration();
taskQueueTest.testQueueRecovery();
```

### 使用示例
```typescript
// 在浏览器控制台中运行
queueExamples.runAllExamples();

// 或运行单个示例
queueExamples.example1_DirectQueueUsage();
queueExamples.example4_BatchProcessing();
```

## 关键优势

1. **持久化**: 任务存储在 IndexedDB 中，应用重启后不会丢失
2. **可靠性**: 自动恢复中断的任务，支持重试机制
3. **性能**: 并发控制避免资源过度消耗
4. **兼容性**: 保持现有 API 的向后兼容
5. **监控**: 提供详细的队列状态和任务进度信息
6. **扩展性**: 支持多种任务类型，易于扩展新功能

## 后续扩展

1. **任务依赖**: 支持任务间的依赖关系
2. **定时任务**: 支持延迟执行和定时任务
3. **任务取消**: 支持取消正在执行的任务
4. **性能监控**: 添加任务执行时间统计
5. **批量操作**: 支持批量任务的原子操作
6. **任务分组**: 支持任务分组和批量管理

## 注意事项

1. **浏览器兼容性**: 需要支持 IndexedDB 的现代浏览器
2. **存储限制**: 受浏览器存储配额限制
3. **内存管理**: 及时清理已完成的任务避免内存泄漏
4. **错误处理**: 提供适当的错误处理和用户反馈
5. **数据迁移**: 未来版本升级时需要考虑数据库结构迁移

这个队列系统为应用提供了可靠的后台任务处理能力，确保即使在应用异常退出的情况下，用户的任务也不会丢失，大大提升了用户体验和系统的可靠性。
