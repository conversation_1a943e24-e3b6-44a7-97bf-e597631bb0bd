import { Node, mergeAttributes, nodeInputRule } from '@tiptap/core';
import { VueNodeViewRenderer } from '@tiptap/vue-3';
// import { NodeSelection } from '@tiptap/pm/state';
import ExcalidrawComponent from '../ExcalidrawComponent.vue';

export interface ExcalidrawOptions {
  HTMLAttributes: Record<string, unknown>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    excalidraw: {
      /**
       * Insert an excalidraw drawing
       */
      insertExcalidraw: (options?: { data?: string }) => ReturnType;
    };
  }
}

export const ExcalidrawExtension = Node.create<ExcalidrawOptions>({
  name: 'excalidraw',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  addAttributes() {
    return {
      data: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-excalidraw'),
        renderHTML: (attributes) => {
          if (!attributes.data) {
            return {};
          }
          return {
            'data-excalidraw': attributes.data,
          };
        },
      },
      width: {
        default: 800,
        parseHTML: (element) => parseInt(element.getAttribute('data-width') || '800'),
        renderHTML: (attributes) => {
          return {
            'data-width': attributes.width,
          };
        },
      },
      height: {
        default: 600,
        parseHTML: (element) => parseInt(element.getAttribute('data-height') || '600'),
        renderHTML: (attributes) => {
          return {
            'data-height': attributes.height,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="excalidraw"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        'data-type': 'excalidraw',
      }),
    ];
  },

  addCommands() {
    return {
      insertExcalidraw:
        (options = {}) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              data: options.data || '',
              width: 800,
              height: 600,
            },
          });
        },
    };
  },

  addInputRules() {
    return [
      nodeInputRule({
        find: /^```excalidraw$/,
        type: this.type,
        getAttributes: () => ({
          data: '',
          width: 800,
          height: 600,
        }),
      }),
    ];
  },

  addKeyboardShortcuts() {
    return {};
  },

  addNodeView() {
    return VueNodeViewRenderer(ExcalidrawComponent);
  },
});
