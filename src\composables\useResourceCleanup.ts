import { onUnmounted } from 'vue';
import { useMemoryManager } from './MemoryManager';
import type { Disposable, ResourceType } from './MemoryManager';
import { isDevelopment } from '../config/performance.config';
import { shouldSkipInitialization, createHotReloadSafeId } from '../utils/hotReloadManager';

/**
 * 统一的资源清理接口
 */
export interface ResourceCleanup {
  dispose(): void;
  isDisposed: boolean;
}

/**
 * 可清理的资源类型
 */
export type CleanupResource =
  | EventTarget
  | AbortController
  | MediaQueryList
  | MutationObserver
  | ResizeObserver
  | IntersectionObserver
  | PerformanceObserver
  | NodeJS.Timeout
  | number // requestAnimationFrame ID
  | (() => void) // 自定义清理函数
  | ResourceCleanup;

/**
 * 资源清理器类
 */
class ResourceCleaner implements Disposable {
  private cleanupFunctions = new Set<() => void>();
  private disposed = false;
  private resourceType: ResourceType;
  private description: string;
  private memoryManagerId: string | null = null;
  private memoryManager: ReturnType<typeof useMemoryManager>['manager'] | undefined;
  private static activeCleaners = new WeakSet<ResourceCleaner>();
  private taggedResources = new Map<string, Set<() => void>>();

  constructor(
    resourceType: ResourceType = 'other',
    description: string = '',
    memoryManager?: ReturnType<typeof useMemoryManager>['manager'],
  ) {
    this.resourceType = resourceType;
    this.description = description;
    this.memoryManager = memoryManager;

    // 防止重复注册
    if (ResourceCleaner.activeCleaners.has(this)) {
      console.warn('ResourceCleaner already active, skipping registration');
      return;
    }

    ResourceCleaner.activeCleaners.add(this);

    // 如果有内存管理器，注册自己
    if (memoryManager) {
      try {
        this.memoryManagerId = memoryManager.register(this, {
          type: resourceType,
          description,
          priority: 'medium',
        });
      } catch (error) {
        console.warn('Failed to register with memory manager:', error);
        this.memoryManagerId = null;
      }
    }
  }

  /**
   * 添加清理函数
   */
  add(cleanupFn: () => void): void {
    if (this.disposed) {
      console.warn(
        '⚠️ [ResourceCleaner] Attempted to add a cleanup function to a disposed cleaner',
      );
      return;
    }

    this.cleanupFunctions.add(cleanupFn);
  }

  /**
   * 添加带标签的资源
   */
  addResource(
    tag: string,
    resource: { cleanup: () => void; priority?: string; type?: string },
  ): void {
    if (this.disposed) {
      console.warn('⚠️ [ResourceCleaner] Attempted to add a resource to a disposed cleaner');
      return;
    }

    if (!this.taggedResources.has(tag)) {
      this.taggedResources.set(tag, new Set());
    }

    const cleanupFn = resource.cleanup;
    const taggedSet = this.taggedResources.get(tag);
    if (taggedSet) {
      taggedSet.add(cleanupFn);
    }
    this.cleanupFunctions.add(cleanupFn);
  }

  /**
   * 按标签清理资源
   */
  cleanupByTag(tag: string): void {
    if (this.disposed) return;

    const taggedFns = this.taggedResources.get(tag);
    if (!taggedFns) return;

    if (isDevelopment) {
      console.log(`🧹 [ResourceCleaner] Cleaning ${taggedFns.size} resources tagged with '${tag}'`);
    }

    taggedFns.forEach((cleanupFn) => {
      try {
        cleanupFn();
        this.cleanupFunctions.delete(cleanupFn);
      } catch (error) {
        console.error(`Error cleaning resources tagged with '${tag}':`, error);
      }
    });

    this.taggedResources.delete(tag);
  }

  /**
   * 清理所有资源
   */
  cleanupAll(): void {
    this.dispose();
  }

  /**
   * 移除清理函数
   */
  remove(cleanupFn: () => void): void {
    this.cleanupFunctions.delete(cleanupFn);
  }

  /**
   * 添加资源（自动生成清理函数）
   */
  addAutoCleanupResource(resource: CleanupResource): void {
    if (this.disposed) {
      console.warn('⚠️ [ResourceCleaner] Attempted to add a resource to a disposed cleaner');
      return;
    }

    const cleanupFn = this.createCleanupFunction(resource);
    if (cleanupFn) {
      this.add(cleanupFn);
    }
  }

  /**
   * 创建清理函数
   */
  private createCleanupFunction(resource: CleanupResource): (() => void) | null {
    // 自定义清理函数
    if (typeof resource === 'function') {
      return resource;
    }

    // 已实现ResourceCleanup接口的对象
    if (resource && typeof resource === 'object' && 'dispose' in resource) {
      return () => {
        if (!resource.isDisposed) {
          resource.dispose();
        }
      };
    }

    // AbortController
    if (resource instanceof AbortController) {
      return () => {
        if (!resource.signal.aborted) {
          resource.abort();
        }
      };
    }

    // Observers
    if (
      resource instanceof MutationObserver ||
      resource instanceof ResizeObserver ||
      resource instanceof IntersectionObserver ||
      resource instanceof PerformanceObserver
    ) {
      return () => {
        try {
          resource.disconnect();
        } catch (error) {
          console.warn('Observer disconnect failed:', error);
        }
      };
    }

    // MediaQueryList
    if (resource instanceof MediaQueryList) {
      return () => {
        // MediaQueryList通常不需要显式清理
        // 但可以移除事件监听器
      };
    }

    // EventTarget (需要与addEventListener配合使用)
    if (resource instanceof EventTarget) {
      console.warn('⚠️ [ResourceCleaner] EventTarget requires event type and handler');
      return null;
    }

    // setTimeout/setInterval返回的ID
    if (typeof resource === 'object' && resource !== null && 'valueOf' in resource) {
      const id = resource.valueOf();
      if (typeof id === 'number') {
        return () => {
          clearTimeout(id);
          clearInterval(id);
        };
      }
    }

    // requestAnimationFrame ID
    if (typeof resource === 'number') {
      return () => {
        cancelAnimationFrame(resource);
      };
    }

    console.warn('⚠️ [ResourceCleaner] Unrecognized resource type:', resource);
    return null;
  }

  /**
   * 添加事件监听器并自动清理
   */
  addEventListener<K extends keyof WindowEventMap>(
    target: EventTarget,
    type: K,
    listener: (this: Window, ev: WindowEventMap[K]) => void,
    options?: boolean | AddEventListenerOptions,
  ): void;
  addEventListener(
    target: EventTarget,
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions,
  ): void;
  addEventListener(
    target: EventTarget,
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions,
  ): void {
    if (this.disposed) return;

    target.addEventListener(type, listener, options);

    this.add(() => {
      target.removeEventListener(type, listener, options);
    });
  }

  /**
   * 创建并管理定时器
   */
  setTimeout(callback: () => void, delay: number): number {
    if (this.disposed) return -1;

    const id = setTimeout(callback, delay) as unknown as number;
    this.add(() => clearTimeout(id));
    return id;
  }

  setInterval(callback: () => void, delay: number): number {
    if (this.disposed) return -1;

    const id = setInterval(callback, delay) as unknown as number;
    this.add(() => clearInterval(id));
    return id;
  }

  requestAnimationFrame(callback: FrameRequestCallback): number {
    if (this.disposed) return -1;

    const id = requestAnimationFrame(callback);
    this.add(() => cancelAnimationFrame(id));
    return id;
  }

  /**
   * 创建并管理观察器
   */
  createMutationObserver(callback: MutationCallback): MutationObserver {
    const observer = new MutationObserver(callback);
    this.addAutoCleanupResource(observer);
    return observer;
  }

  createResizeObserver(callback: ResizeObserverCallback): ResizeObserver {
    const observer = new ResizeObserver(callback);
    this.addAutoCleanupResource(observer);
    return observer;
  }

  createIntersectionObserver(
    callback: IntersectionObserverCallback,
    options?: IntersectionObserverInit,
  ): IntersectionObserver {
    const observer = new IntersectionObserver(callback, options);
    this.addAutoCleanupResource(observer);
    return observer;
  }

  /**
   * 创建并管理AbortController
   */
  createAbortController(): AbortController {
    const controller = new AbortController();
    this.addAutoCleanupResource(controller);
    return controller;
  }

  /**
   * 执行清理
   */
  dispose(): void {
    if (this.disposed) return;

    // 立即标记为已销毁，防止重入
    this.disposed = true;

    // 从活跃清理器集合中移除
    ResourceCleaner.activeCleaners.delete(this);

    if (isDevelopment && this.cleanupFunctions.size > 0) {
      console.log(
        `🧹 [ResourceCleaner] Cleaning ${this.cleanupFunctions.size} resources (${this.resourceType}${this.description ? ': ' + this.description : ''})`,
      );
    }
    const errors: Error[] = [];

    // 执行所有清理函数
    this.cleanupFunctions.forEach((cleanupFn) => {
      try {
        cleanupFn();
      } catch (error) {
        errors.push(error as Error);
      }
    });

    // 清理集合
    this.cleanupFunctions.clear();

    // 从内存管理器中移除 - 防止循环调用
    if (this.memoryManager && this.memoryManagerId) {
      try {
        // 临时保存引用并清空，防止在 unregister 过程中被再次调用
        const managerId = this.memoryManagerId;
        const manager = this.memoryManager;
        this.memoryManagerId = null;
        this.memoryManager = undefined;

        manager.unregister(managerId);
      } catch (error) {
        // 忽略注销错误，可能已经被清理
        console.warn('Failed to unregister from memory manager:', error);
      }
    }

    // 报告错误
    if (errors.length > 0) {
      console.error(
        `❌ [ResourceCleaner] ${errors.length} errors occurred during cleanup:`,
        errors,
      );
    }
  }

  /**
   * 检查是否已销毁
   */
  get isDisposed(): boolean {
    return this.disposed;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      resourceType: this.resourceType,
      description: this.description,
      cleanupFunctionCount: this.cleanupFunctions.size,
      disposed: this.disposed,
    };
  }
}

/**
 * 使用统一资源清理的组合式函数
 */
export function useResourceCleanup(resourceType: ResourceType = 'other', description: string = '') {
  // 创建热重载安全的ID
  const cleanerId = createHotReloadSafeId(`resource_${resourceType}_${description}`);

  // 检查是否应该跳过（热重载情况）
  if (shouldSkipInitialization(cleanerId)) {
    // 返回一个空操作的清理器，避免重复初始化
    return {
      cleaner: null,
      add: () => {},
      addResource: () => {},
      addAutoCleanupResource: () => {},
      cleanupByTag: () => {},
      cleanupAll: () => {},
      addEventListener: () => {},
      setTimeout: () => -1,
      setInterval: () => -1,
      requestAnimationFrame: () => -1,
      createMutationObserver: () => new MutationObserver(() => {}),
      createResizeObserver: () => new ResizeObserver(() => {}),
      createIntersectionObserver: () => new IntersectionObserver(() => {}),
      createAbortController: () => new AbortController(),
      dispose: () => {},
      isDisposed: true,
      stats: () => ({ resourceType, description, cleanupFunctionCount: 0, disposed: true }),
    };
  }

  const memoryManager = useMemoryManager();
  const cleaner = new ResourceCleaner(resourceType, description, memoryManager.manager);

  // 只在 onUnmounted 中清理一次
  onUnmounted(() => {
    if (!cleaner.isDisposed) {
      cleaner.dispose();
    }
  });

  return {
    cleaner,
    add: cleaner.add.bind(cleaner),
    addResource: cleaner.addResource.bind(cleaner),
    addAutoCleanupResource: cleaner.addAutoCleanupResource.bind(cleaner),
    cleanupByTag: cleaner.cleanupByTag.bind(cleaner),
    cleanupAll: cleaner.cleanupAll.bind(cleaner),
    addEventListener: cleaner.addEventListener.bind(cleaner),
    setTimeout: cleaner.setTimeout.bind(cleaner),
    setInterval: cleaner.setInterval.bind(cleaner),
    requestAnimationFrame: cleaner.requestAnimationFrame.bind(cleaner),
    createMutationObserver: cleaner.createMutationObserver.bind(cleaner),
    createResizeObserver: cleaner.createResizeObserver.bind(cleaner),
    createIntersectionObserver: cleaner.createIntersectionObserver.bind(cleaner),
    createAbortController: cleaner.createAbortController.bind(cleaner),
    dispose: cleaner.dispose.bind(cleaner),
    isDisposed: cleaner.isDisposed,
    stats: cleaner.getStats.bind(cleaner),
  };
}

/**
 * 创建全局资源清理器
 */
export function createGlobalResourceCleaner() {
  const cleaners = new Set<ResourceCleaner>();

  const createCleaner = (resourceType: ResourceType = 'other', description: string = '') => {
    const cleaner = new ResourceCleaner(resourceType, description);
    cleaners.add(cleaner);
    return cleaner;
  };

  const disposeAll = () => {
    console.log(`🧹 [GlobalResourceCleaner] Cleaning ${cleaners.size} resource cleaners`);

    cleaners.forEach((cleaner) => {
      if (!cleaner.isDisposed) {
        cleaner.dispose();
      }
    });

    cleaners.clear();
  };

  const getStats = () => {
    return {
      totalCleaners: cleaners.size,
      activeCleaners: Array.from(cleaners).filter((c) => !c.isDisposed).length,
      cleanerStats: Array.from(cleaners).map((c) => c.getStats()),
    };
  };

  return {
    createCleaner,
    disposeAll,
    getStats,
    get size() {
      return cleaners.size;
    },
  };
}

/**
 * 使用引用计数的资源管理
 */
export function useRefCountedResource<T extends Disposable>(
  createResource: () => T,
  resourceId: string,
) {
  // 使用静态存储来跨组件共享资源
  const refCounts =
    useRefCountedResource.refCounts ||
    (useRefCountedResource.refCounts = new Map<string, { resource: T; count: number }>());

  const acquire = (): T => {
    const existing = refCounts.get(resourceId);
    if (existing) {
      existing.count++;
      return existing.resource;
    }

    const resource = createResource();
    refCounts.set(resourceId, { resource, count: 1 });
    return resource;
  };

  const release = (): void => {
    const existing = refCounts.get(resourceId);
    if (!existing) return;

    existing.count--;
    if (existing.count <= 0) {
      try {
        existing.resource.dispose();
      } catch (error) {
        console.warn('Failed to dispose resource:', error);
      }
      refCounts.delete(resourceId);
    }
  };

  // 自动释放 - 添加保护
  let released = false;
  onUnmounted(() => {
    if (!released) {
      released = true;
      release();
    }
  });

  return {
    acquire,
    release,
    get refCount() {
      return refCounts.get(resourceId)?.count || 0;
    },
  };
}

// 为引用计数添加静态属性
useRefCountedResource.refCounts = null;

/**
 * 资源池管理
 */
export class ResourcePool<T extends Disposable> {
  private pool: T[] = [];
  private inUse = new Set<T>();
  private maxSize: number;
  private createResource: () => T;
  private cleaner: ResourceCleaner;

  constructor(createResource: () => T, maxSize: number = 10) {
    this.createResource = createResource;
    this.maxSize = maxSize;
    this.cleaner = new ResourceCleaner('other', 'ResourcePool');
  }

  acquire(): T {
    let resource = this.pool.pop();

    if (!resource) {
      resource = this.createResource();
    }

    this.inUse.add(resource);
    return resource;
  }

  release(resource: T): void {
    if (!this.inUse.has(resource)) return;

    this.inUse.delete(resource);

    if (this.pool.length < this.maxSize) {
      this.pool.push(resource);
    } else {
      resource.dispose();
    }
  }

  dispose(): void {
    // 清理所有资源
    [...this.pool, ...this.inUse].forEach((resource) => {
      resource.dispose();
    });

    this.pool = [];
    this.inUse.clear();
    this.cleaner.dispose();
  }

  getStats() {
    return {
      poolSize: this.pool.length,
      inUseCount: this.inUse.size,
      maxSize: this.maxSize,
    };
  }
}

// 导出类型
export { ResourceCleaner };
