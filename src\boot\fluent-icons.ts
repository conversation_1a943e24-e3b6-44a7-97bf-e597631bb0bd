// Fluent Icons 启动文件
// 注册 Fluent System Icons 到 Quasar

import { boot } from 'quasar/wrappers';
import {
  getFluentIcon,
  createFluentIconClass,
  type FluentIconVariant,
  availableFluentIcons,
} from '../icons/fluent-icons';

// 扩展 QIcon 组件以支持 Fluent Icons
declare module 'quasar' {
  interface QIconProps {
    fluentVariant?: FluentIconVariant;
  }
}

// Fluent Icon 组件渲染函数
function renderFluentIcon(iconName: string, variant: FluentIconVariant = 'regular') {
  // 移除 fluent- 前缀（如果存在）
  const cleanName = iconName.replace(/^fluent-/, '');

  // 获取 Unicode 字符
  const unicode = getFluentIcon(cleanName, variant);

  if (!unicode) {
    console.warn(`Fluent icon "${cleanName}" not found in variant "${variant}"`);
    return null;
  }

  // 创建图标元素
  const iconElement = document.createElement('span');
  iconElement.className = createFluentIconClass(cleanName, variant);
  iconElement.textContent = unicode;

  return iconElement;
}

// 自定义 QIcon 渲染器
const fluentIconRenderer = (iconName: string, node: Element, variant?: FluentIconVariant) => {
  if (!iconName.startsWith('fluent-')) {
    return false; // 不是 Fluent 图标，让其他渲染器处理
  }

  const iconElement = renderFluentIcon(iconName, variant);
  if (iconElement) {
    node.appendChild(iconElement);
    return true;
  }

  return false;
};

export default boot(({ app }) => {
  // 注册全局属性，方便在模板中使用
  app.config.globalProperties.$fluentIcon = {
    get: getFluentIcon,
    createClass: createFluentIconClass,
    available: availableFluentIcons,
    render: renderFluentIcon,
  };

  // 扩展 QIcon 组件
  app.mixin({
    mounted() {
      this.renderFluentIconIfNeeded();
    },
    updated() {
      // 组件更新时也需要重新渲染 Fluent 图标
      this.renderFluentIconIfNeeded();
    },
    methods: {
      renderFluentIconIfNeeded() {
        // 如果是 QIcon 组件且使用 Fluent 图标
        if (this.$options.name === 'QIcon' && this.name?.startsWith('fluent-')) {
          const variant = this.fluentVariant || 'regular';
          const iconElement = renderFluentIcon(this.name, variant);

          if (iconElement && this.$el) {
            // 检查是否已经有 Fluent 图标内容
            const existingFluentIcon = this.$el.querySelector('.fluent-icon');

            if (!existingFluentIcon || existingFluentIcon.textContent !== iconElement.textContent) {
              // 清空原有内容
              this.$el.innerHTML = '';
              // 添加 Fluent 图标
              this.$el.appendChild(iconElement);
            }

            // 确保必要的类名存在
            this.$el.classList.add('fluent-icon', `fluent-${variant}`);
          }
        }
      },
    },
  });

  console.log('✓ Fluent System Icons loaded successfully');
  console.log(`✓ Available icons: ${availableFluentIcons.length}`);
});

// 导出辅助函数供组件使用
export {
  getFluentIcon,
  createFluentIconClass,
  availableFluentIcons,
  renderFluentIcon,
  fluentIconRenderer,
};
