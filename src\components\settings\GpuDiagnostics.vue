<template>
  <q-card bordered>
    <q-card-section>
      <div class="text-h6 q-mb-md">
        <q-icon name="mdi-gpu" class="q-mr-sm" />
        GPU 诊断工具
      </div>

      <div class="row q-gutter-md q-mb-md">
        <q-btn
          color="primary"
          icon="mdi-refresh"
          label="刷新诊断信息"
          @click="refreshDiagnostics"
          :loading="loading"
        />
        <q-btn
          color="warning"
          icon="mdi-restart"
          label="重置GGUF引擎"
          @click="resetGGUF"
          :loading="resetting"
        />
        <q-btn
          color="secondary"
          icon="mdi-test-tube"
          label="测试向量化"
          @click="testEmbedding"
          :loading="testing"
        />
      </div>

      <!-- 诊断信息显示 -->
      <q-card v-if="diagnostics" flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-subtitle1 q-mb-sm">GPU 状态</div>
          <div class="row q-gutter-md">
            <div class="col">
              <q-chip
                :color="diagnostics.gpu_available ? 'green' : 'red'"
                text-color="white"
                :icon="diagnostics.gpu_available ? 'mdi-check' : 'mdi-close'"
              >
                GPU {{ diagnostics.gpu_available ? '可用' : '不可用' }}
              </q-chip>
            </div>
            <div class="col" v-if="diagnostics.gpu_available">
              <div class="text-caption">设备: {{ diagnostics.gpu_device_name }}</div>
              <div class="text-caption">后端: {{ diagnostics.gpu_backend }}</div>
              <div class="text-caption">最大层数: {{ diagnostics.gpu_max_layers }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 模型状态 -->
      <q-card v-if="diagnostics" flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-subtitle1 q-mb-sm">模型状态</div>
          <div class="row q-gutter-md">
            <div class="col">
              <q-chip
                :color="diagnostics.model_loaded ? 'green' : 'orange'"
                text-color="white"
                :icon="diagnostics.model_loaded ? 'mdi-check' : 'mdi-alert'"
              >
                模型 {{ diagnostics.model_loaded ? '已加载' : '未加载' }}
              </q-chip>
            </div>
            <div class="col" v-if="diagnostics.model_loaded">
              <div class="text-caption">
                GPU启用: {{ diagnostics.current_gpu_enabled ? '是' : '否' }}
              </div>
              <div class="text-caption">GPU层数: {{ diagnostics.current_gpu_layers }}</div>
              <div class="text-caption">GPU设备: {{ diagnostics.current_gpu_device }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 推理状态 -->
      <q-card v-if="diagnostics" flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-subtitle1 q-mb-sm">推理状态</div>
          <div class="row q-gutter-md">
            <div class="col">
              <q-chip
                :color="diagnostics.inference_in_progress ? 'orange' : 'green'"
                text-color="white"
                :icon="diagnostics.inference_in_progress ? 'mdi-loading' : 'mdi-check'"
              >
                {{ diagnostics.inference_in_progress ? '推理中' : '空闲' }}
              </q-chip>
            </div>
            <div class="col" v-if="diagnostics.last_error">
              <div class="text-caption text-red">错误: {{ diagnostics.last_error }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 测试结果 -->
      <q-card v-if="testResult" flat bordered>
        <q-card-section>
          <div class="text-subtitle1 q-mb-sm">测试结果</div>
          <pre class="text-caption">{{ testResult }}</pre>
        </q-card-section>
      </q-card>

      <!-- 建议 -->
      <q-card v-if="suggestions.length > 0" flat bordered>
        <q-card-section>
          <div class="text-subtitle1 q-mb-sm">
            <q-icon name="mdi-lightbulb" class="q-mr-sm" />
            优化建议
          </div>
          <ul>
            <li v-for="suggestion in suggestions" :key="suggestion" class="text-caption q-mb-xs">
              {{ suggestion }}
            </li>
          </ul>
        </q-card-section>
      </q-card>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useQuasar } from 'quasar';

interface KnowledgeApi {
  getGpuDiagnostics: () => Promise<string>;
  forceResetLocalGGUF: () => Promise<boolean>;
  testLocalGGUFEmbedding: (text: string) => Promise<string>;
}

interface GpuDiagnosticsData {
  gpu_available: boolean;
  gpu_device_name: string;
  gpu_backend: string;
  gpu_max_layers: number;
  model_loaded: boolean;
  current_gpu_enabled?: boolean;
  current_gpu_layers?: number;
  current_gpu_device?: number;
  inference_in_progress: boolean;
  last_error: string;
}

const $q = useQuasar();

const loading = ref(false);
const resetting = ref(false);
const testing = ref(false);
const diagnostics = ref<GpuDiagnosticsData | null>(null);
const testResult = ref<string>('');

const suggestions = computed(() => {
  const tips: string[] = [];

  if (!diagnostics.value) return tips;

  if (!diagnostics.value.gpu_available) {
    tips.push('GPU不可用，建议检查驱动程序和CUDA/ROCm安装');
  }

  if (diagnostics.value.model_loaded && !diagnostics.value.current_gpu_enabled) {
    tips.push('模型已加载但未启用GPU，建议在设置中启用GPU加速');
  }

  if (diagnostics.value.inference_in_progress) {
    tips.push('检测到推理正在进行中，如果长时间卡住可以尝试重置GGUF引擎');
  }

  if (diagnostics.value.last_error) {
    tips.push('检测到错误，建议重置GGUF引擎或检查模型文件');
  }

  if (diagnostics.value.current_gpu_layers > 35) {
    tips.push('GPU层数较高，如果出现内存问题建议降低到35层以下');
  }

  return tips;
});

const refreshDiagnostics = async () => {
  loading.value = true;
  try {
    const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;
    if (!knowledgeApi?.getGpuDiagnostics) {
      throw new Error('KnowledgeApi not available');
    }

    const result = await knowledgeApi.getGpuDiagnostics();
    diagnostics.value = JSON.parse(result) as GpuDiagnosticsData;

    console.log('🔍 [GpuDiagnostics] 诊断信息:', diagnostics.value);
  } catch (error) {
    console.error('❌ [GpuDiagnostics] 获取诊断信息失败:', error);
    $q.notify({
      type: 'negative',
      message: '获取GPU诊断信息失败',
      timeout: 3000,
    });
  } finally {
    loading.value = false;
  }
};

const resetGGUF = async () => {
  resetting.value = true;
  try {
    const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;
    if (!knowledgeApi?.forceResetLocalGGUF) {
      throw new Error('KnowledgeApi not available');
    }

    const success = await knowledgeApi.forceResetLocalGGUF();

    if (success) {
      $q.notify({
        type: 'positive',
        message: 'GGUF引擎重置成功',
        timeout: 3000,
      });

      // 重新获取诊断信息
      void refreshDiagnostics();
    } else {
      throw new Error('Reset failed');
    }
  } catch (error) {
    console.error('❌ [GpuDiagnostics] 重置GGUF引擎失败:', error);
    $q.notify({
      type: 'negative',
      message: '重置GGUF引擎失败',
      timeout: 3000,
    });
  } finally {
    resetting.value = false;
  }
};

const testEmbedding = async () => {
  testing.value = true;
  testResult.value = '';

  try {
    const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;
    if (!knowledgeApi?.testLocalGGUFEmbedding) {
      throw new Error('KnowledgeApi not available');
    }

    const testText = '这是一个测试文本，用于验证向量化功能是否正常工作。';
    const result = await knowledgeApi.testLocalGGUFEmbedding(testText);
    testResult.value = result;

    const parsed = JSON.parse(result) as {
      success: boolean;
      inference_time_ms?: number;
      error?: string;
    };
    if (parsed.success) {
      $q.notify({
        type: 'positive',
        message: `向量化测试成功，耗时 ${parsed.inference_time_ms}ms`,
        timeout: 3000,
      });
    } else {
      $q.notify({
        type: 'negative',
        message: `向量化测试失败: ${parsed.error}`,
        timeout: 5000,
      });
    }
  } catch (error) {
    console.error('❌ [GpuDiagnostics] 测试向量化失败:', error);
    testResult.value = `测试失败: ${error}`;
    $q.notify({
      type: 'negative',
      message: '向量化测试失败',
      timeout: 3000,
    });
  } finally {
    testing.value = false;
  }
};

onMounted(() => {
  void refreshDiagnostics();
});
</script>
