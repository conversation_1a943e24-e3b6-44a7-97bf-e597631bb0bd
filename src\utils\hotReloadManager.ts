/**
 * 热重载管理器 - 在开发环境中防止资源重复注册
 */

import { isDevelopment } from '../config/performance.config';

class HotReloadManager {
  private static instance: HotReloadManager;
  private reloadCounter = 0;
  private lastReloadTime = 0;
  private componentRegistry = new Map<string, { count: number; lastSeen: number }>();

  private constructor() {
    if (isDevelopment && typeof window !== 'undefined') {
      // 监听热重载事件
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if ((window as any).__VUE_HMR_RUNTIME__) {
        console.log('[HotReloadManager] 检测到 Vue HMR 运行时');
      }
    }
  }

  static getInstance(): HotReloadManager {
    if (!HotReloadManager.instance) {
      HotReloadManager.instance = new HotReloadManager();
    }
    return HotReloadManager.instance;
  }

  /**
   * 检测是否正在热重载
   */
  isHotReloading(): boolean {
    const now = Date.now();
    const timeSinceLastReload = now - this.lastReloadTime;
    // 如果在100ms内，认为是热重载
    return timeSinceLastReload < 100;
  }

  /**
   * 标记热重载
   */
  markReload(): void {
    this.reloadCounter++;
    this.lastReloadTime = Date.now();
  }

  /**
   * 注册组件
   */
  registerComponent(id: string): boolean {
    const now = Date.now();
    const existing = this.componentRegistry.get(id);

    if (existing) {
      const timeSinceLastSeen = now - existing.lastSeen;

      // 如果在500ms内重复注册，可能是热重载
      if (timeSinceLastSeen < 500) {
        existing.count++;
        existing.lastSeen = now;

        if (isDevelopment) {
          console.warn(`[HotReloadManager] 组件 ${id} 可能在热重载 (注册次数: ${existing.count})`);
        }

        return true; // 表示是热重载
      }
    }

    this.componentRegistry.set(id, { count: 1, lastSeen: now });
    return false; // 不是热重载
  }

  /**
   * 清理过期的组件记录
   */
  cleanup(): void {
    const now = Date.now();
    const expireTime = 60000; // 1分钟

    for (const [id, data] of this.componentRegistry.entries()) {
      if (now - data.lastSeen > expireTime) {
        this.componentRegistry.delete(id);
      }
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      reloadCount: this.reloadCounter,
      registeredComponents: this.componentRegistry.size,
      components: Array.from(this.componentRegistry.entries()).map(([id, data]) => ({
        id,
        registrationCount: data.count,
        lastSeen: new Date(data.lastSeen).toLocaleTimeString(),
      })),
    };
  }
}

// 导出单例
export const hotReloadManager = HotReloadManager.getInstance();

// 定期清理
if (isDevelopment && typeof window !== 'undefined') {
  setInterval(() => {
    hotReloadManager.cleanup();
  }, 60000); // 每分钟清理一次
}

/**
 * 创建热重载安全的资源ID
 */
export function createHotReloadSafeId(baseId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 9);
  return `${baseId}_${timestamp}_${random}`;
}

/**
 * 检查是否应该跳过初始化（用于热重载）
 */
export function shouldSkipInitialization(componentId: string): boolean {
  if (!isDevelopment) {
    return false;
  }

  return hotReloadManager.registerComponent(componentId);
}
