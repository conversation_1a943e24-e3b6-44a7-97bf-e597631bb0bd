import type { Message, AssistantMessage as AssistantMessageType, ToolCall } from 'src/types/qwen';
import { computed, type Ref } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import { useUiStore } from 'src/stores/ui';
import { DEFAULT_ANTHROPIC_SETTINGS } from 'src/config/defaultSettings';
import { executeToolCall } from 'src/llm/tools/index';
import { PromptService, type ConversationRole } from 'src/services/promptService';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import { $t } from 'src/composables/useTrans';

export const useAnthropic = () => {
  const { updateConversation } = useSqlite();
  const uiStore = useUiStore();

  const anthropicSettings = computed(() => {
    return uiStore.perferences?.llm?.anthropic || DEFAULT_ANTHROPIC_SETTINGS;
  });

  const readSettings = () => {
    console.log('[useAnthropic] 设置已自动同步:', anthropicSettings.value);
  };

  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const { messages, messagesRef, conversation, tools, enableTools, abortController } = params;

    if (!anthropicSettings.value) return;

    console.log('[useAnthropic] 开始发送消息');
    console.log('[useAnthropic] 消息数量:', messages.length);
    console.log('[useAnthropic] 工具数量:', tools.length);
    console.log('[useAnthropic] 启用工具:', enableTools);

    loading.value = true;

    try {
      // 处理消息格式 - Anthropic API 需要特定的格式
      const anthropicMessages = messages.filter((msg) => msg.role !== 'system');
      const systemMessage = messages.find((msg) => msg.role === 'system')?.content || '';

      // 构建请求头
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'x-api-key': anthropicSettings.value.apiKey,
        'anthropic-version': '2023-06-01',
      };

      // 如果启用了交错思考，添加 beta header
      if (anthropicSettings.value.interleaved_thinking) {
        headers['anthropic-beta'] = 'interleaved-thinking-2025-05-14';
      }

      // 构建请求体
      const requestBody: {
        model: string;
        messages: Message[];
        max_tokens: number;
        temperature?: number;
        top_p?: number;
        stream: boolean;
        system?: string;
        thinking_enabled?: boolean;
        thinking_budget?: number;
        tools?: Array<{
          name: string;
          description: string;
          input_schema: Record<string, unknown>;
        }>;
        tool_choice?: { type: string };
      } = {
        model: anthropicSettings.value.model,
        messages: anthropicMessages,
        max_tokens: anthropicSettings.value.maxTokens || 4096,
        stream: true,
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (
        anthropicSettings.value.temperature !== undefined &&
        !isNaN(anthropicSettings.value.temperature)
      ) {
        requestBody.temperature = anthropicSettings.value.temperature;
      }
      if (anthropicSettings.value.topP !== undefined && !isNaN(anthropicSettings.value.topP)) {
        requestBody.top_p = anthropicSettings.value.topP;
      }

      // 添加系统消息
      if (systemMessage) {
        requestBody.system = systemMessage;
      }

      // 添加思考功能配置
      if (anthropicSettings.value.thinking_enabled) {
        requestBody.thinking_enabled = true;
        if (anthropicSettings.value.thinking_budget) {
          requestBody.thinking_budget = anthropicSettings.value.thinking_budget;
        }
      }

      // 如果有启用的工具，添加工具定义
      if (enableTools && tools.length > 0) {
        console.log(`🔧 [useAnthropic] 启用工具数: ${tools.length}`);
        console.log(`🔧 [useAnthropic] 工具列表: ${tools.map((t) => t.function.name).join(', ')}`);

        // 转换工具格式为 Anthropic 格式
        requestBody.tools = tools.map((tool) => ({
          name: tool.function.name,
          description: tool.function.description,
          input_schema: tool.function.parameters,
        }));
        requestBody.tool_choice = { type: 'auto' };
      }

      const response = await fetch(`${anthropicSettings.value.baseUrl}/messages`, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Anthropic API error: ${errorData.error?.message || response.statusText}`);
      }

      if (!response.body) {
        throw new Error('ReadableStream not supported');
      }

      // 直接操作响应式消息数组，实现流式更新
      messagesRef.value.push({
        role: 'assistant',
        content: '',
        reasoning_content: '',
      });
      const lastMessage = messagesRef.value[messagesRef.value.length - 1];

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      const toolCallsBuffer: Record<string, ToolCall> = {};
      let toolCallIndex = 0;

      while (true) {
        // 检查是否被中断
        if (abortController?.signal.aborted) {
          void reader.cancel();
          throw new DOMException('Operation was aborted', 'AbortError');
        }

        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);

              // 处理不同类型的事件
              if (parsed.type === 'content_block_start') {
                const contentBlock = parsed.content_block;
                if (contentBlock.type === 'thinking') {
                  // 初始化思考内容
                  (lastMessage as AssistantMessageType).reasoning_content = '';
                } else if (contentBlock.type === 'tool_use') {
                  // 初始化工具调用
                  const toolCallId = contentBlock.id;
                  const bufferKey = `tool_${toolCallIndex}`;
                  toolCallsBuffer[bufferKey] = {
                    id: toolCallId,
                    type: 'function',
                    index: toolCallIndex,
                    function: {
                      name: contentBlock.name,
                      arguments: '',
                    },
                  };
                  toolCallIndex++;
                }
              } else if (parsed.type === 'content_block_delta') {
                const delta = parsed.delta;

                if (delta.type === 'thinking_delta') {
                  // 处理思考内容增量
                  (lastMessage as AssistantMessageType).reasoning_content +=
                    delta.thinking_text || '';
                } else if (delta.type === 'text_delta') {
                  // 处理文本内容增量
                  (lastMessage as AssistantMessageType).content += delta.text || '';
                } else if (delta.type === 'input_json_delta') {
                  // 处理工具参数增量
                  const toolCallId = parsed.index;
                  const bufferKey = `tool_${toolCallId}`;
                  if (toolCallsBuffer[bufferKey]) {
                    toolCallsBuffer[bufferKey].function.arguments += delta.partial_json || '';
                  }
                }
              } else if (parsed.type === 'message_delta') {
                // 处理消息级别的更新
                if (parsed.delta.stop_reason) {
                  console.log('[useAnthropic] 消息生成完成，原因:', parsed.delta.stop_reason);
                }
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }

      // 处理完整的工具调用
      const toolCalls = Object.values(toolCallsBuffer);
      if (toolCalls.length > 0 && enableTools) {
        console.log('完整的工具调用列表:', toolCalls);

        // 验证工具调用的完整性
        toolCalls.forEach((toolCall, index) => {
          console.log(`工具调用 ${index + 1}:`, {
            id: toolCall.id,
            functionName: toolCall.function.name,
            argumentsLength: toolCall.function.arguments.length,
            arguments: toolCall.function.arguments,
          });
        });

        // 将工具调用添加到助手消息中
        (lastMessage as AssistantMessageType).tool_calls = toolCalls;

        // 执行工具调用并添加结果消息
        for (const toolCall of toolCalls) {
          try {
            // 验证工具调用完整性
            if (!toolCall.function.name) {
              throw new Error('工具函数名称为空');
            }

            // 解析工具参数
            let parameters = {};
            if (toolCall.function.arguments) {
              try {
                parameters = JSON.parse(toolCall.function.arguments);
                console.log(`解析工具参数成功 - ${toolCall.function.name}:`, parameters);
              } catch (parseError) {
                console.error(`工具参数解析失败 - ${toolCall.function.name}:`, {
                  rawArguments: toolCall.function.arguments,
                  error: parseError,
                });
                throw new Error(`工具参数格式错误: ${parseError.message}`);
              }
            }

            // 执行工具
            console.log(`开始执行工具: ${toolCall.function.name}`, parameters);
            const toolResult = await executeToolCall(toolCall.function.name, parameters);

            // 添加工具结果消息
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify(toolResult),
              tool_call_id: toolCall.id,
            });

            console.log(`工具执行成功 ${toolCall.function.name}:`, {
              parameters,
              result: toolResult,
            });
          } catch (error) {
            console.error(`工具执行失败 ${toolCall.function.name}:`, {
              error: error.message,
              functionName: toolCall.function.name,
              rawArguments: toolCall.function.arguments,
            });

            // 添加错误结果
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify({
                success: false,
                message: $t('src.composeables.useAnthropic.toolExecutionFailed', {
                  error: error.message,
                }),
                toolName: toolCall.function.name,
              }),
              tool_call_id: toolCall.id,
            });
          }
        }

        // 如果有工具调用，递归调用获取AI对工具结果的响应
        console.log('工具执行完成，开始递归调用获取AI响应...');
        console.log('当前消息数量:', messages.length);
        console.log(
          '最后几条消息:',
          messages.slice(-3).map((m) => ({
            role: m.role,
            content:
              typeof m.content === 'string'
                ? m.content.substring(0, 100)
                : JSON.stringify(m.content).substring(0, 100),
          })),
        );

        // 递归调用以获取最终响应
        console.log('🔄 [useAnthropic] 递归调用获取最终响应');
        const recursiveParams: SimplifiedLlmParams = {
          ...params,
          messages: messagesRef.value,
        };
        await sendMessage(recursiveParams, loading);
        return;
      }
      console.log('[useAnthropic] 消息发送完成');
    } catch (error) {
      console.error('[useAnthropic] Error sending message:', error);
      messagesRef.value.push({
        role: 'assistant',
        content: $t('src.composeables.useAnthropic.errorOccurred'),
      });
    } finally {
      loading.value = false;

      await updateConversation(
        conversation.id,
        conversation.title,
        JSON.stringify(messagesRef.value),
        conversation.prompt,
      );
    }
  };

  return {
    readSettings,
    anthropicSettings,
    sendMessage,
    // 导出prompt服务的方法
    getAvailableRoles: () => PromptService.getAvailableRoles(),
    getRoleSuggestions: (role: ConversationRole) => PromptService.getRoleSuggestions(role),
  };
};
