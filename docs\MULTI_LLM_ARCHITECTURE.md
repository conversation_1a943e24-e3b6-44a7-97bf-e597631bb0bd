# 多LLM供应商架构实现

## 概述

本文档描述了inkCop应用中新实现的多LLM供应商架构，支持用户在Qwen和Ollama之间切换。

## 架构设计

### 1. 核心组件

#### LLM路由器 (`useLlmRouter.ts`)
- 统一的LLM供应商管理接口
- 根据用户选择的供应商路由到相应的hook
- 提供统一的`sendMessage`接口

#### 供应商Hook
- `useQwen.ts` - 通义千问供应商实现
- `useOllama.ts` - Ollama供应商实现
- 每个hook独立处理各自的API调用和数据处理逻辑

#### 设置管理
- `LlmSettings.vue` - 供应商选择界面
- `QwenOptions.vue` - 通义千问配置界面
- `OllamaOptions.vue` - Ollama配置界面

### 2. 类型定义

#### LLM设置类型
```typescript
export interface LlmSettings {
  provider: string;
  qwen: QwenSettings;
  ollama: OllamaSettings;
  autoComplete: AutoComplete;
}
```

#### Ollama设置类型
```typescript
export interface OllamaSettings {
  baseUrl: string;
  apiKey: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  seed?: number;
  stop?: string | string[];
  tools?: Tool[];
  tool_choice?: ToolChoice;
  parallel_tool_calls?: boolean;
  avaliableModels?: string[];
}
```

### 3. 默认配置

#### Ollama默认设置
```typescript
export const DEFAULT_OLLAMA_SETTINGS: OllamaSettings = {
  baseUrl: 'http://localhost:11434/v1/chat/completions',
  apiKey: 'ollama',
  model: 'qwen2.5:14b',
  temperature: 0.7,
  maxTokens: 2048,
  stream: true,
  parallel_tool_calls: true,
  avaliableModels: [
    'qwen2.5:14b',
    'qwen2.5:7b',
    'qwen2.5:3b',
    'qwen2.5:1.5b',
    'llama3.2:3b',
    'llama3.2:1b',
    'gemma2:9b',
    'gemma2:2b',
  ],
};
```

## 使用方法

### 1. 切换LLM供应商

1. 打开设置页面
2. 选择"LLM设置"
3. 在左侧供应商列表中选择"通义千问"或"Ollama"
4. 在右侧配置相应的参数

### 2. 配置Ollama

1. 确保本地已安装并运行Ollama服务
2. 在Ollama设置中配置：
   - 服务地址：`http://localhost:11434/v1/chat/completions`
   - API Key：`ollama`（默认值）
   - 选择已安装的模型
3. 点击"测试连接"验证配置
4. 保存设置

### 3. 配置通义千问

1. 在通义千问设置中配置：
   - 服务地址：阿里云API地址
   - API Key：您的通义千问API密钥
   - 选择模型
2. 保存设置

## 技术实现细节

### 1. 消息发送流程

```typescript
// LLM路由器根据当前供应商调用相应的hook
const sendMessage = async (...params) => {
  const provider = currentProvider.value;
  
  if (provider === 'qwen') {
    return await qwenHook.sendMessage(...params);
  } else if (provider === 'ollama') {
    return await ollamaHook.sendMessage(...params);
  }
};
```

### 2. 设置管理

- 使用Pinia store统一管理所有LLM设置
- 支持响应式更新
- 自动保存到本地数据库

### 3. 思考内容处理

Ollama支持`<think>`标签的思考内容处理：
```typescript
const processOllamaContent = (content: string) => {
  // 解析<think>...</think>标签
  // 分离思考内容和实际回答
  return {
    content: processedContent.trim(),
    reasoning_content: reasoning_content.trim(),
  };
};
```

## 测试验证

### 1. 功能测试

- [x] 供应商切换功能
- [x] 设置保存和加载
- [x] Ollama连接测试
- [x] 消息发送和接收
- [x] 思考内容处理

### 2. 界面测试

- [x] 设置界面显示正常
- [x] 供应商图标显示
- [x] 配置表单验证
- [x] 连接状态提示

## 注意事项

1. **向后兼容性**：已移除旧的Ollama测试代码，不再维护向后兼容
2. **错误处理**：每个供应商hook都有独立的错误处理逻辑
3. **性能优化**：使用计算属性和响应式设计，避免不必要的重新渲染
4. **类型安全**：完整的TypeScript类型定义，确保类型安全

## 未来扩展

该架构设计为可扩展的，可以轻松添加新的LLM供应商：

1. 创建新的hook文件（如`useOpenAI.ts`）
2. 添加相应的设置类型定义
3. 在路由器中添加新的条件分支
4. 创建设置界面组件
5. 更新默认配置

这种设计模式使得添加新供应商变得简单且一致。
