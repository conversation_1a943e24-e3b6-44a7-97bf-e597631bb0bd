---
description: 
globs: 
alwaysApply: true
---
# 基础说明
1. 当前项目使用Qt加载Quasar应用
2. Quasar使用vite
3. 类型统一在src/env.d.ts中定义
4. 不允许使用any类型
5. 在Ask模式下不需要生成代码，除非用户要求
6. 当前属于开发阶段，可以不需要处理数据迁移逻辑，如需要重置数据库，可以通知用户手动重置

# 原则
1. 首先理解用户意图，当用户给出方案时，如果有更好的方案应该向用户建议并确认执行方案；
2. 在修改代码前一定阅读根目录下eslint.config.js文件以便确认规则；
3. 使用bun管理依赖，需要处理依赖问题时，务必使用bun；
4. 修改代码后，一定重新检查头文件引入是否正确配置；
5. 一定检查就代码是否有冗余内容，如果存在则删除；
6. 一定不自行添加自认为需要的功能；
7. Agent模式时，除非有多套方案需要用户选择，否则直接修改代码，不要要求用户二次确认；

# 目录结构

```
inkcop/
├── .env                         # 环境配置文件
├── llm/                         # AI 功能目录
│   ├── memory-service/         # Python 微服务
│   │   ├── main.py            # 服务入口
│   │   ├── memory_api.py      # Mem0 API 封装
│   │   ├── config.py          # 配置管理
│   │   ├── requirements.txt   # Python 依赖
│   │   └── build.spec         # PyInstaller 配置
│   ├── integration/           # 集成层
│   │   ├── memory_client.ts   # 前端 Memory 客户端
│   │   └── memory_tools.ts    # AI 工具扩展
│   ├── data/                  # 数据存储
│   │   ├── memory.db         # Mem0 数据
│   │   └── chroma_db/        # ChromaDB 向量存储
│   ├── build-memory-service.sh # 构建脚本
│   ├── run-memory-service.sh   # 开发启动脚本
│   ├── test-config.py          # 配置测试脚本
│   ├── env.template            # 环境配置模板
│   └── README.md              # 本文档
├── qt-src/                     # Qt 源码
│   ├── memoryapi.h            # Qt Memory API 头文件
│   ├── memoryapi.cpp          # Qt Memory API 实现
│   └── ...                    # 现有Qt文件
├── src/                        # Quasar 源码
│   └── ...                    # 现有Quasar文件
└── build/                      # 构建输出
    ├── inkcop-memory-service   # 打包后的可执行文件
    └── memory-service-deploy/  # 部署包
```
