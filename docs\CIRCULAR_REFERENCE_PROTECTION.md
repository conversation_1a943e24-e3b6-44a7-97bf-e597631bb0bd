# 循环引用防护功能

## 功能概述

已为拖拽功能添加了完善的循环引用防护机制，确保文件夹不能被拖拽到自己的子文件夹中，防止创建无效的文件夹层级结构。

## 防护规则

### 1. 不能拖拽到自己
- 文件夹不能拖拽到自己上
- 检查：`dragData.id === dropData.id`

### 2. 不能拖拽到子文件夹
- 文件夹不能拖拽到自己的任何子文件夹（包括深层嵌套的后代文件夹）
- 检查：`docStore.isDescendantOf(dragData.id, dropData.id)`

## 技术实现

### 1. Store 层面检查 (`src/stores/doc.ts`)

在 `moveFolderToParent` 方法中添加了双重检查：

```typescript
// 1. 不能移动到自己
if (targetParentId === folderId) {
  console.warn('Cannot move folder to itself');
  return false;
}

// 2. 不能移动到自己的子文件夹（后代文件夹）
if (targetParentId !== null && this.isDescendantOf(folderId, targetParentId)) {
  console.warn('Cannot move folder to its descendant');
  return false;
}
```

### 2. UI 层面检查 (`src/components/FolderTree.vue`)

在拖拽的 `canDrop` 验证函数中添加了实时检查：

```typescript
(dragData: DragData, dropData: DropData) => {
  if (dragData.type === 'folder') {
    // 1. 不能拖拽到自己
    if (dragData.id === dropData.id) {
      return false;
    }
    
    // 2. 不能拖拽到自己的子文件夹（后代文件夹）
    if (docStore.isDescendantOf(dragData.id, dropData.id)) {
      return false;
    }
  }
  return true;
}
```

### 3. 层级关系检查算法

`isDescendantOf` 方法使用递归算法检查文件夹层级关系：

```typescript
isDescendantOf(ancestorId: number, descendantId: number): boolean {
  const checkDescendant = (folderId: number): boolean => {
    const folder = this.folderMap.get(folderId);
    if (!folder || !folder.children) return false;

    for (const child of folder.children) {
      if (child.id === descendantId) return true;
      if (this.isDescendantOf(child.id, descendantId)) return true;
    }
    return false;
  };

  return checkDescendant(ancestorId);
}
```

## 用户体验

### 1. 实时反馈
- 当用户尝试拖拽文件夹到无效目标时，拖拽操作会被阻止
- 鼠标指针不会显示"可放置"状态
- 无法完成无效的拖拽操作

### 2. 控制台警告
- 当检测到循环引用尝试时，会在控制台输出警告信息
- 帮助开发者调试和理解拖拽限制

## 测试场景

### 场景 1：拖拽到自己
```
文件夹A -> 拖拽到 -> 文件夹A ❌ 被阻止
```

### 场景 2：拖拽到直接子文件夹
```
文件夹A
  └── 文件夹B

文件夹A -> 拖拽到 -> 文件夹B ❌ 被阻止
```

### 场景 3：拖拽到深层子文件夹
```
文件夹A
  └── 文件夹B
      └── 文件夹C
          └── 文件夹D

文件夹A -> 拖拽到 -> 文件夹D ❌ 被阻止
```

### 场景 4：正常拖拽
```
文件夹A
文件夹B
  └── 文件夹C

文件夹A -> 拖拽到 -> 文件夹B ✅ 允许
文件夹C -> 拖拽到 -> 文件夹A ✅ 允许
```

## 性能考虑

- 层级检查算法的时间复杂度为 O(n)，其中 n 是文件夹树的节点数
- 检查在拖拽操作时进行，不影响正常的文件树渲染性能
- 使用了 folderMap 进行快速查找，避免重复遍历

## 安全性

- 双重检查机制：UI 层面 + Store 层面
- 即使 UI 检查被绕过，Store 层面仍会阻止无效操作
- 确保数据库中不会出现循环引用的文件夹结构
