import type { Editor } from '@tiptap/vue-3';
import { ref, computed } from 'vue';
import { useQuasar, Dialog } from 'quasar';
import { convertHtmlTableToMarkdown } from '../../utils/tiptap';

/**
 * 文档操作命令模块
 * 负责文档的创建、删除、复制、快照等文档级别的操作
 */

const $q = useQuasar();

// 快照查看器状态管理
export const snapshotViewerVisible = ref<Map<string, boolean>>(new Map());

// 文档操作状态
const documentOperationState = ref({
  isCreating: false,
  isDeleting: false,
  isCopying: false,
  isExporting: false,
  operationCount: 0,
});

/**
 * 初始化快照查看器状态
 */
export const initSnapshotViewerState = (instanceKey: string) => {
  if (!snapshotViewerVisible.value.has(instanceKey)) {
    snapshotViewerVisible.value.set(instanceKey, false);
  }
};

/**
 * 获取快照查看器状态
 */
export const getSnapshotViewerState = (instanceKey: string) => {
  return snapshotViewerVisible.value.get(instanceKey) ?? false;
};

/**
 * 切换快照查看器状态
 */
export const toggleSnapshotViewer = (instanceKey: string) => {
  const currentState = snapshotViewerVisible.value.get(instanceKey) ?? false;
  snapshotViewerVisible.value.set(instanceKey, !currentState);
};

/**
 * 创建文档操作管理器
 */
export const createDocumentManager = (
  getCurrentEditor: () => Editor | null,
  getCurrentDocId: () => number,
  docStore: {
    createDocument: (
      title: string,
      folderId: number,
    ) => Promise<{ success: boolean; docId?: number; error?: string }>;
    saveDocument: (
      docId: number,
      title: string,
      content: unknown,
      folderId: number,
    ) => Promise<{ success: boolean; error?: string }>;
    deleteDocument: (docId: number) => Promise<{ success: boolean; error?: string }>;
    getDocumentTitle: (docId: number) => string | null;
    getDocument: (docId: number) => { folder_id?: number } | null;
    createSnapshot: (
      docId: number,
      name: string,
      content: string,
    ) => Promise<{ success: boolean; snapshotId?: number; error?: string }>;
    getSnapshot: (snapshotId: number) => Promise<{ name: string; content: string } | null>;
    updateDocumentTitle: (
      docId: number,
      title: string,
    ) => Promise<{ success: boolean; error?: string }>;
    moveDocumentToFolder: (
      docId: number,
      folderId: number,
    ) => Promise<{ success: boolean; error?: string }>;
    getFolderName: (folderId: number) => string | null;
  },
  immediatelySave?: () => void,
) => {
  /**
   * 创建新文档
   */
  const createNewDocument = async (
    title?: string,
    folderId?: number,
  ): Promise<{ success: boolean; docId?: number; error?: string }> => {
    try {
      documentOperationState.value.isCreating = true;
      documentOperationState.value.operationCount++;

      const newTitle = title || `新文档_${new Date().toLocaleString()}`;
      const targetFolderId = folderId ?? -1;

      // 调用store创建文档
      const result = await docStore.createDocument(newTitle, targetFolderId);

      if (result.success && result.docId) {
        $q.notify({
          type: 'positive',
          message: '文档创建成功',
          caption: newTitle,
          position: 'top',
        });

        return { success: true, docId: result.docId };
      } else {
        throw new Error(result.error || '创建文档失败');
      }
    } catch (error) {
      console.error('创建文档失败:', error);
      $q.notify({
        type: 'negative',
        message: '创建文档失败',
        caption: error instanceof Error ? error.message : '未知错误',
        position: 'top',
      });
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    } finally {
      documentOperationState.value.isCreating = false;
      documentOperationState.value.operationCount--;
    }
  };

  /**
   * 复制文档
   */
  const duplicateDocument = async (
    sourceDocId?: number,
    newTitle?: string,
  ): Promise<{ success: boolean; docId?: number; error?: string }> => {
    try {
      documentOperationState.value.isCopying = true;
      documentOperationState.value.operationCount++;

      const sourceId = sourceDocId || getCurrentDocId();
      if (!sourceId) {
        throw new Error('没有指定要复制的文档');
      }

      const editor = getCurrentEditor();
      if (!editor) {
        throw new Error('编辑器不可用');
      }

      // 获取当前文档内容
      const content = editor.getJSON();
      const originalTitle = docStore.getDocumentTitle(sourceId) || '未命名文档';
      const copyTitle = newTitle || `${originalTitle} - 副本`;

      // 获取原文档的文件夹ID
      const originalDoc = docStore.getDocument(sourceId);
      const folderId = originalDoc?.folder_id ?? -1;

      // 创建新文档
      const result = await docStore.createDocument(copyTitle, folderId);

      if (result.success && result.docId) {
        // 保存内容到新文档
        const saveResult = await docStore.saveDocument(result.docId, copyTitle, content, folderId);

        if (saveResult.success) {
          $q.notify({
            type: 'positive',
            message: '文档复制成功',
            caption: copyTitle,
            position: 'top',
          });

          return { success: true, docId: result.docId };
        } else {
          throw new Error(saveResult.error || '保存复制的文档内容失败');
        }
      } else {
        throw new Error(result.error || '创建复制文档失败');
      }
    } catch (error) {
      console.error('复制文档失败:', error);
      $q.notify({
        type: 'negative',
        message: '复制文档失败',
        caption: error instanceof Error ? error.message : '未知错误',
        position: 'top',
      });
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    } finally {
      documentOperationState.value.isCopying = false;
      documentOperationState.value.operationCount--;
    }
  };

  /**
   * 删除文档（带确认）
   */
  const deleteDocument = async (docId?: number): Promise<{ success: boolean; error?: string }> => {
    try {
      const targetDocId = docId || getCurrentDocId();
      if (!targetDocId) {
        throw new Error('没有指定要删除的文档');
      }

      const docTitle = docStore.getDocumentTitle(targetDocId) || '未命名文档';

      // 确认删除
      const confirmed = await new Promise<boolean>((resolve) => {
        Dialog.create({
          title: '确认删除',
          message: `确定要删除文档"${docTitle}"吗？此操作无法撤销。`,
          persistent: false,
          ok: {
            label: '删除',
            color: 'negative',
          },
          cancel: {
            label: '取消',
            color: 'grey',
          },
        })
          .onOk(() => resolve(true))
          .onCancel(() => resolve(false));
      });

      if (!confirmed) {
        return { success: false, error: '用户取消删除' };
      }

      documentOperationState.value.isDeleting = true;
      documentOperationState.value.operationCount++;

      // 执行删除
      const result = await docStore.deleteDocument(targetDocId);

      if (result.success) {
        $q.notify({
          type: 'positive',
          message: '文档删除成功',
          caption: docTitle,
          position: 'top',
        });

        return { success: true };
      } else {
        throw new Error(result.error || '删除文档失败');
      }
    } catch (error) {
      console.error('删除文档失败:', error);
      $q.notify({
        type: 'negative',
        message: '删除文档失败',
        caption: error instanceof Error ? error.message : '未知错误',
        position: 'top',
      });
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    } finally {
      documentOperationState.value.isDeleting = false;
      documentOperationState.value.operationCount--;
    }
  };

  /**
   * 导出文档
   */
  const exportDocument = (
    format: 'markdown' | 'html' | 'json' | 'txt',
    docId?: number,
  ): Promise<{ success: boolean; content?: string; error?: string }> => {
    return new Promise((resolve) => {
      try {
        documentOperationState.value.isExporting = true;
        documentOperationState.value.operationCount++;

        const targetDocId = docId || getCurrentDocId();
        if (!targetDocId) {
          resolve({ success: false, error: '没有指定要导出的文档' });
          return;
        }

        const editor = getCurrentEditor();
        if (!editor) {
          resolve({ success: false, error: '编辑器不可用' });
          return;
        }

        let content: string;
        let mimeType: string;
        let extension: string;

        switch (format) {
          case 'markdown':
            // 需要安装markdown扩展或手动转换
            content = editor.storage.markdown?.getMarkdown() || editor.getText();
            // 转换HTML表格为Markdown表格
            content = convertHtmlTableToMarkdown(content);
            mimeType = 'text/markdown';
            extension = 'md';
            break;
          case 'html':
            content = editor.getHTML();
            mimeType = 'text/html';
            extension = 'html';
            break;
          case 'json':
            content = JSON.stringify(editor.getJSON(), null, 2);
            mimeType = 'application/json';
            extension = 'json';
            break;
          case 'txt':
            content = editor.getText();
            mimeType = 'text/plain';
            extension = 'txt';
            break;
          default:
            resolve({ success: false, error: `不支持的导出格式: ${format as string}` });
            return;
        }

        // 创建下载
        const docTitle = docStore.getDocumentTitle(targetDocId) || '未命名文档';
        const filename = `${docTitle}.${extension}`;

        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        $q.notify({
          type: 'positive',
          message: '文档导出成功',
          caption: `已保存为 ${filename}`,
          position: 'top',
        });

        resolve({ success: true, content });
      } catch (error) {
        console.error('导出文档失败:', error);
        $q.notify({
          type: 'negative',
          message: '导出文档失败',
          caption: error instanceof Error ? error.message : '未知错误',
          position: 'top',
        });
        resolve({ success: false, error: error instanceof Error ? error.message : '未知错误' });
      } finally {
        documentOperationState.value.isExporting = false;
        documentOperationState.value.operationCount--;
      }
    });
  };

  /**
   * 创建文档快照
   */
  const createSnapshot = async (
    name?: string,
    docId?: number,
  ): Promise<{ success: boolean; snapshotId?: number; error?: string }> => {
    try {
      const targetDocId = docId || getCurrentDocId();
      if (!targetDocId) {
        throw new Error('没有指定要创建快照的文档');
      }

      const editor = getCurrentEditor();
      if (!editor) {
        throw new Error('编辑器不可用');
      }

      const content = JSON.stringify(editor.getJSON());
      const snapshotName = name || `快照_${new Date().toLocaleString()}`;

      const result = await docStore.createSnapshot(targetDocId, snapshotName, content);

      if (result.success) {
        $q.notify({
          type: 'positive',
          message: '快照创建成功',
          caption: snapshotName,
          position: 'top',
        });

        return { success: true, snapshotId: result.snapshotId };
      } else {
        throw new Error(result.error || '创建快照失败');
      }
    } catch (error) {
      console.error('创建快照失败:', error);
      $q.notify({
        type: 'negative',
        message: '创建快照失败',
        caption: error instanceof Error ? error.message : '未知错误',
        position: 'top',
      });
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    }
  };

  /**
   * 恢复快照
   */
  const restoreSnapshot = async (
    snapshotId: number,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const editor = getCurrentEditor();
      if (!editor) {
        throw new Error('编辑器不可用');
      }

      const snapshot = await docStore.getSnapshot(snapshotId);
      if (!snapshot || !snapshot.content) {
        throw new Error('快照不存在或内容为空');
      }

      // 确认恢复
      const confirmed = await new Promise<boolean>((resolve) => {
        Dialog.create({
          title: '确认恢复快照',
          message: `确定要恢复快照"${snapshot.name}"吗？当前未保存的更改将丢失。`,
          persistent: false,
          ok: {
            label: '恢复',
            color: 'primary',
          },
          cancel: {
            label: '取消',
            color: 'grey',
          },
        })
          .onOk(() => resolve(true))
          .onCancel(() => resolve(false));
      });

      if (!confirmed) {
        return { success: false, error: '用户取消恢复' };
      }

      // 恢复内容
      const content = JSON.parse(snapshot.content);
      editor.chain().clearContent().insertContent(content).run();

      // 立即保存
      if (immediatelySave) {
        immediatelySave();
      }

      $q.notify({
        type: 'positive',
        message: '快照恢复成功',
        caption: snapshot.name,
        position: 'top',
      });

      return { success: true };
    } catch (error) {
      console.error('恢复快照失败:', error);
      $q.notify({
        type: 'negative',
        message: '恢复快照失败',
        caption: error instanceof Error ? error.message : '未知错误',
        position: 'top',
      });
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    }
  };

  /**
   * 重命名文档
   */
  const renameDocument = async (
    newTitle: string,
    docId?: number,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const targetDocId = docId || getCurrentDocId();
      if (!targetDocId) {
        throw new Error('没有指定要重命名的文档');
      }

      if (!newTitle.trim()) {
        throw new Error('文档名称不能为空');
      }

      const result = await docStore.updateDocumentTitle(targetDocId, newTitle.trim());

      if (result.success) {
        $q.notify({
          type: 'positive',
          message: '文档重命名成功',
          caption: newTitle,
          position: 'top',
        });

        return { success: true };
      } else {
        throw new Error(result.error || '重命名失败');
      }
    } catch (error) {
      console.error('重命名文档失败:', error);
      $q.notify({
        type: 'negative',
        message: '重命名文档失败',
        caption: error instanceof Error ? error.message : '未知错误',
        position: 'top',
      });
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    }
  };

  /**
   * 移动文档到文件夹
   */
  const moveDocumentToFolder = async (
    folderId: number,
    docId?: number,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const targetDocId = docId || getCurrentDocId();
      if (!targetDocId) {
        throw new Error('没有指定要移动的文档');
      }

      const result = await docStore.moveDocumentToFolder(targetDocId, folderId);

      if (result.success) {
        const folderName = docStore.getFolderName(folderId) || '根目录';
        $q.notify({
          type: 'positive',
          message: '文档移动成功',
          caption: `已移动到"${folderName}"`,
          position: 'top',
        });

        return { success: true };
      } else {
        throw new Error(result.error || '移动失败');
      }
    } catch (error) {
      console.error('移动文档失败:', error);
      $q.notify({
        type: 'negative',
        message: '移动文档失败',
        caption: error instanceof Error ? error.message : '未知错误',
        position: 'top',
      });
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    }
  };

  /**
   * 获取文档统计信息
   */
  const getDocumentStats = () => {
    const editor = getCurrentEditor();
    if (!editor) {
      return {
        characters: 0,
        charactersNoSpaces: 0,
        words: 0,
        paragraphs: 0,
        lines: 0,
      };
    }

    const text = editor.getText();
    const html = editor.getHTML();

    return {
      characters: text.length,
      charactersNoSpaces: text.replace(/\s/g, '').length,
      words: text.split(/\s+/).filter((word) => word.length > 0).length,
      paragraphs: html.split('</p>').length - 1,
      lines: text.split('\n').length,
    };
  };

  return {
    // 文档操作
    createNewDocument,
    duplicateDocument,
    deleteDocument,
    exportDocument,
    renameDocument,
    moveDocumentToFolder,

    // 快照操作
    createSnapshot,
    restoreSnapshot,

    // 统计信息
    getDocumentStats,

    // 状态
    documentOperationState,
  };
};

/**
 * 计算文档操作统计
 */
export const getDocumentOperationStats = computed(() => {
  return {
    ...documentOperationState.value,
    isAnyOperationActive: documentOperationState.value.operationCount > 0,
  };
});

/**
 * 清理快照状态
 */
export const cleanupSnapshotStates = (instanceKey?: string) => {
  if (instanceKey) {
    snapshotViewerVisible.value.delete(instanceKey);
  } else {
    snapshotViewerVisible.value.clear();
  }
};

export default {
  snapshotViewerVisible,
  initSnapshotViewerState,
  getSnapshotViewerState,
  toggleSnapshotViewer,
  createDocumentManager,
  getDocumentOperationStats,
  cleanupSnapshotStates,
};
