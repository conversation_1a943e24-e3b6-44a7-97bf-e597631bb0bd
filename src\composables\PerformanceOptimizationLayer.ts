import { ref } from 'vue';
import type { Editor } from '@tiptap/vue-3';
import { MemoryManager } from './MemoryManager';
import { PerformanceMonitor } from './PerformanceMonitor';
import { EditorStateManager } from './EditorStateManager';
import { EditorInstancePool } from './EditorInstancePool';
import { VirtualDragSystem } from './VirtualDragSystem';
import { CommandManager } from './CommandManager';
import {
  MEMORY_THRESHOLDS,
  PERFORMANCE_MONITOR,
  EDITOR_POOL,
  VIRTUAL_RENDERING,
  STATE_MANAGEMENT,
  COMMAND_SYSTEM,
  DRAG_SYSTEM,
  isDevelopment,
} from '../config/performance.config';

// Performance optimization configuration
export interface PerformanceConfig {
  // Memory management
  memoryCheckInterval: number;
  memoryWarningThreshold: number;
  memoryCriticalThreshold: number;
  autoCleanupEnabled: boolean;

  // Editor instance pooling
  maxEditorInstances: number;
  instancePrewarmCount: number;
  instanceRecycleDelay: number;

  // Virtual rendering
  virtualRenderingEnabled: boolean;
  virtualBufferSize: number;
  virtualScrollThreshold: number;

  // State management
  stateUpdateBatchDelay: number;
  statePersistenceEnabled: boolean;
  statePersistenceInterval: number;

  // Performance monitoring
  performanceTrackingEnabled: boolean;
  performanceReportInterval: number;
  performanceMetricsRetention: number;

  // Command system
  commandHistoryLimit: number;
  commandExecutionTimeout: number;

  // Drag system
  dragThrottleDelay: number;
  dragSmoothing: boolean;
  dragPrediction: boolean;
}

export interface PerformanceMetrics {
  memory: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss?: number;
  };
  editors: {
    total: number;
    active: number;
    pooled: number;
    recycled: number;
  };
  performance: {
    fps: number;
    frameTime: number;
    updateTime: number;
    renderTime: number;
  };
  operations: {
    commandsExecuted: number;
    stateUpdates: number;
    memoryCleanups: number;
    instanceRecycles: number;
  };
}

export class PerformanceOptimizationLayer {
  private static instance: PerformanceOptimizationLayer | null = null;

  // Core systems
  private memoryManager = new MemoryManager();
  private performanceMonitor = new PerformanceMonitor();
  private editorStateManager = new EditorStateManager();
  private editorInstancePool = new EditorInstancePool();
  private virtualDragSystem = VirtualDragSystem.getInstance();
  private commandManager = CommandManager.getInstance();

  // Configuration
  private config: PerformanceConfig = {
    // Memory management - 使用统一配置
    memoryCheckInterval: 30000, // 30 seconds
    memoryWarningThreshold: MEMORY_THRESHOLDS.current.warning,
    memoryCriticalThreshold: MEMORY_THRESHOLDS.current.critical,
    autoCleanupEnabled: true,

    // Editor instance pooling - 使用统一配置
    maxEditorInstances: EDITOR_POOL.maxInstances,
    instancePrewarmCount: EDITOR_POOL.prewarmCount,
    instanceRecycleDelay: EDITOR_POOL.recycleDelay,

    // Virtual rendering - 使用统一配置
    virtualRenderingEnabled: VIRTUAL_RENDERING.enabled,
    virtualBufferSize: VIRTUAL_RENDERING.bufferSize,
    virtualScrollThreshold: VIRTUAL_RENDERING.scrollThreshold,

    // State management - 使用统一配置
    stateUpdateBatchDelay: STATE_MANAGEMENT.batchDelay,
    statePersistenceEnabled: STATE_MANAGEMENT.persistenceEnabled,
    statePersistenceInterval: STATE_MANAGEMENT.persistenceInterval,

    // Performance monitoring - 使用统一配置
    performanceTrackingEnabled: PERFORMANCE_MONITOR.enabled,
    performanceReportInterval: PERFORMANCE_MONITOR.reportInterval,
    performanceMetricsRetention: PERFORMANCE_MONITOR.metricsRetention,

    // Command system - 使用统一配置
    commandHistoryLimit: COMMAND_SYSTEM.historyLimit,
    commandExecutionTimeout: COMMAND_SYSTEM.executionTimeout,

    // Drag system - 使用统一配置
    dragThrottleDelay: DRAG_SYSTEM.throttleDelay,
    dragSmoothing: DRAG_SYSTEM.smoothing,
    dragPrediction: DRAG_SYSTEM.prediction,
  };

  // State
  private isInitialized = ref(false);
  private isOptimizing = ref(false);
  private currentMetrics = ref<PerformanceMetrics>({
    memory: { heapUsed: 0, heapTotal: 0, external: 0 },
    editors: { total: 0, active: 0, pooled: 0, recycled: 0 },
    performance: { fps: 60, frameTime: 16, updateTime: 0, renderTime: 0 },
    operations: { commandsExecuted: 0, stateUpdates: 0, memoryCleanups: 0, instanceRecycles: 0 },
  });

  // Timers and intervals
  private memoryCheckInterval: number | null = null;
  private performanceReportInterval: number | null = null;
  private statePersistenceInterval: number | null = null;

  // Optimization strategies
  private optimizationStrategies = new Map<string, () => void>();

  private constructor() {
    this.registerOptimizationStrategies();
  }

  static getInstance(): PerformanceOptimizationLayer {
    if (!PerformanceOptimizationLayer.instance) {
      PerformanceOptimizationLayer.instance = new PerformanceOptimizationLayer();
    }
    return PerformanceOptimizationLayer.instance;
  }

  // Initialize all performance systems
  initialize(customConfig?: Partial<PerformanceConfig>): void {
    if (this.isInitialized.value) {
      if (isDevelopment) {
        console.warn('Performance optimization layer already initialized');
      }
      return;
    }

    // Merge custom config
    if (customConfig) {
      this.config = { ...this.config, ...customConfig };
    }

    // Initialize subsystems
    this.initializeMemoryManagement();
    this.initializeEditorPooling();
    this.initializePerformanceMonitoring();
    this.initializeStateManagement();
    this.initializeCommandSystem();
    this.initializeDragSystem();

    // Start background tasks
    this.startBackgroundTasks();

    this.isInitialized.value = true;
    if (isDevelopment) {
      console.log('Performance optimization layer initialized');
    }
  }

  // Update configuration
  updateConfig(updates: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...updates };

    // Restart background tasks with new config
    this.stopBackgroundTasks();
    this.startBackgroundTasks();
  }

  // Get current configuration
  getConfig(): PerformanceConfig {
    return { ...this.config };
  }

  // Get current metrics
  getMetrics(): PerformanceMetrics {
    const memUsage = this.memoryManager.getMemoryUsage();
    return {
      memory: {
        heapUsed: memUsage.usedJSHeapSize,
        heapTotal: memUsage.totalJSHeapSize,
        external: 0,
        rss: undefined,
      },
      editors: this.getEditorMetrics(),
      performance: this.getPerformanceMetrics(),
      operations: this.getOperationMetrics(),
    };
  }

  // Enable/disable specific optimizations
  setOptimizationEnabled(optimization: string, enabled: boolean): void {
    switch (optimization) {
      case 'virtualRendering':
        this.config.virtualRenderingEnabled = enabled;
        break;
      case 'autoCleanup':
        this.config.autoCleanupEnabled = enabled;
        break;
      case 'performanceTracking':
        this.config.performanceTrackingEnabled = enabled;
        break;
      case 'statePersistence':
        this.config.statePersistenceEnabled = enabled;
        break;
      case 'dragSmoothing':
        this.config.dragSmoothing = enabled;
        this.virtualDragSystem.setSmoothing(enabled);
        break;
      case 'dragPrediction':
        this.config.dragPrediction = enabled;
        this.virtualDragSystem.setPrediction(enabled);
        break;
    }
  }

  // Run optimization pass
  optimize(): void {
    if (this.isOptimizing.value) {
      if (isDevelopment) {
        console.warn('Optimization already in progress');
      }
      return;
    }

    this.isOptimizing.value = true;
    this.performanceMonitor.startTiming('optimization-pass');

    try {
      // Run all optimization strategies
      for (const [name, strategy] of this.optimizationStrategies) {
        this.performanceMonitor.startTiming(`optimize-${name}`);
        try {
          strategy();
        } catch (error) {
          console.error(`Optimization strategy '${name}' failed:`, error);
        } finally {
          this.performanceMonitor.endTiming(`optimize-${name}`);
        }
      }

      // Update metrics
      this.currentMetrics.value = this.getMetrics();
    } finally {
      this.performanceMonitor.endTiming('optimization-pass');
      this.isOptimizing.value = false;
    }
  }

  // Register a new editor
  registerEditor(editorId: string, editor: Editor): void {
    // Register with state manager
    this.editorStateManager.registerEditor(editorId, editor);

    // Register with command manager
    this.commandManager.setEditor(editor);

    // Setup performance tracking
    if (this.config.performanceTrackingEnabled) {
      this.trackEditorPerformance(editorId, editor);
    }
  }

  // Unregister an editor
  unregisterEditor(editorId: string): void {
    this.editorStateManager.unregisterEditor(editorId);
  }

  // Private methods
  private initializeMemoryManagement(): void {
    // Configure memory manager
    this.memoryManager.setThresholds(
      this.config.memoryWarningThreshold,
      this.config.memoryCriticalThreshold,
    );

    // Setup auto cleanup is handled internally by MemoryManager
  }

  private initializeEditorPooling(): void {
    // Configure instance pool
    this.editorInstancePool.setMaxInstances(this.config.maxEditorInstances);

    // Prewarm instances
    for (let i = 0; i < this.config.instancePrewarmCount; i++) {
      void this.editorInstancePool.prewarmInstance();
    }
  }

  private initializePerformanceMonitoring(): void {
    if (!this.config.performanceTrackingEnabled) return;

    // Setup FPS monitoring
    let lastTime = performance.now();
    let frames = 0;

    const measureFPS = () => {
      frames++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        this.currentMetrics.value.performance.fps = frames;
        this.currentMetrics.value.performance.frameTime = 1000 / frames;
        frames = 0;
        lastTime = currentTime;
      }

      if (this.config.performanceTrackingEnabled) {
        requestAnimationFrame(measureFPS);
      }
    };

    requestAnimationFrame(measureFPS);
  }

  private initializeStateManagement(): void {
    // Configure state manager
    this.editorStateManager.setBatchDelay(this.config.stateUpdateBatchDelay);

    // Setup persistence
    if (this.config.statePersistenceEnabled) {
      this.editorStateManager.enablePersistence();
    }
  }

  private initializeCommandSystem(): void {
    // Configure command manager
    // Command manager is self-initializing

    // Track command execution
    this.commandManager.onAfterExecute(() => {
      this.currentMetrics.value.operations.commandsExecuted++;
    });
  }

  private initializeDragSystem(): void {
    // Configure drag system
    this.virtualDragSystem.setThrottleDelay(this.config.dragThrottleDelay);
    this.virtualDragSystem.setSmoothing(this.config.dragSmoothing);
    this.virtualDragSystem.setPrediction(this.config.dragPrediction);
  }

  private startBackgroundTasks(): void {
    // Memory monitoring
    if (this.config.autoCleanupEnabled) {
      this.memoryCheckInterval = window.setInterval(() => {
        const memoryUsage = this.memoryManager.getMemoryUsage();
        // Check thresholds manually
        if (memoryUsage.used > this.config.memoryCriticalThreshold) {
          this.runAggressiveCleanup();
        } else if (memoryUsage.used > this.config.memoryWarningThreshold) {
          this.runModerateCleanup();
        }
      }, this.config.memoryCheckInterval);
    }

    // Performance reporting
    if (this.config.performanceTrackingEnabled) {
      this.performanceReportInterval = window.setInterval(() => {
        this.reportPerformanceMetrics();
      }, this.config.performanceReportInterval);
    }

    // State persistence
    if (this.config.statePersistenceEnabled) {
      this.statePersistenceInterval = window.setInterval(() => {
        this.editorStateManager.persistState();
      }, this.config.statePersistenceInterval);
    }
  }

  private stopBackgroundTasks(): void {
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
    }

    if (this.performanceReportInterval) {
      clearInterval(this.performanceReportInterval);
      this.performanceReportInterval = null;
    }

    if (this.statePersistenceInterval) {
      clearInterval(this.statePersistenceInterval);
      this.statePersistenceInterval = null;
    }
  }

  private registerOptimizationStrategies(): void {
    // Memory optimization
    this.optimizationStrategies.set('memory-cleanup', () => {
      const freed = this.memoryManager.getCurrentUsage();
      this.memoryManager.performCleanup();
      const afterCleanup = this.memoryManager.getCurrentUsage();
      const actualFreed = freed - afterCleanup;
      if (isDevelopment) {
        console.log(`Memory cleanup freed approximately ${actualFreed} bytes`);
      }
    });

    // Editor instance optimization
    this.optimizationStrategies.set('instance-recycling', () => {
      const recycled = this.editorInstancePool.recycleInactiveInstances();
      this.currentMetrics.value.operations.instanceRecycles += recycled;
      if (isDevelopment) {
        console.log(`Recycled ${recycled} editor instances`);
      }
    });

    // State optimization
    this.optimizationStrategies.set('state-compression', () => {
      this.editorStateManager.compressHistory();
      this.currentMetrics.value.operations.stateUpdates++;
    });

    // DOM optimization
    this.optimizationStrategies.set('dom-cleanup', () => {
      this.cleanupDetachedNodes();
    });

    // Event listener optimization
    this.optimizationStrategies.set('listener-cleanup', () => {
      this.cleanupEventListeners();
    });
  }

  private runModerateCleanup(): void {
    if (isDevelopment) {
      console.log('Running moderate cleanup...');
    }
    this.optimizationStrategies.get('memory-cleanup')?.();
    this.optimizationStrategies.get('instance-recycling')?.();
    this.currentMetrics.value.operations.memoryCleanups++;
  }

  private runAggressiveCleanup(): void {
    if (isDevelopment) {
      console.log('Running aggressive cleanup...');
    }

    // Run all optimization strategies
    void this.optimize();

    // Force garbage collection if available
    if ('gc' in window) {
      (window as unknown as { gc: () => void }).gc();
    }

    this.currentMetrics.value.operations.memoryCleanups++;
  }

  private trackEditorPerformance(editorId: string, editor: Editor): void {
    // Track transaction times
    editor.on('update', () => {
      // Performance monitor doesn't have getLastMeasurement, using a placeholder
      const updateTime = 0; // this.performanceMonitor.getLastMeasurement('editor-update') || 0
      this.currentMetrics.value.performance.updateTime =
        (this.currentMetrics.value.performance.updateTime + updateTime) / 2;
    });
  }

  private getEditorMetrics() {
    const instances = this.editorInstancePool.getAllInstances();
    const activeInstances = instances.filter((i) => i.status === 'active');
    const pooledInstances = instances.filter((i) => i.status === 'idle');

    return {
      total: instances.length,
      active: activeInstances.length,
      pooled: pooledInstances.length,
      recycled: this.currentMetrics.value.operations.instanceRecycles,
    };
  }

  private getPerformanceMetrics() {
    return {
      fps: this.currentMetrics.value.performance.fps,
      frameTime: this.currentMetrics.value.performance.frameTime,
      updateTime: this.currentMetrics.value.performance.updateTime,
      renderTime: this.currentMetrics.value.performance.renderTime,
    };
  }

  private getOperationMetrics() {
    return {
      commandsExecuted: this.currentMetrics.value.operations.commandsExecuted,
      stateUpdates: this.currentMetrics.value.operations.stateUpdates,
      memoryCleanups: this.currentMetrics.value.operations.memoryCleanups,
      instanceRecycles: this.currentMetrics.value.operations.instanceRecycles,
    };
  }

  private reportPerformanceMetrics(): void {
    const metrics = this.getMetrics();

    // Log summary
    if (isDevelopment) {
      console.log('Performance Report:', {
        memory: `${Math.round(metrics.memory.heapUsed / 1024 / 1024)}MB / ${Math.round(metrics.memory.heapTotal / 1024 / 1024)}MB`,
        editors: `${metrics.editors.active} active / ${metrics.editors.total} total`,
        fps: metrics.performance.fps,
        operations: metrics.operations,
      });
    }

    // Check for performance issues
    if (metrics.performance.fps < 30) {
      if (isDevelopment) {
        console.warn('Low FPS detected:', metrics.performance.fps);
      }
      void this.optimize();
    }

    if (metrics.memory.heapUsed > this.config.memoryWarningThreshold) {
      if (isDevelopment) {
        console.warn('High memory usage detected:', metrics.memory.heapUsed);
      }
    }
  }

  private cleanupDetachedNodes(): void {
    // Find and remove detached DOM nodes
    const allNodes = document.querySelectorAll('*');
    let cleaned = 0;

    allNodes.forEach((node) => {
      if (!document.body.contains(node) && node.parentNode) {
        node.parentNode.removeChild(node);
        cleaned++;
      }
    });

    if (cleaned > 0) {
      if (isDevelopment) {
        console.log(`Cleaned up ${cleaned} detached DOM nodes`);
      }
    }
  }

  private cleanupEventListeners(): void {
    // This is a placeholder - in practice, you'd track and clean specific listeners
    if (isDevelopment) {
      console.log('Event listener cleanup completed');
    }
  }

  // Cleanup
  destroy(): void {
    this.stopBackgroundTasks();

    // Cleanup subsystems
    this.memoryManager.destroy();
    this.performanceMonitor.destroy();
    this.editorStateManager.destroy();
    this.editorInstancePool.destroy();
    this.virtualDragSystem.destroy();
    this.commandManager.destroy();

    this.optimizationStrategies.clear();
    this.isInitialized.value = false;

    PerformanceOptimizationLayer.instance = null;
  }
}

// Export singleton instance getter
export function usePerformanceOptimization() {
  return PerformanceOptimizationLayer.getInstance();
}

// Types are already exported at declaration
