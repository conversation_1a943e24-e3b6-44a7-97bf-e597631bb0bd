import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { storeToRefs } from 'pinia';
import { useKnowledgeStore } from '../stores/knowledge';
import { useKnowledge } from '../composeables/useKnowledge';
import type { KnowledgeBase } from '../env';
import type { ChunkingMethod, ChunkingConfig } from '../types/qwen';
import type { KnowledgeBaseStats } from '../composeables/useKnowledge';
import { $t } from 'src/composables/useTrans';

/**
 * 知识库管理 Composable
 * 提供知识库和文档的统一管理功能
 */
export function useKnowledgeBaseManager() {
  // Composables
  const $q = useQuasar();
  const knowledgeStore = useKnowledgeStore();
  const knowledge = useKnowledge();

  // Store 响应式数据
  const {
    knowledgeBases,
    currentKnowledgeBase,
    knowledgeDocuments,
    isLoading: storeLoading,
  } = storeToRefs(knowledgeStore);

  // 本地响应式数据
  const isProcessing = ref(false);
  const selectedKnowledgeBase = ref<KnowledgeBase | null>(null);
  const detailLoading = ref(false);
  const stats = ref<KnowledgeBaseStats | null>(null);
  const processing = ref(false);

  // 处理状态跟踪
  const processingDocuments = ref(new Set<number>());
  const recentlyCompletedDocs = ref(new Set<number>());

  // 计算属性
  const hasKnowledgeBases = computed(() => knowledgeBases.value.length > 0);

  /**
   * 获取默认切割策略
   */
  const getDefaultChunkingStrategy = () => {
    return {
      method: 'markdown' as ChunkingMethod,
      config: {
        chunkSize: 800,
        chunkOverlap: 200,
      } as ChunkingConfig,
    };
  };

  /**
   * 加载知识库列表
   */
  const loadKnowledgeBases = async () => {
    try {
      await knowledgeStore.loadKnowledgeBases();
    } catch (error) {
      console.error('加载知识库失败:', error);
      $q.notify({
        type: 'negative',
        message: $t('src.composables.useKnowledgeBaseManager.loadKnowledgeBaseFailed'),
      });
      throw error;
    }
  };

  /**
   * 选择知识库
   */
  const selectKnowledgeBase = async (kb: KnowledgeBase) => {
    selectedKnowledgeBase.value = kb;
    await knowledgeStore.setCurrentKnowledgeBase(kb.id);
    await loadStats(kb.id);
  };

  /**
   * 返回知识库列表
   */
  const goBackToList = () => {
    selectedKnowledgeBase.value = null;
    void knowledgeStore.setCurrentKnowledgeBase(null);
    stats.value = null;
  };

  /**
   * 刷新详情数据
   */
  const refreshDetailData = async () => {
    if (selectedKnowledgeBase.value) {
      await Promise.all([
        knowledgeStore.loadKnowledgeDocuments(selectedKnowledgeBase.value.id),
        loadStats(selectedKnowledgeBase.value.id),
      ]);
    }
  };

  /**
   * 加载知识库统计信息
   */
  const loadStats = async (knowledgeBaseId: number) => {
    try {
      detailLoading.value = true;
      stats.value = await knowledge.getKnowledgeBaseStats(knowledgeBaseId);
    } catch (error) {
      console.error('加载知识库统计失败:', error);
      stats.value = null;
    } finally {
      detailLoading.value = false;
    }
  };

  /**
   * 创建知识库
   */
  const createKnowledgeBase = async (
    name: string,
    description: string,
    settings: Record<string, unknown> = {},
  ) => {
    try {
      isProcessing.value = true;

      const kbId = await knowledge.createKB(name, description, settings);

      // 刷新知识库列表
      await loadKnowledgeBases();

      $q.notify({
        type: 'positive',
        message: $t('src.composables.useKnowledgeBaseManager.knowledgeBaseCreated', { name }),
      });

      return kbId;
    } catch (error) {
      console.error('创建知识库失败:', error);
      $q.notify({
        type: 'negative',
        message: $t('src.composables.useKnowledgeBaseManager.knowledgeBaseCreationFailed'),
      });
      throw error;
    } finally {
      isProcessing.value = false;
    }
  };

  /**
   * 更新知识库
   */
  const updateKnowledgeBase = async (
    id: number,
    name: string,
    description: string,
    settings: Record<string, unknown> = {},
  ) => {
    try {
      isProcessing.value = true;

      await knowledge.updateKB(id, name, description, settings);

      // 刷新知识库列表
      await loadKnowledgeBases();

      // 如果当前正在查看这个知识库，更新选中的知识库信息
      if (selectedKnowledgeBase.value?.id === id) {
        const updatedKb = knowledgeBases.value.find((kb) => kb.id === id);
        if (updatedKb) {
          selectedKnowledgeBase.value = updatedKb;
        }
      }

      $q.notify({
        type: 'positive',
        message: $t('src.composables.useKnowledgeBaseManager.knowledgeBaseUpdated', { name }),
      });
    } catch (error) {
      console.error('更新知识库失败:', error);
      $q.notify({
        type: 'negative',
        message: $t('src.composables.useKnowledgeBaseManager.knowledgeBaseUpdateFailed'),
      });
      throw error;
    } finally {
      isProcessing.value = false;
    }
  };

  /**
   * 删除知识库
   */
  const deleteKnowledgeBase = async (id: number) => {
    try {
      isProcessing.value = true;

      await knowledge.deleteKB(id);

      // 如果删除的是当前选中的知识库，返回列表
      if (selectedKnowledgeBase.value?.id === id) {
        goBackToList();
      }

      // 刷新知识库列表
      await loadKnowledgeBases();

      $q.notify({
        type: 'positive',
        message: $t('src.composables.useKnowledgeBaseManager.knowledgeBaseDeleted'),
      });
    } catch (error) {
      console.error('删除知识库失败:', error);
      $q.notify({
        type: 'negative',
        message: $t('src.composables.useKnowledgeBaseManager.knowledgeBaseDeletionFailed'),
      });
      throw error;
    } finally {
      isProcessing.value = false;
    }
  };

  /**
   * 使用切割策略创建文档
   */
  const createDocumentWithChunking = async (
    knowledgeBaseId: number,
    title: string,
    content: string,
    sourceType: 'created' | 'upload' | 'inkcop_editor' | 'import' | 'template' | 'other',
    filePath: string,
    fileType: string,
    chunkingMethod: ChunkingMethod,
    chunkingConfig: ChunkingConfig,
  ) => {
    try {
      const result = await knowledge.createKnowledgeDocWithChunking(
        knowledgeBaseId,
        title,
        content,
        sourceType,
        filePath,
        fileType,
        chunkingMethod,
        chunkingConfig as Record<string, unknown>,
        true,
        'markdown',
      );

      // 检查返回的结果是否包含处理状态
      if (result && typeof result === 'object' && 'data' in result) {
        const docData = result.data as { id: string; processing?: boolean };
        if (docData.processing) {
          processingDocuments.value.add(parseInt(docData.id));
          console.log('📊 [useKnowledgeBaseManager] 文档进入处理状态:', docData.id);
        }
      }

      // 刷新数据
      await Promise.all([
        refreshDetailData(),
        loadKnowledgeBases(), // 更新文档计数
      ]);

      return result;
    } catch (error) {
      console.error('创建知识库文档失败:', error);
      throw error;
    }
  };

  /**
   * 删除文档
   */
  const deleteDocument = async (documentId: number) => {
    try {
      processing.value = true;

      console.log('🗑️ [useKnowledgeBaseManager] 开始删除知识库文档:', documentId);

      // 删除知识库文档（这个方法已经包含了清除关联关系的逻辑）
      await knowledge.deleteKnowledgeDoc(documentId);

      // 刷新数据
      await Promise.all([
        refreshDetailData(),
        loadKnowledgeBases(), // 更新文档计数
      ]);

      $q.notify({
        type: 'positive',
        message: $t('src.composables.useKnowledgeBaseManager.documentDeleted'),
      });

      console.log('✅ [useKnowledgeBaseManager] 知识库文档删除完成');
    } catch (error) {
      console.error('❌ [useKnowledgeBaseManager] 删除文档失败:', error);
      $q.notify({
        type: 'negative',
        message: $t('src.composables.useKnowledgeBaseManager.documentDeletionFailed'),
      });
      throw error;
    } finally {
      processing.value = false;
    }
  };

  /**
   * 设置文档向量化完成监听
   */
  const setupDocumentProcessingListener = () => {
    const unsubscribe = knowledge.onDocumentVectorized((docId: string, chunkCount: number) => {
      console.log('📡 [useKnowledgeBaseManager] 收到文档向量化完成:', docId, chunkCount);

      const docIdNum = parseInt(docId);
      processingDocuments.value.delete(docIdNum);

      if (chunkCount > 0) {
        recentlyCompletedDocs.value.add(docIdNum);

        $q.notify({
          type: 'positive',
          message: $t('src.composables.useKnowledgeBaseManager.documentVectorized'),
          caption: $t('src.composables.useKnowledgeBaseManager.documentVectorizedCaption', {
            docId,
            chunkCount,
          }),
          timeout: 3000,
        });

        // 3秒后清除完成状态
        setTimeout(() => {
          recentlyCompletedDocs.value.delete(docIdNum);
        }, 3000);

        // 延迟刷新统计数据
        setTimeout(() => {
          void knowledgeStore.refreshKnowledgeBases();
          if (selectedKnowledgeBase.value) {
            void loadStats(selectedKnowledgeBase.value.id);
          }
        }, 1000);
      } else {
        $q.notify({
          type: 'negative',
          message: $t('src.composables.useKnowledgeBaseManager.documentVectorizationFailed'),
          caption: $t(
            'src.composables.useKnowledgeBaseManager.documentVectorizationFailedCaption',
            {
              docId,
            },
          ),
          timeout: 5000,
        });
      }
    });

    return unsubscribe;
  };

  return {
    // 响应式数据
    knowledgeBases,
    currentKnowledgeBase,
    knowledgeDocuments,
    storeLoading,
    isProcessing,
    selectedKnowledgeBase,
    detailLoading,
    stats,
    processing,
    processingDocuments,
    recentlyCompletedDocs,

    // 计算属性
    hasKnowledgeBases,

    // 方法
    getDefaultChunkingStrategy,
    loadKnowledgeBases,
    selectKnowledgeBase,
    goBackToList,
    refreshDetailData,
    loadStats,
    createKnowledgeBase,
    updateKnowledgeBase,
    deleteKnowledgeBase,
    createDocumentWithChunking,
    deleteDocument,
    setupDocumentProcessingListener,
  };
}
