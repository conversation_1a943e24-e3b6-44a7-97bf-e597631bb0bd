<template>
  <div class="fit column no-wrap">
    <div class="gap-xs row q-py-xs q-pl-sm q-pr-xs items-center transparent border-bottom">
      <span>{{ $t('src.components.KnowledgeBaseList.rag_knowledge_base_management') }}</span>
      <q-space />
      <q-btn
        flat
        dense
        size="0.7rem"
        :icon="showSearch ? 'storage' : 'search'"
        @click="$emit('toggle-search')"
      />
      <q-btn flat dense size="0.7rem" icon="add" @click="$emit('create-knowledge-base')" />
    </div>

    <template v-if="!showCreateDialog">
      <template v-if="!showSearch">
        <!-- 加载状态 -->
        <div v-if="storeLoading" class="text-center q-pa-md q-space">
          <q-spinner size="40px" color="primary" />
          <div class="q-mt-md">{{ $t('src.components.KnowledgeBaseList.loading') }}</div>
        </div>
        <template v-else>
          <!-- 知识库列表 -->
          <div
            v-if="knowledgeBases.length === 0 && !storeLoading"
            class="flex-center column q-pa-xl q-space"
          >
            <div class="column flex-center q-space">
              <q-icon name="folder_open" size="60px" color="grey-5" />
              <div class="text-h6 text-grey-6 q-mt-md">
                {{ $t('src.components.KnowledgeBaseList.no_knowledge_base') }}
              </div>
              <div class="text-body2 text-grey-5">
                {{ $t('src.components.KnowledgeBaseList.click_create_knowledge_base') }}
              </div>
            </div>
            <q-space />
          </div>
          <q-list v-else class="q-space">
            <q-item
              v-for="kb in knowledgeBases"
              :key="kb.id"
              clickable
              @click="$emit('select-knowledge-base', kb)"
              class="q-py-md hover-item"
              :class="
                kb.id === currentKnowledgeBase?.id
                  ? $q.dark.isActive
                    ? 'bg-grey-9'
                    : 'bg-blue-1'
                  : ''
              "
            >
              <q-item-section side top>
                <q-icon name="storage" />
              </q-item-section>

              <q-item-section>
                <q-item-label class="text-h6">{{ kb.name }}</q-item-label>
                <q-item-label v-if="kb.description" caption>{{ kb.description }}</q-item-label>
                <q-item-label caption class="q-mt-xs">
                  {{ $t('src.components.KnowledgeBaseList.document_count') }}:
                  {{ kb.document_count }} |
                  {{ $t('src.components.KnowledgeBaseList.created_time') }}:
                  {{ formatDate(kb.created_at) }}
                </q-item-label>
              </q-item-section>

              <q-item-section side top class="hover-show-item">
                <q-btn flat dense round size="sm" icon="mdi-dots-vertical" @click.stop>
                  <q-menu class="shadow-24">
                    <q-list dense>
                      <q-item
                        clickable
                        v-close-popup
                        @click.stop="$emit('edit-knowledge-base', kb)"
                      >
                        <q-item-section side>
                          <q-icon name="edit" />
                        </q-item-section>
                        <q-item-section>{{
                          $t('src.components.KnowledgeBaseList.edit')
                        }}</q-item-section>
                      </q-item>
                      <q-item
                        clickable
                        v-close-popup
                        @click.stop="$emit('delete-knowledge-base', kb)"
                      >
                        <q-item-section side>
                          <q-icon name="delete" />
                        </q-item-section>
                        <q-item-section>{{
                          $t('src.components.KnowledgeBaseList.delete')
                        }}</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-btn>
              </q-item-section>
            </q-item>
          </q-list>
        </template>
      </template>
      <template v-else>
        <KnowledgeSearch>
          <template #top>
            <q-btn
              flat
              dense
              color="primary"
              icon="mdi-arrow-left"
              class="q-mb-md"
              :label="$t('src.components.KnowledgeBaseList.back')"
              @click="$emit('toggle-search')"
            />
          </template>
        </KnowledgeSearch>
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import type { KnowledgeBase } from '../env';
import KnowledgeSearch from './KnowledgeSearch.vue';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

// Composables
const $q = useQuasar();

// Props
defineProps<{
  knowledgeBases: KnowledgeBase[];
  currentKnowledgeBase: KnowledgeBase | null;
  storeLoading: boolean;
  showSearch: boolean;
  showCreateDialog: boolean;
}>();

// Emits
defineEmits<{
  'select-knowledge-base': [kb: KnowledgeBase];
  'create-knowledge-base': [];
  'edit-knowledge-base': [kb: KnowledgeBase];
  'delete-knowledge-base': [kb: KnowledgeBase];
  'toggle-search': [];
}>();

// 方法
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN');
};
</script>

<style scoped>
.hover-item:hover .hover-show-item {
  opacity: 1;
}

.hover-show-item {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.hover-item:hover {
  background-color: var(--q-primary-lighten-4);
}
</style>
