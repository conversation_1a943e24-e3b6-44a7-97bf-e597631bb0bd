# 增强版 MSIX 图标创建脚本
# 专门针对 Windows 11 24H2 的透明背景问题
param(
    [string]$SourceIcon = "public\icons\icon-512x512.png",
    [string]$OutputDir = "msix-icons-enhanced"
)

Write-Host "Creating Enhanced MSIX Icon Set for Windows 11 24H2" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# 检查源图标
if (-not (Test-Path $SourceIcon)) {
    Write-Host "❌ Source icon not found: $SourceIcon" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# 检查 System.Drawing
try {
    Add-Type -AssemblyName System.Drawing
    Write-Host "✅ System.Drawing available" -ForegroundColor Green
} catch {
    Write-Host "❌ System.Drawing not available" -ForegroundColor Red
    exit 1
}

# Windows 11 24H2 兼容的完整图标规格
$iconSpecs = @(
    # 标准图标（用于磁贴）
    @{ Size = 44; Name = "Square44x44Logo.png"; Type = "Standard" },
    @{ Size = 150; Name = "Square150x150Logo.png"; Type = "Standard" },
    @{ Size = 310; Width = 310; Height = 150; Name = "Wide310x150Logo.png"; Type = "Wide" },
    @{ Size = 50; Name = "StoreLogo.png"; Type = "Store" },
    
    # 关键：Windows 11 24H2 所需的 unplated 图标
    @{ Size = 16; Name = "Square44x44Logo.targetsize-16_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 20; Name = "Square44x44Logo.targetsize-20_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 24; Name = "Square44x44Logo.targetsize-24_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 30; Name = "Square44x44Logo.targetsize-30_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 32; Name = "Square44x44Logo.targetsize-32_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 36; Name = "Square44x44Logo.targetsize-36_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 40; Name = "Square44x44Logo.targetsize-40_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 44; Name = "Square44x44Logo.targetsize-44_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 48; Name = "Square44x44Logo.targetsize-48_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 60; Name = "Square44x44Logo.targetsize-60_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 64; Name = "Square44x44Logo.targetsize-64_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 72; Name = "Square44x44Logo.targetsize-72_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 80; Name = "Square44x44Logo.targetsize-80_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 96; Name = "Square44x44Logo.targetsize-96_altform-unplated.png"; Type = "Unplated" },
    @{ Size = 256; Name = "Square44x44Logo.targetsize-256_altform-unplated.png"; Type = "Unplated" },
    
    # 商店图标的 unplated 版本
    @{ Size = 50; Name = "StoreLogo.targetsize-50_altform-unplated.png"; Type = "StoreUnplated" },
    @{ Size = 70; Name = "StoreLogo.targetsize-70_altform-unplated.png"; Type = "StoreUnplated" },
    @{ Size = 150; Name = "StoreLogo.targetsize-150_altform-unplated.png"; Type = "StoreUnplated" },
    
    # 宽图标的 unplated 版本
    @{ Size = 310; Width = 310; Height = 150; Name = "Wide310x150Logo.targetsize-310_altform-unplated.png"; Type = "WideUnplated" }
)

function Create-OptimizedIcon {
    param(
        [string]$SourcePath,
        [string]$OutputPath,
        [int]$Width,
        [int]$Height = $Width
    )
    
    try {
        $sourceImage = [System.Drawing.Image]::FromFile((Resolve-Path $SourcePath).Path)
        
        # 确保图像是 PNG 格式以保持透明度
        $bitmap = New-Object System.Drawing.Bitmap($Width, $Height, [System.Drawing.Imaging.PixelFormat]::Format32bppArgb)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # 最高质量设置
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
        $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
        $graphics.CompositingMode = [System.Drawing.Drawing2D.CompositingMode]::SourceOver
        
        # 完全透明背景
        $graphics.Clear([System.Drawing.Color]::Transparent)
        
        # 计算缩放以保持宽高比
        $sourceAspect = $sourceImage.Width / $sourceImage.Height
        $targetAspect = $Width / $Height
        
        if ($sourceAspect > $targetAspect) {
            $newWidth = $Width
            $newHeight = [int]($Width / $sourceAspect)
            $x = 0
            $y = ($Height - $newHeight) / 2
        } else {
            $newHeight = $Height
            $newWidth = [int]($Height * $sourceAspect)
            $x = ($Width - $newWidth) / 2
            $y = 0
        }
        
        # 绘制图标
        $graphics.DrawImage($sourceImage, $x, $y, $newWidth, $newHeight)
        
        # 保存为 PNG 并确保透明度
        $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        $graphics.Dispose()
        $bitmap.Dispose()
        $sourceImage.Dispose()
        
        return $true
    } catch {
        Write-Host "❌ Error creating $OutputPath : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 创建所有图标
Write-Host ""
Write-Host "Creating enhanced icon set..." -ForegroundColor Yellow

$successCount = 0
$totalCount = $iconSpecs.Count

foreach ($spec in $iconSpecs) {
    $iconPath = "$OutputDir\$($spec.Name)"
    
    if ($spec.Type -eq "Wide") {
        $success = Create-OptimizedIcon -SourcePath $SourceIcon -OutputPath $iconPath -Width $spec.Width -Height $spec.Height
    } else {
        $success = Create-OptimizedIcon -SourcePath $SourceIcon -OutputPath $iconPath -Width $spec.Size
    }
    
    if ($success) {
        Write-Host "✅ Created $($spec.Name) - $($spec.Type)" -ForegroundColor Green
        $successCount++
    }
}

# 创建 PRI 配置文件模板
$priConfigContent = @"
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<resources targetOsVersion="10.0.0" majorVersion="1">
    <index root="\" startIndexAt="\">
        <default>
            <qualifier name="Language" value="en-US"/>
            <qualifier name="Contrast" value="standard"/>
            <qualifier name="Scale" value="100"/>
            <qualifier name="HomeRegion" value="001"/>
            <qualifier name="DXFeatureLevel" value="DX9"/>
            <qualifier name="Configuration" value=""/>
            <qualifier name="AlternateForm" value=""/>
            <qualifier name="Platform" value="UAP"/>
        </default>
        <indexer-config type="folder" foldernameAsQualifier="true" filenameAsQualifier="true" qualifierDelimiter="."/>
        <indexer-config type="resw" convertDotsToHyphens="true" initialPath=""/>
        <indexer-config type="resjson" initialPath=""/>
        <indexer-config type="PRI"/>
    </index>
</resources>
"@

$priConfigPath = "$OutputDir\priconfig.xml"
$priConfigContent | Out-File -FilePath $priConfigPath -Encoding UTF8

# 生成增强版 AppxManifest.xml
$enhancedManifestContent = @"
<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
         xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
         xmlns:uap3="http://schemas.microsoft.com/appx/manifest/uap/windows10/3"
         xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities">
  <Identity Name="InkCop"
            Version="*******"
            Publisher="CN=InkCop, O=InkCop, C=US"
            ProcessorArchitecture="x64" />

  <Properties>
    <DisplayName>InkCop</DisplayName>
    <PublisherDisplayName>InkCop</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
    <Description>InkCop - AI-powered writing assistant</Description>
    <uap:SupportedUsers>multiple</uap:SupportedUsers>
  </Properties>

  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.22621.0" />
  </Dependencies>

  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
    <Capability Name="internetClient" />
    <Capability Name="privateNetworkClientServer" />
  </Capabilities>

  <Applications>
    <Application Id="InkCop" Executable="InkCop.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements DisplayName="InkCop"
                          Description="AI-powered writing assistant"
                          BackgroundColor="transparent"
                          Square150x150Logo="Assets\Square150x150Logo.png"
                          Square44x44Logo="Assets\Square44x44Logo.png"
                          AppListEntry="none">
        <uap:DefaultTile Wide310x150Logo="Assets\Wide310x150Logo.png" 
                         Square310x310Logo="Assets\Square150x150Logo.png"
                         Square71x71Logo="Assets\Square44x44Logo.png"/>
        <uap:SplashScreen Image="Assets\Square150x150Logo.png" BackgroundColor="transparent"/>
      </uap:VisualElements>
      <Extensions>
        <uap3:Extension Category="windows.appExecutionAlias" 
                       Executable="InkCop.exe" 
                       EntryPoint="Windows.FullTrustApplication">
          <uap3:AppExecutionAlias>
            <desktop:ExecutionAlias Alias="InkCop.exe" xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10"/>
          </uap3:AppExecutionAlias>
        </uap3:Extension>
      </Extensions>
    </Application>
  </Applications>
</Package>
"@

$manifestPath = "$OutputDir\AppxManifest-enhanced.xml"
$enhancedManifestContent | Out-File -FilePath $manifestPath -Encoding UTF8

# 验证结果
Write-Host ""
Write-Host "Enhanced icon set creation complete!" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Cyan
Write-Host "Created: $successCount/$totalCount icons" -ForegroundColor Green
Write-Host "Location: $OutputDir" -ForegroundColor Blue
Write-Host ""
Write-Host "Files created:" -ForegroundColor Yellow
Write-Host "- All icon variants (standard + unplated)" -ForegroundColor White
Write-Host "- PRI configuration file" -ForegroundColor White
Write-Host "- Enhanced AppxManifest.xml template" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Run: .\create-installer.ps1 -Type MSIX" -ForegroundColor White
Write-Host "2. The build will use these enhanced icons" -ForegroundColor White
Write-Host "3. Install certificate before installing MSIX" -ForegroundColor White
Write-Host ""
Write-Host "Note: Orange background is a Windows 11 24H2 bug, not packaging issue" -ForegroundColor Yellow