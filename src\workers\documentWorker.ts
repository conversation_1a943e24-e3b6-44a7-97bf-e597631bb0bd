// 文档保存队列
interface SaveTask {
  docId: number;
  title: string;
  content: unknown; // 改为unknown类型以支持JSONContent对象
  folderId: number;
  timestamp: number;
}

let saveQueue: SaveTask[] = [];
let isProcessing = false;
let lastSaveTime = 0;
const SAVE_INTERVAL = 1000; // 最小保存间隔（毫秒）

// 处理保存队列
function processQueue() {
  if (isProcessing || saveQueue.length === 0) return;

  isProcessing = true;
  const task = saveQueue[saveQueue.length - 1]; // 只处理最新的任务

  try {
    // 发送消息给主线程执行实际的保存操作
    self.postMessage({
      type: 'SAVE_DOCUMENT',
      payload: {
        docId: task.docId,
        title: task.title,
        content: task.content,
        folderId: task.folderId,
      },
    });

    // 清空队列，只保留最新的任务
    saveQueue = [];
    lastSaveTime = Date.now();
  } catch (error) {
    console.error('保存文档失败:', error);
  } finally {
    isProcessing = false;
    // 如果队列中还有任务，继续处理
    if (saveQueue.length > 0) {
      void setTimeout(() => {
        processQueue();
      }, SAVE_INTERVAL);
    }
  }
}

// 监听来自主线程的消息
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;

  if (type === 'QUEUE_SAVE') {
    const { docId, title, content, folderId } = payload;

    // 添加到队列
    saveQueue.push({
      docId,
      title,
      content,
      folderId,
      timestamp: Date.now(),
    });

    // 如果距离上次保存超过最小间隔，立即处理
    if (Date.now() - lastSaveTime >= SAVE_INTERVAL) {
      processQueue();
    } else {
      // 否则等待最小间隔后处理
      void setTimeout(
        () => {
          processQueue();
        },
        SAVE_INTERVAL - (Date.now() - lastSaveTime),
      );
    }
  }
});
