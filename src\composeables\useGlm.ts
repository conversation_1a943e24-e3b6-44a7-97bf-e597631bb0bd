import { computed, type Ref } from 'vue';
import type {
  GlmMessage,
  GlmTool,
  GlmResponse,
  GlmStreamResponse,
  GlmToolCall,
} from 'src/types/glm';
import type {
  Message,
  MessageWithToolCall,
  Tool,
  ToolCall,
  AssistantMessage,
} from 'src/types/qwen';
import { useUiStore } from 'src/stores/ui';
import { useSqlite } from 'src/composeables/useSqlite';
import { DEFAULT_GLM_SETTINGS } from 'src/config/defaultSettings';
import { executeToolCall } from 'src/llm/tools/index';
import { PromptService } from 'src/services/promptService';
import type { ConversationRole } from 'src/services/promptService';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import { $t } from 'src/composables/useTrans';

/**
 * GLM (智谱清言) API Hook
 * 遵循统一的 LLM Hook 规范
 */
export const useGlm = () => {
  // 在函数内部初始化 composables 和 stores
  const uiStore = useUiStore();
  const { updateConversation } = useSqlite();

  // API 路径常量
  const CHAT_COMPLETIONS_PATH = '/chat/completions';

  // 获取当前设置
  const glmSettings = computed(() => {
    return uiStore.perferences?.llm?.glm || DEFAULT_GLM_SETTINGS;
  });

  // 辅助函数：转换消息格式到 GLM
  function convertMessageToGlm(message: Message): GlmMessage {
    const glmMsg: GlmMessage = {
      role: message.role,
      content:
        typeof message.content === 'string' ? message.content : JSON.stringify(message.content),
    };

    // 处理工具消息
    if (message.role === 'tool' && 'tool_call_id' in message) {
      glmMsg.tool_call_id = (message as { tool_call_id: string }).tool_call_id;
    }

    // 处理带工具调用的助手消息
    if (message.role === 'assistant' && 'tool_calls' in message) {
      const assistantMsg = message as MessageWithToolCall;
      if (assistantMsg.tool_calls && assistantMsg.tool_calls.length > 0) {
        glmMsg.tool_calls = assistantMsg.tool_calls.map((tc) => ({
          id: tc.id,
          type: tc.type as 'function' | 'web_search' | 'retrieval',
          function: tc.function
            ? {
                name: tc.function.name,
                arguments: tc.function.arguments,
              }
            : undefined,
        }));
      }
    }

    return glmMsg;
  }

  // 辅助函数：转换工具格式到 GLM
  function convertToolToGlm(tool: Tool): GlmTool {
    return {
      type: 'function',
      function: {
        name: tool.function.name,
        description: tool.function.description,
        parameters: tool.function.parameters,
      },
    };
  }

  // 统一的消息发送接口
  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const { messages, messagesRef, conversation, tools, enableTools, abortController } = params;
    const settings = glmSettings.value;

    console.log('[GLM] 发送消息，对话ID:', conversation.id);

    try {
      // 检查设置
      if (!settings || !settings.apiKey) {
        throw new Error('请先配置智谱清言 API Key');
      }

      // 设置加载状态
      loading.value = true;

      // 转换消息格式
      const glmMessages: GlmMessage[] = messages.map(convertMessageToGlm);

      // 转换工具格式（如果启用）
      let glmTools: GlmTool[] | undefined;
      if (enableTools && tools) {
        glmTools = (tools as Tool[]).map(convertToolToGlm);
      }

      // 构建请求体
      const requestBody: {
        model: string;
        messages: typeof glmMessages;
        max_tokens?: number;
        stream?: boolean;
        temperature?: number;
        top_p?: number;
        tools?: typeof glmTools;
        tool_choice?: 'auto' | 'none';
      } = {
        model: settings.model,
        messages: glmMessages,
        max_tokens: settings.maxTokens,
        stream: settings.stream,
        // tools: glmTools,
        // tool_choice: glmTools && glmTools.length > 0 ? 'auto' : undefined,
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (settings.temperature !== undefined && !isNaN(settings.temperature)) {
        requestBody.temperature = settings.temperature;
      }
      if (settings.topP !== undefined && !isNaN(settings.topP)) {
        requestBody.top_p = settings.topP;
      }
      requestBody.messages = requestBody.messages.filter(
        (i) => i.role !== 'tool' && i.role !== 'system',
      );

      console.log('[GLM] 请求URL:', `${settings.baseUrl}${CHAT_COMPLETIONS_PATH}`);
      console.log('[GLM] 请求体:', JSON.stringify(requestBody, null, 2));

      // 添加助手消息占位符
      const assistantMessage: AssistantMessage = {
        role: 'assistant',
        content: '',
        partial: true,
      };
      messagesRef.value.push(assistantMessage as Message);

      const response = await fetch(`${settings.baseUrl}${CHAT_COMPLETIONS_PATH}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${settings.apiKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      console.log('[GLM] 响应:', response);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      // 处理响应
      if (settings.stream) {
        await handleStreamResponse(
          response,
          assistantMessage as Message,
          glmTools,
          params,
          loading,
        );
      } else {
        await handleNonStreamResponse(
          response,
          assistantMessage as Message,
          glmTools,
          params,
          loading,
        );
      }
    } catch (error) {
      console.error('[GLM] 发送消息失败:', error);

      // 添加错误消息
      messagesRef.value.push({
        role: 'assistant',
        content: $t('src.composeables.useGlm.errorOccurred', {
          error:
            error instanceof Error ? error.message : $t('src.composeables.useGlm.unknownError'),
        }),
      } as Message);

      loading.value = false;
      throw error;
    } finally {
      // 无论成功还是失败，都更新对话
      if (conversation.id) {
        await updateConversation(
          conversation.id,
          conversation.title,
          JSON.stringify(messagesRef.value),
          conversation.prompt,
        );
      }
    }
  };

  // 处理流式响应
  async function handleStreamResponse(
    response: Response,
    assistantMessage: Message,
    tools: GlmTool[] | undefined,
    params: SimplifiedLlmParams,
    loading: Ref<boolean>,
  ) {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('Response body is not readable');

    const decoder = new TextDecoder();
    let buffer = '';
    const toolCalls: Record<string, ToolCall> = {};
    let currentToolCall: Partial<GlmToolCall> | null = null;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (!trimmedLine || trimmedLine === 'data: [DONE]') continue;

          if (trimmedLine.startsWith('data: ')) {
            const jsonStr = trimmedLine.slice(6);
            try {
              const data = JSON.parse(jsonStr) as GlmStreamResponse;
              const choice = data.choices?.[0];

              console.log('[GLM] Stream choice:', choice);

              if (choice?.delta?.content) {
                const content = choice.delta.content;
                if (typeof assistantMessage.content === 'string') {
                  assistantMessage.content += content;
                } else {
                  assistantMessage.content = content;
                }
              }

              // 处理工具调用
              if (choice?.delta?.tool_calls && tools) {
                for (const toolCall of choice.delta.tool_calls) {
                  if (toolCall.id) {
                    currentToolCall = {
                      id: toolCall.id,
                      type: toolCall.type,
                      function: { name: '', arguments: '' },
                    };
                  }

                  if (currentToolCall?.function && toolCall.function) {
                    if (toolCall.function.name) {
                      currentToolCall.function.name = toolCall.function.name;
                    }
                    if (toolCall.function.arguments) {
                      currentToolCall.function.arguments += toolCall.function.arguments;
                    }
                  }
                }
              }

              // 当流结束时，执行工具调用
              if (
                choice?.finish_reason === 'tool_calls' &&
                currentToolCall?.id &&
                currentToolCall?.function &&
                tools
              ) {
                const toolCallId = currentToolCall.id;
                if (currentToolCall.type === 'function') {
                  toolCalls[toolCallId] = {
                    id: toolCallId,
                    type: 'function' as const,
                    index: Object.keys(toolCalls).length,
                    function: {
                      name: currentToolCall.function.name,
                      arguments: currentToolCall.function.arguments,
                    },
                  };
                }
              }
            } catch (e) {
              console.error('[GLM] 解析流数据失败:', e);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // 标记消息为完整
    (assistantMessage as AssistantMessage).partial = false;

    // 如果有工具调用，处理它们
    if (Object.keys(toolCalls).length > 0 && tools) {
      await handleToolCalls(toolCalls, params, loading);
    } else {
      loading.value = false;
    }
  }

  // 处理非流式响应
  async function handleNonStreamResponse(
    response: Response,
    assistantMessage: Message,
    tools: GlmTool[] | undefined,
    params: SimplifiedLlmParams,
    loading: Ref<boolean>,
  ) {
    const data = (await response.json()) as GlmResponse;
    const choice = data.choices?.[0];

    if (choice?.message) {
      // 更新助手消息内容
      assistantMessage.content = choice.message.content || '';
      (assistantMessage as AssistantMessage).partial = false;

      // 处理工具调用
      if (choice.message.tool_calls && tools) {
        const toolCalls: Record<string, ToolCall> = {};

        for (const toolCall of choice.message.tool_calls) {
          if (toolCall.type === 'function' && toolCall.function) {
            toolCalls[toolCall.id] = {
              id: toolCall.id,
              type: 'function' as const,
              index: Object.keys(toolCalls).length,
              function: {
                name: toolCall.function.name || '',
                arguments: toolCall.function.arguments || '',
              },
            };
          }
        }

        await handleToolCalls(toolCalls, params, loading);
      } else {
        loading.value = false;
      }
    } else {
      loading.value = false;
      throw new Error('GLM API 返回了空响应');
    }
  }

  // 处理工具调用
  async function handleToolCalls(
    toolCalls: Record<string, ToolCall>,
    params: SimplifiedLlmParams,
    loading: Ref<boolean>,
  ) {
    const { messagesRef } = params;

    // 将工具调用添加到最后的助手消息
    const lastMessage = messagesRef.value[messagesRef.value.length - 1];
    (lastMessage as Message & { tool_calls?: ToolCall[] }).tool_calls = Object.values(toolCalls);

    // 执行工具调用
    console.log('[GLM] 执行工具调用:', toolCalls);

    for (const [toolCallId, toolCall] of Object.entries(toolCalls)) {
      if (toolCall.type === 'function') {
        try {
          const args = JSON.parse(toolCall.function.arguments);
          const result = await executeToolCall(toolCall.function.name, args);

          // 添加工具响应消息
          messagesRef.value.push({
            role: 'tool',
            content: JSON.stringify(result),
            tool_call_id: toolCallId,
            name: toolCall.function.name,
          } as Message);
        } catch (error) {
          console.error('[GLM] 工具调用失败:', error);
          messagesRef.value.push({
            role: 'tool',
            content: JSON.stringify({
              error:
                error instanceof Error ? error.message : $t('src.composeables.useGlm.unknownError'),
            }),
            tool_call_id: toolCallId,
            name: toolCall.function.name,
          } as Message);
        }
      }
    }

    // 递归调用以获取 AI 对工具结果的响应
    console.log('[GLM] 获取 AI 对工具结果的响应');
    await sendMessage(params, loading);
  }

  // 获取可用角色
  const getAvailableRoles = () => PromptService.getAvailableRoles();

  // 获取角色建议
  const getRoleSuggestions = (role: ConversationRole) => PromptService.getRoleSuggestions(role);

  return {
    glmSettings,
    sendMessage,
    getAvailableRoles,
    getRoleSuggestions,
  };
};
