# 文档复制粘贴和剪切功能实现文档

## 功能概述

实现了在DocumentItem上右键复制/剪切文档，在FolderItem上右键粘贴文档的功能。

- **复制模式**：当目标文件夹中存在同名文档时，会自动添加"副本"后缀
- **剪切模式**：移动文档到目标文件夹，如果父文件夹相同则粘贴功能禁用

## 实现细节

### 1. 全局状态管理 (src/stores/ui.ts)

在UI Store中添加了文档复制/剪切状态管理：

```typescript
// 文档复制状态
copiedDocument: null as {
  id: number;
  title: string;
  content: string;
  metadata: string;
  parentId: number;
  mode: 'copy' | 'cut';
} | null,

// 文档复制相关方法
copyDocument(document: { id: number; title: string; content: string; metadata: string; parentId: number }) {
  this.copiedDocument = { ...document, mode: 'copy' };
},
cutDocument(document: { id: number; title: string; content: string; metadata: string; parentId: number }) {
  this.copiedDocument = { ...document, mode: 'cut' };
},
clearCopiedDocument() {
  this.copiedDocument = null;
},
hasCopiedDocument(): boolean {
  return this.copiedDocument !== null;
},
isCutMode(): boolean {
  return this.copiedDocument?.mode === 'cut';
},
isCopyMode(): boolean {
  return this.copiedDocument?.mode === 'copy';
},
isDocumentCut(documentId: number): boolean {
  return this.copiedDocument?.mode === 'cut' && this.copiedDocument?.id === documentId;
},
```

### 2. DocumentItem组件修改

#### 右键菜单添加复制和剪切选项：

```vue
<q-item clickable v-close-popup @click="copyDocument">
  <q-item-section side>
    <q-icon name="mdi-content-copy" />
  </q-item-section>
  <q-item-section>复制</q-item-section>
</q-item>
<q-item clickable v-close-popup @click="cutDocument">
  <q-item-section side>
    <q-icon name="mdi-content-cut" />
  </q-item-section>
  <q-item-section>剪切</q-item-section>
</q-item>
```

#### 复制方法实现：

```typescript
const copyDocument = async () => {
  try {
    console.log('🔗 [文档] 准备复制文档:', props.doc.id);

    // 从后端获取完整的文档内容
    const fullDocument = await getDocument(props.doc.id);
    console.log('📄 [文档] 获取完整文档内容:', fullDocument.title);

    // 复制文档到剪贴板状态
    uiStore.copyDocument({
      id: fullDocument.id,
      title: fullDocument.title || '',
      content: fullDocument.content || '',
      metadata: fullDocument.metadata || '{}',
      parentId: props.parentId,
    });

    $q.notify({
      type: 'positive',
      message: `文档"${fullDocument.title}"已复制`,
      position: 'top',
    });
    console.log('✅ [文档] 文档已复制到剪贴板状态');
  } catch (error) {
    console.error('❌ [文档] 复制文档失败:', error);
    $q.notify({
      type: 'negative',
      message: '复制文档失败',
      position: 'top',
    });
  }
};
```

#### 剪切方法实现：

```typescript
const cutDocument = async () => {
  try {
    console.log('✂️ [文档] 准备剪切文档:', props.doc.id);

    // 从后端获取完整的文档内容
    const fullDocument = await getDocument(props.doc.id);
    console.log('📄 [文档] 获取完整文档内容:', fullDocument.title);

    // 剪切文档到剪贴板状态
    uiStore.cutDocument({
      id: fullDocument.id,
      title: fullDocument.title || '',
      content: fullDocument.content || '',
      metadata: fullDocument.metadata || '{}',
      parentId: props.parentId,
    });

    $q.notify({
      type: 'positive',
      message: `文档"${fullDocument.title}"已剪切`,
      position: 'top',
    });
    console.log('✅ [文档] 文档已剪切到剪贴板状态');
  } catch (error) {
    console.error('❌ [文档] 剪切文档失败:', error);
    $q.notify({
      type: 'negative',
      message: '剪切文档失败',
      position: 'top',
    });
  }
};
```

#### 视觉提示实现：

```vue
<div
  class="row no-wrap hover-highlight q-px-sm document-drag-container full-width"
  :class="[
    uiStore.highlightTreeItem === `document-${doc?.id}`
      ? 'highlight-tree-item'
      : keepHighlightDocumentId === doc.id
        ? $q.dark.mode
          ? 'bg-grey-9'
          : 'bg-grey-2'
        : 'border-placehoder',
    uiStore.isDocumentCut(doc.id) ? 'op-5' : '',
  ]"
></div>
```

被剪切的文档会自动添加 `op-5` class，使其显示为半透明状态，提供清晰的视觉反馈。

### 3. FolderItem组件修改

#### 智能展开逻辑实现：

```typescript
// 获取文件夹的所有父级路径
const getFolderParentPath = (folderId: number): number[] => {
  const path: number[] = [];
  const folder = docStore.folderMap.get(folderId);

  if (folder && folder.parent_id && folder.parent_id !== -1) {
    // 递归获取父级路径
    path.push(...getFolderParentPath(folder.parent_id));
    path.push(folder.parent_id);
  }

  return path;
};

// 确保文件夹及其所有父级都展开的函数
const ensureFolderExpanded = async (folderId: number) => {
  try {
    console.log('📂 [文档] 确保文件夹及其父级展开:', folderId);

    // 获取所有需要展开的父级文件夹路径
    const parentPath = getFolderParentPath(folderId);
    console.log('📂 [文档] 父级路径:', parentPath);

    // 从根级开始，逐级展开所有父级文件夹
    for (const parentId of parentPath) {
      console.log('📂 [文档] 展开父级文件夹:', parentId);
      emit('toggle', parentId);
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // 最后展开目标文件夹本身
    if (!props.expanded) {
      emit('toggle', folderId);
      await new Promise((resolve) => setTimeout(resolve, 150));
      console.log('✅ [文档] 文件夹已展开:', folderId);
    } else {
      console.log('📂 [文档] 文件夹已经是展开状态:', folderId);
    }
  } catch (error) {
    console.error('❌ [文档] 展开文件夹失败:', error);
  }
};
```

#### 右键菜单添加粘贴选项：

```vue
<q-item
  clickable
  v-close-popup
  @click="pasteDocument"
  :disable="
    !uiStore.hasCopiedDocument() ||
    (uiStore.isCutMode() && uiStore.copiedDocument?.parentId === folder.id)
  "
>
  <q-item-section side>
    <q-icon name="mdi-content-paste" />
  </q-item-section>
  <q-item-section class="text-no-wrap">粘贴</q-item-section>
</q-item>
```

#### 粘贴方法实现（支持复制和剪切模式）：

```typescript
const pasteDocument = async () => {
  const copiedDoc = uiStore.copiedDocument;
  if (!copiedDoc) {
    $q.notify({
      type: 'warning',
      message: '没有可粘贴的文档',
      position: 'top',
    });
    return;
  }

  try {
    if (copiedDoc.mode === 'cut') {
      // 剪切模式：移动文档
      console.log('✂️ [文档] 准备移动文档:', copiedDoc.title);

      // 使用docStore的moveDocumentToFolder方法移动文档
      const success = await docStore.moveDocumentToFolder(copiedDoc.id, props.folder.id);

      if (success) {
        // 展开文件夹以显示移动的文档
        emit('toggle', props.folder.id);

        $q.notify({
          type: 'positive',
          message: `文档"${copiedDoc.title}"已移动到文件夹`,
          position: 'top',
        });
        console.log('✅ [文档] 文档移动成功:', copiedDoc.title);

        // 清除剪切状态
        uiStore.clearCopiedDocument();
      } else {
        throw new Error('移动文档失败');
      }
    } else {
      // 复制模式：创建新文档
      console.log('📋 [文档] 准备复制文档:', copiedDoc.title);

      // 检查目标文件夹中是否存在同名文档
      const folder = docStore.folderMap.get(props.folder.id);
      let newTitle = copiedDoc.title;

      if (folder && folder.documents) {
        const existingDoc = folder.documents.find((doc) => doc.title === copiedDoc.title);
        if (existingDoc) {
          // 如果存在同名文档，添加副本后缀
          newTitle = `${copiedDoc.title}(${t('copiedItem')})`;
          console.log('📋 [文档] 检测到同名文档，重命名为:', newTitle);
        }
      }

      // 解析文档内容
      const content = JSON.parse(copiedDoc.content);
      const metadata = copiedDoc.metadata;

      // 创建新文档
      const docId = await useSqlite().createDocument(newTitle, content, props.folder.id, metadata);
      const newDoc = await useSqlite().getDocument(docId);

      // 更新store中的文件树
      docStore.addDocumentToTree(newDoc, props.folder.id);
      emit('document-created', newDoc, props.folder.id);

      // 展开文件夹以显示新创建的文档
      emit('toggle', props.folder.id);

      $q.notify({
        type: 'positive',
        message: `文档"${newTitle}"已复制到文件夹`,
        position: 'top',
      });
      console.log('✅ [文档] 文档复制成功:', newTitle);

      // 清除复制状态
      uiStore.clearCopiedDocument();
    }
  } catch (error) {
    console.error('❌ [文档] 粘贴文档失败:', error);
    $q.notify({
      type: 'negative',
      message: '粘贴文档失败',
      position: 'top',
    });
  }
};
```

### 4. 国际化支持

在国际化文件中已经存在`copiedItem`翻译：

- 中文：`copiedItem: '副本'`
- 英文：`copiedItem: 'Copy'`

## 功能特性

1. **右键复制**：在DocumentItem上右键，选择"复制"选项
2. **右键剪切**：在DocumentItem上右键，选择"剪切"选项
3. **右键粘贴**：在FolderItem上右键，选择"粘贴"选项（仅在有复制/剪切文档时可用）
4. **智能重命名**：复制模式下，当目标文件夹存在同名文档时，自动添加"(副本)"后缀
5. **剪切限制**：剪切模式下，如果父文件夹相同则粘贴功能禁用
6. **状态管理**：使用全局状态管理复制/剪切的文档信息
7. **视觉提示**：被剪切的文档显示为半透明状态（op-5 class）
8. **用户反馈**：提供成功/失败的通知消息
9. **智能展开**：粘贴后自动展开目标文件夹及其所有父级文件夹，确保新文档可见
10. **标签页导航**：切换编辑器标签页时自动展开文档所在文件夹路径，确保文档在文件树中可见
11. **状态清理**：粘贴成功后自动清除复制/剪切状态

### 功能对比

| 功能         | 复制模式                 | 剪切模式                 |
| ------------ | ------------------------ | ------------------------ |
| 操作结果     | 创建文档副本             | 移动原文档               |
| 同名处理     | 自动重命名添加"(副本)"   | 不需要处理               |
| 同文件夹限制 | 无限制                   | 粘贴被禁用               |
| 原文档状态   | 保持不变                 | 被移动到新位置           |
| 视觉提示     | 无特殊显示               | 半透明显示(op-5)         |
| 文件夹展开   | 智能展开目标文件夹及父级 | 智能展开目标文件夹及父级 |

## 使用方法

### 复制文档

1. 在文档树中找到要复制的文档
2. 右键点击文档，选择"复制"
3. 找到目标文件夹，右键点击文件夹
4. 选择"粘贴"选项
5. 文档将被复制到目标文件夹中（如有同名文档会自动重命名）

### 剪切文档

1. 在文档树中找到要移动的文档
2. 右键点击文档，选择"剪切"
3. 找到目标文件夹，右键点击文件夹
4. 选择"粘贴"选项（如果是同一文件夹则会被禁用）
5. 文档将被移动到目标文件夹中

## 技术实现要点

- 使用Vue 3 Composition API
- 集成Quasar UI组件
- 支持国际化
- 完整的错误处理
- 响应式状态管理
- 自动文件夹展开
- 标签页导航自动展开
- 智能命名冲突处理

## 测试建议

### 复制功能测试

1. 测试基本复制粘贴功能
2. 测试同名文档的重命名逻辑
3. 测试跨文件夹复制
4. 测试复制后原文档保持不变

### 剪切功能测试

1. 测试基本剪切粘贴功能
2. 测试同一文件夹内剪切粘贴禁用
3. 测试跨文件夹剪切移动
4. 测试剪切后原文档被移除
5. 测试剪切状态的视觉提示（op-5 class）

### 通用测试

1. 测试错误处理（如数据库错误）
2. 测试UI状态更新（菜单禁用/启用）
3. 测试状态清理（粘贴后清除复制/剪切状态）
4. 测试智能文件夹展开功能

### 智能展开测试

1. 测试深层嵌套文件夹的粘贴操作
2. 验证所有父级文件夹都被正确展开
3. 测试粘贴到已展开文件夹的行为
4. 测试粘贴到折叠文件夹的展开效果

### 标签页导航测试

1. 测试切换到深层文件夹中的文档
2. 验证文档路径自动展开功能
3. 测试快速连续切换标签页的响应
4. 测试不在文件夹中的文档切换行为
