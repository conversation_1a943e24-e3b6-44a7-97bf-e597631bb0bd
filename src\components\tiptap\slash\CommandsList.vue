<template>
  <q-card bordered class="gap-xs row q-pa-xs no-wrap shadow-24">
    <q-list>
      <template v-for="(item, index) in props.items" :key="item.group">
        <q-item
          clickable
          v-ripple
          class="radius-xs"
          :class="{
            'bg-primary text-white': isLeftSelected && selectedLeftIndex === index,
            [$q.dark.isActive ? 'bg-grey-9' : 'bg-grey-3']:
              isRightSelected && item.items.includes(selectedRightKey),
          }"
          @click="selectLeftItem(index)"
        >
          <q-item-section side>
            <q-icon :name="item.icon" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-no-wrap">{{ item.displayName }}</q-item-label>
            <q-item-label caption class="text-no-wrap">{{ item.description }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-list>
    <q-separator vertical inset spaced class="op-5" />
    <div class="scroll-y" style="width: 360px; height: 420px" ref="rightListRef">
      <q-list>
        <template v-for="(item, index) in getSlashMenuItems" :key="item.group">
          <q-item
            class="radius-xs"
            clickable
            v-ripple
            :class="{ 'bg-primary text-white': isRightSelected && selectedRightIndex === index }"
            @click="selectRightItem(index)"
            :ref="
              (el) => {
                if (el && '$el' in el) itemRefs[index] = (el as any).$el;
              }
            "
          >
            <q-item-section side>
              <q-icon :name="item.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label class="text-no-wrap">{{ item.label() }}</q-item-label>
              <q-item-label class="op-5 text-no-wrap">{{ item.description }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </q-list>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import type { Editor } from '@tiptap/vue-3';
import type { ToolbarItem } from '../useCommands';
import useCommands from '../useCommands';

interface SlashItem {
  group: string;
  displayName: string;
  icon?: string;
  description?: string;
  items: string[];
}

interface ToolbarItemWithKey extends ToolbarItem {
  key: string;
}

const props = defineProps<{
  items: SlashItem[];
  command: (item: ToolbarItemWithKey) => void;
  editor: Editor;
  docId: number;
}>();

const $q = useQuasar();

const { getSlashMenuItems } = useCommands(props.docId);

const isLeftSelected = ref(true);
const isRightSelected = ref(false);
const selectedLeftIndex = ref(0);
const selectedRightIndex = ref(0);
const selectedRightKey = ref('');
const rightListRef = ref<HTMLElement | null>(null);
const itemRefs = ref<HTMLElement[]>([]);

// 初始化时选中右侧第一项
onMounted(() => {
  if (getSlashMenuItems.value.length > 0) {
    selectedRightIndex.value = 0;
    selectedRightKey.value = getSlashMenuItems.value[0].key;
    isRightSelected.value = true;
    isLeftSelected.value = false;
  }
});

function selectLeftItem(index: number) {
  selectedLeftIndex.value = index;
  isLeftSelected.value = true;
  isRightSelected.value = false;

  const selectedItem = props.items[index];
  if (selectedItem.items.length > 0) {
    const targetKey = selectedItem.items[0];
    const rightIndex = getSlashMenuItems.value.findIndex((item) => item.key === targetKey);
    if (rightIndex !== -1) {
      selectedRightIndex.value = rightIndex;
      selectedRightKey.value = targetKey;
      scrollToRightItem(rightIndex);
    }
  }
}

function selectRightItem(index: number) {
  selectedRightIndex.value = index;
  selectedRightKey.value = getSlashMenuItems.value[index].key;
  isRightSelected.value = true;
  isLeftSelected.value = false;
  selectItem(index);
}

function scrollToRightItem(index: number) {
  if (itemRefs.value[index] && rightListRef.value) {
    const itemElement = itemRefs.value[index];
    const container = rightListRef.value;
    const itemTop = itemElement.offsetTop;
    const containerHeight = container.clientHeight;
    const scrollTop = itemTop - containerHeight / 2 + itemElement.clientHeight / 2;
    container.scrollTo({ top: scrollTop, behavior: 'smooth' });
  }
}

function selectItem(index: number) {
  const item = getSlashMenuItems.value[index];
  if (item) {
    props.command(item);
  }
}

function upHandler() {
  if (isLeftSelected.value) {
    if (props.items.length) {
      selectedLeftIndex.value =
        (selectedLeftIndex.value + props.items.length - 1) % props.items.length;
      // 触发左侧选中项对应的右侧滚动
      const selectedItem = props.items[selectedLeftIndex.value];
      if (selectedItem.items.length > 0) {
        const targetKey = selectedItem.items[0];
        const rightIndex = getSlashMenuItems.value.findIndex((item) => item.key === targetKey);
        if (rightIndex !== -1) {
          selectedRightIndex.value = rightIndex;
          selectedRightKey.value = targetKey;
          scrollToRightItem(rightIndex);
        }
      }
    }
  } else {
    if (getSlashMenuItems.value.length) {
      selectedRightIndex.value =
        (selectedRightIndex.value + getSlashMenuItems.value.length - 1) %
        getSlashMenuItems.value.length;
      selectedRightKey.value = getSlashMenuItems.value[selectedRightIndex.value].key;
      scrollToRightItem(selectedRightIndex.value);
    }
  }
  return true;
}

function downHandler() {
  if (isLeftSelected.value) {
    if (props.items.length) {
      selectedLeftIndex.value = (selectedLeftIndex.value + 1) % props.items.length;
      // 触发左侧选中项对应的右侧滚动
      const selectedItem = props.items[selectedLeftIndex.value];
      if (selectedItem.items.length > 0) {
        const targetKey = selectedItem.items[0];
        const rightIndex = getSlashMenuItems.value.findIndex((item) => item.key === targetKey);
        if (rightIndex !== -1) {
          selectedRightIndex.value = rightIndex;
          selectedRightKey.value = targetKey;
          scrollToRightItem(rightIndex);
        }
      }
    }
  } else {
    if (getSlashMenuItems.value.length) {
      selectedRightIndex.value = (selectedRightIndex.value + 1) % getSlashMenuItems.value.length;
      selectedRightKey.value = getSlashMenuItems.value[selectedRightIndex.value].key;
      scrollToRightItem(selectedRightIndex.value);
    }
  }
  return true;
}

function leftHandler() {
  if (!isLeftSelected.value) {
    isLeftSelected.value = true;
    isRightSelected.value = false;
  }
  return true;
}

function rightHandler() {
  if (isLeftSelected.value) {
    isLeftSelected.value = false;
    isRightSelected.value = true;
  }
  return true;
}

function enterHandler() {
  if (isRightSelected.value) {
    selectItem(selectedRightIndex.value);
  } else {
    selectLeftItem(selectedLeftIndex.value);
  }
  return true;
}

function onKeyDown({ event }: { event: KeyboardEvent }) {
  if (event.key === 'ArrowUp') {
    return upHandler();
  }
  if (event.key === 'ArrowDown') {
    return downHandler();
  }
  if (event.key === 'ArrowLeft') {
    return leftHandler();
  }
  if (event.key === 'ArrowRight') {
    return rightHandler();
  }
  if (event.key === 'Enter') {
    return enterHandler();
  }
  return false;
}

defineExpose({ onKeyDown });
</script>

<style lang="scss">
/* Dropdown menu */
.dropdown-menu {
  background: var(--white);
  border: 1px solid var(--gray-1);
  border-radius: 0.7rem;
  box-shadow: var(--shadow);
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  overflow: auto;
  padding: 0.4rem;
  position: relative;

  button {
    align-items: center;
    background-color: transparent;
    display: flex;
    gap: 0.25rem;
    text-align: left;
    width: 100%;

    &:hover,
    &:hover.is-selected {
      background-color: var(--gray-3);
    }

    &.is-selected {
      background-color: var(--gray-2);
    }
  }
}

.q-item {
  transition: all 0.2s ease;

  &.bg-primary {
    .q-item__label {
      color: white !important;
    }
  }

  &.bg-grey-2 {
    .q-item__label {
      color: var(--q-primary) !important;
    }
  }
}
</style>
