# Windows平台UI渲染问题修复指南

## 快速修复

如果您在Windows平台遇到UI渲染问题（如像素破碎、界面异常等），请按以下步骤操作：

### 1. 使用测试脚本（推荐）

```powershell
# 运行安全模式测试
.\test-rendering.ps1 -TestMode safe
```

如果问题解决，保留当前配置。如果问题仍存在，尝试：

```powershell
# 运行Windows GPU专用修复模式
.\test-rendering.ps1 -TestMode windows-gpu-fix
```

### 2. 手动配置

如果测试脚本不可用，手动编辑 `qt-rendering.conf` 文件：

```ini
[GPU_RENDERING]
enable_2d_canvas_acceleration=false
enable_webgl=false
enable_scroll_animation=false

[WINDOWS_NATIVE]
enable_window_shadow=false
enable_rounded_corners=false
enable_dwm_composition=false

[TROUBLESHOOTING]
force_software_rendering=true
disable_gpu_thread=true
disable_native_effects=true

[CHROMIUM_FLAGS]
custom_flags=--disable-gpu --disable-software-rasterizer --disable-d3d11 --disable-dxgi-zero-copy-video --disable-accelerated-2d-canvas --use-gl=swiftshader
```

## 常见问题类型

### Intel集成显卡用户
- **问题**: 最容易出现像素破碎
- **解决**: 使用 `windows-gpu-fix` 模式
- **原因**: Intel集成显卡驱动与WebEngine兼容性问题

### 高分辨率显示器用户
- **问题**: 界面元素错位、缩放异常
- **解决**: 在配置中设置 `enable_high_dpi=false`
- **原因**: DPI缩放与WebEngine渲染冲突

### Windows 11用户
- **问题**: 窗口边框异常
- **解决**: 禁用圆角窗口效果
- **原因**: Windows 11圆角效果与WebEngine冲突

## 测试模式说明

| 模式 | 描述 | 适用场景 |
|------|------|----------|
| `safe` | 禁用所有可能问题功能 | 严重渲染问题 |
| `windows-gpu-fix` | 专门解决Windows GPU问题 | Intel集成显卡问题 |
| `minimal` | 仅禁用主要问题功能 | 轻微渲染问题 |
| `debug` | 启用详细日志 | 问题诊断 |

## 验证修复

修复后，请验证以下功能：

1. ✅ 界面正常显示，无像素破碎
2. ✅ 滚动流畅，无撕裂现象
3. ✅ 窗口边框和阴影正常
4. ✅ 文本清晰，无模糊现象
5. ✅ 按钮和控件响应正常

## 性能影响

使用软件渲染可能会影响性能：

- **CPU使用率**: 可能略有增加
- **内存使用**: 基本无影响
- **响应速度**: 轻微降低，但通常不明显
- **电池续航**: 笔记本电脑可能略有影响

## 恢复默认设置

如果需要恢复默认设置：

```powershell
# 如果有备份文件
Copy-Item qt-rendering.conf.backup qt-rendering.conf -Force

# 或者删除配置文件让应用使用默认设置
Remove-Item qt-rendering.conf
```

## 获取帮助

如果问题仍未解决：

1. 运行调试模式收集日志：
   ```powershell
   .\test-rendering.ps1 -TestMode debug -Verbose
   ```

2. 查看详细故障排除指南：
   ```
   docs/UI_RENDERING_TROUBLESHOOTING.md
   ```

3. 收集系统信息：
   - Windows版本
   - 显卡型号和驱动版本
   - 显示器分辨率和DPI设置

## 注意事项

- 修改配置后需要重启应用程序
- 不同的GPU可能需要不同的配置
- 建议先尝试最小化的更改
- Linux平台通常不需要这些修复
