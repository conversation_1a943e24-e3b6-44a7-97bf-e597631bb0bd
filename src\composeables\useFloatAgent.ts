import { type SystemMessage, type UserMessage, type Message } from 'src/types/qwen';
import { ref } from 'vue';
import { processer } from 'src/utils/processer';
import { $t } from 'src/composables/useTrans';
import { useUiStore } from 'src/stores/ui';

export const useFloatAgent = () => {
  let abortController: AbortController | null = null;
  const isGenerating = ref(false);
  const uiStore = useUiStore();

  const floatAgentPrompt = uiStore.perferences?.prompt.floatAgent.list.find(
    (prompt) => prompt.name === (uiStore.perferences?.prompt.floatAgent.selected || 'default'),
  );

  const systemPrompt: SystemMessage = {
    role: 'system',
    content: floatAgentPrompt?.prompt || '',
  };

  const { thinkContent, isOllamaProvider } = processer();
  const floatConfig = uiStore.perferences?.floatAgent;

  const generate = async (
    message: string,
    prompt: string,
    context: string,
    llmResult: { content: string; reasoning_content: string },
  ) => {
    // 重置结果
    llmResult.content = '';
    llmResult.reasoning_content = '';

    isGenerating.value = true;
    abortController = new AbortController();
    const signal = abortController.signal;

    // 检查是否已经被中止
    if (signal.aborted) {
      isGenerating.value = false;
      return;
    }

    const systemMessage = systemPrompt;
    if (prompt) {
      systemMessage.content = systemMessage.content + '\n' + prompt;
    }
    const messages: Message[] = [systemMessage];
    if (context) {
      const contextMessage: SystemMessage = {
        role: 'system',
        content: context,
      };
      messages.push(contextMessage);
    }
    const userMessage: UserMessage = {
      role: 'user',
      content: message,
    };
    messages.push(userMessage);
    console.log(messages);

    try {
      const response = await fetch(`${floatConfig.base_url}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${floatConfig.api_key}`,
        },
        body: JSON.stringify({
          model: floatConfig.body.model,
          messages: [...messages],
          temperature: floatConfig.body.temperature,
          max_tokens: floatConfig.body.maxTokens,
          frequency_penalty: floatConfig.body.frequency_penalty,
          presence_penalty: floatConfig.body.presence_penalty,
          stream: true,
          enable_thinking: true,
          signal,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('ReadableStream not supported');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      // 检测是否使用Ollama
      const isUsingOllama = isOllamaProvider(floatConfig.base_url);
      let ollamaContentBuffer = ''; // 用于累积Ollama的完整内容

      while (true) {
        // 检查是否已经被中止
        if (signal.aborted) {
          await reader.cancel();
          break;
        }

        const { done, value } = await reader.read();
        if (done) break;

        // 检查是否已经被中止
        if (signal.aborted) {
          await reader.cancel();
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          // 检查是否已经被中止
          if (signal.aborted) {
            await reader.cancel();
            break;
          }

          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content || '';
              const reasoning_content = parsed.choices[0]?.delta?.reasoning_content || '';

              if (isUsingOllama) {
                // Ollama处理逻辑：累积所有内容，然后解析
                if (content) {
                  ollamaContentBuffer += content;

                  // 实时处理内容，分离thinking和实际回答
                  const processed = thinkContent(ollamaContentBuffer);

                  // 更新reasoning_content
                  if (processed.reasoning_content) {
                    llmResult.reasoning_content = processed.reasoning_content;
                  } else {
                    llmResult.reasoning_content = '';
                  }

                  // 更新实际内容
                  llmResult.content = processed.content;
                }
              } else {
                // 非Ollama处理逻辑（原有逻辑）
                if (parsed.choices[0]?.index === 0) {
                  llmResult.reasoning_content += '';
                }

                if (reasoning_content) {
                  llmResult.reasoning_content += reasoning_content;
                }
                if (content) {
                  llmResult.content += content;
                }
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      if (error.name === 'AbortError') {
        console.log('生成已中断');
        llmResult.content = '';
        llmResult.reasoning_content = '';
      } else {
        llmResult.content = $t('src.composeables.useFloatAgent.errorOccurred');
      }
    } finally {
      // 确保清理状态
      isGenerating.value = false;
      abortController = null;
    }
  };

  const abort = () => {
    if (!isGenerating.value) return;
    if (abortController) {
      console.log('正在中止生成...');
      abortController.abort();
      // 立即重置状态
      isGenerating.value = false;
      abortController = null;
    }
  };
  return {
    generate,
    abort,
    isGenerating,
  };
};
