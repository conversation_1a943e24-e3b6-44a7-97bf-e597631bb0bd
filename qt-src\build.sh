#!/bin/bash

# inkCop Qt应用构建脚本

set -e

echo "=== inkCop Qt应用构建脚本 ==="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 默认构建类型
BUILD_TYPE="Release"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        -r|--release)
            BUILD_TYPE="Release"
            shift
            ;;
        -c|--clean)
            echo "清理构建目录..."
            rm -rf build debug release
            echo "清理完成"
            exit 0
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  -d, --debug     调试构建"
            echo "  -r, --release   发布构建（默认）"
            echo "  -c, --clean     清理构建目录"
            echo "  -h, --help      显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                    # 进行Release构建"
            echo "  $0 --debug            # 进行Debug构建"
            echo "  $0 --clean            # 清理构建目录"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 $0 --help 查看帮助"
            exit 1
            ;;
    esac
done

echo "构建类型: $BUILD_TYPE"

# 检查CMake
if ! command -v cmake &> /dev/null; then
    echo "错误: 未找到cmake命令，请安装CMake"
    exit 1
fi

CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
echo "CMake版本: $CMAKE_VERSION"

# 使用CMake构建
echo ""
echo "=== 使用CMake构建 ==="

# 创建构建目录
BUILD_DIR="build"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 配置CMake
echo "配置CMake..."
cmake -DCMAKE_BUILD_TYPE="$BUILD_TYPE" ..

# 构建
echo "编译中..."
cmake --build . --config "$BUILD_TYPE" -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

echo ""
echo "=== 构建完成 ==="
echo "可执行文件位置: $(pwd)/bin/inkCop"

echo ""
echo "🎉 构建成功完成！"