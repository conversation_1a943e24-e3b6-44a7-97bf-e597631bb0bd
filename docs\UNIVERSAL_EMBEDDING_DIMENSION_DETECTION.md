# 通用Embedding维度检测系统

## 概述

本文档描述了InkCop知识库系统中实现的通用embedding维度检测功能。该系统支持**所有embedding模式**（云端、本地、自动），能够自动检测并保存向量维度，确保HNSW索引配置的准确性。

## 功能特性

### 🌐 全模式支持
- **云端模式**：自动检测API模型的向量维度
- **本地模式**：检测GGUF模型的向量维度
- **自动模式**：智能选择最佳可用配置并检测维度

### 🔄 智能触发机制
- **配置变化检测**：监听关键配置变化，自动触发维度检测
- **模式切换适配**：根据不同模式使用相应的检测方法
- **手动刷新支持**：提供手动触发维度检测的功能

### 💾 统一配置管理
- **自动保存**：检测到的维度自动保存到`embeddingDimension`字段
- **只读显示**：在界面中显示维度信息，防止用户误操作
- **配置持久化**：确保维度配置在应用重启后保持有效

## 实现架构

### 前端组件更新

#### 1. 通用维度显示
```vue
<!-- 向量维度显示 - 适用于所有模式 -->
<q-input
  v-model="embeddingDimensionDisplay"
  label="向量维度"
  outlined
  readonly
  hint="自动检测的embedding模型向量维度，用于HNSW索引配置"
  :loading="detectingDimension"
>
  <template v-slot:append>
    <q-btn
      v-if="!detectingDimension && canDetectDimension"
      flat
      round
      dense
      icon="refresh"
      @click="triggerDimensionDetection"
    />
  </template>
</q-input>
```

#### 2. 智能检测条件
```typescript
// 计算属性：是否可以检测维度
const canDetectDimension = computed(() => {
  if (!settings.value) return false;
  
  const mode = settings.value.embeddingMode;
  
  // 云端模式：需要有完整的API配置
  if (mode === 'cloud') {
    return !!(settings.value.baseUrl && settings.value.apiKey && settings.value.model);
  }
  
  // 本地模式：需要有模型路径
  if (mode === 'local') {
    return !!(settings.value.localModelPath && settings.value.localModelPath.trim() !== '');
  }
  
  // 自动模式：云端或本地任一可用即可
  if (mode === 'auto') {
    const hasCloudConfig = !!(settings.value.baseUrl && settings.value.apiKey && settings.value.model);
    const hasLocalConfig = !!(settings.value.localModelPath && settings.value.localModelPath.trim() !== '');
    return hasCloudConfig || hasLocalConfig;
  }
  
  return false;
});
```

#### 3. 多模式监听器
```typescript
// 监听关键配置变化，自动检测维度（支持所有模式）
watch(
  () => [
    settings.value?.baseUrl,
    settings.value?.apiKey,
    settings.value?.model,
    settings.value?.embeddingMode,
    settings.value?.localModelPath,
  ],
  ([newBaseUrl, newApiKey, newModel, newMode, newLocalPath], [oldBaseUrl, oldApiKey, oldModel, oldMode, oldLocalPath]) => {
    // 云端模式：检测API配置变化
    if (newMode === 'cloud' && hasCloudConfigChange) {
      if (newBaseUrl && newApiKey && newModel) {
        setTimeout(() => void detectEmbeddingDimension(), 1000);
      }
    }
    
    // 本地模式：检测本地模型路径变化
    else if (newMode === 'local' && hasLocalConfigChange) {
      if (newLocalPath && newLocalPath.trim() !== '') {
        setTimeout(() => void detectLocalModelDimension(), 1000);
      }
    }
    
    // 自动模式：智能选择检测方式
    else if (newMode === 'auto' && hasModeChange) {
      setTimeout(() => void detectBestEmbeddingDimension(), 1000);
    }
  },
  { deep: true }
);
```

### 检测方法实现

#### 1. 云端API维度检测
```typescript
const detectEmbeddingDimension = async () => {
  // 构造请求 URL
  const url = buildEmbeddingApiUrl(baseUrl);
  
  // 发送测试请求
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${apiKey}`,
    },
    body: JSON.stringify({
      model: model,
      input: '维度检测测试文本',
      parameters: { text_type: 'document' },
    }),
  });
  
  // 解析维度并保存
  const responseData = await response.json();
  const dimension = responseData.data[0].embedding.length;
  updateSettings({ embeddingDimension: dimension });
};
```

#### 2. 本地模型维度检测
```typescript
const detectLocalModelDimension = async () => {
  // 使用现有的本地GGUF测试方法
  const testText = '本地模型维度检测测试文本';
  const resultData = await knowledgeApi.testLocalGGUFEmbedding(testText);
  
  const result = JSON.parse(resultData);
  if (result.success && result.dimension > 0) {
    // 保存维度到设置中
    updateSettings({ embeddingDimension: result.dimension });
  }
};
```

#### 3. 智能自动检测
```typescript
const detectBestEmbeddingDimension = async () => {
  // 优先尝试云端API
  if (baseUrl && apiKey && model) {
    try {
      await detectEmbeddingDimension();
      return; // 云端检测成功
    } catch (cloudError) {
      console.log('云端API检测失败，尝试本地模型');
    }
  }
  
  // 云端失败，尝试本地模型
  if (localModelPath && localModelPath.trim() !== '') {
    try {
      await detectLocalModelDimension();
      return; // 本地检测成功
    } catch (localError) {
      console.log('本地模型检测失败');
    }
  }
  
  // 都失败了，使用默认TF-IDF配置
  updateSettings({ embeddingDimension: 100 });
};
```

#### 4. 智能触发器
```typescript
const triggerDimensionDetection = () => {
  const mode = settings.value.embeddingMode;
  
  if (mode === 'cloud') {
    void detectEmbeddingDimension();
  } else if (mode === 'local') {
    void detectLocalModelDimension();
  } else if (mode === 'auto') {
    void detectBestEmbeddingDimension();
  }
};
```

## 支持的检测场景

### 1. 云端API模式
- **OpenAI兼容模型**：text-embedding-ada-002, text-embedding-3-small/large
- **阿里云模型**：text-embedding-v1/v2/v3/v4
- **其他兼容API**：支持标准OpenAI embedding格式的所有API

### 2. 本地GGUF模式
- **任意维度模型**：自动从推理结果中获取维度
- **GPU加速支持**：支持GPU加速的本地模型
- **动态适配**：无需预先知道模型维度

### 3. 自动模式
- **智能降级**：云端→本地→TF-IDF的智能降级策略
- **最佳选择**：自动选择可用性最好的配置
- **容错处理**：确保在任何情况下都有可用配置

## 用户界面体验

### 维度显示状态
- **未检测**：显示"未检测"，提示用户配置embedding模型
- **检测中**：显示加载动画，表示正在进行维度检测
- **已检测**：显示具体维度值，如"1536维"
- **检测失败**：显示错误信息，提供重试选项

### 交互流程
1. **用户配置模型**：选择embedding模式和相关配置
2. **自动触发检测**：系统自动检测配置变化并触发维度检测
3. **实时反馈**：界面实时显示检测状态和结果
4. **配置保存**：检测成功后自动保存维度配置
5. **手动刷新**：用户可随时手动触发重新检测

## 配置验证增强

### 后端验证方法
```cpp
QString KnowledgeApi::validateHNSWConfiguration()
{
    // 检测当前embedding模型的实际维度
    int actualDimension = detectEmbeddingDimension();
    
    // 从知识库配置中获取保存的维度
    int configuredDimension = getEmbeddingDimensionFromKnowledgeBaseConfig();
    
    // HNSW索引配置的维度
    const int HNSW_CONFIGURED_DIMENSION = 1536;
    
    // 多维度比较和建议
    // 返回详细的验证报告
}
```

### 验证报告示例
```json
{
  "success": true,
  "actual_dimension": 1536,
  "hnsw_configured_dimension": 1536,
  "saved_dimension": 1536,
  "dimension_match": true,
  "config_hnsw_match": true,
  "message": "✅ 配置完美！实际维度(1536)与HNSW索引维度匹配",
  "recommendation": "配置已优化，无需调整",
  "embedding_mode": "cloud",
  "embedding_model": "text-embedding-v4"
}
```

## 技术优势

### 1. 全面覆盖
- 支持所有embedding模式，无遗漏
- 统一的检测和配置管理机制
- 完整的错误处理和降级策略

### 2. 智能化程度高
- 自动触发检测，减少用户操作
- 智能模式选择和降级
- 实时配置验证和建议

### 3. 用户体验优化
- 清晰的状态反馈
- 直观的维度显示
- 便捷的手动刷新功能

### 4. 系统可靠性
- 配置持久化保证数据安全
- 多重验证确保配置正确
- 完善的错误处理机制

## 总结

通用embedding维度检测系统成功解决了不同embedding模式下的维度配置问题，实现了：

1. **✅ 全模式支持**：云端、本地、自动模式全覆盖
2. **✅ 智能检测**：自动触发和智能降级机制
3. **✅ 统一管理**：一致的配置保存和显示方式
4. **✅ 用户友好**：直观的界面和便捷的操作
5. **✅ 系统可靠**：完善的验证和错误处理

这个实现确保了无论用户选择哪种embedding模式，都能获得准确的维度配置和最佳的向量搜索性能。
