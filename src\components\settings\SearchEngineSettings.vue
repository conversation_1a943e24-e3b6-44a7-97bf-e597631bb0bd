<template>
  <div class="search-engine-settings">
    <q-splitter
      v-model="splitterModel"
      unit="px"
      :limits="[200, 400]"
      class="full-height"
      separator-class="op-3"
    >
      <!-- 左侧供应商列表 -->
      <template v-slot:before>
        <div class="column full-height">
          <q-scroll-area class="q-space">
            <!-- 供应商列表 -->
            <q-list class="q-space q-pa-sm">
              <q-item
                v-for="provider in searchEngineSettings.providers"
                :key="provider.id"
                clickable
                v-ripple
                class="radius-xs q-mb-xs"
                :active="currentProvider?.id === provider.id"
                @click="selectProvider(provider)"
              >
                <q-item-section avatar>
                  <q-avatar size="48px">
                    <img :src="getProviderIcon(provider.key)" width="48" height="48" />
                  </q-avatar>
                </q-item-section>

                <q-item-section>
                  <q-item-label>{{ provider.name }}</q-item-label>
                  <q-item-label caption>{{ provider.description }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-scroll-area>
        </div>
      </template>

      <!-- 右侧设置详情 -->
      <template v-slot:after>
        <div class="column full-height q-pa-xl">
          <q-scroll-area class="q-space">
            <div v-if="currentProvider" class="column">
              <div class="text-h5">{{ currentProvider.name }} {{ $t('src.components.settings.common.settings') }}</div>

              <!-- 基本信息 -->
              <div class="text-subtitle1">{{ currentProvider.name }}</div>
              <div class="text-body2 q-mb-md">{{ currentProvider.description }}</div>

              <!-- Tavily 配置 -->
              <template v-if="currentProvider.key === 'tavily'">
                <TavilyProviderConfig
                  :config="currentProvider.config as TavilySettings"
                  @update="updateProviderConfig"
                />
              </template>
            </div>

            <div v-else class="flex flex-center q-pa-xl text-grey-6">
              <div class="text-center">
                <q-icon name="mdi-magnify" size="64px" class="q-mb-md" />
                <div class="text-h6">{{ $t('src.components.settings.SearchEngineSettings.selectProvider') }}</div>
                <div class="text-body2">{{ $t('src.components.settings.SearchEngineSettings.selectHint') }}</div>
              </div>
            </div>
          </q-scroll-area>
        </div>
      </template>
    </q-splitter>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useUiStore } from 'src/stores/ui';
import type { SearchEngineProvider, TavilySettings } from 'src/env.d';
import TavilyProviderConfig from './providers/TavilyProviderConfig.vue';

const { t: $t } = useI18n({ useScope: 'global' });
const uiStore = useUiStore();
const splitterModel = ref(300);

// 当前选中的供应商
const currentProvider = ref<SearchEngineProvider | null>(null);

// 搜索引擎设置
const searchEngineSettings = computed(() => uiStore.perferences.searchEngine);

// 获取供应商图标
const getProviderIcon = (key: string) => {
  switch (key) {
    case 'tavily':
      return './icons/llm/tavily-color.svg';
    default:
      return 'mdi-cog';
  }
};

// 选择供应商
const selectProvider = (provider: SearchEngineProvider) => {
  currentProvider.value = provider;
};

// 更新供应商
const updateProvider = (provider: SearchEngineProvider) => {
  uiStore.updateSearchEngineProvider(provider);
};

// 更新供应商配置
const updateProviderConfig = (config: TavilySettings | Record<string, unknown>) => {
  if (currentProvider.value) {
    currentProvider.value.config = config;
    updateProvider(currentProvider.value);
  }
};

onMounted(async () => {
  // 确保设置已加载
  await uiStore.loadSettings();

  // 默认选中第一个供应商
  if (searchEngineSettings.value.providers.length > 0) {
    currentProvider.value = searchEngineSettings.value.providers[0];
  }
});
</script>

<style scoped>
.search-engine-settings {
  height: 100%;
}
</style>
