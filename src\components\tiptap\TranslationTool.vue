<template>
  <div class="translation-tool column">
    <div class="row no-wrap">
      <div class="column no-wrap col-5 q-px-md">
        <!-- 原始内容输入框 -->
        <q-input
          v-model="sourceText"
          type="text"
          autogrow
          :label="$t('src.components.tiptap.translationTool.originalContent')"
          filled
          rows="4"
          class="q-mb-md radius-xs overflow-hidden"
          :placeholder="$t('src.components.tiptap.translationTool.enterContentToTranslate')"
          :loading="loading"
        />

        <!-- 领域提示输入框 -->
        <q-input
          v-model="domainHint"
          :label="$t('src.components.tiptap.translationTool.domainHint')"
          filled
          class="q-mb-md radius-xs overflow-hidden"
          :placeholder="$t('src.components.tiptap.translationTool.domainHintPlaceholder')"
          :hint="$t('src.components.tiptap.translationTool.domainHintTooltip')"
        />

        <!-- 目标语言选择器 -->
        <q-select
          v-model="targetLanguage"
          :options="languageOptions"
          :label="$t('src.components.tiptap.translationTool.targetLanguageLabel')"
          filled
          class="q-mb-md radius-xs overflow-hidden"
          emit-value
          map-options
        />

        <!-- 翻译按钮 -->
        <q-btn
          color="primary"
          :label="$t('src.components.tiptap.translationTool.startTranslation')"
          :loading="loading"
          :disable="!sourceText.trim() || !targetLanguage"
          @click="translate"
          class="q-mb-md full-width"
        />
      </div>

      <!-- 翻译结果 -->
      <div v-if="translationResult" class="q-space q-px-md">
        <div class="text-subtitle2 q-mb-sm">{{ $t('src.components.tiptap.translationTool.translationResults') }}</div>
        <div v-if="translationOptions.length > 1" class="q-mb-sm text-caption text-grey-6">
          {{ $t('src.components.tiptap.translationTool.clickOptionToCopy') }}
        </div>

        <!-- 多个翻译选项 -->
        <div v-if="translationOptions.length > 1" class="translation-options">
          <q-card
            v-for="(option, index) in translationOptions"
            :key="index"
            flat
            bordered
            class="q-mb-sm translation-option-card cursor-pointer"
            @click="copyOption(option)"
          >
            <q-card-section class="q-pa-md">
              <div class="translation-text">{{ option }}</div>
              <div class="text-right q-mt-sm">
                <q-icon name="content_copy" size="sm" class="text-grey-6" />
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- 单个翻译结果 -->
        <q-card v-else flat bordered class="q-pa-md">
          <div class="translation-text">{{ translationResult }}</div>
          <q-btn
            flat
            dense
            icon="content_copy"
:label="$t('src.components.tiptap.translationTool.copy')"
            size="sm"
            class="q-mt-sm"
            @click="copyResult"
          />
        </q-card>
      </div>
    </div>

    <!-- 错误信息 -->
    <q-banner v-if="error" class="bg-negative text-white q-mt-md" rounded>
      <template v-slot:avatar>
        <q-icon name="error" />
      </template>
      {{ error }}
    </q-banner>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { Notify } from 'quasar';
import { useI18n } from 'vue-i18n';
import { copyToClipboard } from 'src/utils/clipboard';

// 状态管理
const uiStore = useUiStore();
const { t: $t } = useI18n({ useScope: 'global' });
const loading = ref(false);
const sourceText = ref('');
const domainHint = ref('');
const targetLanguage = ref('English');
const translationResult = ref('');
const error = ref('');

// 支持的语言选项
const languageOptions = [
  { label: $t('src.components.tiptap.translationTool.languages.english'), value: 'English' },
  { label: $t('src.components.tiptap.translationTool.languages.chinese'), value: 'Chinese' },
  { label: $t('src.components.tiptap.translationTool.languages.japanese'), value: 'Japanese' },
  { label: $t('src.components.tiptap.translationTool.languages.korean'), value: 'Korean' },
  { label: $t('src.components.tiptap.translationTool.languages.french'), value: 'French' },
  { label: $t('src.components.tiptap.translationTool.languages.german'), value: 'German' },
  { label: $t('src.components.tiptap.translationTool.languages.spanish'), value: 'Spanish' },
  { label: $t('src.components.tiptap.translationTool.languages.arabic'), value: 'Arabic' },
  { label: $t('src.components.tiptap.translationTool.languages.russian'), value: 'Russian' },
  { label: $t('src.components.tiptap.translationTool.languages.italian'), value: 'Italian' },
  { label: $t('src.components.tiptap.translationTool.languages.portuguese'), value: 'Portuguese' },
  { label: $t('src.components.tiptap.translationTool.languages.dutch'), value: 'Dutch' },
  { label: $t('src.components.tiptap.translationTool.languages.thai'), value: 'Thai' },
  { label: $t('src.components.tiptap.translationTool.languages.vietnamese'), value: 'Vietnamese' },
  { label: $t('src.components.tiptap.translationTool.languages.indonesian'), value: 'Indonesian' },
  { label: $t('src.components.tiptap.translationTool.languages.turkish'), value: 'Turkish' },
  { label: $t('src.components.tiptap.translationTool.languages.polish'), value: 'Polish' },
  { label: $t('src.components.tiptap.translationTool.languages.czech'), value: 'Czech' },
  { label: $t('src.components.tiptap.translationTool.languages.hungarian'), value: 'Hungarian' },
  { label: $t('src.components.tiptap.translationTool.languages.swedish'), value: 'Swedish' },
  { label: $t('src.components.tiptap.translationTool.languages.finnish'), value: 'Finnish' },
  { label: $t('src.components.tiptap.translationTool.languages.hebrew'), value: 'Hebrew' },
  { label: $t('src.components.tiptap.translationTool.languages.hindi'), value: 'Hindi' },
  { label: $t('src.components.tiptap.translationTool.languages.bengali'), value: 'Bengali' },
  { label: $t('src.components.tiptap.translationTool.languages.malay'), value: 'Malay' },
  { label: $t('src.components.tiptap.translationTool.languages.cantonese'), value: 'Cantonese' },
];

// 计算属性：将翻译结果拆分为多个选项
const translationOptions = computed(() => {
  if (!translationResult.value) return [];

  // 按换行符和分号拆分结果
  const options = translationResult.value
    .split(/[\n;]/)
    .map((option) => option.trim())
    .filter((option) => option.length > 0);

  return options;
});

// 翻译函数
const translate = async () => {
  if (!sourceText.value.trim()) {
    error.value = $t('src.components.tiptap.translationTool.enterContent');
    return;
  }

  if (!targetLanguage.value) {
    error.value = $t('src.components.tiptap.translationTool.selectTargetLanguage');
    return;
  }

  const apiKey = uiStore.perferences?.llm?.qwen?.apiKey;
  if (!apiKey) {
    error.value = $t('src.components.tiptap.translationTool.configureApiKey');
    return;
  }

  loading.value = true;
  error.value = '';
  translationResult.value = '';

  try {
    const baseUrl =
      uiStore.perferences?.llm?.qwen?.baseUrl ||
      'https://dashscope.aliyuncs.com/compatible-mode/v1';

    // 构建翻译选项
    const translationOptions: Record<string, string> = {
      source_lang: 'auto', // 自动检测源语言
      target_lang: targetLanguage.value,
    };

    // 如果有领域提示，添加到翻译选项中
    if (domainHint.value.trim()) {
      translationOptions.domains = domainHint.value.trim();
    }

    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'qwen-mt-plus',
        messages: [
          {
            role: 'user',
            content: sourceText.value.trim(),
          },
        ],
        translation_options: translationOptions,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`,
      );
    }

    const data = await response.json();

    if (data.choices && data.choices[0] && data.choices[0].message) {
      translationResult.value = data.choices[0].message.content;
      Notify.create({
        type: 'positive',
        message: $t('src.components.tiptap.translationTool.translationSuccessful'),
        position: 'top',
      });
    } else {
      throw new Error($t('src.components.tiptap.translationTool.translationResponseInvalid'));

    }
  } catch (err: unknown) {
    console.error('翻译失败:', err);
    let errorMessage = $t('src.components.tiptap.translationTool.translationFailedGeneral');

    if (err instanceof Error) {
      errorMessage = err.message;
    } else if (typeof err === 'string') {
      errorMessage = err;
    } else if (err && typeof err === 'object' && 'message' in err) {
      const errObj = err as { message: unknown };
      if (typeof errObj.message === 'string') {
        errorMessage = errObj.message;
      } else if (errObj.message !== null && errObj.message !== undefined) {
        errorMessage = $t('src.components.tiptap.translationTool.translationRequestFailed');
      }
    }

    error.value = errorMessage;
    Notify.create({
      type: 'negative',
      message: $t('src.components.tiptap.translationTool.translationFailedGeneral'),
      caption: error.value,
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
};

// 复制结果到剪贴板
const copyResult = async () => {
  try {
    await copyToClipboard(translationResult.value, {
      showNotification: true,
      successMessage: $t('src.components.tiptap.translationTool.copySuccessful'),
      errorMessage: $t('src.components.tiptap.translationTool.copyFailed'),
    });
  } catch (err) {
    console.error('复制失败:', err);
    Notify.create({
      type: 'negative',
      message: $t('src.components.tiptap.translationTool.copyFailed'),
      position: 'top',
    });
  }
};

// 复制单个翻译选项到剪贴板
const copyOption = async (option: string) => {
  try {
    await copyToClipboard(option, {
      showNotification: true,
      successMessage: $t('src.components.tiptap.translationTool.copySuccessful'),
      errorMessage: $t('src.components.tiptap.translationTool.copyFailed'),
    });
  } catch (err) {
    console.error('复制失败:', err);
    Notify.create({
      type: 'negative',
      message: $t('src.components.tiptap.translationTool.copyFailed'),
      position: 'top',
    });
  }
};
</script>

<style scoped>
.translation-tool {
  padding: 16px;
  max-height: 100%;
  overflow-y: auto;
}

.translation-result {
  margin-top: 16px;
}

.translation-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  font-size: 14px;
}

.translation-option-card {
  transition: all 0.2s ease;
}

.translation-option-card:hover {
  background-color: rgba(25, 118, 210, 0.04);
  border-color: rgba(25, 118, 210, 0.2);
}

.translation-options {
  max-height: 300px;
  overflow-y: auto;
}
</style>
