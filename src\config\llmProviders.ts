import { $t } from 'src/composables/useTrans';

// 过滤掉隐藏的供应商
const visibleLlmProviders = () => {
  // LLM 供应商列表配置
  const llmProviders = [
    {
      key: 'anthropic',
      name: $t('src.config.llmProviders.name.anthropic'),
      description: $t('src.config.llmProviders.description.anthropic'),
      alt: 'Anthropic',
    },
    {
      key: 'azureOpenai',
      name: $t('src.config.llmProviders.name.azureOpenai'),
      description: $t('src.config.llmProviders.description.azureOpenai'),
      alt: 'Azure OpenAI',
    },
    {
      key: 'deepseek',
      name: $t('src.config.llmProviders.name.deepseek'),
      description: $t('src.config.llmProviders.description.deepseek'),
      alt: 'DeepSeek',
    },
    {
      key: 'gemini',
      name: $t('src.config.llmProviders.name.gemini'),
      description: $t('src.config.llmProviders.description.gemini'),
      alt: 'Google Gemini',
    },
    {
      key: 'glm',
      name: $t('src.config.llmProviders.name.glm'),
      description: $t('src.config.llmProviders.description.glm'),
      alt: 'GLM',
      hidden: true,
    },
    {
      key: 'grok',
      name: $t('src.config.llmProviders.name.grok'),
      description: $t('src.config.llmProviders.description.grok'),
      alt: 'Grok AI',
    },
    {
      key: 'minimax',
      name: $t('src.config.llmProviders.name.minimax'),
      description: $t('src.config.llmProviders.description.minimax'),
      alt: 'MiniMax',
    },
    {
      key: 'moonshot',
      name: $t('src.config.llmProviders.name.moonshot'),
      description: $t('src.config.llmProviders.description.moonshot'),
      alt: 'Moonshot',
    },
    {
      key: 'ollama',
      name: $t('src.config.llmProviders.name.ollama'),
      description: $t('src.config.llmProviders.description.ollama'),
      alt: 'Ollama',
    },
    {
      key: 'openai',
      name: $t('src.config.llmProviders.name.openai'),
      description: $t('src.config.llmProviders.description.openai'),
      alt: 'OpenAI',
    },
    {
      key: 'qwen',
      name: $t('src.config.llmProviders.name.qwen'),
      description: $t('src.config.llmProviders.description.qwen'),
      alt: 'Qwen',
    },
    {
      key: 'volces',
      name: $t('src.config.llmProviders.name.volces'),
      description: $t('src.config.llmProviders.description.volces'),
      alt: 'Volces',
    },
  ];
  return llmProviders.filter((provider) => !provider.hidden);
};

export { visibleLlmProviders as llmProviders };
