<template>
  <div
    :class="`${toolbarClass} ${$q.dark.isActive ? 'bg-dark' : 'bg-white'}`"
    class="gap-xxs row q-pa-xs no-wrap scroll-x"
    style="overflow-y: hidden"
  >
    <template v-for="item in toolbarButtons" :key="item.key">
      <!-- 分割线 -->
      <q-separator v-if="item.type === 'separator::vertical'" vertical inset spaced />
      <q-separator v-else-if="item.type === 'separator::horizontal'" horizontal inset spaced />
      <q-space v-else-if="item.type === 'space'" />

      <!-- 普通按钮 -->
      <q-btn
        v-else-if="item.type === 'button'"
        flat
        dense
        size="0.7rem"
        :icon="item.icon"
        :disable="item.disabled?.()"
        :class="item.class?.()"
        @click="item.handler"
      >
        <q-tooltip
          class="border q-px-sm text-body2 shadow-24"
          :class="$q.dark.isActive ? 'text-white bg-black' : 'text-black bg-white'"
          >{{ item.tooltip }}</q-tooltip
        >
      </q-btn>

      <!-- 菜单按钮 -->
      <q-btn v-else-if="item.type === 'menu'" flat dense :icon="item.icon" size="0.7rem">
        <q-tooltip
          v-if="item.tooltip"
          class="border q-px-sm text-body2 shadow-24"
          :class="$q.dark.isActive ? 'text-white bg-black' : 'text-black bg-white'"
          >{{ item.tooltip }}</q-tooltip
        >
        <q-menu>
          <q-list dense class="tiptap">
            <template v-for="childKey in getMenuItems(item.children)" :key="childKey">
              <q-item
                clickable
                v-close-popup
                :class="toolbarItems[childKey].class?.()"
                class="no-margin"
                @click="toolbarItems[childKey].handler"
              >
                <q-item-section v-if="toolbarItems[childKey].icon" side>
                  <q-icon :name="toolbarItems[childKey].icon" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>
                    {{ toolbarItems[childKey].label() }}
                  </q-item-label>
                </q-item-section>
                <q-tooltip
                  v-if="toolbarItems[childKey].tooltip"
                  class="border q-px-sm text-body2 shadow-24"
                  :class="$q.dark.isActive ? 'text-white bg-black' : 'text-black bg-white'"
                  >{{ toolbarItems[childKey].tooltip }}</q-tooltip
                >
              </q-item>
            </template>
          </q-list>
        </q-menu>
      </q-btn>
    </template>
    <slot name="attach" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
// import type { Editor } from '@tiptap/vue-3';
import { useQuasar } from 'quasar';
import useCommands, { type ToolbarItem } from './useCommands';
// import { useI18n } from 'vue-i18n';

// const { t: $t } = useI18n({ useScope: 'global' });
const $q = useQuasar();

const props = defineProps<{
  docId: number | null;
  showLabel?: boolean;
  catalogVisible?: boolean;
  toolbarType?: 'classic' | 'slash' | 'bubble';
  command?: (item: ToolbarItem) => void;
}>();

// 使用 computed 动态计算 toolbarItems 和 getToolbarItemsByType
const { toolbarItems, getToolbarItemsByType } = computed(() => useCommands(props.docId)).value;

// 计算工具栏背景
const toolbarClass = computed(() => {
  const align = props.toolbarType === 'slash' ? 'no-wrap border radius-sm' : 'items-center';
  if (props.toolbarType === 'bubble') {
    return align + ' ' + ($q.dark.isActive ? 'bg-black' : 'bg-light');
  }
  return align + ' ' + ($q.dark.isActive ? 'bg-grey-10' : 'bg-white');
});

// 工具栏按钮顺序数组
const toolbarOrder = computed(() => {
  return getToolbarItemsByType(props.toolbarType || 'classic');
});

// 根据顺序数组获取工具栏按钮配置
const toolbarButtons = computed(() => {
  return toolbarOrder.value.map((key) => toolbarItems[key]);
});

// 获取菜单子项的计算属性
const getMenuItems = (children: string[] | undefined) => {
  if (!children) return [];
  return children.filter((key) => toolbarItems[key]?.visible?.());
};
</script>

<style lang="scss">
.q-toolbar .q-separator--vertical-inset {
  margin-top: 12px;
  margin-bottom: 12px;
}

.color-block {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
