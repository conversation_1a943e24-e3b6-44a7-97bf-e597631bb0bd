# Fluent System Icons 集成指南

本文档介绍如何在 Quasar 项目中使用 Microsoft Fluent System Icons 字体图标。

## 概述

Fluent System Icons 是微软设计的现代化图标字体，提供了丰富的图标资源。本项目已将其完全集成到 Quasar 框架中，可以与 QIcon 组件无缝配合使用。

## 功能特性

- ✅ 完全集成到 Quasar QIcon 组件
- ✅ 支持 4 种图标变体（Regular, Light, Filled, Resizable）
- ✅ 提供 8000+ 图标
- ✅ 支持图标搜索功能
- ✅ TypeScript 类型支持
- ✅ 响应式设计适配
- ✅ 主题自动适配

## 基础使用

### 在模板中使用

```vue
<template>
  <!-- 基础用法 -->
  <q-icon name="fluent-add" size="24px" />
  <q-icon name="fluent-delete" size="24px" />
  <q-icon name="fluent-edit" size="24px" />
  
  <!-- 指定变体 -->
  <q-icon name="fluent-star" fluent-variant="filled" size="24px" />
  <q-icon name="fluent-heart" fluent-variant="light" size="24px" />
  
  <!-- 在按钮中使用 -->
  <q-btn color="primary" icon="fluent-add" label="添加" />
  <q-btn color="negative" icon="fluent-delete" label="删除" />
</template>
```

### 使用组合式函数

```vue
<script setup lang="ts">
import { useFluentIcons } from '@/composables/useFluentIcons'

const { 
  getIcon, 
  getIconClass, 
  searchIcons, 
  isIconAvailable 
} = useFluentIcons()

// 获取图标 Unicode 字符
const addIcon = getIcon('add', 'regular')

// 获取图标 CSS 类名
const deleteIconClass = getIconClass('delete', 'filled')

// 搜索图标
const searchResults = searchIcons('arrow')

// 检查图标是否可用
const hasIcon = isIconAvailable('custom_icon')
</script>
```

## 图标变体

Fluent System Icons 提供 4 种变体：

### Regular (默认)
```vue
<q-icon name="fluent-add" />
<!-- 或明确指定 -->
<q-icon name="fluent-add" fluent-variant="regular" />
```

### Light
```vue
<q-icon name="fluent-add" fluent-variant="light" />
```

### Filled
```vue
<q-icon name="fluent-add" fluent-variant="filled" />
```

### Resizable
```vue
<q-icon name="fluent-add" fluent-variant="resizable" />
```

## 常用图标

项目预定义了常用图标的映射：

```typescript
// 基础操作
'fluent-add'        // 添加
'fluent-delete'     // 删除
'fluent-edit'       // 编辑
'fluent-save'       // 保存
'fluent-copy'       // 复制
'fluent-cut'        // 剪切

// 导航
'fluent-arrow_left'    // 左箭头
'fluent-arrow_right'   // 右箭头
'fluent-chevron_down'  // 下拉箭头

// 界面
'fluent-home'       // 首页
'fluent-settings'   // 设置
'fluent-search'     // 搜索
'fluent-close'      // 关闭

// 文件操作
'fluent-folder'     // 文件夹
'fluent-document'   // 文档

// 状态
'fluent-checkmark'  // 成功
'fluent-error'      // 错误
'fluent-warning'    // 警告
'fluent-info'       // 信息
```

## 高级用法

### 图标搜索

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { useFluentIcons } from '@/composables/useFluentIcons'

const { searchIcons, clearSearch } = useFluentIcons()
const searchQuery = ref('')
const searchResults = ref<string[]>([])

const handleSearch = () => {
  searchResults.value = searchIcons(searchQuery.value)
}
</script>

<template>
  <q-input 
    v-model="searchQuery" 
    @input="handleSearch"
    placeholder="搜索图标..."
  />
  
  <div v-for="iconName in searchResults" :key="iconName">
    <q-icon :name="`fluent-${iconName}`" />
    {{ iconName }}
  </div>
</template>
```

### 批量获取图标

```typescript
const { getIcons } = useFluentIcons()

const iconSet = getIcons(['add', 'delete', 'edit'], 'filled')
// 返回: { add: '...', delete: '...', edit: '...' }
```

### 动态样式

```typescript
const { createIconStyle } = useFluentIcons()

const iconStyle = createIconStyle('star', 'filled', '32px')
// 返回完整的样式对象
```

## 在组件中使用

### 创建图标组件

```vue
<!-- FluentIcon.vue -->
<template>
  <q-icon 
    :name="`fluent-${name}`" 
    :fluent-variant="variant"
    :size="size"
    :color="color"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import type { FluentIconVariant } from '@/types/fluent-icons'

interface Props {
  name: string
  variant?: FluentIconVariant
  size?: string
  color?: string
}

withDefaults(defineProps<Props>(), {
  variant: 'regular',
  size: '24px'
})
</script>
```

### 使用自定义组件

```vue
<template>
  <FluentIcon name="add" variant="filled" size="32px" color="primary" />
  <FluentIcon name="delete" variant="light" />
</template>
```

## 性能优化

### 按需加载

```typescript
// 只加载需要的变体
const { getIcon } = useFluentIcons({
  defaultVariant: 'regular',
  enableSearch: false // 禁用搜索以减少内存使用
})
```

### 预加载常用图标

```typescript
// 在应用启动时预加载常用图标
import { commonFluentIcons } from '@/icons/fluent-icons'

// 预加载所有常用图标
Object.values(commonFluentIcons).forEach(iconName => {
  getIcon(iconName, 'regular')
  getIcon(iconName, 'filled')
})
```

## 主题适配

图标会自动适配应用主题：

```scss
// 暗色主题
.body--dark .q-icon.fluent-icon {
  opacity: 0.87;
}

// 亮色主题  
.body--light .q-icon.fluent-icon {
  opacity: 0.87;
}
```

## 故障排除

### 图标不显示

1. 确认图标名称正确：
```typescript
import { isIconAvailable } from '@/composables/useFluentIcons'
console.log(isIconAvailable('your_icon_name'))
```

2. 检查变体是否存在：
```typescript
import { getIconVariants } from '@/composables/useFluentIcons'
console.log(getIconVariants('your_icon_name'))
```

3. 确认 CSS 文件已加载：
检查 `quasar.config.ts` 中是否包含 `'fluent-icons.scss'`

### 性能问题

1. 限制搜索结果数量
2. 使用 `v-show` 而不是 `v-if` 来切换图标显示
3. 避免在循环中频繁调用图标函数

## 可用图标数量

- Regular: 8000+ 图标
- Light: 6000+ 图标  
- Filled: 8000+ 图标
- Resizable: 部分图标

## 更新日志

- 2025-07-27: 初始版本，完整集成 Fluent System Icons
- 支持所有 4 种变体
- 提供完整的 TypeScript 类型支持
- 集成搜索功能
