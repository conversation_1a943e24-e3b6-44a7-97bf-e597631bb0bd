<template>
  <q-splitter
    v-model="splitterModel"
    unit="px"
    :limits="[200, 400]"
    class="full-height"
    separator-class="op-3"
  >
    <template v-slot:before>
      <q-scroll-area class="fit">
        <q-list class="q-pa-sm column gap-xs">
          <q-item
            v-for="sense in sences"
            :key="sense.value"
            clickable
            v-ripple
            class="radius-xs"
            @click="selectSense(sense.value)"
            :class="{ 'bg-primary text-white': selectedSense === sense.value }"
          >
            <q-item-section>
              <div class="text-weight-bold">{{ sense.label }}</div>
              <div class="text-caption text-grey">{{ sense.description }}</div>
            </q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </template>
    <template v-slot:after>
      <div class="fit column gap-sm">
        <div class="row justify-between items-center q-px-md q-pt-md">
          <q-select
            v-model="selectedPrompt"
            :options="promptOptions"
            :label="$t('src.components.settings.PromptSettings.selectPrompt')"
            class="q-mr-md"
            style="min-width: 200px"
            dense
            filled
            emit-value
            map-options
          />
          <div class="row gap-sm">
            <q-input
              v-if="toggleAdd"
              v-model="newPromptName"
              autofocus
              :placeholder="$t('src.components.settings.PromptSettings.promptName')"
              class="full-width"
              @keyup.enter="addPrompt"
              @keyup.esc="toggleAdd = false"
            />
            <q-btn
              v-else
              color="primary"
              icon="add"
              :label="$t('src.components.settings.PromptSettings.add')"
              @click="toggleAdd = true"
            />
          </div>
        </div>
        <q-scroll-area class="q-space q-px-md q-pb-md">
          <template v-for="item in motifyTarget?.list" :key="item.name">
            <q-expansion-item
              icon="perm_identity"
              class="radius-xs border q-mb-sm overflow-hidden"
              :label="item.name"
              :default-opened="item.name === selectedPrompt"
            >
              <div v-if="selectedSense !== 'chat'" class="q-pa-xs">
                <q-input v-model="item.prompt" filled square type="text" autogrow />
              </div>
              <template v-else>
                <q-list class="q-pa-sm">
                  <q-item-label header>{{
                    $t('src.components.settings.PromptSettings.responsibilities')
                  }}</q-item-label>
                  <q-item
                    v-for="(resp, idx) in item.prompt.responsibilities"
                    :key="idx"
                    class="q-py-none"
                  >
                    <q-item-section>
                      <q-input
                        v-model="item.prompt.responsibilities[idx]"
                        type="text"
                        filled
                        square
                        :label="$t('src.components.settings.PromptSettings.responsibilities')"
                        class="q-mb-xs hover-item"
                      >
                        <template v-slot:append>
                          <q-btn
                            round
                            dense
                            flat
                            size="sm"
                            icon="mdi-close"
                            class="hover-show-item"
                            @click="deleteResponsibility(item, idx)"
                          />
                        </template>
                      </q-input>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section>
                      <div class="row">
                        <q-btn round dense flat icon="mdi-plus" @click="addResponsibility(item)" />
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
                <q-item-label header>{{
                  $t('src.components.settings.PromptSettings.emphasize')
                }}</q-item-label>
                <div class="row justify-end q-px-md">
                  <q-input
                    v-model="item.prompt.emphasize"
                    filled
                    square
                    type="textarea"
                    :placeholder="$t('src.components.settings.PromptSettings.emphasize')"
                    class="full-width"
                  />
                </div>
              </template>
              <div class="row justify-end q-pb-xs q-px-xs">
                <q-btn
                  flat
                  dense
                  padding="xs md"
                  :label="$t('src.components.settings.PromptSettings.delete')"
                  @click="deletePrompt(item.name)"
                  :disable="item.name === $t('default')"
                />
              </div>
            </q-expansion-item>
          </template>
        </q-scroll-area>
      </div>
    </template>
  </q-splitter>
</template>
<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();
const uiStore = useUiStore();
const splitterModel = ref(320);
const sences = [
  {
    label: $t('src.components.settings.PromptSettings.autoComplete'),
    value: 'autoComplete',
    description: $t('src.components.settings.PromptSettings.autoCompleteDescription'),
  },
  {
    label: $t('src.components.settings.PromptSettings.floatAgent'),
    value: 'floatAgent',
    description: $t('src.components.settings.PromptSettings.floatAgentDescription'),
  },
  {
    label: $t('src.components.settings.PromptSettings.chat'),
    value: 'chat',
    description: $t('src.components.settings.PromptSettings.chatDescription'),
  },
];

const selectedSense = ref('autoComplete');
const motifyTarget = ref(uiStore.perferences?.prompt[selectedSense.value]);
const selectedPrompt = ref('');

// 计算属性：为q-select提供选项
const promptOptions = computed(() => {
  if (!motifyTarget.value?.list) return [];
  return motifyTarget.value.list.map((item) => ({
    label: item.name,
    value: item.name,
  }));
});

// 监听selectedSense变化，更新selectedPrompt
watch(
  () => selectedSense.value,
  (newSense) => {
    motifyTarget.value = uiStore.perferences?.prompt[newSense];
    selectedPrompt.value = motifyTarget.value?.selected || '';
  },
  { immediate: true }
);

// 监听selectedPrompt变化，更新selected字段
watch(
  () => selectedPrompt.value,
  (newPrompt) => {
    if (motifyTarget.value && newPrompt !== motifyTarget.value.selected) {
      motifyTarget.value.selected = newPrompt;
    }
  }
);

const selectSense = (sense: string) => {
  selectedSense.value = sense;
  motifyTarget.value = uiStore.perferences?.prompt[sense];
  selectedPrompt.value = motifyTarget.value?.selected || '';
};

const newPromptName = ref('');
const toggleAdd = ref(false);
const addPrompt = () => {
  const _prompt =
    selectedSense.value === 'chat'
      ? {
          responsibilities: [$t('src.components.settings.PromptSettings.newResponsibility')],
          emphasize: '',
        }
      : '';
  motifyTarget.value?.list.push({
    name: newPromptName.value,
    prompt: _prompt,
  });
  // 添加新提示词后自动选择它
  selectedPrompt.value = newPromptName.value;
  toggleAdd.value = false;
  newPromptName.value = '';
};

const deletePrompt = (name: string) => {
  if (name === $t('default')) return; // 不允许删除默认提示词
  
  // 如果要删除的是当前选中的提示词，则选择第一个可用的提示词
  if (selectedPrompt.value === name) {
    const remainingPrompts = motifyTarget.value?.list.filter((item) => item.name !== name);
    if (remainingPrompts && remainingPrompts.length > 0) {
      selectedPrompt.value = remainingPrompts[0].name;
    }
  }
  
  motifyTarget.value.list = motifyTarget.value.list.filter((item) => item.name !== name);
};

const addResponsibility = (item) => {
  item.prompt.responsibilities.push('');
};

const deleteResponsibility = (item, idx) => {
  item.prompt.responsibilities.splice(idx, 1);
};
</script>
