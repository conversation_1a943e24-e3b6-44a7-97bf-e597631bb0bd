<template>
  <div
    ref="emojiListGrid"
    class="emoji-list-grid radius-sm border q-pa-sm"
    :class="$q.dark.isActive ? 'bg-black' : 'bg-white'"
    tabindex="0"
    @keydown="onKeyDown"
    :style="`
      grid-template-columns: repeat(8, auto);
    `"
  >
    <div
      v-for="(item, index) in items"
      :key="index"
      class="emoji-grid-item"
      :class="{ 'is-selected': index === selectedIndex }"
      @click="selectItem(index)"
    >
      <img v-if="item.fallbackImage" :src="item.fallbackImage" class="emoji-image" />
      <span v-else class="emoji-text">{{ item.emoji }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, useTemplateRef, computed } from 'vue';
import type { Editor } from '@tiptap/core';
import { useQuasar } from 'quasar';

const $q = useQuasar();

interface EmojiItem {
  emoji: string;
  name: string;
  fallbackImage?: string;
}

interface Props {
  items: EmojiItem[];
  command: (params: { name: string }) => void;
  editor: Editor;
}

const props = defineProps<Props>();
const selectedIndex = ref(0);
const maxColCount = 8;
const colCountComputed = computed(() => Math.min(props.items.length, maxColCount));
const emojiListGrid = useTemplateRef('emojiListGrid');
watch(
  () => props.items,
  () => {
    selectedIndex.value = 0;
  },
);

const selectItem = (index: number) => {
  const item = props.items[index];
  if (item) {
    props.command({ name: item.name });
    const { editor } = props;
    const { state, view } = editor;
    const { from } = state.selection;
    if (from > 1) {
      const prevChar = state.doc.textBetween(from - 2, from - 1);
      if (prevChar === ' ') {
        editor.commands.command(({ tr }) => {
          tr.delete(from - 2, from - 1);
          view.dispatch(tr);
          return true;
        });
      }
    }
  }
};

const onKeyDown = (event: KeyboardEvent) => {
  const len = props.items.length;
  if (event.key === 'ArrowUp') {
    event.preventDefault();
    selectedIndex.value = (selectedIndex.value - colCountComputed.value + len) % len;
  } else if (event.key === 'ArrowDown') {
    event.preventDefault();
    selectedIndex.value = (selectedIndex.value + colCountComputed.value) % len;
  } else if (event.key === 'ArrowLeft') {
    event.preventDefault();
    selectedIndex.value = (selectedIndex.value - 1 + len) % len;
  } else if (event.key === 'ArrowRight') {
    event.preventDefault();
    selectedIndex.value = (selectedIndex.value + 1) % len;
  } else if (event.key === 'Enter') {
    event.preventDefault();
    selectItem(selectedIndex.value);
  }
};

// 自动聚焦
onMounted(async () => {
  await nextTick();
  if (emojiListGrid.value) {
    emojiListGrid.value.focus();
  }
});

defineExpose({
  onKeyDown,
});
</script>

<style lang="scss">
.emoji-list-grid {
  display: grid;
  gap: 0.3em;
  outline: none;
  width: max-content;
  min-width: 0;
}
.emoji-grid-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.2em;
  height: 2.2em;
  padding: 0.2em;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.15s;
  font-size: 1.5em;
  &.is-selected {
    background: var(--q-primary-light, #e0eaff);
    box-shadow: 0 0 0 2px var(--q-primary, #1976d2);
  }
  &:hover {
    background: var(--q-primary-light, #e0eaff);
  }
}
.emoji-image {
  width: 1.5em;
  height: 1.5em;
  object-fit: contain;
}
.emoji-text {
  font-size: 1.5em;
  font-family:
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', 'Android Emoji',
    'EmojiSymbols', 'Twemoji Mozilla', 'sans-serif';
  vertical-align: middle;
}
</style>
