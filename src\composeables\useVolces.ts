import type { Message, AssistantMessage as AssistantMessageType } from 'src/types/qwen';
import { computed, type Ref } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import { useUiStore } from 'src/stores/ui';
import { DEFAULT_VOLCES_SETTINGS } from 'src/config/defaultSettings';
import { executeToolCall } from 'src/llm/tools/index';
import { PromptService, type ConversationRole } from 'src/services/promptService';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import { $t } from 'src/composables/useTrans';

export const useVolces = () => {
  // 在函数内部获取组合式函数
  const { updateConversation } = useSqlite();
  const uiStore = useUiStore();

  // 使用 computed 来创建响应式的 volcesSettings
  const volcesSettings = computed(() => {
    return uiStore.perferences?.llm?.volces || DEFAULT_VOLCES_SETTINGS;
  });

  // readSettings 方法现在不再需要，因为设置是响应式的
  // 保留此方法以保持向后兼容性，但实际上不执行任何操作
  const readSettings = () => {
    // 设置现在通过 computed 自动同步，无需手动读取
    console.log('[useVolces] 设置已自动同步:', volcesSettings.value);
  };

  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const { messages, messagesRef, conversation, tools, enableTools, abortController } = params;

    if (!volcesSettings.value) return;

    console.log('[useVolces] 开始发送消息');
    console.log('[useVolces] 消息数量:', messages.length);
    console.log('[useVolces] 工具数量:', tools.length);
    console.log('[useVolces] 启用工具:', enableTools);

    loading.value = true;

    try {
      // 用于累积工具调用数据的临时存储（与MiniMax一致）
      const toolCallsBuffer = new Map<
        string,
        {
          id: string;
          type: string;
          function: {
            name: string;
            arguments: string;
          };
        }
      >();

      // 构建请求体
      const requestBody: {
        model: string;
        messages: Message[];
        temperature?: number;
        max_tokens?: number;
        stream: boolean;
        top_p?: number;
        enable_thinking: boolean;
        tools?: Array<{
          type: string;
          function: {
            name: string;
            description: string;
            parameters: Record<string, unknown>;
          };
        }>;
        tool_choice?: string;
      } = {
        model: volcesSettings.value.model,
        messages: [...messages],
        max_tokens: volcesSettings.value.maxTokens,
        stream: true,
        enable_thinking: true, // 启用思考模式
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (
        volcesSettings.value.temperature !== undefined &&
        !isNaN(volcesSettings.value.temperature)
      ) {
        requestBody.temperature = volcesSettings.value.temperature;
      }
      if (volcesSettings.value.topP !== undefined && !isNaN(volcesSettings.value.topP)) {
        requestBody.top_p = volcesSettings.value.topP;
      }

      // 如果有启用的工具，添加工具定义
      if (enableTools && tools.length > 0) {
        console.log(`🔧 [useVolces] 启用工具数: ${tools.length}`);
        console.log(`🔧 [useVolces] 工具列表: ${tools.map((t) => t.function.name).join(', ')}`);

        requestBody.tools = tools;
        requestBody.tool_choice = 'auto';
      }

      console.log('[useVolces] 请求体:', requestBody);

      // 发送请求
      const apiUrl = `${volcesSettings.value.baseUrl}/chat/completions`;
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${volcesSettings.value.apiKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[useVolces] API 请求失败:', response.status, errorText);
        throw new Error(`Volces API 错误: ${response.status} ${errorText}`);
      }

      // 直接操作响应式消息数组，实现流式更新
      messagesRef.value.push({
        role: 'assistant',
        content: '',
        reasoning_content: '', // 与Qwen一致的思考内容初始化
      });
      const lastMessage = messagesRef.value[messagesRef.value.length - 1];

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let volcesContentBuffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '' || !line.startsWith('data: ')) continue;
            if (line.trim() === 'data: [DONE]') continue;

            const data = line.slice(6);
            if (data.trim() === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices[0]?.delta;
              const finishReason = parsed.choices[0]?.finish_reason;

              if (!delta) continue;

              const content = delta.content || '';
              const reasoning_content = delta.reasoning_content || ''; // 与Qwen一致的思考内容处理

              // 处理思考内容（与Qwen一致）
              if (parsed.choices[0]?.index === 0) {
                (lastMessage as AssistantMessageType).reasoning_content += '';
              }

              if (reasoning_content) {
                (lastMessage as AssistantMessageType).reasoning_content += reasoning_content;
              }

              // 处理普通内容
              if (content) {
                volcesContentBuffer += content;
                (lastMessage as AssistantMessageType).content = volcesContentBuffer;
              }

              // 处理工具调用（与MiniMax一致的累积模式）
              if (delta?.tool_calls) {
                console.log('[useVolces] 检测到工具调用数据:', delta.tool_calls);

                delta.tool_calls.forEach(
                  (
                    toolCall: {
                      id?: string;
                      type?: string;
                      function?: {
                        name?: string;
                        arguments?: string;
                      };
                    },
                    index: number,
                  ) => {
                    // 如果有 ID，说明是第一条消息，创建新的工具调用对象
                    if (toolCall.id && toolCall.id.trim() !== '') {
                      console.log(`[useVolces] 创建新的工具调用对象，ID: ${toolCall.id}`);
                      toolCallsBuffer.set(toolCall.id, {
                        id: toolCall.id,
                        type: toolCall.type || 'function',
                        function: {
                          name: toolCall.function?.name || '',
                          arguments: toolCall.function?.arguments || '',
                        },
                      });
                    } else {
                      // ID 为空，说明是后续消息，需要累加到现有的工具调用对象中
                      const existingEntries = Array.from(toolCallsBuffer.entries());
                      if (existingEntries.length > index) {
                        const [toolCallId, bufferedToolCall] = existingEntries[index];

                        // 累积工具名称
                        if (toolCall.function?.name) {
                          bufferedToolCall.function.name += toolCall.function.name;
                        }

                        // 累积工具参数
                        if (toolCall.function?.arguments) {
                          bufferedToolCall.function.arguments += toolCall.function.arguments;
                        }

                        console.log(`[useVolces] 工具调用累积中 ${toolCallId}:`, bufferedToolCall);
                      }
                    }
                  },
                );
              }

              // 当 finish_reason 为 "tool_calls" 时，完成工具调用累积
              if (finishReason === 'tool_calls' && toolCallsBuffer.size > 0) {
                console.log('[useVolces] 工具调用完成，开始处理完整的工具调用数据');

                // 将累积的工具调用数据转换为最终格式
                (lastMessage as AssistantMessageType).tool_calls = Array.from(
                  toolCallsBuffer.values(),
                ).map((toolCall, index) => ({
                  id: toolCall.id,
                  type: 'function' as const,
                  index: index,
                  function: {
                    name: toolCall.function.name,
                    arguments: toolCall.function.arguments,
                  },
                }));

                console.log(
                  '[useVolces] 最终工具调用数据:',
                  (lastMessage as AssistantMessageType).tool_calls,
                );

                // 清空缓冲区
                toolCallsBuffer.clear();
              } else if (finishReason === 'tool_calls' && toolCallsBuffer.size === 0) {
                console.log('[useVolces] 收到重复的 finish_reason: tool_calls，忽略');
              }
            } catch (parseError) {
              console.warn('[useVolces] 解析响应失败:', parseError, 'data:', data);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      // 最终处理完整内容
      if (volcesContentBuffer) {
        (lastMessage as AssistantMessageType).content = volcesContentBuffer;
        // reasoning_content 已经在流式处理中直接设置，无需额外处理
      }

      // 处理工具调用
      const assistantMessage = lastMessage as AssistantMessageType;
      if (assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {
        console.log(
          `[useVolces] 检测到 ${assistantMessage.tool_calls.length} 个工具调用，开始执行`,
        );

        // 执行工具调用
        for (const toolCall of assistantMessage.tool_calls) {
          try {
            console.log(`[useVolces] 执行工具: ${toolCall.function.name}`);
            console.log(`[useVolces] 工具参数: ${toolCall.function.arguments}`);

            const toolArgs = JSON.parse(toolCall.function.arguments);
            const toolResult = await executeToolCall(toolCall.function.name, toolArgs);

            // 添加工具结果消息
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify(toolResult),
              tool_call_id: toolCall.id,
            });

            console.log(`[useVolces] 工具 ${toolCall.function.name} 执行完成:`, toolResult);
          } catch (error) {
            console.error(`[useVolces] 工具 ${toolCall.function.name} 执行失败:`, error);
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify({
                success: false,
                message: $t('src.composeables.useVolces.toolExecutionFailed', {
                  error: error instanceof Error ? error.message : String(error),
                }),
              }),
              tool_call_id: toolCall.id,
            });
          }
        }

        // 如果有工具调用，递归调用获取AI对工具结果的响应
        console.log('工具执行完成，开始递归调用获取AI响应...');
        console.log('当前消息数量:', messages.length);
        console.log(
          '最后几条消息:',
          messages.slice(-3).map((m) => ({
            role: m.role,
            content:
              typeof m.content === 'string'
                ? m.content.substring(0, 100)
                : JSON.stringify(m.content).substring(0, 100),
          })),
        );

        // 递归调用以获取最终响应
        console.log('🔄 [useVolces] 递归调用获取最终响应');
        const recursiveParams: SimplifiedLlmParams = {
          ...params,
          messages: messagesRef.value,
        };
        await sendMessage(recursiveParams, loading);
        return;
      }
      console.log('[useVolces] 消息发送完成');
    } catch (error) {
      console.error('[useVolces] Error sending message:', error);
      messagesRef.value.push({
        role: 'assistant',
        content: $t('src.composeables.useVolces.errorOccurred'),
      });
    } finally {
      loading.value = false;

      await updateConversation(
        conversation.id,
        conversation.title,
        JSON.stringify(messagesRef.value),
        conversation.prompt,
      );
    }
  };

  return {
    readSettings,
    volcesSettings,
    sendMessage,
    // 导出prompt服务的方法
    getAvailableRoles: () => PromptService.getAvailableRoles(),
    getRoleSuggestions: (role: ConversationRole) => PromptService.getRoleSuggestions(role),
  };
};
