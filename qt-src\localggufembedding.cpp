#include "localggufembedding.h"

// llama.cpp includes
#ifdef ENABLE_LOCAL_GGUF
#include "llama.h"
#include "common.h"
#endif

#include <QStandardPaths>
#include <QCoreApplication>
#include <QElapsedTimer>

// 定义全局推理锁
QMutex LocalGGUFEmbedding::s_globalInferenceMutex;

LocalGGUFEmbedding::LocalGGUFEmbedding(QObject *parent)
    : QObject(parent)
{
    logInfo("Initializing LocalGGUFEmbedding");

#ifdef ENABLE_LOCAL_GGUF
    // 初始化llama.cpp后端
    if (!initializeLlamaBackend())
    {
        logError("Failed to initialize llama.cpp backend");
        return;
    }

    // 检测GPU能力
    m_gpuInfo = detectGpuCapabilities();

    // 初始化GPU设备列表
    initializeGpuDevices();

    if (m_gpuInfo.available)
    {
        logInfo(QString("GPU detected: %1 (%2), max layers: %3")
                    .arg(m_gpuInfo.deviceName)
                    .arg(m_gpuInfo.backend)
                    .arg(m_gpuInfo.maxLayers));

        logInfo(QString("Found %1 GPU device(s)").arg(m_availableGpuDevices.size() - 1)); // -1 because CPU is included
    }
    else
    {
        logInfo("No GPU acceleration available, will use CPU only");
    }
#else
    logError("Local GGUF support not compiled in. Please rebuild with ENABLE_LOCAL_GGUF=ON");
#endif
}

LocalGGUFEmbedding::~LocalGGUFEmbedding()
{
    unloadModel();
    cleanupLlamaBackend();
}

bool LocalGGUFEmbedding::loadModel(const ModelConfig &config)
{
    QWriteLocker locker(&m_rwLock); // 使用写锁保护模型加载

#ifndef ENABLE_LOCAL_GGUF
    m_lastError = "Local GGUF support not compiled in";
    logError(m_lastError);
    emit errorOccurred(m_lastError);
    return false;
#else
    try
    {
        // 1. 验证模型文件
        if (config.modelPath.isEmpty())
        {
            m_lastError = "Model path is empty";
            logError(m_lastError);
            emit errorOccurred(m_lastError);
            return false;
        }

        QFileInfo fileInfo(config.modelPath);
        if (!fileInfo.exists() || !fileInfo.isReadable())
        {
            m_lastError = QString("Model file not found or not readable: %1").arg(config.modelPath);
            logError(m_lastError);
            emit errorOccurred(m_lastError);
            return false;
        }

        if (!isValidGGUFFile(config.modelPath))
        {
            m_lastError = QString("Invalid GGUF file format: %1").arg(config.modelPath);
            logError(m_lastError);
            emit errorOccurred(m_lastError);
            return false;
        }

        // 2. 先卸载现有模型
        if (m_modelLoaded)
        {
            unloadModel();
        }

        // 3. 保存配置
        m_config = config;

        // 4. 创建模型参数
        llama_model_params modelParams = getModelParams();

        logInfo(QString("Loading GGUF model: %1").arg(config.modelPath));
        logInfo(QString("Model parameters - GPU layers: %1, Context: %2, Threads: %3")
                    .arg(config.gpuLayers)
                    .arg(config.contextSize)
                    .arg(config.threads));

        // 5. 加载模型
        std::string modelPathStd = config.modelPath.toStdString();
        m_model = llama_load_model_from_file(modelPathStd.c_str(), modelParams);

        if (!m_model)
        {
            m_lastError = "Failed to load llama model from file";
            logError(m_lastError);
            emit errorOccurred(m_lastError);
            return false;
        }

        logInfo("Model loaded successfully, creating context");

        // 6. 创建上下文
        llama_context_params contextParams = getContextParams();
        m_context = llama_new_context_with_model(m_model, contextParams);

        if (!m_context)
        {
            m_lastError = "Failed to create llama context";
            logError(m_lastError);
            llama_free_model(m_model);
            m_model = nullptr;
            emit errorOccurred(m_lastError);
            return false;
        }

        // 7. 验证嵌入模式
        int embeddingSize = llama_n_embd(m_model);
        if (embeddingSize <= 0)
        {
            m_lastError = "Model does not support embeddings or embedding size is invalid";
            logError(m_lastError);
            llama_free(m_context);
            llama_free_model(m_model);
            m_context = nullptr;
            m_model = nullptr;
            emit errorOccurred(m_lastError);
            return false;
        }

        m_modelLoaded = true;

        logInfo(QString("GGUF model loaded successfully - embedding dimension: %1, GPU layers: %2")
                    .arg(embeddingSize)
                    .arg(config.gpuLayers));

        emit modelLoaded(config.modelPath);
        return true;
    }
    catch (const std::exception &e)
    {
        m_lastError = QString("Exception during model loading: %1").arg(e.what());
        logError(m_lastError);

        // 清理资源
        if (m_context)
        {
            llama_free(m_context);
            m_context = nullptr;
        }
        if (m_model)
        {
            llama_free_model(m_model);
            m_model = nullptr;
        }
        m_modelLoaded = false;

        emit errorOccurred(m_lastError);
        return false;
    }
#endif
}

void LocalGGUFEmbedding::unloadModel()
{
    QWriteLocker locker(&m_rwLock); // 使用写锁保护模型卸载

    if (!m_modelLoaded)
    {
        return;
    }

    logInfo("Unloading model");

#ifdef ENABLE_LOCAL_GGUF
    if (m_context)
    {
        llama_free(m_context);
        m_context = nullptr;
    }

    if (m_model)
    {
        llama_free_model(m_model);
        m_model = nullptr;
    }
#endif

    m_modelLoaded = false;
    emit modelUnloaded();

    logInfo("Model unloaded successfully");
}

bool LocalGGUFEmbedding::isModelLoaded() const
{
    QReadLocker locker(&m_rwLock); // 使用读锁检查状态
    return m_modelLoaded;
}

QString LocalGGUFEmbedding::getModelInfo() const
{
    QReadLocker locker(&m_rwLock); // 使用读锁获取信息

    if (!m_modelLoaded)
    {
        return "No model loaded";
    }

#ifdef ENABLE_LOCAL_GGUF
    if (!m_model)
    {
        return "Model pointer invalid";
    }

    QString info;
    info += QString("Path: %1\n").arg(QFileInfo(m_config.modelPath).fileName());
    info += QString("Context size: %1\n").arg(m_config.contextSize);
    info += QString("GPU layers: %1\n").arg(m_config.gpuLayers);
    info += QString("CPU threads: %1\n").arg(m_config.threads);
    info += QString("GPU enabled: %1").arg(m_config.useGpu ? "Yes" : "No");

    return info;
#else
    return "GGUF support not compiled";
#endif
}

bool LocalGGUFEmbedding::isInferenceInProgress() const
{
    return m_inferenceInProgress.load();
}

void LocalGGUFEmbedding::setInferenceTimeout(int timeoutMs)
{
    QWriteLocker locker(&m_rwLock);
    m_inferenceTimeout = timeoutMs;
    logInfo(QString("Inference timeout set to %1ms").arg(timeoutMs));
}

LocalGGUFEmbedding::GpuInfo LocalGGUFEmbedding::detectGpuCapabilities()
{
    GpuInfo info;
    info.available = false;
    info.backend = "CPU";
    info.deviceName = "CPU";
    info.maxLayers = 0;

#ifdef ENABLE_LOCAL_GGUF
    qDebug() << "🔍 [LocalGGUF] GGUF support compiled, checking GPU capabilities...";

    // 检查CUDA支持
    if (llama_supports_gpu_offload())
    {
        info.available = true;
        info.backend = "CUDA";
        info.deviceName = "NVIDIA GPU";
        info.maxLayers = 50; // 保守估计，实际根据显存动态调整

        qDebug() << "🎮 [LocalGGUF] GPU acceleration available via CUDA";
        qDebug() << "📊 [LocalGGUF] GPU Info - Device: NVIDIA GPU, Backend: CUDA, Max Layers: 50";
    }
    else
    {
        qDebug() << "💻 [LocalGGUF] No GPU acceleration available, using CPU only";
        qDebug() << "🔍 [LocalGGUF] Possible reasons for GPU unavailability:";
        qDebug() << "  - CUDA runtime not installed or not found";
        qDebug() << "  - NVIDIA GPU driver not installed or outdated";
        qDebug() << "  - llama.cpp compiled without CUDA support";
        qDebug() << "  - CUDA libraries not in PATH";

        // 检查是否有NVIDIA GPU硬件
        info.deviceName = "CPU (GPU hardware may be present but not accessible)";
    }
#else
    qDebug() << "⚠️ [LocalGGUF] GGUF support not compiled - ENABLE_LOCAL_GGUF not defined";
    qDebug() << "💡 [LocalGGUF] To enable GPU support, recompile with ENABLE_LOCAL_GGUF=1";
    info.deviceName = "CPU (GGUF support not compiled)";
#endif

    return info;
}

int LocalGGUFEmbedding::getRecommendedGpuLayers(const QString &modelPath)
{
    Q_UNUSED(modelPath)

    GpuInfo gpuInfo = detectGpuCapabilities();
    if (!gpuInfo.available)
    {
        return 0; // 无GPU加速
    }

    // 推荐前20层使用GPU，这对大多数嵌入式模型来说是合理的
    return 20;
}

std::vector<float> LocalGGUFEmbedding::generateEmbedding(const QString &text)
{
    // 使用带超时的全局锁，避免死锁
    const int LOCK_TIMEOUT_MS = 30000; // 30秒超时

    if (!s_globalInferenceMutex.tryLock(LOCK_TIMEOUT_MS))
    {
        m_lastError = "Failed to acquire inference lock within timeout (30s) - possible deadlock detected";
        logError(m_lastError);
        return std::vector<float>();
    }

    // 确保锁在函数结束时释放
    auto lockGuard = [this]()
    {
        s_globalInferenceMutex.unlock();
        logDebug("Global inference lock released");
    };

    logInfo("Starting embedding generation (serialized with timeout protection)");

    // 快速状态检查
    {
        QReadLocker locker(&m_rwLock);
        if (!m_modelLoaded)
        {
            m_lastError = "No model loaded for embedding generation";
            logError(m_lastError);
            lockGuard();
            return std::vector<float>();
        }

        if (text.trimmed().isEmpty())
        {
            m_lastError = "Empty text provided for embedding";
            logError(m_lastError);
            lockGuard();
            return std::vector<float>();
        }
    }

    // 设置推理状态
    m_inferenceInProgress.store(true);

    // 确保在函数结束时重置状态
    auto resetInferenceState = [this]()
    {
        m_inferenceInProgress.store(false);
        logDebug("Inference state reset");
    };

#ifdef ENABLE_LOCAL_GGUF
    try
    {
        QElapsedTimer timer;
        timer.start();

        logDebug(QString("Generating embedding for text length: %1").arg(text.length()));

        // 检查模型状态（在全局锁保护下，无需额外锁）
        if (!m_modelLoaded || !m_model || !m_context)
        {
            m_lastError = "Model became unavailable during inference";
            logError(m_lastError);
            resetInferenceState();
            return std::vector<float>();
        }

        // 1. 分词
        std::vector<int> tokens = tokenizeText(text);
        if (tokens.empty())
        {
            m_lastError = "Failed to tokenize text";
            logError(m_lastError);
            resetInferenceState();
            return std::vector<float>();
        }

        logDebug(QString("Tokenized to %1 tokens").arg(tokens.size()));

        // 2. 提取嵌入向量 - 添加超时检查
        std::vector<float> embedding;

        // 检查是否已经超时
        if (timer.elapsed() > m_inferenceTimeout)
        {
            m_lastError = QString("Inference timeout (%1ms) reached during tokenization").arg(m_inferenceTimeout);
            logError(m_lastError);
            resetInferenceState();
            lockGuard();
            return std::vector<float>();
        }

        embedding = extractEmbeddingFromTokens(tokens);
        if (embedding.empty())
        {
            m_lastError = "Failed to extract embedding from tokens";
            logError(m_lastError);
            resetInferenceState();
            lockGuard();
            return std::vector<float>();
        }

        // 最终超时检查
        if (timer.elapsed() > m_inferenceTimeout)
        {
            m_lastError = QString("Inference timeout (%1ms) reached during embedding extraction").arg(m_inferenceTimeout);
            logError(m_lastError);
            resetInferenceState();
            lockGuard();
            return std::vector<float>();
        }

        qint64 inferenceTime = timer.elapsed();
        logInfo(QString("Embedding generated successfully in %1ms, dimension: %2")
                    .arg(inferenceTime)
                    .arg(embedding.size()));

        resetInferenceState();
        lockGuard(); // 释放全局锁
        return embedding;
    }
    catch (const std::exception &e)
    {
        m_lastError = QString("Exception during embedding generation: %1").arg(e.what());
        logError(m_lastError);
        resetInferenceState();
        lockGuard(); // 释放全局锁
        return std::vector<float>();
    }
#else
    Q_UNUSED(text)
    m_lastError = "GGUF support not compiled";
    logError(m_lastError);
    resetInferenceState();
    lockGuard(); // 释放全局锁
    return std::vector<float>();
#endif
}

LocalGGUFEmbedding::ModelConfig LocalGGUFEmbedding::getCurrentConfig() const
{
    QReadLocker locker(&m_rwLock); // 使用读锁获取配置
    return m_config;
}

bool LocalGGUFEmbedding::isGpuEnabled() const
{
    QReadLocker locker(&m_rwLock); // 使用读锁检查GPU状态
    return m_config.useGpu && m_gpuInfo.available;
}

QString LocalGGUFEmbedding::getLastError() const
{
    QReadLocker locker(&m_rwLock); // 使用读锁获取错误信息
    return m_lastError;
}

void LocalGGUFEmbedding::handleLoadingError(const QString &error)
{
    m_lastError = error;
    logError(error);
    emit errorOccurred(error);
}

bool LocalGGUFEmbedding::initializeLlamaBackend()
{
#ifdef ENABLE_LOCAL_GGUF
    try
    {
        llama_backend_init();
        // llama_numa_init(LLAMA_NUMA_STRATEGY_DISABLED); // 禁用NUMA以简化配置 - 新版本中已移除
        logDebug("llama.cpp backend initialized successfully");
        return true;
    }
    catch (const std::exception &e)
    {
        logError(QString("Failed to initialize llama.cpp backend: %1").arg(e.what()));
        return false;
    }
#else
    logError("GGUF support not compiled");
    return false;
#endif
}

void LocalGGUFEmbedding::cleanupLlamaBackend()
{
#ifdef ENABLE_LOCAL_GGUF
    llama_backend_free();
    logDebug("llama.cpp backend cleaned up");
#endif
}

#ifdef ENABLE_LOCAL_GGUF
llama_model_params LocalGGUFEmbedding::getModelParams() const
{
    llama_model_params params = llama_model_default_params();

    // GPU配置 - 支持设备选择和优化
    if (m_config.useGpu && m_gpuInfo.available)
    {
        // 验证选择的GPU设备是否有效
        if (m_config.selectedGpuDevice >= 0)
        {
            // 检查设备是否可用
            bool deviceValid = validateGpuDevice(m_config.selectedGpuDevice);
            if (deviceValid)
            {
                // 限制GPU层数以避免内存问题
                int safeGpuLayers = std::min(m_config.gpuLayers, 35); // 限制为35层
                params.n_gpu_layers = safeGpuLayers;

                // 设置GPU设备（如果llama.cpp支持）
                params.main_gpu = m_config.selectedGpuDevice;

                // 启用GPU优化
                params.use_mmap = true;
                params.use_mlock = false;

                logInfo(QString("GPU acceleration enabled: %1 layers on device %2 (limited from %3 for stability)")
                            .arg(safeGpuLayers)
                            .arg(m_config.selectedGpuDevice)
                            .arg(m_config.gpuLayers));
            }
            else
            {
                logWarning(QString("Selected GPU device %1 is not available, falling back to CPU")
                               .arg(m_config.selectedGpuDevice));
                params.n_gpu_layers = 0;
            }
        }
        else
        {
            // 使用默认GPU设备，限制层数
            int safeGpuLayers = std::min(m_config.gpuLayers, 35);
            params.n_gpu_layers = safeGpuLayers;
            logInfo(QString("GPU acceleration enabled: %1 layers (default GPU, limited from %2 for stability)")
                        .arg(safeGpuLayers)
                        .arg(m_config.gpuLayers));
        }
    }
    else
    {
        params.n_gpu_layers = 0;
        logDebug("GPU disabled or unavailable, using CPU only");
    }

    // 其他参数
    params.use_mmap = true;   // 使用内存映射提高加载速度
    params.use_mlock = false; // 不锁定内存，允许系统管理

    return params;
}

llama_context_params LocalGGUFEmbedding::getContextParams() const
{
    llama_context_params params = llama_context_default_params();

    params.n_ctx = m_config.contextSize;

    // 报告建议：为避免CPU超额订阅，在线程隔离环境中降低n_threads
    // 对于嵌入任务，1-2个线程通常已足够，避免过度的上下文切换开销
    params.n_threads = std::min(m_config.threads, 2);

    params.embeddings = true;  // 启用嵌入模式
    params.offload_kqv = true; // 卸载KV缓存到GPU

    // 针对嵌入任务的优化配置
    params.rope_scaling_type = LLAMA_ROPE_SCALING_TYPE_NONE; // 禁用不必要的RoPE缩放
    params.pooling_type = LLAMA_POOLING_TYPE_MEAN;           // 使用均值池化提取嵌入

    logDebug(QString("Context params - size: %1, threads: %2 (optimized for embedding)")
                 .arg(params.n_ctx)
                 .arg(params.n_threads));

    return params;
}
#endif

std::vector<int> LocalGGUFEmbedding::tokenizeText(const QString &text)
{
#ifdef ENABLE_LOCAL_GGUF
    if (!m_model)
    {
        logError("Model not loaded for tokenization");
        return std::vector<int>();
    }

    QByteArray utf8Text = text.toUtf8();
    std::vector<llama_token> tokens;

    // 预分配足够的空间
    tokens.resize(utf8Text.length() + 16);

    // 进行分词
    int tokenCount = llama_tokenize(
        llama_model_get_vocab(m_model),
        utf8Text.constData(),
        utf8Text.length(),
        tokens.data(),
        tokens.size(),
        true, // add_special
        false // parse_special
    );

    if (tokenCount < 0)
    {
        logError("Tokenization failed");
        return std::vector<int>();
    }

    // 使用chunkSize来限制token数量，而不是contextSize
    // 这样可以统一云端和本地的切割行为
    // 估算：1个字符约等于0.3-0.5个token（中文更接近1:1，英文更接近1:0.3）
    int estimatedMaxTokens = static_cast<int>(m_config.chunkSize * 1.2); // 保守估计，1.2倍于字符数
    int contextLimit = m_config.contextSize - 64;                        // 上下文硬限制，预留64个token缓冲
    int maxTokens = std::min(estimatedMaxTokens, contextLimit);
    int effectiveTokenCount = std::min(tokenCount, maxTokens);

    if (effectiveTokenCount < tokenCount)
    {
        logDebug(QString("Token count limited from %1 to %2 (chunkSize-based limit: %3, context limit: %4)")
                     .arg(tokenCount)
                     .arg(effectiveTokenCount)
                     .arg(estimatedMaxTokens)
                     .arg(contextLimit));
    }

    // 调整大小并转换类型
    std::vector<int> result;
    result.reserve(effectiveTokenCount);
    for (int i = 0; i < effectiveTokenCount; ++i)
    {
        result.push_back(static_cast<int>(tokens[i]));
    }

    logDebug(QString("Tokenization completed: %1 chars -> %2 tokens (limited to %3)")
                 .arg(text.length())
                 .arg(tokenCount)
                 .arg(effectiveTokenCount));

    return result;
#else
    Q_UNUSED(text)
    return std::vector<int>();
#endif
}

std::vector<float> LocalGGUFEmbedding::extractEmbeddingFromTokens(const std::vector<int> &tokens)
{
#ifdef ENABLE_LOCAL_GGUF
    if (!m_context || !m_model)
    {
        logError("Context or model not available for embedding extraction");
        return std::vector<float>();
    }

    if (tokens.empty())
    {
        logError("Empty tokens for embedding extraction");
        return std::vector<float>();
    }

    // 使用基于chunkSize的合理token限制，而不是contextSize
    // 这样可以与云端API的切割行为保持一致
    size_t estimatedMaxTokens = static_cast<size_t>(m_config.chunkSize * 1.2);
    size_t contextLimit = static_cast<size_t>(m_config.contextSize - 64);
    size_t maxTokens = std::min(estimatedMaxTokens, contextLimit);

    if (tokens.size() > maxTokens)
    {
        logDebug(QString("Token count %1 exceeds chunk-based limit %2 (estimated from chunkSize %3), applying chunking strategy")
                     .arg(tokens.size())
                     .arg(maxTokens)
                     .arg(m_config.chunkSize));

        // 使用基于chunkSize的分块处理
        return extractEmbeddingWithChunking(tokens, maxTokens);
    }

    // 转换token类型
    std::vector<llama_token> llamaTokens;
    llamaTokens.reserve(tokens.size());
    for (int token : tokens)
    {
        llamaTokens.push_back(static_cast<llama_token>(token));
    }

    // 清除上下文 - 对嵌入任务重要
    llama_memory_clear(llama_get_memory(m_context), true);

    // 批量处理tokens
    llama_batch batch = llama_batch_init(llamaTokens.size(), 0, 1);

    for (size_t i = 0; i < llamaTokens.size(); ++i)
    {
        common_batch_add(batch, llamaTokens[i], i, {0}, true);
    }

    // 只为最后一个token计算logits（嵌入任务标准做法）
    // 避免为所有位置计算logits导致的性能问题
    for (int i = 0; i < batch.n_tokens; ++i)
    {
        batch.logits[i] = false;
    }
    if (batch.n_tokens > 0)
    {
        batch.logits[batch.n_tokens - 1] = true;
    }

    // 进行推理 - 添加强制超时机制防止卡死
    logDebug(QString("Starting llama_decode for %1 tokens").arg(batch.n_tokens));
    QElapsedTimer decodeTimer;
    decodeTimer.start();

    // 添加推理前的状态检查
    logDebug(QString("Model status - loaded: %1, context: %2, tokens: %3")
                 .arg(m_modelLoaded ? "true" : "false")
                 .arg(m_context ? "valid" : "null")
                 .arg(batch.n_tokens));

    // 强制超时机制：使用QTimer配合QEventLoop实现超时控制
    std::atomic<bool> decodeCompleted{false};
    std::atomic<int> decodeResult{-1};

    // 在新线程中执行推理，添加异常处理
    QFuture<int> future = QtConcurrent::run([this, &batch, &decodeCompleted, &decodeResult]() -> int
                                            {
        try {
            int result = llama_decode(m_context, batch);
            decodeResult.store(result);
            decodeCompleted.store(true);
            return result;
        } catch (const std::exception &e) {
            logError(QString("Exception during llama_decode: %1").arg(e.what()));
            decodeResult.store(-999); // 特殊错误码表示异常
            decodeCompleted.store(true);
            return -999;
        } catch (...) {
            logError("Unknown exception during llama_decode");
            decodeResult.store(-999);
            decodeCompleted.store(true);
            return -999;
        } });

    // 使用QTimer和QEventLoop实现超时机制
    QEventLoop eventLoop;
    QTimer timeoutTimer;
    timeoutTimer.setSingleShot(true);

    // 监听future完成
    QFutureWatcher<int> watcher;
    watcher.setFuture(future);

    connect(&watcher, &QFutureWatcher<int>::finished, &eventLoop, &QEventLoop::quit);
    connect(&timeoutTimer, &QTimer::timeout, &eventLoop, &QEventLoop::quit);

    // 启动超时定时器（根据token数量动态调整）
    // 基础时间30秒，每100个token增加5秒（CPU推理需要更长时间）
    const int DECODE_TIMEOUT_MS = 30000 + (batch.n_tokens / 100) * 5000;
    timeoutTimer.start(DECODE_TIMEOUT_MS);

    // 等待完成或超时
    eventLoop.exec();

    qint64 decodeTime = decodeTimer.elapsed();

    // 检查是否超时
    bool timedOut = !decodeCompleted.load();
    if (timedOut)
    {
        logError(QString("llama_decode TIMEOUT after %1ms - force cancelling").arg(decodeTime));
        // 无法安全取消，只能记录错误并返回空结果
        llama_batch_free(batch);
        return std::vector<float>();
    }

    int result = decodeResult.load();

    logDebug(QString("llama_decode completed in %1ms, result: %2").arg(decodeTime).arg(result));

    // 检查是否超过预期时间（可能表明性能问题）
    if (decodeTime > 2000)
    {
        logWarning(QString("Decode took unexpectedly long: %1ms for %2 tokens")
                       .arg(decodeTime)
                       .arg(batch.n_tokens));
    }

    if (result != 0)
    {
        if (result == -999)
        {
            logError("Inference failed due to exception during llama_decode");
        }
        else
        {
            logError(QString("Inference failed with code: %1").arg(result));
        }
        llama_batch_free(batch);
        return std::vector<float>();
    }

    // 获取嵌入向量 - 直接使用token级别的嵌入（更简单、更可靠）
    int embeddingSize = llama_n_embd(m_model);
    const float *embeddingData = llama_get_embeddings_ith(m_context, batch.n_tokens - 1);

    if (!embeddingData)
    {
        logError("Failed to get token-level embedding data from model");
        llama_batch_free(batch);
        return std::vector<float>();
    }

    // 复制并归一化嵌入向量
    std::vector<float> embedding(embeddingData, embeddingData + embeddingSize);

    // L2归一化 - 对嵌入质量重要
    normalizeEmbedding(embedding);

    llama_batch_free(batch);

    logDebug(QString("Extracted and normalized embedding with dimension: %1").arg(embeddingSize));
    return embedding;
#else
    Q_UNUSED(tokens)
    return std::vector<float>();
#endif
}

void LocalGGUFEmbedding::logInfo(const QString &message) const
{
    qInfo() << "ℹ️ [LocalGGUF]" << message;
}

void LocalGGUFEmbedding::logError(const QString &message) const
{
    qCritical() << "❌ [LocalGGUF]" << message;
}

void LocalGGUFEmbedding::logDebug(const QString &message) const
{
    qDebug() << "🔍 [LocalGGUF]" << message;
}

void LocalGGUFEmbedding::logWarning(const QString &message) const
{
    qWarning() << "⚠️ [LocalGGUF]" << message;
}

QString LocalGGUFEmbedding::formatFileSize(qint64 bytes)
{
    const qint64 KB = 1024;
    const qint64 MB = KB * 1024;
    const qint64 GB = MB * 1024;

    if (bytes >= GB)
    {
        return QString("%1 GB").arg(static_cast<double>(bytes) / GB, 0, 'f', 2);
    }
    else if (bytes >= MB)
    {
        return QString("%1 MB").arg(static_cast<double>(bytes) / MB, 0, 'f', 1);
    }
    else if (bytes >= KB)
    {
        return QString("%1 KB").arg(static_cast<double>(bytes) / KB, 0, 'f', 1);
    }
    else
    {
        return QString("%1 bytes").arg(bytes);
    }
}

bool LocalGGUFEmbedding::isValidGGUFFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);

    // 检查文件存在和扩展名
    if (!fileInfo.exists() || !fileInfo.isReadable())
    {
        return false;
    }

    QString suffix = fileInfo.suffix().toLower();
    if (suffix != "gguf")
    {
        return false;
    }

    // 检查文件头魔数
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        return false;
    }

    QByteArray header = file.read(4);
    file.close();

    // GGUF文件魔数是 "GGUF"
    return header == "GGUF";
}

std::vector<float> LocalGGUFEmbedding::extractEmbeddingWithChunking(const std::vector<int> &tokens, size_t maxTokens)
{
    logDebug(QString("Processing %1 tokens with context-based chunking strategy, max chunk size: %2")
                 .arg(tokens.size())
                 .arg(maxTokens));

    std::vector<std::vector<float>> chunkEmbeddings;
    size_t start = 0;

    // 使用传入的maxTokens作为分块大小，不再添加额外限制
    size_t effectiveMaxTokens = maxTokens;

    logDebug(QString("Using max tokens per chunk: %1")
                 .arg(effectiveMaxTokens));

    while (start < tokens.size())
    {
        size_t end = std::min(start + effectiveMaxTokens, tokens.size());
        std::vector<int> chunk(tokens.begin() + start, tokens.begin() + end);

        logDebug(QString("Processing chunk from token %1 to %2 (%3 tokens)")
                     .arg(start)
                     .arg(end - 1)
                     .arg(chunk.size()));

        // 直接处理chunk，不再区分大小
        std::vector<float> chunkEmbedding = extractEmbeddingFromTokens(chunk);

        if (!chunkEmbedding.empty())
        {
            chunkEmbeddings.push_back(chunkEmbedding);
            logDebug(QString("Successfully processed chunk %1 with dimension %2")
                         .arg(chunkEmbeddings.size())
                         .arg(chunkEmbedding.size()));
        }
        else
        {
            logWarning(QString("Failed to get embedding for chunk starting at token %1, skipping chunk").arg(start));
            // 跳过失败的chunk，不使用fallback
        }

        start = end;

        // 移除重叠策略以避免复杂性
        // start = std::max(start - maxTokens / 4, start - 10);
    }

    if (chunkEmbeddings.empty())
    {
        logError("No valid chunk embeddings obtained, cannot generate embedding");
        return std::vector<float>();
    }

    // 平均池化所有chunk嵌入
    size_t embeddingDim = chunkEmbeddings[0].size();
    std::vector<float> avgEmbedding(embeddingDim, 0.0f);

    for (const auto &embedding : chunkEmbeddings)
    {
        if (embedding.size() != embeddingDim)
        {
            logWarning("Chunk embedding dimension mismatch, skipping");
            continue;
        }

        for (size_t i = 0; i < embeddingDim; ++i)
        {
            avgEmbedding[i] += embedding[i];
        }
    }

    // 取平均值
    float scale = 1.0f / static_cast<float>(chunkEmbeddings.size());
    for (float &val : avgEmbedding)
    {
        val *= scale;
    }

    // 归一化最终结果
    normalizeEmbedding(avgEmbedding);

    logDebug(QString("Context-based chunking completed: processed %1 chunks, final dimension: %2")
                 .arg(chunkEmbeddings.size())
                 .arg(embeddingDim));

    return avgEmbedding;
}

void LocalGGUFEmbedding::normalizeEmbedding(std::vector<float> &embedding)
{
    if (embedding.empty())
    {
        return;
    }

    // L2归一化：计算向量的模长
    float norm = 0.0f;
    for (float val : embedding)
    {
        norm += val * val;
    }

    norm = std::sqrt(norm);

    if (norm > 1e-8f) // 避免除零
    {
        float invNorm = 1.0f / norm;
        for (float &val : embedding)
        {
            val *= invNorm;
        }
    }
    else
    {
        logWarning("Embedding vector has near-zero norm, normalization skipped");
    }
}

// =============================================================================
// 多GPU设备支持实现
// =============================================================================

void LocalGGUFEmbedding::initializeGpuDevices()
{
    logInfo("Initializing GPU device detection...");

    // 检测所有可用的GPU设备
    m_availableGpuDevices = enumerateGpuDevices();

    logInfo(QString("GPU device detection completed. Found %1 device(s) (including CPU)")
                .arg(m_availableGpuDevices.size()));

    for (const auto &device : m_availableGpuDevices)
    {
        logInfo(QString("Device %1: %2 (%3) - Max layers: %4, Available: %5")
                    .arg(device.deviceId)
                    .arg(device.deviceName)
                    .arg(device.backend)
                    .arg(device.maxLayers)
                    .arg(device.available ? "Yes" : "No"));
    }
}

std::vector<LocalGGUFEmbedding::GpuDeviceInfo> LocalGGUFEmbedding::detectAllGpuDevices()
{
    return enumerateGpuDevices();
}

std::vector<LocalGGUFEmbedding::GpuDeviceInfo> LocalGGUFEmbedding::enumerateGpuDevices()
{
    std::vector<GpuDeviceInfo> devices;

    // 始终添加CPU选项（设备ID: -1）
    GpuDeviceInfo cpuDevice;
    cpuDevice.deviceId = -1;
    cpuDevice.deviceName = "CPU (不使用GPU)";
    cpuDevice.backend = "CPU";
    cpuDevice.available = true;
    cpuDevice.maxLayers = 0;
    cpuDevice.recommendedLayers = 0;
    cpuDevice.memorySize = 0;
    cpuDevice.availableMemory = 0;
    devices.push_back(cpuDevice);

#ifdef ENABLE_LOCAL_GGUF
    // 检查llama.cpp是否支持GPU
    if (llama_supports_gpu_offload())
    {
        qDebug() << "🔍 [LocalGGUF] CUDA GPU acceleration available, detecting devices...";

        // 使用真正的CUDA API检测设备
        int deviceCount = 0;

        // 尝试获取CUDA设备数量
        bool cudaApiAvailable = false;

#ifdef __CUDACC__
        // 如果编译时启用了CUDA，使用CUDA Runtime API
        cudaError_t cudaStatus = cudaGetDeviceCount(&deviceCount);
        if (cudaStatus == cudaSuccess)
        {
            cudaApiAvailable = true;
            qDebug() << QString("🎮 [LocalGGUF] CUDA API available, found %1 device(s)").arg(deviceCount);
        }
        else
        {
            qDebug() << QString("⚠️ [LocalGGUF] CUDA API error: %1").arg(cudaGetErrorString(cudaStatus));
        }
#else
        // 如果没有CUDA编译支持，使用llama.cpp的GPU层数试探法
        // 这是一个fallback方法，不如直接CUDA API准确
        qDebug() << "🔍 [LocalGGUF] CUDA API not available at compile time, using llama.cpp detection";

        // 尝试使用nvidia-ml-py或类似工具的思路
        // 由于llama.cpp已经检测到GPU支持，我们假设至少有一个设备
        deviceCount = detectGpuDeviceCountFallback();
        cudaApiAvailable = (deviceCount > 0);
#endif

        if (cudaApiAvailable && deviceCount > 0)
        {
            for (int i = 0; i < deviceCount; ++i)
            {
                GpuDeviceInfo gpuDevice;
                gpuDevice.deviceId = i;
                gpuDevice.backend = "CUDA";
                gpuDevice.available = true;

                // 获取真实的设备属性
                bool deviceInfoValid = false;

#ifdef __CUDACC__
                // 使用CUDA API获取设备属性
                cudaDeviceProp deviceProp;
                cudaError_t propStatus = cudaGetDeviceProperties(&deviceProp, i);
                if (propStatus == cudaSuccess)
                {
                    gpuDevice.deviceName = QString::fromUtf8(deviceProp.name);
                    gpuDevice.memorySize = deviceProp.totalGlobalMem / (1024 * 1024); // 转换为MB
                    gpuDevice.availableMemory = gpuDevice.memorySize * 0.85;          // 假设85%可用（保守估计）

                    // 基于设备计算能力和内存大小估算合适的GPU层数
                    int computeCapability = deviceProp.major * 10 + deviceProp.minor;
                    if (computeCapability >= 75)
                    {                                                                                             // RTX 20/30/40 series
                        gpuDevice.maxLayers = std::min(80, static_cast<int>(gpuDevice.memorySize / 100));         // 每100MB支持1层
                        gpuDevice.recommendedLayers = std::min(40, static_cast<int>(gpuDevice.memorySize / 200)); // 保守估计
                    }
                    else if (computeCapability >= 60)
                    { // GTX 10 series, older RTX
                        gpuDevice.maxLayers = std::min(60, static_cast<int>(gpuDevice.memorySize / 120));
                        gpuDevice.recommendedLayers = std::min(30, static_cast<int>(gpuDevice.memorySize / 250));
                    }
                    else
                    { // Older GPUs
                        gpuDevice.maxLayers = std::min(40, static_cast<int>(gpuDevice.memorySize / 150));
                        gpuDevice.recommendedLayers = std::min(20, static_cast<int>(gpuDevice.memorySize / 300));
                    }

                    deviceInfoValid = true;

                    qDebug() << QString("🎮 [LocalGGUF] GPU %1: %2, %3MB VRAM, CC %4.%5, Max layers: %6, Recommended: %7")
                                    .arg(i)
                                    .arg(gpuDevice.deviceName)
                                    .arg(gpuDevice.memorySize)
                                    .arg(deviceProp.major)
                                    .arg(deviceProp.minor)
                                    .arg(gpuDevice.maxLayers)
                                    .arg(gpuDevice.recommendedLayers);
                }
                else
                {
                    qDebug() << QString("⚠️ [LocalGGUF] Failed to get properties for GPU %1: %2")
                                    .arg(i)
                                    .arg(cudaGetErrorString(propStatus));
                }
#endif

                // 如果CUDA API失败，使用fallback信息
                if (!deviceInfoValid)
                {
                    gpuDevice.deviceName = getGpuDeviceNameFallback(i);
                    gpuDevice.memorySize = queryGpuMemoryFallback(i);
                    gpuDevice.availableMemory = gpuDevice.memorySize * 0.8;
                    gpuDevice.maxLayers = 50;
                    gpuDevice.recommendedLayers = 20;

                    qDebug() << QString("🔄 [LocalGGUF] Using fallback info for GPU %1: %2, %3MB VRAM")
                                    .arg(i)
                                    .arg(gpuDevice.deviceName)
                                    .arg(gpuDevice.memorySize);
                }

                devices.push_back(gpuDevice);
            }
        }
        else
        {
            qDebug() << "💻 [LocalGGUF] No CUDA devices detected or CUDA API unavailable";
        }
    }
    else
    {
        qDebug() << "💻 [LocalGGUF] No GPU acceleration available";
    }
#endif

    return devices;
}

LocalGGUFEmbedding::GpuDeviceInfo LocalGGUFEmbedding::getGpuDeviceInfo(int deviceId)
{
    std::vector<GpuDeviceInfo> devices = detectAllGpuDevices();

    for (const auto &device : devices)
    {
        if (device.deviceId == deviceId)
        {
            return device;
        }
    }

    // 如果找不到，返回CPU设备
    GpuDeviceInfo cpuDevice;
    cpuDevice.deviceId = -1;
    cpuDevice.deviceName = "CPU (设备未找到)";
    cpuDevice.backend = "CPU";
    cpuDevice.available = true;
    cpuDevice.maxLayers = 0;
    cpuDevice.recommendedLayers = 0;
    return cpuDevice;
}

size_t LocalGGUFEmbedding::getGpuMemoryInfo(int deviceId)
{
    return queryGpuMemory(deviceId);
}

size_t LocalGGUFEmbedding::queryGpuMemory(int deviceId)
{
    if (deviceId < 0)
    {
        return 0; // CPU设备没有显存
    }

#ifdef ENABLE_LOCAL_GGUF
    // 简化实现：返回默认值
    // 实际应该使用CUDA API查询真实显存大小
    // 例如：
    // cudaDeviceProp prop;
    // cudaGetDeviceProperties(&prop, deviceId);
    // return prop.totalGlobalMem / (1024 * 1024); // 转换为MB

    // 目前返回默认值（MB）
    return 8192; // 假设8GB显存
#else
    Q_UNUSED(deviceId)
    return 0;
#endif
}

bool LocalGGUFEmbedding::isGpuDeviceAvailable(int deviceId)
{
    if (deviceId < 0)
    {
        return true; // CPU总是可用的
    }

#ifdef ENABLE_LOCAL_GGUF
    return llama_supports_gpu_offload();
#else
    Q_UNUSED(deviceId)
    return false;
#endif
}

bool LocalGGUFEmbedding::validateGpuDevice(int deviceId)
{
    std::vector<GpuDeviceInfo> devices = detectAllGpuDevices();

    for (const auto &device : devices)
    {
        if (device.deviceId == deviceId && device.available)
        {
            return true;
        }
    }

    return false;
}

// GPU检测fallback方法实现
int LocalGGUFEmbedding::detectGpuDeviceCountFallback()
{
    // 使用多种方法尝试检测GPU数量
    int deviceCount = 0;

#ifdef ENABLE_LOCAL_GGUF
    // 方法1: 尝试通过环境变量检测
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    QString cudaVisibleDevices = env.value("CUDA_VISIBLE_DEVICES", "");
    if (!cudaVisibleDevices.isEmpty() && cudaVisibleDevices != "-1")
    {
        QStringList deviceList = cudaVisibleDevices.split(",", Qt::SkipEmptyParts);
        deviceCount = deviceList.size();
        qDebug() << QString("🔍 [LocalGGUF] Detected %1 GPU(s) from CUDA_VISIBLE_DEVICES: %2")
                        .arg(deviceCount)
                        .arg(cudaVisibleDevices);
        return deviceCount;
    }

    // 方法2: 尝试通过nvidia-smi检测（Windows/Linux通用）
    QProcess process;
    process.setProgram("nvidia-smi");
    process.setArguments(QStringList() << "--query-gpu=count" << "--format=csv,noheader,nounits");
    process.start();

    if (process.waitForFinished(3000))
    { // 3秒超时
        if (process.exitCode() == 0)
        {
            QString output = process.readAllStandardOutput().trimmed();
            bool ok;
            int count = output.toInt(&ok);
            if (ok && count > 0)
            {
                deviceCount = count;
                qDebug() << QString("🔍 [LocalGGUF] Detected %1 GPU(s) from nvidia-smi").arg(deviceCount);
                return deviceCount;
            }
        }
    }

    // 方法3: 尝试通过nvidia-ml查询
    process.setProgram("nvidia-smi");
    process.setArguments(QStringList() << "--list-gpus");
    process.start();

    if (process.waitForFinished(3000))
    {
        if (process.exitCode() == 0)
        {
            QString output = process.readAllStandardOutput();
            QStringList lines = output.split('\n', Qt::SkipEmptyParts);
            deviceCount = 0;
            for (const QString &line : lines)
            {
                if (line.contains("GPU ") && line.contains("UUID"))
                {
                    deviceCount++;
                }
            }
            if (deviceCount > 0)
            {
                qDebug() << QString("🔍 [LocalGGUF] Detected %1 GPU(s) from nvidia-smi --list-gpus").arg(deviceCount);
                return deviceCount;
            }
        }
    }

    // 方法4: 基于llama.cpp GPU支持的保守估计
    // 如果llama.cpp检测到GPU支持，我们假设至少有一个设备
    if (llama_supports_gpu_offload())
    {
        qDebug() << "🔍 [LocalGGUF] llama.cpp reports GPU support, assuming at least 1 device";
        return 1; // 保守估计
    }
#endif

    qDebug() << "🔍 [LocalGGUF] No GPU devices detected via fallback methods";
    return 0;
}

size_t LocalGGUFEmbedding::queryGpuMemoryFallback(int deviceId)
{
    if (deviceId < 0)
    {
        return 0; // CPU设备没有显存
    }

#ifdef ENABLE_LOCAL_GGUF
    // 方法1: 使用nvidia-smi查询特定设备的显存
    QProcess process;
    process.setProgram("nvidia-smi");
    process.setArguments(QStringList()
                         << QString("--query-gpu=memory.total")
                         << "--format=csv,noheader,nounits"
                         << "-i" << QString::number(deviceId));
    process.start();

    if (process.waitForFinished(3000))
    {
        if (process.exitCode() == 0)
        {
            QString output = process.readAllStandardOutput().trimmed();
            bool ok;
            size_t memoryMB = output.toULongLong(&ok);
            if (ok && memoryMB > 0)
            {
                qDebug() << QString("🔍 [LocalGGUF] GPU %1 memory from nvidia-smi: %2MB")
                                .arg(deviceId)
                                .arg(memoryMB);
                return memoryMB;
            }
        }
    }

    // 方法2: 查询所有GPU的显存信息
    process.setProgram("nvidia-smi");
    process.setArguments(QStringList() << "--query-gpu=memory.total" << "--format=csv,noheader,nounits");
    process.start();

    if (process.waitForFinished(3000))
    {
        if (process.exitCode() == 0)
        {
            QString output = process.readAllStandardOutput();
            QStringList lines = output.split('\n', Qt::SkipEmptyParts);
            if (deviceId < lines.size())
            {
                bool ok;
                size_t memoryMB = lines[deviceId].trimmed().toULongLong(&ok);
                if (ok && memoryMB > 0)
                {
                    qDebug() << QString("🔍 [LocalGGUF] GPU %1 memory from nvidia-smi (all): %2MB")
                                    .arg(deviceId)
                                    .arg(memoryMB);
                    return memoryMB;
                }
            }
        }
    }

    // 方法3: 根据设备ID推测常见GPU的显存大小
    // 这是一个非常粗糙的估计，仅作为最后的fallback
    QStringList commonMemorySizes = {"4096", "6144", "8192", "11264", "12288", "16384", "24576"};
    if (deviceId < commonMemorySizes.size())
    {
        size_t estimatedMemory = commonMemorySizes[deviceId].toULongLong();
        qDebug() << QString("🔄 [LocalGGUF] Using estimated memory for GPU %1: %2MB")
                        .arg(deviceId)
                        .arg(estimatedMemory);
        return estimatedMemory;
    }
#endif

    // 默认fallback值
    qDebug() << QString("🔄 [LocalGGUF] Using default memory estimate for GPU %1: 8192MB").arg(deviceId);
    return 8192; // 8GB默认值
}

QString LocalGGUFEmbedding::getGpuDeviceNameFallback(int deviceId)
{
    if (deviceId < 0)
    {
        return "CPU (不使用GPU)";
    }

#ifdef ENABLE_LOCAL_GGUF
    // 方法1: 使用nvidia-smi查询特定设备的名称
    QProcess process;
    process.setProgram("nvidia-smi");
    process.setArguments(QStringList()
                         << QString("--query-gpu=name")
                         << "--format=csv,noheader,nounits"
                         << "-i" << QString::number(deviceId));
    process.start();

    if (process.waitForFinished(3000))
    {
        if (process.exitCode() == 0)
        {
            QString output = process.readAllStandardOutput().trimmed();
            if (!output.isEmpty() && !output.contains("ERROR"))
            {
                qDebug() << QString("🔍 [LocalGGUF] GPU %1 name from nvidia-smi: %2")
                                .arg(deviceId)
                                .arg(output);
                return output;
            }
        }
    }

    // 方法2: 查询所有GPU的名称
    process.setProgram("nvidia-smi");
    process.setArguments(QStringList() << "--query-gpu=name" << "--format=csv,noheader,nounits");
    process.start();

    if (process.waitForFinished(3000))
    {
        if (process.exitCode() == 0)
        {
            QString output = process.readAllStandardOutput();
            QStringList lines = output.split('\n', Qt::SkipEmptyParts);
            if (deviceId < lines.size())
            {
                QString deviceName = lines[deviceId].trimmed();
                if (!deviceName.isEmpty() && !deviceName.contains("ERROR"))
                {
                    qDebug() << QString("🔍 [LocalGGUF] GPU %1 name from nvidia-smi (all): %2")
                                    .arg(deviceId)
                                    .arg(deviceName);
                    return deviceName;
                }
            }
        }
    }

    // 方法3: 使用nvidia-smi --list-gpus获取详细信息
    process.setProgram("nvidia-smi");
    process.setArguments(QStringList() << "--list-gpus");
    process.start();

    if (process.waitForFinished(3000))
    {
        if (process.exitCode() == 0)
        {
            QString output = process.readAllStandardOutput();
            QStringList lines = output.split('\n', Qt::SkipEmptyParts);

            int currentDevice = 0;
            for (const QString &line : lines)
            {
                if (line.contains("GPU ") && line.contains("UUID"))
                {
                    if (currentDevice == deviceId)
                    {
                        // 解析GPU名称，格式通常是: "GPU 0: NVIDIA GeForce RTX 4090 (UUID: GPU-...)"
                        QRegularExpression regex(R"(GPU\s+\d+:\s+([^(]+)\s+\(UUID:)");
                        QRegularExpressionMatch match = regex.match(line);
                        if (match.hasMatch())
                        {
                            QString deviceName = match.captured(1).trimmed();
                            qDebug() << QString("🔍 [LocalGGUF] GPU %1 name from --list-gpus: %2")
                                            .arg(deviceId)
                                            .arg(deviceName);
                            return deviceName;
                        }
                    }
                    currentDevice++;
                }
            }
        }
    }
#endif

    // 默认fallback值
    QString fallbackName = QString("NVIDIA GPU %1").arg(deviceId);
    qDebug() << QString("🔄 [LocalGGUF] Using default name for GPU %1: %2").arg(deviceId).arg(fallbackName);
    return fallbackName;
}