<template>
  <q-list dense style="min-width: 12rem">
    <q-item clickable v-ripple @click="onAddConversation">
      <q-item-section side>
        <q-icon name="mdi-plus" />
      </q-item-section>
      <q-item-section>{{
        $t('src.components.ConversitionHistory.addConversation')
      }}</q-item-section>
    </q-item>
    <q-separator class="op-5" />
    <q-item
      v-for="c in conversations"
      :key="c.id"
      clickable
      v-ripple
      :active="c.id === selectedId"
      class="hover-item"
      @click="onSelect(c)"
    >
      <q-item-section @dblclick.stop="startRename(c)">
        <template v-if="editingId === c.id">
          <q-input
            v-model="editingTitle"
            dense
            autofocus
            borderless
            @keyup.enter="confirmRename(c)"
            @blur="confirmRename(c)"
            @click.stop
            style="min-width: 80px"
          />
        </template>
        <template v-else>
          {{ c.title?.slice(0, 16) + (c.title && c.title.length > 16 ? '...' : '') }}
        </template>
      </q-item-section>
      <q-item-section side class="hover-show-item">
        <div class="row no-wrap items-center">
          <q-btn flat dense round icon="mdi-pencil" @click.stop="startRename(c)" size="sm" />
          <q-btn flat dense round icon="mdi-delete" @click.stop="onDelete(c.id)" size="sm">
            <q-tooltip>{{ $t('src.components.ConversitionHistory.deleteConversation') }}</q-tooltip>
          </q-btn>
        </div>
      </q-item-section>
    </q-item>
  </q-list>
</template>
<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import type { Conversation } from 'src/types/qwen';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

const props = defineProps<{
  conversations: Conversation[];
}>();

const emit = defineEmits<{
  (e: 'select', c: Conversation): void;
  (e: 'add'): void;
  (e: 'delete', id: number): void;
  (e: 'rename', id: number, title: string): void;
}>();

const selectedId = ref<number | undefined>();
const editingId = ref<number | undefined>();
const editingTitle = ref('');

const setSelectedId = (id: number) => {
  selectedId.value = id;
};
defineExpose({
  setSelectedId,
});

watch(
  props.conversations,
  (val) => {
    if (val && val.length > 0 && !val.find((c) => c.id === selectedId.value)) {
      selectedId.value = val[0].id;
    }
    // 如果当前编辑的对话被删除，退出编辑
    if (editingId.value && !val.find((c) => c.id === editingId.value)) {
      editingId.value = undefined;
      editingTitle.value = '';
    }
  },
  { immediate: true },
);

function onSelect(c: Conversation) {
  selectedId.value = c.id;
  emit('select', c);
}
function onAddConversation() {
  emit('add');
}
function onDelete(id?: number) {
  if (id !== undefined) emit('delete', id);
}
async function startRename(c: Conversation) {
  editingId.value = c.id;
  editingTitle.value = c.title || '';
  await nextTick();
  const input = document.querySelector('input.q-field__native');
  if (input) (input as HTMLInputElement).select();
}
function confirmRename(c: Conversation) {
  if (editingId.value === c.id && editingTitle.value.trim() && editingTitle.value !== c.title) {
    emit('rename', c.id, editingTitle.value.trim());
  }
  editingId.value = undefined;
  editingTitle.value = '';
}
</script>
