<template>
  <div
    ref="itemRef"
    class="draggable-item q-space"
    :class="{
      dragging: isDragging,
      'drag-over': isDragOver && showDropIndicator,
    }"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
    @drop="handleDrop"
    :draggable="draggable && uiStore.sortMode === SortMode.CUSTOM"
  >
    <div
      v-if="showDropIndicator && dropPosition === 'top'"
      class="drop-indicator drop-indicator--top"
      @dragover.prevent="handleIndicatorDragOver"
      @drop="handleIndicatorDrop"
    ></div>
    <slot />
    <div
      v-if="showDropIndicator && dropPosition === 'bottom'"
      class="drop-indicator drop-indicator--bottom"
      @dragover.prevent="handleIndicatorDragOver"
      @drop="handleIndicatorDrop"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { SortMode } from 'src/utils/sortUtils';

interface Props {
  itemId: number;
  itemType: 'folder' | 'document';
  itemIndex: number;
  parentId: number | null;
  draggable?: boolean;
}

interface Emits {
  (
    e: 'reorder',
    data: {
      dragIndex: number;
      dropIndex: number;
      position: 'before' | 'after';
    },
  ): void;
  (
    e: 'dragOverCenter',
    data: {
      folderId: number;
      event: DragEvent;
    },
  ): void;
}

const props = withDefaults(defineProps<Props>(), {
  draggable: true,
});

const emit = defineEmits<Emits>();

const uiStore = useUiStore();
const itemRef = ref<HTMLElement>();

const isDragging = ref(false);
const isDragOver = ref(false);
const dropPosition = ref<'top' | 'bottom'>('bottom');

const showDropIndicator = computed(() => {
  // 只有在同级排序时才显示占位符线
  // 跨文件夹移动时应该显示文件夹的 drag-over 样式，而不是占位符线
  return isDragOver.value && uiStore.sortMode === SortMode.CUSTOM;
});

interface DragDataType {
  itemId: number;
  itemType: 'folder' | 'document';
  itemIndex: number;
  parentId: number | null;
}

let dragData: DragDataType | null = null;

/**
 * 检查是否为无意义的拖拽位置
 * @param draggedIndex 被拖拽项的索引
 * @param currentIndex 当前项的索引
 * @param isTopHalf 是否在当前项的上半部分
 * @returns 是否为无意义位置
 */
const checkUselessPosition = (
  draggedIndex: number,
  currentIndex: number,
  isTopHalf: boolean,
): boolean => {
  // 情况1：拖拽到自己的上方或下方（无意义）
  if (draggedIndex === currentIndex) {
    return true;
  }

  // 情况2：拖拽到相邻位置且不会改变顺序（无意义）
  if (isTopHalf) {
    // 拖拽到当前项的上方
    // 如果被拖拽项就在当前项的上一个位置，则无意义
    return draggedIndex === currentIndex - 1;
  } else {
    // 拖拽到当前项的下方
    // 如果被拖拽项就在当前项的下一个位置，则无意义
    return draggedIndex === currentIndex + 1;
  }
};

const handleDragStart = (event: DragEvent) => {
  if (uiStore.sortMode !== SortMode.CUSTOM) {
    event.preventDefault();
    return;
  }

  isDragging.value = true;
  dragData = {
    itemId: props.itemId,
    itemType: props.itemType,
    itemIndex: props.itemIndex,
    parentId: props.parentId,
  };

  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', JSON.stringify(dragData));
    event.dataTransfer.effectAllowed = 'move';
  }
};

const handleDragEnd = () => {
  isDragging.value = false;
  dragData = null;
};

const handleDragOver = (event: DragEvent) => {
  if (uiStore.sortMode !== SortMode.CUSTOM) return;

  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move';
  }

  // 检查拖拽数据，判断是否为同级排序
  try {
    const data = event.dataTransfer?.getData('text/plain');
    if (data) {
      const draggedData = JSON.parse(data);

      // 只有同类型且同父级的拖拽才可能显示占位符
      const isSameTypeAndParent =
        draggedData.itemType === props.itemType &&
        draggedData.parentId === props.parentId &&
        draggedData.itemId !== props.itemId;

      if (isSameTypeAndParent && itemRef.value) {
        const rect = itemRef.value.getBoundingClientRect();
        const relativeY = event.clientY - rect.top;
        const height = rect.height;

        // 计算拖拽位置：上半部分显示上占位符，下半部分显示下占位符
        const isTopHalf = relativeY <= height / 2;
        const draggedIndex = draggedData.itemIndex;
        const currentIndex = props.itemIndex;

        // 检查是否为无意义的拖拽位置
        const isUselessPosition = checkUselessPosition(draggedIndex, currentIndex, isTopHalf);

        if (!isUselessPosition) {
          if (isTopHalf) {
            // 上半部分：显示上占位符
            isDragOver.value = true;
            dropPosition.value = 'top';
          } else {
            // 下半部分：显示下占位符
            isDragOver.value = true;
            dropPosition.value = 'bottom';
          }
        } else {
          // 无意义位置：不显示占位符
          isDragOver.value = false;
        }

        // 如果是文件夹类型且在中间区域，触发展开和内部占位符创建
        if (props.itemType === 'folder' && !isUselessPosition) {
          const middleQuarter = height * 0.25;
          if (relativeY > middleQuarter && relativeY < height - middleQuarter) {
            emit('dragOverCenter', {
              folderId: props.itemId,
              event: event,
            });
          }
        }
      } else {
        isDragOver.value = false;
      }
    }
  } catch (error) {
    // 如果解析失败，默认不显示占位符
    isDragOver.value = false;
    throw new Error(error instanceof Error ? error.message : error);
  }
};

const handleDragLeave = (event: DragEvent) => {
  // 检查鼠标是否离开了元素和占位符区域
  if (itemRef.value) {
    const relatedTarget = event.relatedTarget as Node;

    // 如果鼠标移动到了占位符上，不要隐藏指示器
    if (relatedTarget && relatedTarget instanceof Element) {
      const isMovingToIndicator =
        relatedTarget.classList.contains('drop-indicator') ||
        relatedTarget.closest('.drop-indicator');
      if (isMovingToIndicator) {
        return; // 保持占位符显示
      }
    }

    // 只有当鼠标真正离开元素且不在占位符上时才隐藏指示器
    if (!itemRef.value.contains(relatedTarget)) {
      // 延迟隐藏，给占位符一些时间来处理事件
      setTimeout(() => {
        // 再次检查鼠标是否在占位符区域
        const activeElement = document.elementFromPoint(event.clientX, event.clientY);
        if (
          !activeElement ||
          (!activeElement.classList.contains('drop-indicator') &&
            !activeElement.closest('.drop-indicator'))
        ) {
          isDragOver.value = false;
        }
      }, 50);
    }
  }
};

// 占位符的拖拽事件处理
const handleIndicatorDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move';
  }
  // 保持占位符显示
  isDragOver.value = true;
};

const handleIndicatorDrop = (event: DragEvent) => {
  event.preventDefault();
  console.log('🎯 [DraggableItem] 在占位符上放置');
  handleDrop(event);
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = false;

  if (uiStore.sortMode !== SortMode.CUSTOM) return;

  try {
    const data = event.dataTransfer?.getData('text/plain');
    if (!data) return;

    const draggedData = JSON.parse(data);

    // 检查是否为同类型且同父级的拖拽
    if (
      draggedData.itemType === props.itemType &&
      draggedData.parentId === props.parentId &&
      draggedData.itemId !== props.itemId
    ) {
      const dragIndex = draggedData.itemIndex;
      const dropIndex = props.itemIndex;
      const position = dropPosition.value === 'top' ? 'before' : 'after';

      console.log('🔄 [DraggableItem] 触发排序事件:', {
        dragIndex,
        dropIndex,
        position,
        draggedData,
      });

      emit('reorder', {
        dragIndex,
        dropIndex,
        position,
      });
    }
  } catch (error) {
    console.error('拖拽数据解析失败:', error);
  }
};
</script>

<style scoped>
.draggable-item {
  position: relative;
  transition: all 0.2s ease;
}

.draggable-item.dragging {
  opacity: 0.5;
  transform: scale(0.98);
}

.draggable-item.drag-over {
  background-color: rgba(25, 118, 210, 0.05);
}

.drop-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 26px; /* 增加高度，便于鼠标交互 */
  background-color: #1976d2;
  border-radius: 1px;
  z-index: 1000;
  pointer-events: auto; /* 允许鼠标事件 */
  cursor: copy; /* 显示拖拽可放置的鼠标样式 */

  /* 添加视觉效果 */
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #1976d2;
    transform: translateY(-50%);
  }

  /* 悬停效果 */
  &:hover {
    background-color: rgba(25, 118, 210, 0.2);
  }
}

.drop-indicator--top {
  top: -10px; /* 进一步增加上边距，为占位符交互留出空间 */
}

.drop-indicator--bottom {
  bottom: -10px; /* 进一步增加下边距，为占位符交互留出空间 */
}

/* 自定义排序模式下显示拖拽手柄 */
.draggable-item[draggable='true'] {
  cursor: grab;
}

.draggable-item[draggable='true']:active {
  cursor: grabbing;
}
</style>
