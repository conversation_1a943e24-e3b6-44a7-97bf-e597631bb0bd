<template>
  <div v-if="show" class="absolute-full z-max" :class="$q.dark.isActive ? 'bg-dark' : 'bg-grey-1'">
    <div class="fit column no-wrap">
      <!-- 编辑器 -->
      <div class="col">
        <SimpleEditor
          ref="createEditorRef"
          v-model="createDocumentContent"
          @ready="onEditorReady"
          class="fit"
        >
          <template #toolbar-left>
            <q-input
              v-model="createDocumentTitle"
              dense
              type="text"
              hide-bottom-space
              class="q-space"
              :placeholder="$t('src.components.CreateDocumentDialog.title')"
              :label="$t('src.components.CreateDocumentDialog.title')"
              :rules="[(val) => !!val?.trim() || $t('src.components.CreateDocumentDialog.rule')]"
              @blur="validateTitle"
            >
              <template #prepend>
                <q-btn
                  flat
                  dense
                  size="sm"
                  class="q-mx-sm"
                  icon="arrow_back"
                  @click="closeDialog"
                />
              </template>
              <template #append>
                <q-btn
                  flat
                  dense
                  size="sm"
                  icon="mdi-tune"
                  @click="showSettings = !showSettings"
                  :color="showSettings ? 'primary' : ''"
                />
                <q-btn-dropdown
                  split
                  color="primary"
                  flat
                  dense
                  class="border q-mx-sm"
                  @click="saveDocument"
                  :loading="loading || !!chunkingProgress"
                  :disable-main-btn="
                    !createDocumentContent?.trim() ||
                    !createDocumentTitle?.trim() ||
                    !chunkingConfigValid ||
                    !!chunkingProgress
                  "
                >
                  <template #label>
                    <div class="q-px-sm">
                      {{
                        chunkingProgress
                          ? $t('src.components.CreateDocumentDialog.processing')
                          : $t('src.components.CreateDocumentDialog.save')
                      }}
                    </div>
                  </template>
                  <q-list dense>
                    <q-item clickable v-close-popup @click="closeDialog">
                      <q-item-section side>
                        <q-icon name="close" size="18px" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{
                          $t('src.components.CreateDocumentDialog.cancel')
                        }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-btn-dropdown>
              </template>
            </q-input>
          </template>
        </SimpleEditor>
      </div>

      <!-- 配置面板 -->
      <q-slide-transition>
        <div
          v-show="showSettings"
          class="q-pa-md border-top"
          :class="$q.dark.isActive ? 'bg-dark' : 'bg-white'"
        >
          <div class="text-h6 q-mb-md">
            {{ $t('src.components.CreateDocumentDialog.document_config') }}
          </div>

          <!-- 切割进度显示 -->
          <div v-if="chunkingProgress" class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">
              {{ $t('src.components.CreateDocumentDialog.chunking_progress') }}
            </div>
            <q-linear-progress
              :value="(chunkingProgress.percentage || 0) / 100"
              color="primary"
              size="8px"
              class="q-mb-xs"
            />
            <div class="text-caption text-grey-6">
              {{ chunkingProgress.stage }} ({{ chunkingProgress.percentage || 0 }}%)
            </div>
          </div>

          <!-- 切割结果显示 -->
          <div v-if="chunkingResult" class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">
              {{ $t('src.components.CreateDocumentDialog.chunking_result') }}
            </div>
            <div class="text-body2">
              <div>
                {{ $t('src.components.CreateDocumentDialog.chunking_count') }}:
                {{ chunkingResult.chunkCount }}
              </div>
              <div>
                {{ $t('src.components.CreateDocumentDialog.average_chunk_size') }}:
                {{ chunkingResult.summary.averageChunkSize }}
                {{ $t('src.components.CreateDocumentDialog.characters') }}
              </div>
              <div>
                {{ $t('src.components.CreateDocumentDialog.chunking_size_range') }}:
                {{ chunkingResult.summary.minChunkSize }} -
                {{ chunkingResult.summary.maxChunkSize }}
                {{ $t('src.components.CreateDocumentDialog.characters') }}
              </div>
            </div>
          </div>

          <ChunkingStrategySelector
            v-model="chunkingStrategy"
            @validation-change="onChunkingValidationChange"
            :disabled="!!chunkingProgress"
          />
        </div>
      </q-slide-transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useQuasar } from 'quasar';
import type { ChunkingMethod, ChunkingConfig } from '../types/qwen';
import SimpleEditor from './SimpleEditor.vue';
import ChunkingStrategySelector from './ChunkingStrategySelector.vue';
import {
  splitInBackground,
  type BackgroundTask,
  type ChunkingResult,
} from '../utils/knowledgeBase';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

// Props
const props = defineProps<{
  modelValue: boolean;
  loading: boolean;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  save: [
    data: {
      title: string;
      content: string;
      chunkingMethod: ChunkingMethod;
      chunkingConfig: ChunkingConfig;
    },
  ];
  'chunking-completed': [
    data: {
      documentData: {
        title: string;
        content: string;
        chunkingMethod: ChunkingMethod;
        chunkingConfig: ChunkingConfig;
      };
      chunkingResult: ChunkingResult;
    },
  ];
  'chunking-error': [
    data: {
      documentData: {
        title: string;
        content: string;
        chunkingMethod: ChunkingMethod;
        chunkingConfig: ChunkingConfig;
      };
      error: string;
    },
  ];
}>();

// Composables
const $q = useQuasar();

// 响应式数据
const show = ref(props.modelValue);
const createDocumentTitle = ref('');
const createDocumentContent = ref('');
const createEditorRef = ref();
const showSettings = ref(false);

// 切割策略
const chunkingStrategy = ref<{ method: ChunkingMethod; config: ChunkingConfig }>({
  method: 'markdown',
  config: {
    chunkSize: 800,
    chunkOverlap: 200,
  },
});
const chunkingConfigValid = ref(true);

// 后台切割状态
const chunkingTaskId = ref<string | null>(null);
const chunkingProgress = ref<{ stage: string; percentage: number } | null>(null);
const chunkingResult = ref<ChunkingResult | null>(null);

// 方法
const closeDialog = () => {
  if (hasUnsavedChanges()) {
    $q.dialog({
      title: $t('src.components.CreateDocumentDialog.confirm_close'),
      message: $t('src.components.CreateDocumentDialog.unsaved_changes'),
      cancel: true,
      persistent: true,
    }).onOk(() => {
      resetForm();
      emit('update:modelValue', false);
    });
  } else {
    resetForm();
    emit('update:modelValue', false);
  }
};

const resetForm = () => {
  createDocumentTitle.value = '';
  createDocumentContent.value = '';
  chunkingStrategy.value = {
    method: 'markdown',
    config: {
      chunkSize: 800,
      chunkOverlap: 200,
    },
  };
  chunkingConfigValid.value = true;
  showSettings.value = false;

  // 重置后台切割状态
  chunkingTaskId.value = null;
  chunkingProgress.value = null;
  chunkingResult.value = null;
};

const hasUnsavedChanges = (): boolean => {
  return !!(createDocumentTitle.value.trim() || createDocumentContent.value.trim());
};

const validateTitle = () => {
  if (!createDocumentTitle.value.trim()) {
    $q.notify({
      type: 'warning',
      message: $t('src.components.CreateDocumentDialog.please_input_title'),
    });
    return false;
  }
  return true;
};

const onEditorReady = () => {
  console.log('创建文档编辑器已准备就绪');
};

const onChunkingValidationChange = (isValid: boolean) => {
  chunkingConfigValid.value = isValid;
};

const saveDocument = async () => {
  // 验证标题
  if (!validateTitle()) return;

  // 验证内容
  if (!createDocumentContent.value.trim()) {
    $q.notify({
      type: 'negative',
      message: $t('src.components.CreateDocumentDialog.please_input_content'),
    });
    return;
  }

  // 验证切割配置
  if (!chunkingConfigValid.value) {
    $q.notify({
      type: 'negative',
      message: $t('src.components.CreateDocumentDialog.please_check_chunking_config'),
    });
    return;
  }

  // 获取编辑器的 Markdown 内容
  const markdownContent = createEditorRef.value?.getMarkdown() || createDocumentContent.value;

  try {
    // 重置状态
    chunkingProgress.value = null;
    chunkingResult.value = null;

    // 第一步：发送创建知识库文档请求给父组件
    // 父组件会立即创建文档并返回文档ID
    const documentData = {
      title: createDocumentTitle.value.trim(),
      content: markdownContent,
      chunkingMethod: chunkingStrategy.value.method,
      chunkingConfig: chunkingStrategy.value.config,
    };

    // 发送创建文档事件给父组件
    emit('save', documentData);

    // 第二步：同时启动后台切割任务
    const taskId = await splitInBackground(
      chunkingStrategy.value.method,
      markdownContent,
      {
        chunkSize: chunkingStrategy.value.config.chunkSize || 800,
        chunkOverlap: chunkingStrategy.value.config.chunkOverlap || 200,
      },
      false, // 不启用详细日志
      {
        onProgress: (task: BackgroundTask) => {
          chunkingProgress.value = task.progress || null;
          console.log(`切割进度: ${task.progress?.stage} (${task.progress?.percentage}%)`);
        },
        onCompletion: (_task: BackgroundTask, result?: ChunkingResult) => {
          chunkingResult.value = result || null;
          chunkingProgress.value = null;

          if (result) {
            $q.notify({
              type: 'positive',
              message: $t('src.components.CreateDocumentDialog.chunking_completed', {
                count: result.chunkCount,
                averageSize: result.summary.averageChunkSize,
              }),
              timeout: 3000,
            });

            // 第三步：切割完成后，发送切割结果给父组件
            emit('chunking-completed', {
              documentData,
              chunkingResult: result,
            });
          }
        },
        onError: (_task: BackgroundTask, error: string) => {
          chunkingProgress.value = null;
          console.error($t('src.components.CreateDocumentDialog.chunking_failed'), error);
          $q.notify({
            type: 'negative',
            message: $t('src.components.CreateDocumentDialog.chunking_failed', {
              error,
            }),
            timeout: 5000,
          });

          // 通知父组件切割失败
          emit('chunking-error', {
            documentData,
            error,
          });
        },
      },
    );

    chunkingTaskId.value = taskId;

    // 显示任务提交成功的通知
    $q.notify({
      type: 'info',
      message: $t('src.components.CreateDocumentDialog.document_creating'),
      timeout: 2000,
    });
  } catch (error) {
    console.error($t('src.components.CreateDocumentDialog.submit_chunking_failed'), error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.CreateDocumentDialog.submit_chunking_failed', {
        error: error instanceof Error ? error.message : '未知错误',
      }),
    });
  }
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    show.value = newValue;
    if (newValue) {
      resetForm();
    }
  },
);

// 监听 show 变化
watch(show, (newValue) => {
  if (newValue !== props.modelValue) {
    emit('update:modelValue', newValue);
  }
});
</script>

<style scoped>
.border-top {
  border-top: 1px solid var(--q-separator-color);
}
</style>
