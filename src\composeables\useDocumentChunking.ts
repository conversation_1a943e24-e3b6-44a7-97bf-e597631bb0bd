import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useKnowledge } from './useKnowledge';
import type { ChunkingMethod, ChunkingConfig } from 'src/types/qwen';
import {
  splitInBackground,
  type BackgroundTask,
  type ChunkingResult,
} from 'src/utils/knowledgeBase';

/**
 * 文档切割和向量化处理的hook
 * 抽离自CreateDocumentDialog.vue的逻辑
 */
export function useDocumentChunking() {
  const $q = useQuasar();
  const { t: $t } = useI18n({ useScope: 'global' });
  const knowledge = useKnowledge();

  // 切割进度状态
  const chunkingProgress = ref<Map<string, { stage: string; percentage: number }>>(new Map());
  const chunkingResults = ref<Map<string, ChunkingResult>>(new Map());
  const chunkingTasks = ref<Map<string, string>>(new Map()); // documentId -> taskId

  /**
   * 为单个文档执行完整的切割和向量化流程
   * @param documentId 知识库文档ID
   * @param title 文档标题
   * @param content 文档内容（Markdown格式）
   * @param chunkingMethod 切割方法
   * @param chunkingConfig 切割配置
   * @param callbacks 回调函数
   */
  const processDocumentChunking = async (
    documentId: number,
    title: string,
    content: string,
    chunkingMethod: ChunkingMethod,
    chunkingConfig: ChunkingConfig,
    callbacks?: {
      onProgress?: (documentId: number, progress: { stage: string; percentage: number }) => void;
      onChunkingCompleted?: (documentId: number, result: ChunkingResult) => void;
      onVectorizationCompleted?: (documentId: number, chunkCount: number) => void;
      onError?: (documentId: number, error: string, stage: 'chunking' | 'vectorization') => void;
    },
  ) => {
    const docKey = documentId.toString();

    try {
      console.log('🚀 [useDocumentChunking] 开始处理文档切割和向量化:', {
        documentId,
        title,
        contentLength: content.length,
        chunkingMethod,
      });

      // 重置状态
      chunkingProgress.value.delete(docKey);
      chunkingResults.value.delete(docKey);

      // 第一步：启动后台切割任务
      const taskId = await splitInBackground(
        chunkingMethod,
        content,
        {
          chunkSize: chunkingConfig.chunkSize || 800,
          chunkOverlap: chunkingConfig.chunkOverlap || 200,
        },
        false, // 不启用详细日志
        {
          onProgress: (task: BackgroundTask) => {
            if (task.progress) {
              chunkingProgress.value.set(docKey, task.progress);
              callbacks?.onProgress?.(documentId, task.progress);
              console.log(
                `📊 [useDocumentChunking] 切割进度 ${documentId}: ${task.progress.stage} (${task.progress.percentage}%)`,
              );
            }
          },
          onCompletion: (_task: BackgroundTask, result?: ChunkingResult) => {
            chunkingProgress.value.delete(docKey);

            if (result) {
              chunkingResults.value.set(docKey, result);
              console.log('✅ [useDocumentChunking] 切割完成:', {
                documentId,
                chunkCount: result.chunkCount,
                averageSize: result.summary.averageChunkSize,
              });

              callbacks?.onChunkingCompleted?.(documentId, result);

              // 第二步：提交切割结果到Qt后端进行向量化（异步执行，不阻塞回调）
              void (async () => {
                try {
                  console.log('📤 [useDocumentChunking] 提交切割结果到Qt后端...');

                  const submitResult = await knowledge.submitChunkingResults(
                    documentId,
                    result.chunks,
                  );

                  if (submitResult.success) {
                    console.log('✅ [useDocumentChunking] 切割结果提交成功，开始向量化');

                    $q.notify({
                      type: 'positive',
                      message: $t('src.composeables.useDocumentChunking.chunking_completed', {
                        title,
                        count: result.chunkCount,
                      }),
                      timeout: 3000,
                    });
                  } else {
                    throw new Error('提交切割结果失败');
                  }
                } catch (error) {
                  console.error('❌ [useDocumentChunking] 提交切割结果失败:', error);
                  callbacks?.onError?.(
                    documentId,
                    error instanceof Error ? error.message : '提交切割结果失败',
                    'vectorization',
                  );
                }
              })();
            } else {
              console.error('❌ [useDocumentChunking] 切割结果为空');
              callbacks?.onError?.(documentId, '切割结果为空', 'chunking');
            }
          },
          onError: (_task: BackgroundTask, error: string) => {
            chunkingProgress.value.delete(docKey);
            console.error('❌ [useDocumentChunking] 切割失败:', error);

            $q.notify({
              type: 'negative',
              message: $t('src.composeables.useDocumentChunking.chunking_failed', {
                title,
                error,
              }),
              timeout: 5000,
            });

            callbacks?.onError?.(documentId, error, 'chunking');
          },
        },
      );

      chunkingTasks.value.set(docKey, taskId);
      console.log('📋 [useDocumentChunking] 切割任务已提交:', { documentId, taskId });

      return taskId;
    } catch (error) {
      console.error('❌ [useDocumentChunking] 处理文档切割失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';

      $q.notify({
        type: 'negative',
        message: $t('src.composeables.useDocumentChunking.process_failed', {
          title,
          error: errorMessage,
        }),
        timeout: 5000,
      });

      callbacks?.onError?.(documentId, errorMessage, 'chunking');
      throw error;
    }
  };

  /**
   * 设置向量化完成监听器
   * 这个方法需要在组件中调用来监听向量化完成事件
   */
  const setupVectorizationListener = (
    onVectorizationCompleted?: (documentId: number, chunkCount: number) => void,
  ) => {
    // 设置文档向量化完成监听
    const unsubscribeVectorized = knowledge.onDocumentVectorized(
      (docId: string, chunkCount: number) => {
        const documentId = parseInt(docId);
        console.log('📡 [useDocumentChunking] 收到文档向量化完成:', documentId, chunkCount);

        if (chunkCount > 0) {
          $q.notify({
            type: 'positive',
            message: $t('src.composeables.useDocumentChunking.vectorization_completed', {
              docId: documentId,
              chunkCount,
            }),
            timeout: 3000,
          });

          onVectorizationCompleted?.(documentId, chunkCount);
        } else {
          $q.notify({
            type: 'negative',
            message: $t('src.composeables.useDocumentChunking.vectorization_failed', {
              docId: documentId,
            }),
            timeout: 5000,
          });
        }

        // 清理状态
        const docKey = documentId.toString();
        chunkingProgress.value.delete(docKey);
        chunkingResults.value.delete(docKey);
        chunkingTasks.value.delete(docKey);
      },
    );

    return unsubscribeVectorized;
  };

  /**
   * 获取文档的切割进度
   */
  const getChunkingProgress = (documentId: number) => {
    return chunkingProgress.value.get(documentId.toString());
  };

  /**
   * 获取文档的切割结果
   */
  const getChunkingResult = (documentId: number) => {
    return chunkingResults.value.get(documentId.toString());
  };

  /**
   * 取消文档的切割任务
   */
  const cancelChunkingTask = (documentId: number) => {
    const docKey = documentId.toString();
    const taskId = chunkingTasks.value.get(docKey);

    if (taskId) {
      // 这里可以添加取消任务的逻辑
      console.log('🚫 [useDocumentChunking] 取消切割任务:', { documentId, taskId });
    }

    // 清理状态
    chunkingProgress.value.delete(docKey);
    chunkingResults.value.delete(docKey);
    chunkingTasks.value.delete(docKey);
  };

  /**
   * 重新向量化知识库文档
   * 删除现有的所有片段和向量，重新切割并向量化
   * @param knowledgeDocumentId 知识库文档ID
   * @param chunkingMethod 切割方法
   * @param chunkingConfig 切割配置
   * @param callbacks 回调函数
   */
  const revectorizeKnowledgeDocument = async (
    knowledgeDocumentId: number,
    chunkingMethod: ChunkingMethod,
    chunkingConfig: ChunkingConfig,
    callbacks?: {
      onProgress?: (documentId: number, progress: { stage: string; percentage: number }) => void;
      onChunkingCompleted?: (documentId: number, result: ChunkingResult) => void;
      onVectorizationCompleted?: (documentId: number, chunkCount: number) => void;
      onError?: (documentId: number, error: string, stage: 'chunking' | 'vectorization') => void;
    },
  ) => {
    try {
      console.log('🔄 [重新向量化] 开始处理知识库文档:', knowledgeDocumentId);

      // 1. 获取知识库文档信息
      console.log('📄 [重新向量化] 开始获取知识库文档信息:', knowledgeDocumentId);

      // 使用类似 callKnowledgeApi 的方式安全处理 Promise
      const callKnowledgeApiLocal = <T>(method: string, ...args: unknown[]): Promise<T> => {
        return new Promise((resolve, reject) => {
          if (!window.knowledgeApi || typeof window.knowledgeApi[method] !== 'function') {
            reject(new Error(`KnowledgeApi method ${method} not available`));
            return;
          }

          try {
            const result = window.knowledgeApi[method](...args);

            // Qt WebChannel 方法可能返回 Promise，也可能直接返回值
            const handleResult = (data: unknown) => {
              if (typeof data === 'string') {
                try {
                  const parsed = JSON.parse(data);
                  console.log('📄 [重新向量化] 解析后的文档数据:', parsed);
                  if (parsed.success) {
                    resolve(parsed.data || parsed);
                  } else {
                    reject(new Error(parsed.message || `${method} failed`));
                  }
                } catch (parseError) {
                  reject(new Error(`JSON parse error: ${parseError}`));
                }
              } else {
                resolve(data as T);
              }
            };

            // 检查结果是否为Promise
            if (result && typeof result.then === 'function') {
              result.then(handleResult).catch(reject);
            } else {
              handleResult(result);
            }
          } catch (error) {
            reject(error instanceof Error ? error : new Error(String(error)));
          }
        });
      };

      interface KnowledgeDocument {
        title?: string;
        content?: string;
        [key: string]: unknown;
      }

      const document = await callKnowledgeApiLocal<KnowledgeDocument>(
        'getDocument',
        knowledgeDocumentId.toString(),
      );
      const title = document.title || '无标题文档';
      const content = document.content || '';

      console.log('📄 [重新向量化] 获取文档信息:', content);

      if (!content.trim()) {
        throw new Error('文档内容为空，无法进行重新向量化');
      }

      // 2. 删除现有文档片段
      console.log('�️ [重新向量化] 删除现有文档片段...');
      callbacks?.onProgress?.(knowledgeDocumentId, {
        stage: '删除现有片段',
        percentage: 10,
      });

      // 调用新的 deleteDocumentChunks 方法
      const deleteResult = await callKnowledgeApiLocal<{
        deleted_chunks: number;
        total_chunks_found: number;
      }>('deleteDocumentChunks', knowledgeDocumentId.toString());

      console.log('✅ [重新向量化] 删除现有片段完成:', {
        deletedChunks: deleteResult.deleted_chunks,
        totalFound: deleteResult.total_chunks_found,
      });

      // 通知删除完成，触发统计数据更新
      callbacks?.onProgress?.(knowledgeDocumentId, {
        stage: '删除完成，准备重新切割',
        percentage: 20,
      });

      // 3. 使用传入的知识库配置
      console.log('⚙️ [重新向量化] 使用切割配置:', { chunkingMethod, chunkingConfig });

      // 4. 重新进行切割和向量化
      callbacks?.onProgress?.(knowledgeDocumentId, {
        stage: '开始重新切割文档',
        percentage: 25,
      });

      await processDocumentChunking(
        knowledgeDocumentId,
        title,
        content,
        chunkingMethod,
        chunkingConfig,
        {
          onProgress: (docId, progress) => {
            // 调整进度范围：25-100%
            const adjustedProgress = {
              ...progress,
              percentage: 25 + progress.percentage * 0.75,
            };
            callbacks?.onProgress?.(docId, adjustedProgress);
          },
          onChunkingCompleted: (docId, result) => {
            console.log('✅ [重新向量化] 文档重新切割完成:', result);
            callbacks?.onChunkingCompleted?.(docId, result);
          },
          onVectorizationCompleted: (docId, chunkCount) => {
            console.log('🎯 [重新向量化] 文档重新向量化完成:', { docId, chunkCount });
            callbacks?.onVectorizationCompleted?.(docId, chunkCount);
          },
          onError: (docId, error, stage) => {
            console.error('❌ [重新向量化] 处理失败:', { docId, error, stage });
            callbacks?.onError?.(docId, error, stage);
          },
        },
      );

      console.log('🎉 [重新向量化] 完成处理知识库文档:', knowledgeDocumentId);
    } catch (error) {
      console.error('❌ [重新向量化] 失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      callbacks?.onError?.(knowledgeDocumentId, errorMessage, 'chunking');

      throw error;
    }
  };

  /**
   * 清理所有状态
   */
  const cleanup = () => {
    chunkingProgress.value.clear();
    chunkingResults.value.clear();
    chunkingTasks.value.clear();
  };

  return {
    // 状态
    chunkingProgress,
    chunkingResults,
    chunkingTasks,

    // 方法
    processDocumentChunking,
    revectorizeKnowledgeDocument,
    setupVectorizationListener,
    getChunkingProgress,
    getChunkingResult,
    cancelChunkingTask,
    cleanup,
  };
}
