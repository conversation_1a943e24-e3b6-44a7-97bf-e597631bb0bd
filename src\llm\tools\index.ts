import { editorTools, editorToolMappings, tools as editorToolsSchema } from './editor';
import { fileTools, fileToolMappings, tools as fileToolsSchema } from './file';
import { userTools, userToolMappings, tools as userToolsSchema } from './user';
import { searchTools, searchToolMappings, tools as searchToolsSchema } from './search';
import { pexelsTools, tools as pexelsToolsSchema } from './pexels';
import {
  knowledgeBaseTools,
  knowledgeBaseToolMappings,
  tools as knowledgeBaseToolsSchema,
} from './knowledgeBase';
import { $t } from 'src/composables/useTrans';

// 导出函数对象（用于执行，保持向后兼容）
export const toolFunctions = {
  ...editorTools,
  ...fileTools,
  ...userTools,
  ...searchTools,
  ...pexelsTools,
  ...knowledgeBaseTools,
};

// 统一的工具映射表 - 这是核心改进
const allToolMappings = {
  ...editorToolMappings,
  ...fileToolMappings,
  ...userToolMappings,
  ...searchToolMappings,
  ...pexelsTools,
  ...knowledgeBaseToolMappings,
} as const;

// 导出工具schema数组（用于AI API）
export const tools = [
  ...editorToolsSchema,
  ...fileToolsSchema,
  ...userToolsSchema,
  ...searchToolsSchema,
  ...pexelsToolsSchema,
  ...knowledgeBaseToolsSchema,
];

// 统一的工具执行函数 - 精准查找和执行
export const executeToolCall = async (toolName: string, parameters: Record<string, unknown>) => {
  console.log(`🔧 [工具执行] 开始执行工具: ${toolName}`, parameters);

  // 直接从映射表查找工具函数
  const toolFunction = allToolMappings[toolName as keyof typeof allToolMappings];

  if (toolFunction) {
    try {
      // 执行工具函数（支持同步和异步）
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const result = await toolFunction(parameters as any);

      console.log(`✅ [工具执行] ${toolName} 执行成功:`, result);
      const res = (result) => {
        if (result.success && result.message) {
          return result.message;
        } else {
          return $t('src.composeables.useTools.success');
        }
      };
      return res(result);
    } catch (error) {
      console.error(`❌ [工具执行] ${toolName} 执行失败:`, error);
      return {
        success: false,
        message: `工具 ${toolName} 执行失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  console.error(`❌ [工具执行] 未知的工具: ${toolName}`);
  return {
    success: false,
    message: `未知的工具: ${toolName}`,
  };
};
