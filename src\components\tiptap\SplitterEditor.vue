<template>
  <div
    class="splitter-container absolute-full"
    ref="containerRef"
    :class="{ dragging: editorState.isDragging }"
  >
    <div class="fit column no-wrap">
      <!-- 只在 editor 存在时渲染工具栏 -->
      <EditorToolbar
        v-if="shouldShowToolbar"
        :key="toolbarKey"
        :docId="editorState.docId"
        :catalogVisible="catalogVisible"
        toolbarType="classic"
        class="border-bottom q-px-xs"
      />
      <div class="row no-wrap q-space">
        <template v-for="(pane, index) in visiblePanes" :key="pane.id">
          <SplitterPane
            v-if="pane.documents.length > 0"
            :pane="pane"
            :paneIndex="index"
            :isActive="isActivePane(pane)"
            @ready="onReady"
            @removeDoc="removeDoc"
            @documentChange="onDocumentChange"
            @paneClick="activePane"
          />
          <SplitterHandle
            v-if="shouldShowHandle(index)"
            :index="index"
            :containerRef="containerRef"
            @dragStart="handleDragStart"
            @dragEnd="handleDragEnd"
            @dragUpdate="handleDragUpdate"
          />
        </template>
      </div>

      <!-- PowerEdge 全局面板 -->
      <PowerEdge
        v-if="shouldShowPowerEdge"
        class="border-top"
        :docId="editorState.docId"
        :activePaneIndex="editorState.activePaneIndex"
        :wordCount="debouncedWordCount"
      >
        <template #right>
          <div class="row no-wrap items-center q-pa-xs">
            <span class="q-px-sm">字数: {{ wordCount }}</span>
          </div>
        </template>
      </PowerEdge>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, onMounted, computed, watch, reactive, nextTick } from 'vue';
import type { Document } from 'src/types/doc';
import type { SplitterWindow } from 'src/types/splitterWindow';

import EditorToolbar from './EditorToolbar.vue';
import SplitterPane from './SplitterPane.vue';
import SplitterHandle from './SplitterHandle.vue';
import PowerEdge from './PowerEdge.vue';
import { initCatalogState, getCatalogState } from './useCommands';
import { useVirtualDragSystem } from 'src/composables/VirtualDragSystem';
import { useResourceCleanup } from 'src/composables/useResourceCleanup';
import { useMemoryManager } from 'src/composables/MemoryManager';
import { useGlobalLoading } from 'src/composables/useLoadingState';
import { useGlobalErrorHandler } from 'src/composables/useErrorHandler';
import { useGlobalPreloader } from 'src/composables/useComponentPreloader';
import { countWords } from 'src/utils/wordCount';
import type { DragUpdateContext } from 'src/composables/VirtualDragSystem';
import { isDevelopment } from 'src/config/performance.config';
import { useUiStore } from 'src/stores/ui';
import { useDocStore } from 'src/stores/doc';
const docStore = useDocStore();
const uiStore = useUiStore();

// TypeScript 接口定义
interface EditorState {
  docId: number | null;
  activePaneIndex: number;
  isDragging: boolean;
  currentInstanceKey: string | null;
}

interface ComponentRefs {
  containerRef: HTMLElement | null;
}

// 初始化资源管理和清理系统
const resourceCleanup = useResourceCleanup('editor', 'SplitterEditor');
// 使用默认配置，不需要再单独配置阈值
const memoryManager = useMemoryManager();

// 集成功能模块
const loadingState = useGlobalLoading();
const errorHandler = useGlobalErrorHandler();
const preloader = useGlobalPreloader();

// 虚拟拖拽系统
const { isDragging } = useVirtualDragSystem({
  throttleMs: 8,
  enableRaf: true,
  enableVelocityTracking: true,
  maxVelocity: 3000,
});

// 组件引用
const refs: ComponentRefs = reactive({
  containerRef: null,
});

// 主状态聚合
const editorState: EditorState = reactive({
  docId: null,
  activePaneIndex: 0,
  isDragging: false,
  currentInstanceKey: null,
});

// 监听虚拟拖拽系统状态
watch(isDragging, (newValue) => {
  editorState.isDragging = newValue;
});

// 计算当前实例键
const updateCurrentInstanceKey = (): void => {
  if (
    editorState.docId === null ||
    editorState.activePaneIndex >= docStore.splitterWindows.length
  ) {
    editorState.currentInstanceKey = null;
    return;
  }
  const activeWindow = docStore.splitterWindows[editorState.activePaneIndex];
  editorState.currentInstanceKey = docStore.generateEditorInstanceKey(
    activeWindow.id,
    editorState.docId,
  );
};

// 监听状态变化自动更新实例键
watch(
  [
    () => editorState.docId,
    () => editorState.activePaneIndex,
    () => docStore.splitterWindows.length,
  ],
  updateCurrentInstanceKey,
  { immediate: true },
);

// 组件引用的getter（用于向后兼容）
const containerRef = computed({
  get: () => refs.containerRef,
  set: (value: HTMLElement | null) => {
    refs.containerRef = value;
  },
});

// 字数统计 - 防抖优化的计算属性
const wordCount = computed(() => {
  if (!editorState.docId || editorState.activePaneIndex >= docStore.splitterWindows.length) {
    return 0;
  }

  const activeWindow = docStore.splitterWindows[editorState.activePaneIndex];
  if (!activeWindow) return 0;

  const editorInstance = docStore.getEditorInstance(activeWindow.id, editorState.docId);
  if (!editorInstance) return 0;

  try {
    const text = editorInstance.getText();
    return countWords(text);
  } catch (error) {
    console.warn('字数统计失败:', error);
    return 0;
  }
});

// 防抖的字数统计（用于性能优化）
const debouncedWordCount = ref(0);

watch(wordCount, () => {
  resourceCleanup.setTimeout(() => {
    debouncedWordCount.value = wordCount.value;
  }, 300);
});

// 创建优化的拖拽控制器
const handleDragStart = (index: number): void => {
  if (!refs.containerRef) return;

  try {
    const leftPane = docStore.splitterWindows[index];
    const rightPane = docStore.splitterWindows[index + 1];

    if (!leftPane || !rightPane) {
      throw new Error(`Invalid pane configuration at index ${index}`);
    }

    // 通过虚拟拖拽系统处理，自动优化性能
    console.log(`🎯 [SplitterEditor] 开始拖拽: ${index}`);
  } catch (error) {
    errorHandler.captureError(error as Error, 'drag-operation', {
      userFriendlyMessage: '拖拽操作初始化失败',
      severity: 'medium',
    });
  }
};

const handleDragUpdate = (clientX: number): void => {
  if (!refs.containerRef || !editorState.isDragging) return;

  // 使用批处理更新以减少DOM操作
  const batchUpdate = () => {
    const containerRect = refs.containerRef?.getBoundingClientRect();
    if (!containerRect) return;

    const relativeX = clientX - containerRect.left;
    const containerWidth = containerRect.width;

    // 边界检查
    if (relativeX < 0 || relativeX > containerWidth) return;

    // 这里可以添加具体的面板大小更新逻辑
    // 现在由虚拟拖拽系统自动处理
  };

  // 使用资源清理系统管理的RAF
  resourceCleanup.requestAnimationFrame(batchUpdate);
};

const handleDragEnd = (): void => {
  console.log('🎯 [SplitterEditor] 拖拽结束');
  // 拖拽结束后的清理工作由虚拟拖拽系统自动处理
};

// 优化的计算属性
const shouldShowToolbar = computed(
  () => (docStore.splitterWindows?.length ?? 0) > 0 && uiStore.perferences.editor.enableToolbar,
);

const shouldShowPowerEdge = computed(
  () => (docStore.splitterWindows?.length ?? 0) > 0 && editorState.docId !== null,
);

const visiblePanes = computed(
  () => docStore.splitterWindows?.filter((pane) => pane.documents.length > 0) ?? [],
);

const toolbarKey = computed(() => `toolbar_${editorState.docId}_${editorState.activePaneIndex}`);

const catalogVisible = computed(() =>
  editorState.currentInstanceKey ? getCatalogState(editorState.currentInstanceKey) : false,
);

// 工具函数
const isActivePane = (pane: SplitterWindow): boolean =>
  pane.id === docStore.activeSplitterWindowId && docStore.splitterWindows.length > 1;

const shouldShowHandle = (index: number): boolean => index < docStore.splitterWindows.length - 1;

// 初始化预加载组件（文件树由 DocManager 负责加载）
onMounted(async () => {
  try {
    loadingState.startLoading('splitter-init', {
      text: '初始化编辑器...',
      showProgress: true,
    });

    await preloader.preloadEditorComponents();
    loadingState.updateProgress('splitter-init', 100, '初始化完成');

    loadingState.stopLoading('splitter-init');
  } catch (error) {
    console.error('❌ [SplitterEditor] 编辑器初始化失败:', error);
    errorHandler.captureError(error as Error, 'splitter-init', {
      userFriendlyMessage: '编辑器初始化失败，请检查网络连接',
      severity: 'high',
      autoRecover: false, // 禁用自动恢复，避免无限刷新
    });
    loadingState.stopLoading('splitter-init', '初始化失败');
  }
});

// 事件处理器 - 使用批处理优化
const onReady = (paneId: number, _docId: number): void => {
  const paneIndex = docStore.splitterWindows.findIndex((pane) => pane.id === paneId);
  if (paneIndex !== -1) {
    editorState.activePaneIndex = paneIndex;
    editorState.docId = _docId;
  }
};

const removeDoc = (leftDocId: number): void => {
  editorState.docId = leftDocId;
};

const onDocumentChange = (newDocId: number): void => {
  editorState.docId = newDocId;
};

const activePane = (index: number): void => {
  if (index !== editorState.activePaneIndex) {
    editorState.activePaneIndex = index;
  }
};

// 监听活动面板变化，更新目录状态
watch(
  () => editorState.currentInstanceKey,
  (newInstanceKey) => {
    if (newInstanceKey) {
      // 使用资源清理系统管理的nextTick
      void nextTick(() => {
        initCatalogState(newInstanceKey);
      });
    }
  },
  { flush: 'post' },
);

// 监听虚拟拖拽更新事件
resourceCleanup.addEventListener(window, 'virtualDragUpdate', (event: CustomEvent) => {
  const context = event.detail as DragUpdateContext;
  handleDragUpdate(context.position.x);
});

// 清理和组件卸载
onUnmounted(() => {
  try {
    // 清理所有编辑器的目录状态
    docStore.splitterWindows.forEach((pane) => {
      pane.documents.forEach((doc: Document) => {
        docStore.generateEditorInstanceKey(pane.id, doc.id);
        // 目录状态清理将在 useCommands 中处理
      });
    });

    docStore.destroyEditorInstances();

    // 恢复样式（防止意外情况）
    document.body.style.userSelect = '';
    document.body.style.cursor = '';

    // 输出内存使用统计
    if (isDevelopment) {
      console.log('📊 [SplitterEditor] 内存使用统计:', memoryManager.stats.value);
    }
  } catch (error) {
    console.error('组件清理失败:', error);
  }
});

// 资源清理系统会自动处理所有资源的清理
// 包括事件监听器、定时器、观察器等
</script>

<style scoped>
.splitter-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化容器性能 */
  contain: layout style paint;
}

/* 拖拽时的优化样式 */
.splitter-container.dragging {
  /* 禁用指针事件，提升性能 */
  pointer-events: none;
  /* 提升GPU加速优先级 */
  transform: translateZ(0);
}

.splitter-container.dragging :deep(.split-pane) {
  /* 拖拽时禁用过渡动画 */
  transition: none !important;
  /* 优化重绘性能 */
  will-change: width, flex;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化渲染层 */
  contain: layout style;
}

.splitter-container.dragging :deep(.splitter-handle) {
  /* 拖拽时保持指针事件 */
  pointer-events: auto;
  /* 提升渲染优先级 */
  will-change: transform;
}
</style>
