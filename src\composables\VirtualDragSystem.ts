import { ref, reactive, onUnmounted } from 'vue';
import { useResourceCleanup } from './useResourceCleanup';
import { DRAG_SYSTEM } from '../config/performance.config';

// Drag event handlers
export interface DragHandlers {
  onDragStart?: (event: DragEvent) => void;
  onDragMove?: (event: DragEvent) => void;
  onDragEnd?: (event: DragEvent) => void;
}

// Drag event with extended properties
export interface DragEvent {
  clientX: number;
  clientY: number;
  deltaX: number;
  deltaY: number;
  velocityX: number;
  velocityY: number;
  timestamp: number;
}

// Drag system configuration
export interface DragSystemConfig {
  throttleDelay: number;
  smoothing: boolean;
  prediction: boolean;
  enableMomentum: boolean;
  momentumDecay: number;
  throttleMs?: number;
  enableRaf?: boolean;
  enableVelocityTracking?: boolean;
  maxVelocity?: number;
}

// Drag update context interface
export interface DragUpdateContext {
  handleIndex: number;
  position: {
    x: number;
    y: number;
  };
  velocity: {
    x: number;
    y: number;
  };
  timestamp: number;
}

// Start drag options
export interface StartDragOptions {
  handleIndex: number;
  initialPosition: {
    x: number;
    y: number;
    timestamp: number;
  };
  containerRect: DOMRect;
  targetElements: HTMLElement[];
  onUpdate: (context: DragUpdateContext) => void;
  onEnd: () => void;
}

/**
 * Virtual Drag System
 * Optimized drag handling with smoothing and prediction
 */
export class VirtualDragSystem {
  private static instance: VirtualDragSystem | null = null;
  private config: DragSystemConfig;
  private activeHandles = new Map<HTMLElement, DragHandlers>();
  private dragState = reactive({
    isDragging: false,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    lastX: 0,
    lastY: 0,
    velocityX: 0,
    velocityY: 0,
    lastTimestamp: 0,
  });
  private resourceCleanup: ReturnType<typeof useResourceCleanup>;
  private rafId: number | null = null;
  private throttleTimer: number | null = null;

  private constructor(config: Partial<DragSystemConfig> = {}) {
    this.config = {
      throttleDelay: DRAG_SYSTEM.throttleDelay,
      smoothing: DRAG_SYSTEM.smoothing,
      prediction: DRAG_SYSTEM.prediction,
      enableMomentum: DRAG_SYSTEM.enableMomentum,
      momentumDecay: DRAG_SYSTEM.momentumDecay,
      ...config,
    };
    this.resourceCleanup = useResourceCleanup();
    this.setupGlobalListeners();
  }

  static getInstance(): VirtualDragSystem {
    if (!VirtualDragSystem.instance) {
      VirtualDragSystem.instance = new VirtualDragSystem();
    }
    return VirtualDragSystem.instance;
  }

  /**
   * Register a drag handle
   */
  registerHandle(element: HTMLElement, handlers: DragHandlers): () => void {
    this.activeHandles.set(element, handlers);

    const handleMouseDown = (e: MouseEvent) => {
      this.startDrag(e, handlers);
    };

    element.addEventListener('mousedown', handleMouseDown);

    // Return cleanup function
    return () => {
      element.removeEventListener('mousedown', handleMouseDown);
      this.activeHandles.delete(element);
    };
  }

  /**
   * Set throttle delay
   */
  setThrottleDelay(delay: number): void {
    this.config.throttleDelay = delay;
  }

  /**
   * Enable/disable smoothing
   */
  setSmoothing(enabled: boolean): void {
    this.config.smoothing = enabled;
  }

  /**
   * Enable/disable prediction
   */
  setPrediction(enabled: boolean): void {
    this.config.prediction = enabled;
  }

  /**
   * Start drag operation
   */
  private startDrag(e: MouseEvent, handlers: DragHandlers): void {
    e.preventDefault();

    this.dragState.isDragging = true;
    this.dragState.startX = e.clientX;
    this.dragState.startY = e.clientY;
    this.dragState.currentX = e.clientX;
    this.dragState.currentY = e.clientY;
    this.dragState.lastX = e.clientX;
    this.dragState.lastY = e.clientY;
    this.dragState.velocityX = 0;
    this.dragState.velocityY = 0;
    this.dragState.lastTimestamp = performance.now();

    const dragEvent: DragEvent = {
      clientX: e.clientX,
      clientY: e.clientY,
      deltaX: 0,
      deltaY: 0,
      velocityX: 0,
      velocityY: 0,
      timestamp: this.dragState.lastTimestamp,
    };

    handlers.onDragStart?.(dragEvent);

    // Add temporary global listeners
    const handleMouseMove = (e: MouseEvent) => this.handleDrag(e, handlers);
    const handleMouseUp = (e: MouseEvent) => this.endDrag(e, handlers);

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Store cleanup function
    this.resourceCleanup.addResource('drag-listeners', {
      cleanup: () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      },
      priority: 'high',
      type: 'event',
    });
  }

  /**
   * Handle drag movement
   */
  private handleDrag(e: MouseEvent, handlers: DragHandlers): void {
    if (!this.dragState.isDragging) return;

    const currentTime = performance.now();
    const deltaTime = currentTime - this.dragState.lastTimestamp;

    // Calculate velocity
    if (deltaTime > 0) {
      this.dragState.velocityX = ((e.clientX - this.dragState.lastX) / deltaTime) * 1000;
      this.dragState.velocityY = ((e.clientY - this.dragState.lastY) / deltaTime) * 1000;
    }

    // Update position
    this.dragState.currentX = e.clientX;
    this.dragState.currentY = e.clientY;

    // Apply smoothing if enabled
    let targetX = e.clientX;
    let targetY = e.clientY;

    if (this.config.smoothing) {
      const smoothingFactor = 0.2;
      targetX = this.dragState.lastX + (targetX - this.dragState.lastX) * smoothingFactor;
      targetY = this.dragState.lastY + (targetY - this.dragState.lastY) * smoothingFactor;
    }

    // Apply prediction if enabled
    if (this.config.prediction) {
      const predictionTime = 16; // Predict 16ms ahead
      targetX += (this.dragState.velocityX * predictionTime) / 1000;
      targetY += (this.dragState.velocityY * predictionTime) / 1000;
    }

    const dragEvent: DragEvent = {
      clientX: targetX,
      clientY: targetY,
      deltaX: targetX - this.dragState.startX,
      deltaY: targetY - this.dragState.startY,
      velocityX: this.dragState.velocityX,
      velocityY: this.dragState.velocityY,
      timestamp: currentTime,
    };

    // Throttle drag events if configured
    if (this.config.throttleDelay > 0) {
      if (!this.throttleTimer) {
        handlers.onDragMove?.(dragEvent);
        this.throttleTimer = window.setTimeout(() => {
          this.throttleTimer = null;
        }, this.config.throttleDelay);
      }
    } else {
      handlers.onDragMove?.(dragEvent);
    }

    // Update last position
    this.dragState.lastX = e.clientX;
    this.dragState.lastY = e.clientY;
    this.dragState.lastTimestamp = currentTime;
  }

  /**
   * End drag operation
   */
  private endDrag(e: MouseEvent, handlers: DragHandlers): void {
    if (!this.dragState.isDragging) return;

    this.dragState.isDragging = false;

    const dragEvent: DragEvent = {
      clientX: e.clientX,
      clientY: e.clientY,
      deltaX: e.clientX - this.dragState.startX,
      deltaY: e.clientY - this.dragState.startY,
      velocityX: this.dragState.velocityX,
      velocityY: this.dragState.velocityY,
      timestamp: performance.now(),
    };

    handlers.onDragEnd?.(dragEvent);

    // Apply momentum if enabled
    if (
      this.config.enableMomentum &&
      (Math.abs(this.dragState.velocityX) > 50 || Math.abs(this.dragState.velocityY) > 50)
    ) {
      this.applyMomentum(handlers);
    }

    // Cleanup listeners
    this.resourceCleanup.cleanupByTag('drag-listeners');

    // Clear throttle timer
    if (this.throttleTimer) {
      clearTimeout(this.throttleTimer);
      this.throttleTimer = null;
    }
  }

  /**
   * Apply momentum after drag
   */
  private applyMomentum(handlers: DragHandlers): void {
    let velocityX = this.dragState.velocityX;
    let velocityY = this.dragState.velocityY;
    let positionX = this.dragState.currentX;
    let positionY = this.dragState.currentY;

    const animate = () => {
      // Apply decay
      velocityX *= this.config.momentumDecay;
      velocityY *= this.config.momentumDecay;

      // Update position
      positionX += velocityX * 0.016; // 16ms frame time
      positionY += velocityY * 0.016;

      const dragEvent: DragEvent = {
        clientX: positionX,
        clientY: positionY,
        deltaX: positionX - this.dragState.startX,
        deltaY: positionY - this.dragState.startY,
        velocityX,
        velocityY,
        timestamp: performance.now(),
      };

      handlers.onDragMove?.(dragEvent);

      // Continue if velocity is significant
      if (Math.abs(velocityX) > 1 || Math.abs(velocityY) > 1) {
        this.rafId = requestAnimationFrame(animate);
      } else {
        // Final end event
        handlers.onDragEnd?.(dragEvent);
      }
    };

    this.rafId = requestAnimationFrame(animate);
  }

  /**
   * Setup global listeners
   */
  private setupGlobalListeners(): void {
    // Prevent text selection during drag
    document.addEventListener('selectstart', (e) => {
      if (this.dragState.isDragging) {
        e.preventDefault();
      }
    });
  }

  /**
   * Get current drag state
   */
  getDragState() {
    return { ...this.dragState };
  }

  /**
   * Destroy the drag system
   */
  destroy(): void {
    // Cancel any active animations
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }

    // Clear throttle timer
    if (this.throttleTimer) {
      clearTimeout(this.throttleTimer);
      this.throttleTimer = null;
    }

    // Clear all handles
    this.activeHandles.clear();

    // Cleanup resources
    this.resourceCleanup.cleanupAll();

    VirtualDragSystem.instance = null;
  }
}

/**
 * Composable for using the virtual drag system
 */
export function useVirtualDragSystem(config?: Partial<DragSystemConfig>) {
  const dragSystem = VirtualDragSystem.getInstance();
  const isDragging = ref(dragSystem.getDragState().isDragging);

  // Apply config if provided
  if (config) {
    if (config.throttleMs !== undefined) {
      dragSystem.setThrottleDelay(config.throttleMs);
    }
    if (config.smoothing !== undefined) {
      dragSystem.setSmoothing(config.smoothing);
    }
    if (config.prediction !== undefined) {
      dragSystem.setPrediction(config.prediction);
    }
  }

  const registerHandle = (element: HTMLElement, handlers: DragHandlers) => {
    return dragSystem.registerHandle(element, handlers);
  };

  const setThrottleDelay = (delay: number) => {
    dragSystem.setThrottleDelay(delay);
  };

  const setSmoothing = (enabled: boolean) => {
    dragSystem.setSmoothing(enabled);
  };

  const setPrediction = (enabled: boolean) => {
    dragSystem.setPrediction(enabled);
  };

  const getDragState = () => {
    return dragSystem.getDragState();
  };

  const startDrag = (options: StartDragOptions) => {
    // Emit virtual drag update events
    const cleanup = () => {
      // Cleanup logic
    };

    // Create drag context and emit events
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const context: DragUpdateContext = {
      handleIndex: options.handleIndex,
      position: {
        x: options.initialPosition.x,
        y: options.initialPosition.y,
      },
      velocity: { x: 0, y: 0 },
      timestamp: options.initialPosition.timestamp,
    };

    // Set up drag handling
    const handleMove = (e: MouseEvent) => {
      const newContext: DragUpdateContext = {
        handleIndex: options.handleIndex,
        position: { x: e.clientX, y: e.clientY },
        velocity: { x: 0, y: 0 },
        timestamp: performance.now(),
      };
      options.onUpdate(newContext);

      // Emit custom event
      window.dispatchEvent(new CustomEvent('virtualDragUpdate', { detail: newContext }));
    };

    const handleEnd = () => {
      document.removeEventListener('mousemove', handleMove);
      document.removeEventListener('mouseup', handleEnd);
      options.onEnd();
    };

    document.addEventListener('mousemove', handleMove);
    document.addEventListener('mouseup', handleEnd);

    return cleanup;
  };

  // Update isDragging state
  const updateInterval = setInterval(() => {
    isDragging.value = dragSystem.getDragState().isDragging;
  }, 16);

  onUnmounted(() => {
    clearInterval(updateInterval);
    // Don't destroy singleton on component unmount
    // Just cleanup any component-specific resources if needed
  });

  return {
    dragSystem: {
      startDrag,
    },
    isDragging,
    registerHandle,
    setThrottleDelay,
    setSmoothing,
    setPrediction,
    getDragState,
  };
}
