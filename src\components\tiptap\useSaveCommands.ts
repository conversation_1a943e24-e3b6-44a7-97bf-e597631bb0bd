import type { Editor } from '@tiptap/vue-3';
import { ref } from 'vue';
// import { useDocumentSave } from 'src/composeables/useDocumentSave'; // 暂时注释以解决编译错误

/**
 * 文档保存命令模块
 * 负责文档的自动保存、手动保存、保存状态管理等功能
 */

// 保存状态类型
export type SaveStatus = 'saved' | 'saving' | 'pending' | 'error';

// 保存状态管理 - 使用 Map 存储每个文档的保存状态
export const saveStatusMap = ref<Map<number, SaveStatus>>(new Map());
export const lastSaveTimeMap = ref<Map<number, number>>(new Map());
export const saveTimerMap = ref<Map<number, NodeJS.Timeout | null>>(new Map());

// 保存策略配置
const DEBOUNCE_DELAY = 2000; // 2秒防抖延迟
const MAX_UNSAVED_TIME = 10000; // 最长10秒必须保存一次

/**
 * 获取文档保存状态
 */
export const getSaveStatus = (docId: number): SaveStatus => {
  return saveStatusMap.value.get(docId) ?? 'saved';
};

/**
 * 设置文档保存状态
 */
export const setSaveStatus = (docId: number, status: SaveStatus) => {
  saveStatusMap.value.set(docId, status);
};

/**
 * 清理文档保存相关数据
 */
export const cleanupSaveData = (docId: number) => {
  const timer = saveTimerMap.value.get(docId);
  if (timer) {
    clearTimeout(timer);
    saveTimerMap.value.delete(docId);
  }
  saveStatusMap.value.delete(docId);
  lastSaveTimeMap.value.delete(docId);
};

/**
 * 智能写入文档到数据库
 * 包含防抖和强制保存逻辑
 */
export const createSmartSaveFunction = (
  currentEditor: () => Editor | null,
  docId: () => number,
  getDocTitle?: () => string,
  getFolderId?: () => number
) => {
  // 使用保存相关函数的正确解构方式
  // const { queueSave, isSaving } = useDocumentSave(); // 暂时注释以解决编译错误
  
  // 模拟saveDocToDb函数
  const saveDocToDb = (docId: number, title: string, content: unknown, folderId: number) => {
    // 这里应该调用实际的保存API
    console.log('保存文档:', { docId, title, content, folderId });
    return Promise.resolve({ success: true });
  };

  /**
   * 立即保存文档
   */
  const immediatelySave = async (title?: string, folderId?: number) => {
    const editor = currentEditor();
    const currentDocId = docId();
    
    if (!editor || !currentDocId) {
      console.warn('编辑器或文档ID不可用，跳过保存');
      return;
    }

    console.log(`立即保存文档 ${currentDocId}`);
    setSaveStatus(currentDocId, 'saving');

    try {
      const content = editor.getJSON();
      const finalTitle = title || getDocTitle?.() || '未命名文档';
      const finalFolderId = folderId ?? getFolderId?.() ?? -1;

      const result = await saveDocToDb(currentDocId, finalTitle, content, finalFolderId);
      
      if (result.success) {
        setSaveStatus(currentDocId, 'saved');
        lastSaveTimeMap.value.set(currentDocId, Date.now());
        console.log(`文档 ${currentDocId} 保存成功`);
      } else {
        setSaveStatus(currentDocId, 'error');
        console.error(`文档 ${currentDocId} 保存失败:`, '保存失败');
      }
    } catch (error) {
      setSaveStatus(currentDocId, 'error');
      console.error(`保存文档 ${currentDocId} 时发生错误:`, error);
    }
  };

  /**
   * 智能写入（带防抖）
   */
  const smartWriteDocToDb = (title?: string, folderId?: number) => {
    const currentDocId = docId();
    
    if (!currentDocId) {
      console.warn('文档ID不可用，跳过智能保存');
      return;
    }

    console.log(`智能保存文档 ${currentDocId}`);
    setSaveStatus(currentDocId, 'pending');

    // 清除现有的定时器
    const existingTimer = saveTimerMap.value.get(currentDocId);
    if (existingTimer) {
      clearTimeout(existingTimer);
      saveTimerMap.value.delete(currentDocId);
    }

    const lastSaveTime = lastSaveTimeMap.value.get(currentDocId) ?? 0;
    const timeSinceLastSave = Date.now() - lastSaveTime;

    // 如果超过最大未保存时间，立即保存
    if (timeSinceLastSave >= MAX_UNSAVED_TIME) {
      void immediatelySave(title, folderId);
    } else {
      // 否则使用防抖
      const timer = setTimeout(() => {
        void immediatelySave(title, folderId);
      }, DEBOUNCE_DELAY);
      saveTimerMap.value.set(currentDocId, timer);
    }
  };

  /**
   * 强制保存所有待保存的文档
   */
  const forceFlushPendingSaves = async () => {
    const pendingDocs: number[] = [];
    
    // 找出所有待保存的文档
    for (const [docId, status] of saveStatusMap.value.entries()) {
      if (status === 'pending') {
        pendingDocs.push(docId);
      }
    }

    // 强制保存所有待保存的文档
    const savePromises = pendingDocs.map(async (pendingDocId) => {
      const timer = saveTimerMap.value.get(pendingDocId);
      if (timer) {
        clearTimeout(timer);
        saveTimerMap.value.delete(pendingDocId);
      }
      
      // 如果是当前文档，使用当前编辑器保存
      if (pendingDocId === docId()) {
        await immediatelySave();
      }
    });

    await Promise.all(savePromises);
  };

  /**
   * 检查是否有未保存的更改
   */
  const hasUnsavedChanges = (targetDocId?: number): boolean => {
    const checkDocId = targetDocId ?? docId();
    if (!checkDocId) return false;
    
    const status = getSaveStatus(checkDocId);
    return status === 'pending' || status === 'saving';
  };

  /**
   * 获取保存统计信息
   */
  const getSaveStats = () => {
    const stats = {
      total: saveStatusMap.value.size,
      saved: 0,
      saving: 0,
      pending: 0,
      error: 0,
    };

    for (const status of saveStatusMap.value.values()) {
      stats[status]++;
    }

    return stats;
  };

  /**
   * 清理所有保存相关数据
   */
  const cleanupAllSaveData = () => {
    // 清理所有定时器
    for (const timer of saveTimerMap.value.values()) {
      if (timer) {
        clearTimeout(timer);
      }
    }
    
    saveStatusMap.value.clear();
    lastSaveTimeMap.value.clear();
    saveTimerMap.value.clear();
  };

  /**
   * 设置保存策略配置
   */
  const setSaveConfig = (config: {
    debounceDelay?: number;
    maxUnsavedTime?: number;
  }) => {
    if (config.debounceDelay !== undefined) {
      // 动态修改防抖延迟（仅影响新的保存操作）
      console.log(`保存防抖延迟更新为: ${config.debounceDelay}ms`);
    }
    if (config.maxUnsavedTime !== undefined) {
      // 动态修改最大未保存时间
      console.log(`最大未保存时间更新为: ${config.maxUnsavedTime}ms`);
    }
  };

  return {
    immediatelySave,
    smartWriteDocToDb,
    forceFlushPendingSaves,
    hasUnsavedChanges,
    getSaveStats,
    cleanupAllSaveData,
    setSaveConfig,
    // 保存状态相关
    getSaveStatus,
    setSaveStatus,
    cleanupSaveData,
    saveStatusMap,
    lastSaveTimeMap,
    saveTimerMap,
  };
};

/**
 * 创建全局保存管理器
 */
export const createGlobalSaveManager = () => {
  const activeSaveFunctions = new Map<number, ReturnType<typeof createSmartSaveFunction>>();

  /**
   * 注册文档的保存函数
   */
  const registerDocumentSave = (
    docId: number,
    saveFunction: ReturnType<typeof createSmartSaveFunction>
  ) => {
    activeSaveFunctions.set(docId, saveFunction);
  };

  /**
   * 取消注册文档的保存函数
   */
  const unregisterDocumentSave = (docId: number) => {
    const saveFunction = activeSaveFunctions.get(docId);
    if (saveFunction) {
      saveFunction.cleanupSaveData(docId);
      activeSaveFunctions.delete(docId);
    }
  };

  /**
   * 保存所有文档
   */
  const saveAllDocuments = async () => {
    console.log(`开始保存所有 ${activeSaveFunctions.size} 个文档`);
    
    const savePromises = Array.from(activeSaveFunctions.values()).map(saveFunction => 
      saveFunction.forceFlushPendingSaves()
    );

    await Promise.all(savePromises);
    console.log('所有文档保存完成');
  };

  /**
   * 获取全局保存统计
   */
  const getGlobalSaveStats = () => {
    const globalStats = {
      totalDocuments: activeSaveFunctions.size,
      totalSaved: 0,
      totalSaving: 0,
      totalPending: 0,
      totalError: 0,
    };

    for (const saveFunction of activeSaveFunctions.values()) {
      const stats = saveFunction.getSaveStats();
      globalStats.totalSaved += stats.saved;
      globalStats.totalSaving += stats.saving;
      globalStats.totalPending += stats.pending;
      globalStats.totalError += stats.error;
    }

    return globalStats;
  };

  /**
   * 清理所有文档的保存数据
   */
  const cleanupAllDocuments = () => {
    for (const saveFunction of activeSaveFunctions.values()) {
      saveFunction.cleanupAllSaveData();
    }
    activeSaveFunctions.clear();
  };

  return {
    registerDocumentSave,
    unregisterDocumentSave,
    saveAllDocuments,
    getGlobalSaveStats,
    cleanupAllDocuments,
  };
};

// 全局保存管理器单例
export const globalSaveManager = createGlobalSaveManager();

export default {
  // SaveStatus, // 类型不应该作为值导出
  saveStatusMap,
  lastSaveTimeMap,
  saveTimerMap,
  getSaveStatus,
  setSaveStatus,
  cleanupSaveData,
  createSmartSaveFunction,
  createGlobalSaveManager,
  globalSaveManager,
};