<template>
  <div
    class="absolute-full row no-wrap"
    :class="$q.dark.isActive ? 'bg-grey-10' : 'bg-white text-grey-9'"
  >
    <div class="main-navigation border-right"><AppNavbar /></div>
    <div class="q-space relative-position">
      <router-view v-if="$ink.isQtApp" />
      <div v-else class="absolute-full column flex-center">
        {{ $t('src.layout.MainLayout.pleaseUseDesktop') }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AppNavbar from 'components/AppNavbar.vue';
import { useQuasar } from 'quasar';
import { $ink } from 'src/boot/qt-integration';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

const $q = useQuasar();
</script>

<style scoped>
.main-navigation {
  flex: 0 0 58px;
}
</style>
