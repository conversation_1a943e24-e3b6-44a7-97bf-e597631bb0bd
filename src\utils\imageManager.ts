/**
 * 新的图片处理系统
 * 重构版本：统一图片处理、引用计数、智能删除
 */

import { useSqlite } from 'src/composeables/useSqlite';
import { $t } from 'src/composables/useTrans';

// 图片处理结果接口
export interface ImageProcessResult {
  success: boolean;
  imageId?: number;
  localSrc?: string;
  error?: string;
  isDuplicate?: boolean;
  width?: number;
  height?: number;
  fileSize?: number;
}

// 图片引用信息接口
export interface ImageReference {
  documentId: number;
  documentTitle: string;
}

// 图片信息接口
export interface ImageInfo {
  id: number;
  filePath: string;
  originalUrl?: string;
  mimeType: string;
  fileSize: number;
  width: number;
  height: number;
  hash: string;
  createdAt: string;
  updatedAt: string;
  references: ImageReference[];
}

/**
 * 图片管理器类
 */
export class ImageManager {
  private static instance: ImageManager;
  private sqlite = useSqlite();

  // 单例模式
  public static getInstance(): ImageManager {
    if (!ImageManager.instance) {
      ImageManager.instance = new ImageManager();
    }
    return ImageManager.instance;
  }

  /**
   * 从base64数据保存图片
   */
  async saveImageFromData(
    imageData: string,
    mimeType: string,
    originalUrl?: string,
  ): Promise<ImageProcessResult> {
    return new Promise((resolve) => {
      window.databaseApi.saveImageFromData(
        imageData,
        mimeType,
        originalUrl || '',
        (result: {
          success: boolean;
          id?: number;
          file_path?: string;
          width?: number;
          height?: number;
          file_size?: number;
          is_duplicate?: boolean;
          error?: string;
        }) => {
          if (result.success) {
            resolve({
              success: true,
              imageId: result.id,
              localSrc: `image://${result.id}`,
              isDuplicate: result.is_duplicate || false,
              width: result.width,
              height: result.height,
              fileSize: result.file_size,
            });
          } else {
            resolve({
              success: false,
              error: result.error || $t('src.utils.imageManager.saveImageFailed'),
            });
          }
        },
      );
    });
  }

  /**
   * 从文件路径保存图片
   */
  async saveImageFromFile(filePath: string, originalUrl?: string): Promise<ImageProcessResult> {
    return new Promise((resolve) => {
      window.databaseApi.saveImageFromFile(
        filePath,
        originalUrl || '',
        (result: {
          success: boolean;
          id?: number;
          file_path?: string;
          width?: number;
          height?: number;
          file_size?: number;
          is_duplicate?: boolean;
          error?: string;
        }) => {
          if (result.success) {
            resolve({
              success: true,
              imageId: result.id,
              localSrc: `image://${result.id}`,
              isDuplicate: result.is_duplicate || false,
              width: result.width,
              height: result.height,
              fileSize: result.file_size,
            });
          } else {
            resolve({
              success: false,
              error: result.error || $t('src.utils.imageManager.saveImageFailed'),
            });
          }
        },
      );
    });
  }

  /**
   * 获取图片作为blob数据
   */
  async getImageAsBlob(imageId: number): Promise<string | null> {
    return new Promise((resolve) => {
      window.databaseApi.getImageAsBlob(
        imageId,
        (result: {
          success: boolean;
          data?: string;
          mime_type?: string;
          file_path?: string;
          error?: string;
        }) => {
          if (result.success && result.data) {
            // 创建blob URL
            const binaryData = atob(result.data);
            const bytes = new Uint8Array(binaryData.length);
            for (let i = 0; i < binaryData.length; i++) {
              bytes[i] = binaryData.charCodeAt(i);
            }
            const blob = new Blob([bytes], { type: result.mime_type });
            const blobUrl = URL.createObjectURL(blob);
            resolve(blobUrl);
          } else {
            console.error($t('src.utils.imageManager.getImageFailed'), result.error);
            resolve(null);
          }
        },
      );
    });
  }

  /**
   * 添加图片引用
   */
  async addImageReference(imageId: number, documentId: number): Promise<boolean> {
    return new Promise((resolve) => {
      window.databaseApi.addImageReference(
        imageId,
        documentId,
        (result: { success: boolean; affected_rows?: number; error?: string }) => {
          resolve(result.success);
        },
      );
    });
  }

  /**
   * 移除图片引用
   */
  async removeImageReference(imageId: number, documentId: number): Promise<boolean> {
    return new Promise((resolve) => {
      window.databaseApi.removeImageReference(
        imageId,
        documentId,
        (result: { success: boolean; affected_rows?: number; error?: string }) => {
          resolve(result.success);
        },
      );
    });
  }

  /**
   * 获取图片引用信息
   */
  async getImageReferences(
    imageId: number,
  ): Promise<Array<{ document_id: number; document_title: string }>> {
    return new Promise((resolve) => {
      window.databaseApi.getImageReferences(
        imageId,
        (result: {
          success: boolean;
          references?: Array<{ document_id: number; document_title: string }>;
          reference_count?: number;
          error?: string;
        }) => {
          if (result.success) {
            resolve(result.references || []);
          } else {
            console.error($t('src.utils.imageManager.getImageReferencesFailed'), result.error);
            resolve([]);
          }
        },
      );
    });
  }

  /**
   * 直接关联图片与文档（主动触发，不依赖文档保存）
   * 简化版本：只触发关联事件，不等待回调结果
   */
  associateImageWithDocument(imageId: number, documentId: number): void {
    console.log($t('src.utils.imageManager.triggerImageAssociation'), imageId, documentId);

    // 确保参数是数字类型
    const imgId = Number(imageId);
    const docId = Number(documentId);

    if (!window.databaseApi || !window.databaseApi.addImageReference) {
      console.error($t('src.utils.imageManager.addImageReferenceMethodUnavailable'));
      return;
    }

    try {
      // 直接调用，不等待回调结果
      // 后端会处理关联逻辑并在日志中记录任何错误
      window.databaseApi.addImageReference(imgId, docId, () => {
        // 空回调，不处理结果
      });

      console.log($t('src.utils.imageManager.imageAssociationEventTriggered'), imgId, docId);
    } catch (error) {
      console.error($t('src.utils.imageManager.triggerImageAssociationFailed'), error);
    }
  }

  /**
   * 删除无引用的图片
   */
  async deleteUnreferencedImages(): Promise<{ deletedCount: number; deletedFiles: string[] }> {
    return new Promise((resolve) => {
      window.databaseApi.deleteUnreferencedImages(
        (result: {
          success: boolean;
          deleted_count?: number;
          deleted_files?: string[];
          error?: string;
        }) => {
          if (result.success) {
            resolve({
              deletedCount: result.deleted_count || 0,
              deletedFiles: result.deleted_files || [],
            });
          } else {
            console.error(
              $t('src.utils.imageManager.deleteUnreferencedImagesFailed'),
              result.error,
            );
            resolve({ deletedCount: 0, deletedFiles: [] });
          }
        },
      );
    });
  }

  /**
   * 处理拖拽或粘贴的图片文件
   */
  async processImageFile(file: File, documentId: number): Promise<ImageProcessResult> {
    try {
      // 读取文件为base64
      const base64Data = await this.fileToBase64(file);

      // 保存图片
      const result = await this.saveImageFromData(base64Data, file.type, file.name);

      if (result.success && result.imageId) {
        // 使用新的主动关联方法（简化版本，不等待结果）
        this.associateImageWithDocument(result.imageId, documentId);
      }

      return result;
    } catch (error) {
      console.error($t('src.utils.imageManager.processImageFileFailed'), error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : $t('src.utils.imageManager.processImageFileFailed'),
      };
    }
  }

  /**
   * 处理网络图片URL
   */
  async processImageUrl(imageUrl: string, documentId: number): Promise<ImageProcessResult> {
    try {
      console.log(`[ImageManager] 开始处理网络图片: ${imageUrl}`);

      // 使用Qt后端下载图片，避免CORS问题
      const result = await new Promise<ImageProcessResult>((resolve) => {
        window.databaseApi.downloadAndSaveImage(
          documentId,
          imageUrl,
          (qtResult: {
            success: boolean;
            id?: number;
            file_path?: string;
            width?: number;
            height?: number;
            file_size?: number;
            is_duplicate?: boolean;
            error?: string;
          }) => {
            if (qtResult.success && qtResult.id) {
              console.log(`[ImageManager] Qt后端下载图片成功: ${imageUrl} -> ID: ${qtResult.id}`);
              resolve({
                success: true,
                imageId: qtResult.id,
                localSrc: `image://${qtResult.id}`,
                isDuplicate: qtResult.is_duplicate || false,
                width: qtResult.width,
                height: qtResult.height,
                fileSize: qtResult.file_size,
              });
            } else {
              console.error(`[ImageManager] Qt后端下载图片失败: ${imageUrl}`, qtResult.error);
              resolve({
                success: false,
                error: qtResult.error || $t('src.utils.imageManager.downloadImageFailed'),
              });
            }
          },
        );
      });

      return result;
    } catch (error) {
      console.error('[ImageManager] 处理网络图片失败:', error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : $t('src.utils.imageManager.processImageUrlFailed'),
      };
    }
  }

  /**
   * 处理图片删除（用户主动删除）
   */
  async handleImageDelete(imageId: number, documentId: number): Promise<void> {
    try {
      console.log(`开始处理图片删除: 图片ID=${imageId}, 文档ID=${documentId}`);

      // 先检查图片的引用情况
      const references = await this.getImageReferences(imageId);
      console.log(`图片 ${imageId} 当前引用数量: ${references.length}`);

      if (references.length > 0) {
        console.log(
          `图片 ${imageId} 的引用文档:`,
          references.map((ref) => `文档${ref.document_id}`).join(', '),
        );
      }

      // 移除当前文档的引用
      const removeSuccess = await this.removeImageReference(imageId, documentId);

      if (!removeSuccess) {
        console.error(`移除图片引用失败: 图片ID=${imageId}, 文档ID=${documentId}`);
        return;
      }

      console.log(`已移除图片 ${imageId} 在文档 ${documentId} 中的引用`);

      // 再次检查引用情况，确保安全删除
      const remainingReferences = await this.getImageReferences(imageId);
      console.log(`移除引用后，图片 ${imageId} 剩余引用数量: ${remainingReferences.length}`);

      if (remainingReferences.length === 0) {
        console.log(`图片 ${imageId} 无剩余引用，可以安全删除`);
        // 清理无引用的图片
        const cleanupResult = await this.deleteUnreferencedImages();

        if (cleanupResult.deletedCount > 0) {
          console.log(`清理了 ${cleanupResult.deletedCount} 个无引用的图片`);
        }
      } else {
        console.log(`图片 ${imageId} 仍有 ${remainingReferences.length} 个引用，不删除文件`);
        console.log(
          '剩余引用:',
          remainingReferences.map((ref) => `文档${ref.document_id}`).join(', '),
        );
      }
    } catch (error) {
      console.error('处理图片删除失败:', error);
    }
  }

  /**
   * 处理文档删除（清理所有图片引用）
   */
  async handleDocumentDelete(documentId: number): Promise<void> {
    try {
      // 这个方法会自动移除所有引用并清理无引用的图片
      await new Promise<void>((resolve) => {
        void this.sqlite.deleteDocumentImages(documentId, () => {
          resolve();
        });
      });
    } catch (error) {
      console.error('处理文档删除失败:', error);
    }
  }

  /**
   * 将File转换为base64
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // 移除data:image/xxx;base64,前缀
        const base64Data = result.split(',')[1];
        resolve(base64Data);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * 将Blob转换为base64
   */
  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // 移除data:image/xxx;base64,前缀
        const base64Data = result.split(',')[1];
        resolve(base64Data);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }
}

// 导出单例实例
export const imageManager = ImageManager.getInstance();
