<template>
  <teleport to="body">
    <transition enter-active-class="animated fadeInUp" leave-active-class="animated fadeOutDown">
      <div
        v-if="visible && hasPendingChanges"
        class="ai-change-action-bar fixed-bottom-center z-fab"
        :class="$q.dark.isActive ? 'bg-dark text-white' : 'bg-white text-dark'"
      >
        <q-card
          flat
          bordered
          class="shadow-24 radius-sm overflow-hidden"
          :class="$q.dark.isActive ? 'bg-black border-grey-7' : 'bg-white border-grey-4'"
        >
          <q-card-section class="q-py-xs q-pl-sm q-pr-xs">
            <div class="row items-center no-wrap q-gutter-sm">
              <!-- AI修改提示 -->
              <div class="col-auto">
                <div class="row items-center no-wrap q-gutter-xs">
                  <q-icon
                    name="auto_awesome"
                    size="1rem"
                    :color="$q.dark.isActive ? 'amber-4' : 'amber-7'"
                  />
                  <span class="text-body2 text-weight-medium q-ml-xs">
                    {{
                      $t('src.components.tiptap.aiChangeActionBar.aiSuggestion', {
                        count: changeCount,
                      })
                    }}
                  </span>
                </div>
              </div>

              <!-- 修改详情 -->
              <div v-if="showDetails" class="col-auto">
                <q-btn flat dense size="sm" icon="mdi-information-outline" @click="toggleDetails">
                  <q-tooltip>{{
                    $t('src.components.tiptap.aiChangeActionBar.viewDetails')
                  }}</q-tooltip>
                </q-btn>
              </div>

              <q-space />

              <!-- 操作按钮 -->
              <div class="col-auto">
                <div class="row no-wrap q-gutter-xs">
                  <q-btn
                    flat
                    dense
                    color="negative"
                    padding="none sm"
                    :label="$t('src.components.tiptap.aiChangeActionBar.reject')"
                    @click="handleReject"
                    :loading="processing"
                    :disable="processing"
                  >
                    <q-tooltip>{{
                      $t('src.components.tiptap.aiChangeActionBar.rejectTooltip')
                    }}</q-tooltip>
                  </q-btn>

                  <q-btn
                    unelevated
                    dense
                    color="primary"
                    padding="none sm"
                    class="radius-xs"
                    :label="$t('src.components.tiptap.aiChangeActionBar.accept')"
                    @click="handleAccept"
                    :loading="processing"
                    :disable="processing"
                  >
                    <q-tooltip>{{
                      $t('src.components.tiptap.aiChangeActionBar.acceptTooltip')
                    }}</q-tooltip>
                  </q-btn>
                </div>
              </div>
            </div>

            <!-- 修改详情展开区域 -->
            <q-slide-transition>
              <div v-if="detailsVisible" class="q-mt-sm q-pt-sm border-top">
                <div class="text-caption text-weight-medium q-mb-xs">
                  {{ $t('src.components.tiptap.aiChangeActionBar.changeDetails') }}:
                </div>
                <div class="column q-gutter-xs">
                  <div
                    v-for="(change, index) in changes"
                    :key="index"
                    class="row items-center no-wrap q-gutter-xs text-caption"
                  >
                    <q-icon
                      :name="change.type === 'insertion' ? 'mdi-plus' : 'mdi-minus'"
                      :color="change.type === 'insertion' ? 'positive' : 'negative'"
                      size="xs"
                    />
                    <span class="text-weight-medium">
                      {{
                        change.type === 'insertion'
                          ? $t('src.components.tiptap.aiChangeActionBar.insertion')
                          : $t('src.components.tiptap.aiChangeActionBar.deletion')
                      }}:
                    </span>
                    <span class="text-italic ellipsis">
                      "{{
                        change.text.length > 30
                          ? change.text.substring(0, 30) + '...'
                          : change.text
                      }}"
                    </span>
                  </div>
                </div>
              </div>
            </q-slide-transition>
          </q-card-section>
        </q-card>
      </div>
    </transition>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import {
  hasPendingChanges as checkPendingChanges,
  acceptAllChanges,
  rejectAllChanges,
} from 'src/llm/tools/editor';

const $q = useQuasar();
const { t: $t } = useI18n({ useScope: 'global' });

// 组件状态
const visible = ref(false);
const processing = ref(false);
const detailsVisible = ref(false);
const showDetails = ref(true);

// 修改数据
const changes = ref([]);
const changeCount = computed(() => changes.value.length);
const hasPendingChanges = computed(() => changes.value.length > 0);

// 检查待处理的修改
const checkChanges = () => {
  const result = checkPendingChanges();
  if (result.success) {
    changes.value = result.changes;
    visible.value = result.hasPending;
  } else {
    changes.value = [];
    visible.value = false;
  }
};

// 接受所有修改
const handleAccept = () => {
  if (processing.value) return;

  processing.value = true;

  try {
    const result = acceptAllChanges();

    if (result.success) {
      $q.notify({
        type: 'positive',
        message: result.message,
        position: 'top',
        timeout: 2000,
      });

      // 清理状态
      changes.value = [];
      visible.value = false;
      detailsVisible.value = false;
    } else {
      $q.notify({
        type: 'negative',
        message: result.message,
        position: 'top',
        timeout: 3000,
      });
    }
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: `操作失败: ${error.message}`,
      position: 'top',
      timeout: 3000,
    });
  } finally {
    processing.value = false;
  }
};

// 拒绝所有修改
const handleReject = () => {
  if (processing.value) return;

  processing.value = true;

  try {
    const result = rejectAllChanges();

    if (result.success) {
      $q.notify({
        type: 'info',
        message: result.message,
        position: 'top',
        timeout: 2000,
      });

      // 清理状态
      changes.value = [];
      visible.value = false;
      detailsVisible.value = false;
    } else {
      $q.notify({
        type: 'negative',
        message: result.message,
        position: 'top',
        timeout: 3000,
      });
    }
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: `操作失败: ${error.message}`,
      position: 'top',
      timeout: 3000,
    });
  } finally {
    processing.value = false;
  }
};

// 关闭操作栏
const handleClose = () => {
  visible.value = false;
  detailsVisible.value = false;
};

// 切换详情显示
const toggleDetails = () => {
  detailsVisible.value = !detailsVisible.value;
};

// 键盘快捷键处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (!visible.value || processing.value) return;

  // Ctrl+Y 接受修改
  if (event.key === 'y' && (event.ctrlKey || event.metaKey)) {
    event.preventDefault();
    handleAccept();
  }
  // Shift+Ctrl+Z 拒绝修改
  else if (event.key === 'z' && (event.ctrlKey || event.metaKey) && event.shiftKey) {
    event.preventDefault();
    handleReject();
  }
  // Esc 关闭操作栏
  else if (event.key === 'Escape') {
    event.preventDefault();
    handleClose();
  }
};

// 定期检查修改状态
let checkInterval: number | null = null;

const startChecking = () => {
  checkChanges();
  checkInterval = window.setInterval(checkChanges, 1000);
};

const stopChecking = () => {
  if (checkInterval) {
    clearInterval(checkInterval);
    checkInterval = null;
  }
};

// 暴露给外部调用的方法
const showActionBar = () => {
  checkChanges();
};

const hideActionBar = () => {
  visible.value = false;
};

// 生命周期
onMounted(() => {
  window.addEventListener('keydown', handleKeyDown);
  startChecking();
});

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeyDown);
  stopChecking();
});

// 暴露方法给父组件
defineExpose({
  showActionBar,
  hideActionBar,
  checkPendingChanges: checkChanges,
});
</script>

<style scoped>
.ai-change-action-bar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  max-width: 90vw;
  min-width: 300px;
}

.fixed-bottom-center {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

/* 动画效果 */
.animated {
  animation-duration: 0.3s;
  animation-fill-mode: both;
}

.fadeInUp {
  animation-name: fadeInUp;
}

.fadeOutDown {
  animation-name: fadeOutDown;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, 30px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

@keyframes fadeOutDown {
  from {
    opacity: 1;
    transform: translate(-50%, 0);
  }
  to {
    opacity: 0;
    transform: translate(-50%, 30px);
  }
}

.radius-md {
  border-radius: 12px;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}
</style>
