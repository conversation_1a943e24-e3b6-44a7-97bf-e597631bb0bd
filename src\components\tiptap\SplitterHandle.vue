<template>
  <div
    class="splitter-handle"
    :class="handleClasses"
    @pointerdown="startDrag"
    @pointerenter="handleHover"
    @pointerleave="handleLeave"
  ></div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue';
import { useQuasar } from 'quasar';
import { useVirtualDragSystem } from 'src/composables/VirtualDragSystem';
import type { DragUpdateContext } from 'src/composables/VirtualDragSystem';

interface Props {
  index: number;
  containerRef: HTMLElement | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'dragStart', index: number): void;
  (e: 'dragEnd'): void;
  (e: 'dragUpdate', clientX: number): void;
}>();

const $q = useQuasar();
const isHovering = ref(false);

// 使用虚拟拖拽系统
const { dragSystem, isDragging } = useVirtualDragSystem({
  throttleMs: 8, // 更高的刷新率
  enableRaf: true,
  enableVelocityTracking: true,
  maxVelocity: 3000,
});

// 优化的样式计算
const handleClasses = computed(() => {
  const baseClass = $q.dark.isActive ? 'ink-dark' : 'bg-grey-3';
  return {
    [baseClass]: !isDragging.value,
    'bg-primary': isDragging.value,
    'handle-hover': isHovering.value && !isDragging.value,
    'handle-dragging': isDragging.value,
  };
});

// 悬停状态管理
const handleHover = () => {
  isHovering.value = true;
};

const handleLeave = () => {
  isHovering.value = false;
};

// 拖拽处理函数
const startDrag = (e: PointerEvent) => {
  if (!props.containerRef) return;
  
  // 阻止默认行为
  e.preventDefault();
  e.stopPropagation();
  
  // 确保指针捕获
  const target = e.target as HTMLElement;
  target.setPointerCapture(e.pointerId);
  
  // 获取容器矩形
  const containerRect = props.containerRef.getBoundingClientRect();
  
  // 启动虚拟拖拽系统
  const cleanup = dragSystem.startDrag({
    handleIndex: props.index,
    initialPosition: {
      x: e.clientX,
      y: e.clientY,
      timestamp: performance.now(),
    },
    containerRect,
    targetElements: [target],
    onUpdate: handleDragUpdate,
    onEnd: handleDragEnd,
  });
  
  // 发送拖拽开始事件
  emit('dragStart', props.index);
  
  // 设置样式优化
  document.body.style.userSelect = 'none';
  document.body.style.cursor = 'col-resize';
  
  // 监听指针释放事件
  const handlePointerUp = () => {
    cleanup();
    target.releasePointerCapture(e.pointerId);
    target.removeEventListener('pointerup', handlePointerUp);
    target.removeEventListener('pointercancel', handlePointerUp);
  };
  
  target.addEventListener('pointerup', handlePointerUp, { once: true });
  target.addEventListener('pointercancel', handlePointerUp, { once: true });
};

// 拖拽更新处理
const handleDragUpdate = (context: DragUpdateContext) => {
  // 发送更新事件，使用虚拟坐标
  emit('dragUpdate', context.position.x);
};

// 拖拽结束处理
const handleDragEnd = () => {
  // 恢复样式
  document.body.style.userSelect = '';
  document.body.style.cursor = '';
  
  // 发送结束事件
  emit('dragEnd');
};

// 监听虚拟拖拽系统的事件
const handleVirtualDragUpdate = (event: CustomEvent) => {
  const context = event.detail as DragUpdateContext;
  if (context.handleIndex === props.index) {
    handleDragUpdate(context);
  }
};

// 添加事件监听器
if (typeof window !== 'undefined') {
  window.addEventListener('virtualDragUpdate', handleVirtualDragUpdate);
}

// 组件卸载时清理
onUnmounted(() => {
  // 清理事件监听器
  if (typeof window !== 'undefined') {
    window.removeEventListener('virtualDragUpdate', handleVirtualDragUpdate);
  }
  
  // 恢复样式
  document.body.style.userSelect = '';
  document.body.style.cursor = '';
});
</script>

<style scoped>
.splitter-handle {
  width: 2px;
  cursor: col-resize;
  flex: none;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化拖拽时的性能 */
  will-change: background-color;
  /* 使用GPU合成 */
  backface-visibility: hidden;
  /* 过渡动画优化 */
  transition: background-color 0.15s ease;
  /* 优化指针事件 */
  touch-action: none;
}

.splitter-handle:hover,
.handle-hover {
  /* 悬停效果 */
  opacity: 0.8;
  transform: translateZ(0) scale(1.2);
}

.handle-dragging {
  /* 拖拽时的优化 */
  will-change: transform, background-color;
  /* 提升渲染优先级 */
  z-index: 1000;
  /* 确保在拖拽时可见 */
  opacity: 1;
}

/* 性能优化：减少重绘 */
.splitter-handle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -2px;
  right: -2px;
  bottom: 0;
  /* 扩大触摸区域 */
  z-index: -1;
}

/* 暗色模式优化 */
.body--dark .splitter-handle {
  /* 暗色模式下的特殊优化 */
  filter: brightness(1.1);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .splitter-handle {
    width: 4px;
    /* 移动端更宽的拖拽区域 */
  }
  
  .splitter-handle::before {
    left: -4px;
    right: -4px;
  }
}

/* 减少动画在低端设备上的性能消耗 */
@media (prefers-reduced-motion: reduce) {
  .splitter-handle {
    transition: none;
  }
}
</style>
