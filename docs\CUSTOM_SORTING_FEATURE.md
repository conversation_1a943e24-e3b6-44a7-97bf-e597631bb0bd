# 自定义排序功能实现

## 功能概述

为 FolderTree 组件实现了自定义排序功能，支持：

1. **双排序模式**：
   - 字典排序（默认）：按照数字、英文字符、中文拼音的字母顺序排序
   - 自定义排序：按照数据库中的 sort_order 字段排序

2. **拖拽排序**：
   - 在自定义排序模式下，支持拖拽调整文档和文件夹的排序
   - 拖拽后自动更新数据库中的 sort_order 字段

3. **排序模式切换**：
   - 在文档管理器顶部提供排序模式切换按钮
   - 实时切换排序方式，无需刷新页面

## 技术实现

### 1. 排序工具函数扩展 (`src/utils/sortUtils.ts`)

```typescript
// 新增排序模式枚举
export enum SortMode {
  ALPHABETICAL = 'alphabetical', // 字典排序（默认）
  CUSTOM = 'custom', // 自定义排序（按 sort_order）
}

// 扩展排序函数支持排序模式参数
export function sortFolders(folders: Folder[], mode: SortMode = SortMode.ALPHABETICAL): Folder[];
export function sortDocuments(
  documents: Document[],
  mode: SortMode = SortMode.ALPHABETICAL,
): Document[];
export function sortFoldersAndDocuments(
  folders: Folder[],
  documents: Document[],
  mode: SortMode = SortMode.ALPHABETICAL,
);
export function sortFolderTree(
  folderTree: Folder[],
  mode: SortMode = SortMode.ALPHABETICAL,
): Folder[];
```

### 2. UI 状态管理 (`src/stores/ui.ts`)

```typescript
// 添加排序模式状态
state: () => ({
  // ...其他状态
  sortMode: SortMode.ALPHABETICAL as SortMode, // 默认字典排序
}),

// 添加排序模式管理方法
actions: {
  setSortMode(mode: SortMode) {
    this.sortMode = mode;
  },

  toggleSortMode() {
    this.sortMode = this.sortMode === SortMode.ALPHABETICAL ? SortMode.CUSTOM : SortMode.ALPHABETICAL;
  },
}
```

### 3. FolderTree 组件扩展 (`src/components/FolderTree.vue`)

#### 排序逻辑更新

```typescript
// 使用排序模式进行排序
const folders = computed(() => {
  // ...获取文件夹列表

  // 应用排序：根据当前排序模式排序文件夹和文档
  const currentSortMode = uiStore.sortMode;
  return folderList.map((folder) => {
    const sortedFolder = { ...folder };
    if (sortedFolder.documents && sortedFolder.documents.length > 0) {
      const { sortedDocuments } = sortFoldersAndDocuments(
        [],
        sortedFolder.documents,
        currentSortMode,
      );
      sortedFolder.documents = [...sortedDocuments];
    }
    return sortedFolder;
  });
});
```

#### 拖拽排序功能

```typescript
// 拖拽排序处理函数
const handleFolderReorder = async (
  dragFolderId: number,
  dropFolderId: number,
  parentId: number | null,
) => {
  // 重新排列文件夹顺序
  const reorderedFolders = [...currentFolders];
  const [draggedFolder] = reorderedFolders.splice(dragIndex, 1);
  reorderedFolders.splice(dropIndex, 0, draggedFolder);

  // 更新排序
  const folderIds = reorderedFolders.map((f) => f.id);
  await updateFolderOrder(parentId, folderIds);
};

const handleDocumentReorder = async (dragDocId: number, dropFolderId: number) => {
  // 重新排列文档顺序
  const documentIds = targetFolder.documents.map((d) => d.id);
  await updateDocumentOrder(dropFolderId, documentIds);
};
```

#### 智能拖拽处理

```typescript
// 根据排序模式处理拖拽操作
if (uiStore.sortMode === SortMode.CUSTOM) {
  // 自定义排序模式：处理排序逻辑
  if (dragData.type === 'folder' && dragData.parentId === dropData.id) {
    // 同级文件夹排序
    await handleFolderReorder(dragData.id, dropData.id, dragData.parentId);
  } else if (dragData.type === 'document' && dragData.parentId === dropData.id) {
    // 同级文档排序
    await handleDocumentReorder(dragData.id, dropData.id);
  } else {
    // 跨文件夹移动
    await handleCrossFolderMove(dragData, dropData);
  }
} else {
  // 字典排序模式：只允许跨文件夹移动
  await handleCrossFolderMove(dragData, dropData);
}
```

### 4. 拖拽组件 (`src/components/DraggableItem.vue`)

新创建的通用拖拽组件，提供了完整的拖拽排序功能：

**特性**：

- 支持原生 HTML5 拖拽 API
- 智能拖拽指示器（显示拖拽位置）
- 只在自定义排序模式下启用拖拽
- 提供视觉反馈（半透明、高亮等）
- 发射 `reorder` 事件通知父组件

**使用方式**：

```vue
<DraggableItem
  :item-id="item.id"
  :item-type="'folder' | 'document'"
  :item-index="index"
  :parent-id="parentId"
  @reorder="handleReorder"
>
  <slot /> <!-- 被拖拽的内容 -->
</DraggableItem>
```

### 5. 文档管理器 UI (`src/components/DocManager.vue`)

```vue
<!-- 排序模式切换按钮 -->
<q-btn
  v-if="hasFolders"
  dense
  size="0.7rem"
  flat
  :icon="
    uiStore.sortMode === 'alphabetical'
      ? 'mdi-sort-alphabetical-ascending'
      : 'mdi-sort-numeric-ascending'
  "
  :color="uiStore.sortMode === 'custom' ? 'primary' : undefined"
  @click="toggleSortMode"
>
  <q-tooltip>
    {{ uiStore.sortMode === 'alphabetical' ? '切换到自定义排序' : '切换到字典排序' }}
  </q-tooltip>
</q-btn>
```

## 使用方法

### 1. 排序模式切换

- 在文档管理器顶部点击排序按钮
- 字典排序图标：`mdi-sort-alphabetical-ascending`
- 自定义排序图标：`mdi-sort-numeric-ascending`（高亮显示）

### 2. 拖拽排序

- 切换到自定义排序模式
- 拖拽文件夹或文档到目标位置
- 拖拽时会显示蓝色的拖拽指示线
- 可以拖拽到目标项的上方或下方
- 系统自动更新数据库中的 sort_order 字段
- 排序立即生效

### 3. 拖拽效果

- **拖拽开始**：被拖拽的项目变为半透明并略微缩小
- **拖拽悬停**：目标区域显示蓝色背景高亮
- **拖拽指示器**：显示蓝色线条指示拖拽位置（上方或下方）
- **拖拽完成**：项目移动到新位置，数据库自动更新

### 3. 排序规则

#### 字典排序（默认）

- 数字 (0-9) - 最高优先级
- 英文字符 (a-z, A-Z) - 中等优先级
- 中文字符 - 按拼音字母顺序排序
- 其他字符 - 最低优先级

#### 自定义排序

- 按照数据库中的 sort_order 字段升序排列
- 支持拖拽调整顺序
- 新创建的项目自动分配最大的 sort_order + 1

## 数据库支持

后端已实现相关 API：

- `updateFolderOrder(parent_id, folder_ids)` - 更新文件夹排序
- `updateDocumentOrder(folder_id, document_ids)` - 更新文档排序

数据库表结构：

- `folder_folder_rel.sort_order` - 文件夹排序字段
- `folder_document_rel.sort_order` - 文档排序字段

## 特性

✅ **保持向后兼容**：默认使用字典排序，不影响现有功能
✅ **实时响应**：排序模式切换立即生效
✅ **拖拽友好**：自定义排序模式下支持直观的拖拽操作
✅ **数据持久化**：排序结果保存到数据库
✅ **智能处理**：区分同级排序和跨文件夹移动
✅ **用户体验**：清晰的视觉反馈和工具提示
✅ **原生拖拽**：使用 HTML5 拖拽 API，性能优异
✅ **拖拽指示器**：显示蓝色指示线，明确拖拽位置
✅ **通用组件**：DraggableItem 组件可复用于其他场景
✅ **类型安全**：完整的 TypeScript 类型定义
✅ **无依赖冲突**：移除了复杂的第三方拖拽库依赖
