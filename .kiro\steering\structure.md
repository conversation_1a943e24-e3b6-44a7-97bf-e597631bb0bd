# InkCop Project Structure

## Root Directory Organization

```
├── src/                    # Vue.js frontend source code
├── qt-src/                 # Qt C++ backend source code
├── docs/                   # Project documentation
├── build-*/                # Build output directories
├── dist/                   # Frontend build output
├── third-party/            # External dependencies
├── public/                 # Static frontend assets
├── patches/                # Package patches
└── *.ps1, *.sh            # Build scripts
```

## Frontend Structure (`src/`)

```
src/
├── components/             # Reusable Vue components
├── pages/                  # Route-based page components
├── layouts/                # Layout wrapper components
├── stores/                 # Pinia state management
├── composables/            # Vue composition functions
├── services/               # API and business logic
├── utils/                  # Utility functions
├── types/                  # TypeScript type definitions
├── i18n/                   # Internationalization files
├── css/                    # Global styles
├── boot/                   # Quasar boot files
├── router/                 # Vue Router configuration
├── workers/                # Web Workers
├── llm/                    # LLM integration logic
└── App.vue                 # Root application component
```

## Backend Structure (`qt-src/`)

```
qt-src/
├── main.cpp                # Application entry point
├── mainwindow.{h,cpp}      # Main window implementation
├── customtoolbar.{h,cpp}   # Custom toolbar component
├── customwebengineview.{h,cpp} # Custom web view
├── windowapi.{h,cpp}       # Window control API
├── databaseapi.{h,cpp}     # Database operations
├── knowledgeapi.{h,cpp}    # Knowledge base API
├── localggufembedding.{h,cpp} # Local GGUF model support
├── settings.{h,cpp}        # Application settings
├── objectbox/              # ObjectBox schema and generated code
└── app.{ico,rc}           # Windows application resources
```

## Key Configuration Files

- **Frontend**: `quasar.config.ts`, `package.json`, `tsconfig.json`
- **Backend**: `CMakeLists.txt`, `objectbox-model.json`
- **Build**: `win-dev.ps1`, `win-prod.ps1`, build scripts
- **Environment**: `.env`, `.env.production`, `.env.release`
- **Code Quality**: `eslint.config.js`, `.prettierrc.json`

## Build Artifacts

- `build-dev/`: Development builds with console output
- `build-prod/`: Production builds for distribution
- `dist/spa/`: Frontend build output (embedded in Qt app)
- `build-installer/`: Installer package output

## Naming Conventions

- **Files**: kebab-case for components, PascalCase for classes
- **Directories**: lowercase with hyphens
- **Vue Components**: PascalCase (e.g., `MyComponent.vue`)
- **Qt Classes**: PascalCase with descriptive names
- **API Methods**: camelCase following JavaScript conventions
- **Constants**: UPPER_SNAKE_CASE

## Import Patterns

- Use path aliases: `src/*`, `components/*`, `stores/*`
- Prefer named imports over default imports
- Group imports: external libraries, internal modules, relative imports
- Use TypeScript type imports with `import type`
