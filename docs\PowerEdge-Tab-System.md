# PowerEdge Tab 系统使用说明

## 概述

PowerEdge 组件现在支持动态 tab 系统，可以根据导入的组件自动生成 tab 和相应的 tab-panel。

## 功能特性

### 1. 动态 Tab 生成

- Tab 条目根据 `tabConfigs` 数组中的组件配置自动生成
- 每个 tab 对应一个组件，支持图标和标签自定义
- 支持无限扩展，只需在配置中添加新组件即可

### 2. 本地存储支持

- Tab 选择状态自动保存到 localStorage (`powerEdgeActiveTab`)
- 页面刷新后会恢复上次选择的 tab
- 默认选择第一个可用的 tab

### 3. 组件通信

- 所有 tab 组件都会接收到以下 props：
  - `docId`: 当前编辑的文档 ID
  - `activePaneIndex`: 活动面板索引
  - `wordCount`: 字数统计

## 当前可用组件

### 1. DocumentMetadata 组件

- **功能**: 显示和编辑文档元数据
- **特性**:
  - 自动获取当前聚焦文档的数据（使用 `docStore.getFocusedDocument()`）
  - 显示文档的 metadata、created_at、updated_at 字段
  - **结构化元数据编辑**：
    - metadata 字段存储为 JSON 字符串
    - 包含 `prompt` 和 `note` 两个字段
    - 提供独立的输入框编辑这两个字段
    - 自动处理 JSON 解析和序列化
  - 使用 `useSqlite().updateDocument()` 方法保存更改
  - 实时显示文档基本信息（ID、标题等）
  - **自动保存机制**：
    - 输入框默认为可编辑状态，无需手动开启编辑模式
    - 输入框失去焦点时自动检查内容变化并保存
    - 组件销毁时自动保存未保存的变化
    - 切换文档时自动保存当前文档的变化
  - **默认值处理**：当后端返回 metadata 为 null 时，使用默认的空值结构

### 2. WordCountStats 组件

- **功能**: 显示文档统计信息
- **特性**:
  - 字数统计
  - 字符数计算
  - 段落数估算
  - 阅读时间计算
  - 实时更新统计信息

## 如何添加新组件

### 步骤 1: 创建组件

创建一个新的 Vue 组件。根据组件需求，可以选择：

**选项A**: 使用 props 接收数据（适用于统计类组件）

```typescript
interface Props {
  docId?: number | null;
  activePaneIndex?: number;
  wordCount?: number;
}
```

**选项B**: 直接从 docStore 获取数据（适用于文档信息类组件）

```typescript
import { useDocStore } from 'src/stores/doc';
const docStore = useDocStore();
const focusedDocument = computed(() => docStore.getFocusedDocument());
```

### 步骤 2: 导入组件

在 `PowerEdge.vue` 中导入你的新组件：

```typescript
import YourNewComponent from './YourNewComponent.vue';
```

### 步骤 3: 添加到配置

在 `tabConfigs` 数组中添加新的配置项：

```typescript
const tabConfigs: TabConfig[] = [
  // 现有配置...
  {
    name: 'your-component',
    label: '你的组件',
    component: YourNewComponent,
    icon: 'your-icon', // 可选
  },
];
```

### 示例组件模板

```vue
<template>
  <div class="your-component">
    <div v-if="!docId" class="text-grey-6 text-center q-pa-md">请选择一个文档</div>

    <div v-else class="q-pa-sm">
      <!-- 你的组件内容 -->
      <div>文档 ID: {{ docId }}</div>
      <div>字数: {{ wordCount }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  docId?: number | null;
  activePaneIndex?: number;
  wordCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  docId: null,
  activePaneIndex: 0,
  wordCount: 0,
});
</script>

<style scoped>
.your-component {
  height: 100%;
  overflow-y: auto;
}
</style>
```

## 配置选项

### TabConfig 接口

```typescript
interface TabConfig {
  name: string; // 唯一标识符
  label: string; // 显示标签
  component: any; // Vue 组件
  icon?: string; // 可选图标（Quasar 图标名称）
}
```

## 注意事项

1. **组件命名**: 确保每个 tab 的 `name` 是唯一的
2. **Props 兼容**: 新组件必须支持标准的 props 接口
3. **样式一致**: 建议使用一致的样式和布局模式
4. **性能考虑**: 使用 `keep-alive` 保持组件状态，避免重复渲染
5. **错误处理**: 组件应该优雅地处理 `docId` 为空的情况

## 本地存储键

- `powerEdgeActiveTab`: 存储当前选中的 tab 名称
- `powerEdgeHeight`: 存储 PowerEdge 组件的高度

## Metadata 数据结构

DocumentMetadata 组件使用以下 JSON 结构存储元数据：

```typescript
interface MetadataObject {
  prompt: string; // AI 处理的提示信息
  note: string; // 文档备注信息
}
```

### 数据处理流程

1. **读取**: 从数据库获取 metadata 字符串 → JSON.parse() → 分离为 prompt 和 note 字段
2. **编辑**: 用户在两个独立的输入框中编辑 prompt 和 note
3. **自动保存**: 检查变化 → 合并为对象 → JSON.stringify() → 保存到数据库的 metadata 字段

### 自动保存触发条件

- **输入框失去焦点** (`@blur` 事件)
- **切换到其他文档** (watch 监听器)
- **组件销毁** (`onBeforeUnmount` 钩子)

只有当内容实际发生变化时才会执行保存操作，避免不必要的数据库写入。

### 默认值处理

当 metadata 为 null 或解析失败时，使用默认值：

```json
{
  "prompt": "",
  "note": ""
}
```

## API 集成

DocumentMetadata 组件使用以下方法：

- `docStore.getFocusedDocument()`: 获取当前聚焦的文档数据
- `useSqlite().updateDocument()`: 更新文档（包括 metadata 字段）
- `docStore.getEditorContent(docId)`: 获取文档内容用于更新操作

这些方法都是项目内部已实现的方法，无需额外配置。
