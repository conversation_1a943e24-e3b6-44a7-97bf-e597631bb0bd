// Code generated by ObjectBox; DO NOT EDIT.

#include "knowledge.obx.hpp"

const obx::Property<InkCop::Knowledge::KnowledgeBase, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeBase_::id(1);
const obx::Property<InkCop::Knowledge::KnowledgeBase, OBXPropertyType_String> InkCop::Knowledge::KnowledgeBase_::name(2);
const obx::Property<InkCop::Knowledge::KnowledgeBase, OBXPropertyType_String> InkCop::Knowledge::KnowledgeBase_::description(3);
const obx::Property<InkCop::Knowledge::KnowledgeBase, OBXPropertyType_String> InkCop::Knowledge::KnowledgeBase_::user_id(4);
const obx::Property<InkCop::Knowledge::KnowledgeBase, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeBase_::created_at(5);
const obx::Property<InkCop::Knowledge::KnowledgeBase, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeBase_::updated_at(6);

void InkCop::Knowledge::KnowledgeBase::_OBX_MetaInfo::toFlatBuffer(flatbuffers::FlatBufferBuilder &fbb, const InkCop::Knowledge::KnowledgeBase &object)
{
    fbb.Clear();
    auto offsetname = fbb.CreateString(object.name);
    auto offsetdescription = fbb.CreateString(object.description);
    auto offsetuser_id = fbb.CreateString(object.user_id);
    flatbuffers::uoffset_t fbStart = fbb.StartTable();
    fbb.AddElement(4, object.id);
    fbb.AddOffset(6, offsetname);
    fbb.AddOffset(8, offsetdescription);
    fbb.AddOffset(10, offsetuser_id);
    fbb.AddElement(12, object.created_at);
    fbb.AddElement(14, object.updated_at);
    flatbuffers::Offset<flatbuffers::Table> offset;
    offset.o = fbb.EndTable(fbStart);
    fbb.Finish(offset);
}

InkCop::Knowledge::KnowledgeBase InkCop::Knowledge::KnowledgeBase::_OBX_MetaInfo::fromFlatBuffer(const void *data, size_t size)
{
    InkCop::Knowledge::KnowledgeBase object;
    fromFlatBuffer(data, size, object);
    return object;
}

std::unique_ptr<InkCop::Knowledge::KnowledgeBase> InkCop::Knowledge::KnowledgeBase::_OBX_MetaInfo::newFromFlatBuffer(const void *data, size_t size)
{
    auto object = std::unique_ptr<InkCop::Knowledge::KnowledgeBase>(new InkCop::Knowledge::KnowledgeBase());
    fromFlatBuffer(data, size, *object);
    return object;
}

void InkCop::Knowledge::KnowledgeBase::_OBX_MetaInfo::fromFlatBuffer(const void *data, size_t, InkCop::Knowledge::KnowledgeBase &outObject)
{
    const auto *table = flatbuffers::GetRoot<flatbuffers::Table>(data);
    assert(table);
    outObject.id = table->GetField<obx_id>(4, 0);
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(6);
        if (ptr)
        {
            outObject.name.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.name.clear();
        }
    }
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(8);
        if (ptr)
        {
            outObject.description.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.description.clear();
        }
    }
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(10);
        if (ptr)
        {
            outObject.user_id.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.user_id.clear();
        }
    }
    outObject.created_at = table->GetField<uint64_t>(12, 0);
    outObject.updated_at = table->GetField<uint64_t>(14, 0);
}

const obx::Property<InkCop::Knowledge::KnowledgeChunk, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeChunk_::id(1);
const obx::Property<InkCop::Knowledge::KnowledgeChunk, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeChunk_::knowledge_document_id(2);
const obx::Property<InkCop::Knowledge::KnowledgeChunk, OBXPropertyType_Int> InkCop::Knowledge::KnowledgeChunk_::chunk_index(3);
const obx::Property<InkCop::Knowledge::KnowledgeChunk, OBXPropertyType_String> InkCop::Knowledge::KnowledgeChunk_::content(4);
const obx::Property<InkCop::Knowledge::KnowledgeChunk, OBXPropertyType_FloatVector> InkCop::Knowledge::KnowledgeChunk_::embedding(5);
const obx::Property<InkCop::Knowledge::KnowledgeChunk, OBXPropertyType_String> InkCop::Knowledge::KnowledgeChunk_::metadata(6);
const obx::Property<InkCop::Knowledge::KnowledgeChunk, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeChunk_::created_at(7);
const obx::Property<InkCop::Knowledge::KnowledgeChunk, OBXPropertyType_Bool> InkCop::Knowledge::KnowledgeChunk_::is_vectorized(8);

void InkCop::Knowledge::KnowledgeChunk::_OBX_MetaInfo::toFlatBuffer(flatbuffers::FlatBufferBuilder &fbb, const InkCop::Knowledge::KnowledgeChunk &object)
{
    fbb.Clear();
    auto offsetcontent = fbb.CreateString(object.content);
    auto offsetembedding = fbb.CreateVector(object.embedding);
    auto offsetmetadata = fbb.CreateString(object.metadata);
    flatbuffers::uoffset_t fbStart = fbb.StartTable();
    fbb.AddElement(4, object.id);
    fbb.AddElement(6, object.knowledge_document_id);
    fbb.AddElement(8, object.chunk_index);
    fbb.AddOffset(10, offsetcontent);
    fbb.AddOffset(12, offsetembedding);
    fbb.AddOffset(14, offsetmetadata);
    fbb.AddElement(16, object.created_at);
    fbb.AddElement(18, object.is_vectorized);
    flatbuffers::Offset<flatbuffers::Table> offset;
    offset.o = fbb.EndTable(fbStart);
    fbb.Finish(offset);
}

InkCop::Knowledge::KnowledgeChunk InkCop::Knowledge::KnowledgeChunk::_OBX_MetaInfo::fromFlatBuffer(const void *data, size_t size)
{
    InkCop::Knowledge::KnowledgeChunk object;
    fromFlatBuffer(data, size, object);
    return object;
}

std::unique_ptr<InkCop::Knowledge::KnowledgeChunk> InkCop::Knowledge::KnowledgeChunk::_OBX_MetaInfo::newFromFlatBuffer(const void *data, size_t size)
{
    auto object = std::unique_ptr<InkCop::Knowledge::KnowledgeChunk>(new InkCop::Knowledge::KnowledgeChunk());
    fromFlatBuffer(data, size, *object);
    return object;
}

void InkCop::Knowledge::KnowledgeChunk::_OBX_MetaInfo::fromFlatBuffer(const void *data, size_t, InkCop::Knowledge::KnowledgeChunk &outObject)
{
    const auto *table = flatbuffers::GetRoot<flatbuffers::Table>(data);
    assert(table);
    outObject.id = table->GetField<obx_id>(4, 0);
    outObject.knowledge_document_id = table->GetField<uint64_t>(6, 0);
    outObject.chunk_index = table->GetField<uint32_t>(8, 0);
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(10);
        if (ptr)
        {
            outObject.content.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.content.clear();
        }
    }
    {
        auto *ptr = table->GetPointer<const flatbuffers::Vector<float> *>(12);
        if (ptr)
        {
            outObject.embedding.assign(ptr->begin(), ptr->end());
        }
        else
        {
            outObject.embedding.clear();
        }
    }
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(14);
        if (ptr)
        {
            outObject.metadata.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.metadata.clear();
        }
    }
    outObject.created_at = table->GetField<uint64_t>(16, 0);
    outObject.is_vectorized = table->GetField<bool>(18, false);
}

const obx::Property<InkCop::Knowledge::KnowledgeDocument, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeDocument_::id(1);
const obx::Property<InkCop::Knowledge::KnowledgeDocument, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeDocument_::kb_id(2);
const obx::Property<InkCop::Knowledge::KnowledgeDocument, OBXPropertyType_String> InkCop::Knowledge::KnowledgeDocument_::title(3);
const obx::Property<InkCop::Knowledge::KnowledgeDocument, OBXPropertyType_String> InkCop::Knowledge::KnowledgeDocument_::content(4);
const obx::Property<InkCop::Knowledge::KnowledgeDocument, OBXPropertyType_String> InkCop::Knowledge::KnowledgeDocument_::document_type(5);
const obx::Property<InkCop::Knowledge::KnowledgeDocument, OBXPropertyType_String> InkCop::Knowledge::KnowledgeDocument_::metadata(6);
const obx::Property<InkCop::Knowledge::KnowledgeDocument, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeDocument_::created_at(7);
const obx::Property<InkCop::Knowledge::KnowledgeDocument, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeDocument_::updated_at(8);

void InkCop::Knowledge::KnowledgeDocument::_OBX_MetaInfo::toFlatBuffer(flatbuffers::FlatBufferBuilder &fbb, const InkCop::Knowledge::KnowledgeDocument &object)
{
    fbb.Clear();
    auto offsettitle = fbb.CreateString(object.title);
    auto offsetcontent = fbb.CreateString(object.content);
    auto offsetdocument_type = fbb.CreateString(object.document_type);
    auto offsetmetadata = fbb.CreateString(object.metadata);
    flatbuffers::uoffset_t fbStart = fbb.StartTable();
    fbb.AddElement(4, object.id);
    fbb.AddElement(6, object.kb_id);
    fbb.AddOffset(8, offsettitle);
    fbb.AddOffset(10, offsetcontent);
    fbb.AddOffset(12, offsetdocument_type);
    fbb.AddOffset(14, offsetmetadata);
    fbb.AddElement(16, object.created_at);
    fbb.AddElement(18, object.updated_at);
    flatbuffers::Offset<flatbuffers::Table> offset;
    offset.o = fbb.EndTable(fbStart);
    fbb.Finish(offset);
}

InkCop::Knowledge::KnowledgeDocument InkCop::Knowledge::KnowledgeDocument::_OBX_MetaInfo::fromFlatBuffer(const void *data, size_t size)
{
    InkCop::Knowledge::KnowledgeDocument object;
    fromFlatBuffer(data, size, object);
    return object;
}

std::unique_ptr<InkCop::Knowledge::KnowledgeDocument> InkCop::Knowledge::KnowledgeDocument::_OBX_MetaInfo::newFromFlatBuffer(const void *data, size_t size)
{
    auto object = std::unique_ptr<InkCop::Knowledge::KnowledgeDocument>(new InkCop::Knowledge::KnowledgeDocument());
    fromFlatBuffer(data, size, *object);
    return object;
}

void InkCop::Knowledge::KnowledgeDocument::_OBX_MetaInfo::fromFlatBuffer(const void *data, size_t, InkCop::Knowledge::KnowledgeDocument &outObject)
{
    const auto *table = flatbuffers::GetRoot<flatbuffers::Table>(data);
    assert(table);
    outObject.id = table->GetField<obx_id>(4, 0);
    outObject.kb_id = table->GetField<uint64_t>(6, 0);
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(8);
        if (ptr)
        {
            outObject.title.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.title.clear();
        }
    }
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(10);
        if (ptr)
        {
            outObject.content.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.content.clear();
        }
    }
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(12);
        if (ptr)
        {
            outObject.document_type.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.document_type.clear();
        }
    }
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(14);
        if (ptr)
        {
            outObject.metadata.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.metadata.clear();
        }
    }
    outObject.created_at = table->GetField<uint64_t>(16, 0);
    outObject.updated_at = table->GetField<uint64_t>(18, 0);
}

const obx::Property<InkCop::Knowledge::KnowledgeQuery, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeQuery_::id(1);
const obx::Property<InkCop::Knowledge::KnowledgeQuery, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeQuery_::knowledge_base_id(2);
const obx::Property<InkCop::Knowledge::KnowledgeQuery, OBXPropertyType_String> InkCop::Knowledge::KnowledgeQuery_::query_text(3);
const obx::Property<InkCop::Knowledge::KnowledgeQuery, OBXPropertyType_FloatVector> InkCop::Knowledge::KnowledgeQuery_::query_embedding(4);
const obx::Property<InkCop::Knowledge::KnowledgeQuery, OBXPropertyType_String> InkCop::Knowledge::KnowledgeQuery_::results(5);
const obx::Property<InkCop::Knowledge::KnowledgeQuery, OBXPropertyType_Long> InkCop::Knowledge::KnowledgeQuery_::created_at(6);

void InkCop::Knowledge::KnowledgeQuery::_OBX_MetaInfo::toFlatBuffer(flatbuffers::FlatBufferBuilder &fbb, const InkCop::Knowledge::KnowledgeQuery &object)
{
    fbb.Clear();
    auto offsetquery_text = fbb.CreateString(object.query_text);
    auto offsetquery_embedding = fbb.CreateVector(object.query_embedding);
    auto offsetresults = fbb.CreateString(object.results);
    flatbuffers::uoffset_t fbStart = fbb.StartTable();
    fbb.AddElement(4, object.id);
    fbb.AddElement(6, object.knowledge_base_id);
    fbb.AddOffset(8, offsetquery_text);
    fbb.AddOffset(10, offsetquery_embedding);
    fbb.AddOffset(12, offsetresults);
    fbb.AddElement(14, object.created_at);
    flatbuffers::Offset<flatbuffers::Table> offset;
    offset.o = fbb.EndTable(fbStart);
    fbb.Finish(offset);
}

InkCop::Knowledge::KnowledgeQuery InkCop::Knowledge::KnowledgeQuery::_OBX_MetaInfo::fromFlatBuffer(const void *data, size_t size)
{
    InkCop::Knowledge::KnowledgeQuery object;
    fromFlatBuffer(data, size, object);
    return object;
}

std::unique_ptr<InkCop::Knowledge::KnowledgeQuery> InkCop::Knowledge::KnowledgeQuery::_OBX_MetaInfo::newFromFlatBuffer(const void *data, size_t size)
{
    auto object = std::unique_ptr<InkCop::Knowledge::KnowledgeQuery>(new InkCop::Knowledge::KnowledgeQuery());
    fromFlatBuffer(data, size, *object);
    return object;
}

void InkCop::Knowledge::KnowledgeQuery::_OBX_MetaInfo::fromFlatBuffer(const void *data, size_t, InkCop::Knowledge::KnowledgeQuery &outObject)
{
    const auto *table = flatbuffers::GetRoot<flatbuffers::Table>(data);
    assert(table);
    outObject.id = table->GetField<obx_id>(4, 0);
    outObject.knowledge_base_id = table->GetField<uint64_t>(6, 0);
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(8);
        if (ptr)
        {
            outObject.query_text.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.query_text.clear();
        }
    }
    {
        auto *ptr = table->GetPointer<const flatbuffers::Vector<float> *>(10);
        if (ptr)
        {
            outObject.query_embedding.assign(ptr->begin(), ptr->end());
        }
        else
        {
            outObject.query_embedding.clear();
        }
    }
    {
        auto *ptr = table->GetPointer<const flatbuffers::String *>(12);
        if (ptr)
        {
            outObject.results.assign(ptr->c_str(), ptr->size());
        }
        else
        {
            outObject.results.clear();
        }
    }
    outObject.created_at = table->GetField<uint64_t>(14, 0);
}
