import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useSqlite } from './useSqlite';
import { useKnowledgeDocumentBatch } from './useKnowledgeDocumentBatch';
import { useDocumentChunking } from './useDocumentChunking';
import { useKnowledgeStore } from '../stores/knowledge';
import { convertTiptapToMarkdown } from '../utils/tiptap';
import type { ChunkingMethod, ChunkingConfig } from '../types/qwen';

/**
 * 文档添加到知识库的配置
 */
export interface DocumentToKnowledgeConfig {
  knowledgeBaseId: number;
  chunkingMethod: ChunkingMethod;
  chunkingConfig: ChunkingConfig;
}

/**
 * 单个文档处理结果
 */
export interface DocumentProcessResult {
  documentId: number;
  originalDocumentId: number;
  title: string;
  success: boolean;
  error?: string;
  knowledgeDocumentId?: number;
}

/**
 * 批量处理结果
 */
export interface BatchProcessResult {
  totalCount: number;
  processedCount: number;
  errorCount: number;
  results: DocumentProcessResult[];
}

/**
 * 处理进度回调
 */
export interface ProcessCallbacks {
  onProgress?: (current: number, total: number, currentTitle: string) => void;
  onDocumentCompleted?: (result: DocumentProcessResult) => void;
  onChunkingProgress?: (docId: number, progress: { stage: string; percentage: number }) => void;
  onChunkingCompleted?: (docId: number, result: { chunkCount: number }) => void;
  onVectorizationCompleted?: (docId: number, chunkCount: number) => void;
  onError?: (
    docId: number,
    error: string,
    stage: 'creation' | 'chunking' | 'vectorization',
  ) => void;
}

/**
 * 文档添加到知识库的 Hook
 */
export function useDocumentToKnowledge() {
  const $q = useQuasar();
  const { t: $t } = useI18n({ useScope: 'global' });

  const sqlite = useSqlite();
  const knowledgeBatch = useKnowledgeDocumentBatch();
  const documentChunking = useDocumentChunking();
  const knowledgeStore = useKnowledgeStore();

  // 状态管理
  const isProcessing = ref(false);
  const currentProgress = ref({ current: 0, total: 0, currentTitle: '' });

  /**
   * 处理单个文档添加到知识库
   */
  const processDocument = async (
    docId: number,
    config: DocumentToKnowledgeConfig,
    callbacks?: ProcessCallbacks,
  ): Promise<DocumentProcessResult> => {
    const result: DocumentProcessResult = {
      documentId: docId,
      originalDocumentId: docId,
      title: '',
      success: false,
    };

    try {
      // 从后端获取完整的文档内容
      const fullDocument = await sqlite.getDocument(docId);
      result.title = fullDocument.title || '无标题文档';

      console.log('📄 [文档处理] 处理文档:', result.title);

      let markdownContent = '';

      if (fullDocument.content) {
        // 尝试解析为JSON格式的TipTap内容
        const jsonContent =
          typeof fullDocument.content === 'string'
            ? JSON.parse(fullDocument.content)
            : fullDocument.content;
        markdownContent = convertTiptapToMarkdown(jsonContent);
        console.log('📝 [文档处理] TipTap转Markdown成功，长度:', markdownContent.length);
      } else {
        console.log('📝 [文档处理] 文档内容为空:', result.title);
      }

      // 检查内容是否为空
      if (!markdownContent.trim()) {
        throw new Error('文档内容为空');
      }

      // 第一步：创建知识库文档
      const knowledgeDocumentId = await knowledgeBatch.createKnowledgeDocumentOnly(
        config.knowledgeBaseId,
        result.title,
        markdownContent,
        config.chunkingMethod,
        config.chunkingConfig,
      );

      if (!knowledgeDocumentId) {
        throw new Error('创建知识库文档失败');
      }

      result.knowledgeDocumentId = knowledgeDocumentId;

      // 标记该知识库的文档列表需要刷新（用于keep-alive缓存场景）
      console.log('🔄 [useDocumentToKnowledge] 标记知识库需要刷新:', {
        knowledgeBaseId: config.knowledgeBaseId,
        documentTitle: result.title,
        knowledgeDocumentId,
      });
      knowledgeStore.markKnowledgeBaseNeedsRefresh(config.knowledgeBaseId);

      // 创建文档-知识库关联关系
      try {
        await sqlite.addDocumentKnowledgeAssociation(
          docId,
          knowledgeDocumentId,
          config.knowledgeBaseId,
        );
        console.log('✅ [文档处理] 文档知识库关联创建成功:', {
          originalDocumentId: docId,
          knowledgeDocumentId,
          knowledgeBaseId: config.knowledgeBaseId,
          title: result.title,
        });
      } catch (error) {
        console.error('❌ [文档处理] 创建文档知识库关联失败:', error);
        // 不阻断主流程，只记录错误
      }

      // 第二步：立即开始切割和向量化
      console.log('🔄 [文档处理] 开始切割和向量化文档:', result.title);

      await documentChunking.processDocumentChunking(
        knowledgeDocumentId,
        result.title,
        markdownContent,
        config.chunkingMethod,
        config.chunkingConfig,
        {
          onProgress: (docId, progress) => {
            console.log(
              `📊 [文档处理] 文档 ${docId} 切割进度: ${progress.stage} (${progress.percentage}%)`,
            );
            callbacks?.onChunkingProgress?.(docId, progress);
          },
          onChunkingCompleted: (docId, chunkingResult) => {
            console.log(
              `✅ [文档处理] 文档 ${docId} 切割完成，共 ${chunkingResult.chunkCount} 个块`,
            );
            callbacks?.onChunkingCompleted?.(docId, chunkingResult);
          },
          onVectorizationCompleted: (docId, chunkCount) => {
            console.log(`🎯 [文档处理] 文档 ${docId} 向量化完成，处理了 ${chunkCount} 个块`);
            callbacks?.onVectorizationCompleted?.(docId, chunkCount);
          },
          onError: (docId, error, stage) => {
            console.error(`❌ [文档处理] 文档 ${docId} ${stage}失败:`, error);
            callbacks?.onError?.(docId, error, stage);
          },
        },
      );

      result.success = true;
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '处理文档失败';
      console.error('❌ [文档处理] 处理文档失败，文档ID:', docId, error);
      result.error = errorMessage;
      callbacks?.onError?.(docId, errorMessage, 'creation');
      return result;
    }
  };

  /**
   * 批量处理多个文档添加到知识库
   */
  const processBatchDocuments = async (
    documentIds: number[],
    config: DocumentToKnowledgeConfig,
    callbacks?: ProcessCallbacks,
  ): Promise<BatchProcessResult> => {
    if (isProcessing.value) {
      throw new Error('已有处理任务在进行中');
    }

    isProcessing.value = true;
    currentProgress.value = { current: 0, total: documentIds.length, currentTitle: '' };

    const batchResult: BatchProcessResult = {
      totalCount: documentIds.length,
      processedCount: 0,
      errorCount: 0,
      results: [],
    };

    try {
      console.log(`🚀 [批量处理] 开始处理 ${documentIds.length} 个文档`);

      for (let i = 0; i < documentIds.length; i++) {
        const docId = documentIds[i];
        currentProgress.value.current = i + 1;

        const result = await processDocument(docId, config, callbacks);
        batchResult.results.push(result);

        if (result.success) {
          batchResult.processedCount++;
        } else {
          batchResult.errorCount++;
        }

        currentProgress.value.currentTitle = result.title;
        callbacks?.onProgress?.(i + 1, documentIds.length, result.title);
        callbacks?.onDocumentCompleted?.(result);

        console.log(`✅ [批量处理] 文档 ${i + 1}/${documentIds.length} 已完成处理`);
      }

      console.log(
        `🎉 [批量处理] 批量处理完成: 成功 ${batchResult.processedCount}，失败 ${batchResult.errorCount}`,
      );

      // 显示完成通知
      $q.notify({
        type: batchResult.errorCount === 0 ? 'positive' : 'warning',
        message: $t('src.composeables.useDocumentToKnowledge.batch_completed', {
          processed: batchResult.processedCount,
          total: batchResult.totalCount,
          errors: batchResult.errorCount,
        }),
        timeout: 5000,
      });

      return batchResult;
    } finally {
      isProcessing.value = false;
      currentProgress.value = { current: 0, total: 0, currentTitle: '' };
    }
  };

  /**
   * 取消当前处理任务
   */
  const cancelProcessing = () => {
    if (isProcessing.value) {
      isProcessing.value = false;
      currentProgress.value = { current: 0, total: 0, currentTitle: '' };
      console.log('🚫 [文档处理] 用户取消了处理任务');
    }
  };

  /**
   * 重新向量化知识库文档
   * 删除现有的所有片段和向量，使用当前知识库配置重新切片并向量化
   */
  const revectorizeDocument = async (
    knowledgeDocumentId: number,
    knowledgeBaseId: number,
    callbacks?: ProcessCallbacks,
  ): Promise<DocumentProcessResult> => {
    try {
      console.log('🔄 [重新向量化] 开始重新向量化文档:', {
        knowledgeDocumentId,
        knowledgeBaseId,
      });

      isProcessing.value = true;
      currentProgress.value = {
        current: 0,
        total: 1,
        currentTitle: '准备重新向量化...',
      };

      // 1. 获取知识库文档信息（通过API调用）
      // 使用类似 callKnowledgeApi 的方式安全处理 Promise
      const callKnowledgeApiLocal = <T>(method: string, ...args: unknown[]): Promise<T> => {
        return new Promise((resolve, reject) => {
          if (!window.knowledgeApi || typeof window.knowledgeApi[method] !== 'function') {
            reject(new Error(`KnowledgeApi method ${method} not available`));
            return;
          }

          try {
            const result = window.knowledgeApi[method](...args);

            // Qt WebChannel 方法可能返回 Promise，也可能直接返回值
            const handleResult = (data: unknown) => {
              if (typeof data === 'string') {
                try {
                  const parsed = JSON.parse(data);
                  if (parsed.success) {
                    resolve(parsed.data || parsed);
                  } else {
                    reject(new Error(parsed.message || `${method} failed`));
                  }
                } catch (parseError) {
                  reject(new Error(`JSON parse error: ${parseError}`));
                }
              } else {
                resolve(data as T);
              }
            };

            // 检查结果是否为Promise
            if (result && typeof result.then === 'function') {
              result.then(handleResult).catch(reject);
            } else {
              handleResult(result);
            }
          } catch (error) {
            reject(error instanceof Error ? error : new Error(String(error)));
          }
        });
      };

      interface KnowledgeDocument {
        title?: string;
        content?: string;
        [key: string]: unknown;
      }

      const document = await callKnowledgeApiLocal<KnowledgeDocument>(
        'getDocument',
        knowledgeDocumentId.toString(),
      );
      const title = document.title || '无标题文档';
      console.log('📄 [重新向量化] 获取文档信息:', title);

      // 2. 调用后端API删除现有片段
      console.log('🗑️ [重新向量化] 删除现有片段...');
      const deleteResult = await callKnowledgeApiLocal<{
        deleted_chunks: number;
        total_chunks_found: number;
      }>('deleteDocumentChunks', knowledgeDocumentId.toString());

      console.log('✅ [重新向量化] 删除现有片段完成:', {
        deletedChunks: deleteResult.deleted_chunks,
        totalFound: deleteResult.total_chunks_found,
      });

      // 通知删除完成，触发统计数据更新
      callbacks?.onChunkingProgress?.(knowledgeDocumentId, {
        stage: '删除完成，准备重新切割',
        percentage: 25,
      });

      // 3. 通知处理完成
      callbacks?.onChunkingCompleted?.(knowledgeDocumentId, {
        chunkCount: 0, // 删除操作不产生新的chunks，后续由processDocumentChunking生成
      });

      // 4. 等待向量化完成（通过监听器）
      let finalChunkCount = 0;
      const vectorizationPromise = new Promise<void>((resolve) => {
        const cleanup = documentChunking.setupVectorizationListener(
          (docId: number, chunkCount: number) => {
            if (docId === knowledgeDocumentId) {
              console.log('🎯 [重新向量化] 向量化完成:', { docId, chunkCount });
              finalChunkCount = chunkCount;
              callbacks?.onVectorizationCompleted?.(docId, chunkCount);
              cleanup();
              resolve();
            }
          },
        );

        // 设置超时，避免无限等待
        setTimeout(() => {
          console.warn('⚠️ [重新向量化] 向量化超时，但重新生成已完成');
          cleanup();
          resolve();
        }, 30000); // 30秒超时
      });

      // 等待向量化完成
      await vectorizationPromise;

      const processResult: DocumentProcessResult = {
        documentId: knowledgeDocumentId,
        originalDocumentId: knowledgeDocumentId,
        title,
        success: true,
        knowledgeDocumentId,
      };

      callbacks?.onDocumentCompleted?.(processResult);

      $q.notify({
        type: 'positive',
        message: $t('src.composeables.useDocumentToKnowledge.revectorize_completed', {
          title,
          chunks: finalChunkCount || 0,
        }),
        position: 'top',
        timeout: 5000,
      });

      console.log('🎉 [重新向量化] 完成:', processResult);
      return processResult;
    } catch (error) {
      console.error('❌ [重新向量化] 失败:', error);

      const errorMessage = error instanceof Error ? error.message : '未知错误';
      callbacks?.onError?.(knowledgeDocumentId, errorMessage, 'chunking');

      $q.notify({
        type: 'negative',
        message: $t('src.composeables.useDocumentToKnowledge.revectorize_failed', {
          error: errorMessage,
        }),
        position: 'top',
      });

      return {
        documentId: knowledgeDocumentId,
        originalDocumentId: knowledgeDocumentId,
        title: '未知文档',
        success: false,
        error: errorMessage,
      };
    } finally {
      isProcessing.value = false;
      currentProgress.value = {
        current: 0,
        total: 0,
        currentTitle: '',
      };
    }
  };

  return {
    // 状态
    isProcessing,
    currentProgress,

    // 方法
    processDocument,
    processBatchDocuments,
    revectorizeDocument,
    cancelProcessing,
  };
}
