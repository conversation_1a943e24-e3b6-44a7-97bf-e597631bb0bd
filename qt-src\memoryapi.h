#ifndef MEMORYAPI_H
#define MEMORYAPI_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QTimer>
#include <QProcess>
#include <QString>
#include <QUrl>

/**
 * @class MemoryAPI
 * @brief Qt端的Memory API，用于与Python Memory Service通信
 * 
 * 该类负责：
 * 1. 管理Python Memory Service进程
 * 2. 提供HTTP API接口访问记忆功能
 * 3. 处理服务状态监控和重连
 */
class MemoryAPI : public QObject
{
    Q_OBJECT

public:
    explicit MemoryAPI(QObject *parent = nullptr);
    ~MemoryAPI();

    /**
     * @brief 服务状态枚举
     */
    enum ServiceStatus {
        Stopped,        // 服务已停止
        Starting,       // 服务启动中
        Running,        // 服务运行中
        Stopping,       // 服务停止中
        Error           // 服务错误
    };
    Q_ENUM(ServiceStatus)

    /**
     * @brief 初始化Memory API
     * @param serviceUrl 服务URL (默认: http://127.0.0.1:38899)
     * @param autoStart 是否自动启动服务
     * @return 是否初始化成功
     */
    bool initialize(const QString &serviceUrl = "http://127.0.0.1:38899", bool autoStart = true);

    /**
     * @brief 启动Memory Service
     * @param executablePath 可执行文件路径
     * @return 是否启动成功
     */
    bool startService(const QString &executablePath = "");

    /**
     * @brief 停止Memory Service
     */
    void stopService();

    /**
     * @brief 检查服务健康状态
     */
    void checkHealth();

    /**
     * @brief 添加记忆
     * @param messages 消息列表（JSON格式）
     * @param userId 用户ID
     * @param agentId 代理ID
     * @param sessionId 会话ID
     * @param metadata 元数据（JSON格式）
     */
    void addMemory(const QJsonArray &messages, 
                   const QString &userId = "default_user",
                   const QString &agentId = "inkcop_assistant",
                   const QString &sessionId = "",
                   const QJsonObject &metadata = QJsonObject());

    /**
     * @brief 搜索记忆
     * @param query 搜索查询
     * @param userId 用户ID
     * @param agentId 代理ID
     * @param sessionId 会话ID
     * @param limit 返回结果数量限制
     */
    void searchMemory(const QString &query,
                      const QString &userId = "default_user",
                      const QString &agentId = "inkcop_assistant",
                      const QString &sessionId = "",
                      int limit = 10);

    /**
     * @brief 获取所有记忆
     * @param userId 用户ID
     * @param agentId 代理ID
     * @param sessionId 会话ID
     * @param limit 返回结果数量限制
     */
    void getAllMemories(const QString &userId = "default_user",
                        const QString &agentId = "inkcop_assistant",
                        const QString &sessionId = "",
                        int limit = 100);

    /**
     * @brief 删除记忆
     * @param memoryId 记忆ID
     */
    void deleteMemory(const QString &memoryId);

    /**
     * @brief 清除记忆
     * @param userId 用户ID
     * @param agentId 代理ID
     * @param sessionId 会话ID
     */
    void clearMemories(const QString &userId = "default_user",
                       const QString &agentId = "inkcop_assistant",
                       const QString &sessionId = "");

    /**
     * @brief 获取上下文记忆
     * @param documentContent 文档内容
     * @param conversationHistory 对话历史（JSON格式）
     * @param userId 用户ID
     * @param limit 返回结果数量限制
     */
    void getContextMemories(const QString &documentContent,
                            const QJsonArray &conversationHistory = QJsonArray(),
                            const QString &userId = "default_user",
                            int limit = 5);

    // Getter方法
    ServiceStatus getServiceStatus() const { return m_serviceStatus; }
    QString getServiceUrl() const { return m_serviceUrl; }
    bool isServiceRunning() const { return m_serviceStatus == Running; }

signals:
    /**
     * @brief 服务状态变化信号
     * @param status 新的服务状态
     */
    void serviceStatusChanged(ServiceStatus status);

    /**
     * @brief 健康检查完成信号
     * @param isHealthy 服务是否健康
     */
    void healthCheckCompleted(bool isHealthy);

    /**
     * @brief 添加记忆完成信号
     * @param success 是否成功
     * @param result 结果JSON对象
     */
    void memoryAdded(bool success, const QJsonObject &result);

    /**
     * @brief 搜索记忆完成信号
     * @param success 是否成功
     * @param results 结果数组
     */
    void memorySearchCompleted(bool success, const QJsonArray &results);

    /**
     * @brief 获取所有记忆完成信号
     * @param success 是否成功
     * @param memories 记忆数组
     */
    void allMemoriesRetrieved(bool success, const QJsonArray &memories);

    /**
     * @brief 删除记忆完成信号
     * @param success 是否成功
     * @param memoryId 删除的记忆ID
     */
    void memoryDeleted(bool success, const QString &memoryId);

    /**
     * @brief 清除记忆完成信号
     * @param success 是否成功
     * @param count 清除的记忆数量
     */
    void memoriesCleared(bool success, int count);

    /**
     * @brief 上下文记忆获取完成信号
     * @param success 是否成功
     * @param contextMemories 上下文记忆数组
     */
    void contextMemoriesRetrieved(bool success, const QJsonArray &contextMemories);

    /**
     * @brief 错误信号
     * @param error 错误信息
     */
    void error(const QString &error);

private slots:
    /**
     * @brief 处理网络请求完成
     */
    void onNetworkReplyFinished();

    /**
     * @brief 处理Python进程完成
     * @param exitCode 退出代码
     * @param exitStatus 退出状态
     */
    void onServiceProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);

    /**
     * @brief 处理Python进程错误
     * @param error 进程错误
     */
    void onServiceProcessError(QProcess::ProcessError error);

    /**
     * @brief 健康检查定时器超时
     */
    void onHealthCheckTimer();

private:
    /**
     * @brief 发送HTTP请求
     * @param method HTTP方法
     * @param endpoint API端点
     * @param data 请求数据
     * @param requestType 请求类型标识
     */
    void sendRequest(const QString &method, 
                     const QString &endpoint, 
                     const QJsonObject &data = QJsonObject(),
                     const QString &requestType = "");

    /**
     * @brief 处理API响应
     * @param reply 网络回复
     * @param requestType 请求类型
     */
    void handleApiResponse(QNetworkReply *reply, const QString &requestType);

    /**
     * @brief 设置服务状态
     * @param status 新状态
     */
    void setServiceStatus(ServiceStatus status);

    /**
     * @brief 查找Memory Service可执行文件
     * @return 可执行文件路径
     */
    QString findServiceExecutable();

    /**
     * @brief 等待服务启动
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否启动成功
     */
    bool waitForServiceStart(int timeoutMs = 10000);

private:
    QNetworkAccessManager *m_networkManager;   // 网络管理器
    QProcess *m_serviceProcess;                // Python服务进程
    QTimer *m_healthCheckTimer;                // 健康检查定时器
    
    QString m_serviceUrl;                      // 服务URL
    ServiceStatus m_serviceStatus;             // 服务状态
    
    // 请求映射，用于关联请求和响应
    QMap<QNetworkReply*, QString> m_pendingRequests;
    
    // 配置参数
    int m_healthCheckInterval;                 // 健康检查间隔（毫秒）
    int m_requestTimeout;                      // 请求超时时间（毫秒）
    QString m_serviceExecutablePath;           // 服务可执行文件路径
};

#endif // MEMORYAPI_H 