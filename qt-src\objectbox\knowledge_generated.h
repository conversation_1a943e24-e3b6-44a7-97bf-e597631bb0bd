// automatically generated by the <PERSON>Buffers compiler, do not modify

#ifndef FLATBUFFERS_GENERATED_KNOWLEDGE_INKCOP_KNOWLEDGE_H_
#define FLATBUFFERS_GENERATED_KNOWLEDGE_INKCOP_KNOWLEDGE_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 25 &&
                  FLATBUFFERS_VERSION_MINOR == 2 &&
                  FLATBUFFERS_VERSION_REVISION == 10,
              "Non-compatible flatbuffers version included");

namespace InkCop
{
  namespace Knowledge
  {

    struct KnowledgeBase;
    struct KnowledgeBaseBuilder;
    struct KnowledgeBaseT;

    struct KnowledgeDocument;
    struct KnowledgeDocumentBuilder;
    struct KnowledgeDocumentT;

    struct KnowledgeChunk;
    struct KnowledgeChunkBuilder;
    struct KnowledgeChunkT;

    struct KnowledgeQuery;
    struct KnowledgeQueryBuilder;
    struct KnowledgeQueryT;

    struct KnowledgeBaseT : public ::flatbuffers::NativeTable
    {
      typedef KnowledgeBase TableType;
      uint64_t id = 0;
      std::string name{};
      std::string description{};
      std::string user_id{};
      std::string settings{};
      uint64_t created_at = 0;
      uint64_t updated_at = 0;
    };

    struct KnowledgeBase FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table
    {
      typedef KnowledgeBaseT NativeTableType;
      typedef KnowledgeBaseBuilder Builder;
      enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE
      {
        VT_ID = 4,
        VT_NAME = 6,
        VT_DESCRIPTION = 8,
        VT_USER_ID = 10,
        VT_SETTINGS = 12,
        VT_CREATED_AT = 14,
        VT_UPDATED_AT = 16
      };
      uint64_t id() const
      {
        return GetField<uint64_t>(VT_ID, 0);
      }
      const ::flatbuffers::String *name() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_NAME);
      }
      const ::flatbuffers::String *description() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
      }
      const ::flatbuffers::String *user_id() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_USER_ID);
      }
      const ::flatbuffers::String *settings() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_SETTINGS);
      }
      uint64_t created_at() const
      {
        return GetField<uint64_t>(VT_CREATED_AT, 0);
      }
      uint64_t updated_at() const
      {
        return GetField<uint64_t>(VT_UPDATED_AT, 0);
      }
      bool Verify(::flatbuffers::Verifier &verifier) const
      {
        return VerifyTableStart(verifier) &&
               VerifyField<uint64_t>(verifier, VT_ID, 8) &&
               VerifyOffset(verifier, VT_NAME) &&
               verifier.VerifyString(name()) &&
               VerifyOffset(verifier, VT_DESCRIPTION) &&
               verifier.VerifyString(description()) &&
               VerifyOffset(verifier, VT_USER_ID) &&
               verifier.VerifyString(user_id()) &&
               VerifyOffset(verifier, VT_SETTINGS) &&
               verifier.VerifyString(settings()) &&
               VerifyField<uint64_t>(verifier, VT_CREATED_AT, 8) &&
               VerifyField<uint64_t>(verifier, VT_UPDATED_AT, 8) &&
               verifier.EndTable();
      }
      KnowledgeBaseT *UnPack(const ::flatbuffers::resolver_function_t *_resolver = nullptr) const;
      void UnPackTo(KnowledgeBaseT *_o, const ::flatbuffers::resolver_function_t *_resolver = nullptr) const;
      static ::flatbuffers::Offset<KnowledgeBase> Pack(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeBaseT *_o, const ::flatbuffers::rehasher_function_t *_rehasher = nullptr);
    };

    struct KnowledgeBaseBuilder
    {
      typedef KnowledgeBase Table;
      ::flatbuffers::FlatBufferBuilder &fbb_;
      ::flatbuffers::uoffset_t start_;
      void add_id(uint64_t id)
      {
        fbb_.AddElement<uint64_t>(KnowledgeBase::VT_ID, id, 0);
      }
      void add_name(::flatbuffers::Offset<::flatbuffers::String> name)
      {
        fbb_.AddOffset(KnowledgeBase::VT_NAME, name);
      }
      void add_description(::flatbuffers::Offset<::flatbuffers::String> description)
      {
        fbb_.AddOffset(KnowledgeBase::VT_DESCRIPTION, description);
      }
      void add_user_id(::flatbuffers::Offset<::flatbuffers::String> user_id)
      {
        fbb_.AddOffset(KnowledgeBase::VT_USER_ID, user_id);
      }
      void add_settings(::flatbuffers::Offset<::flatbuffers::String> settings)
      {
        fbb_.AddOffset(KnowledgeBase::VT_SETTINGS, settings);
      }
      void add_created_at(uint64_t created_at)
      {
        fbb_.AddElement<uint64_t>(KnowledgeBase::VT_CREATED_AT, created_at, 0);
      }
      void add_updated_at(uint64_t updated_at)
      {
        fbb_.AddElement<uint64_t>(KnowledgeBase::VT_UPDATED_AT, updated_at, 0);
      }
      explicit KnowledgeBaseBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
          : fbb_(_fbb)
      {
        start_ = fbb_.StartTable();
      }
      ::flatbuffers::Offset<KnowledgeBase> Finish()
      {
        const auto end = fbb_.EndTable(start_);
        auto o = ::flatbuffers::Offset<KnowledgeBase>(end);
        return o;
      }
    };

    inline ::flatbuffers::Offset<KnowledgeBase> CreateKnowledgeBase(
        ::flatbuffers::FlatBufferBuilder &_fbb,
        uint64_t id = 0,
        ::flatbuffers::Offset<::flatbuffers::String> name = 0,
        ::flatbuffers::Offset<::flatbuffers::String> description = 0,
        ::flatbuffers::Offset<::flatbuffers::String> user_id = 0,
        ::flatbuffers::Offset<::flatbuffers::String> settings = 0,
        uint64_t created_at = 0,
        uint64_t updated_at = 0)
    {
      KnowledgeBaseBuilder builder_(_fbb);
      builder_.add_updated_at(updated_at);
      builder_.add_created_at(created_at);
      builder_.add_id(id);
      builder_.add_settings(settings);
      builder_.add_user_id(user_id);
      builder_.add_description(description);
      builder_.add_name(name);
      return builder_.Finish();
    }

    inline ::flatbuffers::Offset<KnowledgeBase> CreateKnowledgeBaseDirect(
        ::flatbuffers::FlatBufferBuilder &_fbb,
        uint64_t id = 0,
        const char *name = nullptr,
        const char *description = nullptr,
        const char *user_id = nullptr,
        const char *settings = nullptr,
        uint64_t created_at = 0,
        uint64_t updated_at = 0)
    {
      auto name__ = name ? _fbb.CreateString(name) : 0;
      auto description__ = description ? _fbb.CreateString(description) : 0;
      auto user_id__ = user_id ? _fbb.CreateString(user_id) : 0;
      auto settings__ = settings ? _fbb.CreateString(settings) : 0;
      return InkCop::Knowledge::CreateKnowledgeBase(
          _fbb,
          id,
          name__,
          description__,
          user_id__,
          settings__,
          created_at,
          updated_at);
    }

    ::flatbuffers::Offset<KnowledgeBase> CreateKnowledgeBase(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeBaseT *_o, const ::flatbuffers::rehasher_function_t *_rehasher = nullptr);

    struct KnowledgeDocumentT : public ::flatbuffers::NativeTable
    {
      typedef KnowledgeDocument TableType;
      uint64_t id = 0;
      uint64_t knowledge_base_id = 0;
      std::string title{};
      std::string content{};
      std::string file_path{};
      std::string file_type{};
      std::string source_type{};
      std::string metadata{};
      uint64_t created_at = 0;
      uint64_t updated_at = 0;
    };

    struct KnowledgeDocument FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table
    {
      typedef KnowledgeDocumentT NativeTableType;
      typedef KnowledgeDocumentBuilder Builder;
      enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE
      {
        VT_ID = 4,
        VT_KNOWLEDGE_BASE_ID = 6,
        VT_TITLE = 8,
        VT_CONTENT = 10,
        VT_FILE_PATH = 12,
        VT_FILE_TYPE = 14,
        VT_SOURCE_TYPE = 16,
        VT_METADATA = 18,
        VT_CREATED_AT = 20,
        VT_UPDATED_AT = 22
      };
      uint64_t id() const
      {
        return GetField<uint64_t>(VT_ID, 0);
      }
      uint64_t knowledge_base_id() const
      {
        return GetField<uint64_t>(VT_KNOWLEDGE_BASE_ID, 0);
      }
      const ::flatbuffers::String *title() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_TITLE);
      }
      const ::flatbuffers::String *content() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_CONTENT);
      }
      const ::flatbuffers::String *file_path() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_FILE_PATH);
      }
      const ::flatbuffers::String *file_type() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_FILE_TYPE);
      }
      const ::flatbuffers::String *source_type() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_SOURCE_TYPE);
      }
      const ::flatbuffers::String *metadata() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_METADATA);
      }
      uint64_t created_at() const
      {
        return GetField<uint64_t>(VT_CREATED_AT, 0);
      }
      uint64_t updated_at() const
      {
        return GetField<uint64_t>(VT_UPDATED_AT, 0);
      }
      bool Verify(::flatbuffers::Verifier &verifier) const
      {
        return VerifyTableStart(verifier) &&
               VerifyField<uint64_t>(verifier, VT_ID, 8) &&
               VerifyField<uint64_t>(verifier, VT_KNOWLEDGE_BASE_ID, 8) &&
               VerifyOffset(verifier, VT_TITLE) &&
               verifier.VerifyString(title()) &&
               VerifyOffset(verifier, VT_CONTENT) &&
               verifier.VerifyString(content()) &&
               VerifyOffset(verifier, VT_FILE_PATH) &&
               verifier.VerifyString(file_path()) &&
               VerifyOffset(verifier, VT_FILE_TYPE) &&
               verifier.VerifyString(file_type()) &&
               VerifyOffset(verifier, VT_SOURCE_TYPE) &&
               verifier.VerifyString(source_type()) &&
               VerifyOffset(verifier, VT_METADATA) &&
               verifier.VerifyString(metadata()) &&
               VerifyField<uint64_t>(verifier, VT_CREATED_AT, 8) &&
               VerifyField<uint64_t>(verifier, VT_UPDATED_AT, 8) &&
               verifier.EndTable();
      }
      KnowledgeDocumentT *UnPack(const ::flatbuffers::resolver_function_t *_resolver = nullptr) const;
      void UnPackTo(KnowledgeDocumentT *_o, const ::flatbuffers::resolver_function_t *_resolver = nullptr) const;
      static ::flatbuffers::Offset<KnowledgeDocument> Pack(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeDocumentT *_o, const ::flatbuffers::rehasher_function_t *_rehasher = nullptr);
    };

    struct KnowledgeDocumentBuilder
    {
      typedef KnowledgeDocument Table;
      ::flatbuffers::FlatBufferBuilder &fbb_;
      ::flatbuffers::uoffset_t start_;
      void add_id(uint64_t id)
      {
        fbb_.AddElement<uint64_t>(KnowledgeDocument::VT_ID, id, 0);
      }
      void add_knowledge_base_id(uint64_t knowledge_base_id)
      {
        fbb_.AddElement<uint64_t>(KnowledgeDocument::VT_KNOWLEDGE_BASE_ID, knowledge_base_id, 0);
      }
      void add_title(::flatbuffers::Offset<::flatbuffers::String> title)
      {
        fbb_.AddOffset(KnowledgeDocument::VT_TITLE, title);
      }
      void add_content(::flatbuffers::Offset<::flatbuffers::String> content)
      {
        fbb_.AddOffset(KnowledgeDocument::VT_CONTENT, content);
      }
      void add_file_path(::flatbuffers::Offset<::flatbuffers::String> file_path)
      {
        fbb_.AddOffset(KnowledgeDocument::VT_FILE_PATH, file_path);
      }
      void add_file_type(::flatbuffers::Offset<::flatbuffers::String> file_type)
      {
        fbb_.AddOffset(KnowledgeDocument::VT_FILE_TYPE, file_type);
      }
      void add_source_type(::flatbuffers::Offset<::flatbuffers::String> source_type)
      {
        fbb_.AddOffset(KnowledgeDocument::VT_SOURCE_TYPE, source_type);
      }
      void add_metadata(::flatbuffers::Offset<::flatbuffers::String> metadata)
      {
        fbb_.AddOffset(KnowledgeDocument::VT_METADATA, metadata);
      }
      void add_created_at(uint64_t created_at)
      {
        fbb_.AddElement<uint64_t>(KnowledgeDocument::VT_CREATED_AT, created_at, 0);
      }
      void add_updated_at(uint64_t updated_at)
      {
        fbb_.AddElement<uint64_t>(KnowledgeDocument::VT_UPDATED_AT, updated_at, 0);
      }
      explicit KnowledgeDocumentBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
          : fbb_(_fbb)
      {
        start_ = fbb_.StartTable();
      }
      ::flatbuffers::Offset<KnowledgeDocument> Finish()
      {
        const auto end = fbb_.EndTable(start_);
        auto o = ::flatbuffers::Offset<KnowledgeDocument>(end);
        return o;
      }
    };

    inline ::flatbuffers::Offset<KnowledgeDocument> CreateKnowledgeDocument(
        ::flatbuffers::FlatBufferBuilder &_fbb,
        uint64_t id = 0,
        uint64_t knowledge_base_id = 0,
        ::flatbuffers::Offset<::flatbuffers::String> title = 0,
        ::flatbuffers::Offset<::flatbuffers::String> content = 0,
        ::flatbuffers::Offset<::flatbuffers::String> file_path = 0,
        ::flatbuffers::Offset<::flatbuffers::String> file_type = 0,
        ::flatbuffers::Offset<::flatbuffers::String> source_type = 0,
        ::flatbuffers::Offset<::flatbuffers::String> metadata = 0,
        uint64_t created_at = 0,
        uint64_t updated_at = 0)
    {
      KnowledgeDocumentBuilder builder_(_fbb);
      builder_.add_updated_at(updated_at);
      builder_.add_created_at(created_at);
      builder_.add_knowledge_base_id(knowledge_base_id);
      builder_.add_id(id);
      builder_.add_metadata(metadata);
      builder_.add_source_type(source_type);
      builder_.add_file_type(file_type);
      builder_.add_file_path(file_path);
      builder_.add_content(content);
      builder_.add_title(title);
      return builder_.Finish();
    }

    inline ::flatbuffers::Offset<KnowledgeDocument> CreateKnowledgeDocumentDirect(
        ::flatbuffers::FlatBufferBuilder &_fbb,
        uint64_t id = 0,
        uint64_t knowledge_base_id = 0,
        const char *title = nullptr,
        const char *content = nullptr,
        const char *file_path = nullptr,
        const char *file_type = nullptr,
        const char *source_type = nullptr,
        const char *metadata = nullptr,
        uint64_t created_at = 0,
        uint64_t updated_at = 0)
    {
      auto title__ = title ? _fbb.CreateString(title) : 0;
      auto content__ = content ? _fbb.CreateString(content) : 0;
      auto file_path__ = file_path ? _fbb.CreateString(file_path) : 0;
      auto file_type__ = file_type ? _fbb.CreateString(file_type) : 0;
      auto source_type__ = source_type ? _fbb.CreateString(source_type) : 0;
      auto metadata__ = metadata ? _fbb.CreateString(metadata) : 0;
      return InkCop::Knowledge::CreateKnowledgeDocument(
          _fbb,
          id,
          knowledge_base_id,
          title__,
          content__,
          file_path__,
          file_type__,
          source_type__,
          metadata__,
          created_at,
          updated_at);
    }

    ::flatbuffers::Offset<KnowledgeDocument> CreateKnowledgeDocument(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeDocumentT *_o, const ::flatbuffers::rehasher_function_t *_rehasher = nullptr);

    struct KnowledgeChunkT : public ::flatbuffers::NativeTable
    {
      typedef KnowledgeChunk TableType;
      uint64_t id = 0;
      uint64_t knowledge_document_id = 0;
      uint32_t chunk_index = 0;
      std::string content{};
      std::vector<float> embedding{};
      std::string metadata{};
      uint64_t created_at = 0;
      bool is_vectorized = false; // 添加缺失的字段
    };

    struct KnowledgeChunk FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table
    {
      typedef KnowledgeChunkT NativeTableType;
      typedef KnowledgeChunkBuilder Builder;
      enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE
      {
        VT_ID = 4,
        VT_KNOWLEDGE_DOCUMENT_ID = 6,
        VT_CHUNK_INDEX = 8,
        VT_CONTENT = 10,
        VT_EMBEDDING = 12,
        VT_METADATA = 14,
        VT_CREATED_AT = 16,
        VT_IS_VECTORIZED = 18
      };
      uint64_t id() const
      {
        return GetField<uint64_t>(VT_ID, 0);
      }
      uint64_t knowledge_document_id() const
      {
        return GetField<uint64_t>(VT_KNOWLEDGE_DOCUMENT_ID, 0);
      }
      uint32_t chunk_index() const
      {
        return GetField<uint32_t>(VT_CHUNK_INDEX, 0);
      }
      const ::flatbuffers::String *content() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_CONTENT);
      }
      const ::flatbuffers::Vector<float> *embedding() const
      {
        return GetPointer<const ::flatbuffers::Vector<float> *>(VT_EMBEDDING);
      }
      const ::flatbuffers::String *metadata() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_METADATA);
      }
      uint64_t created_at() const
      {
        return GetField<uint64_t>(VT_CREATED_AT, 0);
      }
      bool is_vectorized() const
      {
        return GetField<uint8_t>(VT_IS_VECTORIZED, 0) != 0;
      }
      bool Verify(::flatbuffers::Verifier &verifier) const
      {
        return VerifyTableStart(verifier) &&
               VerifyField<uint64_t>(verifier, VT_ID, 8) &&
               VerifyField<uint64_t>(verifier, VT_KNOWLEDGE_DOCUMENT_ID, 8) &&
               VerifyField<uint32_t>(verifier, VT_CHUNK_INDEX, 4) &&
               VerifyOffset(verifier, VT_CONTENT) &&
               verifier.VerifyString(content()) &&
               VerifyOffset(verifier, VT_EMBEDDING) &&
               verifier.VerifyVector(embedding()) &&
               VerifyOffset(verifier, VT_METADATA) &&
               verifier.VerifyString(metadata()) &&
               VerifyField<uint64_t>(verifier, VT_CREATED_AT, 8) &&
               VerifyField<uint8_t>(verifier, VT_IS_VECTORIZED, 1) &&
               verifier.EndTable();
      }
      KnowledgeChunkT *UnPack(const ::flatbuffers::resolver_function_t *_resolver = nullptr) const;
      void UnPackTo(KnowledgeChunkT *_o, const ::flatbuffers::resolver_function_t *_resolver = nullptr) const;
      static ::flatbuffers::Offset<KnowledgeChunk> Pack(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeChunkT *_o, const ::flatbuffers::rehasher_function_t *_rehasher = nullptr);
    };

    struct KnowledgeChunkBuilder
    {
      typedef KnowledgeChunk Table;
      ::flatbuffers::FlatBufferBuilder &fbb_;
      ::flatbuffers::uoffset_t start_;
      void add_id(uint64_t id)
      {
        fbb_.AddElement<uint64_t>(KnowledgeChunk::VT_ID, id, 0);
      }
      void add_knowledge_document_id(uint64_t knowledge_document_id)
      {
        fbb_.AddElement<uint64_t>(KnowledgeChunk::VT_KNOWLEDGE_DOCUMENT_ID, knowledge_document_id, 0);
      }
      void add_chunk_index(uint32_t chunk_index)
      {
        fbb_.AddElement<uint32_t>(KnowledgeChunk::VT_CHUNK_INDEX, chunk_index, 0);
      }
      void add_content(::flatbuffers::Offset<::flatbuffers::String> content)
      {
        fbb_.AddOffset(KnowledgeChunk::VT_CONTENT, content);
      }
      void add_embedding(::flatbuffers::Offset<::flatbuffers::Vector<float>> embedding)
      {
        fbb_.AddOffset(KnowledgeChunk::VT_EMBEDDING, embedding);
      }
      void add_metadata(::flatbuffers::Offset<::flatbuffers::String> metadata)
      {
        fbb_.AddOffset(KnowledgeChunk::VT_METADATA, metadata);
      }
      void add_created_at(uint64_t created_at)
      {
        fbb_.AddElement<uint64_t>(KnowledgeChunk::VT_CREATED_AT, created_at, 0);
      }
      void add_is_vectorized(bool is_vectorized)
      {
        fbb_.AddElement<uint8_t>(KnowledgeChunk::VT_IS_VECTORIZED, static_cast<uint8_t>(is_vectorized), 0);
      }
      explicit KnowledgeChunkBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
          : fbb_(_fbb)
      {
        start_ = fbb_.StartTable();
      }
      ::flatbuffers::Offset<KnowledgeChunk> Finish()
      {
        const auto end = fbb_.EndTable(start_);
        auto o = ::flatbuffers::Offset<KnowledgeChunk>(end);
        return o;
      }
    };

    inline ::flatbuffers::Offset<KnowledgeChunk> CreateKnowledgeChunk(
        ::flatbuffers::FlatBufferBuilder &_fbb,
        uint64_t id = 0,
        uint64_t knowledge_document_id = 0,
        uint32_t chunk_index = 0,
        ::flatbuffers::Offset<::flatbuffers::String> content = 0,
        ::flatbuffers::Offset<::flatbuffers::Vector<float>> embedding = 0,
        ::flatbuffers::Offset<::flatbuffers::String> metadata = 0,
        uint64_t created_at = 0,
        bool is_vectorized = false)
    {
      KnowledgeChunkBuilder builder_(_fbb);
      builder_.add_is_vectorized(is_vectorized);
      builder_.add_created_at(created_at);
      builder_.add_knowledge_document_id(knowledge_document_id);
      builder_.add_id(id);
      builder_.add_metadata(metadata);
      builder_.add_embedding(embedding);
      builder_.add_content(content);
      builder_.add_chunk_index(chunk_index);
      return builder_.Finish();
    }

    inline ::flatbuffers::Offset<KnowledgeChunk> CreateKnowledgeChunkDirect(
        ::flatbuffers::FlatBufferBuilder &_fbb,
        uint64_t id = 0,
        uint64_t knowledge_document_id = 0,
        uint32_t chunk_index = 0,
        const char *content = nullptr,
        const std::vector<float> *embedding = nullptr,
        const char *metadata = nullptr,
        uint64_t created_at = 0,
        bool is_vectorized = false)
    {
      auto content__ = content ? _fbb.CreateString(content) : 0;
      auto embedding__ = embedding ? _fbb.CreateVector<float>(*embedding) : 0;
      auto metadata__ = metadata ? _fbb.CreateString(metadata) : 0;
      return InkCop::Knowledge::CreateKnowledgeChunk(
          _fbb,
          id,
          knowledge_document_id,
          chunk_index,
          content__,
          embedding__,
          metadata__,
          created_at,
          is_vectorized);
    }

    ::flatbuffers::Offset<KnowledgeChunk> CreateKnowledgeChunk(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeChunkT *_o, const ::flatbuffers::rehasher_function_t *_rehasher = nullptr);

    struct KnowledgeQueryT : public ::flatbuffers::NativeTable
    {
      typedef KnowledgeQuery TableType;
      uint64_t id = 0;
      uint64_t knowledge_base_id = 0;
      std::string query_text{};
      std::vector<float> query_embedding{};
      std::string results{};
      uint64_t created_at = 0;
    };

    struct KnowledgeQuery FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table
    {
      typedef KnowledgeQueryT NativeTableType;
      typedef KnowledgeQueryBuilder Builder;
      enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE
      {
        VT_ID = 4,
        VT_KNOWLEDGE_BASE_ID = 6,
        VT_QUERY_TEXT = 8,
        VT_QUERY_EMBEDDING = 10,
        VT_RESULTS = 12,
        VT_CREATED_AT = 14
      };
      uint64_t id() const
      {
        return GetField<uint64_t>(VT_ID, 0);
      }
      uint64_t knowledge_base_id() const
      {
        return GetField<uint64_t>(VT_KNOWLEDGE_BASE_ID, 0);
      }
      const ::flatbuffers::String *query_text() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_QUERY_TEXT);
      }
      const ::flatbuffers::Vector<float> *query_embedding() const
      {
        return GetPointer<const ::flatbuffers::Vector<float> *>(VT_QUERY_EMBEDDING);
      }
      const ::flatbuffers::String *results() const
      {
        return GetPointer<const ::flatbuffers::String *>(VT_RESULTS);
      }
      uint64_t created_at() const
      {
        return GetField<uint64_t>(VT_CREATED_AT, 0);
      }
      bool Verify(::flatbuffers::Verifier &verifier) const
      {
        return VerifyTableStart(verifier) &&
               VerifyField<uint64_t>(verifier, VT_ID, 8) &&
               VerifyField<uint64_t>(verifier, VT_KNOWLEDGE_BASE_ID, 8) &&
               VerifyOffset(verifier, VT_QUERY_TEXT) &&
               verifier.VerifyString(query_text()) &&
               VerifyOffset(verifier, VT_QUERY_EMBEDDING) &&
               verifier.VerifyVector(query_embedding()) &&
               VerifyOffset(verifier, VT_RESULTS) &&
               verifier.VerifyString(results()) &&
               VerifyField<uint64_t>(verifier, VT_CREATED_AT, 8) &&
               verifier.EndTable();
      }
      KnowledgeQueryT *UnPack(const ::flatbuffers::resolver_function_t *_resolver = nullptr) const;
      void UnPackTo(KnowledgeQueryT *_o, const ::flatbuffers::resolver_function_t *_resolver = nullptr) const;
      static ::flatbuffers::Offset<KnowledgeQuery> Pack(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeQueryT *_o, const ::flatbuffers::rehasher_function_t *_rehasher = nullptr);
    };

    struct KnowledgeQueryBuilder
    {
      typedef KnowledgeQuery Table;
      ::flatbuffers::FlatBufferBuilder &fbb_;
      ::flatbuffers::uoffset_t start_;
      void add_id(uint64_t id)
      {
        fbb_.AddElement<uint64_t>(KnowledgeQuery::VT_ID, id, 0);
      }
      void add_knowledge_base_id(uint64_t knowledge_base_id)
      {
        fbb_.AddElement<uint64_t>(KnowledgeQuery::VT_KNOWLEDGE_BASE_ID, knowledge_base_id, 0);
      }
      void add_query_text(::flatbuffers::Offset<::flatbuffers::String> query_text)
      {
        fbb_.AddOffset(KnowledgeQuery::VT_QUERY_TEXT, query_text);
      }
      void add_query_embedding(::flatbuffers::Offset<::flatbuffers::Vector<float>> query_embedding)
      {
        fbb_.AddOffset(KnowledgeQuery::VT_QUERY_EMBEDDING, query_embedding);
      }
      void add_results(::flatbuffers::Offset<::flatbuffers::String> results)
      {
        fbb_.AddOffset(KnowledgeQuery::VT_RESULTS, results);
      }
      void add_created_at(uint64_t created_at)
      {
        fbb_.AddElement<uint64_t>(KnowledgeQuery::VT_CREATED_AT, created_at, 0);
      }
      explicit KnowledgeQueryBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
          : fbb_(_fbb)
      {
        start_ = fbb_.StartTable();
      }
      ::flatbuffers::Offset<KnowledgeQuery> Finish()
      {
        const auto end = fbb_.EndTable(start_);
        auto o = ::flatbuffers::Offset<KnowledgeQuery>(end);
        return o;
      }
    };

    inline ::flatbuffers::Offset<KnowledgeQuery> CreateKnowledgeQuery(
        ::flatbuffers::FlatBufferBuilder &_fbb,
        uint64_t id = 0,
        uint64_t knowledge_base_id = 0,
        ::flatbuffers::Offset<::flatbuffers::String> query_text = 0,
        ::flatbuffers::Offset<::flatbuffers::Vector<float>> query_embedding = 0,
        ::flatbuffers::Offset<::flatbuffers::String> results = 0,
        uint64_t created_at = 0)
    {
      KnowledgeQueryBuilder builder_(_fbb);
      builder_.add_created_at(created_at);
      builder_.add_knowledge_base_id(knowledge_base_id);
      builder_.add_id(id);
      builder_.add_results(results);
      builder_.add_query_embedding(query_embedding);
      builder_.add_query_text(query_text);
      return builder_.Finish();
    }

    inline ::flatbuffers::Offset<KnowledgeQuery> CreateKnowledgeQueryDirect(
        ::flatbuffers::FlatBufferBuilder &_fbb,
        uint64_t id = 0,
        uint64_t knowledge_base_id = 0,
        const char *query_text = nullptr,
        const std::vector<float> *query_embedding = nullptr,
        const char *results = nullptr,
        uint64_t created_at = 0)
    {
      auto query_text__ = query_text ? _fbb.CreateString(query_text) : 0;
      auto query_embedding__ = query_embedding ? _fbb.CreateVector<float>(*query_embedding) : 0;
      auto results__ = results ? _fbb.CreateString(results) : 0;
      return InkCop::Knowledge::CreateKnowledgeQuery(
          _fbb,
          id,
          knowledge_base_id,
          query_text__,
          query_embedding__,
          results__,
          created_at);
    }

    ::flatbuffers::Offset<KnowledgeQuery> CreateKnowledgeQuery(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeQueryT *_o, const ::flatbuffers::rehasher_function_t *_rehasher = nullptr);

    inline KnowledgeBaseT *KnowledgeBase::UnPack(const ::flatbuffers::resolver_function_t *_resolver) const
    {
      auto _o = std::unique_ptr<KnowledgeBaseT>(new KnowledgeBaseT());
      UnPackTo(_o.get(), _resolver);
      return _o.release();
    }

    inline void KnowledgeBase::UnPackTo(KnowledgeBaseT *_o, const ::flatbuffers::resolver_function_t *_resolver) const
    {
      (void)_o;
      (void)_resolver;
      {
        auto _e = id();
        _o->id = _e;
      }
      {
        auto _e = name();
        if (_e)
          _o->name = _e->str();
      }
      {
        auto _e = description();
        if (_e)
          _o->description = _e->str();
      }
      {
        auto _e = user_id();
        if (_e)
          _o->user_id = _e->str();
      }
      {
        auto _e = settings();
        if (_e)
          _o->settings = _e->str();
      }
      {
        auto _e = created_at();
        _o->created_at = _e;
      }
      {
        auto _e = updated_at();
        _o->updated_at = _e;
      }
    }

    inline ::flatbuffers::Offset<KnowledgeBase> KnowledgeBase::Pack(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeBaseT *_o, const ::flatbuffers::rehasher_function_t *_rehasher)
    {
      return CreateKnowledgeBase(_fbb, _o, _rehasher);
    }

    inline ::flatbuffers::Offset<KnowledgeBase> CreateKnowledgeBase(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeBaseT *_o, const ::flatbuffers::rehasher_function_t *_rehasher)
    {
      (void)_rehasher;
      (void)_o;
      struct _VectorArgs
      {
        ::flatbuffers::FlatBufferBuilder *__fbb;
        const KnowledgeBaseT *__o;
        const ::flatbuffers::rehasher_function_t *__rehasher;
      } _va = {&_fbb, _o, _rehasher};
      (void)_va;
      auto _id = _o->id;
      auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
      auto _description = _o->description.empty() ? 0 : _fbb.CreateString(_o->description);
      auto _user_id = _o->user_id.empty() ? 0 : _fbb.CreateString(_o->user_id);
      auto _settings = _o->settings.empty() ? 0 : _fbb.CreateString(_o->settings);
      auto _created_at = _o->created_at;
      auto _updated_at = _o->updated_at;
      return InkCop::Knowledge::CreateKnowledgeBase(
          _fbb,
          _id,
          _name,
          _description,
          _user_id,
          _settings,
          _created_at,
          _updated_at);
    }

    inline KnowledgeDocumentT *KnowledgeDocument::UnPack(const ::flatbuffers::resolver_function_t *_resolver) const
    {
      auto _o = std::unique_ptr<KnowledgeDocumentT>(new KnowledgeDocumentT());
      UnPackTo(_o.get(), _resolver);
      return _o.release();
    }

    inline void KnowledgeDocument::UnPackTo(KnowledgeDocumentT *_o, const ::flatbuffers::resolver_function_t *_resolver) const
    {
      (void)_o;
      (void)_resolver;
      {
        auto _e = id();
        _o->id = _e;
      }
      {
        auto _e = knowledge_base_id();
        _o->knowledge_base_id = _e;
      }
      {
        auto _e = title();
        if (_e)
          _o->title = _e->str();
      }
      {
        auto _e = content();
        if (_e)
          _o->content = _e->str();
      }
      {
        auto _e = file_path();
        if (_e)
          _o->file_path = _e->str();
      }
      {
        auto _e = file_type();
        if (_e)
          _o->file_type = _e->str();
      }
      {
        auto _e = source_type();
        if (_e)
          _o->source_type = _e->str();
      }
      {
        auto _e = metadata();
        if (_e)
          _o->metadata = _e->str();
      }
      {
        auto _e = created_at();
        _o->created_at = _e;
      }
      {
        auto _e = updated_at();
        _o->updated_at = _e;
      }
    }

    inline ::flatbuffers::Offset<KnowledgeDocument> KnowledgeDocument::Pack(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeDocumentT *_o, const ::flatbuffers::rehasher_function_t *_rehasher)
    {
      return CreateKnowledgeDocument(_fbb, _o, _rehasher);
    }

    inline ::flatbuffers::Offset<KnowledgeDocument> CreateKnowledgeDocument(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeDocumentT *_o, const ::flatbuffers::rehasher_function_t *_rehasher)
    {
      (void)_rehasher;
      (void)_o;
      struct _VectorArgs
      {
        ::flatbuffers::FlatBufferBuilder *__fbb;
        const KnowledgeDocumentT *__o;
        const ::flatbuffers::rehasher_function_t *__rehasher;
      } _va = {&_fbb, _o, _rehasher};
      (void)_va;
      auto _id = _o->id;
      auto _knowledge_base_id = _o->knowledge_base_id;
      auto _title = _o->title.empty() ? 0 : _fbb.CreateString(_o->title);
      auto _content = _o->content.empty() ? 0 : _fbb.CreateString(_o->content);
      auto _file_path = _o->file_path.empty() ? 0 : _fbb.CreateString(_o->file_path);
      auto _file_type = _o->file_type.empty() ? 0 : _fbb.CreateString(_o->file_type);
      auto _source_type = _o->source_type.empty() ? 0 : _fbb.CreateString(_o->source_type);
      auto _metadata = _o->metadata.empty() ? 0 : _fbb.CreateString(_o->metadata);
      auto _created_at = _o->created_at;
      auto _updated_at = _o->updated_at;
      return InkCop::Knowledge::CreateKnowledgeDocument(
          _fbb,
          _id,
          _knowledge_base_id,
          _title,
          _content,
          _file_path,
          _file_type,
          _source_type,
          _metadata,
          _created_at,
          _updated_at);
    }

    inline KnowledgeChunkT *KnowledgeChunk::UnPack(const ::flatbuffers::resolver_function_t *_resolver) const
    {
      auto _o = std::unique_ptr<KnowledgeChunkT>(new KnowledgeChunkT());
      UnPackTo(_o.get(), _resolver);
      return _o.release();
    }

    inline void KnowledgeChunk::UnPackTo(KnowledgeChunkT *_o, const ::flatbuffers::resolver_function_t *_resolver) const
    {
      (void)_o;
      (void)_resolver;
      {
        auto _e = id();
        _o->id = _e;
      }
      {
        auto _e = knowledge_document_id();
        _o->knowledge_document_id = _e;
      }
      {
        auto _e = chunk_index();
        _o->chunk_index = _e;
      }
      {
        auto _e = content();
        if (_e)
          _o->content = _e->str();
      }
      {
        auto _e = embedding();
        if (_e)
        {
          _o->embedding.resize(_e->size());
          for (::flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++)
          {
            _o->embedding[_i] = _e->Get(_i);
          }
        }
        else
        {
          _o->embedding.resize(0);
        }
      }
      {
        auto _e = metadata();
        if (_e)
          _o->metadata = _e->str();
      }
      {
        auto _e = created_at();
        _o->created_at = _e;
      }
      {
        auto _e = is_vectorized();
        _o->is_vectorized = _e;
      }
    }

    inline ::flatbuffers::Offset<KnowledgeChunk> KnowledgeChunk::Pack(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeChunkT *_o, const ::flatbuffers::rehasher_function_t *_rehasher)
    {
      return CreateKnowledgeChunk(_fbb, _o, _rehasher);
    }

    inline ::flatbuffers::Offset<KnowledgeChunk> CreateKnowledgeChunk(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeChunkT *_o, const ::flatbuffers::rehasher_function_t *_rehasher)
    {
      (void)_rehasher;
      (void)_o;
      struct _VectorArgs
      {
        ::flatbuffers::FlatBufferBuilder *__fbb;
        const KnowledgeChunkT *__o;
        const ::flatbuffers::rehasher_function_t *__rehasher;
      } _va = {&_fbb, _o, _rehasher};
      (void)_va;
      auto _id = _o->id;
      auto _knowledge_document_id = _o->knowledge_document_id;
      auto _chunk_index = _o->chunk_index;
      auto _content = _o->content.empty() ? 0 : _fbb.CreateString(_o->content);
      auto _embedding = _o->embedding.size() ? _fbb.CreateVector(_o->embedding) : 0;
      auto _metadata = _o->metadata.empty() ? 0 : _fbb.CreateString(_o->metadata);
      auto _created_at = _o->created_at;
      auto _is_vectorized = _o->is_vectorized;
      return InkCop::Knowledge::CreateKnowledgeChunk(
          _fbb,
          _id,
          _knowledge_document_id,
          _chunk_index,
          _content,
          _embedding,
          _metadata,
          _created_at,
          _is_vectorized);
    }

    inline KnowledgeQueryT *KnowledgeQuery::UnPack(const ::flatbuffers::resolver_function_t *_resolver) const
    {
      auto _o = std::unique_ptr<KnowledgeQueryT>(new KnowledgeQueryT());
      UnPackTo(_o.get(), _resolver);
      return _o.release();
    }

    inline void KnowledgeQuery::UnPackTo(KnowledgeQueryT *_o, const ::flatbuffers::resolver_function_t *_resolver) const
    {
      (void)_o;
      (void)_resolver;
      {
        auto _e = id();
        _o->id = _e;
      }
      {
        auto _e = knowledge_base_id();
        _o->knowledge_base_id = _e;
      }
      {
        auto _e = query_text();
        if (_e)
          _o->query_text = _e->str();
      }
      {
        auto _e = query_embedding();
        if (_e)
        {
          _o->query_embedding.resize(_e->size());
          for (::flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++)
          {
            _o->query_embedding[_i] = _e->Get(_i);
          }
        }
        else
        {
          _o->query_embedding.resize(0);
        }
      }
      {
        auto _e = results();
        if (_e)
          _o->results = _e->str();
      }
      {
        auto _e = created_at();
        _o->created_at = _e;
      }
    }

    inline ::flatbuffers::Offset<KnowledgeQuery> KnowledgeQuery::Pack(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeQueryT *_o, const ::flatbuffers::rehasher_function_t *_rehasher)
    {
      return CreateKnowledgeQuery(_fbb, _o, _rehasher);
    }

    inline ::flatbuffers::Offset<KnowledgeQuery> CreateKnowledgeQuery(::flatbuffers::FlatBufferBuilder &_fbb, const KnowledgeQueryT *_o, const ::flatbuffers::rehasher_function_t *_rehasher)
    {
      (void)_rehasher;
      (void)_o;
      struct _VectorArgs
      {
        ::flatbuffers::FlatBufferBuilder *__fbb;
        const KnowledgeQueryT *__o;
        const ::flatbuffers::rehasher_function_t *__rehasher;
      } _va = {&_fbb, _o, _rehasher};
      (void)_va;
      auto _id = _o->id;
      auto _knowledge_base_id = _o->knowledge_base_id;
      auto _query_text = _o->query_text.empty() ? 0 : _fbb.CreateString(_o->query_text);
      auto _query_embedding = _o->query_embedding.size() ? _fbb.CreateVector(_o->query_embedding) : 0;
      auto _results = _o->results.empty() ? 0 : _fbb.CreateString(_o->results);
      auto _created_at = _o->created_at;
      return InkCop::Knowledge::CreateKnowledgeQuery(
          _fbb,
          _id,
          _knowledge_base_id,
          _query_text,
          _query_embedding,
          _results,
          _created_at);
    }

  } // namespace Knowledge
} // namespace InkCop

#endif // FLATBUFFERS_GENERATED_KNOWLEDGE_INKCOP_KNOWLEDGE_H_
