<template>
  <div
    class="fit column items-center no-wrap gap-md q-py-md"
    style="padding-left: 6px; padding-right: 6px"
    @mousedown="handleMouseDown"
    @dblclick="handleDoubleClick"
  >
    <NotifyBlock />
    <template v-for="i in _apps" :key="i.key">
      <q-avatar
        v-if="i.type === 'app'"
        square
        :icon="i.icon"
        class="radius-sm cursor-pointer transition"
        :class="
          app === i.key
            ? `${$q.dark.isActive ? 'bg-primary-darker border' : 'bg-white text-grey-10 border-placeholder'} app-active`
            : ``
        "
        size="44px"
        @click="enterApp(i)"
      >
        <div v-if="app === i.key" class="flex absolute-left full-height q-pl-xs q-py-sm">
          <div style="width: 3px; background-color: var(--q-primary)"></div>
        </div>
      </q-avatar>
      <q-icon
        v-else-if="i.type === 'botton'"
        :color="app === i.key ? 'primary' : void 0"
        :name="i.icon"
        size="1.5rem"
        class="cursor-pointer transition"
        :class="app === i.key ? '' : 'app-btn'"
        @click="btnClick(i)"
      />
      <q-space v-else-if="i.type === 'space'" class="fit drag-indicator" />
    </template>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted } from 'vue';
import NotifyBlock from './NotifyBlock.vue';
import { useQuasar } from 'quasar';
import type { App } from 'src/env.d';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { useSelectiveWindowDrag } from 'src/utils/windowDrag';
import { useUiStore } from 'src/stores/ui';

const uiStore = useUiStore();
const router = useRouter();

const { app, apps } = storeToRefs(uiStore);

const $q = useQuasar();
const isDev = process.env.NODE_ENV === 'development';
const _apps = computed(() => (isDev ? apps.value : apps.value.filter((i) => !i.isDev)));

const enterApp = async (i: App) => {
  uiStore.setApp(i.key);
  if (i.key !== 'knowledge') {
    await router.push(i.link);
  }
};

const btnClick = (i: App) => {
  if (i.handler) {
    i.handler();
  } else {
    void enterApp(i);
  }
};

// 使用选择性窗口拖拽工具
const { handleMouseDown, handleDoubleClick } = useSelectiveWindowDrag();
onMounted(() => {
  const app = localStorage.getItem('app');
  if (app) {
    void enterApp(apps.value.find((i) => i.key === app));
  } else {
    void enterApp(apps.value[0]);
  }
});
</script>

<style lang="scss" scoped>
.app-btn {
  opacity: 0.7;
}
.app-btn:hover {
  opacity: 1;
}
.app-active {
  transition: all 0.3s ease;
  box-shadow: 5px 11px 30px 0 rgb(1 14 208 / 64%);
  .body--light & {
    box-shadow: 5px 11px 30px 0 rgba(0, 7, 20, 0.466);
  }
}
.app-active:hover {
  box-shadow: 0 0 20px 1px #ffbb763f;
  .body--light & {
    box-shadow: 5px 11px 30px 0 rgba(0, 33, 94, 0.564);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

.loader {
  animation: spin 5s linear;
  animation-play-state: paused;
}

.loader.loading {
  animation-play-state: running;
}
</style>
