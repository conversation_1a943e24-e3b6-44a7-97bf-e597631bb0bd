// Fluent System Icons 图标集定义
// 为 Quasar QIcon 组件提供 Fluent System Icons 支持

// 移除未使用的导入，直接定义我们需要的类型;

// 导入图标映射数据
import fluentRegularIcons from '../fonts/FluentSystemIcons-Regular.json';
import fluentLightIcons from '../fonts/FluentSystemIcons-Light.json';
import fluentFilledIcons from '../fonts/FluentSystemIcons-Filled.json';

// 图标变体类型
export type FluentIconVariant = 'regular' | 'light' | 'filled' | 'resizable';

// 图标映射接口
interface FluentIconMap {
  [key: string]: string;
}

// 自定义图标集类型定义
interface FluentIconSet {
  name: string;
  type?: {
    positive?: string;
    negative?: string;
    info?: string;
    warning?: string;
  };
  arrow?: {
    up?: string;
    right?: string;
    down?: string;
    left?: string;
    dropdown?: string;
  };
  chevron?: {
    left?: string;
    right?: string;
  };
}

// 创建图标名称映射函数
function createIconMap(
  iconData: Record<string, number>,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  variant: FluentIconVariant,
): FluentIconMap {
  const iconMap: FluentIconMap = {};

  Object.keys(iconData).forEach((key) => {
    // 移除 ic_fluent_ 前缀和变体后缀，提取核心图标名
    const cleanName = key
      .replace(/^ic_fluent_/, '')
      .replace(/_\d+_(regular|light|filled|resizable)$/, '');

    // 转换为 Unicode 字符
    const unicode = String.fromCharCode(iconData[key]);
    iconMap[cleanName] = unicode;
  });

  return iconMap;
}

// 创建各变体的图标映射
export const fluentRegularMap = createIconMap(fluentRegularIcons, 'regular');
export const fluentLightMap = createIconMap(fluentLightIcons, 'light');
export const fluentFilledMap = createIconMap(fluentFilledIcons, 'filled');

// 获取图标 Unicode 字符的函数
export function getFluentIcon(name: string, variant: FluentIconVariant = 'regular'): string {
  const maps = {
    regular: fluentRegularMap,
    light: fluentLightMap,
    filled: fluentFilledMap,
    resizable: fluentRegularMap, // 暂时使用 regular 作为 resizable 的映射
  };

  return maps[variant][name] || '';
}

// 创建 QIcon 组件可用的类名
export function createFluentIconClass(
  name: string,
  variant: FluentIconVariant = 'regular',
): string {
  return `fluent-icon fluent-${variant} fluent-${name}`;
}

// 常用图标的快捷映射
export const commonFluentIcons = {
  // 基础操作
  add: 'add',
  delete: 'delete',
  edit: 'edit',
  save: 'save',
  copy: 'copy',
  cut: 'cut',
  paste: 'clipboard_paste',
  undo: 'arrow_undo',
  redo: 'arrow_redo',

  // 导航
  arrow_left: 'arrow_left',
  arrow_right: 'arrow_right',
  arrow_up: 'arrow_up',
  arrow_down: 'arrow_down',
  chevron_left: 'chevron_left',
  chevron_right: 'chevron_right',
  chevron_up: 'chevron_up',
  chevron_down: 'chevron_down',
  back: 'arrow_left',
  forward: 'arrow_right',

  // 界面
  home: 'home',
  settings: 'settings',
  search: 'search',
  filter: 'filter',
  more: 'more_horizontal',
  close: 'dismiss',
  minimize: 'subtract',
  maximize: 'maximize',
  menu: 'navigation',

  // 文件操作
  folder: 'folder',
  folder_open: 'folder_open',
  document: 'document',
  file: 'document',
  upload: 'arrow_upload',
  download: 'arrow_download',

  // 通信
  chat: 'chat',
  mail: 'mail',
  phone: 'phone',
  video: 'video',

  // 状态
  checkmark: 'checkmark',
  success: 'checkmark_circle',
  error: 'error_circle',
  warning: 'warning',
  info: 'info',

  // 媒体
  play: 'play',
  pause: 'pause',
  stop: 'stop',
  volume: 'speaker_2',

  // 其他常用
  star: 'star',
  heart: 'heart',
  bookmark: 'bookmark',
  share: 'share',
  print: 'print',
  calendar: 'calendar_ltr',
  clock: 'clock',
  location: 'location',
};

// Quasar 图标集定义（部分常用图标）
export const fluentIconSet: FluentIconSet = {
  name: 'fluent-icons',
  type: {
    positive: 'fluent-checkmark_circle',
    negative: 'fluent-error_circle',
    info: 'fluent-info',
    warning: 'fluent-warning',
  },
  arrow: {
    up: 'fluent-arrow_up',
    right: 'fluent-arrow_right',
    down: 'fluent-arrow_down',
    left: 'fluent-arrow_left',
    dropdown: 'fluent-chevron_down',
  },
  chevron: {
    left: 'fluent-chevron_left',
    right: 'fluent-chevron_right',
  },
};

// 导出所有可用的图标名称
export const availableFluentIcons = Object.keys(fluentRegularMap);

// 搜索图标的辅助函数
export function searchFluentIcons(query: string): string[] {
  const lowerQuery = query.toLowerCase();
  return availableFluentIcons
    .filter((name) => name.toLowerCase().includes(lowerQuery))
    .slice(0, 50); // 限制结果数量
}
