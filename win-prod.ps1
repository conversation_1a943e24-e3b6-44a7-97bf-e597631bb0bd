# InkCop Production Build Script
# Build Quasar frontend and complete production Qt application

param(
    [string]$BuildType = "Release"
)

Write-Host "======================================" -ForegroundColor Cyan
Write-Host "InkCop Production Build" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "Build Type: $BuildType" -ForegroundColor Blue
Write-Host "Console Mode: Enabled (terminal output visible)" -ForegroundColor Blue
Write-Host ""

# Step 0: Setup production environment configuration
Write-Host "Step 0: Setting up production environment..." -ForegroundColor Yellow
if (Test-Path ".env.production") {
    Write-Host "Using production environment configuration (with API keys)..." -ForegroundColor Blue
    Copy-Item ".env.production" ".env" -Force
    Write-Host "Production .env configuration applied!" -ForegroundColor Green
} else {
    Write-Host "Warning: .env.production not found, using current .env" -ForegroundColor Yellow
}
Write-Host ""

# Step 1: Build frontend
Write-Host "Step 1: Building Quasar frontend..." -ForegroundColor Magenta
Write-Host "Running: bun run build" -ForegroundColor Blue

& bun run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Frontend build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Frontend build completed!" -ForegroundColor Green
Write-Host ""

# Step 2: Setup Qt environment
Write-Host "Step 2: Setting up Qt 6.9.1 MSVC environment..." -ForegroundColor Magenta
$qtPath = "C:\Qt\6.9.1\msvc2022_64"
$qwkPath = "third-party/build-qwindowkit/install"

if (-not (Test-Path $qtPath)) {
    Write-Host "Error: Qt installation not found at: $qtPath" -ForegroundColor Red
    exit 1
}

# Include QWindowKit in CMAKE_PREFIX_PATH
$combinedPrefixPath = "$qwkPath;$qtPath"
$env:CMAKE_PREFIX_PATH = $combinedPrefixPath
$env:Qt6_DIR = "$qtPath\lib\cmake\Qt6"
$env:PATH = "$qtPath\bin;$env:PATH"

Write-Host "Qt environment configured!" -ForegroundColor Green

# Setup Visual Studio environment
Write-Host "Loading Visual Studio environment..." -ForegroundColor Yellow
$vsPath = "C:\Program Files\Microsoft Visual Studio\2022\Community"
if (Test-Path $vsPath) {
    & "$vsPath\VC\Auxiliary\Build\vcvars64.bat" >$null 2>&1
    Write-Host "Visual Studio environment loaded!" -ForegroundColor Green
} else {
    Write-Host "Warning: Visual Studio 2022 not found" -ForegroundColor Yellow
}

# Step 3: Build Qt application
Write-Host ""
Write-Host "Step 3: Building Qt application..." -ForegroundColor Magenta

# Create build directory
$buildDir = "build-prod"
if (Test-Path $buildDir) {
    Write-Host "Cleaning old build directory..." -ForegroundColor Yellow
    Remove-Item -Path $buildDir -Recurse -Force
}
New-Item -ItemType Directory -Path $buildDir -Force | Out-Null

# Enter build directory
Set-Location $buildDir

Write-Host "Configuring CMake..." -ForegroundColor Yellow
$cmakeArgs = @(
    "-G", "Visual Studio 17 2022",
    "-A", "x64",
    "-DCMAKE_BUILD_TYPE=$BuildType",
    "-DCMAKE_PREFIX_PATH=$env:CMAKE_PREFIX_PATH",
    "-DQt6_DIR=$env:Qt6_DIR",
    "-DCONSOLE_MODE=ON",
    "-DENABLE_LOCAL_GGUF=ON",
    "-DCMAKE_CXX_FLAGS=/Zm300 /bigobj",
    ".."
)

& cmake @cmakeArgs
if ($LASTEXITCODE -ne 0) {
    Write-Host "CMake configuration failed!" -ForegroundColor Red
    Set-Location ..
    exit 1
}

Write-Host "Building project..." -ForegroundColor Yellow
& cmake --build . --config $BuildType
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Step 4: Deploy application
Write-Host ""
Write-Host "Step 4: Deploying application..." -ForegroundColor Magenta

Write-Host "Running windeployqt..." -ForegroundColor Yellow
$exePath = "bin\$BuildType\InkCop.exe"
if (Test-Path $exePath) {
    Write-Host "Deploying Qt libraries..." -ForegroundColor Blue
    & windeployqt $exePath --webenginewidgets --webenginecore --webchannel --sql --network --widgets --gui --core --force --verbose 2
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Qt deployment completed!" -ForegroundColor Green
    } else {
        Write-Host "Warning: windeployqt returned exit code $LASTEXITCODE" -ForegroundColor Yellow
    }
} else {
    Write-Host "Warning: Executable not found, skipping Qt deployment" -ForegroundColor Yellow
}

# Copy ObjectBox DLL
$targetDir = "bin\$BuildType"
$objectboxDllPaths = @(
    "..\third-party\objectbox-windows\lib\objectbox.dll",
    "..\third-party\objectbox-c\lib\objectbox.dll",
    "third-party\objectbox-windows\lib\objectbox.dll",
    "third-party\objectbox-c\lib\objectbox.dll"
)

$objectboxCopied = $false
foreach ($objectboxDll in $objectboxDllPaths) {
    if (Test-Path $objectboxDll) {
        Copy-Item $objectboxDll $targetDir -Force
        Write-Host "ObjectBox DLL copied from: $objectboxDll" -ForegroundColor Green
        $objectboxCopied = $true
        break
    }
}

if (-not $objectboxCopied) {
    Write-Host "Warning: ObjectBox DLL not found in any of the expected locations:" -ForegroundColor Yellow
    foreach ($path in $objectboxDllPaths) {
        Write-Host "  - $path" -ForegroundColor Gray
    }
}

# Copy llama.cpp DLLs if GGUF support is enabled
$llamaPath = "..\third-party\llama.cpp\build-cuda\bin\Release"
if (Test-Path $llamaPath) {
    Write-Host "Copying llama.cpp DLLs..." -ForegroundColor Yellow
    
    $llamaDlls = @(
        "ggml-base.dll",
        "ggml-cpu.dll", 
        "ggml-cuda.dll",
        "ggml.dll",
        "llama.dll"
    )
    
    $copiedCount = 0
    foreach ($dll in $llamaDlls) {
        $sourceDll = Join-Path $llamaPath $dll
        if (Test-Path $sourceDll) {
            Copy-Item $sourceDll $targetDir -Force
            Write-Host "  Copied $dll" -ForegroundColor Blue
            $copiedCount++
        } else {
            Write-Host "  Warning: $dll not found" -ForegroundColor Yellow
        }
    }
    
    if ($copiedCount -gt 0) {
        Write-Host "llama.cpp DLLs copied! ($copiedCount/$($llamaDlls.Count) files)" -ForegroundColor Green

        # 复制必要的 CUDA 运行时库
        Write-Host "Copying CUDA runtime libraries..." -ForegroundColor Yellow

        $cudaPaths = @(
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin",
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin",
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.7\bin",
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin",
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.5\bin",
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin",
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.3\bin",
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.2\bin",
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\bin",
            "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.0\bin"
        )

        $cudaDlls = @(
            "cudart64_12.dll",
            "cublas64_12.dll",
            "cublasLt64_12.dll",
            "curand64_10.dll",
            "cusparse64_12.dll"
        )

        $cudaPath = $null
        foreach ($path in $cudaPaths) {
            if (Test-Path $path) {
                $cudaPath = $path
                break
            }
        }

        if ($cudaPath) {
            Write-Host "  Found CUDA installation: $cudaPath" -ForegroundColor Blue
            $cudaCopiedCount = 0

            foreach ($dll in $cudaDlls) {
                $sourceDll = Join-Path $cudaPath $dll
                if (Test-Path $sourceDll) {
                    try {
                        Copy-Item $sourceDll $targetDir -Force
                        Write-Host "    Copied $dll" -ForegroundColor Blue
                        $cudaCopiedCount++
                    } catch {
                        Write-Host "    Warning: Failed to copy $dll - $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                } else {
                    Write-Host "    Note: $dll not found (may not be required)" -ForegroundColor Gray
                }
            }

            if ($cudaCopiedCount -gt 0) {
                Write-Host "CUDA runtime libraries copied! ($cudaCopiedCount files)" -ForegroundColor Green
            } else {
                Write-Host "Warning: No CUDA runtime libraries were copied" -ForegroundColor Yellow
            }
        } else {
            Write-Host "Warning: CUDA installation not found" -ForegroundColor Yellow
            Write-Host "  CUDA runtime libraries may be missing from the package" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Warning: No llama.cpp DLLs found in $llamaPath" -ForegroundColor Yellow
    }
} else {
    Write-Host "Warning: llama.cpp build directory not found: $llamaPath" -ForegroundColor Yellow
    Write-Host "  Make sure to build llama.cpp with CUDA support first" -ForegroundColor Yellow
}

# Copy QWindowKit DLLs
$qwkDllPath = "..\third-party\qwindowkit\build-qwk\out-amd64-Release\bin"
if (Test-Path $qwkDllPath) {
    Write-Host "Copying QWindowKit DLLs..." -ForegroundColor Yellow

    $qwkDlls = Get-ChildItem -Path $qwkDllPath -Filter "*.dll"
    $copiedCount = 0

    foreach ($dll in $qwkDlls) {
        try {
            Copy-Item $dll.FullName $targetDir -Force
            Write-Host "  Copied $($dll.Name)" -ForegroundColor Blue
            $copiedCount++
        } catch {
            Write-Host "  Warning: Failed to copy $($dll.Name)" -ForegroundColor Yellow
        }
    }

    if ($copiedCount -gt 0) {
        Write-Host "QWindowKit DLLs copied! ($copiedCount files)" -ForegroundColor Green
    } else {
        Write-Host "Warning: No QWindowKit DLLs found or copied" -ForegroundColor Yellow
    }
} else {
    Write-Host "Warning: QWindowKit DLL directory not found: $qwkDllPath" -ForegroundColor Yellow
    Write-Host "  Make sure to build QWindowKit first" -ForegroundColor Yellow
}

Set-Location ..

# Step 5: Generate build report
Write-Host ""
Write-Host "Step 5: Generating build report..." -ForegroundColor Magenta

$exeFullPath = "$buildDir\bin\$BuildType\InkCop.exe"
if (Test-Path $exeFullPath) {
    $fileInfo = Get-Item $exeFullPath
    $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)

    Write-Host ""
    Write-Host "======================================" -ForegroundColor Green
    Write-Host "Production Build Completed!" -ForegroundColor Green
    Write-Host "======================================" -ForegroundColor Green
    Write-Host "Executable: $exeFullPath" -ForegroundColor Blue
    Write-Host "File Size: $fileSizeMB MB" -ForegroundColor Blue
    Write-Host "Created: $($fileInfo.CreationTime)" -ForegroundColor Blue
    Write-Host "Frontend: Embedded in application" -ForegroundColor Blue
    Write-Host "Console Mode: Enabled (terminal output visible)" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Deployment directory contents:" -ForegroundColor Yellow
    Get-ChildItem "$buildDir\bin\$BuildType" | ForEach-Object {
        $size = if ($_.PSIsContainer) { "[DIR]" } else { "$([math]::Round($_.Length / 1KB, 1)) KB" }
        Write-Host "  $($_.Name) - $size" -ForegroundColor White
    }
    Write-Host ""
    Write-Host "Application ready for production deployment!" -ForegroundColor Green
    Write-Host "You can run: $exeFullPath" -ForegroundColor Cyan
} else {
    Write-Host "Error: Executable not found: $exeFullPath" -ForegroundColor Red
    exit 1
}
