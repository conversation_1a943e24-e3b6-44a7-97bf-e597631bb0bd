<template>
  <div v-if="doc" class="document-item-wrapper">
    <div
      :ref="setDocumentRef"
      class="row no-wrap hover-highlight q-px-sm document-drag-container full-width"
      :class="[
        uiStore.highlightTreeItem === `document-${doc?.id}`
          ? 'highlight-tree-item'
          : keepHighlightDocumentId === doc.id
            ? $q.dark.mode
              ? 'bg-grey-9 border-placehoder'
              : 'bg-grey-2 border-placehoder'
            : 'border-placehoder',
        uiStore.isDocumentCut(doc.id) ? 'op-5' : '',
      ]"
      @contextmenu.prevent="showContextMenu"
    >
      <slot name="padding" />
      <div class="row no-wrap gap-xs items-center q-space">
        <div
          v-if="!isRenaming"
          class="cursor-pointer row no-wrap gap-xs q-space hover-item q-py-none q-px-sm"
          @click="openDocument(doc)"
        >
          <div class="row no-wrap items-center" style="flex: 0 0 16px; height: 32px">
            <q-icon name="mdi-file-document" size="xs" class="cursor-pointer" />
          </div>
          <span class="row no-wrap items-center q-space q-pl-sm">{{ doc.title }}</span>
        </div>
        <q-input
          v-if="isRenaming"
          ref="renameInputRef"
          v-model="renameNameLocal"
          dense
          autofocus
          hide-bottom-space
          input-class="q-px-sm"
          class="q-space overflow-hidden border"
          @keydown.enter="handleRenameEnter"
          @keydown.esc="cancelRename"
          @compositionstart="isComposing = true"
          @compositionend="isComposing = false"
        >
          <template #append>
            <div class="flex flex-center q-pr-sm">
              <q-btn
                v-if="renameNameLocal !== doc.title"
                icon="mdi-check"
                dense
                size="sm"
                round
                flat
                @click="confirmRename"
              />
              <q-btn v-else icon="close" dense size="sm" round flat @click="cancelRename" />
            </div>
          </template>
        </q-input>
      </div>

      <!-- 右键菜单 -->
      <q-menu
        ref="contextMenuRef"
        class="shadow-24"
        context-menu
        @show="keepHighlightDocumentId = doc.id"
        @hide="keepHighlightDocumentId = null"
      >
        <q-list dense>
          <q-item clickable v-close-popup @click="addToConversation">
            <q-item-section side>
              <q-icon name="mdi-chat-plus" />
            </q-item-section>
            <q-item-section>{{
              $t('src.components.DocumentItem.add_to_conversation')
            }}</q-item-section>
          </q-item>
          <q-separator class="op-5" />
          <q-item
            :disable="knowledgeUnavailable"
            clickable
            v-close-popup
            @click="addToKnowledgeBase"
          >
            <q-item-section side>
              <q-icon name="mdi-brain" />
            </q-item-section>
            <q-item-section>{{
              $t('src.components.DocumentItem.add_to_knowledge_base')
            }}</q-item-section>
          </q-item>
          <q-item :disable="knowledgeUnavailable" clickable>
            <q-item-section side>
              <q-icon name="mdi-brain" />
            </q-item-section>
            <q-item-section>{{
              $t('src.components.DocumentItem.quick_add_to_knowledge_base')
            }}</q-item-section>
            <q-item-section side>
              <q-icon name="mdi-chevron-right" />
            </q-item-section>

            <q-menu v-if="!knowledgeUnavailable" anchor="top end" self="top start">
              <q-list style="min-width: 150px; max-width: 350px">
                <template
                  v-if="knowledgeStore.knowledgeBases && knowledgeStore.knowledgeBases.length > 0"
                >
                  <q-item
                    v-for="kb in knowledgeStore.knowledgeBases"
                    :key="kb.id"
                    :clickable="!isDocumentInKnowledgeBase(kb.id)"
                    :disable="isDocumentInKnowledgeBase(kb.id)"
                    v-close-popup="2"
                    @click="
                      !isDocumentInKnowledgeBase(kb.id) && addToKnowledgeBaseDirect(kb.id, kb.name)
                    "
                    class="q-py-sm"
                    :class="{ 'op-5': isDocumentInKnowledgeBase(kb.id) }"
                  >
                    <q-item-section side top>
                      <q-icon
                        :name="
                          isDocumentInKnowledgeBase(kb.id) ? 'mdi-database-check' : 'mdi-database'
                        "
                        size="sm"
                        :color="isDocumentInKnowledgeBase(kb.id) ? 'grey-5' : 'primary'"
                      />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label
                        class="text-weight-medium"
                        :class="{ 'text-grey-5': isDocumentInKnowledgeBase(kb.id) }"
                      >
                        {{ kb.name }}
                        <q-chip
                          v-if="isDocumentInKnowledgeBase(kb.id)"
                          size="sm"
                          color="deep-orange"
                        >
                          {{ $t('src.components.DocumentItem.already_added') }}
                        </q-chip>
                      </q-item-label>
                      <q-item-label caption class="text-grey-7">
                        {{
                          $t('src.components.DocumentItem.documents_count', {
                            count: kb.document_count || 0,
                          })
                        }}
                        {{ kb.description ? '|' + kb.description : '' }}
                      </q-item-label>
                    </q-item-section>

                    <!-- 悬停提示 -->
                    <q-tooltip
                      v-if="isDocumentInKnowledgeBase(kb.id)"
                      :delay="500"
                      class="bg-grey-8"
                    >
                      {{
                        $t('src.components.DocumentItem.document_already_in_knowledge_base', {
                          name: kb.name,
                        })
                      }}
                    </q-tooltip>
                  </q-item>
                </template>
                <q-item v-else class="text-center">
                  <q-item-section>
                    <q-item-label class="text-grey-6">
                      <q-icon name="mdi-database-off" class="q-mr-sm" />
                      {{ $t('src.components.DocumentItem.no_knowledge_bases_available') }}
                    </q-item-label>
                    <q-item-label caption class="text-grey-5">
                      {{ $t('src.components.DocumentItem.please_create_knowledge_base_first') }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-item>
          <q-separator class="op-5" />
          <q-item clickable v-close-popup @click="showRename">
            <q-item-section side>
              <q-icon name="mdi-pencil" />
            </q-item-section>
            <q-item-section>{{ $t('src.components.DocumentItem.rename') }}</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="copyDocument">
            <q-item-section side>
              <q-icon name="mdi-content-copy" />
            </q-item-section>
            <q-item-section>{{ $t('src.components.DocumentItem.copy') }}</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="cutDocument">
            <q-item-section side>
              <q-icon name="mdi-content-cut" />
            </q-item-section>
            <q-item-section>{{ $t('src.components.DocumentItem.cut') }}</q-item-section>
          </q-item>
          <q-separator class="op-5" />
          <q-item clickable v-close-popup @click="deleteDocument">
            <q-item-section side>
              <q-icon name="mdi-delete" color="negative" />
            </q-item-section>
            <q-item-section>{{ $t('src.components.DocumentItem.delete') }}</q-item-section>
          </q-item>
        </q-list>
      </q-menu>

      <!-- 添加到知识库组件 -->
      <q-popup-proxy
        v-if="showCreateKnowledgeDocCard"
        v-model="showCreateKnowledgeDocCard"
        class="shadow-24"
        :offset="[-40, -10]"
      >
        <CreatekKnowlegeDocCard
          :content="documentContent"
          :default-title="doc.title"
          :original-document-id="doc.id"
          :document-id="doc.id"
          :exclude-knowledge-base-id="documentKnowledgeBase?.id"
          :bolderless="true"
          :documentKnowledgeAssociations="documentKnowledgeAssociations"
          style="min-width: 26rem"
          @cancel="showCreateKnowledgeDocCard = false"
          @saved="onKnowledgeDocSaved"
        />
      </q-popup-proxy>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import type { Document } from 'src/types/doc';
import { useDocumentActions } from '../composeables/useDocumentActions';
import { useQuasar, QInput } from 'quasar';
import { useSqlite } from 'src/composeables/useSqlite';
import { makeDraggable, makeDropTarget } from 'src/utils/dragAndDrop';
import type { DragData, DropData } from 'src/utils/dragAndDrop';
import { useKnowledgeStore } from '../stores/knowledge';
import { convertTiptapToMarkdown } from '../utils/tiptap';
import { useDocumentToKnowledge } from '../composeables/useDocumentToKnowledge';
import { useDocumentChunking } from '../composeables/useDocumentChunking';
import CreatekKnowlegeDocCard from './CreatekKnowlegeDocCard.vue';

import { useUiStore } from 'src/stores/ui';
import { SortMode } from 'src/utils/sortUtils';
import { useLlmStore } from 'src/stores/llm';
import { useI18n } from 'vue-i18n';
import { useKnowledge } from 'src/composeables/useKnowledge';

const { t: $t } = useI18n({ useScope: 'global' });

const uiStore = useUiStore();
const llmStore = useLlmStore();

const knowledgeUnavailable = computed(() => !uiStore.perferences.knowledgeBase.apiKey);

const { onRenameDocument } = useDocumentActions();
const { getDocument } = useSqlite();
const knowledgeStore = useKnowledgeStore();
const documentToKnowledge = useDocumentToKnowledge();
const documentChunking = useDocumentChunking();
const knowledge = useKnowledge();
const props = defineProps<{
  doc: Document;
  parentId: number;
  documentIndex?: number;
  depth?: number;
}>();
const emit = defineEmits<{
  'document-updated': [document: Document];
  'document-deleted': [documentId: number];
  open: [document: Document];
  'document-reorder': [
    data: {
      dragIndex: number;
      dropIndex: number;
      position: 'before' | 'after';
      parentId: number;
      draggedDocumentId?: number;
      newParentId?: number;
    },
  ];
}>();
const $q = useQuasar();

// 内部状态管理
const isRenaming = ref(false);
const renameNameLocal = ref('');
const keepHighlightDocumentId = ref<number | null>(null);
const isComposing = ref(false);
const contextMenuRef = ref();
const renameInputRef = ref<InstanceType<typeof QInput> | null>(null);

// 知识库相关状态
const showCreateKnowledgeDocCard = ref(false);
const documentContent = ref('');

// 文档的知识库关联信息
const documentKnowledgeAssociations = ref<
  Array<{
    id: number;
    knowledge_document_id: number;
    knowledge_base_id: number;
    created_at: string;
  }>
>([]);

// 文档所属知识库信息（保持向后兼容）
const documentKnowledgeBase = ref<{ id: number; name: string } | null>(null);

// 监听重命名状态变化，自动聚焦输入框
watch(isRenaming, async (newValue) => {
  if (newValue) {
    await nextTick();
    if (renameInputRef.value) {
      renameInputRef.value.focus();
    }
  }
});

// 监听documentIndex变化，重新设置拖拽数据
watch(
  () => props.documentIndex,
  (newIndex, oldIndex) => {
    if (documentElement.value && newIndex !== oldIndex) {
      console.log('🔄 [DocumentItem] documentIndex 变化，重新设置拖拽数据:', {
        docId: props.doc.id,
        docTitle: props.doc.title,
        oldIndex,
        newIndex,
      });
      setupDocumentDrag();
    }
  },
);

// 监听排序模式变化，重新设置拖拽数据
watch(
  () => uiStore.sortMode,
  (newMode, oldMode) => {
    if (documentElement.value && newMode !== oldMode) {
      console.log('🔄 [DocumentItem] sortMode 变化，重新设置拖拽数据:', {
        docId: props.doc.id,
        docTitle: props.doc.title,
        oldMode,
        newMode,
        showPlaceholder: newMode === SortMode.CUSTOM,
      });
      setupDocumentDrag();
    }
  },
);

// 拖拽相关
const documentElement = ref<HTMLElement | null>(null);
let dragCleanup: (() => void) | null = null;

const setDocumentRef = (el: unknown) => {
  if (el && el instanceof HTMLElement) {
    // 检查是否已经设置过相同的元素，避免重复设置
    if (documentElement.value === el) {
      return;
    }

    documentElement.value = el;
    setupDocumentDrag();
  } else {
    documentElement.value = null;
    cleanupDocumentDrag();
  }
};

const setupDocumentDrag = () => {
  if (!documentElement.value) return;

  // 先清理之前的拖拽事件
  cleanupDocumentDrag();

  // 设置可拖拽
  console.log('🔄 [DocumentItem] 设置拖拽数据:', {
    docId: props.doc.id,
    docTitle: props.doc.title,
    parentId: props.parentId,
    documentIndex: props.documentIndex,
  });

  const dragCleanupFn = makeDraggable(
    documentElement.value,
    {
      type: 'document',
      id: props.doc.id,
      parentId: props.parentId,
      item: props.doc,
      index: props.documentIndex || 0,
    },
    {
      onDragStart: () => {
        console.log('开始拖拽文档:', props.doc.title);
      },
      onDragEnd: () => {
        console.log('结束拖拽文档:', props.doc.title);
      },
    },
  );

  // 设置拖拽目标（用于文档排序）
  const dropCleanupFn = makeDropTarget(
    documentElement.value,
    {
      type: 'document',
      id: props.doc.id,
      item: props.doc,
      index: props.documentIndex || 0,
      position: 'after',
      parentId: props.parentId, // 添加parentId信息
    },
    {
      onDrop: (dragData: DragData, dropData: DropData) => {
        console.log('🔄 [DocumentItem] 文档拖拽放置:', {
          dragData,
          dropData,
          currentDocId: props.doc.id,
          currentDocIndex: props.documentIndex,
          currentDocTitle: props.doc.title,
        });

        // 检查是否为文档类型的拖拽
        if (dragData.type === 'document' && dragData.id !== props.doc.id) {
          // 检查排序模式
          if (uiStore.sortMode === SortMode.ALPHABETICAL) {
            // 字典排序模式：只允许跨文件夹移动，不允许同文件夹排序
            if (dragData.parentId === props.parentId) {
              console.log('🔄 [DocumentItem] 字典排序模式下不允许同文件夹排序，忽略');
              return;
            }

            // 跨文件夹移动：将文档设置为目标文件夹的第一个
            const reorderData = {
              dragIndex: dragData.index,
              dropIndex: 0, // 总是插入到第一个位置
              position: 'before' as 'before' | 'after',
              parentId: props.parentId, // 目标文件夹ID
              draggedDocumentId: dragData.id,
              newParentId: props.parentId, // 新的父文件夹ID
            };

            console.log('🔄 [DocumentItem] 字典排序模式 - 跨文件夹移动到第一个位置:', reorderData);
            emit('document-reorder', reorderData);
          } else {
            // 自定义排序模式：支持同文件夹排序和跨文件夹移动
            const position: 'before' | 'after' =
              dropData.position === 'before' ? 'before' : 'after';
            const reorderData = {
              dragIndex: dragData.index,
              dropIndex: props.documentIndex || 0,
              position,
              parentId: props.parentId, // 目标文件夹ID
              draggedDocumentId: dragData.id,
              newParentId: props.parentId, // 新的父文件夹ID
            };

            console.log(
              '🔄 [DocumentItem] 自定义排序模式 - 准备触发 document-reorder 事件:',
              reorderData,
            );
            emit('document-reorder', reorderData);
          }
        } else {
          console.log('🔄 [DocumentItem] 跳过排序 - 条件不满足:', {
            isDragTypeDocument: dragData.type === 'document',
            isDifferentDoc: dragData.id !== props.doc.id,
            dragDataId: dragData.id,
            currentDocId: props.doc.id,
          });
        }
      },
    },
    {
      // 只在自定义排序模式下显示占位符（支持同文件夹排序）
      // 字典排序模式下不显示占位符，只支持跨文件夹移动
      showPlaceholder: uiStore.sortMode === SortMode.CUSTOM,
      depth: props.depth || 0,
    },
  );

  // 保存清理函数
  dragCleanup = () => {
    dragCleanupFn();
    dropCleanupFn();
  };
};

const cleanupDocumentDrag = () => {
  if (dragCleanup) {
    dragCleanup();
    dragCleanup = null;
  }
};
// 重命名相关方法
const showRename = () => {
  isRenaming.value = true;
  renameNameLocal.value = props.doc.title || '';
};

const cancelRename = () => {
  isRenaming.value = false;
  renameNameLocal.value = '';
};

const confirmRename = async () => {
  if (!renameNameLocal.value || renameNameLocal.value === props.doc.title) {
    cancelRename();
    return;
  }

  try {
    const res: Document = await onRenameDocument(props.doc.id, renameNameLocal.value);
    if (!res.error) {
      emit('document-updated', res);
      isRenaming.value = false;
    }
  } catch (error) {
    console.error('重命名文档失败:', error);
  }
};

const handleRenameEnter = (event: KeyboardEvent) => {
  // 如果正在进行中文输入法组合输入，不触发重命名事件
  if (isComposing.value) {
    return;
  }

  // 阻止默认行为并触发重命名
  event.preventDefault();
  void confirmRename();
};

// 删除方法
const deleteDocument = () => {
  emit('document-deleted', props.doc.id);
};

const openDocument = (doc: Document) => {
  uiStore.highlightTreeItem = `document-${doc.id}`;
  emit('open', doc);
};

const showContextMenu = (event: MouseEvent) => {
  if (contextMenuRef.value) {
    contextMenuRef.value.show(event);
  }
};

const addToConversation = async () => {
  try {
    console.log('🔗 [文档] 准备添加文档到对话:', props.doc.id);

    // 从后端获取完整的文档内容
    const fullDocument = await getDocument(props.doc.id);
    console.log('📄 [文档] 获取完整文档内容:', fullDocument.title);

    // 直接添加到附加内容中，同时保持关联文档的同步
    const attachmentAdded = llmStore.addDocumentAttachment(fullDocument);

    if (attachmentAdded) {
      // 同步添加到关联文档列表（用于内部逻辑）
      llmStore.addRelatedDocument(fullDocument);
      llmStore.relatedDocumentsAddByUser.push(fullDocument);

      $q.notify({
        type: 'positive',
        message: `文档"${fullDocument.title}"已添加到当前对话`,
        position: 'top',
      });
      console.log('✅ [文档] 文档已添加到对话附加内容列表');
    } else {
      $q.notify({
        type: 'warning',
        message: `文档"${fullDocument.title}"已经在当前对话中`,
        position: 'top',
      });
      console.log('⚠️ [文档] 文档已存在于附加内容列表中');
    }
  } catch (error) {
    console.error('❌ [文档] 添加文档到对话失败:', error);
    $q.notify({
      type: 'negative',
      message: '添加文档到对话失败',
      position: 'top',
    });
  }
};

const copyDocument = async () => {
  try {
    console.log('🔗 [文档] 准备复制文档:', props.doc.id);

    // 从后端获取完整的文档内容
    const fullDocument = await getDocument(props.doc.id);
    console.log('📄 [文档] 获取完整文档内容:', fullDocument.title);

    // 复制文档到剪贴板状态
    uiStore.copyDocument({
      id: fullDocument.id,
      title: fullDocument.title || '',
      content: fullDocument.content || '',
      metadata: fullDocument.metadata || '{}',
      parentId: props.parentId,
    });

    $q.notify({
      type: 'positive',
      message: `文档"${fullDocument.title}"已复制`,
      position: 'top',
    });
    console.log('✅ [文档] 文档已复制到剪贴板状态');
  } catch (error) {
    console.error('❌ [文档] 复制文档失败:', error);
    $q.notify({
      type: 'negative',
      message: '复制文档失败',
      position: 'top',
    });
  }
};

const cutDocument = async () => {
  try {
    console.log('✂️ [文档] 准备剪切文档:', props.doc.id);

    // 从后端获取完整的文档内容
    const fullDocument = await getDocument(props.doc.id);
    console.log('📄 [文档] 获取完整文档内容:', fullDocument.title);

    // 剪切文档到剪贴板状态
    uiStore.cutDocument({
      id: fullDocument.id,
      title: fullDocument.title || '',
      content: fullDocument.content || '',
      metadata: fullDocument.metadata || '{}',
      parentId: props.parentId,
    });

    $q.notify({
      type: 'positive',
      message: `文档"${fullDocument.title}"已剪切`,
      position: 'top',
    });
    console.log('✅ [文档] 文档已剪切到剪贴板状态');
  } catch (error) {
    console.error('❌ [文档] 剪切文档失败:', error);
    $q.notify({
      type: 'negative',
      message: '剪切文档失败',
      position: 'top',
    });
  }
};

const addToKnowledgeBase = async () => {
  try {
    console.log('🧠 [文档] 准备添加文档到知识库:', props.doc.id);

    // 检查文档是否已添加到知识库

    // 从后端获取完整的文档内容
    const fullDocument = await getDocument(props.doc.id);
    console.log('📄 [文档] 获取完整文档内容:', {
      title: fullDocument.title,
      contentType: typeof fullDocument.content,
      contentLength: fullDocument.content ? JSON.stringify(fullDocument.content).length : 0,
      contentPreview: fullDocument.content
        ? JSON.stringify(fullDocument.content).substring(0, 200)
        : 'null',
    });

    // 将 TipTap JSON 内容转换为 Markdown
    let markdownContent = '';
    if (fullDocument.content) {
      // 尝试解析为JSON格式的TipTap内容
      const jsonContent =
        typeof fullDocument.content === 'string'
          ? JSON.parse(fullDocument.content)
          : fullDocument.content;
      markdownContent = convertTiptapToMarkdown(jsonContent);
    } else {
      console.log('📝 [文档] 文档内容为空');
    }

    // 检查内容是否为空
    if (!markdownContent.trim()) {
      console.warn('⚠️ [文档] 文档内容为空，无法添加到知识库');
      $q.notify({
        type: 'warning',
        message: `文档"${fullDocument.title}"内容为空，无法添加到知识库`,
        position: 'top',
      });
      return;
    }

    // 设置文档内容并显示知识库选择组件
    documentContent.value = markdownContent;
    console.log('📝 [文档] 设置documentContent.value，长度:', documentContent.value.length);
    showCreateKnowledgeDocCard.value = true;

    console.log('✅ [文档] 准备显示知识库选择界面');
  } catch (error) {
    console.error('❌ [文档] 获取文档内容失败:', error);
    $q.notify({
      type: 'negative',
      message: '获取文档内容失败',
      position: 'top',
    });
  }
};

const onKnowledgeDocSaved = async (knowledgeBaseId: number, documentId: number, title: string) => {
  showCreateKnowledgeDocCard.value = false;

  try {
    // 刷新知识库列表数据，更新文档数量统计
    await knowledgeStore.refreshKnowledgeBases();

    // 刷新文档的知识库关联信息
    await loadDocumentKnowledgeAssociations();

    // 标记该知识库的文档列表需要刷新（用于keep-alive缓存场景）
    knowledgeStore.markKnowledgeBaseNeedsRefresh(knowledgeBaseId);
    console.log('🔄 [DocumentItem] 标记知识库需要刷新文档列表:', {
      knowledgeBaseId,
      documentTitle: title,
      documentId,
    });

    $q.notify({
      type: 'positive',
      message: `文档"${title}"已保存到知识库，正在进行切割和向量化...`,
      position: 'top',
    });

    console.log('✅ [文档] 文档已保存到知识库，开始切割和向量化:', {
      knowledgeBaseId,
      documentId,
      title,
    });

    // 启用切割和向量化监听器，确保处理完成
    // 注意：CreatekKnowlegeDocCard 已经通过 createKnowledgeDocWithChunking 启动了处理流程
    // 这里我们设置监听器来跟踪处理进度和完成状态
    try {
      // 设置文档切割和向量化的监听器
      documentChunking.setupVectorizationListener((docId: number, chunkCount: number) => {
        console.log(`🎯 [文档] 文档 ${docId} 向量化完成，处理了 ${chunkCount} 个块`);
        $q.notify({
          type: 'positive',
          message: `文档"${title}"向量化完成，已可用于搜索`,
          position: 'top',
          timeout: 5000,
        });
      });

      // 可选：如果需要额外的处理确认，可以调用状态检查
      console.log('🔄 [文档] 切割和向量化处理已启动，等待完成通知...');

      // 显示处理中的提示
      $q.notify({
        type: 'info',
        message: '文档正在进行切割和向量化处理，请稍候...',
        position: 'top',
        timeout: 3000,
      });
    } catch (processingError) {
      console.error('❌ [文档] 设置处理监听器失败:', processingError);
      $q.notify({
        type: 'warning',
        message: '无法监听处理进度，请手动检查知识库状态',
        position: 'top',
      });
    }
  } catch (error) {
    console.error('❌ [文档] 刷新知识库数据失败:', error);
  }
};

/**
 * 直接添加文档到指定知识库（使用默认配置）
 * 跳过配置界面，直接使用默认的切割策略
 */
const addToKnowledgeBaseDirect = async (knowledgeBaseId: number, knowledgeBaseName: string) => {
  try {
    console.log('🚀 [文档] 开始快速添加文档到知识库:', {
      docId: props.doc.id,
      knowledgeBaseId,
      knowledgeBaseName,
    });

    // 检查文档是否已添加到指定知识库
    if (isDocumentInKnowledgeBase(knowledgeBaseId)) {
      $q.notify({
        type: 'warning',
        message: $t('src.components.DocumentItem.document_already_in_knowledge_base', {
          name: knowledgeBaseName,
        }),
        position: 'top',
      });
      return;
    }

    // 显示处理开始的通知
    $q.notify({
      type: 'info',
      message: `正在将文档添加到知识库"${knowledgeBaseName}"...`,
      position: 'top',
    });

    // 使用新的 hook 进行完整的文档处理
    const result = await documentToKnowledge.processDocument(
      props.doc.id,
      {
        knowledgeBaseId,
        chunkingMethod: 'markdown', // 默认使用 markdown 切割方法
        chunkingConfig: {
          chunkSize: 800,
          chunkOverlap: 200,
        },
      },
      {
        onProgress: (current, total, currentTitle) => {
          console.log(`📊 [文档] 快速处理进度: ${current}/${total} - ${currentTitle}`);
        },
        onDocumentCompleted: (result) => {
          console.log(`✅ [文档] 文档处理完成:`, result);
        },
        onChunkingProgress: (docId, progress) => {
          console.log(
            `📊 [文档] 文档 ${docId} 切割进度: ${progress.stage} (${progress.percentage}%)`,
          );
        },
        onChunkingCompleted: (docId, result) => {
          console.log(`✅ [文档] 文档 ${docId} 切割完成，共 ${result.chunkCount} 个块`);
          $q.notify({
            type: 'info',
            message: `文档切割完成，共生成 ${result.chunkCount} 个文本块`,
            position: 'top',
          });
        },
        onVectorizationCompleted: (docId, chunkCount) => {
          console.log(`🎯 [文档] 文档 ${docId} 向量化完成，处理了 ${chunkCount} 个块`);
          $q.notify({
            type: 'positive',
            message: `文档"${props.doc.title}"已成功添加到知识库并完成向量化`,
            position: 'top',
            timeout: 5000,
          });
        },
        onError: (docId, error, stage) => {
          console.error(`❌ [文档] 文档 ${docId} ${stage}失败:`, error);
          $q.notify({
            type: 'negative',
            message: `文档${stage}失败: ${error}`,
            position: 'top',
          });
        },
      },
    );

    if (result.success) {
      // 刷新知识库数据
      await knowledgeStore.refreshKnowledgeBases();

      // 刷新文档的知识库关联信息
      await loadDocumentKnowledgeAssociations();

      // 标记该知识库的文档列表需要刷新（用于keep-alive缓存场景）
      knowledgeStore.markKnowledgeBaseNeedsRefresh(knowledgeBaseId);
      console.log('🔄 [DocumentItem] 标记知识库需要刷新文档列表:', {
        knowledgeBaseId,
        knowledgeBaseName,
        documentTitle: props.doc.title,
        documentId: props.doc.id,
      });

      console.log('🎉 [文档] 快速添加到知识库完成:', result);
    }
  } catch (error) {
    console.error('❌ [文档] 快速添加到知识库失败:', error);
    $q.notify({
      type: 'negative',
      message: `快速添加失败: ${error instanceof Error ? error.message : '未知错误'}`,
      position: 'top',
    });
  }
};

/**
 * 获取文档的知识库关联信息
 */
const loadDocumentKnowledgeAssociations = async () => {
  try {
    const { getDocumentKnowledgeAssociations } = useSqlite();
    const associations = await getDocumentKnowledgeAssociations(props.doc.id);
    documentKnowledgeAssociations.value = associations;

    // 为了向后兼容，如果有关联，设置第一个关联的知识库信息
    if (associations.length > 0) {
      const firstAssociation = associations[0];
      const knowledgeBase = knowledgeStore.knowledgeBases.find(
        (kb) => kb.id === firstAssociation.knowledge_base_id,
      );
      if (knowledgeBase) {
        documentKnowledgeBase.value = {
          id: knowledgeBase.id,
          name: knowledgeBase.name,
        };
      }
    } else {
      documentKnowledgeBase.value = null;
    }

    console.log('📋 [DocumentItem] 文档知识库关联信息已加载:', {
      documentId: props.doc.id,
      associationsCount: associations.length,
      associations: associations.map((a) => ({
        knowledgeBaseId: a.knowledge_base_id,
        knowledgeDocumentId: a.knowledge_document_id,
      })),
    });
  } catch (error) {
    console.error('❌ [DocumentItem] 获取文档知识库关联失败:', error);
    documentKnowledgeAssociations.value = [];
    documentKnowledgeBase.value = null;
  }
};

/**
 * 检查文档是否已添加到指定知识库
 */
const isDocumentInKnowledgeBase = (knowledgeBaseId: number): boolean => {
  // 检查文档的知识库关联列表中是否包含指定的知识库ID
  return documentKnowledgeAssociations.value.some(
    (association) => association.knowledge_base_id === knowledgeBaseId,
  );
};

// 事件监听器清理函数
let knowledgeEventCleanup: (() => void)[] = [];

// 组件挂载时加载文档知识库关联信息
onMounted(async () => {
  // 确保知识库列表已加载
  if (knowledgeStore.knowledgeBases.length === 0) {
    await knowledgeStore.loadKnowledgeBases();
  }
  // 加载文档的知识库关联信息
  await loadDocumentKnowledgeAssociations();

  // 设置知识库事件监听器
  const unsubscribeAdded = knowledge.on('knowledge_document_added', () => {
    console.log('📡 [DocumentItem] 收到知识库文档添加事件，刷新关联信息');
    void loadDocumentKnowledgeAssociations();
  });

  const unsubscribeRemoved = knowledge.on('knowledge_document_removed', () => {
    console.log('📡 [DocumentItem] 收到知识库文档删除事件，刷新关联信息');
    void loadDocumentKnowledgeAssociations();
  });

  // 保存清理函数
  knowledgeEventCleanup = [unsubscribeAdded, unsubscribeRemoved];
});

// 组件卸载时清理拖拽事件和知识库事件监听器
onUnmounted(() => {
  cleanupDocumentDrag();

  // 清理知识库事件监听器
  knowledgeEventCleanup.forEach((cleanup) => cleanup());
  knowledgeEventCleanup = [];
});
</script>

<style scoped>
.document-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.doc-title {
  cursor: pointer;
  margin-right: 8px;
}
</style>
