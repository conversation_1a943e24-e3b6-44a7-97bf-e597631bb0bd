<template>
  <div class="knowledge-debug q-pa-lg">
    <div class="text-h5 q-mb-lg">
      {{ $t('src.components.KnowledgeDebug.knowledge_base_debug_tool') }}
    </div>

    <!-- 数据库状态检查 -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">
          {{ $t('src.components.KnowledgeDebug.database_status_check') }}
        </div>
        <div class="row q-gutter-md">
          <q-btn color="primary" @click="checkDatabaseStatus" :loading="checking">
            {{ $t('src.components.KnowledgeDebug.check_database_status') }}
          </q-btn>
          <q-btn color="secondary" @click="checkChunkData" :loading="checkingChunks">
            {{ $t('src.components.KnowledgeDebug.check_chunk_data') }}
          </q-btn>
        </div>
      </q-card-section>

      <q-card-section v-if="databaseStatus">
        <div class="text-subtitle2 q-mb-sm">
          {{ $t('src.components.KnowledgeDebug.database_status') }}
        </div>
        <pre class="debug-output">{{ databaseStatus }}</pre>
      </q-card-section>
    </q-card>

    <!-- 向量化测试 -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ $t('src.components.KnowledgeDebug.test_embedding') }}</div>
        <q-input
          v-model="testText"
          :label="$t('src.components.KnowledgeDebug.test_text')"
          outlined
          rows="3"
          type="textarea"
          class="q-mb-md"
        />
        <div class="row q-gutter-md">
          <q-btn color="positive" @click="testEmbedding" :loading="testingEmbedding">
            {{ $t('src.components.KnowledgeDebug.test_embedding') }}
          </q-btn>
          <q-btn color="orange" @click="testApiConnection" :loading="testingApi">
            {{ $t('src.components.KnowledgeDebug.test_api_connection') }}
          </q-btn>
          <q-btn color="purple" @click="testLocalModel" :loading="testingLocal">
            {{ $t('src.components.KnowledgeDebug.test_local_model') }}
          </q-btn>
        </div>
      </q-card-section>

      <q-card-section v-if="embeddingResult">
        <div class="text-subtitle2 q-mb-sm">
          {{ $t('src.components.KnowledgeDebug.embedding_result') }}
        </div>
        <pre class="debug-output">{{ embeddingResult }}</pre>
      </q-card-section>
    </q-card>

    <!-- 搜索测试 -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ $t('src.components.KnowledgeDebug.search_test') }}</div>
        <div class="row q-gutter-md q-mb-md">
          <q-input
            v-model="searchQuery"
            :label="$t('src.components.KnowledgeDebug.search_query')"
            outlined
            class="col"
          />
          <q-select
            v-model="selectedKB"
            :options="kbOptions"
            :label="$t('src.components.KnowledgeDebug.select_knowledge_base')"
            outlined
            style="min-width: 200px"
            clearable
          />
        </div>
        <div class="row q-gutter-md">
          <q-btn color="teal" @click="testSearch" :loading="testingSearch">
            {{ $t('src.components.KnowledgeDebug.test_search') }}
          </q-btn>
          <q-btn color="cyan" @click="debugSearchPipeline" :loading="debuggingPipeline">
            {{ $t('src.components.KnowledgeDebug.debug_search_pipeline') }}
          </q-btn>
        </div>
      </q-card-section>

      <q-card-section v-if="searchResult">
        <div class="text-subtitle2 q-mb-sm">
          {{ $t('src.components.KnowledgeDebug.search_result') }}
        </div>
        <pre class="debug-output">{{ searchResult }}</pre>
      </q-card-section>
    </q-card>

    <!-- 数据修复工具 -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-md">
          {{ $t('src.components.KnowledgeDebug.data_repair_tool') }}
        </div>
        <div class="row q-gutter-md q-mb-md">
          <q-input
            v-model="documentId"
            :label="$t('src.components.KnowledgeDebug.document_id')"
            outlined
            type="number"
          />
        </div>
        <div class="row q-gutter-md">
          <q-btn color="warning" @click="regenerateChunks" :loading="regenerating">
            {{ $t('src.components.KnowledgeDebug.regenerate_chunks') }}
          </q-btn>
          <q-btn color="red" @click="updateChunkEmbeddings" :loading="updatingEmbeddings">
            {{ $t('src.components.KnowledgeDebug.update_embeddings') }}
          </q-btn>
          <q-btn color="grey" @click="viewDocumentChunks" :loading="viewingChunks">
            {{ $t('src.components.KnowledgeDebug.view_document_chunks') }}
          </q-btn>
        </div>
      </q-card-section>

      <q-card-section v-if="repairResult">
        <div class="text-subtitle2 q-mb-sm">
          {{ $t('src.components.KnowledgeDebug.repair_result') }}
        </div>
        <pre class="debug-output">{{ repairResult }}</pre>
      </q-card-section>
    </q-card>

    <!-- 日志输出 -->
    <q-card>
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ $t('src.components.KnowledgeDebug.debug_logs') }}</div>
        <q-btn color="red" flat @click="clearLogs" class="q-mb-md">{{
          $t('src.components.KnowledgeDebug.clear_logs')
        }}</q-btn>
        <div class="debug-logs">
          <div v-for="(log, index) in debugLogs" :key="index" class="log-entry">
            <span class="log-time">{{ log.time }}</span>
            <span :class="`log-level log-${log.level}`">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useKnowledge } from '../composeables/useKnowledge';
import { useKnowledgeStore } from '../stores/knowledge';
import { storeToRefs } from 'pinia';
import type { KnowledgeBase } from '../env';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

// 组合式API
const knowledge = useKnowledge();
const knowledgeStore = useKnowledgeStore();
const { knowledgeBases } = storeToRefs(knowledgeStore);

// 响应式数据
const checking = ref(false);
const checkingChunks = ref(false);
const testingEmbedding = ref(false);
const testingApi = ref(false);
const testingLocal = ref(false);
const testingSearch = ref(false);
const debuggingPipeline = ref(false);
const regenerating = ref(false);
const updatingEmbeddings = ref(false);
const viewingChunks = ref(false);

// 表单数据
const testText = ref('这是一个测试文本，用于验证向量化功能是否正常工作。');
const searchQuery = ref('测试');
const selectedKB = ref<{ label: string; value: number } | null>(null);
const documentId = ref<number | null>(null);

// 结果数据
const databaseStatus = ref('');
const embeddingResult = ref('');
const searchResult = ref('');
const repairResult = ref('');

// 调试日志
const debugLogs = ref<Array<{ time: string; level: string; message: string }>>([]);

// 计算属性
const kbOptions = ref<Array<{ label: string; value: number }>>([]);

// 日志记录
const addLog = (level: 'info' | 'warn' | 'error' | 'success', message: string) => {
  debugLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message,
  });

  // 限制日志数量
  if (debugLogs.value.length > 100) {
    debugLogs.value = debugLogs.value.slice(0, 100);
  }
};

// 数据库状态检查
const checkDatabaseStatus = async () => {
  checking.value = true;
  addLog('info', $t('src.components.KnowledgeDebug.start_checking_database_status'));

  try {
    await knowledge.testObjectBoxConnection();

    // 获取基本统计信息
    const stats = await Promise.all(
      knowledgeBases.value.map(async (kb: KnowledgeBase) => {
        const kbStats = await knowledge.getKnowledgeBaseStats(kb.id);
        return {
          id: kb.id,
          name: kb.name,
          ...kbStats,
        };
      }),
    );

    databaseStatus.value = JSON.stringify(stats, null, 2);
    addLog('success', $t('src.components.KnowledgeDebug.database_status_check_completed'));
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    databaseStatus.value =
      $t('src.components.KnowledgeDebug.database_status_check_failed') + errMsg;
    addLog('error', $t('src.components.KnowledgeDebug.database_status_check_failed') + errMsg);
  } finally {
    checking.value = false;
  }
};

// 检查知识块数据
const checkChunkData = async () => {
  checkingChunks.value = true;
  addLog('info', $t('src.components.KnowledgeDebug.start_checking_chunk_data'));

  try {
    // 调用后端调试接口查看所有数据
    const result = await knowledge.callKnowledgeApi<Record<string, unknown>>(
      'debugObjectBoxData',
      '',
    );
    databaseStatus.value = JSON.stringify(result, null, 2);
    addLog('success', $t('src.components.KnowledgeDebug.chunk_data_check_completed'));
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    databaseStatus.value = $t('src.components.KnowledgeDebug.chunk_data_check_failed') + errMsg;
    addLog('error', $t('src.components.KnowledgeDebug.chunk_data_check_failed') + errMsg);
  } finally {
    checkingChunks.value = false;
  }
};

// 测试向量生成
const testEmbedding = async () => {
  if (!testText.value.trim()) return;

  testingEmbedding.value = true;
  addLog(
    'info',
    $t('src.components.KnowledgeDebug.start_testing_embedding') +
      testText.value.slice(0, 50) +
      '...',
  );

  try {
    // 使用新的调试接口
    const result = await knowledge.callKnowledgeApi<{
      success: boolean;
      message?: string;
      [key: string]: unknown;
    }>('debugEmbeddingGeneration', testText.value);

    embeddingResult.value = JSON.stringify(result, null, 2);

    if (result.success) {
      addLog('success', $t('src.components.KnowledgeDebug.embedding_generation_completed'));
    } else {
      addLog(
        'warn',
        $t('src.components.KnowledgeDebug.embedding_generation_failed') + result.message ||
          $t('src.components.KnowledgeDebug.unknown_error'),
      );
    }
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    embeddingResult.value =
      $t('src.components.KnowledgeDebug.embedding_generation_failed') + errMsg;
    addLog('error', $t('src.components.KnowledgeDebug.embedding_generation_failed') + errMsg);
  } finally {
    testingEmbedding.value = false;
  }
};

// 测试API连接
const testApiConnection = async () => {
  testingApi.value = true;
  addLog('info', $t('src.components.KnowledgeDebug.start_testing_api_connection'));

  try {
    const result = await knowledge.testEmbeddingApi(testText.value);
    embeddingResult.value = JSON.stringify(result, null, 2);

    if (result.success) {
      addLog('success', $t('src.components.KnowledgeDebug.api_connection_test_success'));
    } else {
      addLog(
        'warn',
        $t('src.components.KnowledgeDebug.api_connection_test_failed') + result.message,
      );
    }
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    embeddingResult.value = $t('src.components.KnowledgeDebug.api_connection_test_failed') + errMsg;
    addLog('error', $t('src.components.KnowledgeDebug.api_connection_test_failed') + errMsg);
  } finally {
    testingApi.value = false;
  }
};

// 测试本地模型
const testLocalModel = async () => {
  testingLocal.value = true;
  addLog('info', $t('src.components.KnowledgeDebug.start_testing_local_model'));

  try {
    const result = await knowledge.callKnowledgeApi<Record<string, unknown>>(
      'testLocalGGUFEmbedding',
      testText.value,
    );
    embeddingResult.value = JSON.stringify(result, null, 2);
    addLog('success', $t('src.components.KnowledgeDebug.local_model_test_completed'));
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    embeddingResult.value = $t('src.components.KnowledgeDebug.local_model_test_failed') + errMsg;
    addLog('error', $t('src.components.KnowledgeDebug.local_model_test_failed') + errMsg);
  } finally {
    testingLocal.value = false;
  }
};

// 测试搜索
const testSearch = async () => {
  if (!searchQuery.value.trim()) return;

  testingSearch.value = true;
  addLog('info', $t('src.components.KnowledgeDebug.start_testing_search') + searchQuery.value);

  try {
    const results = await knowledge.searchKnowledge(
      searchQuery.value,
      selectedKB.value?.value,
      10,
      0.1,
    );

    searchResult.value = JSON.stringify(
      {
        query: searchQuery.value,
        knowledge_base: selectedKB.value?.label || '全部',
        results_count: results.length,
        results: results.slice(0, 3), // 只显示前3个结果
        searched_at: new Date().toISOString(),
      },
      null,
      2,
    );

    addLog('success', $t('src.components.KnowledgeDebug.search_completed') + results.length);
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    searchResult.value = $t('src.components.KnowledgeDebug.search_failed') + errMsg;
    addLog('error', $t('src.components.KnowledgeDebug.search_failed') + errMsg);
  } finally {
    testingSearch.value = false;
  }
};

// 调试搜索流程
const debugSearchPipeline = async () => {
  if (!searchQuery.value.trim()) return;

  debuggingPipeline.value = true;
  addLog('info', $t('src.components.KnowledgeDebug.start_debugging_search_pipeline'));

  try {
    // 先测试向量生成
    const embedding = await knowledge.generateEmbedding(searchQuery.value);

    // 然后执行搜索
    let searchMethod;
    let kbId;

    if (selectedKB.value) {
      searchMethod = 'searchKnowledgeBase';
      kbId = selectedKB.value.value.toString();
    } else {
      searchMethod = 'searchAllKnowledgeBases';
    }

    const searchParams = selectedKB.value
      ? [kbId, searchQuery.value, '10']
      : [searchQuery.value, '10'];

    const rawResult = await knowledge.callKnowledgeApi<Record<string, unknown>>(
      searchMethod,
      ...searchParams,
    );

    searchResult.value = JSON.stringify(
      {
        debug_info: {
          query: searchQuery.value,
          embedding_generated: !!embedding,
          embedding_dimension: embedding?.length || 0,
          search_method: searchMethod,
          search_params: searchParams,
        },
        raw_backend_response: rawResult,
      },
      null,
      2,
    );

    addLog('success', $t('src.components.KnowledgeDebug.search_pipeline_debug_completed'));
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    searchResult.value = $t('src.components.KnowledgeDebug.search_pipeline_debug_failed') + errMsg;
    addLog('error', $t('src.components.KnowledgeDebug.search_pipeline_debug_failed') + errMsg);
  } finally {
    debuggingPipeline.value = false;
  }
};

// 重新生成知识块
const regenerateChunks = async () => {
  if (!documentId.value) return;

  regenerating.value = true;
  addLog(
    'info',
    $t('src.components.KnowledgeDebug.start_regenerating_document_chunks') + documentId.value,
  );

  try {
    await knowledge.regenerateDocumentChunks(documentId.value);
    repairResult.value =
      $t('src.components.KnowledgeDebug.document_chunks_regenerated') + documentId.value;
    addLog('success', $t('src.components.KnowledgeDebug.document_chunks_regenerated_completed'));
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    repairResult.value =
      $t('src.components.KnowledgeDebug.document_chunks_regenerated_failed') + errMsg;
    addLog(
      'error',
      $t('src.components.KnowledgeDebug.document_chunks_regenerated_failed') + errMsg,
    );
  } finally {
    regenerating.value = false;
  }
};

// 更新向量
const updateChunkEmbeddings = async () => {
  if (!documentId.value) return;

  updatingEmbeddings.value = true;
  addLog(
    'info',
    $t('src.components.KnowledgeDebug.start_updating_document_embeddings') + documentId.value,
  );

  try {
    await knowledge.updateChunkEmbeddings(documentId.value);
    repairResult.value =
      $t('src.components.KnowledgeDebug.document_embeddings_updated') + documentId.value;
    addLog('success', $t('src.components.KnowledgeDebug.document_embeddings_updated_completed'));
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    repairResult.value =
      $t('src.components.KnowledgeDebug.document_embeddings_updated_failed') + errMsg;
    addLog(
      'error',
      $t('src.components.KnowledgeDebug.document_embeddings_updated_failed') + errMsg,
    );
  } finally {
    updatingEmbeddings.value = false;
  }
};

// 查看文档块
const viewDocumentChunks = async () => {
  if (!documentId.value) return;

  viewingChunks.value = true;
  addLog(
    'info',
    $t('src.components.KnowledgeDebug.start_viewing_document_chunks') + documentId.value,
  );

  try {
    const result = await knowledge.callKnowledgeApi<Record<string, unknown>>(
      'viewDocumentChunks',
      documentId.value.toString(),
    );
    repairResult.value = JSON.stringify(result, null, 2);
    addLog('success', $t('src.components.KnowledgeDebug.document_chunks_viewed_completed'));
  } catch (error) {
    const errMsg =
      error instanceof Error ? error.message : $t('src.components.KnowledgeDebug.unknown_error');
    repairResult.value = $t('src.components.KnowledgeDebug.document_chunks_viewed_failed') + errMsg;
    addLog('error', $t('src.components.KnowledgeDebug.document_chunks_viewed_failed') + errMsg);
  } finally {
    viewingChunks.value = false;
  }
};

// 清空日志
const clearLogs = () => {
  debugLogs.value = [];
  addLog('info', $t('src.components.KnowledgeDebug.logs_cleared'));
};

// 组件挂载时的初始化
onMounted(async () => {
  // 加载知识库列表
  if (knowledgeBases.value.length === 0) {
    await knowledgeStore.loadKnowledgeBases();
  }

  // 构建知识库选项
  kbOptions.value = knowledgeBases.value.map((kb: KnowledgeBase) => ({
    label: kb.name,
    value: kb.id,
  }));

  addLog('info', $t('src.components.KnowledgeDebug.debug_tool_initialized'));
});
</script>

<style scoped>
.knowledge-debug {
  max-width: 1200px;
  margin: 0 auto;
}

.debug-output {
  border: 1px solid #797979;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.debug-logs {
  background: #1a1a1a;
  color: #fff;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.log-entry {
  display: block;
  margin-bottom: 4px;
}

.log-time {
  color: #888;
  margin-right: 8px;
}

.log-level {
  margin-right: 8px;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
}

.log-info {
  background: #2196f3;
}
.log-success {
  background: #4caf50;
}
.log-warn {
  background: #ff9800;
}
.log-error {
  background: #f44336;
}

.log-message {
  color: #fff;
}
</style>
