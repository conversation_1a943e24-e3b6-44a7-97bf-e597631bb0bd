<template>
  <div class="model-manager">
    <!-- 标题和添加按钮 -->
    <div class="row items-center justify-between q-mb-md">
      <div class="text-subtitle2">{{ $t('src.components.settings.ModelManager.title') }}</div>
      <q-btn-dropdown
        icon="mdi-plus"
        flat
        :label="$t('src.components.settings.ModelManager.addModel')"
        dropdown-icon="expand_more"
        dense
      >
        <q-list dense>
          <q-item
            v-for="(label, type) in ModelTypeLabels"
            :key="type"
            clickable
            v-close-popup
            @click="startAddingModel(type)"
          >
            <q-item-section avatar>
              <q-icon :name="getModelTypeIcon(type)" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ label }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-btn-dropdown>
    </div>

    <!-- 已存在的模型分类展示 -->
    <div v-if="existingCategories.length === 0" class="text-grey-5 text-center q-py-lg">
      <q-icon name="psychology" size="48px" class="q-mb-md" />
      <div>{{ $t('src.components.settings.ModelManager.noModels') }}</div>
    </div>

    <div v-else>
      <div v-for="category in existingCategories" :key="category" class="model-category q-mb-lg">
        <!-- 分类标题 -->
        <div class="q-mb-sm text-grey">
          {{ getCategoryLabel(category) }}
        </div>

        <!-- 该分类下的模型 -->
        <div class="model-chips q-mb-md">
          <q-chip
            v-for="model in getCategoryModels(category)"
            :key="model"
            removable
            color="primary"
            text-color="white"
            :label="model"
            @remove="removeModel(category, model)"
            class="q-ma-xs"
          >
            <q-tooltip>{{
              $t('src.components.settings.ModelManager.removeModelTooltip')
            }}</q-tooltip>
          </q-chip>
        </div>

        <!-- 如果正在为此分类添加模型，显示输入框 -->
        <div v-if="addingToCategory === category" class="add-model-input">
          <!-- 工具调用分组使用下拉选择 -->
          <div v-if="isToolsCallsCategory(category)" class="row q-gutter-sm">
            <div class="col">
              <q-select
                v-model="selectedModel"
                :options="availableModelOptions"
                outlined
                dense
                :placeholder="`${$t('src.components.settings.ModelManager.selectModel')} ${getCategoryLabel(category)}`"
                :rules="[
                  (val) => !!val || $t('src.components.settings.ModelManager.selectRequired'),
                  (val) =>
                    !getCategoryModels(category).includes(val) ||
                    $t('src.components.settings.ModelManager.modelExists'),
                ]"
                @keyup.esc="cancelAdding"
              >
                <template v-slot:prepend>
                  <q-icon name="build" />
                </template>
              </q-select>
            </div>
            <div class="col-auto">
              <q-btn
                color="primary"
                icon="check"
                :disable="!selectedModel || getCategoryModels(category).includes(selectedModel)"
                @click="addSelectedModel"
              />
              <q-btn flat icon="close" class="q-ml-xs" @click="cancelAdding" />
            </div>
          </div>

          <!-- 其他分组使用文本输入 -->
          <div v-else class="column q-gutter-sm">
            <div class="row q-gutter-sm">
              <div class="column col">
                <q-input
                  ref="newModelInput"
                  v-model="newModelName"
                  outlined
                  dense
                  :placeholder="`${$t('src.components.settings.ModelManager.inputModel')} ${getCategoryLabel(category)}`"
                  :rules="[
                    (val) => !!val || $t('src.components.settings.ModelManager.inputRequired'),
                    (val) =>
                      !getCategoryModels(category).includes(val) ||
                      $t('src.components.settings.ModelManager.modelExists'),
                  ]"
                  @keyup.enter="addModel"
                  @keyup.esc="cancelAdding"
                >
                  <template v-slot:prepend>
                    <q-icon name="psychology" />
                  </template>
                </q-input>
                <div class="row items-center no-wrap gap-sm">
                  <q-btn
                    flat
                    dense
                    icon="close"
                    padding="xs md"
                    class="q-ml-xs"
                    :label="$t('src.components.settings.common.cancel')"
                    @click="cancelAdding"
                  />
                  <q-space />
                  <!-- 工具调用能力选择 -->
                  <q-checkbox v-model="enableToolsCalls" color="primary" size="sm" dense />
                  <span
                    class="text-caption q-ml-sm cursor-pointer"
                    @click="enableToolsCalls = !enableToolsCalls"
                    >{{ $t('src.components.settings.ModelManager.toolsCalls') }}</span
                  >
                  <q-btn
                    dense
                    icon="check"
                    padding="xs md"
                    :label="$t('src.components.settings.common.add')"
                    color="primary"
                    class="q-ml-md"
                    :disable="!newModelName || getCategoryModels(category).includes(newModelName)"
                    @click="addModel"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 如果正在为新分类添加模型，显示新分类区域 -->
    <div
      v-if="addingToCategory && !existingCategories.includes(addingToCategory)"
      class="new-category"
    >
      <div class="text-h6 text-primary q-mb-sm">
        <q-icon name="category" class="q-mr-sm" />
        {{ getCategoryLabel(addingToCategory) }}
        <q-chip
          size="sm"
          color="orange"
          text-color="white"
          :label="$t('src.components.settings.ModelManager.newCategory')"
          class="q-ml-sm"
        />
      </div>

      <div class="add-model-input">
        <!-- 工具调用分组使用下拉选择 -->
        <div v-if="isToolsCallsCategory(addingToCategory)" class="row q-gutter-sm">
          <div class="col">
            <q-select
              v-model="selectedModel"
              :options="availableModelOptions"
              outlined
              dense
              :placeholder="`${$t('src.components.settings.ModelManager.selectModel')} ${getCategoryLabel(addingToCategory)}`"
              :rules="[(val) => !!val || $t('src.components.settings.ModelManager.selectRequired')]"
              @keyup.esc="cancelAdding"
            >
              <template v-slot:prepend>
                <q-icon name="build" />
              </template>
            </q-select>
          </div>
          <div class="col-auto">
            <q-btn
              color="primary"
              icon="check"
              :disable="!selectedModel"
              @click="addSelectedModel"
            />
            <q-btn flat icon="close" class="q-ml-xs" @click="cancelAdding" />
          </div>
        </div>

        <!-- 其他分组使用文本输入 -->
        <div v-else class="column q-gutter-sm">
          <div class="row q-gutter-sm">
            <div class="col">
              <q-input
                ref="newModelInput"
                v-model="newModelName"
                outlined
                dense
                :placeholder="`${$t('src.components.settings.ModelManager.inputModel')} ${getCategoryLabel(addingToCategory)}`"
                :rules="[
                  (val) => !!val || $t('src.components.settings.ModelManager.inputRequired'),
                ]"
                @keyup.enter="addModel"
                @keyup.esc="cancelAdding"
              >
                <template v-slot:prepend>
                  <q-icon name="psychology" />
                </template>

                <!-- 工具调用能力选择 -->
                <template v-slot:append>
                  <q-checkbox
                    v-model="enableToolsCalls"
                    color="primary"
                    size="sm"
                    :label="$t('src.components.settings.ModelManager.toolsCalls')"
                  />
                </template>
              </q-input>
            </div>
            <div class="col-auto">
              <q-btn color="primary" icon="check" :disable="!newModelName" @click="addModel" />
              <q-btn flat icon="close" class="q-ml-xs" @click="cancelAdding" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作提示 -->
    <div class="text-caption text-grey-6 q-mt-md">
      <q-icon name="info" class="q-mr-xs" />
      {{ $t('src.components.settings.ModelManager.description') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { ModelType, ModelTypeLabels, type CategorizedModels } from 'src/types/modelCategories';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

// Props
interface Props {
  modelValue: CategorizedModels;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: CategorizedModels): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 新模型名称输入
const newModelName = ref('');

// 当前正在添加模型的分类
const addingToCategory = ref<ModelType | null>(null);

// 选择的模型（用于toolsCalls分组）
const selectedModel = ref<string | null>(null);

// 是否同时添加到工具调用分组
const enableToolsCalls = ref(false);

// 输入框引用
const newModelInput = ref();

// 已存在模型的分类列表
const existingCategories = computed(() => {
  return Object.keys(props.modelValue)
    .filter((category) => {
      const models = props.modelValue[category as ModelType];
      return Array.isArray(models) && models.length > 0;
    })
    .filter((category) => Object.values(ModelType).includes(category as ModelType))
    .map((category) => category as ModelType);
});

// 获取指定分类下的模型列表，确保返回字符串数组
const getCategoryModels = (category: ModelType): string[] => {
  const models = props.modelValue[category];
  if (!models) return [];

  // 确保返回的是字符串数组，而不是字符串
  if (Array.isArray(models)) {
    return models;
  }

  // 如果意外是字符串，则包装成数组
  if (typeof models === 'string') {
    return [models];
  }

  return [];
};

// 获取分类标签，确保能正确显示
const getCategoryLabel = (category: ModelType): string => {
  return ModelTypeLabels[category] || category;
};

// 获取所有已存在的模型列表（用于toolsCalls分组选择）
const allExistingModels = computed(() => {
  const models: string[] = [];
  Object.keys(props.modelValue).forEach((category) => {
    if (category !== ModelType.TOOLS_CALLS.toString()) {
      const categoryModels = getCategoryModels(category as ModelType);
      models.push(...categoryModels);
    }
  });
  return [...new Set(models)].sort(); // 去重并排序
});

// 获取可选择的模型选项（用于q-select）
const availableModelOptions = computed(() => {
  return allExistingModels.value.map((model) => ({
    label: model,
    value: model,
  }));
});

// 判断是否为工具调用分组
const isToolsCallsCategory = (category: ModelType): boolean => {
  return category === ModelType.TOOLS_CALLS;
};

// 获取模型类型对应的图标
const getModelTypeIcon = (type: ModelType): string => {
  const iconMap: Record<ModelType, string> = {
    [ModelType.TEXT_GENERATION]: 'mdi-text',
    [ModelType.MULTIMODAL]: 'mdi-multimedia',
    [ModelType.REASONING]: 'mdi-brain',
    [ModelType.AUDIO_UNDERSTANDING]: 'mdi-microphone',
    [ModelType.VIDEO_UNDERSTANDING]: 'mdi-video',
    [ModelType.VIDEO_GENERATION]: 'mdi-video-plus',
    [ModelType.IMAGE_PROCESSING]: 'mdi-image-edit',
    [ModelType.IMAGE_UNDERSTANDING]: 'mdi-image-search',
    [ModelType.IMAGE_GENERATION]: 'mdi-image-plus',
    [ModelType.EMBEDDING]: 'mdi-vector-triangle',
    [ModelType.TEXT_TO_SPEECH]: 'graphic_eq',
    [ModelType.SPEECH_TO_TEXT]: 'hearing',
    [ModelType.RANKING]: 'mdi-sort-numeric-variant',
    [ModelType.TOOLS_CALLS]: 'mdi-tools',
  };

  return iconMap[type] || 'mdi-help-circle';
};

// 开始添加模型到指定分类
const startAddingModel = async (category: ModelType) => {
  addingToCategory.value = category;
  newModelName.value = '';
  selectedModel.value = null;
  enableToolsCalls.value = false;

  // 等待DOM更新后聚焦输入框（非工具调用分组）
  if (!isToolsCallsCategory(category)) {
    await nextTick();
    if (newModelInput.value) {
      newModelInput.value.focus();
    }
  }
};

// 取消添加模型
const cancelAdding = () => {
  addingToCategory.value = null;
  newModelName.value = '';
  selectedModel.value = null;
  enableToolsCalls.value = false;
};

// 添加模型（文本输入方式）
const addModel = () => {
  if (!newModelName.value || !addingToCategory.value) {
    return;
  }

  const category = addingToCategory.value;
  const updatedModels = { ...props.modelValue };

  // 获取当前分类下的模型列表
  const currentModels = getCategoryModels(category);

  // 检查模型是否已存在
  if (currentModels.includes(newModelName.value)) {
    return;
  }

  // 添加新模型到当前分类
  updatedModels[category] = [...currentModels, newModelName.value];

  // 如果启用了工具调用能力，同时添加到工具调用分组
  if (enableToolsCalls.value) {
    const toolsCallsModels = getCategoryModels(ModelType.TOOLS_CALLS);
    if (!toolsCallsModels.includes(newModelName.value)) {
      updatedModels[ModelType.TOOLS_CALLS] = [...toolsCallsModels, newModelName.value];
    }
  }

  emit('update:modelValue', updatedModels);

  // 清空输入并取消添加状态
  newModelName.value = '';
  addingToCategory.value = null;
  enableToolsCalls.value = false;
};

// 添加选择的模型（下拉选择方式，用于toolsCalls分组）
const addSelectedModel = () => {
  if (!selectedModel.value || !addingToCategory.value) {
    return;
  }

  const category = addingToCategory.value;
  const updatedModels = { ...props.modelValue };

  // 获取当前分类下的模型列表
  const currentModels = getCategoryModels(category);

  // 检查模型是否已存在
  if (currentModels.includes(selectedModel.value)) {
    return;
  }

  // 添加选择的模型
  updatedModels[category] = [...currentModels, selectedModel.value];

  emit('update:modelValue', updatedModels);

  // 清空选择并取消添加状态
  selectedModel.value = null;
  addingToCategory.value = null;
};

// 删除模型
const removeModel = (category: ModelType, modelName: string) => {
  const updatedModels = { ...props.modelValue };
  const currentModels = getCategoryModels(category);

  // 过滤掉要删除的模型
  const filteredModels = currentModels.filter((model) => model !== modelName);

  // 如果分类下没有模型了，删除该分类
  if (filteredModels.length === 0) {
    delete updatedModels[category];
  } else {
    updatedModels[category] = filteredModels;
  }

  emit('update:modelValue', updatedModels);
};
</script>
