/**
 * 统一的性能和内存管理配置
 * 所有性能相关的阈值和配置都应该在这里定义
 */

// 检测是否为开发环境
export const isDevelopment = process.env.NODE_ENV === 'development' || process.env.DEV === 'true';

// 内存阈值配置（字节）
export const MEMORY_THRESHOLDS = {
  // 桌面应用的内存阈值
  desktop: {
    warning: 512 * 1024 * 1024, // 512MB - 警告级别
    critical: 1024 * 1024 * 1024, // 1GB - 临界级别
    emergency: 2048 * 1024 * 1024, // 2GB - 紧急级别
  },
  // Web应用的内存阈值（如果需要）
  web: {
    warning: 256 * 1024 * 1024, // 256MB
    critical: 512 * 1024 * 1024, // 512MB
    emergency: 1024 * 1024 * 1024, // 1GB
  },
  // 获取当前环境的阈值
  get current() {
    // 可以根据实际运行环境动态选择
    return this.desktop; // 默认使用桌面应用阈值
  },
};

// 内存泄漏检测配置
export const MEMORY_LEAK_DETECTION = {
  enabled: isDevelopment, // 仅在开发环境启用
  detectionInterval: 30000, // 30秒检测一次
  objectGrowthThreshold: 0.5, // 对象数量增长50%视为异常
  minObjectCount: 500, // 最小对象数量阈值
  suspiciousGrowthRate: 0.3, // 30%的增长率视为可疑
};

// 内存监控配置
export const MEMORY_MONITOR = {
  enabled: true, // 生产环境也需要基本监控
  monitorInterval: isDevelopment ? 5000 : 30000, // 开发5秒，生产30秒
  autoCleanup: true, // 自动清理
  aggressiveCleanup: true, // 是否启用激进清理
  reportMetrics: isDevelopment, // 仅开发环境报告详细指标
};

// 性能监控配置
export const PERFORMANCE_MONITOR = {
  enabled: isDevelopment, // 仅在开发环境启用
  trackingEnabled: isDevelopment, // 详细追踪仅开发环境
  reportInterval: 10000, // 10秒报告一次
  metricsRetention: 300000, // 指标保留5分钟
  fpsTracking: isDevelopment, // FPS追踪仅开发环境
  operationTracking: isDevelopment, // 操作追踪仅开发环境
  timingPrecision: isDevelopment ? 'high' : 'low', // 计时精度
};

// 资源清理策略配置
export const CLEANUP_STRATEGIES = {
  editor: {
    maxAge: 30 * 60 * 1000, // 30分钟
    maxIdle: 10 * 60 * 1000, // 10分钟空闲
    maxCount: 50, // 最大数量
    cleanupRatio: 0.3, // 清理30%
  },
  image: {
    maxAge: 20 * 60 * 1000, // 20分钟
    maxIdle: 5 * 60 * 1000, // 5分钟空闲
    maxCount: 100,
    cleanupRatio: 0.5,
  },
  cache: {
    maxAge: 15 * 60 * 1000, // 15分钟
    maxIdle: 3 * 60 * 1000, // 3分钟空闲
    maxCount: 200,
    cleanupRatio: 0.6,
  },
  event: {
    maxAge: 60 * 60 * 1000, // 1小时
    maxIdle: 30 * 60 * 1000, // 30分钟空闲
    maxCount: 1000,
    cleanupRatio: 0.2,
  },
  timer: {
    maxAge: 10 * 60 * 1000, // 10分钟
    maxIdle: 2 * 60 * 1000, // 2分钟空闲
    maxCount: 50,
    cleanupRatio: 0.8,
  },
  observer: {
    maxAge: 60 * 60 * 1000, // 1小时
    maxIdle: 15 * 60 * 1000, // 15分钟空闲
    maxCount: 20,
    cleanupRatio: 0.4,
  },
  other: {
    maxAge: 5 * 60 * 1000, // 5分钟
    maxIdle: 1 * 60 * 1000, // 1分钟空闲
    maxCount: 100,
    cleanupRatio: 0.7,
  },
};

// 编辑器实例池配置
export const EDITOR_POOL = {
  maxInstances: 20, // 最大实例数
  prewarmCount: isDevelopment ? 2 : 3, // 预热实例数
  recycleDelay: 10000, // 10秒后回收
  reuseThreshold: 5000, // 5秒内可重用
};

// 虚拟渲染配置
export const VIRTUAL_RENDERING = {
  enabled: true, // 默认启用
  bufferSize: 5, // 缓冲区大小
  scrollThreshold: 50, // 滚动阈值
  renderDelay: 16, // 渲染延迟（60fps）
};

// 状态管理配置
export const STATE_MANAGEMENT = {
  batchDelay: 16, // 批处理延迟（60fps）
  persistenceEnabled: true, // 启用持久化
  persistenceInterval: 30000, // 30秒持久化一次
  compressionEnabled: true, // 启用压缩
  maxHistorySize: 100, // 最大历史记录
};

// 命令系统配置
export const COMMAND_SYSTEM = {
  historyLimit: 200, // 命令历史限制
  executionTimeout: 10000, // 10秒执行超时
  recentCommandsLimit: 10, // 最近命令数量
  paletteEnabled: true, // 启用命令面板
};

// 拖拽系统配置
export const DRAG_SYSTEM = {
  throttleDelay: 16, // 节流延迟（60fps）
  smoothing: true, // 启用平滑
  prediction: true, // 启用预测
  hapticFeedback: false, // 触觉反馈
  enableMomentum: true, // 启用惯性
  momentumDecay: 0.95, // 惯性衰减系数
};

// 日志配置
export const LOGGING = {
  enabled: isDevelopment, // 仅开发环境启用详细日志
  level: isDevelopment ? 'debug' : 'error', // 日志级别
  performanceLogs: isDevelopment, // 性能日志
  memoryLogs: isDevelopment, // 内存日志
  operationLogs: isDevelopment, // 操作日志
};

// 调试工具配置
export const DEBUG_TOOLS = {
  enabled: isDevelopment, // 仅开发环境启用
  showMetricsOverlay: false, // 显示指标覆盖层
  showMemoryStats: isDevelopment, // 显示内存统计
  showFPSCounter: isDevelopment, // 显示FPS计数器
  enableProfiling: false, // 启用性能分析
};

// 导出统一配置对象
export const PERFORMANCE_CONFIG = {
  memory: {
    thresholds: MEMORY_THRESHOLDS.current,
    monitor: MEMORY_MONITOR,
    leakDetection: MEMORY_LEAK_DETECTION,
    cleanupStrategies: CLEANUP_STRATEGIES,
  },
  performance: {
    monitor: PERFORMANCE_MONITOR,
    virtualRendering: VIRTUAL_RENDERING,
  },
  editor: {
    pool: EDITOR_POOL,
    state: STATE_MANAGEMENT,
  },
  ui: {
    command: COMMAND_SYSTEM,
    drag: DRAG_SYSTEM,
  },
  debug: {
    logging: LOGGING,
    tools: DEBUG_TOOLS,
  },
  isDevelopment,
};

// 配置验证函数
export function validateConfig(): boolean {
  try {
    // 验证内存阈值的合理性
    const thresholds = MEMORY_THRESHOLDS.current;
    if (thresholds.warning >= thresholds.critical || thresholds.critical >= thresholds.emergency) {
      console.error('❌ 内存阈值配置错误：warning < critical < emergency');
      return false;
    }

    // 验证时间间隔的合理性
    if (MEMORY_MONITOR.monitorInterval < 1000) {
      console.error('❌ 内存监控间隔不能小于1秒');
      return false;
    }

    return true;
  } catch (error) {
    console.error('❌ 配置验证失败:', error);
    return false;
  }
}

// 获取环境特定的配置
export function getEnvironmentConfig() {
  return {
    ...PERFORMANCE_CONFIG,
    // 可以根据环境添加特定配置
    environment: isDevelopment ? 'development' : 'production',
  };
}

// 导出类型定义
export type PerformanceConfigType = typeof PERFORMANCE_CONFIG;
export type MemoryThresholdsType = typeof MEMORY_THRESHOLDS.current;
export type CleanupStrategyType = typeof CLEANUP_STRATEGIES;
