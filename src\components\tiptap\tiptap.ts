import { Dark } from 'quasar';
import githubLight from 'highlight.js/styles/github.css?url';
import githubDark from 'highlight.js/styles/github-dark.css?url';
import { Node } from '@tiptap/core';
import { useSqlite } from 'src/composeables/useSqlite';

import type { Selection, Transaction } from 'prosemirror-state';
import type { Fragment, Node as ProsemirrorNode } from 'prosemirror-model';
import { TableCell } from '@tiptap/extension-table-cell';
import type { Editor as VueEditor } from '@tiptap/vue-3';
import type { JSONContent } from '@tiptap/vue-3';
import { useDocStore } from 'src/stores/doc';

export interface TableSelection extends Selection {
  $anchorCell?: { pos: number } | null;
  $headCell?: { pos: number } | null;
}

export interface TableEditor {
  view: {
    state: {
      doc: {
        nodeAt: (pos: number) => ProsemirrorNode | null;
        nodesBetween: (
          start: number,
          end: number,
          callback: (node: ProsemirrorNode) => void,
        ) => void;
        type: {
          schema: {
            node: (
              type: string,
              attrs?: Record<string, unknown>,
              content?: ProsemirrorNode,
            ) => ProsemirrorNode;
          };
        };
      };
      tr: Transaction;
      selection: TableSelection;
    };
    dispatch: (tr: Transaction) => void;
  };
  commands: {
    mergeCells: () => boolean;
    splitCell: () => boolean;
  };
}

export interface ReadyEvent {
  id: string;
  editor: VueEditor;
}

// 自定义表格单元格扩展
export const CustomTableCell = TableCell.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      colspan: {
        default: 1,
        parseHTML: (element) => element.getAttribute('colspan'),
        renderHTML: (attributes) => {
          if (!attributes.colspan) {
            return {};
          }
          return {
            colspan: attributes.colspan,
          };
        },
      },
      rowspan: {
        default: 1,
        parseHTML: (element) => element.getAttribute('rowspan'),
        renderHTML: (attributes) => {
          if (!attributes.rowspan) {
            return {};
          }
          return {
            rowspan: attributes.rowspan,
          };
        },
      },
    };
  },
});

// 添加合并单元格命令
export const addTableCommands = (editor: TableEditor) => {
  editor.commands.mergeCells = () => {
    const { state, dispatch } = editor.view;
    const { selection } = state;
    const { $anchorCell, $headCell } = selection;

    if (!$anchorCell || !$headCell) return false;

    const start = $anchorCell.pos;
    const end = $headCell.pos;

    if (start === end) return false;

    const startCell = state.doc.nodeAt(start);
    const endCell = state.doc.nodeAt(end);

    if (!startCell || !endCell) return false;

    const tr = state.tr;
    const content: ProsemirrorNode[] = [];

    // 收集所有选中单元格的内容
    state.doc.nodesBetween(start, end, (node: ProsemirrorNode) => {
      if (node.type.name === 'tableCell' || node.type.name === 'tableHeader') {
        content.push(node.content as unknown as ProsemirrorNode);
      }
    });

    // 合并内容
    const mergedContent = content.reduce((acc: ProsemirrorNode, curr: ProsemirrorNode) => {
      const fragment = curr as unknown as Fragment;
      const newContent = acc.content.append(fragment);
      return acc.copy(newContent);
    }, state.doc.type.schema.node('paragraph'));

    // 设置合并后的单元格属性
    const attrs = {
      ...startCell.attrs,
      colspan: Math.abs($headCell.pos - $anchorCell.pos) + 1,
    };

    // 替换选中区域为合并后的单元格
    const newNode = state.doc.type.schema.node(
      'tableCell',
      attrs,
      mergedContent as unknown as ProsemirrorNode,
    );
    if (newNode) {
      tr.replaceWith(start, end, newNode);
      dispatch(tr);
      return true;
    }
    return false;
  };

  editor.commands.splitCell = () => {
    const { state, dispatch } = editor.view;
    const { selection } = state;
    const { $anchorCell } = selection;

    if (!$anchorCell) return false;

    const cell = state.doc.nodeAt($anchorCell.pos);
    if (!cell) return false;

    const { colspan, rowspan } = cell.attrs;
    if (colspan === 1 && rowspan === 1) return false;

    const tr = state.tr;
    const content = cell.content;

    // 创建新的单元格
    const newCell = state.doc.type.schema.node(
      'tableCell',
      {},
      content as unknown as ProsemirrorNode,
    );
    if (newCell) {
      tr.replaceWith($anchorCell.pos, $anchorCell.pos + cell.nodeSize, newCell);
      dispatch(tr);
      return true;
    }
    return false;
  };
};

// 将全局变量改为 Map
const editorDestroyingMap = new Map<number | string, boolean>();

export const tiptap = () => {
  // 监听主题变化
  const updateTheme = () => {
    const isDark = Dark.isActive;
    const styleElement = document.getElementById('code-block-theme');

    if (styleElement) {
      styleElement.remove();
    }

    const newStyle = document.createElement('link');
    newStyle.id = 'code-block-theme';
    newStyle.rel = 'stylesheet';
    newStyle.href = isDark ? githubDark : githubLight;

    document.head.appendChild(newStyle);
  };

  return {
    updateTheme,
    ImageWithId,
    setEditorDestroying: (instanceKey: number | string, destroying: boolean) => {
      editorDestroyingMap.set(instanceKey, destroying);
    },
    isEditorDestroying: (instanceKey: number | string) => {
      return editorDestroyingMap.get(instanceKey) || false;
    },
  };
};

// 添加 ImageWithId 扩展
export const ImageWithId = Node.create({
  name: 'image',
  group: 'block',
  atom: true,

  addOptions() {
    return {
      documentId: null, // 添加文档ID选项
    };
  },

  addAttributes() {
    return {
      // 保留src属性用于序列化，确保永远不为null
      src: {
        default: (attrs?: Record<string, unknown>) => {
          // 如果有 data-image-id，使用 image:// 格式
          if (attrs && attrs['data-image-id']) {
            const imageId = attrs['data-image-id'];
            if (typeof imageId === 'string' || typeof imageId === 'number') {
              return `image://${String(imageId)}`;
            }
          }
          // 否则使用占位符
          return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjwvc3ZnPg==';
        },
        // 不序列化src属性到JSON（保存时不包含）
        renderHTML: false,
        parseHTML: false,
      },
      alt: { default: '' },
      'data-image-id': {
        default: null,
        // 这是我们保存到数据库的唯一标识
        renderHTML: (attributes) => {
          if (!attributes['data-image-id']) {
            return false;
          }
          return {
            'data-image-id': attributes['data-image-id'],
          };
        },
      },
      title: { default: '' },
      width: { default: null },
      height: { default: null },
    };
  },

  parseHTML() {
    return [
      {
        // 优先匹配有data-image-id的图片节点
        tag: 'img[data-image-id]',
        getAttrs: (element) => {
          const img = element as HTMLImageElement;
          return {
            'data-image-id': img.getAttribute('data-image-id'),
            alt: img.getAttribute('alt'),
            title: img.getAttribute('title'),
            width: img.getAttribute('width'),
            height: img.getAttribute('height'),
            // 不解析src属性
          };
        },
      },
      {
        // 向后兼容：解析image://格式的src
        tag: 'img[src^="image://"]',
        getAttrs: (element) => {
          const img = element as HTMLImageElement;
          const src = img.getAttribute('src');
          if (!src || !src.startsWith('image://')) {
            return false;
          }
          const imageId = src.replace('image://', '');
          return {
            'data-image-id': imageId,
            alt: img.getAttribute('alt'),
            title: img.getAttribute('title'),
            width: img.getAttribute('width'),
            height: img.getAttribute('height'),
          };
        },
      },
      {
        // 最后兼容普通的img标签（用于粘贴等场景）
        tag: 'img[src]',
        getAttrs: (element) => {
          const img = element as HTMLImageElement;
          const src = img.getAttribute('src');
          // 如果是blob或data URL，不解析（需要通过其他方式处理）
          if (src?.startsWith('blob:') || src?.startsWith('data:')) {
            return false;
          }
          return {
            src: src, // 临时保存，后续会被处理
            alt: img.getAttribute('alt'),
            title: img.getAttribute('title'),
            width: img.getAttribute('width'),
            height: img.getAttribute('height'),
          };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    // 只渲染必要的属性，不包含src（src会在NodeView中动态设置）
    const attrs: Record<string, string> = {};

    if (HTMLAttributes['data-image-id']) {
      attrs['data-image-id'] = HTMLAttributes['data-image-id'];
    }
    if (HTMLAttributes.alt) {
      attrs.alt = HTMLAttributes.alt;
    }
    if (HTMLAttributes.title) {
      attrs.title = HTMLAttributes.title;
    }
    if (HTMLAttributes.width) {
      attrs.width = HTMLAttributes.width;
    }
    if (HTMLAttributes.height) {
      attrs.height = HTMLAttributes.height;
    }

    // 添加一个占位符src，避免HTML验证错误
    attrs.src =
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjwvc3ZnPg==';

    return ['img', attrs];
  },

  // 添加自定义的 Markdown 序列化逻辑，覆盖默认的序列化器
  addStorage() {
    return {
      markdown: {
        serialize: (
          state: { write: (text: string) => void; esc: (text: string) => string },
          node: { attrs: Record<string, unknown> },
        ) => {
          // 安全地获取属性，确保类型安全
          const alt = typeof node.attrs.alt === 'string' ? node.attrs.alt : '';
          const title = typeof node.attrs.title === 'string' ? node.attrs.title : '';

          // 构造安全的 src 值
          let src = node.attrs.src;

          // 如果 src 为 null 或 undefined，使用 data-image-id 构造
          if (!src || src === null || src === undefined) {
            const imageId = node.attrs['data-image-id'];
            if (imageId && (typeof imageId === 'string' || typeof imageId === 'number')) {
              src = `image://${String(imageId)}`;
            } else {
              // 使用占位符
              src = 'placeholder-image';
            }
          }

          // 安全地处理特殊字符 - 确保 src 是字符串
          const srcStr =
            typeof src === 'string'
              ? src
              : typeof src === 'number'
                ? String(src)
                : 'placeholder-image';
          const safeSrc = srcStr.replace(/[()]/g, '\\$&');
          const safeTitle = title ? ` "${title.replace(/"/g, '\\"')}"` : '';

          // 写入 Markdown 格式
          state.write(`![${state.esc(alt)}](${safeSrc}${safeTitle})`);
        },
        parse: {
          // 解析由 markdown-it 处理
        },
      },
    };
  },

  addNodeView() {
    const extensionOptions = this.options;
    return ({ HTMLAttributes, getPos, editor }) => {
      // 确保 HTMLAttributes 存在
      if (!HTMLAttributes) {
        console.error('HTMLAttributes 为空');
        HTMLAttributes = {};
      }

      const dom = document.createElement('div');
      dom.className = 'image-container';

      const img = document.createElement('img');
      img.className = 'image-preview';
      img.alt = HTMLAttributes?.alt || '';
      img.title = HTMLAttributes?.title || '';

      // 获取图片ID（只从data-image-id获取）
      const imageId = HTMLAttributes['data-image-id'];

      // 重要：在DOM元素上设置data-image-id属性，确保序列化时保留
      if (imageId) {
        img.setAttribute('data-image-id', imageId.toString());
        dom.setAttribute('data-image-id', imageId.toString());
      }

      if (imageId) {
        // 总是从后端获取图片，确保数据一致性
        void loadImageFromBackend();

        // 从后端加载图片的函数
        async function loadImageFromBackend() {
          // 设置加载状态
          img.src =
            'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJzNC40OCAxMCAxMCAxMCAxMC00LjQ4IDEwLTEwUzE3LjUyIDIgMTIgMnptMSAxNWgtMnYtNkg3di0yaDZ2NnptMC04aC0yVjdoMnYyeiIvPjwvc3ZnPg==';
          img.classList.add('loading');

          try {
            // 使用新的imageManager获取blob URL
            const { imageManager } = await import('src/utils/imageManager');
            const blobUrl = await imageManager.getImageAsBlob(Number(imageId));

            if (blobUrl) {
              img.src = blobUrl;
              img.classList.remove('loading');

              // 注册blob URL到图片ID的映射，用于文档间复制
              try {
                const blobMappingModule = await import('../../utils/blobUrlMapping');
                blobMappingModule.registerBlobUrlMapping(blobUrl, Number(imageId));
                console.log(`[NodeView] 注册blob URL映射: ${blobUrl} -> ${imageId}`);
              } catch (mappingError) {
                console.warn('[NodeView] 注册blob URL映射失败:', mappingError);
              }
            } else {
              throw new Error('无法获取图片数据');
            }
          } catch (error) {
            console.error('加载图片失败:', error);
            img.src =
              'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJzNC40OCAxMCAxMCAxMCAxMC00LjQ4IDEwLTEwUzE3LjUyIDIgMTIgMnptMSAxNWgtMnYtNkg3di0yaDZ2NnptMC04aC0yVjdoMnYyeiIvPjwvc3ZnPg==';
            img.classList.add('error');
            img.classList.remove('loading');
          }
        }

        // 添加删除按钮
        const deleteButton = document.createElement('button');
        deleteButton.className = 'image-delete-button';
        deleteButton.innerHTML = '×';
        deleteButton.title = '删除图片';
        deleteButton.onclick = async (e) => {
          e.preventDefault();
          e.stopPropagation();
          try {
            console.log(
              '开始删除图片，imageId:',
              imageId,
              'documentId:',
              extensionOptions.documentId,
            );

            // 用户主动删除图片：先删除本地图片文件，再从编辑器中删除节点
            const { deleteImage } = useSqlite();
            const documentId = extensionOptions.documentId || -1;

            if (!imageId || isNaN(Number(imageId))) {
              throw new Error('无效的图片ID: ' + imageId);
            }

            await deleteImage(Number(imageId), documentId);
            console.log('用户删除图片，已删除本地文件:', imageId);

            // 从编辑器中删除图片节点
            if (typeof getPos === 'function') {
              const pos = getPos();
              if (typeof pos === 'number') {
                editor.commands.command(({ tr }) => {
                  tr.delete(pos, pos + 1);
                  return true;
                });
              }
            }
          } catch (error) {
            console.error('删除图片失败:', error);

            // 显示用户友好的错误信息
            const errorMessage = error instanceof Error ? error.message : '删除图片失败';

            // 如果是引用错误，显示更详细的信息
            if (errorMessage.includes('仍被以下文档引用')) {
              // 可以在这里添加更友好的UI提示，比如弹窗显示引用的文档列表
              console.warn('图片被其他文档引用，无法删除:', errorMessage);
            }
          }
        };
        dom.appendChild(deleteButton);
      } else if (HTMLAttributes.src) {
        // 处理普通图片URL
        img.src = HTMLAttributes.src;

        // 如果有其他属性也要保留
        if (HTMLAttributes['data-image-id']) {
          img.setAttribute('data-image-id', HTMLAttributes['data-image-id'].toString());
          dom.setAttribute('data-image-id', HTMLAttributes['data-image-id'].toString());
        }
      }

      // 添加图片尺寸控制
      if (HTMLAttributes.width) {
        img.style.width =
          typeof HTMLAttributes.width === 'number'
            ? `${HTMLAttributes.width}px`
            : HTMLAttributes.width;
      }
      if (HTMLAttributes.height) {
        img.style.height =
          typeof HTMLAttributes.height === 'number'
            ? `${HTMLAttributes.height}px`
            : HTMLAttributes.height;
      }

      dom.appendChild(img);

      // 添加全局点击事件监听器来取消图片选中
      const handleGlobalClick = (e: Event) => {
        const target = e.target as HTMLElement;
        // 如果点击的不是图片或图片容器，则取消所有图片的选中状态
        if (!target.closest('.image-container')) {
          const allImages = document.querySelectorAll('.image-preview');
          allImages.forEach((image) => {
            image.classList.remove('selected-image');
          });
        }
      };

      // 添加事件监听器
      document.addEventListener('click', handleGlobalClick);

      return {
        dom,
        destroy() {
          // 清理事件监听器
          document.removeEventListener('click', handleGlobalClick);

          // 原有的销毁逻辑
          // 获取编辑器实例的 key
          const docStore = useDocStore();
          const instanceKey = docStore.findInstanceKeyByEditor(editor as unknown as VueEditor);

          // 检查编辑器是否正在销毁
          const isDestroying = instanceKey ? tiptap().isEditorDestroying(instanceKey) : false;

          // 只有在编辑器正常运行时（非销毁状态）删除节点才删除图片
          // 这里处理的是用户通过键盘删除、撤销等操作删除图片节点的情况
          if (imageId && !isDestroying) {
            console.log(
              '图片节点被删除（非编辑器销毁），检查是否需要删除本地文件，imageId:',
              imageId,
            );
            const content = (editor as VueEditor).getJSON();
            void handleImageNodeDeleted(Number(imageId), content);
          } else if (isDestroying) {
            console.log('编辑器正在销毁，跳过图片删除检查，imageId:', imageId);
          }
        },
      };
    };
  },
});

// 处理图片节点被删除的函数
async function handleImageNodeDeleted(imageId: number, content: JSONContent | null) {
  try {
    // 检查文档内容
    if (content) {
      // 递归检查内容中是否包含该图片ID
      const containsImage = (node: Record<string, unknown>): boolean => {
        if (!node) return false;

        // 检查当前节点是否为图片节点且包含目标图片ID
        const attrs = node.attrs as Record<string, unknown> | undefined;
        if (
          node.type === 'image' &&
          attrs &&
          (attrs['data-image-id'] === String(imageId) || attrs.src === `image://${imageId}`)
        ) {
          return true;
        }

        // 递归检查子节点
        if (node.content && Array.isArray(node.content)) {
          return node.content.some((child: Record<string, unknown>) => containsImage(child));
        }

        return false;
      };

      if (containsImage(content)) {
        console.log('图片在当前文档中存在，跳过删除:', imageId);
        return;
      }
    }

    // 如果文档中不存在该图片，则执行删除
    const { deleteImage } = useSqlite();
    await deleteImage(imageId);
    console.log('图片节点被删除，已从数据库中删除图片:', imageId);
  } catch (error) {
    console.error('删除图片失败:', error);
  }
}
