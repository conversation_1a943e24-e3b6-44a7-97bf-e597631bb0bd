cmake_minimum_required(VERSION 3.25)

# Set policies for Qt6 compatibility
if(POLICY CMP0144)
    cmake_policy(SET CMP0144 NEW)
endif()
if(POLICY CMP0167)
    cmake_policy(SET CMP0167 NEW)
endif()

project(InkCop VERSION 0.0.1 LANGUAGES CXX C)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Development mode option
option(DEV_MODE "Enable development mode (load from localhost:9000)" OFF)

# Console mode option for development builds
option(CONSOLE_MODE "Enable console window for debugging output" OFF)

# Local GGUF embedding support
option(ENABLE_LOCAL_GGUF "Enable local GGUF model support with llama.cpp" OFF)

# 设置输出目录
if(WIN32)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
elseif(APPLE)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/InkCop.app/Contents/MacOS)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/InkCop.app/Contents/Frameworks)
else()
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
endif()

# Configure Qt6 path for Windows MSVC build
if(WIN32)
    # Set Qt6 installation path - adjust this path if Qt is installed elsewhere
    set(Qt6_DIR "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6")
    set(CMAKE_PREFIX_PATH "C:/Qt/6.9.1/msvc2022_64" ${CMAKE_PREFIX_PATH})
    message(STATUS "Using Qt6 from: C:/Qt/6.9.1/msvc2022_64")
    message(STATUS "Qt6_DIR set to: ${Qt6_DIR}")
endif()

# Find Qt6 (Qt6::Sql includes SQLite driver)
# Clear any cached variables that might cause issues
unset(Qt6_DIR CACHE)
unset(Qt6_FOUND CACHE)

# Set Qt6 directory explicitly
set(Qt6_DIR "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6")

# Find Qt6 without version constraints
# Try to find WebEngineWidgets, but make it optional if dependencies are missing
find_package(Qt6 REQUIRED COMPONENTS Core Widgets WebChannel Sql Network)
find_package(Qt6 COMPONENTS WebEngineWidgets QUIET)

if(NOT Qt6WebEngineWidgets_FOUND)
    message(WARNING "Qt6WebEngineWidgets not found - this may be due to missing Qt6Positioning dependency")
    message(WARNING "Please install Qt6Positioning module through Qt MaintenanceTool")
    message(FATAL_ERROR "Qt6WebEngineWidgets is required for this application")
endif()

# Find QWindowKit
find_package(QWindowKit COMPONENTS Core Widgets REQUIRED)

# FlatBuffers integration - ObjectBox dependency
# Use local FlatBuffers headers to avoid network issues
set(FLATBUFFERS_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/third-party/flatbuffers/include")

# Check if local FlatBuffers headers exist
if(EXISTS "${FLATBUFFERS_INCLUDE_DIR}/flatbuffers/flatbuffers.h")
    message(STATUS "Using local FlatBuffers headers from: ${FLATBUFFERS_INCLUDE_DIR}")
    set(flatbuffers_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/third-party/flatbuffers")
else()
    # Fallback: try to download if local headers don't exist
    message(STATUS "Local FlatBuffers not found, trying to download...")
    include(FetchContent)
    FetchContent_Declare(
        flatbuffers
        GIT_REPOSITORY https://github.com/google/flatbuffers.git
        GIT_TAG        v23.5.26
        GIT_SHALLOW    TRUE
    )

    FetchContent_GetProperties(flatbuffers)
    if(NOT flatbuffers_POPULATED)
        message(STATUS "Downloading FlatBuffers headers...")
        FetchContent_Populate(flatbuffers)
        message(STATUS "FlatBuffers headers downloaded successfully")
    endif()
endif()

# ObjectBox集成 - 使用本地Windows预构建版本
set(OBJECTBOX_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/third-party/objectbox-windows")

# 检查本地ObjectBox Windows版本是否存在
if(EXISTS "${OBJECTBOX_ROOT}/include/objectbox.h" AND EXISTS "${OBJECTBOX_ROOT}/lib")
    message(STATUS "Using local ObjectBox Windows binaries from: ${OBJECTBOX_ROOT}")

    # 创建ObjectBox导入目标
    add_library(objectbox SHARED IMPORTED)

    # 设置包含目录 - 包括FlatBuffers头文件
    set_target_properties(objectbox PROPERTIES
        INTERFACE_INCLUDE_DIRECTORIES "${OBJECTBOX_ROOT}/include;${flatbuffers_SOURCE_DIR}/include"
    )

    # Windows - 设置DLL和LIB文件
    set(OBJECTBOX_DLL "${OBJECTBOX_ROOT}/lib/objectbox.dll")
    set(OBJECTBOX_LIB "${OBJECTBOX_ROOT}/lib/objectbox.lib")

    if(EXISTS "${OBJECTBOX_DLL}" AND EXISTS "${OBJECTBOX_LIB}")
        set_target_properties(objectbox PROPERTIES
            IMPORTED_LOCATION "${OBJECTBOX_DLL}"
            IMPORTED_IMPLIB "${OBJECTBOX_LIB}"
        )
        message(STATUS "Found ObjectBox DLL: ${OBJECTBOX_DLL}")
        message(STATUS "Found ObjectBox LIB: ${OBJECTBOX_LIB}")
        message(STATUS "FlatBuffers include path: ${flatbuffers_SOURCE_DIR}/include")
        message(STATUS "ObjectBox successfully configured from local Windows installation")
    else()
        message(FATAL_ERROR "ObjectBox Windows libraries not found at expected locations")
    endif()
else()
    message(FATAL_ERROR "Local ObjectBox Windows installation not found at ${OBJECTBOX_ROOT}")
endif()

# ObjectBox Generator集成 - 稍后在目标定义后配置

# Qt6 MOC 配置
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 设置构建类型（如果没有指定，默认为 Release）
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 根据构建类型设置编译选项
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DQT_DEBUG)
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g")
endif()

# 开发模式支持
option(INKCOP_DEV_MODE "Enable development mode with hot reload" OFF)
option(INKCOP_DEV_SERVER_PORT "Development server port" 9000)

if(INKCOP_DEV_MODE)
    add_definitions(-DINKCOP_DEV_MODE)
    add_definitions(-DINKCOP_DEV_SERVER_PORT=${INKCOP_DEV_SERVER_PORT})
    message(STATUS "Development mode enabled - Hot reload on port ${INKCOP_DEV_SERVER_PORT}")
endif()

# 修复 RPATH 问题，用于 RPM 打包
set(CMAKE_SKIP_BUILD_RPATH FALSE)
set(CMAKE_BUILD_WITH_INSTALL_RPATH FALSE)
set(CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE)

# 设置安装时的 RPATH (仅限Unix系统)
if(NOT WIN32)
    if(CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
        set(CMAKE_INSTALL_PREFIX "/usr" CACHE PATH "Install prefix" FORCE)
    endif()

    # 设置库搜索路径
    set(CMAKE_INSTALL_RPATH "${CMAKE_INSTALL_PREFIX}/lib;${CMAKE_INSTALL_PREFIX}/lib64")

    # 在安装时清理 RPATH
    set(CMAKE_INSTALL_RPATH "")
else()
    # Windows默认安装路径
    if(CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
        set(CMAKE_INSTALL_PREFIX "C:/Program Files/InkCop" CACHE PATH "Install prefix" FORCE)
    endif()
endif()

# 抑制ObjectBox生成代码中的C++17弃用警告
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-deprecated")
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    # Windows MSVC编译器特定设置
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4996")  # 禁用弃用警告
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /utf-8")   # 使用UTF-8编码
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)        # 禁用CRT安全警告
    add_definitions(-DNOMINMAX)                        # 避免Windows.h中的min/max宏冲突
endif()

# 动态生成 QRC 文件 - 分割为大文件优化
set(WEBAPP_DIR ${CMAKE_CURRENT_SOURCE_DIR}/dist/spa)

# 检查前端构建文件是否存在
if(NOT EXISTS ${WEBAPP_DIR})
    message(WARNING "前端构建目录不存在: ${WEBAPP_DIR}")
    message(WARNING "请先运行前端构建: npm run build 或 bun run build")
    # 创建一个空的QRC文件以避免构建失败
    set(QRC_FILE ${CMAKE_CURRENT_BINARY_DIR}/webapp_resources.qrc)
    set(QRC_CONTENT "<!DOCTYPE RCC>\n<RCC version=\"1.0\">\n  <qresource prefix=\"/\">\n  </qresource>\n</RCC>\n")
    file(WRITE ${QRC_FILE} "${QRC_CONTENT}")
else()
    # 分割QRC文件以避免内存问题
    file(GLOB_RECURSE WEBAPP_FILES "${WEBAPP_DIR}/*")
    
    # 按文件类型分组
    set(JS_FILES "")
    set(CSS_FILES "")
    set(FONT_FILES "")
    set(ICON_FILES "")
    set(HTML_FILES "")
    set(OTHER_FILES "")
    
    foreach(FILE_PATH ${WEBAPP_FILES})
        string(REPLACE "\\" "/" FILE_PATH_NORMALIZED ${FILE_PATH})
        string(REPLACE "\\" "/" WEBAPP_DIR_NORMALIZED ${WEBAPP_DIR})
        string(REPLACE "${WEBAPP_DIR_NORMALIZED}/" "" ALIAS_PATH ${FILE_PATH_NORMALIZED})
        
        # 分类文件
        if(FILE_PATH MATCHES "\\.js$")
            list(APPEND JS_FILES "    <file alias=\"${ALIAS_PATH}\">${FILE_PATH}</file>")
        elseif(FILE_PATH MATCHES "\\.css$")
            list(APPEND CSS_FILES "    <file alias=\"${ALIAS_PATH}\">${FILE_PATH}</file>")
        elseif(FILE_PATH MATCHES "\\.(woff|woff2|ttf|eot)$")
            list(APPEND FONT_FILES "    <file alias=\"${ALIAS_PATH}\">${FILE_PATH}</file>")
        elseif(FILE_PATH MATCHES "\\.(png|jpg|jpeg|gif|svg|ico)$")
            list(APPEND ICON_FILES "    <file alias=\"${ALIAS_PATH}\">${FILE_PATH}</file>")
        elseif(FILE_PATH MATCHES "\\.html$")
            list(APPEND HTML_FILES "    <file alias=\"${ALIAS_PATH}\">${FILE_PATH}</file>")
        else()
            list(APPEND OTHER_FILES "    <file alias=\"${ALIAS_PATH}\">${FILE_PATH}</file>")
        endif()
    endforeach()
    
    # 创建多个较小的QRC文件
    set(QRC_BASE ${CMAKE_CURRENT_BINARY_DIR}/webapp_resources)
    
    # QRC 1: 主要文件 (HTML, favicon)
    set(QRC_CONTENT_1 "<!DOCTYPE RCC>\n<RCC version=\"1.0\">\n  <qresource prefix=\"/\">\n")
    foreach(ITEM ${HTML_FILES})
        set(QRC_CONTENT_1 "${QRC_CONTENT_1}\n${ITEM}")
    endforeach()
    set(QRC_CONTENT_1 "${QRC_CONTENT_1}\n  </qresource>\n</RCC>\n")
    file(WRITE "${QRC_BASE}_main.qrc" "${QRC_CONTENT_1}")
    
    # QRC 2: JavaScript文件
    set(QRC_CONTENT_2 "<!DOCTYPE RCC>\n<RCC version=\"1.0\">\n  <qresource prefix=\"/\">\n")
    foreach(ITEM ${JS_FILES})
        set(QRC_CONTENT_2 "${QRC_CONTENT_2}\n${ITEM}")
    endforeach()
    set(QRC_CONTENT_2 "${QRC_CONTENT_2}\n  </qresource>\n</RCC>\n")
    file(WRITE "${QRC_BASE}_js.qrc" "${QRC_CONTENT_2}")
    
    # QRC 3: CSS文件
    set(QRC_CONTENT_3 "<!DOCTYPE RCC>\n<RCC version=\"1.0\">\n  <qresource prefix=\"/\">\n")
    foreach(ITEM ${CSS_FILES})
        set(QRC_CONTENT_3 "${QRC_CONTENT_3}\n${ITEM}")
    endforeach()
    set(QRC_CONTENT_3 "${QRC_CONTENT_3}\n  </qresource>\n</RCC>\n")
    file(WRITE "${QRC_BASE}_css.qrc" "${QRC_CONTENT_3}")
    
    # QRC 4: 字体文件
    set(QRC_CONTENT_4 "<!DOCTYPE RCC>\n<RCC version=\"1.0\">\n  <qresource prefix=\"/\">\n")
    foreach(ITEM ${FONT_FILES})
        set(QRC_CONTENT_4 "${QRC_CONTENT_4}\n${ITEM}")
    endforeach()
    set(QRC_CONTENT_4 "${QRC_CONTENT_4}\n  </qresource>\n</RCC>\n")
    file(WRITE "${QRC_BASE}_fonts.qrc" "${QRC_CONTENT_4}")
    
    # QRC 5: 图标和图片
    set(QRC_CONTENT_5 "<!DOCTYPE RCC>\n<RCC version=\"1.0\">\n  <qresource prefix=\"/\">\n")
    foreach(ITEM ${ICON_FILES})
        set(QRC_CONTENT_5 "${QRC_CONTENT_5}\n${ITEM}")
    endforeach()
    set(QRC_CONTENT_5 "${QRC_CONTENT_5}\n  </qresource>\n</RCC>\n")
    file(WRITE "${QRC_BASE}_icons.qrc" "${QRC_CONTENT_5}")
    
    # QRC 6: 其他文件
    set(QRC_CONTENT_6 "<!DOCTYPE RCC>\n<RCC version=\"1.0\">\n  <qresource prefix=\"/\">\n")
    foreach(ITEM ${OTHER_FILES})
        set(QRC_CONTENT_6 "${QRC_CONTENT_6}\n${ITEM}")
    endforeach()
    set(QRC_CONTENT_6 "${QRC_CONTENT_6}\n  </qresource>\n</RCC>\n")
    file(WRITE "${QRC_BASE}_other.qrc" "${QRC_CONTENT_6}")
    
    message(STATUS "生成多个QRC文件以避免内存问题")
    message(STATUS "主要文件: ${QRC_BASE}_main.qrc")
    message(STATUS "JavaScript: ${QRC_BASE}_js.qrc")
    message(STATUS "CSS: ${QRC_BASE}_css.qrc")
    message(STATUS "字体: ${QRC_BASE}_fonts.qrc")
    message(STATUS "图标: ${QRC_BASE}_icons.qrc")
    message(STATUS "其他: ${QRC_BASE}_other.qrc")
endif()

# 源文件
set(SOURCES
    qt-src/main.cpp
    qt-src/mainwindow.cpp

    qt-src/qwkcustomtitlebar.cpp
    qt-src/windowapi.cpp
    qt-src/databaseapi.cpp
    qt-src/knowledgeapi.cpp
    qt-src/localggufembedding.cpp
    qt-src/settings.cpp
    qt-src/customwebengineview.cpp
    # ObjectBox生成的源文件
    qt-src/objectbox/knowledge.obx.cpp
)

set(HEADERS
    qt-src/mainwindow.h

    qt-src/qwkcustomtitlebar.h
    qt-src/windowapi.h
    qt-src/databaseapi.h
    qt-src/knowledgeapi.h
    qt-src/localggufembedding.h
    qt-src/settings.h
    qt-src/customwebengineview.h
    # ObjectBox生成的头文件
    qt-src/objectbox/knowledge.obx.hpp
    qt-src/objectbox/knowledge_generated.h
    qt-src/objectbox/objectbox-model.h
)

# 添加QRC资源文件 - 使用分割的QRC文件
set(RESOURCES
    ${QRC_BASE}_main.qrc
    ${QRC_BASE}_js.qrc
    ${QRC_BASE}_css.qrc
    ${QRC_BASE}_fonts.qrc
    ${QRC_BASE}_icons.qrc
    ${QRC_BASE}_other.qrc
)

# Windows图标资源
if(WIN32)
    set(ICON_RESOURCE qt-src/app.rc)
    enable_language(RC)
endif()

# 可执行文件
if(WIN32)
    if(CONSOLE_MODE)
        # Windows控制台应用程序 - 显示控制台窗口用于调试
        add_executable(InkCop ${SOURCES} ${HEADERS} ${RESOURCES} ${ICON_RESOURCE})
        message(STATUS "Windows console mode enabled - terminal output will be visible")
    else()
        # Windows GUI应用程序 - 不显示控制台窗口
        add_executable(InkCop WIN32 ${SOURCES} ${HEADERS} ${RESOURCES} ${ICON_RESOURCE})
        message(STATUS "Windows GUI mode - no console window")
    endif()
else()
    # 其他平台使用标准可执行文件
    add_executable(InkCop ${SOURCES} ${HEADERS} ${RESOURCES})
endif()

# 检查是否使用静态链接
option(USE_STATIC_OBJECTBOX "Use static linking for ObjectBox" OFF)

if(USE_STATIC_OBJECTBOX)
    # 查找静态库
    find_library(OBJECTBOX_STATIC_LIB
        NAMES libobjectbox.a objectbox.a
        PATHS ${objectbox_SOURCE_DIR}/lib
        NO_DEFAULT_PATH
    )

    if(OBJECTBOX_STATIC_LIB)
        message(STATUS "使用 ObjectBox 静态库: ${OBJECTBOX_STATIC_LIB}")
        target_link_libraries(InkCop
            Qt6::Core
            Qt6::Widgets
            Qt6::WebEngineWidgets
            Qt6::WebChannel
            Qt6::Sql
            Qt6::Network
            ${OBJECTBOX_STATIC_LIB}
        )
    else()
        message(WARNING "未找到 ObjectBox 静态库，使用动态链接")
        target_link_libraries(InkCop
            Qt6::Core
            Qt6::Widgets
            Qt6::WebEngineWidgets
            Qt6::WebChannel
            Qt6::Sql
            Qt6::Network
            objectbox
        )
    endif()
else()
    # 使用动态链接（默认）
    target_link_libraries(InkCop
        Qt6::Core
        Qt6::Widgets
        Qt6::WebEngineWidgets
        Qt6::WebChannel
        Qt6::Sql
        Qt6::Network
        objectbox
        QWindowKit::Widgets
    )
endif()

# Development mode compile definitions
if(DEV_MODE)
    target_compile_definitions(InkCop PRIVATE DEV_MODE=1)
    message(STATUS "Development mode enabled - will load frontend from localhost:9000")
else()
    message(STATUS "Production mode - will use embedded frontend resources")
endif()

# Local GGUF support configuration
if(ENABLE_LOCAL_GGUF)
    message(STATUS "Local GGUF support enabled - looking for llama.cpp")
    
    # Find llama.cpp library
    find_library(LLAMA_CPP_LIB
        NAMES llama llama.dll libllama.a
        PATHS
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/bin/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/bin/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/src
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build
            /usr/local/lib
            /usr/lib
        NO_DEFAULT_PATH
    )
    
    # Find common library
    find_library(LLAMA_COMMON_LIB
        NAMES common
        PATHS
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/common/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/common
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/common/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/common
            /usr/local/lib
            /usr/lib
        NO_DEFAULT_PATH
    )
    
    # Find ggml library
    find_library(GGML_LIB
        NAMES ggml
        PATHS
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/ggml/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/bin/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/ggml/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/bin/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/ggml/src
            /usr/local/lib
            /usr/lib
        NO_DEFAULT_PATH
    )
    
    # Find ggml-base library
    find_library(GGML_BASE_LIB
        NAMES ggml-base
        PATHS
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/ggml/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/bin/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/ggml/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/bin/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/ggml/src
            /usr/local/lib
            /usr/lib
        NO_DEFAULT_PATH
    )
    
    # Find ggml-cpu library
    find_library(GGML_CPU_LIB
        NAMES ggml-cpu
        PATHS
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/ggml/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/bin/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/ggml/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/bin/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/ggml/src
            /usr/local/lib
            /usr/lib
        NO_DEFAULT_PATH
    )
    
    # Find ggml-cuda library (for CUDA support)
    find_library(GGML_CUDA_LIB
        NAMES ggml-cuda
        PATHS
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/ggml/src/ggml-cuda/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/ggml/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build-cuda/bin/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/ggml/src/ggml-cuda/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/ggml/src/Release
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/build/bin/Release
            /usr/local/lib
            /usr/lib
        NO_DEFAULT_PATH
    )
    
    # Find llama.cpp headers
    find_path(LLAMA_CPP_INCLUDE_DIR
        NAMES llama.h
        PATHS
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/include
            ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp
            /usr/local/include
            /usr/include
        NO_DEFAULT_PATH
    )
    
    if(LLAMA_CPP_LIB AND LLAMA_COMMON_LIB AND GGML_LIB AND GGML_CPU_LIB AND LLAMA_CPP_INCLUDE_DIR)
        message(STATUS "Found llama.cpp library: ${LLAMA_CPP_LIB}")
        message(STATUS "Found llama.cpp common library: ${LLAMA_COMMON_LIB}")
        message(STATUS "Found ggml library: ${GGML_LIB}")
        if(GGML_BASE_LIB)
            message(STATUS "Found ggml-base library: ${GGML_BASE_LIB}")
        endif()
        message(STATUS "Found ggml-cpu library: ${GGML_CPU_LIB}")
        if(GGML_CUDA_LIB)
            message(STATUS "Found ggml-cuda library: ${GGML_CUDA_LIB}")
        endif()
        message(STATUS "Found llama.cpp headers: ${LLAMA_CPP_INCLUDE_DIR}")
        
        # Add compile definition
        target_compile_definitions(InkCop PRIVATE ENABLE_LOCAL_GGUF=1)
        
        # Add include directory
        target_include_directories(InkCop PRIVATE ${LLAMA_CPP_INCLUDE_DIR})
        target_include_directories(InkCop PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/ggml/include)
        target_include_directories(InkCop PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/third-party/llama.cpp/common)
        
        # Link llama.cpp library
        target_link_libraries(InkCop ${LLAMA_CPP_LIB})
        target_link_libraries(InkCop ${LLAMA_COMMON_LIB})
        target_link_libraries(InkCop ${GGML_LIB})
        if(GGML_BASE_LIB)
            target_link_libraries(InkCop ${GGML_BASE_LIB})
        endif()
        target_link_libraries(InkCop ${GGML_CPU_LIB})
        if(GGML_CUDA_LIB)
            target_link_libraries(InkCop ${GGML_CUDA_LIB})
        endif()
        
        # Additional dependencies for CUDA support (if available)
        if(WIN32)
            # Windows-specific CUDA libraries
            find_library(CUDA_RUNTIME_LIB cudart PATHS "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/*/lib/x64")
            if(CUDA_RUNTIME_LIB)
                message(STATUS "Found CUDA runtime: ${CUDA_RUNTIME_LIB}")
                target_link_libraries(InkCop ${CUDA_RUNTIME_LIB})
            endif()
        endif()
        
        message(STATUS "Local GGUF support configured successfully")
    else()
        message(WARNING "llama.cpp library or headers not found")
        message(WARNING "GGUF support will be disabled at compile time")
        message(WARNING "To enable GGUF support:")
        message(WARNING "  1. Clone llama.cpp to third-party/llama.cpp")
        message(WARNING "  2. Build llama.cpp with CUDA support if needed")
        message(WARNING "  3. Re-run cmake with -DENABLE_LOCAL_GGUF=ON")
    endif()
else()
    message(STATUS "Local GGUF support disabled - use -DENABLE_LOCAL_GGUF=ON to enable")
endif()

# ObjectBox Generator集成
# find_package(ObjectBoxGenerator 4.0.0 QUIET)
# if(ObjectBoxGenerator_FOUND)
#     add_obx_schema(
#         TARGET InkCop
#         SCHEMA_FILES qt-src/objectbox/knowledge.fbs
#         INSOURCE
#         CXX_STANDARD 11
#     )
#     message(STATUS "ObjectBox Generator found and configured")
# else()
#     message(WARNING "ObjectBox Generator not found - using manual approach")
#     # 手动包含ObjectBox头文件
#     target_include_directories(InkCop PRIVATE qt-src/objectbox)
# endif()

# 手动包含ObjectBox头文件
target_include_directories(InkCop PRIVATE qt-src/objectbox)

# 包含目录
target_include_directories(InkCop PRIVATE 
    qt-src
)

# 添加编译定义
target_compile_definitions(InkCop PRIVATE 
    SQLITE_THREADSAFE=1
)

# 安装规则
if(WIN32)
    install(TARGETS InkCop
        RUNTIME DESTINATION bin
        LIBRARY DESTINATION bin
    )

    # Windows特定：安装Qt依赖库
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        # 在安装时运行windeployqt
        install(CODE "
            execute_process(
                COMMAND windeployqt \${CMAKE_INSTALL_PREFIX}/bin/InkCop.exe --webengine
                RESULT_VARIABLE DEPLOY_RESULT
            )
            if(NOT DEPLOY_RESULT EQUAL 0)
                message(WARNING \"windeployqt failed, you may need to manually copy Qt libraries\")
            endif()
        ")
    endif()

elseif(APPLE)
    install(TARGETS InkCop
        RUNTIME DESTINATION MacOS
        LIBRARY DESTINATION Frameworks
    )
else()
    install(TARGETS InkCop
        RUNTIME DESTINATION bin
        LIBRARY DESTINATION lib
    )
endif()

# 复制Quasar构建文件到输出目录 (已由QRC资源系统替代)
# add_custom_command(TARGET InkCop POST_BUILD
#     COMMAND ${CMAKE_COMMAND} -E copy_directory
#     ${CMAKE_SOURCE_DIR}/dist/spa ${CMAKE_BINARY_DIR}/bin/webapp
#     COMMENT "Copying Quasar app files"
# )