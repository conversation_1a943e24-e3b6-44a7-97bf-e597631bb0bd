<template>
  <div class="q-pa-xl">
    <div class="row items-center q-mb-lg">
      <div class="column">
        <div class="text-h6">{{ $t('src.components.settings.KnowledgeBaseSettings.title') }}</div>
        <div class="text-caption text-grey-6">
          {{ $t('src.components.settings.KnowledgeBaseSettings.description') }}
        </div>
      </div>
    </div>

    <q-separator class="q-mb-md" />

    <!-- 嵌入模式选择 -->
    <div class="text-subtitle1 q-mb-md">
      {{ $t('src.components.settings.KnowledgeBaseSettings.embeddingMode') }}
    </div>
    <q-option-group
      :model-value="settings.embeddingMode"
      :options="embeddingModeOptions"
      color="primary"
      inline
      class="q-mb-md"
      @update:model-value="
        (value) => updateSettings({ embeddingMode: value as 'cloud' | 'local' | 'auto' })
      "
    >
      <template v-slot:label="opt">
        <div class="row items-center">
          <q-icon :name="opt.icon" size="sm" class="q-mr-sm" />
          <span>{{ opt.label }}</span>
        </div>
      </template>
    </q-option-group>
    <div class="text-caption text-grey-6 q-mb-md">
      {{ $t('src.components.settings.KnowledgeBaseSettings.selectProvider') }}
    </div>
    <div v-if="settings.embeddingMode === 'local'" class="text-caption text-grey-6 q-mb-md">
      {{ $t('src.components.settings.KnowledgeBaseSettings.localWarning') }}
    </div>

    <q-separator class="q-mb-md" />

    <!-- 云端API配置 -->
    <div v-show="settings.embeddingMode !== 'local'" class="cloud-api-section">
      <div class="text-subtitle1 q-mb-md">
        {{ $t('src.components.settings.KnowledgeBaseSettings.cloudApi') }}
      </div>

      <!-- 供应商选择 -->
      <q-select
        v-model="selectedProvider"
        :options="availableProviders"
        :label="$t('src.components.settings.KnowledgeBaseSettings.selectProvider')"
        outlined
        emit-value
        map-options
        class="q-mb-md"
        :rules="[(val) => !!val || $t('src.components.settings.common.selectRequired')]"
        :hint="$t('src.components.settings.KnowledgeBaseSettings.selectProvider')"
        @update:model-value="onProviderChange"
      >
        <template v-slot:prepend>
          <q-icon name="business" />
        </template>
      </q-select>

      <!-- 模型选择 -->
      <q-select
        v-model="selectedModel"
        :options="availableModels"
        :label="$t('src.components.settings.KnowledgeBaseSettings.selectEmbeddingModel')"
        outlined
        emit-value
        map-options
        class="q-mb-md"
        :disable="!selectedProvider"
        :rules="[(val) => !!val || $t('src.components.settings.common.selectRequired')]"
        :hint="$t('src.components.settings.KnowledgeBaseSettings.selectEmbeddingModel')"
        @update:model-value="onModelChange"
      >
        <template v-slot:prepend>
          <q-icon name="psychology" />
        </template>
      </q-select>

      <div v-if="selectedProvider && selectedModel" class="text-caption text-grey-6 q-mb-md">
        {{ $t('src.components.settings.KnowledgeBaseSettings.selectedProvider') }}:
        {{ selectedProvider }} / {{ selectedModel }}
      </div>
    </div>

    <!-- 向量维度显示 - 适用于所有模式 -->
    <q-input
      v-model="embeddingDimensionDisplay"
      :label="$t('src.components.settings.KnowledgeBaseSettings.embeddingDimension')"
      outlined
      readonly
      class="q-mb-md"
      :hint="$t('src.components.settings.KnowledgeBaseSettings.embeddingDimensionHint')"
      :loading="detectingDimension"
    >
      <template v-slot:prepend>
        <q-icon name="straighten" />
      </template>
      <template v-if="canDetectDimension && !detectingDimension" v-slot:append>
        <q-btn
          flat
          round
          dense
          icon="refresh"
          :tooltip="$t('src.components.settings.KnowledgeBaseSettings.refreshDimension')"
          @click="triggerDimensionDetection"
        />
      </template>
    </q-input>

    <!-- 本地GGUF模型配置 -->
    <div v-show="settings.embeddingMode !== 'cloud'" class="local-gguf-section">
      <div class="text-subtitle1 q-mb-md">
        {{ $t('src.components.settings.KnowledgeBaseSettings.localGGUFConfig') }}
      </div>

      <q-input
        v-model="settings.localModelPath"
        :label="$t('src.components.settings.KnowledgeBaseSettings.modelFilePath')"
        outlined
        type="text"
        class="q-mb-md"
        :rules="[
          (val) =>
            !val ||
            val.endsWith('.gguf') ||
            $t('src.components.settings.KnowledgeBaseSettings.selectValidGGUF'),
        ]"
        :hint="$t('src.components.settings.KnowledgeBaseSettings.modelFilePathHint')"
        readonly
        @click="selectLocalModel"
        @update:model-value="updateSettings({ localModelPath: String($event) })"
      >
        <template v-slot:prepend>
          <q-icon name="mdi-file-outline" />
        </template>
        <template v-slot:append>
          <q-btn
            flat
            dense
            icon="folder_open"
            @click="selectLocalModel"
            :title="$t('src.components.settings.KnowledgeBaseSettings.selectValidGGUF')"
          />
        </template>
      </q-input>

      <div class="row q-gutter-md q-mb-md">
        <div class="col">
          <q-input
            v-model.number="settings.localGpuLayers"
            :label="$t('src.components.settings.KnowledgeBaseSettings.gpuLayers')"
            outlined
            type="number"
            :rules="[
              (val) =>
                val >= 0 || $t('src.components.settings.KnowledgeBaseSettings.gpuLayersNegative'),
            ]"
            :hint="$t('src.components.settings.KnowledgeBaseSettings.gpuLayersHint')"
            @update:model-value="updateSettings({ localGpuLayers: Number($event) })"
          >
            <template v-slot:prepend>
              <q-icon name="mdi-layers" />
            </template>
          </q-input>
        </div>
        <div class="col">
          <q-input
            v-model.number="settings.localContextSize"
            :label="$t('src.components.settings.KnowledgeBaseSettings.contextSize')"
            outlined
            type="number"
            :rules="[
              (val) =>
                val > 0 || $t('src.components.settings.KnowledgeBaseSettings.contextSizePositive'),
            ]"
            :hint="$t('src.components.settings.KnowledgeBaseSettings.contextSizeHint')"
            @update:model-value="updateSettings({ localContextSize: Number($event) })"
          >
            <template v-slot:prepend>
              <q-icon name="mdi-format-text" />
            </template>
          </q-input>
        </div>
      </div>

      <q-toggle
        v-model="settings.localUseGpu"
        color="positive"
        :label="$t('src.components.settings.KnowledgeBaseSettings.enableGpuAcceleration')"
        class="q-mb-md"
        @update:model-value="updateSettings({ localUseGpu: Boolean($event) })"
      />
      <div class="text-caption text-grey-6 q-mb-md">
        {{ $t('src.components.settings.KnowledgeBaseSettings.enableGpuAccelerationHint') }}
      </div>

      <!-- 模型状态指示器 -->
      <div class="q-mb-md">
        <q-chip
          v-if="isLocalModelLoaded"
          color="positive"
          text-color="white"
          icon="mdi-check-circle"
        >
          {{ $t('src.components.settings.KnowledgeBaseSettings.localModelLoaded') }}
        </q-chip>
        <q-chip v-else-if="loadingModel" color="orange" text-color="white" icon="mdi-loading">
          <q-spinner-gears size="1em" class="q-mr-sm" />
          {{ loadingProgress || $t('src.components.settings.KnowledgeBaseSettings.loadingModel') }}
        </q-chip>
        <q-chip v-else color="grey-6" text-color="white" icon="mdi-circle-outline">
          {{ $t('src.components.settings.KnowledgeBaseSettings.localModelNotLoaded') }}
        </q-chip>

        <!-- GPU设备状态显示 -->
        <q-chip
          v-if="gpuDetectionComplete"
          :color="settings.selectedGpuDevice === -1 ? 'grey-7' : 'blue-6'"
          text-color="white"
          :icon="settings.selectedGpuDevice === -1 ? 'computer' : 'memory'"
          class="q-ml-sm"
        >
          {{ gpuDeviceStatusText }}
        </q-chip>
      </div>

      <!-- GPU能力检测 -->
      <div class="row q-gutter-md q-mb-md">
        <q-btn color="secondary" outline :loading="detectingGpu" @click="detectGpuCapabilities">
          <q-icon name="graphic_eq" class="q-mr-sm" />
          {{ $t('src.components.settings.KnowledgeBaseSettings.detectGpuCapabilities') }}
        </q-btn>
        <q-btn
          v-if="!isLocalModelLoaded"
          color="primary"
          outline
          :loading="loadingModel"
          :disable="!settings.localModelPath"
          @click="loadLocalModel"
        >
          <q-spinner-gears
            v-if="loadingModel"
            color="primary"
            size="1.2em"
            :thickness="5"
            class="q-mr-sm"
          />
          <q-icon v-else name="mdi-play" class="q-mr-sm" />
          {{ $t('src.components.settings.KnowledgeBaseSettings.loadModel') }}
        </q-btn>

        <q-btn
          v-if="isLocalModelLoaded"
          color="negative"
          outline
          :loading="unloadingModel"
          @click="unloadLocalModel"
        >
          <q-spinner-gears
            v-if="unloadingModel"
            color="negative"
            size="1.2em"
            :thickness="5"
            class="q-mr-sm"
          />
          <q-icon v-else name="mdi-stop" class="q-mr-sm" />
          {{ $t('src.components.settings.KnowledgeBaseSettings.unloadModel') }}
        </q-btn>
        <q-btn
          v-if="isLocalModelLoaded && settings.embeddingMode !== 'cloud'"
          color="info"
          outline
          :loading="testingLocalGGUF"
          :disable="!isLocalModelLoaded"
          @click="testLocalGGUF"
        >
          <q-icon name="mdi-rocket" class="q-mr-sm" />
          {{ $t('src.components.settings.KnowledgeBaseSettings.testLocalModel') }}
        </q-btn>
      </div>

      <!-- GPU设备选择 -->
      <div v-if="gpuDetectionComplete && availableGpuDevices.length > 0" class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">
          {{ $t('src.components.settings.KnowledgeBaseSettings.gpuDeviceSelection') }}
        </div>
        <q-option-group
          :model-value="settings.selectedGpuDevice"
          :options="
            availableGpuDevices.map((device) => ({
              value: device.deviceId,
              label: `${device.deviceName} (${device.backend})`,
              disable: !device.available,
            }))
          "
          color="primary"
          class="q-mb-md"
          @update:model-value="(value: number) => updateSettings({ selectedGpuDevice: value })"
        >
          <template v-slot:label="opt">
            <div class="row items-center">
              <q-icon
                :name="opt.value === -1 ? 'computer' : 'memory'"
                size="sm"
                class="q-mr-sm"
                :color="opt.value === settings.selectedGpuDevice ? 'primary' : 'grey-6'"
              />
              <div class="column">
                <span>{{ opt.label }}</span>
                <span v-if="opt.value !== -1" class="text-caption text-grey-6">
                  {{ $t('src.components.settings.KnowledgeBaseSettings.maxLayers') }}:
                  {{ availableGpuDevices.find((d) => d.deviceId === opt.value)?.maxLayers || 0 }}，
                  {{ $t('src.components.settings.KnowledgeBaseSettings.recommendedLayers') }}:
                  {{
                    availableGpuDevices.find((d) => d.deviceId === opt.value)?.recommendedLayers ||
                    0
                  }}
                </span>
                <span v-else class="text-caption text-grey-6">
                  {{ $t('src.components.settings.KnowledgeBaseSettings.cpuMode') }}
                </span>
              </div>
            </div>
          </template>
        </q-option-group>
        <div class="text-caption text-grey-6 q-mb-md">
          {{ $t('src.components.settings.KnowledgeBaseSettings.gpuDeviceInfo') }}
        </div>
      </div>
    </div>

    <!-- 测试结果显示 -->
    <div v-if="testResult && settings.embeddingMode !== 'cloud'" class="q-mt-md">
      <q-banner
        :class="testResult.success ? 'bg-positive' : 'bg-negative'"
        text-color="white"
        rounded
      >
        <template v-slot:avatar>
          <q-icon :name="testResult.success ? 'check_circle' : 'error'" />
        </template>
        <div class="text-body1">{{ testResult.message }}</div>
        <template v-if="testResult.success" v-slot:action>
          <q-btn flat color="white" label="关闭" @click="testResult = null" size="sm" />
        </template>
      </q-banner>
    </div>

    <q-separator class="q-mb-md" />

    <!-- 向量化参数配置 -->
    <div class="text-subtitle1 q-mb-md">
      {{ $t('src.components.settings.KnowledgeBaseSettings.vectorizationParams') }}
    </div>

    <q-input
      v-model.number="settings.chunkSize"
      :label="$t('src.components.settings.KnowledgeBaseSettings.chunkSize')"
      outlined
      type="number"
      class="q-mb-md"
      :rules="[
        (val) => val > 0 || $t('src.components.settings.KnowledgeBaseSettings.chunkSizePositive'),
      ]"
      :hint="$t('src.components.settings.KnowledgeBaseSettings.chunkSizeHint')"
      @update:model-value="updateSettings({ chunkSize: Number($event) })"
    >
      <template v-slot:prepend>
        <q-icon name="mdi-file-document-outline" />
      </template>
    </q-input>

    <q-input
      v-model.number="settings.chunkOverlap"
      :label="$t('src.components.settings.KnowledgeBaseSettings.chunkOverlap')"
      outlined
      type="number"
      class="q-mb-md"
      :rules="[
        (val) =>
          val >= 0 || $t('src.components.settings.KnowledgeBaseSettings.chunkOverlapNonNegative'),
      ]"
      :hint="$t('src.components.settings.KnowledgeBaseSettings.chunkOverlapHint')"
      @update:model-value="updateSettings({ chunkOverlap: Number($event) })"
    >
      <template v-slot:prepend>
        <q-icon name="mdi-file-replace-outline" />
      </template>
    </q-input>

    <!-- 语义相似度阈值 -->
    <q-item class="q-pa-none q-mb-md">
      <q-item-section avatar>
        <q-icon name="mdi-target" />
      </q-item-section>
      <q-item-section>
        <q-slider
          v-model="settings.semanticThreshold"
          :min="0"
          :max="1"
          :step="0.1"
          label
          :label-always="true"
          :label-value="
            $t('src.components.settings.KnowledgeBaseSettings.semanticThreshold') +
            ': ' +
            settings.semanticThreshold
          "
          track-size="2px"
          color="grey"
          label-color="primary"
          thumb-color="primary"
          @update:model-value="updateSettings({ semanticThreshold: Number($event) })"
        />
        <span class="text-grey">{{
          $t('src.components.settings.KnowledgeBaseSettings.semanticThresholdHint')
        }}</span>
      </q-item-section>
    </q-item>

    <q-input
      v-model.number="settings.searchLimit"
      :label="$t('src.components.settings.KnowledgeBaseSettings.searchLimit')"
      outlined
      type="number"
      class="q-mb-md"
      :rules="[
        (val) => val > 0 || $t('src.components.settings.KnowledgeBaseSettings.searchLimitPositive'),
      ]"
      :hint="$t('src.components.settings.KnowledgeBaseSettings.searchLimitHint')"
      @update:model-value="updateSettings({ searchLimit: Number($event) })"
    >
      <template v-slot:prepend>
        <q-icon name="mdi-magnify" />
      </template>
    </q-input>

    <q-separator class="q-mb-md" />

    <!-- 默认切割策略配置 -->
    <div class="text-subtitle1 q-mb-md">
      {{ $t('src.components.settings.KnowledgeBaseSettings.chunkStrategy') }}
    </div>

    <q-select
      v-model="settings.defaultChunkStrategy"
      :options="chunkStrategyOptions"
      :label="$t('src.components.settings.KnowledgeBaseSettings.chunkStrategy')"
      outlined
      emit-value
      map-options
      class="q-mb-md"
      :hint="$t('src.components.settings.KnowledgeBaseSettings.chunkStrategyHint')"
      @update:model-value="updateSettings({ defaultChunkStrategy: String($event) })"
    >
      <template v-slot:prepend>
        <q-icon name="view_module" />
      </template>
    </q-select>

    <div class="text-caption text-grey-6 q-mb-md">
      {{ $t('src.components.settings.KnowledgeBaseSettings.chunkStrategyDescription') }}
    </div>

    <q-separator class="q-mb-md" />

    <!-- GPU诊断工具 -->
    <div v-if="isDev && settings.embeddingMode === 'local'" class="q-mb-md">
      <div class="text-subtitle1 q-mb-md">
        <q-icon name="mdi-gpu" class="q-mr-sm" />
        GPU 诊断工具
      </div>
      <GpuDiagnostics />
    </div>

    <q-separator class="q-mb-md" />

    <!-- 连接测试 -->
    <div class="text-subtitle1 q-mb-md">
      {{ $t('src.components.settings.KnowledgeBaseSettings.testApiConnection') }}
    </div>
    <div class="text-caption text-grey-6 q-mb-md">
      {{ $t('src.components.settings.KnowledgeBaseSettings.testVectorization') }}
    </div>

    <div class="row q-gutter-md">
      <q-btn
        color="primary"
        :loading="testingConnection"
        :disable="
          !settings.apiKey ||
          settings.apiKey.trim() === '' ||
          !settings.baseUrl ||
          settings.baseUrl.trim() === ''
        "
        @click="testConnection"
      >
        <q-icon name="wifi" class="q-mr-sm" />
        {{ $t('src.components.settings.KnowledgeBaseSettings.testApiConnection') }}
      </q-btn>

      <q-btn
        color="secondary"
        outline
        :loading="testingEmbedding"
        :disable="
          !settings.apiKey ||
          settings.apiKey.trim() === '' ||
          !settings.baseUrl ||
          settings.baseUrl.trim() === '' ||
          !settings.model ||
          settings.model.trim() === ''
        "
        @click="testEmbedding"
      >
        <q-icon name="mdi-test-tube" class="q-mr-sm" />
        {{ $t('src.components.settings.KnowledgeBaseSettings.testVectorization') }}
      </q-btn>

      <q-btn
        v-if="isDev"
        color="purple"
        outline
        icon="bug_report"
        :label="$t('src.components.settings.KnowledgeBaseSettings.debugTool')"
        @click="openDebugTool"
      />

      <q-btn
        v-if="gpuDetectionComplete"
        color="deep-purple"
        outline
        icon="science"
        :label="$t('src.components.settings.KnowledgeBaseSettings.testGpuWorkflow')"
        @click="testGpuSelectionWorkflow"
        class="q-ml-md"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onUnmounted, onMounted, watch } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { useRouter } from 'vue-router';
import type { KnowledgeBaseSettings, GpuDeviceInfo } from 'src/types/qwen';
import type { KnowledgeApi } from 'src/env';
import {
  EMBEDDING_MODEL_TYPES,
  hasModelsOfType,
  getModelsByType,
  type CategorizedModels,
} from 'src/types/modelCategories';
import { strategies } from 'src/utils/knowledgeBase';
import { useI18n } from 'vue-i18n';
import GpuDiagnostics from './GpuDiagnostics.vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();
const { t: $t } = useI18n({ useScope: 'global' });
const store = useUiStore();
const router = useRouter();
const testingConnection = ref(false);
const testingEmbedding = ref(false);
const testingLocalGGUF = ref(false);
const detectingGpu = ref(false);
const loadingModel = ref(false);
const unloadingModel = ref(false);
const loadingProgress = ref('');
const isDev = computed(() => process.env.NODE_ENV === 'development');

// GPU相关状态
const availableGpuDevices = ref<GpuDeviceInfo[]>([]);
const gpuDetectionComplete = ref(false);

// 响应式的模型加载状态 - 直接使用ref来确保UI及时更新
const isLocalModelLoaded = ref(false);

// 检查并更新模型状态 - 优化版本，减少不必要的调用
const updateModelStatus = async () => {
  // 如果正在加载模型，跳过状态检查
  if (loadingModel.value || unloadingModel.value) {
    return;
  }

  try {
    const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;
    if (!knowledgeApi?.isLocalGGUFLoaded) {
      return;
    }

    // Qt WebChannel 可能将调用包装成 Promise，所以我们总是用 await 处理
    const currentStatus = await Promise.resolve(knowledgeApi.isLocalGGUFLoaded());

    if (isLocalModelLoaded.value !== currentStatus) {
      isLocalModelLoaded.value = currentStatus;
      console.log(
        '🔄 [KnowledgeBaseSettings] 模型状态已更新:',
        currentStatus ? '已加载' : '未加载',
      );
    }
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] 检查模型状态失败:', error);
  }
};

// 初始化时检查一次状态
void updateModelStatus();

// 减少轮询频率，避免UI阻塞
let statusCheckInterval: NodeJS.Timeout | null = null;

// 启动状态检查
const startStatusCheck = () => {
  if (statusCheckInterval) clearInterval(statusCheckInterval);
  // 改为每5秒检查一次，减少UI阻塞
  statusCheckInterval = setInterval(() => {
    void updateModelStatus();
  }, 5000);
};

// 停止状态检查
const stopStatusCheck = () => {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval);
    statusCheckInterval = null;
  }
};

// 在组件卸载时清理定时器
onUnmounted(() => {
  stopStatusCheck();
});

// 启动状态检查
startStatusCheck();

// 根据模型名称查找对应的嵌入模型供应商
const findEmbeddingModelProvider = (modelName: string): string | null => {
  const llmSettings = store.perferences?.llm;
  if (!llmSettings) return null;

  for (const [providerKey, providerSettings] of Object.entries(llmSettings)) {
    if (
      providerSettings &&
      typeof providerSettings === 'object' &&
      'enabled' in providerSettings &&
      'avaliableModels' in providerSettings &&
      providerSettings.enabled &&
      providerSettings.avaliableModels
    ) {
      const categorizedModels = providerSettings.avaliableModels as CategorizedModels;

      // 检查是否有嵌入模型
      if (hasModelsOfType(categorizedModels, EMBEDDING_MODEL_TYPES)) {
        const embeddingModels = getModelsByType(categorizedModels, EMBEDDING_MODEL_TYPES);
        if (embeddingModels.includes(modelName)) {
          return providerKey;
        }
      }
    }
  }

  return null;
};

// 初始化供应商和模型选择
const initializeProviderAndModel = () => {
  console.log('🔄 [KnowledgeBaseSettings] 初始化供应商和模型选择...');

  const currentModel = settings.value.model;
  console.log('🔍 [KnowledgeBaseSettings] 当前保存的模型:', currentModel);

  if (currentModel && currentModel.trim() !== '') {
    const providerKey = findEmbeddingModelProvider(currentModel);
    console.log('🔍 [KnowledgeBaseSettings] 找到的供应商:', providerKey);

    if (providerKey) {
      // 找到了对应的启用供应商，恢复选择
      selectedProvider.value = providerKey;
      selectedModel.value = currentModel;
      console.log('✅ [KnowledgeBaseSettings] 已恢复供应商和模型选择:', {
        provider: providerKey,
        model: currentModel,
      });
    } else {
      // 没找到对应的启用供应商，保持为空状态
      selectedProvider.value = '';
      selectedModel.value = '';
      console.log('⚠️ [KnowledgeBaseSettings] 未找到启用的供应商，保持未选择状态');
    }
  } else {
    // 没有保存的模型，保持为空状态
    selectedProvider.value = '';
    selectedModel.value = '';
    console.log('ℹ️ [KnowledgeBaseSettings] 没有保存的模型，保持未选择状态');
  }
};

// 监听LLM设置变化，自动重新初始化选择
watch(
  () => store.perferences?.llm,
  (newLlmSettings, oldLlmSettings) => {
    // 只有在LLM设置真正发生变化时才重新初始化
    if (newLlmSettings !== oldLlmSettings) {
      console.log('🔄 [KnowledgeBaseSettings] 检测到LLM设置变化，重新初始化选择...');
      initializeProviderAndModel();
    }
  },
  { deep: true },
);

// 组件挂载时自动检测GPU和初始化选择
onMounted(() => {
  console.log('🚀 [KnowledgeBaseSettings] 组件已挂载，开始初始化...');

  // 首先初始化供应商和模型选择
  initializeProviderAndModel();

  // 只有在非云端模式时才执行GPU检测
  if (settings.value.embeddingMode !== 'cloud') {
    console.log(
      '🔍 [KnowledgeBaseSettings] 嵌入模式为',
      settings.value.embeddingMode,
      '，开始GPU检测...',
    );
    // 延迟执行GPU检测，确保组件完全渲染
    setTimeout(() => {
      void detectGpuCapabilities();
    }, 500);
  } else {
    console.log('ℹ️ [KnowledgeBaseSettings] 嵌入模式为 cloud，跳过GPU检测');
  }
});

const testResult = ref<{ success: boolean; message: string } | null>(null);

// 维度检测相关状态
const detectingDimension = ref(false);

// 计算属性：显示embedding维度
const embeddingDimensionDisplay = computed(() => {
  const dimension = settings.value?.embeddingDimension;
  if (dimension && dimension > 0) {
    return `${dimension}维`;
  }
  return '未检测';
});

// 计算属性：是否可以检测维度
const canDetectDimension = computed(() => {
  if (!settings.value) return false;

  const mode = settings.value.embeddingMode;

  // 云端模式：需要有完整的API配置
  if (mode === 'cloud') {
    return !!(settings.value.baseUrl && settings.value.apiKey && settings.value.model);
  }

  // 本地模式：需要有模型路径
  if (mode === 'local') {
    return !!(settings.value.localModelPath && settings.value.localModelPath.trim() !== '');
  }

  // 自动模式：云端或本地任一可用即可
  if (mode === 'auto') {
    const hasCloudConfig = !!(
      settings.value.baseUrl &&
      settings.value.apiKey &&
      settings.value.model
    );
    const hasLocalConfig = !!(
      settings.value.localModelPath && settings.value.localModelPath.trim() !== ''
    );
    return hasCloudConfig || hasLocalConfig;
  }

  return false;
});

// 智能触发维度检测
const triggerDimensionDetection = () => {
  if (!settings.value || detectingDimension.value) return;

  const mode = settings.value.embeddingMode;

  console.log('🔄 [KnowledgeBaseSettings] 手动触发维度检测，模式:', mode);

  if (mode === 'cloud') {
    void detectEmbeddingDimension();
  } else if (mode === 'local') {
    void detectLocalModelDimension();
  } else if (mode === 'auto') {
    void detectBestEmbeddingDimension();
  }
};

// 嵌入模式选项 - 使用计算属性以响应语言变化
const embeddingModeOptions = computed(() => [
  {
    value: 'cloud',
    label: $t('src.components.settings.KnowledgeBaseSettings.cloudApi'),
    icon: 'cloud',
  },
  {
    value: 'local',
    label: $t('src.components.settings.KnowledgeBaseSettings.localModel'),
    icon: 'home',
  },
  {
    value: 'auto',
    label: $t('src.components.settings.KnowledgeBaseSettings.autoSelect'),
    icon: 'smart_toy',
  },
]);

// 从 CHUNKING_STRATEGIES 获取分块策略选项
const chunkStrategyOptions = computed(() => {
  return Object.entries(strategies()).map(([key, strategy]) => ({
    label: strategy.displayName,
    value: key,
  }));
});

// 选择的供应商和模型
const selectedProvider = ref('');
const selectedModel = ref('');

// 获取有嵌入模型的供应商
const availableProviders = computed(() => {
  const providers: Array<{ label: string; value: string }> = [];

  const llmSettings = store.perferences?.llm;
  if (!llmSettings) return providers;

  Object.entries(llmSettings).forEach(([providerKey, providerSettings]) => {
    if (
      providerSettings &&
      typeof providerSettings === 'object' &&
      'enabled' in providerSettings &&
      'avaliableModels' in providerSettings &&
      providerSettings.enabled &&
      providerSettings.avaliableModels
    ) {
      const categorizedModels = providerSettings.avaliableModels as CategorizedModels;

      // 检查是否有嵌入模型
      if (hasModelsOfType(categorizedModels, EMBEDDING_MODEL_TYPES)) {
        providers.push({
          label: providerKey,
          value: providerKey,
        });
      }
    }
  });

  return providers;
});

// 获取选中供应商的嵌入模型
const availableModels = computed(() => {
  if (!selectedProvider.value) return [];

  const llmSettings = store.perferences?.llm;
  const providerSettings = llmSettings?.[selectedProvider.value as keyof typeof llmSettings];

  if (
    providerSettings &&
    typeof providerSettings === 'object' &&
    'avaliableModels' in providerSettings &&
    providerSettings.avaliableModels
  ) {
    const categorizedModels = providerSettings.avaliableModels as CategorizedModels;
    const embeddingModels = getModelsByType(categorizedModels, EMBEDDING_MODEL_TYPES);

    return embeddingModels.map((model) => ({
      label: model,
      value: model,
    }));
  }

  return [];
});

// 处理供应商变化
const onProviderChange = (provider: string) => {
  selectedProvider.value = provider;
  selectedModel.value = ''; // 重置模型选择

  // 自动选择第一个可用模型
  const models = availableModels.value;
  if (models.length > 0) {
    selectedModel.value = models[0].value;
    onModelChange(models[0].value);
  }
};

// 处理模型变化
const onModelChange = (model: string) => {
  selectedModel.value = model;

  // 从LLM设置中获取配置信息
  const llmSettings = store.perferences?.llm;
  const providerSettings = llmSettings?.[selectedProvider.value as keyof typeof llmSettings];

  if (
    providerSettings &&
    typeof providerSettings === 'object' &&
    'baseUrl' in providerSettings &&
    'apiKey' in providerSettings
  ) {
    // 更新知识库设置
    updateSettings({
      model: model,
      baseUrl: providerSettings.baseUrl,
      apiKey: providerSettings.apiKey,
    });

    // 模型变化时自动检测维度
    setTimeout(() => {
      void detectEmbeddingDimension();
    }, 500);
  }
};

// 检测embedding模型维度
const detectEmbeddingDimension = async () => {
  if (!settings.value || detectingDimension.value) return;

  const { baseUrl, apiKey, model } = settings.value;
  if (!baseUrl || !apiKey || !model) {
    console.log('🔍 [KnowledgeBaseSettings] 配置不完整，跳过维度检测');
    return;
  }

  detectingDimension.value = true;
  console.log('🔍 [KnowledgeBaseSettings] 开始检测embedding维度...');

  try {
    // 构造请求 URL
    const url = buildEmbeddingApiUrl(baseUrl);

    // 使用简单的测试文本
    const requestBody = {
      model: model,
      input: '维度检测测试文本',
      parameters: {
        text_type: 'document',
      },
    };

    console.log('🔍 [KnowledgeBaseSettings] 发送维度检测请求:', {
      url,
      model,
      hasApiKey: !!apiKey,
    });

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log('🔍 [KnowledgeBaseSettings] 维度检测响应:', responseData);

    if (responseData.data && responseData.data.length > 0) {
      const embedding = responseData.data[0].embedding;
      if (embedding && Array.isArray(embedding)) {
        const dimension = embedding.length;
        console.log('✅ [KnowledgeBaseSettings] 检测到embedding维度:', dimension);

        // 保存维度到设置中
        updateSettings({
          embeddingDimension: dimension,
        });

        testResult.value = {
          success: true,
          message: `维度检测成功！模型 ${model} 输出 ${dimension} 维向量`,
        };
      } else {
        throw new Error('响应中没有找到有效的embedding数据');
      }
    } else {
      throw new Error('响应格式不正确');
    }
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] 维度检测失败:', error);

    let errorMessage = '维度检测失败';
    if (error instanceof Error) {
      errorMessage += ': ' + error.message;
    }

    testResult.value = {
      success: false,
      message: errorMessage,
    };
  } finally {
    detectingDimension.value = false;
  }
};

// 检测本地模型维度
const detectLocalModelDimension = async () => {
  if (!settings.value || detectingDimension.value) return;

  const { localModelPath } = settings.value;
  if (!localModelPath || localModelPath.trim() === '') {
    console.log('🔍 [KnowledgeBaseSettings] 本地模型路径为空，跳过维度检测');
    return;
  }

  // 检查是否在Qt环境中
  const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;
  if (!knowledgeApi) {
    console.log('⚠️ [KnowledgeBaseSettings] knowledgeApi不可用，跳过本地模型维度检测');
    return;
  }

  detectingDimension.value = true;
  console.log('🔍 [KnowledgeBaseSettings] 开始检测本地模型维度...');

  try {
    // 使用现有的本地GGUF测试方法
    const testText = '本地模型维度检测测试文本';
    const resultDataPromise = knowledgeApi.testLocalGGUFEmbedding(testText);
    console.log('🔍 [KnowledgeBaseSettings] 本地模型维度检测返回结果:', resultDataPromise);
    console.log('🔍 [KnowledgeBaseSettings] 返回结果类型:', typeof resultDataPromise);

    let resultData: string;

    // 检查是否是Promise
    if (
      resultDataPromise &&
      typeof (resultDataPromise as unknown as { then?: (...args: unknown[]) => unknown }).then ===
        'function'
    ) {
      console.log('🔍 [KnowledgeBaseSettings] 检测到Promise，等待解析...');
      resultData = await (resultDataPromise as unknown as Promise<string>);
      console.log('🔍 [KnowledgeBaseSettings] Promise解析后的结果:', resultData);
    } else {
      console.log('🔍 [KnowledgeBaseSettings] 直接返回值:', resultDataPromise);
      resultData = resultDataPromise;
    }

    const result = JSON.parse(resultData);
    console.log('🔍 [KnowledgeBaseSettings] 本地模型维度检测结果:', result);

    if (result.success && result.dimension && result.dimension > 0) {
      console.log('✅ [KnowledgeBaseSettings] 检测到本地模型维度:', result.dimension);

      // 保存维度到设置中
      updateSettings({
        embeddingDimension: result.dimension,
      });

      testResult.value = {
        success: true,
        message: `本地模型维度检测成功！维度: ${result.dimension}，推理时间: ${result.inference_time_ms}ms`,
      };
    } else {
      throw new Error(result.error || '本地模型维度检测失败');
    }
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] 本地模型维度检测失败:', error);

    let errorMessage = '本地模型维度检测失败';
    if (error instanceof Error) {
      errorMessage += ': ' + error.message;
    }

    testResult.value = {
      success: false,
      message: errorMessage,
    };
  } finally {
    detectingDimension.value = false;
  }
};

// 智能检测最佳embedding维度（自动模式）
const detectBestEmbeddingDimension = async () => {
  if (!settings.value || detectingDimension.value) return;

  detectingDimension.value = true;
  console.log('🔍 [KnowledgeBaseSettings] 开始智能检测最佳embedding配置...');

  try {
    // 优先尝试云端API
    const { baseUrl, apiKey, model } = settings.value;
    if (baseUrl && apiKey && model) {
      console.log('🌐 [KnowledgeBaseSettings] 尝试云端API维度检测...');
      try {
        await detectEmbeddingDimension();
        return; // 云端检测成功，直接返回
      } catch (cloudError) {
        console.log('⚠️ [KnowledgeBaseSettings] 云端API检测失败，尝试本地模型:', cloudError);
      }
    }

    // 云端失败，尝试本地模型
    const { localModelPath } = settings.value;
    if (localModelPath && localModelPath.trim() !== '') {
      console.log('🏠 [KnowledgeBaseSettings] 尝试本地模型维度检测...');
      try {
        await detectLocalModelDimension();
        return; // 本地检测成功，直接返回
      } catch (localError) {
        console.log('⚠️ [KnowledgeBaseSettings] 本地模型检测失败:', localError);
      }
    }

    // 都失败了，使用默认配置
    console.log('📝 [KnowledgeBaseSettings] 使用默认TF-IDF配置...');
    updateSettings({
      embeddingDimension: 100, // TF-IDF默认维度
    });

    testResult.value = {
      success: true,
      message: '自动模式：云端和本地模型都不可用，使用内置TF-IDF (100维)',
    };
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] 智能维度检测失败:', error);

    testResult.value = {
      success: false,
      message: `智能维度检测失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  } finally {
    detectingDimension.value = false;
  }
};

// 智能构建embedding API URL的工具函数
const buildEmbeddingApiUrl = (baseUrl: string): string => {
  if (!baseUrl) return '';

  // 检测Ollama服务
  if (baseUrl.includes('localhost:11434') || baseUrl.includes('127.0.0.1:11434')) {
    // 错误的聊天完成端点，修正为embeddings端点
    if (baseUrl.includes('/v1/chat/completions')) {
      return baseUrl.replace('/v1/chat/completions', '/v1/embeddings');
    }
    // 其他Ollama原生端点，修正为embeddings端点
    else if (baseUrl.includes('/api/generate') || baseUrl.includes('/api/chat')) {
      const correctedBase = baseUrl.replace(/\/api\/(generate|chat).*$/, '');
      return correctedBase + '/v1/embeddings';
    }
    // OpenAI兼容格式
    else if (baseUrl.endsWith('/v1') || baseUrl.endsWith('/v1/')) {
      const trimmedUrl = baseUrl.trim();
      return (trimmedUrl.endsWith('/') ? trimmedUrl : trimmedUrl + '/') + 'embeddings';
    }
    // Ollama原生API格式
    else if (baseUrl.endsWith('/api') || baseUrl.endsWith('/api/')) {
      const trimmedUrl = baseUrl.trim();
      return (trimmedUrl.endsWith('/') ? trimmedUrl : trimmedUrl + '/') + 'embeddings';
    }
    // 基础URL，默认使用OpenAI兼容格式
    else {
      const trimmedUrl = baseUrl.trim();
      return (trimmedUrl.endsWith('/') ? trimmedUrl.slice(0, -1) : trimmedUrl) + '/v1/embeddings';
    }
  }
  // 非Ollama服务，使用标准格式
  else {
    const trimmedUrl = baseUrl.trim();
    return (trimmedUrl.endsWith('/') ? trimmedUrl.slice(0, -1) : trimmedUrl) + '/embeddings';
  }
};

// 从 store 获取设置
const settings = computed<KnowledgeBaseSettings>(() => {
  const baseSettings = store.perferences?.knowledgeBase || {};
  return {
    // 云端API配置
    baseUrl:
      (baseSettings as KnowledgeBaseSettings).baseUrl ||
      'https://dashscope.aliyuncs.com/compatible-mode/v1',
    apiKey: (baseSettings as KnowledgeBaseSettings).apiKey || '',
    model: (baseSettings as KnowledgeBaseSettings).model || 'text-embedding-v4',

    // 向量化参数
    chunkSize: (baseSettings as KnowledgeBaseSettings).chunkSize || 800,
    chunkOverlap: (baseSettings as KnowledgeBaseSettings).chunkOverlap || 200,
    semanticThreshold: (baseSettings as KnowledgeBaseSettings).semanticThreshold || 0.7,
    searchLimit: (baseSettings as KnowledgeBaseSettings).searchLimit || 10,

    // 默认切割策略
    defaultChunkStrategy:
      (baseSettings as KnowledgeBaseSettings).defaultChunkStrategy || 'recursiveCharacter',

    // 本地GGUF模型设置 - 确保embeddingMode有明确的默认值
    embeddingMode: (baseSettings as KnowledgeBaseSettings).embeddingMode || 'cloud',
    localModelPath: (baseSettings as KnowledgeBaseSettings).localModelPath || '',
    localGpuLayers: (baseSettings as KnowledgeBaseSettings).localGpuLayers || 20,
    localContextSize: (baseSettings as KnowledgeBaseSettings).localContextSize || 2048,
    localUseGpu: (baseSettings as KnowledgeBaseSettings).localUseGpu !== false,
    selectedGpuDevice: (baseSettings as KnowledgeBaseSettings).selectedGpuDevice ?? -1,

    // 向量维度设置
    embeddingDimension: (baseSettings as KnowledgeBaseSettings).embeddingDimension || undefined,
  };
});

// 监听嵌入模式变化，处理GPU检测
watch(
  () => settings.value.embeddingMode,
  (newMode, oldMode) => {
    if (newMode !== oldMode) {
      console.log('🔄 [KnowledgeBaseSettings] 嵌入模式从', oldMode, '切换到', newMode);

      // 如果从云端模式切换到本地或自动模式，且GPU检测尚未完成，则执行GPU检测
      if (
        oldMode === 'cloud' &&
        (newMode === 'local' || newMode === 'auto') &&
        !gpuDetectionComplete.value
      ) {
        console.log('🔍 [KnowledgeBaseSettings] 切换到本地模式，开始GPU检测...');
        setTimeout(() => {
          void detectGpuCapabilities();
        }, 100);
      }

      // 如果切换到云端模式，可以清理一些本地模型相关的状态（可选）
      if (newMode === 'cloud') {
        console.log('ℹ️ [KnowledgeBaseSettings] 切换到云端模式，本地GPU检测不再需要');
      }

      // 嵌入模式变化时，清除之前的维度检测结果
      if (newMode !== oldMode) {
        updateSettings({
          embeddingDimension: undefined,
        });
      }
    }
  },
);

// 监听关键配置变化，自动检测维度（支持所有模式）
watch(
  () => [
    settings.value?.baseUrl,
    settings.value?.apiKey,
    settings.value?.model,
    settings.value?.embeddingMode,
    settings.value?.localModelPath,
  ],
  (
    [newBaseUrl, newApiKey, newModel, newMode, newLocalPath],
    [oldBaseUrl, oldApiKey, oldModel, oldMode, oldLocalPath],
  ) => {
    // 检查是否有关键配置变化
    const hasCloudConfigChange =
      newBaseUrl !== oldBaseUrl || newApiKey !== oldApiKey || newModel !== oldModel;

    const hasLocalConfigChange = newLocalPath !== oldLocalPath;

    const hasModeChange = newMode !== oldMode;

    // 云端模式：检测API配置变化
    if (newMode === 'cloud' && (hasCloudConfigChange || (hasModeChange && newMode === 'cloud'))) {
      if (newBaseUrl && newApiKey && newModel) {
        console.log('🔄 [KnowledgeBaseSettings] 检测到云端配置变化，自动检测维度...');
        setTimeout(() => {
          void detectEmbeddingDimension();
        }, 1000);
      }
    }

    // 本地模式：检测本地模型路径变化
    else if (
      newMode === 'local' &&
      (hasLocalConfigChange || (hasModeChange && newMode === 'local'))
    ) {
      if (newLocalPath && newLocalPath.trim() !== '') {
        console.log('🔄 [KnowledgeBaseSettings] 检测到本地模型配置变化，自动检测维度...');
        setTimeout(() => {
          void detectLocalModelDimension();
        }, 1000);
      }
    }

    // 自动模式：根据可用性选择检测方式
    else if (newMode === 'auto' && hasModeChange) {
      console.log('🔄 [KnowledgeBaseSettings] 切换到自动模式，检测最佳配置...');
      setTimeout(() => {
        void detectBestEmbeddingDimension();
      }, 1000);
    }
  },
  { deep: true },
);

// 更新设置
const updateSettings = (newSettings: Partial<KnowledgeBaseSettings>) => {
  console.log('[KnowledgeBaseSettings] 尝试更新设置:', newSettings);

  const currentSettings = settings.value;
  if (!currentSettings) {
    console.log('[KnowledgeBaseSettings] 当前设置为空，无法更新');
    return;
  }

  // 检查是否真的有变化 - 使用更严格的比较
  let hasChanges = false;
  for (const [key, value] of Object.entries(newSettings)) {
    const currentValue = currentSettings[key as keyof KnowledgeBaseSettings];

    // 对于undefined和其他值的比较，使用严格相等
    if (currentValue !== value) {
      hasChanges = true;
      console.log(
        `[KnowledgeBaseSettings] 设置变化检测: ${key} 从 "${JSON.stringify(currentValue)}" 改为 "${JSON.stringify(value)}"`,
      );
      break;
    }
  }

  if (!hasChanges) {
    console.log('[KnowledgeBaseSettings] 设置无变化，但仍然尝试更新以确保响应式');
    // 即使没有变化，也强制更新一次以确保响应式系统工作
    store.updateKnowledgeBaseSettings(newSettings);
    return;
  }

  console.log('[KnowledgeBaseSettings] 检测到设置变化，更新:', newSettings);
  store.updateKnowledgeBaseSettings(newSettings);
};

// 测试连接
const testConnection = async () => {
  if (!settings.value) return;

  testingConnection.value = true;
  testResult.value = null;

  try {
    const { baseUrl, apiKey } = settings.value;

    if (!apiKey || !baseUrl) {
      throw new Error('API Key 或 Base URL 不能为空');
    }

    // 构造请求 URL - 智能修正Ollama URL
    const url = buildEmbeddingApiUrl(baseUrl);

    console.log('🔍 [KnowledgeBaseSettings] 原始baseUrl:', baseUrl);
    console.log('🔍 [KnowledgeBaseSettings] 修正后URL:', url);

    // 构造请求体 - 使用简单的测试文本
    const requestBody = {
      model: settings.value.model || 'text-embedding-v4',
      input: '这是一个连接测试',
      parameters: {
        text_type: 'document',
      },
    };

    console.log('🔍 [KnowledgeBaseSettings] 测试连接:', {
      url,
      model: requestBody.model,
      hasApiKey: !!apiKey,
    });

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
        'User-Agent': 'InkCop/1.0',
      },
      body: JSON.stringify(requestBody),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('❌ [KnowledgeBaseSettings] API 错误:', responseData);
      throw new Error(
        responseData.error?.message || responseData.message || `HTTP ${response.status}`,
      );
    }

    // 检查返回数据格式
    if (!responseData.data || !Array.isArray(responseData.data)) {
      throw new Error('API 返回格式不正确', responseData);
    }

    const embeddings = responseData.data;
    if (embeddings.length === 0 || !Array.isArray(embeddings[0].embedding)) {
      throw new Error('嵌入向量数据为空');
    }

    const vectorDimension = embeddings[0].embedding.length;
    console.log('✅ [KnowledgeBaseSettings] 连接测试成功:', {
      model: responseData.model,
      vectorDimension,
      tokenUsage: responseData.usage,
    });

    testResult.value = {
      success: true,
      message: `连接测试成功！模型: ${responseData.model}，向量维度: ${vectorDimension}，用量: ${responseData.usage?.total_tokens || 0} tokens`,
    };
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] 连接测试失败:', error);

    let errorMessage = '连接测试失败';
    if (error instanceof Error) {
      errorMessage += ': ' + error.message;
    }

    // 提供更具体的错误提示
    if (errorMessage.includes('401')) {
      errorMessage = '认证失败：请检查 API Key 是否正确';
    } else if (errorMessage.includes('404')) {
      if (
        settings.value.baseUrl.includes('localhost:11434') ||
        settings.value.baseUrl.includes('127.0.0.1:11434')
      ) {
        errorMessage = 'Ollama服务未找到：请确保Ollama正在运行，并检查端口11434是否可访问';
      } else {
        errorMessage = '服务未找到：请检查 Base URL 是否正确';
      }
    } else if (errorMessage.includes('Network')) {
      errorMessage = '网络错误：请检查网络连接和服务器状态';
    } else if (errorMessage.includes('timeout')) {
      errorMessage = '请求超时：服务器响应时间过长';
    }

    testResult.value = {
      success: false,
      message: errorMessage,
    };
  } finally {
    testingConnection.value = false;
  }
};

// 测试向量化
const testEmbedding = async () => {
  if (!settings.value) return;

  testingEmbedding.value = true;
  testResult.value = null;

  try {
    const { baseUrl, apiKey, model } = settings.value;

    if (!apiKey || !baseUrl || !model) {
      throw new Error('API Key、Base URL 或 Model 不能为空');
    }

    // 构造请求 URL - 智能修正Ollama URL
    const url = buildEmbeddingApiUrl(baseUrl);

    console.log('🧪 [KnowledgeBaseSettings] 原始baseUrl:', baseUrl);
    console.log('🧪 [KnowledgeBaseSettings] 修正后URL:', url);

    // 使用更复杂的测试文本来验证向量化效果
    const testTexts = [
      '人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。',
      '机器学习是人工智能的一个子领域，专注于算法和统计模型，使计算机能够从数据中学习。',
      '深度学习是机器学习的一个分支，使用多层神经网络来模拟人脑的工作方式。',
    ];

    // 构造请求体
    const requestBody = {
      model: model,
      input: testTexts,
      parameters: {
        text_type: 'document',
      },
    };

    console.log('🔍 [KnowledgeBaseSettings] 测试向量化:', {
      url,
      model,
      inputCount: testTexts.length,
      hasApiKey: !!apiKey,
    });

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
        'User-Agent': 'InkCop/1.0',
      },
      body: JSON.stringify(requestBody),
    });

    const responseData = await response.json();
    console.log('🔍 [KnowledgeBaseSettings] 测试向量化:', responseData);

    if (!response.ok) {
      console.error('❌ [KnowledgeBaseSettings] API 错误:', responseData);
      throw new Error(
        responseData.error?.message || responseData.message || `HTTP ${response.status}`,
      );
    }

    // 检查返回数据格式
    if (!responseData.data || !Array.isArray(responseData.data)) {
      throw new Error('API 返回格式不正确', responseData);
    }

    const embeddings = responseData.data;
    if (embeddings.length !== testTexts.length) {
      throw new Error(`期望 ${testTexts.length} 个向量，实际返回 ${embeddings.length} 个`);
    }

    // 验证向量格式和维度
    const firstEmbedding = embeddings[0].embedding;
    if (!Array.isArray(firstEmbedding) || firstEmbedding.length === 0) {
      throw new Error('嵌入向量数据格式错误');
    }

    const vectorDimension = firstEmbedding.length;

    // 计算向量之间的相似度以验证语义理解
    const similarity1 = calculateCosineSimilarity(embeddings[0].embedding, embeddings[1].embedding);
    const similarity2 = calculateCosineSimilarity(embeddings[0].embedding, embeddings[2].embedding);

    console.log('✅ [KnowledgeBaseSettings] 向量化测试成功:', {
      model: responseData.model,
      vectorDimension,
      similarity1: similarity1.toFixed(4),
      similarity2: similarity2.toFixed(4),
      tokenUsage: responseData.usage,
    });

    testResult.value = {
      success: true,
      message: `向量化测试成功！模型: ${responseData.model}，向量维度: ${vectorDimension}，相似度: ${similarity1.toFixed(3)}/${similarity2.toFixed(3)}，用量: ${responseData.usage?.total_tokens || 0} tokens`,
    };
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] 向量化测试失败:', error);

    let errorMessage = '向量化测试失败';
    if (error instanceof Error) {
      errorMessage += ': ' + error.message;
    }

    // 提供更具体的错误提示
    if (errorMessage.includes('401')) {
      errorMessage = '认证失败：请检查 API Key 是否正确';
    } else if (errorMessage.includes('404')) {
      if (
        settings.value.baseUrl.includes('localhost:11434') ||
        settings.value.baseUrl.includes('127.0.0.1:11434')
      ) {
        errorMessage = 'Ollama服务未找到：请确保Ollama正在运行，并检查端口11434是否可访问';
      } else {
        errorMessage = '服务未找到：请检查 Base URL 是否正确';
      }
    } else if (errorMessage.includes('model')) {
      errorMessage = '模型错误：请检查模型名称是否正确';
    } else if (errorMessage.includes('quota')) {
      errorMessage = '配额不足：请检查账户余额和配额限制';
    }

    testResult.value = {
      success: false,
      message: errorMessage,
    };
  } finally {
    testingEmbedding.value = false;
  }
};

// 计算当前选择的GPU设备信息
const selectedGpuDeviceInfo = computed(() => {
  if (!gpuDetectionComplete.value) return null;
  return (
    availableGpuDevices.value.find(
      (device) => device.deviceId === settings.value.selectedGpuDevice,
    ) || null
  );
});

// 计算GPU设备状态文本
const gpuDeviceStatusText = computed(() => {
  if (!gpuDetectionComplete.value) {
    return '等待GPU检测...';
  }

  const deviceInfo = selectedGpuDeviceInfo.value;
  if (!deviceInfo) {
    return '未选择GPU设备';
  }

  if (deviceInfo.deviceId === -1) {
    return 'CPU模式 (不使用GPU)';
  }

  return `${deviceInfo.deviceName} - ${deviceInfo.backend}`;
});
const calculateCosineSimilarity = (vecA: number[], vecB: number[]): number => {
  if (vecA.length !== vecB.length) {
    throw new Error('向量维度不匹配');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] * vecA[i];
    normB += vecB[i] * vecB[i];
  }

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
};

// 本地GGUF模型相关函数
const selectLocalModel = () => {
  // 检查是否在Qt环境中
  const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;

  if (knowledgeApi && typeof knowledgeApi.selectFile === 'function') {
    // 在Qt环境中使用Qt的文件选择对话框
    try {
      console.log('🔍 [KnowledgeBaseSettings] 调用Qt文件选择对话框...');
      const filePathPromise = knowledgeApi.selectFile(
        'gguf', // 文件扩展名过滤器
        '选择GGUF模型文件', // 对话框标题
        'GGUF模型文件 (*.gguf)', // 文件类型描述
      );

      console.log('🔍 [KnowledgeBaseSettings] Qt文件选择返回Promise:', filePathPromise);
      console.log('🔍 [KnowledgeBaseSettings] 返回值类型:', typeof filePathPromise);

      // 检查是否有then方法
      if (
        filePathPromise &&
        typeof (filePathPromise as unknown as Promise<string>).then === 'function'
      ) {
        // 使用.then()处理Promise
        (filePathPromise as unknown as Promise<string>)
          .then((filePath: string) => {
            console.log('🔍 [KnowledgeBaseSettings] Promise解析后的文件路径:', filePath);
            console.log('🔍 [KnowledgeBaseSettings] 文件路径类型:', typeof filePath);
            console.log(
              '🔍 [KnowledgeBaseSettings] 文件路径长度:',
              filePath ? filePath.length : 'null/undefined',
            );

            if (filePath && typeof filePath === 'string' && filePath.trim() !== '') {
              updateSettings({ localModelPath: filePath });
              console.log('✅ [KnowledgeBaseSettings] 选择的模型文件:', filePath);

              testResult.value = {
                success: true,
                message: `模型文件已选择: ${filePath.split(/[/\\]/).pop()}`,
              };
            } else {
              console.log('ℹ️ [KnowledgeBaseSettings] 用户取消了文件选择');
            }
          })
          .catch((error: Error) => {
            console.error('❌ [KnowledgeBaseSettings] Promise处理失败:', error);
            testResult.value = {
              success: false,
              message: `文件选择失败: ${error.message}`,
            };
          });
      } else {
        // 如果不是Promise，直接处理
        console.log('🔍 [KnowledgeBaseSettings] 不是Promise对象，直接处理:', filePathPromise);
        const filePath = filePathPromise;

        if (filePath && typeof filePath === 'string' && filePath.trim() !== '') {
          updateSettings({ localModelPath: filePath });
          console.log('✅ [KnowledgeBaseSettings] 选择的模型文件:', filePath);

          testResult.value = {
            success: true,
            message: `模型文件已选择: ${filePath.split(/[/\\]/).pop()}`,
          };
        } else {
          console.log('ℹ️ [KnowledgeBaseSettings] 用户取消了文件选择');
        }
      }
    } catch (error) {
      console.error('❌ [KnowledgeBaseSettings] Qt文件选择失败:', error);
      testResult.value = {
        success: false,
        message: `文件选择失败: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  } else {
    // 回退到Web文件选择器（开发环境）
    console.log('ℹ️ [KnowledgeBaseSettings] 使用Web文件选择器 (开发环境)');
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.gguf';
    input.onchange = (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        // 在Web环境中，只能获取文件名，不能获取完整路径
        const fileName = file.name;
        updateSettings({ localModelPath: fileName });
        console.log('⚠️ [KnowledgeBaseSettings] Web环境选择的文件名:', fileName);

        testResult.value = {
          success: false,
          message: `Web环境无法获取完整文件路径，请在Qt应用环境中使用此功能`,
        };
      }
    };
    input.click();
  }
};

const detectGpuCapabilities = async () => {
  const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;
  if (!knowledgeApi) {
    testResult.value = {
      success: false,
      message: '未检测到Qt环境，GPU检测功能不可用',
    };
    return;
  }

  detectingGpu.value = true;
  testResult.value = null;

  try {
    console.log('🔍 [KnowledgeBaseSettings] 检测所有GPU设备...');

    // 使用新的detectAllGpuDevices API - 正确处理Promise
    const resultDataPromise = knowledgeApi.detectAllGpuDevices();
    console.log('🔍 [KnowledgeBaseSettings] GPU设备检测返回Promise:', resultDataPromise);
    console.log('🔍 [KnowledgeBaseSettings] 返回结果类型:', typeof resultDataPromise);

    let resultData: string;

    // 检查是否是Promise
    if (
      resultDataPromise &&
      typeof (resultDataPromise as unknown as { then?: (...args: unknown[]) => unknown }).then ===
        'function'
    ) {
      console.log('🔍 [KnowledgeBaseSettings] 检测到Promise，等待解析...');
      resultData = await (resultDataPromise as unknown as Promise<string>);
      console.log('🔍 [KnowledgeBaseSettings] Promise解析后的结果:', resultData);
    } else {
      // 直接使用返回值
      resultData = resultDataPromise;
      console.log('🔍 [KnowledgeBaseSettings] 直接使用返回值:', resultData);
    }

    let result: {
      devices: Array<{
        deviceId: number;
        deviceName: string;
        backend: string;
        available: boolean;
        maxLayers?: number;
        recommendedLayers?: number;
        memorySize?: number;
        availableMemory?: number;
      }>;
      count: number;
    };

    // 处理不同类型的返回值
    if (typeof resultData === 'string') {
      // 如果是字符串，解析为JSON
      result = JSON.parse(resultData);
    } else if (typeof resultData === 'object' && resultData !== null) {
      // 如果是对象，直接使用
      result = resultData;
    } else {
      throw new Error(`Invalid result type: expected string or object, got ${typeof resultData}`);
    }

    console.log('🔍 [KnowledgeBaseSettings] 解析后的设备列表:', result);

    if (result.devices && result.devices.length > 0) {
      // 构建GPU设备信息列表
      const devices: GpuDeviceInfo[] = result.devices.map((device) => ({
        deviceId: device.deviceId,
        deviceName: device.deviceName,
        backend: device.backend,
        available: device.available,
        maxLayers: device.maxLayers || 0,
        recommendedLayers: device.recommendedLayers || 20,
        memorySize: device.memorySize || 0,
        availableMemory: device.availableMemory || 0,
      }));

      availableGpuDevices.value = devices;
      gpuDetectionComplete.value = true;

      // 查找可用的GPU设备
      const gpuDevices = devices.filter((device) => device.deviceId >= 0 && device.available);

      if (gpuDevices.length > 0) {
        testResult.value = {
          success: true,
          message: `GPU检测成功！发现 ${gpuDevices.length} 个GPU设备`,
        };

        // 如果没有选择GPU设备，默认选择第一个可用的GPU
        if (settings.value.selectedGpuDevice === -1 && gpuDevices.length > 0) {
          updateSettings({ selectedGpuDevice: gpuDevices[0].deviceId });
        }

        // 自动更新推荐的GPU层数
        const selectedDevice = devices.find(
          (device) => device.deviceId === settings.value.selectedGpuDevice,
        );
        if (
          selectedDevice &&
          selectedDevice.recommendedLayers &&
          settings.value.localGpuLayers === 20
        ) {
          updateSettings({ localGpuLayers: selectedDevice.recommendedLayers });
        }
      } else {
        // 只有CPU可用
        testResult.value = {
          success: false,
          message: 'GPU不可用，将使用CPU进行推理',
        };

        // 强制选择CPU
        updateSettings({ selectedGpuDevice: -1, localUseGpu: false });
      }
    } else {
      throw new Error('No devices returned from GPU detection');
    }
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] GPU检测失败:', error);

    // 检测失败时，提供CPU选项
    const cpuDevice: GpuDeviceInfo = {
      deviceId: -1,
      deviceName: 'CPU (GPU检测失败)',
      backend: 'CPU',
      available: true,
      maxLayers: 0,
      recommendedLayers: 0,
    };

    availableGpuDevices.value = [cpuDevice];
    gpuDetectionComplete.value = true;
    updateSettings({ selectedGpuDevice: -1, localUseGpu: false });

    testResult.value = {
      success: false,
      message: `GPU检测失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  } finally {
    detectingGpu.value = false;
  }
};

const loadLocalModel = async () => {
  console.log('🔄 [KnowledgeBaseSettings] 开始加载模型，设置 loadingModel = true');
  loadingModel.value = true;
  loadingProgress.value = '正在初始化...';

  // 在加载期间停止状态检查，避免冲突
  stopStatusCheck();

  // 等待 UI 更新，确保 spinner 能够显示
  await nextTick();

  const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;
  if (!knowledgeApi) {
    testResult.value = {
      success: false,
      message: '未检测到Qt环境，本地模型功能不可用',
    };
    loadingModel.value = false;
    loadingProgress.value = '';
    // 重启状态检查
    startStatusCheck();
    return;
  }

  if (!settings.value.localModelPath) {
    testResult.value = {
      success: false,
      message: '请先选择本地模型文件',
    };
    loadingModel.value = false;
    loadingProgress.value = '';
    // 重启状态检查
    startStatusCheck();
    return;
  }

  testResult.value = null;

  try {
    console.log('🔍 [KnowledgeBaseSettings] 启动后台模型加载:', settings.value.localModelPath);
    loadingProgress.value = '正在加载模型文件...';

    // 设置加载完成的回调
    const onLoadCompleted = (success: boolean, message: string) => {
      console.log('📡 [KnowledgeBaseSettings] 收到模型加载完成信号:', { success, message });

      if (success) {
        // 立即更新状态为已加载
        isLocalModelLoaded.value = true;
        loadingProgress.value = '加载完成';

        testResult.value = {
          success: true,
          message: message,
        };

        // 获取模型信息
        try {
          const modelInfo = knowledgeApi.getLocalGGUFInfo();
          console.log('📊 [KnowledgeBaseSettings] 模型信息:', modelInfo);
        } catch (error) {
          console.warn('⚠️ [KnowledgeBaseSettings] 获取模型信息失败:', error);
        }

        console.log('✅ [KnowledgeBaseSettings] 模型已加载，UI状态已更新');
      } else {
        testResult.value = {
          success: false,
          message: message,
        };
        console.error('❌ [KnowledgeBaseSettings] 模型加载失败:', message);
      }

      // 重置加载状态
      loadingModel.value = false;
      loadingProgress.value = '';

      // 断开信号连接
      knowledgeApi.localModelLoadCompleted.disconnect(onLoadCompleted);

      // 重启状态检查
      startStatusCheck();
    };

    // 连接信号
    knowledgeApi.localModelLoadCompleted.connect(onLoadCompleted);

    // 启动异步加载（不阻塞UI）- 使用支持GPU选择的新API
    knowledgeApi.loadLocalGGUFModelAsyncWithGpu(
      settings.value.localModelPath,
      settings.value.localGpuLayers,
      settings.value.localContextSize,
      settings.value.selectedGpuDevice ?? -1,
    );

    console.log('🚀 [KnowledgeBaseSettings] 异步加载已启动，等待后台完成...');
    console.log('🎯 [KnowledgeBaseSettings] 选中的GPU设备:', settings.value.selectedGpuDevice);

    // 显示加载进度
    setTimeout(() => {
      if (loadingModel.value) {
        loadingProgress.value = '正在处理模型数据...';
      }
    }, 2000);

    setTimeout(() => {
      if (loadingModel.value) {
        loadingProgress.value = '即将完成加载...';
      }
    }, 5000);
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] 启动异步加载失败:', error);
    testResult.value = {
      success: false,
      message: `启动加载失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
    loadingModel.value = false;
    loadingProgress.value = '';
    // 重启状态检查
    startStatusCheck();
  }
};

const unloadLocalModel = async () => {
  const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;
  if (!knowledgeApi) {
    testResult.value = {
      success: false,
      message: '未检测到Qt环境，本地模型功能不可用',
    };
    return;
  }

  console.log('🔄 [KnowledgeBaseSettings] 开始卸载模型，设置 unloadingModel = true');
  unloadingModel.value = true;

  // 等待 UI 更新，确保 spinner 能够显示
  await nextTick();

  testResult.value = null;

  try {
    console.log('🔍 [KnowledgeBaseSettings] 卸载本地模型...');

    // 使用 setTimeout 来确保 UI 有时间更新
    await new Promise((resolve) => setTimeout(resolve, 100));

    knowledgeApi.unloadLocalGGUFModel();

    // 立即更新状态为未加载
    isLocalModelLoaded.value = false;

    testResult.value = {
      success: true,
      message: '本地模型已成功卸载',
    };

    console.log('✅ [KnowledgeBaseSettings] 模型已卸载，UI状态已更新');
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] 本地模型卸载失败:', error);
    testResult.value = {
      success: false,
      message: `本地模型卸载失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  } finally {
    unloadingModel.value = false;
  }
};

const testLocalGGUF = async () => {
  const knowledgeApi = (window as unknown as { knowledgeApi?: KnowledgeApi }).knowledgeApi;
  if (!knowledgeApi) {
    testResult.value = {
      success: false,
      message: '未检测到Qt环境，本地模型功能不可用',
    };
    return;
  }

  testingLocalGGUF.value = true;
  testResult.value = null;

  try {
    const testText = '这是一个本地GGUF模型测试文本，用于验证嵌入向量生成功能。';
    console.log('🔍 [KnowledgeBaseSettings] 测试本地GGUF模型...');
    console.log('🎯 [KnowledgeBaseSettings] 当前选中的GPU设备:', settings.value.selectedGpuDevice);

    // Qt WebChannel 可能返回 Promise 或直接返回值，统一处理为 Promise
    const resultDataPromise = knowledgeApi.testLocalGGUFEmbedding(testText);
    console.log('🔍 [KnowledgeBaseSettings] 本地GGUF测试返回结果:', resultDataPromise);
    console.log('🔍 [KnowledgeBaseSettings] 返回结果类型:', typeof resultDataPromise);

    let resultData: string;

    // 检查是否是Promise
    if (
      resultDataPromise &&
      typeof (resultDataPromise as unknown as { then?: (...args: unknown[]) => unknown }).then ===
        'function'
    ) {
      console.log('🔍 [KnowledgeBaseSettings] 检测到Promise，等待解析...');
      resultData = await (resultDataPromise as unknown as Promise<string>);
      console.log('🔍 [KnowledgeBaseSettings] Promise解析后的结果:', resultData);
    } else {
      // 直接使用返回值
      resultData = resultDataPromise;
      console.log('🔍 [KnowledgeBaseSettings] 直接使用返回值:', resultData);
    }

    let result: {
      success: boolean;
      dimension?: number;
      inference_time_ms?: number;
      text_length?: number;
      gpu_enabled?: boolean;
      embedding_preview?: number[];
      message?: string;
      error?: string;
    };

    // 处理不同类型的返回值
    if (typeof resultData === 'string') {
      // 如果是字符串，解析为JSON
      result = JSON.parse(resultData);
    } else if (typeof resultData === 'object' && resultData !== null) {
      // 如果是对象，直接使用
      result = resultData;
    } else {
      throw new Error(`Invalid result type: expected string or object, got ${typeof resultData}`);
    }

    console.log('🔍 [KnowledgeBaseSettings] 解析后的结果:', result);

    if (result.success) {
      const resultData = result as typeof result & {
        recommended_max_safe_tokens?: number;
        auto_saved_to_database?: boolean;
      };

      testResult.value = {
        success: true,
        message: `本地GGUF模型测试成功！向量维度: ${result.dimension}，推理时间: ${result.inference_time_ms}ms，GPU加速: ${result.gpu_enabled ? '是' : '否'}`,
      };

      console.log('✅ [KnowledgeBaseSettings] 本地GGUF测试成功:', {
        dimension: result.dimension,
        inferenceTime: result.inference_time_ms,
        gpuEnabled: result.gpu_enabled,
        textLength: result.text_length,
        autoSaved: resultData.auto_saved_to_database,
      });

      // 保存检测到的维度
      if (result.dimension && result.dimension > 0) {
        updateSettings({
          embeddingDimension: result.dimension,
        });
        console.log('💾 [KnowledgeBaseSettings] 已保存本地模型维度:', result.dimension);
      }
    } else {
      testResult.value = {
        success: false,
        message: `本地GGUF模型测试失败: ${result.error}`,
      };
    }
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] 本地GGUF测试失败:', error);
    testResult.value = {
      success: false,
      message: `本地GGUF模型测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  } finally {
    testingLocalGGUF.value = false;
  }
};

// 测试GPU选择功能的完整流程
const testGpuSelectionWorkflow = async () => {
  try {
    $q.notify({
      type: 'info',
      message: $t('src.components.settings.KnowledgeBaseSettings.test_gpu_start'),
      timeout: 1000,
    });
    // 1. 检查GPU检测是否完成
    if (!gpuDetectionComplete.value) {
      console.log('⚠️ [KnowledgeBaseSettings] GPU检测尚未完成，开始检测...');
      await detectGpuCapabilities();
      return;
    }

    // 2. 检查是否有可用的GPU设备
    console.log('📊 [KnowledgeBaseSettings] 可用GPU设备列表:', availableGpuDevices.value);

    // 3. 检查当前选择的GPU设备
    const selectedDevice = selectedGpuDeviceInfo.value;
    console.log('🎯 [KnowledgeBaseSettings] 当前选择的GPU设备:', selectedDevice);

    // 4. 检查配置是否保存到数据库
    const savedSettings = settings.value;
    console.log('💾 [KnowledgeBaseSettings] 当前保存的知识库设置:', {
      embeddingMode: savedSettings.embeddingMode,
      localModelPath: savedSettings.localModelPath,
      selectedGpuDevice: savedSettings.selectedGpuDevice,
      localUseGpu: savedSettings.localUseGpu,
      localGpuLayers: savedSettings.localGpuLayers,
    });

    // 5. 模拟GPU设备切换测试
    if (availableGpuDevices.value.length > 1) {
      const currentDeviceId = settings.value.selectedGpuDevice;
      const otherDevice = availableGpuDevices.value.find(
        (device) => device.deviceId !== currentDeviceId,
      );

      if (otherDevice) {
        console.log('🔄 [KnowledgeBaseSettings] 测试GPU设备切换:', {
          from: currentDeviceId,
          to: otherDevice.deviceId,
        });

        // 临时切换到另一个设备
        updateSettings({ selectedGpuDevice: otherDevice.deviceId });

        // 等待一下让UI更新
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 验证切换是否生效
        if (settings.value.selectedGpuDevice === otherDevice.deviceId) {
          console.log('✅ [KnowledgeBaseSettings] GPU设备切换测试成功');

          // 切换回原设备
          updateSettings({ selectedGpuDevice: currentDeviceId });
        } else {
          console.error('❌ [KnowledgeBaseSettings] GPU设备切换测试失败');
        }
      }
    }

    // 6. 检查知识库配置验证逻辑
    const isConfigured = store.isKnowledgeBaseConfigured();
    console.log('🔍 [KnowledgeBaseSettings] 知识库配置验证结果:', isConfigured);

    // 7. 如果有本地模型路径且选择了GPU，测试模型相关功能
    if (savedSettings.localModelPath && savedSettings.selectedGpuDevice !== undefined) {
      console.log('🧪 [KnowledgeBaseSettings] 执行模型相关功能测试...');

      if (isLocalModelLoaded.value) {
        console.log('✅ [KnowledgeBaseSettings] 本地模型已加载，可以执行GPU相关测试');

        // 记录当前模型状态
        testResult.value = {
          success: true,
          message: `GPU选择功能测试完成！当前使用设备: ${gpuDeviceStatusText.value}，本地模型状态: 已加载`,
        };
      } else {
        console.log('ℹ️ [KnowledgeBaseSettings] 本地模型未加载，GPU设备配置已就绪');

        testResult.value = {
          success: true,
          message: `GPU选择功能配置完成！当前使用设备: ${gpuDeviceStatusText.value}，可尝试加载本地模型进行测试`,
        };
      }
    } else {
      testResult.value = {
        success: true,
        message: `GPU检测和选择功能正常工作！当前状态: ${gpuDeviceStatusText.value}`,
      };
    }

    console.log('🎉 [KnowledgeBaseSettings] GPU选择功能测试流程完成');
    $q.notify({
      type: 'positive',
      message: $t('src.components.settings.KnowledgeBaseSettings.test_gpu_success', {
        device: gpuDeviceStatusText.value,
      }),
      timeout: 1000,
    });
  } catch (error) {
    console.error('❌ [KnowledgeBaseSettings] GPU选择功能测试失败:', error);
    const errMessage = `GPU选择功能测试失败: ${error instanceof Error ? error.message : '未知错误'}`;
    testResult.value = {
      success: false,
      message: errMessage,
    };
    $q.notify({
      type: 'negative',
      message: $t('src.components.settings.KnowledgeBaseSettings.test_gpu_failed', {
        error: errMessage,
      }),
      timeout: 1000,
    });
  }
};
const openDebugTool = () => {
  void router.push('/debug');
};
</script>

<style lang="scss" scoped>
.knowledge-base-settings {
  width: 100%;
}
</style>
