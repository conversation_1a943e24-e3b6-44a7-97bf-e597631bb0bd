# 拖拽排序功能重构说明

## 概述

本次重构简化了分割窗口内文档的拖拽排序逻辑，解决了原有实现中的bug和复杂性问题，并增加了重复文档检查功能。

## 主要改进

### 1. 统一的拖拽状态管理

**之前的问题：**

- 拖拽状态分散在多个地方
- 使用简单的 `dragDocument` 变量，缺乏完整的上下文信息
- 事件处理逻辑复杂且容易出错

**重构后的解决方案：**

```typescript
interface DragState {
  isDragging: boolean;
  draggedDocument: Document | null;
  sourceWindowId: number | null;
  sourceIndex: number | null;
}
```

### 2. 简化的拖拽方法

**新的核心方法：**

- `startDragDocument(windowId, docId)` - 开始拖拽
- `endDragDocument()` - 结束拖拽
- `sortDocumentsInWindow(windowId, oldIndex, newIndex)` - 窗口内排序
- `moveDocumentToWindow(targetWindowId, targetIndex)` - 跨窗口移动
- `handleDragComplete(targetWindowId, targetIndex)` - 统一处理拖拽完成

### 3. 重复文档检查功能 ⭐ **新增**

**核心特性：**

- 同一分割窗口内不能存在相同的文档
- 跨窗口拖拽时自动检查重复文档
- 分割窗口时检查文档是否已存在
- 用户友好的重复文档提示

**新增的辅助方法：**

- `isDocumentInWindow(windowId, docId)` - 检查文档是否在指定窗口中
- `getWindowContainingDocument(docId)` - 获取包含指定文档的窗口
- `removeDuplicateDocuments(windowId)` - 移除窗口内的重复文档

### 4. 改进的事件处理

**EditorGroup.vue 中的简化：**

```typescript
const dragOptions = [
  props.documents,
  {
    animation: 150,
    group: 'documents',
    disabled: false,
    ghostClass: 'ghost',
    onStart: (evt) => {
      const docId = Number(evt.item.getAttribute('data-id'));
      if (docId) {
        docStore.startDragDocument(props.winId, docId);
      }
    },
    onAdd: (evt) => {
      docStore.setActiveSplitterWindowId(props.winId);

      // 检查重复文档
      const draggedDoc = docStore.draggedDocument;
      if (draggedDoc && docStore.isDocumentInWindow(props.winId, draggedDoc.id)) {
        $q.notify({
          type: 'warning',
          message: '该文档已在此窗口中打开',
          position: 'top',
          timeout: 2000,
        });
        docStore.endDragDocument();
        return;
      }

      if (docStore.isDragging) {
        docStore.handleDragComplete(props.winId, evt.newIndex);
      }
    },
    onSort: (evt) => {
      if (docStore.isDragging) {
        docStore.sortDocumentsInWindow(props.winId, evt.oldIndex, evt.newIndex);
        docStore.endDragDocument();
      }
    },
  },
];
```

## 功能特性

### 1. 窗口内排序

- 支持在同一分割窗口内拖拽排序文档
- 自动更新活动文档状态
- 自动移除排序后产生的重复文档

### 2. 跨窗口移动

- 支持将文档从一个分割窗口拖拽到另一个窗口
- 自动检查目标窗口是否已存在相同文档
- 如果存在重复文档，只从源窗口移除，不添加到目标窗口
- 自动处理空窗口的清理
- 自动设置目标窗口为活动窗口

### 3. 重复文档防护

- **同一窗口内**：排序后自动移除重复文档
- **跨窗口拖拽**：检测到重复时阻止添加，显示用户提示
- **分割窗口**：检查文档是否已存在，如果存在则直接激活对应窗口
- **用户反馈**：通过通知提示用户重复文档的情况

### 4. 状态同步

- 拖拽过程中保持数据一致性
- 自动更新编辑器实例映射
- 正确处理活动文档切换

## 兼容性

为了保持向后兼容性，保留了原有的方法名作为包装器：

- `setDragDocument()` → `startDragDocument()`
- `dragDocmentInWin()` → `handleDragComplete()`
- `sortDocuments()` → `sortDocumentsInWindow()`
- `dragDocmentOutWin()` → `endDragDocument()`

## 使用示例

```typescript
// 开始拖拽
docStore.startDragDocument(windowId, docId);

// 检查文档是否在窗口中
const exists = docStore.isDocumentInWindow(windowId, docId);

// 获取包含文档的窗口
const window = docStore.getWindowContainingDocument(docId);

// 处理拖拽完成（同一窗口内排序）
docStore.sortDocumentsInWindow(windowId, oldIndex, newIndex);

// 处理跨窗口移动
docStore.moveDocumentToWindow(targetWindowId, targetIndex);

// 结束拖拽
docStore.endDragDocument();
```

## 注意事项

1. 拖拽操作会自动处理窗口的创建和销毁
2. 空窗口会被自动清理
3. 活动文档状态会自动更新
4. 编辑器实例映射会正确维护
5. **重复文档检查是强制性的，无法绕过**
6. 用户会收到重复文档的友好提示
7. 分割窗口时会智能检查文档是否已存在
