# PowerEdge 组件高度拖拽调节功能

## 功能概述

PowerEdge 组件现在支持通过拖拽调节高度，为用户提供更灵活的界面布局体验。该功能专为展示文档附加信息、数据和第三方工具而设计。

## 主要特性

### 1. 拖拽调节高度

- **拖拽手柄**: 组件顶部有一个透明的拖拽手柄，拖拽时显示蓝色背景
- **高度限制**: 最小高度 37px，无最大高度限制
- **实时调节**: 拖拽过程中实时更新高度
- **持久化**: 用户设置的高度会保存到本地存储

### 2. 性能优化

- **useMouse响应式**: 使用 @vueuse/core 的 useMouse 实现实时响应鼠标位置
- **watch监听**: 通过 watch 监听鼠标y位置变化，无延迟更新高度
- **flex布局**: 使用 flex: 0 0 \*\*\*px 设置高度，适配上级flex容器
- **自动保存**: 高度变化自动保存到localStorage，下次启动自动恢复
- **避免编辑器干扰**: 拖拽操作不会影响编辑器的正常功能

### 3. 用户体验

- **精确拖拽**: 修复了光标漂移问题，拖拽响应更精确
- **视觉反馈**: 拖拽手柄在悬停和拖拽时有清晰的视觉反馈
- **触摸支持**: 支持移动设备的触摸拖拽
- **防止文本选择**: 拖拽时禁用文本选择
- **光标指示**: 拖拽时显示调节光标
- **流畅动画**: 优化了拖拽过程的流畅性

## 技术实现

### 组件结构

```vue
<template>
  <div class="power-edge-container">
    <!-- 拖拽手柄 -->
    <div
      class="resize-handle"
      :class="{ 'bg-primary': isResizing }"
      @mousedown="startResize"
      @touchstart="startResize"
    />

    <!-- 内容区域 -->
    <div class="power-edge-content column no-wrap">
      <!-- Tab 导航和内容 -->
    </div>
  </div>
</template>
```

### 核心方法

#### startResize()

- 初始化拖拽状态
- 记录起始位置和高度
- 添加全局事件监听器
- 设置防选择样式

#### handleResize()

- 计算新高度
- 应用高度限制
- 使用 requestAnimationFrame 优化性能
- 触发 resize 事件

#### stopResize()

- 清理拖拽状态
- 移除事件监听器
- 恢复默认样式

### 拖拽优化改进

**问题修复**：

- **光标漂移**: 修正了拖拽手柄位置计算，避免光标向上漂移
- **响应精度**: 使用组件内部高度状态而不是DOM查询，提高响应精度
- **视觉反馈**: 增强了拖拽手柄的视觉效果和交互反馈

**useMouse方案的最终实现**：

```vue
<!-- flex布局设置高度 -->
<div class="power-edge-container" :style="`flex: 0 0 ${currentHeight}px`">
  <!-- 动态class控制拖拽状态 -->
  <div class="resize-handle" :class="{ 'bg-primary': isResizing }" @mousedown="startResize" />
</div>
```

```typescript
import { useMouse } from '@vueuse/core';

const { y } = useMouse();

// 从localStorage加载保存的高度，默认37px
const currentHeight = ref(Number(localStorage.getItem('powerEdgeHeight')) || 37);
const isResizing = ref(false);
const startY = ref(0);
const startHeight = ref(0);

// 监听鼠标y位置变化，实时更新高度
watch(y, (newY) => {
  if (isResizing.value) {
    const deltaY = startY.value - newY; // 向上拖拽为正值
    const newHeight = Math.max(MIN_HEIGHT, startHeight.value + deltaY);
    currentHeight.value = newHeight;
  }
});

// 监听currentHeight变化，保存到localStorage
watch(
  currentHeight,
  (newHeight) => {
    localStorage.setItem('powerEdgeHeight', newHeight.toString());
  },
  { immediate: false },
);

// 开始拖拽调整大小
const startResize = (event) => {
  isResizing.value = true;

  // 记录初始y位置和高度
  startY.value = y.value;
  startHeight.value = currentHeight.value;

  document.body.classList.add('resizing');
  document.addEventListener('mouseup', stopResize);
  event.preventDefault();
};
```

**交互优化**：

```typescript
// 使用 requestAnimationFrame 优化拖拽性能
let animationFrameId: number | null = null;
let pendingHeight: number | null = null;

const updateHeight = () => {
  if (pendingHeight === null) return;

  currentHeight.value = pendingHeight;
  pendingHeight = null;
  animationFrameId = null;
};

// 处理拖拽过程 - 使用节流优化性能
const handleResize = (event) => {
  const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;
  const deltaY = startY.value - clientY;
  const newHeight = Math.max(MIN_HEIGHT, startHeight.value + deltaY);

  // 使用 requestAnimationFrame 节流更新
  pendingHeight = newHeight;
  if (animationFrameId === null) {
    animationFrameId = requestAnimationFrame(updateHeight);
  }
};

const stopResize = () => {
  // 确保最后一次更新完成
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId);
    updateHeight();
  }

  // 保存到localStorage
  localStorage.setItem('powerEdgeHeight', currentHeight.value.toString());
  // ...
};
```

### 组件自管理架构

PowerEdge 组件采用完全自管理的架构设计：

```vue
<!-- 在 TipTap.vue 中的使用 -->
<PowerEdge class="border-top">
  <template #right>
    <!-- 右侧内容插槽 -->
  </template>
</PowerEdge>
```

```typescript
// PowerEdge 组件内部管理
const currentHeight = ref(37);

// 从本地存储加载高度
onMounted(() => {
  const savedHeight = localStorage.getItem('powerEdgeHeight');
  if (savedHeight) {
    const height = Number(savedHeight);
    if (height >= MIN_HEIGHT && height <= MAX_HEIGHT) {
      currentHeight.value = height;
    }
  }
});

// 拖拽时直接更新内部高度
const handleResize = (event) => {
  // ... 计算新高度
  requestAnimationFrame(() => {
    currentHeight.value = newHeight;
    // 防抖保存到本地存储
    saveHeightToStorage(newHeight);
  });
};
```

### 架构优势

1. **组件封装性**: 所有高度管理逻辑都在PowerEdge组件内部，符合单一职责原则
2. **减少父子通信**: 不需要通过事件和props在父子组件间传递高度数据
3. **性能优化**: 避免了不必要的父组件重新渲染
4. **代码简洁**: 父组件使用更简单，只需要基本的插槽内容
5. **维护性**: 高度相关的所有逻辑集中在一个组件中，便于维护和调试

### 架构升级：移至SplitterEditor层级

**升级原因**：

- **性能优化**: 从每个TipTap实例一个PowerEdge改为全局单一实例
- **状态管理**: 直接访问全局状态（docId、activePaneIndex）
- **减少重复**: 多分割面板时避免重复渲染
- **更好的响应性**: 能够响应当前编辑内容的变化

**新的组件层级**：

```
SplitterEditor (顶层容器)
├── EditorToolbar (全局工具栏)
├── PowerEdge (全局面板 - 新位置)
└── SplitterPane (分割面板)
    └── EditorGroup (编辑器组)
        └── TipTap (编辑器实例)
```

**实现方式**：

```vue
<!-- SplitterEditor.vue -->
<PowerEdge
  v-if="docStore.splitterWindows?.length > 0 && docId"
  class="border-top"
  :docId="docId"
  :activePaneIndex="activePaneIndex"
  :wordCount="wordCount"
>
  <template #right>
    <div class="row no-wrap items-center q-pa-xs">
      <span class="q-px-sm">字数: {{ wordCount }}</span>
    </div>
  </template>
</PowerEdge>
```

**字数统计实现**：

```typescript
// src/utils/wordCount.ts - 智能字数统计工具
export function countWords(text: string): number {
  if (!text || text.trim().length === 0) {
    return 0;
  }

  // 移除多余的空白字符
  const cleanText = text.replace(/\s+/g, ' ').trim();

  // 分离中文字符和英文内容
  const chineseChars = cleanText.match(/[\u4e00-\u9fff]/g) || [];
  const chineseCount = chineseChars.length;

  // 移除中文字符，只保留英文内容
  const englishText = cleanText.replace(/[\u4e00-\u9fff]/g, '').trim();

  // 统计英文单词数量（以空格分隔的非空字符串）
  const englishWords = englishText.split(/\s+/).filter((word) => word.length > 0);
  const englishCount = englishWords.length;

  return chineseCount + englishCount;
}

// SplitterEditor.vue 中的字数统计
const wordCount = computed(() => {
  if (!docId.value || activePaneIndex.value >= docStore.splitterWindows.length) {
    return 0;
  }

  const activeWindow = docStore.splitterWindows[activePaneIndex.value];
  const editorInstance = docStore.getEditorInstance(activeWindow.id, docId.value);

  if (editorInstance) {
    // 获取编辑器内容的纯文本并使用智能字数统计
    const text = editorInstance.getText();
    return countWords(text);
  }

  return 0;
});
```

**字数统计规则**：

- **中文字符**: 按字符数统计（每个汉字计为1个字）
- **英文单词**: 按单词数统计（以空格分隔的单词）
- **数字和标点**: 按单词处理
- **混合文本**: 中文字符数 + 英文单词数

**示例**：

- `"这是测试"` → 4字（4个中文字符）
- `"This is test"` → 3字（3个英文单词）
- `"这是 a test"` → 4字（2个中文字符 + 2个英文单词）

## 使用场景

### 1. 文档附加信息

- 显示文档元数据
- 展示相关链接和引用
- 显示文档统计信息

### 2. 第三方工具集成

- 邮件工具面板
- 提醒和通知工具
- 多媒体处理工具
- 数据分析工具

### 3. 开发者工具

- 调试信息显示
- 性能监控面板
- 日志查看器

## 性能考虑

### 1. 避免编辑器性能影响

- 拖拽操作使用独立的事件处理
- 不干扰编辑器的输入和渲染
- 使用 requestAnimationFrame 优化动画

### 2. 内存管理

- 及时清理事件监听器
- 避免内存泄漏
- 合理使用防抖和节流

### 3. 响应式设计

- 支持不同屏幕尺寸
- 触摸设备友好
- 高 DPI 屏幕适配

## 未来扩展

### 1. 更多布局选项

- 支持左右拖拽调节宽度
- 支持多面板布局
- 支持面板折叠/展开

### 2. 高级功能

- 预设高度快速切换
- 键盘快捷键支持
- 面板内容的拖拽排序

### 3. 主题集成

- 深色模式适配
- 自定义主题颜色
- 动画效果配置

## 注意事项

1. **高度限制**: 确保在合理范围内调节高度
2. **性能监控**: 注意拖拽操作对整体性能的影响
3. **兼容性**: 测试不同浏览器和设备的兼容性
4. **用户体验**: 保持操作的直观性和响应性
