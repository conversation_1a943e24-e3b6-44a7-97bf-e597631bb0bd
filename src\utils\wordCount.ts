/**
 * 字数统计工具函数
 * 区分中文字符和英文单词的统计方式
 */

/**
 * 统计文本的字数
 * - 中文：按字符数统计
 * - 英文：按单词数统计
 * - 数字和标点符号：按单词处理
 *
 * @param text 要统计的文本
 * @returns 字数统计结果
 */
export function countWords(text: string): number {
  if (!text || text.trim().length === 0) {
    return 0;
  }

  // 移除多余的空白字符
  const cleanText = text.replace(/\s+/g, ' ').trim();

  // 分离中文字符和英文内容
  const chineseChars = cleanText.match(/[\u4e00-\u9fff]/g) || [];
  const chineseCount = chineseChars.length;

  // 移除中文字符，只保留英文内容
  const englishText = cleanText.replace(/[\u4e00-\u9fff]/g, '').trim();

  // 统计英文单词数量（以空格分隔的非空字符串）
  const englishWords = englishText.split(/\s+/).filter((word) => word.length > 0);
  const englishCount = englishWords.length;

  return chineseCount + englishCount;
}

/**
 * 获取详细的字数统计信息
 * @param text 要统计的文本
 * @returns 详细统计信息
 */
export function getWordCountDetails(text: string): {
  total: number;
  chinese: number;
  english: number;
  characters: number;
  charactersNoSpaces: number;
} {
  if (!text || text.trim().length === 0) {
    return {
      total: 0,
      chinese: 0,
      english: 0,
      characters: 0,
      charactersNoSpaces: 0,
    };
  }

  const cleanText = text.replace(/\s+/g, ' ').trim();

  // 中文字符统计
  const chineseChars = cleanText.match(/[\u4e00-\u9fff]/g) || [];
  const chineseCount = chineseChars.length;

  // 英文单词统计
  const englishText = cleanText.replace(/[\u4e00-\u9fff]/g, '').trim();
  const englishWords = englishText.split(/\s+/).filter((word) => word.length > 0);
  const englishCount = englishWords.length;

  // 字符统计
  const totalCharacters = text.length;
  const charactersNoSpaces = text.replace(/\s/g, '').length;

  return {
    total: chineseCount + englishCount,
    chinese: chineseCount,
    english: englishCount,
    characters: totalCharacters,
    charactersNoSpaces: charactersNoSpaces,
  };
}
