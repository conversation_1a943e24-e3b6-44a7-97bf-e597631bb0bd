# 拖拽功能实现文档

## 功能概述

已成功为 FolderTree.vue 组件实现了拖拽功能，支持：

1. **文档拖拽**：可以将文档拖拽到文件夹上，修改其父文件夹
2. **文件夹拖拽**：可以将文件夹拖拽到其他文件夹上，修改其父文件夹
3. **实时UI更新**：拖拽完成后自动更新文件树结构和UI显示
4. **循环引用防护**：防止将文件夹拖拽到其子文件夹中

## 技术实现

### 1. 拖拽库
使用 `@atlaskit/pragmatic-drag-and-drop` 库，该库已在项目中安装。

### 2. 核心文件

#### `src/utils/dragAndDrop.ts`
- 封装了拖拽功能的工具函数
- `makeDraggable()`: 设置元素为可拖拽
- `makeDropTarget()`: 设置元素为拖拽目标
- 定义了 `DragData` 和 `DropData` 接口

#### `src/stores/doc.ts`
新增方法：
- `moveDocumentToFolder()`: 移动文档到新文件夹
- `moveFolderToParent()`: 移动文件夹到新父文件夹
- `isDescendantOf()`: 检查文件夹层级关系，防止循环引用

#### `src/components/FolderTree.vue`
- 为文件夹元素添加拖拽功能
- 设置文件夹为拖拽目标
- 处理拖拽事件和UI更新

#### `src/components/DocumentItem.vue`
- 为文档元素添加拖拽功能
- 支持文档的拖拽操作

### 3. 拖拽样式
在 FolderTree.vue 中添加了拖拽相关的CSS样式：
- `.dragging`: 拖拽时的视觉反馈
- `.drag-over`: 拖拽悬停时的高亮效果

## 使用方法

1. **拖拽文档**：
   - 点击并拖拽任意文档
   - 将其拖拽到目标文件夹上
   - 释放鼠标完成移动

2. **拖拽文件夹**：
   - 点击并拖拽任意文件夹
   - 将其拖拽到目标父文件夹上
   - 释放鼠标完成移动

## 数据库更新

拖拽操作会自动更新数据库中的关系：
- 文档的 `folder_id` 字段
- 文件夹的 `parent_id` 字段

## 安全特性

1. **类型安全**：使用 TypeScript 确保类型安全
2. **循环引用检查**：防止将文件夹移动到其子文件夹
3. **数据一致性**：确保UI和数据库状态同步

## 扩展性

该实现具有良好的扩展性，可以轻松添加：
- 拖拽预览
- 更复杂的验证规则
- 拖拽动画效果
- 批量拖拽操作

## 测试建议

1. 测试文档在不同文件夹间的移动
2. 测试文件夹的嵌套移动
3. 验证循环引用防护机制
4. 检查UI更新的实时性
5. 测试拖拽的视觉反馈效果
