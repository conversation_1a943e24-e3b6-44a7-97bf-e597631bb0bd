import { ref, computed } from 'vue';
import { Notify } from 'quasar';
import type { KnowledgeBase, KnowledgeSearchResult } from '../env';
import { $t } from 'src/composables/useTrans';

/**
 * Qt端搜索结果接口
 */
export interface QtSearchResult {
  chunk_id: string;
  document_id: string;
  document_title: string;
  content: string;
  similarity: number;
  knowledge_base_id?: string;
  knowledge_base_name?: string;
}

/**
 * 知识库事件类型
 */
export type KnowledgeEvent =
  | 'knowledge_base_created'
  | 'knowledge_base_updated'
  | 'knowledge_base_deleted'
  | 'knowledge_document_added'
  | 'knowledge_document_updated'
  | 'knowledge_document_removed'
  | 'knowledge_search_performed'
  | 'knowledge_context_updated'
  | 'knowledge_processing_started'
  | 'knowledge_processing_completed'
  | 'knowledge_error_occurred';

/**
 * 知识库事件载荷
 */
export interface KnowledgeEventPayload {
  event: KnowledgeEvent;
  timestamp: number;
  data?: unknown;
  error?: Error;
  metadata?: Record<string, unknown>;
}

/**
 * 知识库操作状态
 */
export interface KnowledgeOperationState {
  isLoading: boolean;
  isProcessing: boolean;
  error: Error | null;
  lastOperation: string | null;
  operationProgress?: number;
}

/**
 * 知识库统计信息
 */
export interface KnowledgeStats {
  totalBases: number;
  totalDocuments: number;
  totalChunks: number;
  lastUpdated: string;
  storageSize?: number;
}

/**
 * 特定知识库的详细统计信息
 */
export interface KnowledgeBaseStats {
  knowledge_base_id: number;
  knowledge_base_name?: string;
  document_count: number;
  total_documents: number; // 保持兼容性
  total_characters: number;
  total_size: number;
  total_chunks: number; // 已向量化的chunks数量（保持向后兼容）
  vectorized_chunks: number; // 已向量化的chunks数量
  total_chunks_count: number; // 总chunks数量
  average_chunks_per_doc: number;
  error?: string;
}

// 全局状态（用于事件系统和临时操作状态）
const knowledgeState = ref<{
  operationState: KnowledgeOperationState;
  stats: KnowledgeStats;
  eventHistory: KnowledgeEventPayload[];
}>({
  operationState: {
    isLoading: false,
    isProcessing: false,
    error: null,
    lastOperation: null,
  },
  stats: {
    totalBases: 0,
    totalDocuments: 0,
    totalChunks: 0,
    lastUpdated: new Date().toISOString(),
  },
  eventHistory: [],
});

// 事件监听器
const eventListeners = ref<Map<KnowledgeEvent, Array<(payload: KnowledgeEventPayload) => void>>>(
  new Map(),
);

// 文档向量化完成监听器
const documentVectorizedListeners = ref<Array<(docId: string, chunkCount: number) => void>>([]);

// 向量化进度监听器
const vectorizationProgressListeners = ref<
  Array<(kbId: string, docId: string, completed: number, total: number) => void>
>([]);

// 全局标识符，防止重复连接
let signalListenersSetup = false;

// 防抖机制，避免过多信号处理导致UI卡死
const progressDebounceMap = new Map<string, NodeJS.Timeout>();

// 监听Qt信号
const setupSignalListeners = () => {
  if (signalListenersSetup) {
    console.log('📡 [useKnowledge] 信号监听器已经设置过，跳过重复设置');
    return;
  }

  if (window.knowledgeApi && typeof window.knowledgeApi.documentVectorized === 'object') {
    // Qt WebChannel 信号连接
    window.knowledgeApi.documentVectorized.connect((docId: number, chunkCount: number) => {
      console.log('📡 [useKnowledge] 收到文档向量化完成信号:', docId, chunkCount);

      // 通知所有监听器
      documentVectorizedListeners.value.forEach((listener) => {
        try {
          // 确保 docId 是字符串格式
          listener(docId.toString(), chunkCount);
        } catch (error) {
          console.error('❌ [useKnowledge] 信号监听器执行失败:', error);
        }
      });
    });

    // 监听向量化进度信号
    if (window.knowledgeApi.vectorizationProgress) {
      window.knowledgeApi.vectorizationProgress.connect(
        (kbId: string, docId: string, completed: number, total: number) => {
          console.log('📡 [useKnowledge] 收到向量化进度信号:', { kbId, docId, completed, total });

          // 使用防抖机制，避免过多信号处理导致UI卡死
          const progressKey = `${kbId}-${docId}`;

          // 清除之前的定时器
          if (progressDebounceMap.has(progressKey)) {
            const timeoutId = progressDebounceMap.get(progressKey);
            if (timeoutId) {
              clearTimeout(timeoutId);
            }
          }

          // 设置新的防抖定时器
          const timeoutId = setTimeout(() => {
            // 通知所有监听器
            vectorizationProgressListeners.value.forEach((listener) => {
              try {
                listener(kbId, docId, completed, total);
              } catch (error) {
                console.error('❌ [useKnowledge] 向量化进度监听器执行失败:', error);
              }
            });

            // 清理防抖映射
            progressDebounceMap.delete(progressKey);
          }, 100); // 100ms防抖延迟

          progressDebounceMap.set(progressKey, timeoutId);
        },
      );
    }

    // 监听单个chunk向量化完成信号
    if (window.knowledgeApi.chunkVectorized) {
      window.knowledgeApi.chunkVectorized.connect(
        (kbId: string, docId: string, chunkIndex: number, totalChunks: number) => {
          console.log('📡 [useKnowledge] 收到chunk向量化完成信号:', {
            kbId,
            docId,
            chunkIndex,
            totalChunks,
          });
          // 可以在这里触发事件或更新UI
        },
      );
    }

    signalListenersSetup = true;
    console.log('📡 [useKnowledge] 信号监听器设置完成');
  } else {
    console.warn('⚠️ [useKnowledge] knowledgeApi.documentVectorized 不可用');
  }
};

// 添加文档向量化完成监听器
const onDocumentVectorized = (callback: (docId: string, chunkCount: number) => void) => {
  documentVectorizedListeners.value.push(callback);

  // 返回取消监听的函数
  return () => {
    const index = documentVectorizedListeners.value.indexOf(callback);
    if (index > -1) {
      documentVectorizedListeners.value.splice(index, 1);
    }
  };
};

// 添加向量化进度监听器
const onVectorizationProgress = (
  callback: (kbId: string, docId: string, completed: number, total: number) => void,
) => {
  vectorizationProgressListeners.value.push(callback);

  // 返回取消监听的函数
  return () => {
    const index = vectorizationProgressListeners.value.indexOf(callback);
    if (index > -1) {
      vectorizationProgressListeners.value.splice(index, 1);
    }
  };
};

// Helper function to call Qt KnowledgeApi
const callKnowledgeApi = <T>(method: string, ...args: unknown[]): Promise<T> => {
  return new Promise((resolve, reject) => {
    if (!window.knowledgeApi || typeof window.knowledgeApi[method] !== 'function') {
      reject(new Error(`KnowledgeApi method ${method} not available`));
      return;
    }

    try {
      const result = window.knowledgeApi[method](...args);

      // Qt WebChannel 方法可能返回 Promise，也可能直接返回值
      const handleResult = (data: unknown) => {
        if (typeof data === 'string') {
          try {
            const parsed = JSON.parse(data);
            if (parsed.success) {
              // 对于 getKnowledgeBaseStats，返回 data 字段
              // 对于其他方法，如果有 data 字段则返回 data，否则返回整个对象
              if (method === 'getKnowledgeBaseStats' && parsed.data) {
                resolve(parsed.data);
              } else {
                resolve(parsed.data || parsed);
              }
            } else {
              reject(new Error(parsed.message || `${method} failed`));
            }
          } catch (parseError) {
            reject(new Error(`JSON parse error: ${parseError}`));
          }
        } else {
          resolve(data as T);
        }
      };

      // 检查是否是 Promise
      if (result && typeof result.then === 'function') {
        result
          .then((data: unknown) => {
            handleResult(data);
          })
          .catch((error: Error) => {
            reject(error);
          });
      } else {
        // 直接返回值
        handleResult(result);
      }
    } catch (error) {
      reject(error instanceof Error ? error : new Error(String(error)));
    }
  });
};

export function useKnowledge() {
  // 计算属性
  const isLoading = computed(() => knowledgeState.value.operationState.isLoading);
  const isProcessing = computed(() => knowledgeState.value.operationState.isProcessing);
  const hasError = computed(() => !!knowledgeState.value.operationState.error);
  const currentError = computed(() => knowledgeState.value.operationState.error);
  const stats = computed(() => knowledgeState.value.stats);

  /**
   * 触发知识库事件
   */
  const emitEvent = (event: KnowledgeEvent, data?: unknown, error?: Error) => {
    const payload: KnowledgeEventPayload = {
      event,
      timestamp: Date.now(),
      data,
      error,
      metadata: {
        operationId: `op_${Date.now()}`,
        userId: 'current_user', // 可以从认证状态获取
      },
    };

    // 添加到事件历史
    knowledgeState.value.eventHistory.unshift(payload);

    // 限制事件历史数量
    if (knowledgeState.value.eventHistory.length > 100) {
      knowledgeState.value.eventHistory = knowledgeState.value.eventHistory.slice(0, 100);
    }

    // 触发事件监听器
    const listeners = eventListeners.value.get(event) || [];
    listeners.forEach((listener) => {
      try {
        listener(payload);
      } catch (err) {
        console.error(`[useKnowledge] 事件监听器执行失败:`, err);
      }
    });

    console.log(`📢 [useKnowledge] 事件触发:`, event, data);
  };

  /**
   * 监听知识库事件
   */
  const on = (event: KnowledgeEvent, listener: (payload: KnowledgeEventPayload) => void) => {
    if (!eventListeners.value.has(event)) {
      eventListeners.value.set(event, []);
    }
    eventListeners.value.get(event).push(listener);

    // 返回取消监听的函数
    return () => {
      const listeners = eventListeners.value.get(event);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  };

  /**
   * 移除事件监听器
   */
  const off = (event: KnowledgeEvent, listener?: (payload: KnowledgeEventPayload) => void) => {
    if (!listener) {
      eventListeners.value.delete(event);
    } else {
      const listeners = eventListeners.value.get(event);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    }
  };

  /**
   * 设置操作状态
   */
  const setOperationState = (updates: Partial<KnowledgeOperationState>) => {
    Object.assign(knowledgeState.value.operationState, updates);
  };

  /**
   * 清除错误状态
   */
  const clearError = () => {
    setOperationState({ error: null });
  };

  /**
   * 创建知识库
   */
  const createKB = async (
    name: string,
    description: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _settings: Record<string, unknown> = {},
  ): Promise<number> => {
    setOperationState({ isProcessing: true, lastOperation: 'createKnowledgeBase' });

    try {
      console.log('🔄 [useKnowledge] 开始创建知识库:', { name, description });

      interface CreateKBResult {
        id: string | number;
        name: string;
        description: string;
        user_id?: string;
        settings?: string;
        created_at?: number | string;
        updated_at?: number | string;
      }

      const result = await callKnowledgeApi<CreateKBResult>(
        'createKnowledgeBase',
        name,
        description,
        'default_user',
      );

      console.log('📋 [useKnowledge] 创建知识库返回结果:', result);

      // 确保id是数字类型
      const id = typeof result.id === 'string' ? parseInt(result.id, 10) : result.id;

      emitEvent('knowledge_base_created', { id, name, description });

      Notify.create({
        type: 'positive',
        message: $t('src.composeables.useKnowledge.knowledgeBaseCreated', { name }),
        timeout: 2000,
      });

      return id;
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.createKnowledgeBaseFailed'));
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isProcessing: false });
    }
  };

  /**
   * 更新知识库
   */
  const updateKB = async (
    id: number,
    name: string,
    description: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _settings: Record<string, unknown> = {},
  ): Promise<void> => {
    setOperationState({ isProcessing: true, lastOperation: 'updateKnowledgeBase' });

    try {
      await callKnowledgeApi('updateKnowledgeBase', id.toString(), name, description);

      emitEvent('knowledge_base_updated', { id, name, description });

      Notify.create({
        type: 'positive',
        message: $t('src.composeables.useKnowledge.knowledgeBaseUpdated', { name }),
        timeout: 2000,
      });
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.updateKnowledgeBaseFailed'));
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isProcessing: false });
    }
  };

  /**
   * 删除知识库
   */
  const deleteKB = async (id: number): Promise<void> => {
    setOperationState({ isProcessing: true, lastOperation: 'deleteKnowledgeBase' });

    try {
      await callKnowledgeApi('deleteKnowledgeBase', id.toString());

      emitEvent('knowledge_base_deleted', { id });

      Notify.create({
        type: 'positive',
        message: $t('src.composeables.useKnowledge.knowledgeBaseDeleted'),
        timeout: 2000,
      });
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.deleteKnowledgeBaseFailed'));
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isProcessing: false });
    }
  };

  /**
   * 获取知识库详情
   */
  const getKnowledgeBase = async (id: number): Promise<KnowledgeBase> => {
    setOperationState({ isLoading: true, lastOperation: 'getKnowledgeBase' });

    try {
      interface RawKnowledgeBaseDetail {
        id: string | number;
        name: string;
        description: string;
        user_id: string;
        settings?: string;
        document_count?: number;
        created_at: number | string;
        updated_at: number | string;
      }

      const result = await callKnowledgeApi<RawKnowledgeBaseDetail>(
        'getKnowledgeBase',
        id.toString(),
      );

      console.log('🔍 [useKnowledge] getKnowledgeBase原始返回:', result);

      if (!result) {
        throw new Error($t('src.composeables.useKnowledge.getKnowledgeBaseEmptyData'));
      }

      // 转换数据格式
      const kb: KnowledgeBase = {
        ...result,
        id: typeof result.id === 'string' ? parseInt(result.id, 10) : result.id,
        document_count: result.document_count || 0,
        created_at:
          typeof result.created_at === 'number'
            ? new Date(result.created_at).toISOString()
            : result.created_at,
        updated_at:
          typeof result.updated_at === 'number'
            ? new Date(result.updated_at).toISOString()
            : result.updated_at,
        settings: result.settings || '{}',
      };

      emitEvent('knowledge_context_updated', { knowledgeBase: kb });

      return kb;
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.loadKnowledgeBaseDetailFailed'));
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isLoading: false });
    }
  };

  /**
   * 文档管理 - 创建知识库文档
   * @param knowledgeBaseId 知识库ID
   * @param title 文档标题
   * @param content 文档内容
   * @param sourceType 文档来源类型：'created' | 'upload' | 'inkcop_editor' | 'import' | 'template' | 'other'
   * @param filePath 文件路径（可选）
   * @param fileType 文件类型（可选）
   * @param _processContent 是否处理内容（可选）
   */
  const createKnowledgeDoc = async (
    knowledgeBaseId: number,
    title: string,
    content: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    sourceType:
      | 'created'
      | 'upload'
      | 'inkcop_editor'
      | 'import'
      | 'template'
      | 'other' = 'created',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    filePath: string = '',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    fileType: string = '',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _processContent: boolean = true,
    documentType: string = 'markdown', // 新增documentType参数
  ): Promise<{ data: { id: string; processing?: boolean } } | number> => {
    setOperationState({ isProcessing: true, lastOperation: 'createKnowledgeDoc' });

    try {
      // 创建知识库文档（ObjectBox）
      // ObjectBox会自动处理所有数据存储，传递文档类型用于智能切割
      const result = await callKnowledgeApi<{ id: string; processing?: boolean }>(
        'addDocumentToKnowledgeBase',
        knowledgeBaseId.toString(),
        title,
        content,
        documentType, // 传递文档类型而不是sourceType
      );

      // 检查是否有处理状态信息
      if (result && typeof result === 'object' && 'processing' in result) {
        // 返回包含处理状态的对象
        return { data: result };
      }

      const knowledgeDocumentId = parseInt(result.id, 10);

      console.log('✅ [useKnowledge] 创建知识库文档成功，ID:', knowledgeDocumentId);

      Notify.create({
        type: 'positive',
        message: $t('src.composeables.useKnowledge.knowledgeDocumentAdded', { title }),
        timeout: 3000,
      });

      emitEvent('knowledge_document_added', {
        knowledgeBaseId,
        documentId: knowledgeDocumentId,
        title,
      });

      return knowledgeDocumentId;
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.createKnowledgeDocFailed'));
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isProcessing: false });
    }
  };

  /**
   * 文档管理 - 更新知识库文档
   */
  const updateKnowledgeDoc = async (
    knowledgeDocumentId: number,
    title: string,
    content: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _sourceType: string = 'upload',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _filePath: string = '',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _fileType: string = '',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _updateVector: boolean = true,
    documentType: string = 'markdown', // 新增documentType参数
  ): Promise<{ data: { id: string; processing?: boolean } } | void> => {
    setOperationState({ isProcessing: true, lastOperation: 'updateKnowledgeDoc' });

    try {
      // 更新知识库文档（ObjectBox）
      // ObjectBox会自动处理所有数据更新，传递文档类型用于智能切割
      const result = await callKnowledgeApi<{ id?: string; processing?: boolean }>(
        'updateDocument',
        knowledgeDocumentId.toString(),
        title,
        content,
        documentType,
      );

      // 检查是否有处理状态信息
      if (result && typeof result === 'object' && 'processing' in result) {
        // 返回包含处理状态的对象
        return { data: { id: knowledgeDocumentId.toString(), processing: result.processing } };
      }

      Notify.create({
        type: 'positive',
        message: $t('src.composeables.useKnowledge.knowledgeDocumentUpdated'),
      });

      emitEvent('knowledge_document_updated', { documentId: knowledgeDocumentId, title });

      console.log('✅ [useKnowledge] 知识库文档已更新');
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.updateKnowledgeDocFailed'));
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isProcessing: false });
    }
  };

  /**
   * 文档管理 - 删除知识库文档
   */
  const deleteKnowledgeDoc = async (
    knowledgeDocumentId: number,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _cleanupVector: boolean = true,
  ): Promise<void> => {
    setOperationState({ isProcessing: true, lastOperation: 'deleteKnowledgeDoc' });

    try {
      console.log('🗑️ [useKnowledge] 开始删除知识库文档:', knowledgeDocumentId);

      // 第一步：删除知识库文档（ObjectBox）
      // ObjectBox会自动处理所有相关数据的清理
      await callKnowledgeApi('deleteDocument', knowledgeDocumentId.toString());
      console.log('✅ [useKnowledge] 知识库文档删除成功');

      // 第二步：清除关联的原始文档的知识库字段
      try {
        const { useSqlite } = await import('./useSqlite');
        const sqlite = useSqlite();

        // 调用后端API清除知识库文档关联
        const clearResult = await sqlite.clearDocumentKnowledgeAssociation(knowledgeDocumentId);

        if (clearResult.success) {
          console.log('✅ [useKnowledge] 知识库文档关联清除成功:', {
            clearedCount: clearResult.cleared_count,
            clearedDocuments: clearResult.cleared_documents,
            message: clearResult.message,
          });
        } else {
          console.error('❌ [useKnowledge] 清除知识库文档关联失败:', clearResult.message);
        }
      } catch (clearError) {
        console.error('❌ [useKnowledge] 清除关联文档失败:', clearError);
        // 不阻断主流程，只记录错误
      }

      Notify.create({
        type: 'positive',
        message: $t('src.composeables.useKnowledge.knowledgeDocumentDeleted'),
      });

      emitEvent('knowledge_document_removed', { documentId: knowledgeDocumentId });

      console.log('✅ [useKnowledge] 知识库文档删除完成');
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.deleteKnowledgeDocFailed'));
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isProcessing: false });
    }
  };

  /**
   * 从文档库添加文档到知识库
   */
  const addDocumentToKB = async (
    knowledgeBaseId: number,
    title: string,
    content: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _processContent: boolean = true,
  ): Promise<number> => {
    setOperationState({ isProcessing: true, lastOperation: 'addDocumentToKB' });

    try {
      // 添加文档到知识库（ObjectBox）
      // ObjectBox会自动处理所有数据存储
      const result = await callKnowledgeApi<{ id: string }>(
        'addDocumentToKnowledgeBase',
        knowledgeBaseId.toString(),
        title,
        content,
        'inkcop_editor',
      );

      const knowledgeDocumentId = parseInt(result.id, 10);

      console.log('✅ [useKnowledge] 文档已添加到知识库，知识库文档ID:', knowledgeDocumentId);

      emitEvent('knowledge_document_added', {
        knowledgeBaseId,
        documentId: knowledgeDocumentId,
        title,
      });

      return knowledgeDocumentId;
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.addDocumentToKBFailed'));
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isProcessing: false });
    }
  };

  /**
   * 从知识库移除文档
   */
  const removeDocumentFromKB = async (
    knowledgeDocumentId: number,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _cleanupVector: boolean = true,
  ): Promise<void> => {
    setOperationState({ isProcessing: true, lastOperation: 'removeDocumentFromKB' });

    try {
      // 从知识库移除文档（ObjectBox）
      // ObjectBox会自动处理所有相关数据的清理
      await callKnowledgeApi('deleteDocument', knowledgeDocumentId.toString());

      Notify.create({
        type: 'positive',
        message: $t('src.composeables.useKnowledge.documentRemovedFromKB'),
      });

      emitEvent('knowledge_document_removed', { documentId: knowledgeDocumentId });

      console.log('✅ [useKnowledge] 文档已从知识库移除');
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.removeDocumentFromKBFailed'));
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isProcessing: false });
    }
  };

  /**
   * 搜索知识库
   */
  const searchKnowledge = async (
    query: string,
    knowledgeBaseId?: number,
    limit: number = 10,
    minScore: number = 0.1,
  ): Promise<KnowledgeSearchResult[]> => {
    setOperationState({ isLoading: true, lastOperation: 'searchKnowledge' });

    try {
      console.log(`🔍 [useKnowledge] 开始搜索知识库:`, { query, knowledgeBaseId, limit, minScore });

      let qtResults: QtSearchResult[] = [];

      if (knowledgeBaseId) {
        // 搜索指定知识库
        qtResults = await searchKnowledgeBase(knowledgeBaseId, query, limit, minScore);
      } else {
        // 搜索所有知识库
        qtResults = await searchAllKnowledgeBases(query, limit, minScore);
      }

      const results: KnowledgeSearchResult[] = qtResults
        .filter((result) => {
          return result.similarity >= minScore;
        }) // 应用最小相似度阈值
        .map((qtResult) => ({
          id: qtResult.chunk_id,
          memory: qtResult.content,
          score: qtResult.similarity,
          knowledge_base_id: qtResult.knowledge_base_id || knowledgeBaseId?.toString() || '',
          knowledge_document_id: qtResult.document_id,
          document_title: qtResult.document_title,
          chunk_index: undefined, // Qt结果中没有chunk_index
          chunk_content: qtResult.content,
          metadata: {
            knowledge_base_id: qtResult.knowledge_base_id || knowledgeBaseId?.toString(),
            knowledge_document_id: qtResult.document_id,
            document_title: qtResult.document_title,
            chunk_content: qtResult.content,
            chunk_type: 'text',
            source: 'objectbox_search',
            knowledge_base_name: qtResult.knowledge_base_name,
          },
        }));

      // console.log(`✅ [useKnowledge] 搜索完成，找到${results.length}个相关结果`);

      emitEvent('knowledge_search_performed', {
        query,
        knowledgeBaseId,
        resultsCount: results.length,
        minScore,
      });
      return results;
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.searchKnowledgeFailed'));
      console.error('❌ [useKnowledge] 搜索失败:', err);
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);

      Notify.create({
        type: 'negative',
        message: $t('src.composeables.useKnowledge.searchFailed', { error: err.message }),
        timeout: 5000,
      });

      throw err;
    } finally {
      setOperationState({ isLoading: false });
    }
  };

  /**
   * 批量搜索多个知识库
   */
  const searchMultipleKnowledgeBases = async (
    query: string,
    knowledgeBaseIds: number[],
    limit: number = 10,
    minScore: number = 0.1,
  ): Promise<{ [kbId: number]: KnowledgeSearchResult[] }> => {
    setOperationState({ isLoading: true, lastOperation: 'searchMultipleKnowledgeBases' });

    try {
      console.log(`🔍 [useKnowledge] 开始批量搜索知识库:`, {
        query,
        knowledgeBaseIds,
        limit,
        minScore,
      });

      const results: { [kbId: number]: KnowledgeSearchResult[] } = {};

      // 并行搜索所有知识库
      const searchPromises = knowledgeBaseIds.map(async (kbId) => {
        try {
          const kbResults = await searchKnowledge(query, kbId, limit, minScore);
          results[kbId] = kbResults;
          console.log(`✅ [useKnowledge] 知识库 ${kbId} 搜索完成，找到 ${kbResults.length} 个结果`);
        } catch (error) {
          console.warn(`⚠️ [useKnowledge] 搜索知识库 ${kbId} 失败:`, error);
          results[kbId] = [];
        }
      });

      await Promise.all(searchPromises);

      const totalResults = Object.values(results).reduce((sum, arr) => sum + arr.length, 0);

      console.log(`🎉 [useKnowledge] 批量搜索完成，总共找到 ${totalResults} 个结果`);

      emitEvent('knowledge_search_performed', {
        query,
        knowledgeBaseIds,
        totalResults,
        minScore,
      });

      // 显示批量搜索结果通知
      if (totalResults > 0) {
        Notify.create({
          type: 'positive',
          message: $t('src.composeables.useKnowledge.batchSearchCompleted', {
            knowledgeBaseIdsLength: knowledgeBaseIds.length,
            totalResults,
          }),
          timeout: 3000,
        });
      } else {
        Notify.create({
          type: 'info',
          message: $t('src.composeables.useKnowledge.batchSearchNoResults', {
            knowledgeBaseIdsLength: knowledgeBaseIds.length,
          }),
          timeout: 3000,
        });
      }

      return results;
    } catch (error) {
      const err =
        error instanceof Error
          ? error
          : new Error($t('src.composeables.useKnowledge.batchSearchFailed'));
      console.error('❌ [useKnowledge] 批量搜索失败:', err);
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);

      Notify.create({
        type: 'negative',
        message: $t('src.composeables.useKnowledge.batchSearchFailedMessage', {
          error: err.message,
        }),
        timeout: 5000,
      });

      throw err;
    } finally {
      setOperationState({ isLoading: false });
    }
  };

  /**
   * 获取知识库统计信息
   */
  const getKnowledgeBaseStats = async (knowledgeBaseId: number): Promise<KnowledgeBaseStats> => {
    try {
      console.log('📊 [useKnowledge] 开始获取知识库统计信息...', knowledgeBaseId);

      // 直接调用API，期望返回统计数据对象
      const basicStats = await callKnowledgeApi<{
        knowledge_base_id: string;
        document_count: number;
        total_characters: number;
        total_size: number;
        total_chunks: number;
        vectorized_chunks: number;
        total_chunks_count: number;
        average_chunks_per_doc: number;
      }>('getKnowledgeBaseStats', knowledgeBaseId.toString());

      console.log('📋 [useKnowledge] getKnowledgeBaseStats解析结果:', basicStats);

      // callKnowledgeApi 已经处理了 success 检查，直接使用返回的数据
      const stats = {
        knowledge_base_id: knowledgeBaseId,
        document_count: basicStats.document_count || 0,
        total_documents: basicStats.document_count || 0, // 保持兼容性
        total_characters: basicStats.total_characters || 0,
        total_size: basicStats.total_size || 0,
        total_chunks: basicStats.total_chunks || 0,
        vectorized_chunks: basicStats.vectorized_chunks || 0,
        total_chunks_count: basicStats.total_chunks_count || 0,
        average_chunks_per_doc: basicStats.average_chunks_per_doc || 0,
        knowledge_base_name: '',
      };

      console.log('✅ [useKnowledge] 知识库统计信息获取成功:', stats);
      return stats;
    } catch (error) {
      console.error('❌ [useKnowledge] 获取知识库统计失败:', error);
      return {
        knowledge_base_id: knowledgeBaseId,
        document_count: 0,
        total_documents: 0,
        total_characters: 0,
        total_size: 0,
        total_chunks: 0,
        vectorized_chunks: 0,
        total_chunks_count: 0,
        average_chunks_per_doc: 0,
        knowledge_base_name: '',
        error:
          error instanceof Error
            ? error.message
            : $t('src.composeables.useKnowledge.getKnowledgeBaseStatsFailed'),
      };
    }
  };

  /**
   * 在指定知识库中搜索
   */
  const searchKnowledgeBase = async (
    kbId: number,
    query: string,
    limit = 10,
    minScore = 0.1,
  ): Promise<QtSearchResult[]> => {
    try {
      console.log('🔍 [useKnowledge] 开始搜索知识库...', { kbId, query, limit, minScore });

      // 添加搜索超时处理
      const searchPromise = callKnowledgeApi<{
        success: boolean;
        results?: QtSearchResult[];
        total_results?: number;
        message?: string;
      }>('searchKnowledgeBase', kbId.toString(), query, limit.toString(), minScore.toString());

      // 设置30秒超时
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('搜索超时，请检查网络连接或向量化配置'));
        }, 30000);
      });

      const result = await Promise.race([searchPromise, timeoutPromise]);

      console.log('📋 [useKnowledge] searchKnowledgeBase解析结果:', result);

      if (result.success && result.results) {
        console.log(`✅ [useKnowledge] 知识库搜索成功，找到 ${result.results.length} 个结果`);
        return result.results;
      } else {
        console.warn('❌ [useKnowledge] 搜索失败:', result.message);
        return [];
      }
    } catch (error) {
      console.error('❌ [useKnowledge] 搜索知识库时出错:', error);

      // 如果是超时错误，给出更友好的提示
      if (error instanceof Error && error.message.includes('超时')) {
        Notify.create({
          type: 'warning',
          message: $t('src.composeables.useKnowledge.searchTimeout'),
          caption: $t('src.composeables.useKnowledge.searchTimeoutCaption'),
          timeout: 5000,
        });
      }

      return [];
    }
  };

  /**
   * 在所有知识库中搜索
   */
  const searchAllKnowledgeBases = async (
    query: string,
    limit = 10,
    minScore = 0.1,
  ): Promise<QtSearchResult[]> => {
    try {
      console.log('🔍 [useKnowledge] 开始全局搜索...', { query, limit, minScore });

      const result = await callKnowledgeApi<{
        success: boolean;
        results?: QtSearchResult[];
        total_results?: number;
        message?: string;
      }>('searchAllKnowledgeBases', query, limit.toString(), minScore.toString());

      console.log('📋 [useKnowledge] searchAllKnowledgeBases解析结果:', result);

      if (result.success && result.results) {
        console.log(`✅ [useKnowledge] 全局搜索成功，找到 ${result.results.length} 个结果`);
        return result.results;
      } else {
        console.warn('❌ [useKnowledge] 全局搜索失败:', result.message);
        return [];
      }
    } catch (error) {
      console.error('❌ [useKnowledge] 全局搜索时出错:', error);
      return [];
    }
  };

  /**
   * 生成文本向量
   */
  const generateEmbedding = async (text: string): Promise<number[] | null> => {
    try {
      console.log('🔤 [useKnowledge] 开始生成向量...');

      const result = await callKnowledgeApi<{
        success: boolean;
        embedding?: number[];
        dimension?: number;
        message?: string;
      }>('generateEmbedding', text);

      console.log('📋 [useKnowledge] generateEmbedding解析结果:', result);

      if (result.success && result.embedding) {
        Notify.create({
          type: 'positive',
          message: $t('src.composeables.useKnowledge.embeddingGenerated', {
            dimension: result.dimension,
          }),
          timeout: 2000,
        });
        return result.embedding;
      } else {
        Notify.create({
          type: 'negative',
          message: $t('src.composeables.useKnowledge.embeddingGeneratedFailed', {
            error: result.message,
          }),
          timeout: 3000,
        });
        return null;
      }
    } catch (error) {
      console.error('❌ [useKnowledge] 生成向量时出错:', error);
      Notify.create({
        type: 'negative',
        message: $t('src.composeables.useKnowledge.embeddingGeneratedError', { error }),
        timeout: 3000,
      });
      return null;
    }
  };

  /**
   * 更新文档片段向量
   */
  const updateChunkEmbeddings = async (documentId: number): Promise<void> => {
    try {
      console.log('🔄 [useKnowledge] 开始更新文档片段向量...');

      const result = await callKnowledgeApi<{
        success: boolean;
        updated_chunks?: number;
        message?: string;
      }>('updateChunkEmbeddings', documentId.toString());

      console.log('📋 [useKnowledge] updateChunkEmbeddings解析结果:', result);

      if (result.success) {
        Notify.create({
          type: 'positive',
          message: $t('src.composeables.useKnowledge.chunkVectorUpdated', {
            message: result.message,
          }),
          caption: $t('src.composeables.useKnowledge.chunkVectorUpdatedCaption', {
            updated_chunks: result.updated_chunks || 0,
          }),
          timeout: 3000,
        });
      } else {
        Notify.create({
          type: 'negative',
          message: $t('src.composeables.useKnowledge.chunkVectorUpdatedFailed', {
            error: result.message,
          }),
          timeout: 3000,
        });
      }
    } catch (error) {
      console.error('❌ [useKnowledge] 更新片段向量时出错:', error);
      Notify.create({
        type: 'negative',
        message: $t('src.composeables.useKnowledge.chunkVectorUpdatedError', { error }),
        timeout: 3000,
      });
    }
  };

  /**
   * 测试embedding API
   */
  const testEmbeddingApi = async (
    text: string,
  ): Promise<{
    success: boolean;
    api_available?: boolean;
    api_config?: string;
    embedding_generated?: boolean;
    embedding_dimension?: number;
    embedding_preview?: number[];
    message?: string;
    error?: string;
  }> => {
    try {
      console.log('🧪 [useKnowledge] 测试embedding API...');

      const result = await callKnowledgeApi<{
        success: boolean;
        api_available?: boolean;
        api_config?: string;
        embedding_generated?: boolean;
        embedding_dimension?: number;
        embedding_preview?: number[];
        message?: string;
        error?: string;
      }>('testEmbeddingApi', text);

      console.log('📋 [useKnowledge] testEmbeddingApi结果:', result);

      if (result.success) {
        Notify.create({
          type: 'positive',
          message: $t('src.composeables.useKnowledge.embeddingApiTestSuccess'),
          caption: $t('src.composeables.useKnowledge.embeddingApiTestSuccessCaption', {
            dimension: result.embedding_dimension || 'N/A',
          }),
          timeout: 3000,
        });
      } else {
        Notify.create({
          type: 'negative',
          message: $t('src.composeables.useKnowledge.embeddingApiTestFailed', {
            error: result.message,
          }),
          timeout: 3000,
        });
      }

      return result;
    } catch (error) {
      console.error('❌ [useKnowledge] 测试embedding API时出错:', error);
      Notify.create({
        type: 'negative',
        message: $t('src.composeables.useKnowledge.embeddingApiTestError'),
        timeout: 3000,
      });
      return { success: false, error: error.message };
    }
  };

  /**
   * 测试多重语义向量化
   */
  const testMultiSemanticEmbedding = async (
    text: string,
  ): Promise<{
    success: boolean;
    api_available?: boolean;
    embedding_generated?: boolean;
    embedding_dimension?: number;
    processing_strategy?: string;
    chunk_count?: number;
    chunks_processed?: number;
    embedding_preview?: number[];
    message?: string;
    error?: string;
  }> => {
    try {
      console.log('🧪 [useKnowledge] 测试多重语义向量化...');

      const result = await callKnowledgeApi<{
        success: boolean;
        api_available?: boolean;
        embedding_generated?: boolean;
        embedding_dimension?: number;
        processing_strategy?: string;
        chunk_count?: number;
        chunks_processed?: number;
        embedding_preview?: number[];
        message?: string;
        error?: string;
      }>('testMultiSemanticEmbedding', text);

      console.log('📋 [useKnowledge] testMultiSemanticEmbedding结果:', result);

      if (result.success) {
        Notify.create({
          type: 'positive',
          message: $t('src.composeables.useKnowledge.multiSemanticEmbeddingTestSuccess'),
          caption: $t('src.composeables.useKnowledge.multiSemanticEmbeddingTestSuccessCaption', {
            processing_strategy: result.processing_strategy,
            embedding_dimension: result.embedding_dimension,
          }),
          timeout: 3000,
        });
      } else {
        Notify.create({
          type: 'negative',
          message: $t('src.composeables.useKnowledge.multiSemanticEmbeddingTestFailed', {
            error: result.message,
          }),
          timeout: 3000,
        });
      }

      return result;
    } catch (error) {
      console.error('❌ [useKnowledge] 测试多重语义向量化时出错:', error);
      Notify.create({
        type: 'negative',
        message: $t('src.composeables.useKnowledge.multiSemanticEmbeddingTestError'),
        timeout: 3000,
      });
      return { success: false, error: error.message };
    }
  };

  /**
   * 重新生成文档片段
   */
  const regenerateDocumentChunks = async (documentId: number): Promise<void> => {
    try {
      console.log('🔄 [useKnowledge] 开始重新生成文档片段...');

      const result = await callKnowledgeApi<{
        success: boolean;
        message?: string;
        old_chunks?: number;
        new_chunks?: number;
      }>('regenerateDocumentChunks', documentId.toString());

      console.log('📋 [useKnowledge] regenerateDocumentChunks解析结果:', result);

      if (result.success) {
        Notify.create({
          type: 'positive',
          message: $t('src.composeables.useKnowledge.documentChunksRegenerated', {
            message: result.message,
          }),
          caption: $t('src.composeables.useKnowledge.documentChunksRegeneratedCaption', {
            old_chunks: result.old_chunks || 0,
            new_chunks: result.new_chunks || 0,
          }),
          timeout: 3000,
        });
      } else {
        Notify.create({
          type: 'negative',
          message: $t('src.composeables.useKnowledge.documentChunksRegenerationFailed', {
            error: result.message,
          }),
          timeout: 5000,
        });
      }
    } catch (error) {
      console.error('❌ [useKnowledge] 重新生成文档片段失败:', error);
      Notify.create({
        type: 'negative',
        message: $t('src.composeables.useKnowledge.documentChunksRegenerationFailed', {
          error: error instanceof Error ? error.message : String(error),
        }),
        timeout: 5000,
      });
    }
  };

  /**
   * 测试ObjectBox连接状态
   */
  const testObjectBoxConnection = async (): Promise<void> => {
    try {
      console.log('🔍 [useKnowledge] 开始测试ObjectBox连接...');

      const result = await callKnowledgeApi<{
        success: boolean;
        details?: string;
        message?: string;
      }>('testConnection');
      console.log('📋 [useKnowledge] testConnection解析结果:', result);

      if (result.success) {
        Notify.create({
          type: 'positive',
          message: $t('src.composeables.useKnowledge.objectBoxConnectionTestSuccess', {
            details: result.details,
          }),
          timeout: 3000,
        });
      } else {
        Notify.create({
          type: 'negative',
          message: $t('src.composeables.useKnowledge.objectBoxConnectionTestFailed', {
            error: result.message,
          }),
          caption: result.details,
          timeout: 5000,
        });
      }
    } catch (error) {
      console.error('❌ [useKnowledge] ObjectBox连接测试失败:', error);
      Notify.create({
        type: 'negative',
        message: $t('src.composeables.useKnowledge.objectBoxConnectionTestFailed', {
          error: error instanceof Error ? error.message : String(error),
        }),
        timeout: 5000,
      });
    }
  };

  /**
   * 获取事件历史
   */
  const getEventHistory = (eventType?: KnowledgeEvent, limit: number = 50) => {
    let history = knowledgeState.value.eventHistory;

    if (eventType) {
      history = history.filter((event) => event.event === eventType);
    }

    return history.slice(0, limit);
  };

  /**
   * 清空事件历史
   */
  const clearEventHistory = () => {
    knowledgeState.value.eventHistory = [];
  };

  /**
   * 创建知识库文档（带切割策略）
   */
  const createKnowledgeDocWithChunking = async (
    knowledgeBaseId: number,
    title: string,
    content: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    sourceType:
      | 'created'
      | 'upload'
      | 'inkcop_editor'
      | 'import'
      | 'template'
      | 'other' = 'created',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    filePath: string = '',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    fileType: string = '',
    chunkingMethod: string = 'byChunkSize',
    chunkingConfig: Record<string, unknown> = {},
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _processContent: boolean = true,
    documentType: string = 'markdown',
  ): Promise<{ data: { id: string; processing?: boolean } } | number> => {
    setOperationState({ isProcessing: true, lastOperation: 'createKnowledgeDocWithChunking' });

    try {
      // 1. 先创建文档（不进行向量化）
      const docResult = await callKnowledgeApi<{ id: string; processing?: boolean }>(
        'createKnowledgeDocumentOnly',
        knowledgeBaseId.toString(),
        title,
        content,
        documentType,
      );

      if (!docResult || !docResult.id) {
        throw new Error('文档创建失败');
      }

      console.log('📄 [useKnowledge] 文档创建成功，ID:', docResult.id);

      // 2. 使用前端切割功能进行文档切割
      const configJson = JSON.stringify(chunkingConfig);
      const chunks = await chunkDocumentWithMethod(content, chunkingMethod, configJson);

      if (!chunks || !Array.isArray(chunks)) {
        throw new Error('文档切割失败');
      }

      console.log('✂️ [useKnowledge] 文档切割完成，块数量:', chunks.length);

      // 3. 提交切割结果到后端进行向量化
      console.log('📤 [useKnowledge] 准备提交切割结果:', {
        documentId: docResult.id,
        chunkCount: chunks.length,
        firstChunkPreview: chunks[0]
          ? {
              pageContent: chunks[0].pageContent?.substring(0, 100) + '...',
              metadata: chunks[0].metadata,
            }
          : null,
      });

      const submitResult = await callKnowledgeApi<{ success: boolean; message?: string }>(
        'submitChunkingResults',
        docResult.id,
        chunks,
      );

      console.log('📤 [useKnowledge] 切割结果提交响应:', submitResult);

      if (!submitResult.success) {
        throw new Error(submitResult.message || '提交切割结果失败');
      }

      console.log('✅ [useKnowledge] 切割结果提交成功，向量化已启动');

      // 检查是否有处理状态信息
      if (docResult && typeof docResult === 'object' && 'processing' in docResult) {
        return { data: { ...docResult, processing: true } };
      }

      const knowledgeDocumentId = parseInt(docResult.id, 10);

      emitEvent('knowledge_document_added', {
        knowledgeBaseId,
        documentId: knowledgeDocumentId,
        title,
        chunkingMethod,
        chunkingConfig,
      });

      return knowledgeDocumentId;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('创建知识库文档失败');
      setOperationState({ error: err });
      emitEvent('knowledge_error_occurred', null, err);
      throw err;
    } finally {
      setOperationState({ isProcessing: false });
    }
  };

  /**
   * 获取可用的切割方法
   */
  const getAvailableChunkingMethods = async (): Promise<unknown> => {
    try {
      const result = await callKnowledgeApi<unknown>('getAvailableChunkingMethods');
      return result;
    } catch (error) {
      console.error('获取切割方法失败:', error);
      throw error;
    }
  };

  /**
   * 获取切割方法信息
   */
  const getChunkingMethodInfo = async (method: string): Promise<unknown> => {
    try {
      const result = await callKnowledgeApi<unknown>('getChunkingMethodInfo', method);
      return result;
    } catch (error) {
      console.error('获取切割方法信息失败:', error);
      throw error;
    }
  };

  /**
   * 验证切割配置
   */
  const validateChunkingConfig = async (method: string, configJson: string): Promise<unknown> => {
    try {
      const result = await callKnowledgeApi<unknown>('validateChunkingConfig', method, configJson);
      return result;
    } catch (error) {
      console.error('验证切割配置失败:', error);
      throw error;
    }
  };

  /**
   * 使用指定方法切割文档
   */
  const chunkDocumentWithMethod = async (
    text: string,
    method: string,
    configJson: string = '{}',
  ): Promise<unknown> => {
    try {
      // 解析配置
      const config = JSON.parse(configJson);

      // 导入切割工具
      const {
        splitMarkdownWithLangChain,
        splitTextWithRecursiveCharacter,
        splitLatexWithLangChain,
        splitDocumentSmart,
      } = await import('../utils/knowledgeBase');

      // 定义结果类型
      interface ChunkingResult {
        chunks: Array<{ pageContent: string; metadata: Record<string, unknown> }>;
        originalLength: number;
        chunkCount: number;
        summary: {
          averageChunkSize: number;
          minChunkSize: number;
          maxChunkSize: number;
        };
      }

      let result: ChunkingResult;

      // 根据方法选择对应的切割器
      switch (method) {
        case 'markdown':
          result = await splitMarkdownWithLangChain(text, config, false);
          break;
        case 'recursiveCharacter':
        case 'byChunkSize':
          result = await splitTextWithRecursiveCharacter(text, config, false);
          break;
        case 'latex':
          result = await splitLatexWithLangChain(text, config, false);
          break;
        case 'smart':
        case 'auto':
          result = await splitDocumentSmart(text, config, 'auto', false);
          break;
        default:
          // 默认使用递归字符切割
          result = await splitTextWithRecursiveCharacter(text, config, false);
          break;
      }

      // 返回原始格式，保持与 submitChunkingResults 期望的格式一致
      return result.chunks;
    } catch (error) {
      console.error('文档切割失败:', error);
      throw error;
    }
  };

  /**
   * 提交切割结果到Qt后端
   */
  const submitChunkingResults = async (
    documentId: number,
    chunks: Array<{ pageContent: string; metadata: Record<string, unknown> }>,
  ): Promise<{
    success: boolean;
    message?: string;
    chunks_saved?: number;
    chunks_total?: number;
  }> => {
    console.log('📤 [useKnowledge] 提交切割结果到Qt后端:', {
      documentId,
      chunkCount: chunks.length,
    });

    try {
      // 将数字ID转换为字符串传递给Qt后端
      const result = await callKnowledgeApi<{
        success: boolean;
        message?: string;
        chunks_saved?: number;
        chunks_total?: number;
      }>('submitChunkingResults', documentId.toString(), chunks);

      console.log('✅ [useKnowledge] 切割结果提交成功:', result);
      return result;
    } catch (error) {
      console.error('💥 [useKnowledge] 提交切割结果失败:', error);
      throw error;
    }
  };

  /**
   * 获取向量化进度
   */
  const getVectorizationProgress = async (
    documentId: number,
  ): Promise<{
    success: boolean;
    document_id?: string;
    pending_chunks?: number;
    is_processing?: boolean;
    message?: string;
  }> => {
    try {
      const result = await callKnowledgeApi<{
        success: boolean;
        document_id?: string;
        pending_chunks?: number;
        is_processing?: boolean;
        message?: string;
      }>('getVectorizationProgress', documentId.toString());

      return result;
    } catch (error) {
      console.error('💥 [useKnowledge] 获取向量化进度失败:', error);
      throw error;
    }
  };

  /**
   * 检查待向量化的chunks
   */
  const checkPendingVectorization = async (): Promise<{
    success: boolean;
    total_pending_chunks?: number;
    pending_documents?: Array<{ document_id: string; pending_chunks: number }>;
    has_pending?: boolean;
    message?: string;
  }> => {
    try {
      const result = await callKnowledgeApi<{
        success: boolean;
        total_pending_chunks?: number;
        pending_documents?: Array<{ document_id: string; pending_chunks: number }>;
        has_pending?: boolean;
        message?: string;
      }>('checkPendingVectorization');

      return result;
    } catch (error) {
      console.error('💥 [useKnowledge] 检查待向量化chunks失败:', error);
      throw error;
    }
  };

  /**
   * 创建知识库文档（仅文档，不处理向量化）
   */
  const createKnowledgeDocumentOnly = async (
    knowledgeBaseId: number,
    title: string,
    content: string,
    documentType: string = 'markdown',
  ): Promise<{ id: number; processing?: boolean; [key: string]: unknown }> => {
    console.log('🚀 [useKnowledge] 创建知识库文档（仅文档）:', {
      knowledgeBaseId,
      title,
      contentLength: content.length,
      documentType,
    });

    try {
      const result = await callKnowledgeApi<{
        id: number;
        processing?: boolean;
        [key: string]: unknown;
      }>('createKnowledgeDocumentOnly', knowledgeBaseId.toString(), title, content, documentType);

      console.log('✅ [useKnowledge] 知识库文档创建成功:', result);
      return result;
    } catch (error) {
      console.error('💥 [useKnowledge] 创建知识库文档失败:', error);
      throw error;
    }
  };

  // 初始化信号监听器
  setupSignalListeners();

  return {
    // 计算属性
    isLoading,
    isProcessing,
    hasError,
    currentError,
    stats,

    // 知识库基础操作
    createKB,
    updateKB,
    deleteKB,
    getKnowledgeBase,

    // 文档管理
    createKnowledgeDoc,
    createKnowledgeDocWithChunking,
    updateKnowledgeDoc,
    deleteKnowledgeDoc,
    addDocumentToKB,
    removeDocumentFromKB,

    // 搜索
    searchKnowledge,
    searchMultipleKnowledgeBases,

    // 统计和维护
    getKnowledgeBaseStats,

    // 连接测试
    testObjectBoxConnection,

    // 搜索功能
    searchKnowledgeBase,
    searchAllKnowledgeBases,

    // 向量功能
    generateEmbedding,
    updateChunkEmbeddings,

    // 测试功能
    testEmbeddingApi,
    testMultiSemanticEmbedding,

    // 维护工具
    regenerateDocumentChunks,

    // 切割功能
    getAvailableChunkingMethods,
    getChunkingMethodInfo,
    validateChunkingConfig,
    chunkDocumentWithMethod,

    // 新的分离式接口
    createKnowledgeDocumentOnly,
    submitChunkingResults,
    getVectorizationProgress,
    checkPendingVectorization,

    // 错误处理
    clearError,

    // 事件系统
    on,
    off,
    emitEvent,
    getEventHistory,
    clearEventHistory,

    // 信号监听系统
    onDocumentVectorized,
    onVectorizationProgress,
    setupSignalListeners,

    // 调试工具 - 暴露内部API用于调试
    callKnowledgeApi,
  };
}

// 默认导出单例实例
export const knowledgeManager = useKnowledge();
