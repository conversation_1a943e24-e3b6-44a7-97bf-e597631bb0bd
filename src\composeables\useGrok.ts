import type { Message } from 'src/types/qwen';
import { computed, type Ref } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import { useUiStore } from 'src/stores/ui';
import { DEFAULT_GROK_SETTINGS } from 'src/config/defaultSettings';
import { executeToolCall } from 'src/llm/tools/index';
import { PromptService, type ConversationRole } from 'src/services/promptService';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import { $t } from 'src/composables/useTrans';

interface GrokMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  tool_calls?: Array<{
    id: string;
    type: 'function';
    function: {
      name: string;
      arguments: string;
    };
  }>;
  tool_call_id?: string;
}

interface GrokTool {
  type: 'function';
  function: {
    name: string;
    description?: string;
    parameters: Record<string, unknown>;
  };
}

export const useGrok = () => {
  const { updateConversation } = useSqlite();
  const uiStore = useUiStore();

  const grokSettings = computed(() => {
    return uiStore.perferences?.llm?.grok || DEFAULT_GROK_SETTINGS;
  });

  const readSettings = () => {
    console.log('[useGrok] 设置已自动同步:', grokSettings.value);
  };

  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const { messages, messagesRef, conversation, tools, enableTools, abortController } = params;

    if (!grokSettings.value) return;

    console.log('[useGrok] 开始发送消息');
    console.log('[useGrok] 消息数量:', messages.length);
    console.log('[useGrok] 工具数量:', tools.length);
    console.log('[useGrok] 启用工具:', enableTools);

    loading.value = true;

    try {
      // 构建请求头
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${grokSettings.value.apiKey}`,
      };

      // 转换消息格式
      const grokMessages: GrokMessage[] = messages.map((msg) => ({
        role: msg.role,
        content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content),
        ...(msg.role === 'assistant' && 'tool_calls' in msg && msg.tool_calls
          ? { tool_calls: msg.tool_calls }
          : {}),
        ...(msg.role === 'tool' && 'tool_call_id' in msg ? { tool_call_id: msg.tool_call_id } : {}),
      }));

      // 构建请求体
      const requestBody: {
        model: string;
        messages: GrokMessage[];
        max_tokens?: number | null;
        temperature?: number;
        top_p?: number;
        frequency_penalty?: number;
        presence_penalty?: number;
        stream: boolean;
        tools?: GrokTool[];
        tool_choice?: 'auto' | 'none' | { type: 'function'; function: { name: string } };
      } = {
        model: grokSettings.value.model,
        messages: grokMessages,
        frequency_penalty: grokSettings.value.frequencyPenalty,
        presence_penalty: grokSettings.value.presencePenalty,
        stream: true,
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (grokSettings.value.temperature !== undefined && !isNaN(grokSettings.value.temperature)) {
        requestBody.temperature = grokSettings.value.temperature;
      }
      if (grokSettings.value.topP !== undefined && !isNaN(grokSettings.value.topP)) {
        requestBody.top_p = grokSettings.value.topP;
      }

      // 添加 max_tokens 如果设置了
      if (grokSettings.value.maxTokens) {
        requestBody.max_tokens = grokSettings.value.maxTokens;
      }

      // 如果有启用的工具，添加工具定义
      if (enableTools && tools.length > 0) {
        console.log(`🔧 [useGrok] 启用工具数: ${tools.length}`);
        console.log(`🔧 [useGrok] 工具列表: ${tools.map((t) => t.function.name).join(', ')}`);

        requestBody.tools = tools as GrokTool[];
        requestBody.tool_choice = 'auto';
      }

      const response = await fetch(`${grokSettings.value.baseUrl}/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Grok API error: ${errorData.error?.message || response.statusText}`);
      }

      if (!response.body) {
        throw new Error('ReadableStream not supported');
      }

      // 直接操作响应式消息数组，实现流式更新
      messagesRef.value.push({
        role: 'assistant',
        content: '',
      } as Message);
      const lastMessage = messagesRef.value[messagesRef.value.length - 1];

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      const toolCallsBuffer: Record<
        string,
        {
          id: string;
          type: 'function';
          function: {
            name: string;
            arguments: string;
          };
        }
      > = {};

      while (true) {
        // 检查是否被中断
        if (abortController?.signal.aborted) {
          void reader.cancel();
          throw new DOMException('Operation was aborted', 'AbortError');
        }

        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              const choice = parsed.choices?.[0];

              if (choice?.delta) {
                // 处理文本内容
                if (choice.delta.content) {
                  if (typeof lastMessage.content === 'string') {
                    lastMessage.content += choice.delta.content;
                  } else {
                    lastMessage.content = choice.delta.content;
                  }
                }

                // 处理工具调用
                if (choice.delta.tool_calls) {
                  for (const toolCallDelta of choice.delta.tool_calls) {
                    const index = toolCallDelta.index;
                    const bufferKey = `tool_${index}`;

                    if (!toolCallsBuffer[bufferKey]) {
                      toolCallsBuffer[bufferKey] = {
                        id: toolCallDelta.id || '',
                        type: 'function',
                        function: {
                          name: '',
                          arguments: '',
                        },
                      };
                    }

                    if (toolCallDelta.id) {
                      toolCallsBuffer[bufferKey].id = toolCallDelta.id;
                    }

                    if (toolCallDelta.function?.name) {
                      toolCallsBuffer[bufferKey].function.name = toolCallDelta.function.name;
                    }

                    if (toolCallDelta.function?.arguments) {
                      toolCallsBuffer[bufferKey].function.arguments +=
                        toolCallDelta.function.arguments;
                    }
                  }
                }
              }

              // 检查是否完成
              if (choice?.finish_reason) {
                console.log('[useGrok] 消息生成完成，原因:', choice.finish_reason);
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }

      // 处理完整的工具调用
      const toolCalls = Object.values(toolCallsBuffer);
      if (toolCalls.length > 0 && enableTools) {
        console.log('完整的工具调用列表:', toolCalls);

        // 验证工具调用的完整性
        toolCalls.forEach((toolCall, index) => {
          console.log(`工具调用 ${index + 1}:`, {
            id: toolCall.id,
            functionName: toolCall.function.name,
            argumentsLength: toolCall.function.arguments.length,
            arguments: toolCall.function.arguments,
          });
        });

        // 将工具调用添加到助手消息中
        if ('tool_calls' in lastMessage) {
          (lastMessage as Message & { tool_calls?: typeof toolCalls }).tool_calls = toolCalls;
        }

        // 执行工具调用并添加结果消息
        for (const toolCall of toolCalls) {
          try {
            // 验证工具调用完整性
            if (!toolCall.function.name) {
              throw new Error('工具函数名称为空');
            }

            // 解析工具参数
            let parameters = {};
            if (toolCall.function.arguments) {
              try {
                parameters = JSON.parse(toolCall.function.arguments);
                console.log(`解析工具参数成功 - ${toolCall.function.name}:`, parameters);
              } catch (parseError) {
                console.error(`工具参数解析失败 - ${toolCall.function.name}:`, {
                  rawArguments: toolCall.function.arguments,
                  error: parseError,
                });
                throw new Error(`工具参数格式错误: ${(parseError as Error).message}`);
              }
            }

            // 执行工具
            console.log(`开始执行工具: ${toolCall.function.name}`, parameters);
            const toolResult = await executeToolCall(toolCall.function.name, parameters);

            // 添加工具结果消息
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify(toolResult),
              tool_call_id: toolCall.id,
            } as Message);

            console.log(`工具执行成功 ${toolCall.function.name}:`, {
              parameters,
              result: toolResult,
            });
          } catch (error) {
            console.error(`工具执行失败 ${toolCall.function.name}:`, {
              error: (error as Error).message,
              functionName: toolCall.function.name,
              rawArguments: toolCall.function.arguments,
            });

            // 添加错误结果
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify({
                success: false,
                message: `工具执行失败: ${(error as Error).message}`,
                toolName: toolCall.function.name,
              }),
              tool_call_id: toolCall.id,
            } as Message);
          }
        }

        // 如果有工具调用，递归调用获取AI对工具结果的响应
        console.log('工具执行完成，开始递归调用获取AI响应...');
        console.log('当前消息数量:', messages.length);
        console.log(
          '最后几条消息:',
          messages.slice(-3).map((m) => ({
            role: m.role,
            content:
              typeof m.content === 'string'
                ? m.content.substring(0, 100)
                : JSON.stringify(m.content).substring(0, 100),
          })),
        );

        // 递归调用以获取最终响应
        console.log('🔄 [useGrok] 递归调用获取最终响应');
        const recursiveParams: SimplifiedLlmParams = {
          ...params,
          messages: messagesRef.value,
        };
        await sendMessage(recursiveParams, loading);
        return;
      }
      console.log('[useGrok] 消息发送完成');
    } catch (error) {
      console.error('[useGrok] Error sending message:', error);
      messagesRef.value.push({
        role: 'assistant',
        content: $t('src.composeables.useGrok.errorOccurred'),
      } as Message);
    } finally {
      loading.value = false;

      await updateConversation(
        conversation.id,
        conversation.title,
        JSON.stringify(messagesRef.value),
        conversation.prompt,
      );
    }
  };

  return {
    readSettings,
    grokSettings,
    sendMessage,
    // 导出prompt服务的方法
    getAvailableRoles: () => PromptService.getAvailableRoles(),
    getRoleSuggestions: (role: ConversationRole) => PromptService.getRoleSuggestions(role),
  };
};
