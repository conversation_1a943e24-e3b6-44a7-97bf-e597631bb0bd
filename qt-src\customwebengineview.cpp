#include "customwebengineview.h"
#include <QApplication>
#include <QMouseEvent>
#include <QWidget>
#include <QDebug>

CustomWebEngineView::CustomWebEngineView(QWidget *parent)
    : QWebEngineView(parent)
{
    // 方案1&2：设置WebEngine页面背景为透明，但保持WebView本身不透明
    // 这样只有页面背景透明，WebView控件本身仍然可以正常工作
    setupTransparentBackground();

    // 连接页面加载完成信号，确保透明背景在页面加载后仍然有效
    connect(this, &QWebEngineView::loadFinished, this, [this](bool success)
            {
        if (success) {
            setupTransparentBackground();
            qDebug() << "CustomWebEngineView: Reapplied transparent background after page load";
        } });

    qDebug() << "CustomWebEngineView: Initialized with transparent page background";
}

void CustomWebEngineView::setupTransparentBackground()
{
    // 关键：只设置页面背景透明，不设置整个WebView透明
    if (page())
    {
        page()->setBackgroundColor(Qt::transparent);
        qDebug() << "CustomWebEngineView: Page background set to transparent";
    }

    // 不设置WebView本身的透明属性，避免整个控件透明
    // setAttribute(Qt::WA_TranslucentBackground, false); // 确保WebView本身不透明

    // 设置样式，让WebView有透明背景但保持控件功能
    setStyleSheet("QWebEngineView { background-color: transparent; }");
}

void CustomWebEngineView::mousePressEvent(QMouseEvent *event)
{
    // Only handle window dragging for specific areas, not the entire web view
    // This allows normal web page interactions to work properly
    QWebEngineView::mousePressEvent(event);
}

void CustomWebEngineView::mouseMoveEvent(QMouseEvent *event)
{
    // Allow normal web page mouse move events
    QWebEngineView::mouseMoveEvent(event);
}

void CustomWebEngineView::mouseReleaseEvent(QMouseEvent *event)
{
    // Allow normal web page mouse release events
    QWebEngineView::mouseReleaseEvent(event);
}

CustomWebEngineView::~CustomWebEngineView()
{
    // 在删除 WebView 之前，先删除页面
    if (page())
    {
        page()->deleteLater();
    }
}