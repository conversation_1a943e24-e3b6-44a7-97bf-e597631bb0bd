import {
  draggable,
  dropTargetForElements,
  monitorForElements,
} from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import type { Document, Folder } from 'src/types/doc';
import { $t } from 'src/composables/useTrans';

// 扩展 HTMLElement 类型以支持拖拽清理函数
interface HTMLElementWithDragCleanup extends HTMLElement {
  __dragCleanup?: () => void;
  __dropCleanup?: () => void;
  __placeholderCleanup?: () => void;
}

export interface DragData {
  type: 'document' | 'folder';
  id: number;
  parentId: number | null;
  item: Document | Folder;
  index: number;
}

export interface DropData {
  type: 'folder' | 'document';
  id: number;
  item: Folder | Document;
  index: number;
  position: 'before' | 'after' | 'inside-first' | 'inside-last';
  parentId?: number | null; // 添加parentId字段，用于判断同级拖拽
}

// 放置区域类型
export type DropZoneType = 'top' | 'bottom' | 'inside-top' | 'inside-bottom' | 'inside-middle';

// 拖拽状态管理
export interface DragState {
  isDragging: boolean;
  dragData: DragData | null;
  activeDropZone: string | null;
  dragElementHeight?: number;
  currentPlaceholder: HTMLElement | null; // 全局当前占位符
}

// 拖拽事件处理
export interface DragEvents {
  onDragStart?: (data: DragData) => void;
  onDragEnd?: (data: DragData) => void;
  onDragEnter?: (data: DropData) => void;
  onDragLeave?: (data: DropData) => void;
  onDrop?: (dragData: DragData, dropData: DropData) => void | Promise<void>;
}

// 全局拖拽状态
const globalDragState: DragState = {
  isDragging: false,
  dragData: null,
  activeDropZone: null,
  dragElementHeight: undefined,
  currentPlaceholder: null,
};

// 自动展开状态管理
interface AutoExpandState {
  targetFolderId: number | null;
  timer: NodeJS.Timeout | null;
  isExpanded: boolean;
}

const autoExpandState: AutoExpandState = {
  targetFolderId: null,
  timer: null,
  isExpanded: false,
};

/**
 * 获取元素的精确放置区域
 * @param element 目标元素
 * @param clientY 鼠标Y坐标
 * @param targetType 目标元素类型
 * @returns 放置区域类型
 */
function getPreciseDropZone(
  element: HTMLElement,
  clientY: number,
  targetType: 'folder' | 'document',
): DropZoneType {
  const rect = element.getBoundingClientRect();
  const elementHeight = rect.height;
  const relativeY = clientY - rect.top;

  if (targetType === 'document') {
    // 文档类型：上下等分，不需要中部区域
    const halfHeight = elementHeight / 2;

    if (relativeY <= halfHeight) {
      return 'top';
    } else {
      return 'bottom';
    }
  } else {
    // 文件夹类型：保持原有的1/4区域划分
    const quarterHeight = elementHeight / 4;

    // 顶部1/4区域
    if (relativeY <= quarterHeight) {
      return 'top';
    }

    // 底部1/4区域
    if (relativeY >= elementHeight - quarterHeight) {
      return 'bottom';
    }

    // 中间区域
    return 'inside-middle';
  }
}

/**
 * 创建占位符元素
 */
function createPlaceholder(
  type: 'folder' | 'document',
  position: DropZoneType,
  targetIndex: number,
  depth: number = 0,
  targetElement?: HTMLElement, // 添加目标元素参数，用于触发自定义事件
): HTMLElement {
  // 清理全局的当前占位符
  if (globalDragState.currentPlaceholder) {
    globalDragState.currentPlaceholder.remove();
    globalDragState.currentPlaceholder = null;
  }

  const placeholder = document.createElement('div');
  placeholder.className = `drag-placeholder drag-placeholder--${type} drag-placeholder--${position}`;
  placeholder.dataset.position = position;
  placeholder.dataset.targetIndex = String(targetIndex);
  placeholder.dataset.type = type;

  // 根据层级设置缩进
  const indent = depth * 16;
  placeholder.style.marginLeft = `${indent}px`;

  // 设置占位符高度和样式
  // 使用被拖拽元素的高度，如果没有则使用默认高度
  const height = globalDragState.dragElementHeight || 48;
  placeholder.style.height = `${height}px`;
  placeholder.style.backgroundColor = 'rgba(25, 118, 210, 0.1)';
  placeholder.style.border = '2px dashed rgba(25, 118, 210, 0.3)';
  placeholder.style.borderRadius = '4px';
  placeholder.style.margin = '2px 0';
  placeholder.style.transition = 'all 0.2s ease';
  placeholder.style.display = 'flex';
  placeholder.style.alignItems = 'center';
  placeholder.style.justifyContent = 'center';
  placeholder.style.pointerEvents = 'auto'; // 确保可以接收鼠标事件
  placeholder.style.position = 'relative'; // 确保定位正确

  // 添加提示文字
  const label = document.createElement('div');
  label.className = 'drag-placeholder__label';
  // 使用国际化文本
  label.textContent = $t('src.components.DragDropIndicator.dropHere');
  label.style.fontSize = '11px';
  label.style.color = '#1976d2';
  label.style.opacity = '0';
  label.style.transition = 'opacity 0.2s ease';

  placeholder.appendChild(label);

  // 占位符需要基本的拖拽事件处理来显示正确的鼠标状态
  placeholder.addEventListener('dragover', (event) => {
    event.preventDefault();
    event.stopPropagation();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
    // 显示提示文字
    label.style.opacity = '1';
  });

  placeholder.addEventListener('dragleave', (event) => {
    event.preventDefault();
    event.stopPropagation();
    // 隐藏提示文字
    label.style.opacity = '0';
  });

  placeholder.addEventListener('drop', (event) => {
    event.preventDefault();
    event.stopPropagation();

    console.log('🎯 [createPlaceholder] 占位符接收到 drop 事件，委托给目标元素处理');

    // 创建一个自定义事件，包含占位符的位置信息
    const customEvent = new CustomEvent('placeholder-drop', {
      bubbles: true,
      detail: {
        position,
        targetIndex,
        type,
        originalEvent: event,
      },
    });

    // 如果有目标元素，直接在目标元素上触发事件；否则在占位符上触发（冒泡）
    if (targetElement) {
      console.log('🎯 [createPlaceholder] 在目标元素上触发自定义事件');
      targetElement.dispatchEvent(customEvent);
    } else {
      console.log('🎯 [createPlaceholder] 在占位符上触发自定义事件（冒泡）');
      placeholder.dispatchEvent(customEvent);
    }
  });

  console.log('🎯 [createPlaceholder] 创建占位符（带基本事件处理）:', {
    type,
    position,
    targetIndex,
    depth,
  });

  // 将新创建的占位符保存到全局状态
  globalDragState.currentPlaceholder = placeholder;

  return placeholder;
}

/**
 * 显示占位符
 */
function showPlaceholder(placeholder: HTMLElement) {
  placeholder.classList.add('drag-placeholder--active');
  const label = placeholder.querySelector('.drag-placeholder__label');
  if (label instanceof HTMLElement) {
    label.style.opacity = '1';
  }
}

/**
 * 隐藏占位符
 */
function hidePlaceholder(placeholder: HTMLElement) {
  placeholder.classList.remove('drag-placeholder--active');
  const label = placeholder.querySelector('.drag-placeholder__label');
  if (label instanceof HTMLElement) {
    label.style.opacity = '0';
  }
}

/**
 * 清理占位符
 */
function cleanupPlaceholders() {
  const placeholders = document.querySelectorAll('.drag-placeholder');
  placeholders.forEach((placeholder) => placeholder.remove());
  // 清理全局占位符状态
  globalDragState.currentPlaceholder = null;
}

/**
 * 清理自动展开状态
 */
function cleanupAutoExpand() {
  if (autoExpandState.timer) {
    clearTimeout(autoExpandState.timer);
    autoExpandState.timer = null;
  }
  autoExpandState.targetFolderId = null;
  autoExpandState.isExpanded = false;
}

/**
 * 开始自动展开计时器
 */
function startAutoExpandTimer(folderId: number, expandCallback: () => void) {
  // 如果已经有计时器且目标相同，不重复设置
  if (autoExpandState.targetFolderId === folderId && autoExpandState.timer) {
    return;
  }

  // 清理之前的计时器
  cleanupAutoExpand();

  // 设置新的计时器
  autoExpandState.targetFolderId = folderId;
  autoExpandState.timer = setTimeout(() => {
    console.log('🎯 [autoExpand] 自动展开文件夹:', folderId);
    expandCallback();
    autoExpandState.isExpanded = true;
  }, 1000); // 1秒后自动展开
}

/**
 * 检查是否应该显示占位符（避免无意义的拖拽操作）
 */
function checkIfShouldShowPlaceholder(
  dragData: DragData | null,
  dropData: DropData,
  dropZone: DropZoneType,
): boolean {
  if (!dragData) {
    return true; // 没有拖拽数据，正常显示占位符
  }

  // 只检查同类型的拖拽
  if (dragData.type !== dropData.type) {
    return true; // 不同类型的拖拽，正常显示占位符
  }

  try {
    // 对于文档类型，使用简化的逻辑
    if (dragData.type === 'document') {
      // 检查是否是同一个文档
      if (dragData.id === dropData.id) {
        console.log('🎯 [checkIfShouldShowPlaceholder] 拖拽到自己，不显示占位符', {
          dragId: dragData.id,
          dropId: dropData.id,
        });
        return false;
      }

      // 检查是否是同父级的相邻无意义拖拽
      if (dragData.parentId === dropData.parentId) {
        const draggedIndex = dragData.index;
        const targetIndex = dropData.index;

        if (typeof draggedIndex === 'number' && typeof targetIndex === 'number') {
          // 检查无意义的相邻拖拽
          if (dropZone === 'top' && draggedIndex === targetIndex - 1) {
            console.log('🎯 [checkIfShouldShowPlaceholder] 文档拖拽到相邻元素上方，不显示占位符', {
              draggedIndex,
              targetIndex,
              dropZone,
            });
            return false;
          }

          if (dropZone === 'bottom' && draggedIndex === targetIndex + 1) {
            console.log('🎯 [checkIfShouldShowPlaceholder] 文档拖拽到相邻元素下方，不显示占位符', {
              draggedIndex,
              targetIndex,
              dropZone,
            });
            return false;
          }
        }
      }

      return true; // 其他文档拖拽情况都显示占位符
    }

    // 对于文件夹类型，保持原有逻辑
    const dropDataParentId = (dropData.item as Folder).parent_id;
    const isSameParent = dragData.parentId === dropDataParentId;

    if (!isSameParent) {
      return true; // 跨层级拖拽，总是显示占位符
    }

    // 同级拖拽时，检查是否是相邻元素的无意义拖拽
    const draggedIndex = dragData.index;
    const targetIndex = dropData.index;

    if (typeof draggedIndex !== 'number' || typeof targetIndex !== 'number') {
      return true; // 没有索引信息，正常显示
    }

    // 检查是否是相邻元素的无意义拖拽
    if (dropZone === 'top') {
      // 拖拽到目标元素上方
      // 如果拖拽元素就在目标元素的上方（相邻），则是无意义的
      if (draggedIndex === targetIndex - 1) {
        console.log('🎯 [checkIfShouldShowPlaceholder] 文件夹拖拽到相邻元素上方，不显示占位符', {
          draggedIndex,
          targetIndex,
          dropZone,
        });
        return false;
      }
    } else if (dropZone === 'bottom') {
      // 拖拽到目标元素下方
      // 如果拖拽元素就在目标元素的下方（相邻），则是无意义的
      if (draggedIndex === targetIndex + 1) {
        console.log('🎯 [checkIfShouldShowPlaceholder] 文件夹拖拽到相邻元素下方，不显示占位符', {
          draggedIndex,
          targetIndex,
          dropZone,
        });
        return false;
      }
    }

    return true; // 其他情况正常显示占位符
  } catch (error) {
    console.warn('🎯 [checkIfShouldShowPlaceholder] 检查失败，默认显示占位符:', error);
    return true; // 出错时默认显示占位符
  }
}

/**
 * 设置元素为可拖拽（新版本的精确拖拽）
 */
export function makeDraggable(
  element: HTMLElement,
  data: DragData,
  events: DragEvents = {},
): () => void {
  const elementWithCleanup = element as HTMLElementWithDragCleanup;

  // 清理现有拖拽
  if (elementWithCleanup.__dragCleanup) {
    elementWithCleanup.__dragCleanup();
    delete elementWithCleanup.__dragCleanup;
  }

  // 添加原生拖拽事件监听器来设置 dataTransfer
  const handleDragStart = (e: DragEvent) => {
    if (e.dataTransfer) {
      const dragData = {
        type: data.type,
        id: data.id,
        parentId: data.parentId,
        itemType: data.type,
        itemId: data.id,
        index: data.index,
      };
      e.dataTransfer.setData('application/json', JSON.stringify(dragData));
      e.dataTransfer.effectAllowed = 'move';
      console.log('🎯 [dragAndDrop] 设置 dataTransfer 数据:', dragData);
    }
  };

  element.addEventListener('dragstart', handleDragStart);

  const cleanup = draggable({
    element,
    getInitialData: () => ({
      type: data.type,
      id: data.id,
      parentId: data.parentId,
      itemType: data.type,
      itemId: data.id,
      index: data.index,
    }),
    onDragStart: () => {
      globalDragState.isDragging = true;
      globalDragState.dragData = data;
      // 获取被拖拽元素的高度
      globalDragState.dragElementHeight = element.getBoundingClientRect().height;
      element.classList.add('dragging');
      events.onDragStart?.(data);

      // 添加全局拖拽样式
      document.body.classList.add('dragging-active');
    },
    onDrop: () => {
      // 延迟清理拖拽数据，确保 makeDropTarget 的 onDrop 事件能够访问到数据
      setTimeout(() => {
        globalDragState.isDragging = false;
        globalDragState.dragData = null;
        globalDragState.dragElementHeight = undefined;
        element.classList.remove('dragging');

        // 清理占位符和自动展开状态
        cleanupPlaceholders();
        cleanupAutoExpand();
        document.body.classList.remove('dragging-active');
      }, 0);

      // 立即调用用户的 onDragEnd 事件
      events.onDragEnd?.(data);
    },
  });

  // 返回清理函数，包括移除原生事件监听器
  const enhancedCleanup = () => {
    element.removeEventListener('dragstart', handleDragStart);
    cleanup();
  };

  elementWithCleanup.__dragCleanup = enhancedCleanup;
  return enhancedCleanup;
}

/**
 * 设置元素为拖拽目标（支持精确区域检测）
 */
export function makeDropTarget(
  element: HTMLElement,
  data: DropData,
  events: DragEvents = {},
  options: {
    canDrop?: (dragData: DragData, dropData: DropData) => boolean;
    depth?: number;
    autoExpand?: boolean;
    showPlaceholder?: boolean; // 新增：控制是否显示占位符
    isExpanded?: () => boolean; // 新增：检查文件夹是否已展开
  } = {},
): () => void {
  const elementWithCleanup = element as HTMLElementWithDragCleanup;
  let lastDropZone: DropZoneType | null = null;

  // 清理现有拖拽目标
  if (elementWithCleanup.__dropCleanup) {
    elementWithCleanup.__dropCleanup();
    delete elementWithCleanup.__dropCleanup;
  }

  // 监听占位符的自定义drop事件
  const handlePlaceholderDrop = (event: Event) => {
    const customEvent = event as CustomEvent;
    const { position, targetIndex, type } = customEvent.detail;

    console.log('🎯 [makeDropTarget] 接收到占位符drop事件:', {
      position,
      targetIndex,
      type,
      elementId: data.id,
    });

    if (globalDragState.dragData) {
      const dragData = globalDragState.dragData;
      let dropPosition: 'before' | 'after' | 'inside-first' | 'inside-last';

      switch (position) {
        case 'top':
          dropPosition = 'before';
          break;
        case 'bottom':
          dropPosition = 'after';
          break;
        default:
          dropPosition = 'after';
      }

      console.log('🎯 [makeDropTarget] 占位符drop - 调用 events.onDrop:', {
        dragData,
        dropData: { ...data, position: dropPosition },
      });

      void events.onDrop?.(dragData, { ...data, position: dropPosition });
    }
  };

  element.addEventListener('placeholder-drop', handlePlaceholderDrop);

  const cleanup = dropTargetForElements({
    element,
    getData: () => ({
      type: data.type,
      id: data.id,
      targetType: data.type,
      targetId: data.id,
      index: data.index,
    }),
    canDrop: () => {
      if (!globalDragState.dragData) return false;

      // 基本验证
      const dragData = globalDragState.dragData;

      // 不能拖拽到自己
      if (dragData.type === data.type && dragData.id === data.id) {
        return false;
      }

      // 自定义验证
      if (options.canDrop) {
        return options.canDrop(dragData, data);
      }

      return true;
    },
    onDragEnter: () => {
      element.classList.add('drag-over');
      // 调用用户提供的 onDragEnter 回调
      events.onDragEnter?.(data);
    },
    onDragLeave: () => {
      element.classList.remove('drag-over');
      if (globalDragState.currentPlaceholder) {
        hidePlaceholder(globalDragState.currentPlaceholder);
      }
      lastDropZone = null;

      // 清理自动展开状态
      if (autoExpandState.targetFolderId === data.id) {
        cleanupAutoExpand();
      }

      // 调用用户提供的 onDragLeave 回调
      events.onDragLeave?.(data);
    },
    onDrag: ({ location }) => {
      const dropZone = getPreciseDropZone(element, location.current.input.clientY, data.type);

      // 只在区域变化时更新
      if (dropZone !== lastDropZone) {
        console.log('🎯 [makeDropTarget] onDrag 设置 lastDropZone:', {
          oldZone: lastDropZone,
          newZone: dropZone,
          elementId: data.id,
        });
        lastDropZone = dropZone;

        // 清理旧占位符（现在由 createPlaceholder 函数全局管理）
        // 不需要在这里手动清理，createPlaceholder 会自动处理

        // 创建新的占位符（仅在明确允许显示占位符时）
        console.log('🎯 [makeDropTarget] 检查占位符创建条件:', {
          dropZone,
          showPlaceholder: options.showPlaceholder,
          dragDataType: globalDragState.dragData?.type,
          dataType: data.type,
        });

        if ((dropZone === 'top' || dropZone === 'bottom') && options.showPlaceholder === true) {
          // 检查拖拽类型是否匹配：只有同类型拖拽才显示占位符
          const isDragTypeMatch = globalDragState.dragData?.type === data.type;

          console.log('🎯 [makeDropTarget] 拖拽类型匹配检查:', {
            isDragTypeMatch,
            dragDataType: globalDragState.dragData?.type,
            dataType: data.type,
          });

          if (isDragTypeMatch) {
            // 检查是否是无意义的拖拽操作
            const shouldShowPlaceholder = checkIfShouldShowPlaceholder(
              globalDragState.dragData,
              data,
              dropZone,
            );

            console.log('🎯 [makeDropTarget] 无意义拖拽检查结果:', {
              shouldShowPlaceholder,
              dragData: globalDragState.dragData,
              dropData: data,
              dropZone,
            });

            if (shouldShowPlaceholder) {
              const placeholder = createPlaceholder(
                data.type,
                dropZone,
                data.index,
                options.depth || 0,
                element, // 传递目标元素，用于触发自定义事件
              );

              if (dropZone === 'top') {
                element.parentNode?.insertBefore(placeholder, element);
              } else {
                element.parentNode?.insertBefore(placeholder, element.nextSibling);
              }

              showPlaceholder(placeholder);
            }
          }
        }

        if (dropZone === 'inside-middle' && data.type === 'folder') {
          // 处理拖拽到文件夹中部的逻辑
          if (options.autoExpand) {
            // 检查文件夹是否已经展开
            const isExpanded = options.isExpanded ? options.isExpanded() : false;

            if (isExpanded) {
              // 如果已经展开，立即触发内部排序逻辑
              console.log('🎯 [makeDropTarget] 文件夹已展开，立即触发内部排序');
              events.onDragEnter?.({ ...data, position: 'inside-first' });
            } else {
              // 如果未展开，启动自动展开计时器
              console.log('🎯 [makeDropTarget] 文件夹未展开，启动自动展开计时器');
              startAutoExpandTimer(data.id, () => {
                console.log('🎯 [makeDropTarget] 自动展开计时器触发');
                events.onDragEnter?.({ ...data, position: 'inside-first' });
              });
            }
          }
        }
      }
    },
    onDrop: () => {
      element.classList.remove('drag-over');

      console.log('🎯 [makeDropTarget] onDrop 被调用:', {
        lastDropZone,
        hasDragData: !!globalDragState.dragData,
        elementId: data.id,
        hasEventsOnDrop: !!events.onDrop,
      });

      if (lastDropZone && globalDragState.dragData) {
        const dragData = globalDragState.dragData;
        let position: 'before' | 'after' | 'inside-first' | 'inside-last';

        switch (lastDropZone) {
          case 'top':
            position = 'before';
            break;
          case 'bottom':
            position = 'after';
            break;
          case 'inside-middle':
            // 拖拽到文件夹中部，设置为内部第一个位置
            position = 'inside-first';
            break;
          default:
            position = 'after';
        }

        console.log('🎯 [makeDropTarget] 调用 events.onDrop:', {
          dragData,
          dropData: { ...data, position },
        });

        void events.onDrop?.(dragData, { ...data, position });
      } else {
        console.log('🎯 [makeDropTarget] onDrop 条件不满足:', {
          lastDropZone,
          hasDragData: !!globalDragState.dragData,
        });
      }

      // 清理全局占位符
      if (globalDragState.currentPlaceholder) {
        globalDragState.currentPlaceholder.remove();
        globalDragState.currentPlaceholder = null;
      }
      lastDropZone = null;
    },
  });

  // 创建增强的清理函数，包含占位符事件监听器的清理
  const enhancedCleanup = () => {
    element.removeEventListener('placeholder-drop', handlePlaceholderDrop);
    cleanup();
  };

  elementWithCleanup.__dropCleanup = enhancedCleanup;
  return enhancedCleanup;
}

/**
 * 监控拖拽事件
 */
export function monitorDragEvents(events: {
  onDragStart?: (data: DragData) => void;
  onDragEnd?: (data: DragData) => void;
}) {
  return monitorForElements({
    onDragStart: () => {
      // 使用全局拖拽状态中的数据，因为它包含完整的 DragData 信息
      if (globalDragState.dragData) {
        events.onDragStart?.(globalDragState.dragData);
      }
    },
    onDrop: () => {
      // 使用全局拖拽状态中的数据，因为它包含完整的 DragData 信息
      if (globalDragState.dragData) {
        events.onDragEnd?.(globalDragState.dragData);
      }
    },
  });
}

/**
 * 获取拖拽样式
 */
export const dragStyles = `
  .dragging {
    opacity: 0.5;
    transform: scale(0.95);
    transition: all 0.2s ease;
  }

  .dragging-active {
    cursor: grabbing !important;
  }

  .drag-over {
    background-color: rgba(25, 118, 210, 0.1);
    border: 2px dashed rgba(25, 118, 210, 0.3);
    transition: all 0.2s ease;
  }

  .drag-placeholder {
    position: relative;
    width: 100%;
    min-height: 8px;
    transition: all 0.2s ease;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1px 0;
  }

  .drag-placeholder--active {
    background-color: rgba(25, 118, 210, 0.15);
    border: 2px dashed rgba(25, 118, 210, 0.5);
    min-height: 12px;
  }

  .drag-placeholder__label {
    font-size: 11px;
    color: #1976d2;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .drag-placeholder--active .drag-placeholder__label {
    opacity: 1;
  }

  .drag-placeholder--folder {
    margin-left: 0;
  }

  .drag-placeholder--document {
    margin-left: 16px;
  }

  /* 暗色主题适配 */
  .body--dark .drag-placeholder--active {
    background-color: rgba(144, 202, 249, 0.15);
    border-color: rgba(144, 202, 249, 0.5);
  }

  .body--dark .drag-placeholder__label {
    color: #90caf9;
  }
`;

// 导出全局状态
export { globalDragState };
