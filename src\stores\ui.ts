import { defineStore, acceptHMRUpdate } from 'pinia';
import { Dark } from 'quasar';
import { watch } from 'vue';
import type { App, SearchEngineProvider, ResourceProvider, FloatAgent } from 'src/env.d';
import type {
  QwenSettings,
  OllamaSettings,
  MiniMaxSettings,
  DeepSeekSettings,
  VolcesSettings,
  MoonshotSettings,
  AnthropicSettings,
  KnowledgeBaseSettings,
  GrokSettings,
} from 'src/types/qwen';
import type { OpenAISettings } from 'src/types/openai';
import type { AzureOpenAISettings } from 'src/types/azureOpenai';
import type { GeminiSettings } from 'src/types/gemini';
import type { GlmSettings } from 'src/types/glm';
import type { AutoComplete } from 'src/env.d';
import {
  DEFAULT_APP_SETTINGS,
  getCompleteAppSettings,
  getCompleteLlmSettings,
} from 'src/config/defaultSettings';
import { getAllModels, type CategorizedModels } from 'src/types/modelCategories';
import { SortMode } from 'src/utils/sortUtils';

export const useUiStore = defineStore('ui', {
  state: () => ({
    leftDrawerOpen: true,
    rightDrawerOpen: true,
    isDarkTheme: false, // 初始值不再重要，会被Qt覆盖
    // 性能优化：防抖timeout
    _leftDrawerToggleTimeout: null as NodeJS.Timeout | null,
    _rightDrawerToggleTimeout: null as NodeJS.Timeout | null,
    // 文档复制状态
    copiedDocument: null as {
      id: number;
      title: string;
      content: string;
      metadata: string;
      parentId: number;
      mode: 'copy' | 'cut';
    } | null,
    apps: [
      {
        title: 'InkCop',
        key: 'inkcop',
        icon: 'fluent-code_text_edit',
        link: '/doc',
        position: 'top',
        type: 'app',
      },
      {
        title: 'Knowledge',
        key: 'knowledge',
        icon: 'fluent-notebook_lightning',
        link: '/knowledge',
        position: 'top',
        type: 'app',
      },
      {
        type: 'space',
        position: 'top',
      },
      {
        title: 'Settings',
        key: 'settings',
        icon: 'fluent-slide_settings',
        link: '/settings',
        position: 'bottom',
        type: 'botton',
      },
      {
        title: 'icons',
        key: 'icons',
        icon: 'fluent-icons',
        link: '/fluent-icons-test',
        position: 'bottom',
        type: 'botton',
        isDev: true,
      },
    ] as App[],
    app: 'inkcop',
    perferences: DEFAULT_APP_SETTINGS,
    settingfor: 'editor' as string,
    agentWriter: {
      insertPos: null,
    },
    highlightTreeItem: null,
    lowlight: false,
    expandedFolders: new Set<number>(),
    // 文件夹展开路径管理
    pendingExpansionPath: [] as number[],
    expandFolderPath: 0, // 用于触发响应式更新的时间戳
    // 排序模式管理
    sortMode: SortMode.CUSTOM as SortMode, // 默认字典排序
  }),

  getters: {
    // 添加 LLM 设置相关的 getter
    currentLlmProvider: (state) => state.perferences.provider,
    currentLlmSettings: (state) => {
      const provider = state.perferences.provider;
      return state.perferences.llm[provider as keyof typeof state.perferences.llm];
    },
    // 搜索引擎相关的 getter
    defaultSearchEngineProvider: (state) => {
      const defaultId = state.perferences.searchEngine.defaultProvider;
      return state.perferences.searchEngine.providers.find((p) => p.id === defaultId);
    },
    // 资源服务商相关的 getter
    defaultResourceProvider: (state) => {
      const defaultId = state.perferences.resourceProvider.defaultProvider;
      return state.perferences.resourceProvider.providers.find((p) => p.id === defaultId);
    },
    // AutoComplete 相关的 getter
    hasEnabledLlmProviders: (state) => {
      return Object.values(state.perferences.llm).some(
        (provider) =>
          provider && typeof provider === 'object' && 'enabled' in provider && provider.enabled,
      );
    },
    firstEnabledLlmProvider: (state) => {
      const providers = state.perferences.llm;
      for (const [key, provider] of Object.entries(providers)) {
        if (provider && typeof provider === 'object' && 'enabled' in provider && provider.enabled) {
          return { key, provider };
        }
      }
      return null;
    },
  },

  actions: {
    /**
     * 清理所有定时器和资源
     * 在 store 销毁时调用
     */
    $dispose() {
      // 清理防抖定时器
      if (this._leftDrawerToggleTimeout) {
        clearTimeout(this._leftDrawerToggleTimeout);
        this._leftDrawerToggleTimeout = null;
      }
      if (this._rightDrawerToggleTimeout) {
        clearTimeout(this._rightDrawerToggleTimeout);
        this._rightDrawerToggleTimeout = null;
      }
    },
    setApp(app: string) {
      this.app = app;
      localStorage.setItem('app', app);
    },
    toggleLeftDrawer() {
      // 添加防抖，避免快速切换导致的性能问题
      if (this._leftDrawerToggleTimeout) {
        clearTimeout(this._leftDrawerToggleTimeout);
      }
      this._leftDrawerToggleTimeout = setTimeout(() => {
        this.leftDrawerOpen = !this.leftDrawerOpen;
        // 保存状态到localStorage
        localStorage.setItem('leftDrawerOpen', this.leftDrawerOpen.toString());
      }, 50);
    },
    toggleRightDrawer() {
      // 添加防抖，避免快速切换导致的性能问题
      if (this._rightDrawerToggleTimeout) {
        clearTimeout(this._rightDrawerToggleTimeout);
      }
      this._rightDrawerToggleTimeout = setTimeout(() => {
        this.rightDrawerOpen = !this.rightDrawerOpen;
        // 保存状态到localStorage
        localStorage.setItem('rightDrawerOpen', this.rightDrawerOpen.toString());
      }, 50);
    },
    setTheme(isDark: boolean) {
      Dark.set(isDark);
      this.isDarkTheme = isDark;
    },
    toggleTheme() {
      // 简化逻辑：直接切换主题状态
      this.isDarkTheme = !this.isDarkTheme;
      Dark.set(this.isDarkTheme);

      // 更新数据库中的主题设置
      this.perferences.base.theme = this.isDarkTheme ? 'dark' : 'light';

      // 直接调用Qt端的主题切换方法
      if (window.qtWindow?.setThemeDirectly) {
        window.qtWindow.setThemeDirectly(this.isDarkTheme);
      }
    },
    async loadSettings() {
      try {
        const { useSqlite } = await import('src/composeables/useSqlite');
        const { getAppSettings } = useSqlite();
        const userSettings = await getAppSettings();

        // 使用统一的设置合并逻辑，确保所有默认值都被正确应用
        this.perferences = getCompleteAppSettings(userSettings);
      } catch (error) {
        console.error('Failed to load settings:', error);
        // 如果加载失败，使用默认设置
        this.perferences = DEFAULT_APP_SETTINGS;
      }
    },
    async saveSettings() {
      try {
        const { useSqlite } = await import('src/composeables/useSqlite');
        const { setAppSettings } = useSqlite();
        await setAppSettings(this.perferences);
      } catch (error) {
        console.error('Failed to save settings:', error);
      }
    },
    watchSettings() {
      watch(
        () => this.perferences,
        async () => {
          // 统一保存应用设置，LLM设置作为应用设置的一部分一起保存
          try {
            await this.saveSettings();
          } catch (error) {
            console.error('[UI] 设置自动保存失败:', error);
          }
        },
        { deep: true },
      );
    },
    // 添加 LLM 设置相关的 actions
    setLlmProvider(provider: string) {
      this.perferences.provider = provider;

      // 确保切换provider时，配置对象完整性
      // 使用深度合并确保所有默认值都存在
      this.perferences.llm = getCompleteLlmSettings(this.perferences.llm);
    },
    updateQwenSettings(settings: Partial<QwenSettings>) {
      this.perferences.llm.qwen = {
        ...this.perferences.llm.qwen,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateOllamaSettings(settings: Partial<OllamaSettings>) {
      this.perferences.llm.ollama = {
        ...this.perferences.llm.ollama,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateMiniMaxSettings(settings: Partial<MiniMaxSettings>) {
      this.perferences.llm.minimax = {
        ...this.perferences.llm.minimax,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateDeepSeekSettings(settings: Partial<DeepSeekSettings>) {
      this.perferences.llm.deepseek = {
        ...this.perferences.llm.deepseek,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateVolcesSettings(settings: Partial<VolcesSettings>) {
      this.perferences.llm.volces = {
        ...this.perferences.llm.volces,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateMoonshotSettings(settings: Partial<MoonshotSettings>) {
      this.perferences.llm.moonshot = {
        ...this.perferences.llm.moonshot,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateAnthropicSettings(settings: Partial<AnthropicSettings>) {
      this.perferences.llm.anthropic = {
        ...this.perferences.llm.anthropic,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateOpenAISettings(settings: Partial<OpenAISettings>) {
      this.perferences.llm.openai = {
        ...this.perferences.llm.openai,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateAzureOpenAISettings(settings: Partial<AzureOpenAISettings>) {
      this.perferences.llm.azureOpenai = {
        ...this.perferences.llm.azureOpenai,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateGeminiSettings(settings: Partial<GeminiSettings>) {
      this.perferences.llm.gemini = {
        ...this.perferences.llm.gemini,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateGrokSettings(settings: Partial<GrokSettings>) {
      this.perferences.llm.grok = {
        ...this.perferences.llm.grok,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateGlmSettings(settings: Partial<GlmSettings>) {
      this.perferences.llm.glm = {
        ...this.perferences.llm.glm,
        ...settings,
      };
      // 如果更新了enabled状态，触发AutoComplete自动调整
      if ('enabled' in settings) {
        this.autoAdjustAutoComplete();
      }
    },
    updateKnowledgeBaseSettings(settings: Partial<KnowledgeBaseSettings>) {
      this.perferences.knowledgeBase = {
        ...this.perferences.knowledgeBase,
        ...settings,
      };
      console.log('[UI Store] 知识库设置已更新:', settings);
    },
    updateAutoCompleteSettings(settings: Partial<AutoComplete>) {
      this.perferences.autoComplete = {
        ...this.perferences.autoComplete,
        ...settings,
      };
    },
    updateFloatAgentSettings(settings: Partial<FloatAgent>) {
      this.perferences.floatAgent = {
        ...this.perferences.floatAgent,
        ...settings,
      };
    },
    updateEditorSettings(settings: Partial<typeof this.perferences.editor>) {
      this.perferences.editor = {
        ...this.perferences.editor,
        ...settings,
      };
    },

    // 自动调整AutoComplete设置
    autoAdjustAutoComplete() {
      console.log('[UI Store] 开始自动调整AutoComplete设置');

      const hasEnabled = this.hasEnabledLlmProviders;
      const currentModel = this.perferences.autoComplete.body.model;

      // 如果没有启用任何供应商，禁用AutoComplete
      if (!hasEnabled) {
        console.log('[UI Store] 没有启用的供应商，禁用AutoComplete');
        this.updateAutoCompleteSettings({ enabled: false });
        return;
      }

      // 检查当前模型的供应商是否还启用
      const currentModelProvider = this.getCurrentModelProvider(currentModel);
      const isCurrentProviderEnabled =
        currentModelProvider && this.isProviderEnabled(currentModelProvider);

      if (isCurrentProviderEnabled) {
        console.log(
          `[UI Store] 当前模型 ${currentModel} 的供应商 ${currentModelProvider} 仍然启用，保持不变`,
        );
        // 如果之前被禁用，现在重新启用
        if (!this.perferences.autoComplete.enabled) {
          this.updateAutoCompleteSettings({ enabled: true });
        }
        return;
      }

      // 当前模型的供应商未启用，选择第一个启用的供应商的模型
      const firstEnabled = this.firstEnabledLlmProvider;
      if (firstEnabled) {
        const newModel = firstEnabled.provider.model;
        const newBaseUrl = this.getProviderBaseUrl(firstEnabled.key, firstEnabled.provider.baseUrl);
        const newApiKey = firstEnabled.provider.apiKey || '';

        console.log(`[UI Store] 切换到第一个启用的供应商 ${firstEnabled.key}，模型: ${newModel}`);

        this.updateAutoCompleteSettings({
          enabled: true,
          base_url: newBaseUrl,
          api_key: newApiKey,
          body: {
            ...this.perferences.autoComplete.body,
            model: newModel,
          },
        });
      }
    },
    autoAdjustFloatAgent() {
      console.log('[UI Store] 开始自动调整FloatAgent设置');

      const hasEnabled = this.hasEnabledLlmProviders;
      const currentModel = this.perferences.floatAgent.body.model;

      // 如果没有启用任何供应商，禁用AutoComplete
      if (!hasEnabled) {
        console.log('[UI Store] 没有启用的供应商，禁用FloatAgent');
        this.updateFloatAgentSettings({ enabled: false });
        return;
      }

      // 检查当前模型的供应商是否还启用
      const currentModelProvider = this.getCurrentModelProvider(currentModel);
      const isCurrentProviderEnabled =
        currentModelProvider && this.isProviderEnabled(currentModelProvider);

      if (isCurrentProviderEnabled) {
        console.log(
          `[UI Store] 当前模型 ${currentModel} 的供应商 ${currentModelProvider} 仍然启用，保持不变`,
        );
        // 如果之前被禁用，现在重新启用
        if (!this.perferences.floatAgent.enabled) {
          this.updateFloatAgentSettings({ enabled: true });
        }
        return;
      }

      // 当前模型的供应商未启用，选择第一个启用的供应商的模型
      const firstEnabled = this.firstEnabledLlmProvider;
      if (firstEnabled) {
        const newModel = firstEnabled.provider.model;
        const newBaseUrl = this.getProviderBaseUrl(firstEnabled.key, firstEnabled.provider.baseUrl);
        const newApiKey = firstEnabled.provider.apiKey || '';

        console.log(`[UI Store] 切换到第一个启用的供应商 ${firstEnabled.key}，模型: ${newModel}`);

        this.updateFloatAgentSettings({
          enabled: true,
          base_url: newBaseUrl,
          api_key: newApiKey,
          body: {
            ...this.perferences.floatAgent.body,
            model: newModel,
          },
        });
      }
    },

    // 获取模型对应的供应商
    getCurrentModelProvider(modelName: string): string | null {
      for (const [providerKey, provider] of Object.entries(this.perferences.llm)) {
        if (provider && typeof provider === 'object' && 'avaliableModels' in provider) {
          const categorizedModels = (provider as { avaliableModels: CategorizedModels })
            .avaliableModels;
          if (categorizedModels && typeof categorizedModels === 'object') {
            const allModels = getAllModels(categorizedModels);
            if (allModels.includes(modelName)) {
              return providerKey;
            }
          }
        }
      }
      return null;
    },

    // 检查供应商是否启用
    isProviderEnabled(providerKey: string): boolean {
      const provider = this.perferences.llm[providerKey as keyof typeof this.perferences.llm];
      return (
        provider &&
        typeof provider === 'object' &&
        'enabled' in provider &&
        provider.enabled === true
      );
    },

    // 获取供应商的BaseURL
    getProviderBaseUrl(providerKey: string, baseUrl: string): string {
      const needsChatCompletions = ['qwen', 'deepseek', 'volces', 'openai'].includes(providerKey);
      return needsChatCompletions ? `${baseUrl}/chat/completions` : baseUrl;
    },

    // 搜索引擎供应商管理
    updateSearchEngineProvider(provider: SearchEngineProvider) {
      const index = this.perferences.searchEngine.providers.findIndex((p) => p.id === provider.id);
      if (index !== -1) {
        this.perferences.searchEngine.providers[index] = provider;
      }
    },

    // 资源服务商管理
    updateResourceProvider(provider: ResourceProvider) {
      const index = this.perferences.resourceProvider.providers.findIndex(
        (p) => p.id === provider.id,
      );
      if (index !== -1) {
        this.perferences.resourceProvider.providers[index] = provider;
      }
    },

    async loadLlmSettings() {
      try {
        const { useSqlite } = await import('src/composeables/useSqlite');
        const { getLlmSettings } = useSqlite();
        const userLlmSettings = await getLlmSettings();

        // 使用统一的 LLM 设置合并逻辑
        this.perferences.llm = getCompleteLlmSettings(userLlmSettings);
      } catch (error) {
        console.error('Failed to load LLM settings:', error);
        // 如果加载失败，使用默认 LLM 设置
        this.perferences.llm = DEFAULT_APP_SETTINGS.llm;
      }
    },
    async saveLlmSettings() {
      try {
        const { useSqlite } = await import('src/composeables/useSqlite');
        const { setLlmSettings } = useSqlite();
        await setLlmSettings(this.perferences.llm);
      } catch (error) {
        console.error('Failed to save LLM settings:', error);
      }
    },
    /**
     * 迁移独立的LLM设置到应用设置中
     * 如果独立的LLM设置表中有数据，但应用设置中没有对应的LLM设置，则进行迁移
     */
    async migrateLlmSettings() {
      try {
        const { useSqlite } = await import('src/composeables/useSqlite');
        const { getLlmSettings } = useSqlite();
        const separateLlmSettings = await getLlmSettings();

        if (separateLlmSettings) {
          // 检查当前应用设置中的LLM设置是否完整
          const currentLlmSettings = this.perferences.llm;

          // 使用深度合并，优先使用独立LLM设置中的数据
          const mergedLlmSettings = getCompleteLlmSettings({
            ...currentLlmSettings,
            ...separateLlmSettings,
          });

          // 更新应用设置中的LLM部分
          this.perferences.llm = mergedLlmSettings;

          // 保存合并后的设置到应用设置表
          await this.saveSettings();
        }
      } catch (error) {
        console.error('[UI Store] LLM设置迁移失败:', error);
      }
    },
    // 文档复制相关方法
    copyDocument(document: {
      id: number;
      title: string;
      content: string;
      metadata: string;
      parentId: number;
    }) {
      this.copiedDocument = { ...document, mode: 'copy' };
    },
    cutDocument(document: {
      id: number;
      title: string;
      content: string;
      metadata: string;
      parentId: number;
    }) {
      this.copiedDocument = { ...document, mode: 'cut' };
    },
    clearCopiedDocument() {
      this.copiedDocument = null;
    },
    hasCopiedDocument(): boolean {
      return this.copiedDocument !== null;
    },
    isCutMode(): boolean {
      return this.copiedDocument?.mode === 'cut';
    },
    isCopyMode(): boolean {
      return this.copiedDocument?.mode === 'copy';
    },
    isDocumentCut(documentId: number): boolean {
      return this.copiedDocument?.mode === 'cut' && this.copiedDocument?.id === documentId;
    },
    // 检查知识库配置是否可用
    isKnowledgeBaseConfigured(): boolean {
      const kbSettings = this.perferences.knowledgeBase;

      // 基础配置检查
      const hasBaseConfig = !!(
        kbSettings?.baseUrl?.trim() &&
        kbSettings?.apiKey?.trim() &&
        kbSettings?.model?.trim()
      );

      // 如果使用本地模式，需要额外检查本地模型配置
      if (kbSettings?.embeddingMode === 'local') {
        const hasLocalConfig = !!(
          kbSettings?.localModelPath?.trim() && kbSettings?.selectedGpuDevice !== undefined
        );
        return hasBaseConfig && hasLocalConfig;
      }

      return hasBaseConfig;
    },
    // 展开文档所在文件夹及其所有父级文件夹
    async expandDocumentPath(documentId: number) {
      try {
        // 动态导入docStore，避免循环依赖
        const { useDocStore } = await import('src/stores/doc');
        const docStore = useDocStore();

        // 从文档文件夹映射中获取文档所在的文件夹ID
        const folderId = docStore.documentFolderMap.get(documentId);
        if (!folderId || folderId === -1) {
          // console.log('📂 [UI] 文档不在任何文件夹中，无需展开:', documentId);
          return;
        }

        // console.log('📂 [UI] 开始展开文档路径:', documentId, '文件夹ID:', folderId);

        // 获取文件夹的完整父级路径
        const getFolderParentPath = (folderId: number): number[] => {
          const path: number[] = [];
          const folder = docStore.folderMap.get(folderId);

          if (folder && folder.parent_id && folder.parent_id !== -1) {
            // 递归获取父级路径
            path.push(...getFolderParentPath(folder.parent_id));
            path.push(folder.parent_id);
          }

          return path;
        };

        // 获取需要展开的完整路径（包括文档所在文件夹）
        const parentPath = getFolderParentPath(folderId);
        const fullPath = [...parentPath, folderId];

        // console.log('📂 [UI] 需要展开的文件夹路径:', fullPath);

        // 触发文件夹展开事件
        this.triggerFolderExpansion(fullPath);

        // console.log('✅ [UI] 文档路径展开完成:', documentId);
      } catch (error) {
        console.error('❌ [UI] 展开文档路径失败:', error);
      }
    },

    // 触发文件夹展开事件
    triggerFolderExpansion(folderPath: number[]) {
      // 设置需要展开的文件夹路径
      this.pendingExpansionPath = folderPath;

      // 触发展开事件，让FolderTree组件监听并处理
      this.expandFolderPath = Date.now(); // 使用时间戳触发响应式更新
    },

    // 排序模式管理
    setSortMode(mode: SortMode) {
      this.sortMode = mode;
    },

    toggleSortMode() {
      this.sortMode =
        this.sortMode === SortMode.ALPHABETICAL ? SortMode.CUSTOM : SortMode.ALPHABETICAL;
    },
  },
});
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useUiStore, import.meta.hot));
}
