<template>
  <div class="llm-settings absolute-full">
    <q-splitter
      v-model="splitterModel"
      unit="px"
      :limits="[220, 360]"
      separator-class="op-3"
      class="fit"
    >
      <!-- 左侧供应商列表 -->
      <template v-slot:before>
        <div class="column full-height">
          <q-scroll-area class="q-space">
            <q-list class="q-pa-sm">
              <q-item
                v-for="provider in providers"
                :key="provider.key"
                v-ripple
                clickable
                :active="settingFor === provider.key"
                class="radius-xs"
                @click="changeSettingFor(provider.key)"
              >
                <q-item-section avatar>
                  <img
                    :src="getResource(provider.key)?.icon[$q.dark.isActive ? 'dark' : 'light']"
                    :alt="provider.alt"
                    width="36"
                    height="36"
                  />
                  {{ provider.hidden }}
                </q-item-section>
                <q-item-section>
                  <q-item-label>
                    <strong>{{ provider.name }}</strong>
                  </q-item-label>
                  <q-item-label caption class="text-grey">{{ provider.description }}</q-item-label>
                </q-item-section>
                <q-item-section v-if="uiStore.currentLlmProvider === provider.key" side>
                  <q-badge color="positive" rounded />
                </q-item-section>
              </q-item>
            </q-list>
          </q-scroll-area>
        </div>
      </template>

      <!-- 右侧设置详情 -->
      <template v-slot:after>
        <div class="column full-height">
          <q-banner v-if="!hasEnabled" class="bg-negative text-white q-mb-lg radius-sm">
            {{ $t('src.components.settings.LlmSettings.enableRequired') }}
          </q-banner>
          <q-scroll-area class="q-space q-pa-xl">
            <template v-if="settingFor === 'qwen'">
              <QwenOptions />
            </template>
            <template v-else-if="settingFor === 'ollama'">
              <OllamaOptions />
            </template>
            <template v-else-if="settingFor === 'minimax'">
              <MiniMaxOptions />
            </template>
            <template v-else-if="settingFor === 'deepseek'">
              <DeepSeekOptions />
            </template>
            <template v-else-if="settingFor === 'volces'">
              <VolcesOptions />
            </template>
            <template v-else-if="settingFor === 'moonshot'">
              <MoonshotOptions />
            </template>
            <template v-else-if="settingFor === 'anthropic'">
              <AnthropicOptions />
            </template>
            <template v-else-if="settingFor === 'openai'">
              <OpenAIOptions />
            </template>
            <template v-else-if="settingFor === 'azureOpenai'">
              <AzureOpenAIOptions />
            </template>
            <template v-else-if="settingFor === 'gemini'">
              <GeminiOptions />
            </template>
            <template v-else-if="settingFor === 'grok'">
              <GrokOptions />
            </template>
            <template v-else-if="settingFor === 'glm'">
              <GlmOptions />
            </template>
          </q-scroll-area>
        </div>
      </template>
    </q-splitter>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import QwenOptions from './llms/QwenOptions.vue';
import OllamaOptions from './llms/OllamaOptions.vue';
import MiniMaxOptions from './llms/MiniMaxOptions.vue';
import DeepSeekOptions from './llms/DeepSeekOptions.vue';
import VolcesOptions from './llms/VolcesOptions.vue';
import MoonshotOptions from './llms/MoonshotOptions.vue';
import AnthropicOptions from './llms/AnthropicOptions.vue';
import OpenAIOptions from './llms/OpenAIOptions.vue';
import AzureOpenAIOptions from './llms/AzureOpenAIOptions.vue';
import GeminiOptions from './llms/GeminiOptions.vue';
import GrokOptions from './llms/GrokOptions.vue';
import GlmOptions from './llms/GlmOptions.vue';
import { getResource } from 'src/config/resourceMap';
import { useQuasar } from 'quasar';
import { useUiStore } from 'src/stores/ui';
import { llmProviders } from 'src/config/llmProviders';

const { t: $t } = useI18n({ useScope: 'global' });
const uiStore = useUiStore();
const $q = useQuasar();

const providers = llmProviders();

const splitterModel = ref(240);
// 从 store 获取当前供应商
const settingFor = ref(uiStore.currentLlmProvider || 'qwen');

// 设置供应商
const changeSettingFor = (provider: string) => {
  settingFor.value = provider;
};

const hasEnabled = ref(true);

const checkEnabled = (_llm) => {
  // 检查所有供应商配置（现在llm对象中只包含供应商配置）
  hasEnabled.value = Object.values(_llm).some(
    (provider) =>
      provider && typeof provider === 'object' && 'enabled' in provider && provider.enabled,
  );
};
watch(
  uiStore.perferences.llm,
  () => {
    checkEnabled(uiStore.perferences.llm);
  },
  { deep: true, immediate: true },
);
</script>

<style lang="scss" scoped>
.llm-settings {
  .full-height {
    height: 100%;
  }
}
</style>
