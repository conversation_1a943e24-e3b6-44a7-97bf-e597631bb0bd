import type { Editor } from '@tiptap/vue-3';
import { ref } from 'vue';

/**
 * 工具栏命令模块
 * 负责工具栏配置、按钮管理、菜单项配置等功能
 */

// 定义按钮项的类型
export type ToolbarItem = {
  key: string;
  label?: (...args: boolean[]) => string;
  icon?: string;
  type: 'button' | 'menu' | 'separator::vertical' | 'separator::horizontal' | 'space';
  description?: string;
  tooltip?: string;
  disabled?: () => boolean;
  visible?: () => boolean;
  class?: () => Record<string, boolean>;
  handler?: () => void;
  children?: string[];
  slashCommand?: (payload: { editor: Editor; range: { from: number; to: number } }) => void;
};

// 工具栏配置状态
const toolbarConfig = ref({
  enableCustomization: true,
  showLabels: false,
  compactMode: false,
  theme: 'default' as 'default' | 'minimal' | 'rich',
});

/**
 * 创建工具栏管理器
 */
export const createToolbarManager = (
  getCurrentEditor: () => Editor | null,
  executeCommand: (command: string) => void,
  onColorSelect: (color: string) => void,
  isColorActive: (color: string) => boolean,
  colors: string[]
) => {
  
  // 所有按钮和分割线的配置对象
  const toolbarItems: Record<string, ToolbarItem> = {
    // 基础间距
    space: {
      key: 'space',
      type: 'space',
    },

    // AI功能
    agentWriter: {
      key: 'inkcop',
      label: () => 'InkCop',
      icon: 'auto_awesome',
      type: 'button',
      description: 'InkCop 智能体',
      tooltip: 'AI 续/改 写',
      disabled: () => false,
      visible: () => true,
      handler: () => {
        // AI写作功能
        console.log('启动AI写作功能');
      },
    },

    // 文本格式化
    bold: {
      key: 'bold',
      label: () => '加粗',
      icon: 'format_bold',
      type: 'button',
      description: '加粗文本',
      tooltip: 'Ctrl+B',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('bold') ?? false,
      }),
      handler: () => executeCommand('bold'),
    },

    italic: {
      key: 'italic',
      label: () => '斜体',
      icon: 'format_italic',
      type: 'button',
      description: '斜体文本',
      tooltip: 'Ctrl+I',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('italic') ?? false,
      }),
      handler: () => executeCommand('italic'),
    },

    underline: {
      key: 'underline',
      label: () => '下划线',
      icon: 'format_underlined',
      type: 'button',
      description: '下划线文本',
      tooltip: 'Ctrl+U',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('underline') ?? false,
      }),
      handler: () => executeCommand('underline'),
    },

    strikethrough: {
      key: 'strikethrough',
      label: () => '删除线',
      icon: 'format_strikethrough',
      type: 'button',
      description: '删除线文本',
      tooltip: 'Ctrl+Shift+S',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('strike') ?? false,
      }),
      handler: () => executeCommand('strike'),
    },

    code: {
      key: 'code',
      label: () => '行内代码',
      icon: 'code',
      type: 'button',
      description: '行内代码',
      tooltip: 'Ctrl+`',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('code') ?? false,
      }),
      handler: () => executeCommand('code'),
    },

    // 标题
    heading1: {
      key: 'heading1',
      label: () => 'H1',
      icon: 'title',
      type: 'button',
      description: '一级标题',
      tooltip: 'Ctrl+Alt+1',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('heading', { level: 1 }) ?? false,
      }),
      handler: () => executeCommand('heading1'),
    },

    heading2: {
      key: 'heading2',
      label: () => 'H2',
      icon: 'title',
      type: 'button',
      description: '二级标题',
      tooltip: 'Ctrl+Alt+2',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('heading', { level: 2 }) ?? false,
      }),
      handler: () => executeCommand('heading2'),
    },

    heading3: {
      key: 'heading3',
      label: () => 'H3',
      icon: 'title',
      type: 'button',
      description: '三级标题',
      tooltip: 'Ctrl+Alt+3',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('heading', { level: 3 }) ?? false,
      }),
      handler: () => executeCommand('heading3'),
    },

    // 列表
    bulletList: {
      key: 'bulletList',
      label: () => '无序列表',
      icon: 'format_list_bulleted',
      type: 'button',
      description: '无序列表',
      tooltip: 'Ctrl+Shift+8',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('bulletList') ?? false,
      }),
      handler: () => executeCommand('bulletList'),
    },

    orderedList: {
      key: 'orderedList',
      label: () => '有序列表',
      icon: 'format_list_numbered',
      type: 'button',
      description: '有序列表',
      tooltip: 'Ctrl+Shift+7',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('orderedList') ?? false,
      }),
      handler: () => executeCommand('orderedList'),
    },

    taskList: {
      key: 'taskList',
      label: () => '任务列表',
      icon: 'checklist',
      type: 'button',
      description: '任务列表',
      tooltip: '待办事项',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('taskList') ?? false,
      }),
      handler: () => executeCommand('taskList'),
    },

    // 引用和代码块
    blockquote: {
      key: 'blockquote',
      label: () => '引用',
      icon: 'format_quote',
      type: 'button',
      description: '引用块',
      tooltip: 'Ctrl+Shift+B',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('blockquote') ?? false,
      }),
      handler: () => executeCommand('blockquote'),
    },

    codeBlock: {
      key: 'codeBlock',
      label: () => '代码块',
      icon: 'code_blocks',
      type: 'button',
      description: '代码块',
      tooltip: 'Ctrl+Alt+C',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive('codeBlock') ?? false,
      }),
      handler: () => executeCommand('codeBlock'),
    },

    // 对齐
    alignLeft: {
      key: 'alignLeft',
      label: () => '左对齐',
      icon: 'format_align_left',
      type: 'button',
      description: '左对齐',
      tooltip: 'Ctrl+Shift+L',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive({ textAlign: 'left' }) ?? false,
      }),
      handler: () => executeCommand('alignLeft'),
    },

    alignCenter: {
      key: 'alignCenter',
      label: () => '居中对齐',
      icon: 'format_align_center',
      type: 'button',
      description: '居中对齐',
      tooltip: 'Ctrl+Shift+E',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive({ textAlign: 'center' }) ?? false,
      }),
      handler: () => executeCommand('alignCenter'),
    },

    alignRight: {
      key: 'alignRight',
      label: () => '右对齐',
      icon: 'format_align_right',
      type: 'button',
      description: '右对齐',
      tooltip: 'Ctrl+Shift+R',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      class: () => ({
        'text-primary': getCurrentEditor()?.isActive({ textAlign: 'right' }) ?? false,
      }),
      handler: () => executeCommand('alignRight'),
    },

    // 撤销重做
    undo: {
      key: 'undo',
      label: () => '撤销',
      icon: 'undo',
      type: 'button',
      description: '撤销',
      tooltip: 'Ctrl+Z',
      disabled: () => !getCurrentEditor()?.can().undo(),
      visible: () => true,
      handler: () => executeCommand('undo'),
    },

    redo: {
      key: 'redo',
      label: () => '重做',
      icon: 'redo',
      type: 'button',
      description: '重做',
      tooltip: 'Ctrl+Y',
      disabled: () => !getCurrentEditor()?.can().redo(),
      visible: () => true,
      handler: () => executeCommand('redo'),
    },

    // 分割线
    separator: {
      key: 'separator',
      type: 'separator::vertical',
    },

    // 表格
    insertTable: {
      key: 'insertTable',
      label: () => '插入表格',
      icon: 'table_chart',
      type: 'button',
      description: '插入表格',
      tooltip: '插入3x3表格',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      handler: () => executeCommand('insertTable'),
    },

    // 图片
    insertImage: {
      key: 'insertImage',
      label: () => '插入图片',
      icon: 'image',
      type: 'button',
      description: '插入图片',
      tooltip: '上传或链接图片',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      handler: () => {
        // 图片插入功能
        console.log('插入图片');
      },
    },

    // 链接
    insertLink: {
      key: 'insertLink',
      label: () => '插入链接',
      icon: 'link',
      type: 'button',
      description: '插入链接',
      tooltip: 'Ctrl+K',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      handler: () => {
        // 链接插入功能
        console.log('插入链接');
      },
    },

    // 搜索
    search: {
      key: 'search',
      label: () => '搜索',
      icon: 'search',
      type: 'button',
      description: '搜索替换',
      tooltip: 'Ctrl+F',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      handler: () => {
        // 搜索功能
        console.log('打开搜索面板');
      },
    },

    // 清除格式
    clearFormat: {
      key: 'clearFormat',
      label: () => '清除格式',
      icon: 'format_clear',
      type: 'button',
      description: '清除格式',
      tooltip: 'Ctrl+\\',
      disabled: () => !getCurrentEditor(),
      visible: () => true,
      handler: () => executeCommand('clearFormat'),
    },
  };

  /**
   * 根据类型获取工具栏项
   */
  const getToolbarItemsByType = (type: string): ToolbarItem[] => {
    const typeMap: Record<string, string[]> = {
      basic: ['bold', 'italic', 'underline', 'separator', 'bulletList', 'orderedList'],
      formatting: ['bold', 'italic', 'underline', 'strikethrough', 'code', 'separator', 
                   'heading1', 'heading2', 'heading3', 'separator', 'clearFormat'],
      lists: ['bulletList', 'orderedList', 'taskList'],
      alignment: ['alignLeft', 'alignCenter', 'alignRight'],
      blocks: ['blockquote', 'codeBlock', 'insertTable'],
      media: ['insertImage', 'insertLink'],
      history: ['undo', 'redo'],
      tools: ['search', 'clearFormat'],
      full: [
        'agentWriter', 'separator',
        'bold', 'italic', 'underline', 'strikethrough', 'code', 'separator',
        'heading1', 'heading2', 'heading3', 'separator',
        'bulletList', 'orderedList', 'taskList', 'separator',
        'alignLeft', 'alignCenter', 'alignRight', 'separator',
        'blockquote', 'codeBlock', 'separator',
        'insertTable', 'insertImage', 'insertLink', 'separator',
        'search', 'clearFormat', 'separator',
        'undo', 'redo'
      ],
      minimal: ['bold', 'italic', 'separator', 'bulletList', 'separator', 'undo', 'redo'],
    };

    const itemKeys = typeMap[type] || typeMap.basic;
    return itemKeys.map(key => toolbarItems[key]).filter(Boolean);
  };

  /**
   * 获取颜色选择器项目
   */
  const getColorPickerItems = () => {
    return colors.map(color => ({
      key: `color-${color}`,
      type: 'button' as const,
      style: { backgroundColor: color },
      active: isColorActive(color),
      handler: () => onColorSelect(color),
    }));
  };

  /**
   * 获取斜杠菜单项
   */
  const getSlashMenuItems = () => {
    const slashItems = [
      {
        key: 'heading1',
        title: '标题 1',
        description: '大标题',
        icon: 'title',
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).setHeading({ level: 1 }).run();
        },
      },
      {
        key: 'heading2',
        title: '标题 2',
        description: '中标题',
        icon: 'title',
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).setHeading({ level: 2 }).run();
        },
      },
      {
        key: 'heading3',
        title: '标题 3',
        description: '小标题',
        icon: 'title',
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).setHeading({ level: 3 }).run();
        },
      },
      {
        key: 'bulletList',
        title: '无序列表',
        description: '创建无序列表',
        icon: 'format_list_bulleted',
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).toggleBulletList().run();
        },
      },
      {
        key: 'orderedList',
        title: '有序列表',
        description: '创建有序列表',
        icon: 'format_list_numbered',
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).toggleOrderedList().run();
        },
      },
      {
        key: 'taskList',
        title: '任务列表',
        description: '创建待办事项',
        icon: 'checklist',
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).toggleTaskList().run();
        },
      },
      {
        key: 'blockquote',
        title: '引用',
        description: '创建引用块',
        icon: 'format_quote',
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).toggleBlockquote().run();
        },
      },
      {
        key: 'codeBlock',
        title: '代码块',
        description: '创建代码块',
        icon: 'code_blocks',
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).toggleCodeBlock().run();
        },
      },
      {
        key: 'table',
        title: '表格',
        description: '插入表格',
        icon: 'table_chart',
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
        },
      },
      {
        key: 'horizontalRule',
        title: '分割线',
        description: '插入分割线',
        icon: 'horizontal_rule',
        // 需要HorizontalRule扩展
        // command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
        //   editor.chain().focus().deleteRange(range).setHorizontalRule().run();
        // },
        command: ({ editor, range }: { editor: Editor; range: { from: number; to: number } }) => {
          editor.chain().focus().deleteRange(range).insertContent('<hr>').run();
        },
      },
    ];

    return slashItems;
  };

  /**
   * 自定义工具栏配置
   */
  const customizeToolbar = (customItems: string[]) => {
    return customItems.map(key => toolbarItems[key]).filter(Boolean);
  };

  /**
   * 获取工具栏配置
   */
  const getToolbarConfig = () => toolbarConfig.value;

  /**
   * 更新工具栏配置
   */
  const updateToolbarConfig = (config: Partial<typeof toolbarConfig.value>) => {
    toolbarConfig.value = { ...toolbarConfig.value, ...config };
  };

  return {
    toolbarItems,
    getToolbarItemsByType,
    getColorPickerItems,
    getSlashMenuItems,
    customizeToolbar,
    getToolbarConfig,
    updateToolbarConfig,
  };
};

export default {
  // ToolbarItem, // 类型不应该作为值导出
  createToolbarManager,
};