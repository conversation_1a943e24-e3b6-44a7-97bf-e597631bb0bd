<template>
  <div class="base-settings q-pa-xl">
    <div class="text-h6 q-mb-md">{{ $t('src.components.settings.BaseSettings.title') }}</div>

    <!-- 语言设置 -->
    <div class="setting-item q-mb-lg">
      <div class="text-subtitle2 q-mb-sm">
        {{ $t('src.components.settings.BaseSettings.interfaceLanguage') }}
      </div>
      <q-select
        v-model="currentLanguage"
        :options="localeOptions"
        :label="$t('settings.selectLanguage')"
        outlined
        emit-value
        map-options
        options-dense
        style="min-width: 200px"
        @update:model-value="onLanguageChange"
      />
      <div class="text-caption text-grey-6 q-mt-xs">
        {{ $t('src.components.settings.BaseSettings.languageDescription') }}
      </div>
    </div>

    <div class="setting-item q-mb-lg">
      <div class="text-subtitle2 q-mb-sm">
        {{ $t('src.components.settings.BaseSettings.interfaceTheme') }}
      </div>
      <q-select
        v-model="currentTheme"
        :options="themeOptions"
        :label="$t('settings.selectTheme')"
        outlined
        emit-value
        map-options
        options-dense
        style="min-width: 200px"
        @update:model-value="onThemeChange"
      />
      <div class="text-caption text-grey-6 q-mt-sm">
        {{ $t('src.components.settings.BaseSettings.themeDescription') }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });
const store = useUiStore();
const $q = useQuasar();

const localeOptions = [
  { value: 'en-US', label: 'English (英语)' },
  { value: 'zh-CN', label: '中文 (Chinese)' },
];
const themeOptions = [
  { value: 'light', label: $t('theme_light') },
  { value: 'dark', label: $t('theme_dark') },
];

// 从store获取当前语言设置
const currentLanguage = computed({
  get: () => store.perferences.base.language,
  set: (value: string) => {
    store.perferences.base.language = value;
  },
});
const currentTheme = computed({
  get: () => store.perferences.base.theme,
  set: (value: 'light' | 'dark') => {
    store.perferences.base.theme = value;
  },
});

// 语言变更处理
const onLanguageChange = async (newLanguage: string) => {
  try {
    // 更新store中的设置（会自动保存到数据库）
    currentLanguage.value = newLanguage;

    // 同时更新localStorage，确保下次启动时能正确初始化
    try {
      localStorage.setItem('app_language', newLanguage);
      console.log('[BaseSettings] 语言设置已保存到localStorage:', newLanguage);
    } catch (error) {
      console.warn('[BaseSettings] 无法保存语言设置到localStorage:', error);
    }

    // 直接更新 i18n 语言设置
    try {
      // 尝试通过 i18n 实例直接设置
      const { i18nInstance } = await import('src/boot/i18n');
      if (i18nInstance && i18nInstance.global) {
        const globalInstance = i18nInstance.global as unknown as {
          locale: { value: string };
        };
        if (globalInstance.locale) {
          globalInstance.locale.value = newLanguage;
          console.log('[BaseSettings] 已直接更新i18n语言设置为:', newLanguage);
        }
      }
    } catch (i18nError) {
      console.warn('[BaseSettings] 无法直接更新i18n语言设置:', i18nError);
    }

    // 显示成功提示
    $q.notify({
      type: 'positive',
      message: $t('settings.languageSaved'),
      position: 'top',
    });

    console.log('[BaseSettings] 语言已更改为:', newLanguage);
  } catch (error) {
    console.error('[BaseSettings] 语言设置失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('settings.languageSaveFailed'),
      position: 'top',
    });
  }
};

const onThemeChange = (newTheme: 'light' | 'dark') => {
  try {
    // 更新store中的设置（会自动保存到数据库）
    currentTheme.value = newTheme;

    // // 更新Quasar的暗色模式
    const isDark = newTheme === 'dark';
    $q.dark.set(isDark);

    // 直接调用Qt端的主题切换方法
    if (window.qtWindow?.setThemeDirectly) {
      window.qtWindow.setThemeDirectly(isDark);
    }

    console.log('[BaseSettings] 主题设置已更新:', newTheme);
  } catch (error) {
    console.error('[BaseSettings] 主题设置失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.settings.BaseSettings.interfaceThemeSaveFailed'),
      position: 'top',
    });
  }
};

// 组件挂载时确保设置已加载（语言设置在应用启动时已经应用）
onMounted(async () => {
  try {
    // 确保设置已加载
    await store.loadSettings();
    console.log('[BaseSettings] 设置已加载，当前语言:', store.perferences.base.language);
  } catch (error) {
    console.error('[BaseSettings] 加载设置失败:', error);
  }
});
</script>

<style lang="scss" scoped>
.base-settings {
  max-width: 600px;

  .setting-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    padding-bottom: 16px;

    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
  }
}

.body--dark .base-settings .setting-item {
  border-bottom-color: rgba(255, 255, 255, 0.12);
}
</style>
