import { Editor } from '@tiptap/core';
import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import Heading from '@tiptap/extension-heading';
import Bold from '@tiptap/extension-bold';
import Italic from '@tiptap/extension-italic';
import Strike from '@tiptap/extension-strike';
import Underline from '@tiptap/extension-underline';
import Code from '@tiptap/extension-code';
import TextStyle from '@tiptap/extension-text-style';
import Gapcursor from '@tiptap/extension-gapcursor';
import FontFamily from '@tiptap/extension-font-family';
import Color from '@tiptap/extension-color';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import Blockquote from '@tiptap/extension-blockquote';
import ListItem from '@tiptap/extension-list-item';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import Highlight from '@tiptap/extension-highlight';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import History from '@tiptap/extension-history';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import Placeholder from '@tiptap/extension-placeholder';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableHeader from '@tiptap/extension-table-header';
import Dropcursor from '@tiptap/extension-dropcursor';
import { Markdown } from 'tiptap-markdown';
import { common, createLowlight } from 'lowlight';
import { SearchAndReplace } from '@sereneinserenade/tiptap-search-and-replace';

// 导入自定义扩展
import { TrackChange } from '../components/tiptap/extensions/TrackChange';
import Mathematics from '@tiptap-pro/extension-mathematics';
import Details from '@tiptap-pro/extension-details';
import DetailsSummary from '@tiptap-pro/extension-details-summary';
import DetailsContent from '@tiptap-pro/extension-details-content';
import UniqueID from '@tiptap-pro/extension-unique-id';
import TableOfContents from '@tiptap-pro/extension-table-of-contents';
import FileHandler from '@tiptap-pro/extension-file-handler';
import NodeRange from '@tiptap-pro/extension-node-range';
import Emoji from '@tiptap-pro/extension-emoji';

import { CustomTableCell, ImageWithId } from '../components/tiptap/tiptap';
import { AgentWriter } from '../components/tiptap/extensions/AgentWriter';
import { AutoComplete } from '../components/tiptap/AutoCompleteExtension';
import { MermaidExtension } from '../components/tiptap/extensions/MermaidExtension';
import { ExcalidrawExtension } from '../components/tiptap/extensions/ExcalidrawExtension';
import slashCommands from '../components/tiptap/slash/commands.js';
import slashSuggestion from '../components/tiptap/slash/suggestion.js';
import suggestion from '../components/tiptap/emoji/suggestion';
import { $t } from 'src/composables/useTrans';

// 创建 lowlight 实例
const lowlight = createLowlight(common);

/**
 * 编辑器配置选项
 */
export interface EditorFactoryOptions {
  /** 文档ID，用于图片扩展 */
  docId?: number;
  /** 是否启用自动补全 */
  enableAutoComplete?: boolean;
  /** 文档标题，用于自动补全 */
  documentTitle?: string;
  /** 是否为只读模式 */
  editable?: boolean;
  /** 初始内容 */
  content?: string | object;
  /** 是否自动聚焦 */
  autofocus?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 自动补全回调 */
  onSuggestionSelected?: (text: string) => void;
}

/**
 * 创建标准的 TipTap 编辑器实例
 * 包含项目中使用的所有扩展配置
 */
export function createStandardEditor(options: EditorFactoryOptions = {}): Editor {
  const {
    docId,
    enableAutoComplete = false,
    documentTitle = '',
    editable = true,
    content = '',
    autofocus = false,
    placeholder = $t('src.utils.editorFactory.placeholder'),
    onSuggestionSelected,
  } = options;

  return new Editor({
    content,
    autofocus,
    editable,
    extensions: [
      Document,
      TrackChange.configure({
        HTMLAttributes: {
          class: 'track-change',
        },
      }),
      Mathematics.configure({
        shouldRender: (state, pos, node) => {
          const $pos = state.doc.resolve(pos);
          return node.type.name === 'text' && $pos.parent.type.name !== 'codeBlock';
        },
        katexOptions: {
          maxSize: 300,
        },
      }),
      Emoji.configure({
        suggestion,
      }),
      Details.configure({
        persist: true,
        openClassName: 'is-open',
        HTMLAttributes: {
          class: 'details radius-xs border',
        },
      }),
      DetailsSummary,
      DetailsContent,
      Heading.configure({
        levels: [1, 2, 3, 4, 5, 6],
        HTMLAttributes: {
          class: 'heading',
        },
      }),
      Paragraph,
      Text,
      Bold,
      Italic,
      Strike,
      Underline,
      Code,
      TextStyle,
      Gapcursor,
      FontFamily,
      Color.configure({
        types: ['textStyle'],
      }),
      CodeBlockLowlight.configure({
        lowlight,
        HTMLAttributes: {
          class: 'code-block',
        },
      }),
      Blockquote,
      ListItem,
      BulletList,
      OrderedList,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      CustomTableCell,
      TableHeader,
      NodeRange.configure({
        key: null,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      Subscript,
      Superscript,
      ImageWithId.configure({
        HTMLAttributes: {
          class: 'editor-image',
        },
        documentId: docId,
      }),
      Placeholder.configure({
        placeholder,
      }),
      History.configure({
        depth: 100,
        newGroupDelay: 500,
      }),
      UniqueID.configure({
        types: [
          'heading',
          'paragraph',
          'bulletList',
          'orderedList',
          'listItem',
          'table',
          'tableRow',
          'tableCell',
          'tableHeader',
          'blockquote',
          'codeBlock',
          'image',
          'ImageWithId',
          'taskList',
          'taskItem',
          'mathematics',
          'mermaid',
          'excalidraw',
        ],
      }),
      TableOfContents.configure({
        HTMLAttributes: {
          class: 'table-of-contents',
        },
      }),
      FileHandler.configure({
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
      }),
      Dropcursor,
      Markdown.configure({
        html: true,
        tightLists: true,
        tightListClass: 'tight',
        bulletListMarker: '-',
        linkify: false,
        breaks: false,
        transformPastedText: true,
        transformCopiedText: true,
      }),
      slashCommands.configure({
        suggestion: slashSuggestion,
      }),
      AgentWriter.configure({
        HTMLAttributes: {
          class: 'agent-writer',
        },
      }),
      AutoComplete.configure({
        enabled: enableAutoComplete,
        triggerLength: 10,
        debounceTime: 500,
        docId: docId,
        documentTitle: documentTitle,
        onSuggestionSelected: onSuggestionSelected || (() => {}),
      }),
      MermaidExtension.configure({
        HTMLAttributes: {
          class: 'mermaid-node',
        },
      }),
      ExcalidrawExtension.configure({
        HTMLAttributes: {
          class: 'excalidraw-node',
        },
      }),
      SearchAndReplace.configure(),
    ],
  });
}

/**
 * 获取标准编辑器扩展配置
 * 用于 useEditor 等 Vue 组合式 API
 */
export function getStandardEditorExtensions(options: EditorFactoryOptions = {}) {
  const {
    docId,
    enableAutoComplete = false,
    documentTitle = '',
    placeholder = $t('src.utils.editorFactory.placeholder'),
    onSuggestionSelected,
  } = options;

  return [
    Document,
    TrackChange.configure({
      HTMLAttributes: {
        class: 'track-change',
      },
    }),
    Mathematics.configure({
      shouldRender: (state, pos, node) => {
        const $pos = state.doc.resolve(pos);
        return node.type.name === 'text' && $pos.parent.type.name !== 'codeBlock';
      },
      katexOptions: {
        maxSize: 300,
      },
    }),
    Emoji.configure({
      suggestion,
    }),
    Details.configure({
      persist: true,
      openClassName: 'is-open',
      HTMLAttributes: {
        class: 'details radius-xs border',
      },
    }),
    DetailsSummary,
    DetailsContent,
    Heading.configure({
      levels: [1, 2, 3, 4, 5, 6],
      HTMLAttributes: {
        class: 'heading',
      },
    }),
    Paragraph,
    Text,
    Bold,
    Italic,
    Strike,
    Underline,
    Code,
    TextStyle,
    Gapcursor,
    FontFamily,
    Color.configure({
      types: ['textStyle'],
    }),
    CodeBlockLowlight.configure({
      lowlight,
      HTMLAttributes: {
        class: 'code-block',
      },
    }),
    Blockquote,
    ListItem,
    BulletList,
    OrderedList,
    TaskList,
    TaskItem.configure({
      nested: true,
    }),
    Table.configure({
      resizable: true,
    }),
    TableRow,
    CustomTableCell,
    TableHeader,
    NodeRange.configure({
      key: null,
    }),
    Link.configure({
      openOnClick: false,
      HTMLAttributes: {
        class: 'text-primary',
      },
    }),
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    Highlight.configure({
      multicolor: true,
    }),
    Subscript,
    Superscript,
    ImageWithId.configure({
      HTMLAttributes: {
        class: 'editor-image',
      },
      documentId: docId,
    }),
    Placeholder.configure({
      placeholder,
    }),
    History.configure({
      depth: 100,
      newGroupDelay: 500,
    }),
    UniqueID.configure({
      types: [
        'heading',
        'paragraph',
        'bulletList',
        'orderedList',
        'listItem',
        'table',
        'tableRow',
        'tableCell',
        'tableHeader',
        'blockquote',
        'codeBlock',
        'image',
        'ImageWithId',
        'taskList',
        'taskItem',
        'mathematics',
        'mermaid',
        'excalidraw',
      ],
    }),
    TableOfContents.configure({
      HTMLAttributes: {
        class: 'table-of-contents',
      },
    }),
    FileHandler.configure({
      allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
    }),
    Dropcursor,
    Markdown.configure({
      html: true,
      tightLists: true,
      tightListClass: 'tight',
      bulletListMarker: '-',
      linkify: false,
      breaks: false,
      transformPastedText: true,
      transformCopiedText: true,
    }),
    slashCommands.configure({
      suggestion: slashSuggestion,
    }),
    AgentWriter.configure({
      HTMLAttributes: {
        class: 'agent-writer',
      },
    }),
    AutoComplete.configure({
      enabled: enableAutoComplete,
      triggerLength: 10,
      debounceTime: 500,
      docId: docId,
      documentTitle: documentTitle,
      onSuggestionSelected: onSuggestionSelected || (() => {}),
    }),
    MermaidExtension.configure({
      HTMLAttributes: {
        class: 'mermaid-node',
      },
    }),
    ExcalidrawExtension.configure({
      HTMLAttributes: {
        class: 'excalidraw-node',
      },
    }),
    SearchAndReplace.configure(),
  ];
}

/**
 * 创建用于内容转换的轻量级编辑器
 * 只包含必要的扩展，用于 JSON 到 Markdown 的转换
 */
export function createConverterEditor(): Editor {
  return createStandardEditor({
    editable: false,
    autofocus: false,
  });
}
