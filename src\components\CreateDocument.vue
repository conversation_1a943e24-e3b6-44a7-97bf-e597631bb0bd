<template>
  <q-input
    ref="inputRef"
    v-model="documentName"
    :placeholder="$t('src.components.CreateDocument.title')"
    dense
    autofocus
    square
    filled
    color="primary"
    hide-bottom-space
    input-class="items-center"
    class="border input-pr-none"
    @keydown.enter="handleEnterKey"
    @keydown.esc="emit('cancel')"
    @compositionstart="isComposing = true"
    @compositionend="isComposing = false"
  >
    <template v-if="documentName.trim().length > 0">
      <div class="flex flex-center q-pr-sm">
        <q-btn icon="mdi-check" dense size="sm" round flat @click="createDocumentHandler" />
      </div>
    </template>
    <template v-else>
      <div class="flex flex-center q-pr-sm">
        <q-btn icon="close" dense size="sm" round flat @click="emit('cancel')" />
      </div>
    </template>
  </q-input>
</template>
<script setup lang="ts">
import { ref, onMounted, nextTick, onBeforeUnmount, useTemplateRef } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import type { Document } from 'src/types/doc';
import { QInput } from 'quasar';
import { useUiStore } from 'src/stores/ui';
import { useDocStore } from 'src/stores/doc';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

const docStore = useDocStore();
const uiStore = useUiStore();

const { parentId } = defineProps<{
  parentId: number | null;
}>();

const emit = defineEmits<{
  (e: 'documentCreated', doc: Document): void;
  (e: 'cancel'): void;
}>();

const documentName = ref('');
const isComposing = ref(false);
const inputRef = useTemplateRef('inputRef');

// 组件挂载后手动聚焦输入框
onMounted(async () => {
  await nextTick();
  if (inputRef.value) {
    // console.log('聚焦输入框');
    inputRef.value.focus();
  }
  uiStore.lowlight = true;
});
onBeforeUnmount(() => {
  uiStore.lowlight = false;
});

const handleEnterKey = (event: KeyboardEvent) => {
  // 如果正在进行中文输入法组合输入，不触发创建文档事件
  if (isComposing.value) {
    return;
  }

  // 阻止默认行为并触发创建文档
  event.preventDefault();
  void createDocumentHandler();
};
const createDocumentHandler = async () => {
  if (!documentName.value) return;
  try {
    const sqlite = useSqlite();
    // Tiptap 的空文档json结构
    const content = docStore.tiptapEmptyContent;
    const docId = await sqlite.createDocument(documentName.value, content, parentId);
    const newDoc = await sqlite.getDocument(docId);
    emit('documentCreated', newDoc);
    documentName.value = '';
  } catch (error) {
    console.error('创建文档失败:', error);
  }
};
</script>
