import { Mark, mergeAttributes } from '@tiptap/core';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    trackChange: {
      /**
       * 添加修改标记
       */
      setTrackChange: (attributes: {
        type: 'insertion' | 'deletion' | 'formatting';
        author: string;
        timestamp: number;
        status: 'pending' | 'accepted' | 'rejected';
        comment?: string;
      }) => ReturnType;
      /**
       * 接受修改
       */
      acceptTrackChange: () => ReturnType;
      /**
       * 拒绝修改
       */
      rejectTrackChange: () => ReturnType;
    };
  }
}

export const TrackChange = Mark.create({
  name: 'trackChange',

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  addAttributes() {
    return {
      type: {
        default: 'insertion',
        parseHTML: (element) => element.getAttribute('data-track-change-type'),
        renderHTML: (attributes) => {
          return {
            'data-track-change-type': attributes.type,
          };
        },
      },
      author: {
        default: '',
        parseHTML: (element) => element.getAttribute('data-track-change-author'),
        renderHTML: (attributes) => {
          return {
            'data-track-change-author': attributes.author,
          };
        },
      },
      timestamp: {
        default: Date.now(),
        parseHTML: (element) => Number(element.getAttribute('data-track-change-timestamp')),
        renderHTML: (attributes) => {
          return {
            'data-track-change-timestamp': attributes.timestamp,
          };
        },
      },
      status: {
        default: 'pending',
        parseHTML: (element) => element.getAttribute('data-track-change-status'),
        renderHTML: (attributes) => {
          return {
            'data-track-change-status': attributes.status,
          };
        },
      },
      comment: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-track-change-comment'),
        renderHTML: (attributes) => {
          if (!attributes.comment) return {};
          return {
            'data-track-change-comment': attributes.comment,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-track-change]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'span',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, { 'data-track-change': '' }),
      0,
    ];
  },

  addCommands() {
    return {
      setTrackChange:
        (attributes) =>
        ({ chain }) => {
          return chain().setMark(this.name, attributes).run();
        },
      acceptTrackChange:
        () =>
        ({ chain }) => {
          return chain()
            .command(({ tr }) => {
              tr.doc.descendants((node, pos) => {
                if (node.marks.some((mark) => mark.type.name === this.name)) {
                  node.marks.forEach((mark) => {
                    if (mark.type.name === this.name) {
                      tr.addMark(
                        pos,
                        pos + node.nodeSize,
                        this.type.create({
                          ...mark.attrs,
                          status: 'accepted',
                        }),
                      );
                    }
                  });
                }
                return true;
              });
              return true;
            })
            .run();
        },
      rejectTrackChange:
        () =>
        ({ chain }) => {
          return chain()
            .command(({ tr }) => {
              tr.doc.descendants((node, pos) => {
                if (node.marks.some((mark) => mark.type.name === this.name)) {
                  node.marks.forEach((mark) => {
                    if (mark.type.name === this.name) {
                      tr.addMark(
                        pos,
                        pos + node.nodeSize,
                        this.type.create({
                          ...mark.attrs,
                          status: 'rejected',
                        }),
                      );
                    }
                  });
                }
                return true;
              });
              return true;
            })
            .run();
        },
    };
  },
});
