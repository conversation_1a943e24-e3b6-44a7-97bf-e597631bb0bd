// Code generated by ObjectBox; DO NOT EDIT.

#pragma once

#include <cstdbool>
#include <cstdint>

#include "flatbuffers/flatbuffers.h"
#include "objectbox.h"
#include "objectbox.hpp"

namespace InkCop
{
    namespace Knowledge
    {
        struct KnowledgeBase_;

        struct KnowledgeBase
        {
            obx_id id;
            std::string name;
            std::string description;
            std::string user_id;
            uint64_t created_at;
            uint64_t updated_at;

            struct _OBX_MetaInfo
            {
                static constexpr obx_schema_id entityId() { return 1; }

                static void setObjectId(KnowledgeBase &object, obx_id newId) { object.id = newId; }

                /// Write given object to the FlatBufferBuilder
                static void toFlatBuffer(flatbuffers::FlatBufferBuilder &fbb, const KnowledgeBase &object);

                /// Read an object from a valid FlatBuffer
                static KnowledgeBase fromFlatBuffer(const void *data, size_t size);

                /// Read an object from a valid FlatBuffer
                static std::unique_ptr<KnowledgeBase> newFromFlatBuffer(const void *data, size_t size);

                /// Read an object from a valid FlatBuffer
                static void fromFlatBuffer(const void *data, size_t size, KnowledgeBase &outObject);
            };
        };

        struct KnowledgeBase_
        {
            static const obx::Property<KnowledgeBase, OBXPropertyType_Long> id;
            static const obx::Property<KnowledgeBase, OBXPropertyType_String> name;
            static const obx::Property<KnowledgeBase, OBXPropertyType_String> description;
            static const obx::Property<KnowledgeBase, OBXPropertyType_String> user_id;
            static const obx::Property<KnowledgeBase, OBXPropertyType_Long> created_at;
            static const obx::Property<KnowledgeBase, OBXPropertyType_Long> updated_at;
        };
    } // namespace Knowledge
} // namespace InkCop

namespace InkCop
{
    namespace Knowledge
    {
        struct KnowledgeChunk_;

        struct KnowledgeChunk
        {
            obx_id id;
            uint64_t knowledge_document_id;
            uint32_t chunk_index;
            std::string content;
            std::vector<float> embedding;
            std::string metadata;
            uint64_t created_at;
            bool is_vectorized;

            struct _OBX_MetaInfo
            {
                static constexpr obx_schema_id entityId() { return 2; }

                static void setObjectId(KnowledgeChunk &object, obx_id newId) { object.id = newId; }

                /// Write given object to the FlatBufferBuilder
                static void toFlatBuffer(flatbuffers::FlatBufferBuilder &fbb, const KnowledgeChunk &object);

                /// Read an object from a valid FlatBuffer
                static KnowledgeChunk fromFlatBuffer(const void *data, size_t size);

                /// Read an object from a valid FlatBuffer
                static std::unique_ptr<KnowledgeChunk> newFromFlatBuffer(const void *data, size_t size);

                /// Read an object from a valid FlatBuffer
                static void fromFlatBuffer(const void *data, size_t size, KnowledgeChunk &outObject);
            };
        };

        struct KnowledgeChunk_
        {
            static const obx::Property<KnowledgeChunk, OBXPropertyType_Long> id;
            static const obx::Property<KnowledgeChunk, OBXPropertyType_Long> knowledge_document_id;
            static const obx::Property<KnowledgeChunk, OBXPropertyType_Int> chunk_index;
            static const obx::Property<KnowledgeChunk, OBXPropertyType_String> content;
            static const obx::Property<KnowledgeChunk, OBXPropertyType_FloatVector> embedding;
            static const obx::Property<KnowledgeChunk, OBXPropertyType_String> metadata;
            static const obx::Property<KnowledgeChunk, OBXPropertyType_Long> created_at;
            static const obx::Property<KnowledgeChunk, OBXPropertyType_Bool> is_vectorized;
        };
    } // namespace Knowledge
} // namespace InkCop

namespace InkCop
{
    namespace Knowledge
    {
        struct KnowledgeDocument_;

        struct KnowledgeDocument
        {
            obx_id id;
            uint64_t kb_id;
            std::string title;
            std::string content;
            std::string document_type;
            std::string metadata;
            uint64_t created_at;
            uint64_t updated_at;

            struct _OBX_MetaInfo
            {
                static constexpr obx_schema_id entityId() { return 3; }

                static void setObjectId(KnowledgeDocument &object, obx_id newId) { object.id = newId; }

                /// Write given object to the FlatBufferBuilder
                static void toFlatBuffer(flatbuffers::FlatBufferBuilder &fbb, const KnowledgeDocument &object);

                /// Read an object from a valid FlatBuffer
                static KnowledgeDocument fromFlatBuffer(const void *data, size_t size);

                /// Read an object from a valid FlatBuffer
                static std::unique_ptr<KnowledgeDocument> newFromFlatBuffer(const void *data, size_t size);

                /// Read an object from a valid FlatBuffer
                static void fromFlatBuffer(const void *data, size_t size, KnowledgeDocument &outObject);
            };
        };

        struct KnowledgeDocument_
        {
            static const obx::Property<KnowledgeDocument, OBXPropertyType_Long> id;
            static const obx::Property<KnowledgeDocument, OBXPropertyType_Long> kb_id;
            static const obx::Property<KnowledgeDocument, OBXPropertyType_String> title;
            static const obx::Property<KnowledgeDocument, OBXPropertyType_String> content;
            static const obx::Property<KnowledgeDocument, OBXPropertyType_String> document_type;
            static const obx::Property<KnowledgeDocument, OBXPropertyType_String> metadata;
            static const obx::Property<KnowledgeDocument, OBXPropertyType_Long> created_at;
            static const obx::Property<KnowledgeDocument, OBXPropertyType_Long> updated_at;
        };
    } // namespace Knowledge
} // namespace InkCop

namespace InkCop
{
    namespace Knowledge
    {
        struct KnowledgeQuery_;

        struct KnowledgeQuery
        {
            obx_id id;
            uint64_t knowledge_base_id;
            std::string query_text;
            std::vector<float> query_embedding;
            std::string results;
            uint64_t created_at;

            struct _OBX_MetaInfo
            {
                static constexpr obx_schema_id entityId() { return 4; }

                static void setObjectId(KnowledgeQuery &object, obx_id newId) { object.id = newId; }

                /// Write given object to the FlatBufferBuilder
                static void toFlatBuffer(flatbuffers::FlatBufferBuilder &fbb, const KnowledgeQuery &object);

                /// Read an object from a valid FlatBuffer
                static KnowledgeQuery fromFlatBuffer(const void *data, size_t size);

                /// Read an object from a valid FlatBuffer
                static std::unique_ptr<KnowledgeQuery> newFromFlatBuffer(const void *data, size_t size);

                /// Read an object from a valid FlatBuffer
                static void fromFlatBuffer(const void *data, size_t size, KnowledgeQuery &outObject);
            };
        };

        struct KnowledgeQuery_
        {
            static const obx::Property<KnowledgeQuery, OBXPropertyType_Long> id;
            static const obx::Property<KnowledgeQuery, OBXPropertyType_Long> knowledge_base_id;
            static const obx::Property<KnowledgeQuery, OBXPropertyType_String> query_text;
            static const obx::Property<KnowledgeQuery, OBXPropertyType_FloatVector> query_embedding;
            static const obx::Property<KnowledgeQuery, OBXPropertyType_String> results;
            static const obx::Property<KnowledgeQuery, OBXPropertyType_Long> created_at;
        };
    } // namespace Knowledge
} // namespace InkCop
