Name:           inkcop
Version:        0.0.1
Release:        1%{?dist}
Summary:        A copilot app for writer

License:        MIT
URL:            https://github.com/yourusername/inkcop
Source0:        %{name}-%{version}.tar.gz
BuildRequires:  cmake
BuildRequires:  gcc-c++
BuildRequires:  qt6-qtbase-devel
BuildRequires:  qt6-qtwebengine-devel
BuildRequires:  sqlite-devel
BuildRequires:  flatbuffers-devel
BuildRequires:  flatbuffers-compiler
BuildRequires:  patchelf
# nodejs 和 npm 仅在预构建阶段需要，RPM 构建时使用预构建的前端资源
# BuildRequires:  nodejs
# BuildRequires:  npm

Requires:       qt6-qtbase
Requires:       qt6-qtwebengine
Requires:       sqlite

%description
InkCop is a copilot app for writer, built with Qt and Quasar Framework.

%prep
%autosetup -p1

%build
# 检查预构建的前端资源
if [ ! -d "dist/spa" ]; then
    echo "错误: 未找到预构建的前端资源 dist/spa"
    echo "请在打包前运行: npm run build 或 bun run build"
    exit 1
fi

echo "✅ 使用预构建的前端资源: dist/spa"

# 清理 build 目录
rm -rf build

# 构建 Qt 应用
mkdir -p build
cd build
cmake -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/usr ..
cmake --build . -j$(nproc)

%install
# 创建必要的目录
mkdir -p %{buildroot}%{_bindir}
mkdir -p %{buildroot}%{_libdir}
mkdir -p %{buildroot}%{_datadir}/inkcop
mkdir -p %{buildroot}%{_datadir}/applications
mkdir -p %{buildroot}%{_datadir}/icons/hicolor/512x512/apps

# 安装可执行文件
install -m 755 build/bin/InkCop %{buildroot}%{_bindir}/

# 查找并安装 ObjectBox 库
OBJECTBOX_LIB=$(find build/_deps/objectbox-download-src -name "libobjectbox.so" 2>/dev/null | head -1)
if [ -n "$OBJECTBOX_LIB" ] && [ -f "$OBJECTBOX_LIB" ]; then
    echo "找到 ObjectBox 库: $OBJECTBOX_LIB"
    install -m 755 "$OBJECTBOX_LIB" %{buildroot}%{_libdir}/
    # 设置 RPATH 指向系统库目录
    patchelf --set-rpath %{_libdir} %{buildroot}%{_bindir}/InkCop || true
else
    echo "警告: 未找到 ObjectBox 库，尝试清理 RPATH"
    patchelf --remove-rpath %{buildroot}%{_bindir}/InkCop || true
fi

# 安装桌面文件
cat > %{buildroot}%{_datadir}/applications/inkcop.desktop << EOF
[Desktop Entry]
Name=InkCop
Comment=A copilot app for writer
Exec=InkCop
Icon=inkcop
Terminal=false
Type=Application
Categories=Office;
StartupWMClass=InkCop
EOF

# 安装图标
install -m 644 public/icons/icon-512x512.png %{buildroot}%{_datadir}/icons/hicolor/512x512/apps/inkcop.png

%files
%{_bindir}/InkCop
%{_libdir}/libobjectbox.so*
%{_datadir}/applications/inkcop.desktop
%{_datadir}/icons/hicolor/512x512/apps/inkcop.png

%changelog
* %(date "+%a %b %d %Y") %{packager} - %{version}-%{release}
- Initial release 