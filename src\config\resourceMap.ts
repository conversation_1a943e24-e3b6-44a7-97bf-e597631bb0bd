import { $t } from 'src/composables/useTrans';

export const getResource = (key: string) => {
  // TODO 集中管理静态资源
  const resources = [
    {
      key: 'qwen',
      name: $t('src.config.resourceMap.name.qwen'),
      description: $t('src.config.resourceMap.description.qwen'),
      icon: {
        light: './icons/llm/qwen.svg',
        dark: './icons/llm/qwen.webp',
      },
    },
    {
      key: 'ollama',
      name: $t('src.config.resourceMap.name.ollama'),
      description: $t('src.config.resourceMap.description.ollama'),
      icon: {
        light: './icons/llm/ollama.svg',
        dark: './icons/llm/ollama-white.svg',
      },
    },
    {
      key: 'minimax',
      name: $t('src.config.resourceMap.name.minimax'),
      description: $t('src.config.resourceMap.description.minimax'),
      icon: {
        light: './icons/llm/minimax-color.svg',
        dark: './icons/llm/minimax-color.svg',
      },
    },
    {
      key: 'deepseek',
      name: $t('src.config.resourceMap.name.deepseek'),
      description: $t('src.config.resourceMap.description.deepseek'),
      icon: {
        light: './icons/llm/deepseek-color.svg',
        dark: './icons/llm/deepseek-color.svg',
      },
    },
    {
      key: 'volces',
      name: $t('src.config.resourceMap.name.volces'),
      description: $t('src.config.resourceMap.description.volces'),
      icon: {
        light: './icons/llm/doubao-color.svg',
        dark: './icons/llm/doubao-color.svg',
      },
    },
    {
      key: 'moonshot',
      name: $t('src.config.resourceMap.name.moonshot'),
      description: $t('src.config.resourceMap.description.moonshot'),
      icon: {
        light: './icons/llm/moonshot.svg',
        dark: './icons/llm/moonshot-white.svg',
      },
    },
    {
      key: 'anthropic',
      name: $t('src.config.resourceMap.name.anthropic'),
      description: $t('src.config.resourceMap.description.anthropic'),
      icon: {
        light: './icons/llm/anthropic.svg',
        dark: './icons/llm/anthropic-white.svg',
      },
    },
    {
      key: 'openai',
      name: $t('src.config.resourceMap.name.openai'),
      description: $t('src.config.resourceMap.description.openai'),
      icon: {
        light: './icons/llm/openai.svg',
        dark: './icons/llm/openai-white.svg',
      },
    },
    {
      key: 'azureOpenai',
      name: $t('src.config.resourceMap.name.azureOpenai'),
      description: $t('src.config.resourceMap.description.azureOpenai'),
      icon: {
        light: './icons/llm/azureai-color.svg',
        dark: './icons/llm/azureai-color.svg',
      },
    },
    {
      key: 'gemini',
      name: $t('src.config.resourceMap.name.gemini'),
      description: $t('src.config.resourceMap.description.gemini'),
      icon: {
        light: './icons/llm/gemini.svg',
        dark: './icons/llm/gemini.svg',
      },
    },
    {
      key: 'grok',
      name: $t('src.config.resourceMap.name.grok'),
      description: $t('src.config.resourceMap.description.grok'),
      icon: {
        light: './icons/llm/grok.svg',
        dark: './icons/llm/grok-white.svg',
      },
    },
    {
      key: 'glm',
      name: $t('src.config.resourceMap.name.glm'),
      description: $t('src.config.resourceMap.description.glm'),
      icon: {
        light: './icons/llm/zhipu-color.svg',
        dark: './icons/llm/zhipu-color.svg',
      },
    },
    {
      key: 'pexels',
      name: $t('src.config.resourceMap.name.pexels'),
      description: $t('src.config.resourceMap.description.pexels'),
      icon: {
        light: './icons/llm/pexels-color.svg',
        dark: './icons/llm/pexels-color.svg',
      },
    },
    {
      key: 'tavily',
      name: $t('src.config.resourceMap.name.tavily'),
      description: $t('src.config.resourceMap.description.tavily'),
      icon: {
        light: './icons/llm/tavily-color.svg',
        dark: './icons/llm/tavily-color.svg',
      },
    },
  ];
  return resources.find((resource) => resource.key === key) || null;
};
