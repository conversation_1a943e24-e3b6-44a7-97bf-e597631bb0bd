<template>
  <q-dialog
    v-model="show"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
  >
    <q-card class="column no-wrap">
      <!-- 标题栏 -->
      <q-card-section class="row items-center q-pa-md bg-primary text-white">
        <div class="text-h6">{{ $t('src.components.AddDocumentDialog.title') }}</div>
        <q-space />
        <q-btn flat round dense icon="close" @click="closeDialog" :disable="loading" />
      </q-card-section>

      <!-- 搜索栏 -->
      <q-card-section class="q-pa-md border-bottom">
        <div class="row q-gutter-md items-center">
          <div class="col">
            <q-input
              v-model="searchQuery"
              :placeholder="$t('src.components.AddDocumentDialog.searchPlaceholder')"
              outlined
              dense
              clearable
              debounce="300"
            >
              <template #prepend>
                <q-icon name="search" />
              </template>
            </q-input>
          </div>
          <div class="col-auto">
            <q-btn
              flat
              color="primary"
              :label="`${$t('src.components.AddDocumentDialog.selectedCount', {
                count: selectedDocuments.size,
              })}`"
              :disable="selectedDocuments.size === 0"
              @click="clearSelection"
              icon-right="clear"
            />
          </div>
        </div>
      </q-card-section>

      <!-- 文档列表 -->
      <q-card-section class="col q-pa-none">
        <div v-if="loading" class="fit column items-center justify-center">
          <q-spinner size="48px" color="primary" />
          <div class="text-grey-6 q-mt-md">
            {{ $t('src.components.AddDocumentDialog.loading') }}
          </div>
        </div>

        <div
          v-else-if="filteredDocuments.length === 0"
          class="fit column items-center justify-center"
        >
          <q-icon name="folder_open" size="64px" class="text-grey-4" />
          <div class="text-h6 text-grey-6 q-mt-md">
            {{
              searchQuery
                ? $t('src.components.AddDocumentDialog.noMatch')
                : $t('src.components.AddDocumentDialog.noDocuments')
            }}
          </div>
          <div class="text-body2 text-grey-5 q-mt-sm">
            {{
              searchQuery
                ? $t('src.components.AddDocumentDialog.tryOtherKeywords')
                : $t('src.components.AddDocumentDialog.allDocumentsAdded')
            }}
          </div>
        </div>

        <q-virtual-scroll
          v-else
          :items="filteredDocuments"
          separator
          class="fit"
          :virtual-scroll-item-size="80"
        >
          <template #default="{ item: doc }">
            <q-item
              clickable
              @click="toggleSelection(doc.id)"
              :class="{
                'bg-blue-1': selectedDocuments.has(doc.id),
                'text-primary': selectedDocuments.has(doc.id),
              }"
            >
              <q-item-section side>
                <q-checkbox
                  :model-value="selectedDocuments.has(doc.id)"
                  @update:model-value="toggleSelection(doc.id)"
                  color="primary"
                />
              </q-item-section>

              <q-item-section>
                <q-item-label class="text-weight-medium">
                  {{ doc.title || $t('src.components.AddDocumentDialog.unnamedDocument') }}
                </q-item-label>
                <q-item-label caption class="text-grey-6">
                  <div class="row items-center q-gutter-sm">
                    <span v-if="doc.created_at"> 创建于 {{ formatDate(doc.created_at) }} </span>
                    <span v-if="doc.updated_at"> • 更新于 {{ formatDate(doc.updated_at) }} </span>
                  </div>
                </q-item-label>
              </q-item-section>

              <q-item-section side>
                <div class="column items-end">
                  <q-chip
                    :label="getDocumentTypeLabel(doc)"
                    size="sm"
                    color="grey-3"
                    text-color="grey-8"
                    class="q-mb-xs"
                  />
                  <div class="text-caption text-grey-6">ID: {{ doc.id }}</div>
                </div>
              </q-item-section>
            </q-item>
          </template>
        </q-virtual-scroll>
      </q-card-section>

      <!-- 底部操作栏 -->
      <q-card-section class="q-pa-md border-top">
        <div class="row items-center justify-between">
          <div class="text-body2 text-grey-6">
            {{
              $t('src.components.AddDocumentDialog.totalDocuments', {
                count: filteredDocuments.length,
              })
            }}
            {{
              selectedDocuments.size > 0
                ? $t('src.components.AddDocumentDialog.selectedCount', {
                    count: selectedDocuments.size,
                  })
                : ''
            }}
          </div>
          <div class="row q-gutter-sm">
            <q-btn
              color="primary"
              :label="$t('src.components.AddDocumentDialog.addSelectedDocuments')"
              :loading="loading"
              :disable="selectedDocuments.size === 0"
              @click="handleAddDocuments"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { Document } from '../types/doc';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

interface Props {
  modelValue: boolean;
  availableDocuments: Document[];
  loading?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'add-documents', documentIds: number[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<Emits>();

// 响应式数据
const show = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const searchQuery = ref('');
const selectedDocuments = ref(new Set<number>());

// 计算属性
const filteredDocuments = computed(() => {
  if (!searchQuery.value) {
    return props.availableDocuments;
  }

  const query = searchQuery.value.toLowerCase().trim();
  return props.availableDocuments.filter((doc) => doc.title?.toLowerCase().includes(query));
});

// 方法
const toggleSelection = (docId: number) => {
  if (selectedDocuments.value.has(docId)) {
    selectedDocuments.value.delete(docId);
  } else {
    selectedDocuments.value.add(docId);
  }
  // 触发响应式更新
  selectedDocuments.value = new Set(selectedDocuments.value);
};

const clearSelection = () => {
  selectedDocuments.value.clear();
  selectedDocuments.value = new Set();
};

const closeDialog = () => {
  if (!props.loading) {
    show.value = false;
  }
};

const handleAddDocuments = () => {
  if (selectedDocuments.value.size > 0) {
    emit('add-documents', Array.from(selectedDocuments.value));
  }
};

const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch {
    return dateString;
  }
};

const getDocumentTypeLabel = (doc: Document): string => {
  // 根据文档内容或其他属性判断类型
  if (doc.content) {
    try {
      // 尝试解析为 JSON，如果成功则可能是富文本
      JSON.parse(doc.content);
      return '富文本';
    } catch {
      // 解析失败，可能是纯文本或 Markdown
      return '文本';
    }
  }
  return '文档';
};

// 监听器
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 对话框打开时重置状态
      clearSelection();
      searchQuery.value = '';
    }
  },
);

// 监听可用文档变化，清除无效选择
watch(
  () => props.availableDocuments,
  (newDocs) => {
    const validIds = new Set(newDocs.map((doc) => doc.id));
    const currentSelection = Array.from(selectedDocuments.value);
    const validSelection = currentSelection.filter((id) => validIds.has(id));

    if (validSelection.length !== currentSelection.length) {
      selectedDocuments.value = new Set(validSelection);
    }
  },
  { deep: true },
);
</script>

<style scoped>
.border-bottom {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.border-top {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

/* 深色模式下的边框颜色 */
.body--dark .border-bottom,
.body--dark .border-top {
  border-color: rgba(255, 255, 255, 0.12);
}

/* 选中状态的背景色在深色模式下的适配 */
.body--dark .bg-blue-1 {
  background-color: rgba(33, 150, 243, 0.1) !important;
}

/* 虚拟滚动容器样式 */
.q-virtual-scroll {
  height: 100%;
}

/* 复选框和文本对齐 */
.q-item-section.side {
  min-width: auto;
}
</style>
