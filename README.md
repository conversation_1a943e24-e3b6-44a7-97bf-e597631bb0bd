# InkCop

A copilot app for writers - AI-powered writing assistant built with Qt and Vue.js

## 🚀 Quick Start

### Windows

```powershell
# Development build (with hot reload)
.\win-dev.ps1

# Production build
.\win-prod.ps1

# Create installer package
.\build-installer.ps1 -Type MSIX
```

### Linux

```bash
# Development build
./build-qt-dev.sh

# Production build
./build-qt.sh
```

## 📚 Documentation

- **[BUILD_GUIDE.md](BUILD_GUIDE.md)** - Complete build and deployment guide
- **[INSTALLER_README.md](INSTALLER_README.md)** - Windows installer documentation
- **[QUICK_START_INSTALLER.md](QUICK_START_INSTALLER.md)** - Quick installer guide

## 🛠️ Development

### Frontend Development

```bash
# Install dependencies
bun install

# Start development server
bun run dev

# Build for production
bun run build
```

### Qt Development

See [BUILD_GUIDE.md](BUILD_GUIDE.md) for complete Qt development setup.

## 📦 Features

- **AI-Powered Writing**: Integrated LLM support for writing assistance
- **Rich Text Editor**: TipTap-based editor with advanced formatting
- **Knowledge Management**: Built-in knowledge base and search
- **Cross-Platform**: Windows, Linux, and macOS support
- **Modern UI**: Vue.js + Quasar Framework interface
- **Offline Capable**: Embedded database with ObjectBox

## 🔧 Tech Stack

- **Frontend**: Vue 3, Quasar Framework, TipTap
- **Backend**: Qt 6.7.3, QtWebEngine
- **Database**: ObjectBox (embedded)
- **Build**: CMake, Vite, PowerShell scripts
