# QWindowKit Integration Guide

This document explains how to integrate QWindowKit into InkCop to replace the custom toolbar with a native system title bar.

## Overview

QWindowKit provides a cross-platform solution for creating frameless windows with system title bars, shadows, and native window behaviors. This integration replaces the custom `CustomToolBar` with QWindowKit's `WidgetWindowAgent`.

## Features

- ✅ Cross-platform frameless windows (Windows, macOS, Linux)
- ✅ Native system title bar with standard controls
- ✅ Window shadows and rounded corners
- ✅ Snap layouts support (Windows 11)
- ✅ System integration for window management

## Build Instructions

### Prerequisites

1. **Qt 6.7.3** with MSVC 2022 toolchain
2. **CMake 3.19+**
3. **Visual Studio 2022 Community**
4. **Git** with submodule support

### Quick Start

1. **Build QWindowKit**:

   ```powershell
   .\build-qwindowkit.ps1
   ```

2. **Build InkCop with QWindowKit**:
   ```powershell
   .\build-with-qwk.ps1
   ```

### Build Options

#### Development Build

```powershell
# Enable development mode with hot reload
.\build-with-qwk.ps1 -DevMode -Console
```

#### Production Build

```powershell
# Clean production build
.\build-with-qwk.ps1 -Clean
```

#### With GGUF Support

```powershell
# Build with local GGUF model support
.\build-with-qwk.ps1 -EnableGGUF
```

## Manual Build Process

### 1. Clone QWindowKit

```bash
git clone --recursive https://github.com/stdware/qwindowkit.git third-party/qwindowkit
```

### 2. Build QWindowKit

```bash
# Create build directory
mkdir build-qwindowkit
cd build-qwindowkit

# Configure
cmake ../third-party/qwindowkit \
  -DCMAKE_PREFIX_PATH="C:/Qt/6.7.3/msvc2022_64" \
  -DCMAKE_INSTALL_PREFIX="./install" \
  -DQWINDOWKIT_BUILD_QUICK=ON \
  -DQWINDOWKIT_BUILD_EXAMPLES=OFF \
  -DCMAKE_BUILD_TYPE=Release

# Build and install
cmake --build . --config Release
cmake --install . --config Release
```

### 3. Build InkCop

```bash
# Configure with QWindowKit
cmake -B build -S . \
  -DCMAKE_PREFIX_PATH="build-qwindowkit/install;C:/Qt/6.7.3/msvc2022_64" \
  -DCMAKE_BUILD_TYPE=Release

# Build
cmake --build build --config Release
```

## Architecture Changes

### Before (Custom Toolbar)

```
┌─────────────────────────────────────┐
│ Custom Toolbar (40px)               │
│ ┌─┬─────────────┬─────┬───┬───┬───┐ │
│ │I│   Title     │Tools│ _ │ ☐ │ X │ │
│ └─┴─────────────┴─────┴───┴───┴───┘ │
├─────────────────────────────────────┤
│                                     │
│         WebView Content            │
│                                     │
└─────────────────────────────────────┘
```

### After (QWindowKit)

```
┌─────────────────────────────────────┐
│ System Title Bar (Native)           │
│ ┌─┬─────────────┬─────┬───┬───┬───┐ │
│ │I│   InkCop    │     │ _ │ ☐ │ X │ │
│ └─┴─────────────┴─────┴───┴───┴───┘ │
├─────────────────────────────────────┤
│                                     │
│         WebView Content            │
│                                     │
└─────────────────────────────────────┘
```

## Code Changes

### CMakeLists.txt

- Added QWindowKit package discovery
- Added QWindowKit::Widgets to target_link_libraries

### MainWindow

- Replaced custom toolbar with QWindowKit::WidgetWindowAgent
- Removed Windows-specific native event handling
- Simplified window setup

### Key Files Modified

- `CMakeLists.txt`: QWindowKit integration
- `mainwindow.h`: Added QWindowKit includes and member
- `mainwindow.cpp`: Replaced custom toolbar with QWindowKit

## Migration Details

### Removed Features

- Custom toolbar rendering and styling
- Windows-specific native window effects
- Manual window border/shadow handling
- Native event filtering for window management

### Retained Features

- WebView functionality
- System tray integration
- Theme switching
- Window geometry saving/loading
- All API integrations (WindowApi, DatabaseApi, etc.)

### New Features

- Native system title bar on all platforms
- Automatic window shadow and rounded corners
- Platform-specific window behaviors
- Better system integration

## Troubleshooting

### Common Issues

1. **QWindowKit not found**:

   ```
   CMake Error: Could not find a package configuration file provided by "QWindowKit"
   ```

   **Solution**: Run `.\build-qwindowkit.ps1` first

2. **Build failures**:
   - Ensure Qt 6.9.1 path is correct
   - Check Visual Studio 2022 is installed
   - Verify CMake version is 3.19+

3. **Missing dependencies**:
   - Install Qt WebEngine component
   - Ensure MSVC 2022 toolchain is available

### Environment Setup

```powershell
# Set Qt environment variables
$env:QTDIR = "C:\Qt\6.7.3\msvc2022_64"
$env:PATH = "$env:QTDIR\bin;$env:PATH"
```

## Testing

### Basic Functionality

1. Window resizing from all edges
2. System title bar controls (minimize, maximize, close)
3. Window dragging
4. Theme switching
5. System tray integration

### Platform-specific

- **Windows**: Snap layouts, rounded corners
- **macOS**: Native traffic lights, mission control
- **Linux**: Window manager integration

## Development

### Building for Development

```powershell
# Full development setup
.\build-qwindowkit.ps1 -Debug
.\build-with-qwk.ps1 -DevMode -Console -Debug
```

### Frontend Development

```bash
# Start frontend dev server
bun run dev

# In another terminal, run with development mode
.\build-with-qwk.ps1 -DevMode -Console
```

## Performance Notes

- QWindowKit provides better performance than custom toolbar
- Native window management reduces CPU usage
- System integration improves battery life on laptops

## Future Enhancements

- Custom title bar content via QWindowKit API
- Advanced window effects (blur, transparency)
- Platform-specific customizations
