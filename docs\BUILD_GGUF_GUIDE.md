# InkCop GGUF 支持构建指南

## 概述

本指南将帮助您完整地集成 llama.cpp 到 InkCop 项目中，实现本地 GGUF 模型支持。

## 当前状态

### ✅ 已完成的工作

1. **llama.cpp 源码**: 已包含在 `third-party/llama.cpp/`
2. **Qt 集成代码**: `LocalGGUFEmbedding` 类已完整实现
3. **CMake 配置**: 已配置 llama.cpp 库的自动检测和链接
4. **构建脚本**: 提供了完整的自动化构建脚本

### ⚠️ 需要完成的步骤

llama.cpp 库文件需要完整构建（当前只有部分库文件）。

## 系统要求

- Windows 10/11 (64位)
- Visual Studio 2022 Community/Professional/Enterprise
- Qt 6.7.3 MSVC 2022 64-bit
- CMake 3.16+
- Git
- Bun 包管理器
- **可选**: NVIDIA CUDA Toolkit 11.0+ (用于 GPU 加速)

## 快速开始

### 方法一：一键构建（推荐）

```powershell
# 构建 llama.cpp + InkCop (带 CUDA 支持)
.\build-with-gguf.ps1

# 仅 CPU 版本
.\build-with-gguf.ps1 -EnableCUDA:$false

# 强制重新构建
.\build-with-gguf.ps1 -Force
```

### 方法二：分步构建

```powershell
# 步骤 1: 构建 llama.cpp
.\build-llama-cpp.ps1

# 步骤 2: 构建 InkCop
.\build-with-gguf.ps1 -SkipLlamaCpp
```

## 详细构建步骤

### 1. 环境准备

确保在 **Visual Studio Developer Command Prompt** 中运行：

```powershell
# 检查环境
cl.exe           # 应该显示编译器版本
cmake --version  # 应该显示 CMake 版本
git --version    # 应该显示 Git 版本
```

### 2. 构建 llama.cpp

```powershell
# 基本构建（带 CUDA）
.\build-llama-cpp.ps1

# CPU 版本
.\build-llama-cpp.ps1 -EnableCUDA:$false

# 强制重新构建
.\build-llama-cpp.ps1 -Force
```

脚本会：

- 自动检测 CUDA 环境
- 配置和构建所有必要的库文件
- 验证构建结果

### 3. 验证 llama.cpp 构建

检查以下库文件是否存在：

```
third-party/llama.cpp/build-cuda/src/Release/llama.lib
third-party/llama.cpp/build-cuda/common/Release/common.lib
third-party/llama.cpp/build-cuda/ggml/src/Release/ggml-base.lib
third-party/llama.cpp/build-cuda/ggml/src/Release/ggml-cpu.lib
third-party/llama.cpp/build-cuda/ggml/src/Release/ggml-cuda.lib  # CUDA 版本
```

### 4. 构建 InkCop

```powershell
# 完整构建
.\build-with-gguf.ps1 -SkipLlamaCpp

# 或者使用原始开发脚本
.\win-dev.ps1
```

## 构建脚本说明

### `build-llama-cpp.ps1`

专门用于构建 llama.cpp 库。

**参数:**

- `-BuildType`: 构建类型 (Release/Debug)
- `-EnableCUDA`: 启用 CUDA 支持
- `-Force`: 强制重新构建

### `build-with-gguf.ps1`

一键构建 llama.cpp 和 InkCop。

**参数:**

- `-BuildType`: 构建类型
- `-EnableCUDA`: 启用 CUDA 支持
- `-Force`: 强制重新构建
- `-SkipLlamaCpp`: 跳过 llama.cpp 构建

### `win-dev.ps1`

原始开发构建脚本，已集成 GGUF 支持。

**特点:**

- 包含 `-DENABLE_LOCAL_GGUF=ON`
- 自动检测和链接 llama.cpp 库
- 支持热重载开发模式

## 使用 GGUF 功能

### 1. 启动应用

构建完成后，启动：

```
build-gguf/bin/Release/InkCop.exe
```

### 2. 配置设置

1. 打开 **设置** → **知识库设置**
2. 在 **嵌入模式** 中选择 "本地模型" 或 "自动选择"
3. 点击 **"检测GPU能力"** 按钮

### 3. 加载模型

1. 点击 **"选择模型文件"**
2. 选择 `.gguf` 格式的嵌入模型
3. 系统会自动检测最佳 GPU 层数设置

## 故障排除

### llama.cpp 构建失败

1. **检查 Visual Studio 环境**:

   ```powershell
   # 确保在 VS Developer Command Prompt 中
   cl.exe  # 应该有输出
   ```

2. **CUDA 问题**:

   ```powershell
   # 检查 CUDA 环境
   echo $env:CUDA_PATH
   nvcc --version
   ```

3. **重新构建**:
   ```powershell
   .\build-llama-cpp.ps1 -Force
   ```

### InkCop 链接错误

1. **检查库文件**:

   ```powershell
   # 验证必要的库文件存在
   Get-ChildItem "third-party\llama.cpp\build-cuda" -Recurse -Include "*.lib"
   ```

2. **清理重建**:
   ```powershell
   .\build-with-gguf.ps1 -Force
   ```

### 运行时错误

1. **DLL 缺失**: 确保 `windeployqt` 正确执行
2. **CUDA 运行时**: 安装最新的 NVIDIA 驱动
3. **控制台模式**: 使用 `-DCONSOLE_MODE=ON` 查看调试信息

## GPU 支持验证

启动应用后，在知识库设置中应该能看到：

```
✅ GPU detected: NVIDIA RTX A4000 (CUDA)
✅ Max layers: 40
✅ Recommended GPU layers: 35
```

## 下一步计划

完成第一步 llama.cpp 集成后，接下来的步骤：

1. **第二步**: 完善 GGUF 格式支持

   - 添加更多模型格式支持
   - 优化内存使用
   - 添加模型验证

2. **第三步**: 增强 CUDA 支持
   - 多 GPU 支持
   - 内存优化
   - 性能调优

## 技术细节

### CMake 集成

CMakeLists.txt 中的关键配置：

```cmake
# 启用 GGUF 支持
option(ENABLE_LOCAL_GGUF "Enable local GGUF model support" OFF)

# 查找 llama.cpp 库
find_library(LLAMA_CPP_LIB NAMES llama...)
find_library(LLAMA_COMMON_LIB NAMES common...)
# ... 其他库文件

# 链接库
target_link_libraries(InkCop ${LLAMA_CPP_LIB} ${LLAMA_COMMON_LIB}...)
```

### Qt 集成

`LocalGGUFEmbedding` 类提供：

- 模型加载和管理
- GPU 能力检测
- 线程安全的推理接口
- 错误处理和日志

## 支持和反馈

如遇到问题，请检查：

1. 构建日志输出
2. Visual Studio 环境设置
3. CUDA 安装状态
4. 库文件完整性

---

**完成第一步集成后，您的 InkCop 将具备完整的本地 GGUF 模型支持能力！**
