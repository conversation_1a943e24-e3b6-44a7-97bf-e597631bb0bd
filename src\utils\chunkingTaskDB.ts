/**
 * 切割任务数据库管理
 * 用于持久化切割任务状态，支持意外恢复
 */

import Dexie, { type Table } from 'dexie';
import type { ChunkingConfig, ChunkingResult } from './knowledgeBase';

// 切割任务状态
export type ChunkingTaskStatus = 'pending' | 'processing' | 'completed' | 'failed';

// 切割任务数据结构
export interface ChunkingTask {
  id: string; // 任务ID
  status: ChunkingTaskStatus; // 任务状态
  strategy: 'markdown' | 'recursiveCharacter' | 'latex' | 'smart'; // 切割策略
  content: string; // 原始文档内容
  config?: ChunkingConfig; // 切割配置
  enableLogging?: boolean; // 是否启用日志
  
  // 结果数据
  chunks?: ChunkingResult; // 切割结果
  
  // 时间戳
  createdAt: number; // 创建时间
  updatedAt: number; // 更新时间
  completedAt?: number; // 完成时间
  
  // 错误信息
  error?: string; // 错误消息
}

// 数据库类
class ChunkingTaskDatabase extends Dexie {
  tasks!: Table<ChunkingTask>;

  constructor() {
    super('ChunkingTaskDB');
    
    this.version(1).stores({
      tasks: 'id, status, strategy, createdAt, updatedAt, completedAt',
    });
  }
}

// 数据库实例
const db = new ChunkingTaskDatabase();

/**
 * 切割任务数据库管理器
 */
export class ChunkingTaskManager {
  private db = db;

  /**
   * 创建新的切割任务
   */
  async createTask(
    id: string,
    strategy: ChunkingTask['strategy'],
    content: string,
    config?: ChunkingConfig,
    enableLogging?: boolean,
  ): Promise<ChunkingTask> {
    const now = Date.now();
    
    const task: ChunkingTask = {
      id,
      status: 'pending',
      strategy,
      content,
      config,
      enableLogging,
      createdAt: now,
      updatedAt: now,
    };

    await this.db.tasks.add(task);
    console.log(`📝 [ChunkingTaskDB] 创建切割任务: ${id}`);
    
    return task;
  }

  /**
   * 更新任务状态
   */
  async updateTaskStatus(id: string, status: ChunkingTaskStatus, error?: string): Promise<void> {
    const updates: Partial<ChunkingTask> = {
      status,
      updatedAt: Date.now(),
    };

    if (status === 'completed' || status === 'failed') {
      updates.completedAt = Date.now();
    }

    if (error) {
      updates.error = error;
    }

    await this.db.tasks.update(id, updates);
    console.log(`🔄 [ChunkingTaskDB] 更新任务状态: ${id} -> ${status}`);
  }

  /**
   * 保存切割结果
   */
  async saveChunkingResult(id: string, chunks: ChunkingResult): Promise<void> {
    await this.db.tasks.update(id, {
      chunks,
      status: 'completed',
      updatedAt: Date.now(),
      completedAt: Date.now(),
    });
    
    console.log(`💾 [ChunkingTaskDB] 保存切割结果: ${id}, 块数: ${chunks.chunkCount}`);
  }

  /**
   * 获取任务
   */
  async getTask(id: string): Promise<ChunkingTask | undefined> {
    return await this.db.tasks.get(id);
  }

  /**
   * 获取所有任务
   */
  async getAllTasks(): Promise<ChunkingTask[]> {
    return await this.db.tasks.orderBy('createdAt').reverse().toArray();
  }

  /**
   * 获取指定状态的任务
   */
  async getTasksByStatus(status: ChunkingTaskStatus): Promise<ChunkingTask[]> {
    return await this.db.tasks.where('status').equals(status).toArray();
  }

  /**
   * 删除任务
   */
  async deleteTask(id: string): Promise<void> {
    await this.db.tasks.delete(id);
    console.log(`🗑️ [ChunkingTaskDB] 删除任务: ${id}`);
  }

  /**
   * 清理已完成的任务（可选的清理功能）
   */
  async cleanupCompletedTasks(olderThanDays: number = 7): Promise<number> {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    
    const deletedCount = await this.db.tasks
      .where('status')
      .anyOf(['completed', 'failed'])
      .and(task => task.completedAt ? task.completedAt < cutoffTime : false)
      .delete();
    
    console.log(`🧹 [ChunkingTaskDB] 清理了 ${deletedCount} 个旧任务`);
    return deletedCount;
  }

  /**
   * 获取任务统计
   */
  async getTaskStats(): Promise<{
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
  }> {
    const tasks = await this.getAllTasks();
    
    const stats = {
      total: tasks.length,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
    };

    tasks.forEach(task => {
      stats[task.status]++;
    });

    return stats;
  }
}

// 导出单例实例
export const chunkingTaskManager = new ChunkingTaskManager();
