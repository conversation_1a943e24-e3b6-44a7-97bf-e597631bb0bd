import { ref, reactive } from 'vue';
import { Notify } from 'quasar';

import { $t } from 'src/composables/useTrans';

export interface ErrorInfo {
  id: string;
  message: string;
  context: string;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  stack?: string;
  userFriendlyMessage?: string;
}

export interface ErrorHandlerOptions {
  showNotification?: boolean;
  logToConsole?: boolean;
  severity?: ErrorInfo['severity'];
  userFriendlyMessage?: string;
  autoRecover?: boolean;
}

/**
 * 错误处理组合式函数
 */
export function useErrorHandler() {
  const errors = reactive<Map<string, ErrorInfo>>(new Map());
  const errorCount = ref(0);

  /**
   * 生成错误ID
   */
  const generateErrorId = (): string => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  /**
   * 捕获错误
   */
  const captureError = (
    error: Error | string,
    context: string,
    options: ErrorHandlerOptions = {},
  ): string => {
    const errorId = generateErrorId();
    const errorMessage = error instanceof Error ? error.message : error;
    const stack = error instanceof Error ? error.stack : undefined;

    const errorInfo: ErrorInfo = {
      id: errorId,
      message: errorMessage,
      context,
      timestamp: Date.now(),
      severity: options.severity || 'medium',
      stack,
      userFriendlyMessage: options.userFriendlyMessage,
    };

    errors.set(errorId, errorInfo);
    errorCount.value++;

    // 控制台日志
    if (options.logToConsole !== false) {
      console.error(`[${context}] ${errorMessage}`, error);
    }

    // 显示用户友好的通知
    if (options.showNotification !== false) {
      showUserFriendlyMessage(errorInfo);
    }

    // 自动恢复
    if (options.autoRecover) {
      setTimeout(() => {
        recoverFromError(errorId);
      }, 5000);
    }

    return errorId;
  };

  /**
   * 显示用户友好的错误消息
   */
  const showUserFriendlyMessage = (errorInfo: ErrorInfo) => {
    const message = errorInfo.userFriendlyMessage || getDefaultUserMessage(errorInfo);
    const color = getSeverityColor(errorInfo.severity);

    Notify.create({
      type: 'negative',
      message,
      color,
      position: 'top',
      timeout: getSeverityTimeout(errorInfo.severity),
      actions: [
        {
          label: $t('src.composables.useErrorHandler.retry'),
          color: 'white',
          handler: () => recoverFromError(errorInfo.id),
        },
        {
          label: $t('src.composables.useErrorHandler.close'),
          color: 'white',
          handler: () => dismissError(errorInfo.id),
        },
      ],
    });
  };

  /**
   * 获取默认用户消息
   */
  const getDefaultUserMessage = (errorInfo: ErrorInfo): string => {
    const contextMessages: Record<string, string> = {
      'editor-load': $t('src.composables.useErrorHandler.editorLoad'),
      'content-sync': $t('src.composables.useErrorHandler.contentSync'),
      'drag-operation': $t('src.composables.useErrorHandler.dragOperation'),
      'file-tree': $t('src.composables.useErrorHandler.fileTree'),
      'save-operation': $t('src.composables.useErrorHandler.saveOperation'),
    };

    return (
      contextMessages[errorInfo.context] || $t('src.composables.useErrorHandler.operationFailed')
    );
  };

  /**
   * 获取严重程度对应的颜色
   */
  const getSeverityColor = (severity: ErrorInfo['severity']): string => {
    const colors = {
      low: 'orange',
      medium: 'red',
      high: 'red',
      critical: 'red',
    };
    return colors[severity];
  };

  /**
   * 获取严重程度对应的超时时间
   */
  const getSeverityTimeout = (severity: ErrorInfo['severity']): number => {
    const timeouts = {
      low: 3000,
      medium: 5000,
      high: 8000,
      critical: 0, // 不自动关闭
    };
    return timeouts[severity];
  };

  /**
   * 从错误中恢复
   */
  const recoverFromError = (errorId: string) => {
    const errorInfo = errors.get(errorId);
    if (!errorInfo) return;

    console.log(`🔄 [ErrorHandler] 尝试从错误中恢复: ${errorInfo.context}`);

    // 根据错误上下文执行恢复操作
    switch (errorInfo.context) {
      case 'editor-load':
        console.log('🔄 [ErrorHandler] 重新加载页面以恢复编辑器');
        window.location.reload();
        break;
      case 'content-sync':
        // 触发重新同步
        console.log('🔄 [ErrorHandler] 触发内容重新同步');
        window.dispatchEvent(new CustomEvent('retryContentSync'));
        break;
      case 'file-tree':
        // 触发重新加载文件树
        console.log('🔄 [ErrorHandler] 触发文件树重新加载');
        window.dispatchEvent(new CustomEvent('retryFileTreeLoad'));
        break;
      case 'splitter-init':
      case 'database-init':
        // 初始化错误不自动刷新页面，只记录日志
        console.warn('⚠️ [ErrorHandler] 初始化错误，不执行自动恢复以避免无限循环');
        break;
      default:
        // 对于未知错误，也不自动刷新页面
        console.warn('⚠️ [ErrorHandler] 未知错误类型，不执行自动恢复:', errorInfo.context);
        break;
    }

    dismissError(errorId);
  };

  /**
   * 忽略错误
   */
  const dismissError = (errorId: string) => {
    errors.delete(errorId);
  };

  /**
   * 获取错误信息
   */
  const getError = (errorId: string): ErrorInfo | null => {
    return errors.get(errorId) || null;
  };

  /**
   * 获取所有错误
   */
  const getAllErrors = (): ErrorInfo[] => {
    return Array.from(errors.values());
  };

  /**
   * 清理旧错误
   */
  const cleanupOldErrors = (maxAge: number = 300000) => {
    // 5分钟
    const now = Date.now();
    for (const [id, error] of errors.entries()) {
      if (now - error.timestamp > maxAge) {
        errors.delete(id);
      }
    }
  };

  /**
   * 清理所有错误
   */
  const clearAllErrors = () => {
    errors.clear();
    errorCount.value = 0;
  };

  // 定期清理旧错误
  setInterval(cleanupOldErrors, 60000); // 每分钟清理一次

  return {
    captureError,
    showUserFriendlyMessage,
    recoverFromError,
    dismissError,
    getError,
    getAllErrors,
    clearAllErrors,
    errorCount,
  };
}

/**
 * 全局错误处理器
 */
const globalErrorHandler = useErrorHandler();

export const useGlobalErrorHandler = () => globalErrorHandler;
