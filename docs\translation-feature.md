# 智能翻译功能使用说明

## 功能概述

智能翻译功能已集成到PowerEdge组件中，使用阿里云通义千问的qwen-mt-plus翻译模型，支持高质量的多语言翻译。

## 功能特点

- **多语言支持**: 支持32种语言互译，包括中英日韩法德西阿等主流语言
- **自动语言检测**: 自动识别源语言，无需手动指定
- **领域提示**: 支持领域特定的翻译优化
- **高质量翻译**: 基于通义千问优化的机器翻译模型
- **多选项结果**: 自动识别并拆分多个翻译备选项
- **一键复制**: 翻译结果可一键复制到剪贴板，支持单独复制每个选项

## 使用方法

### 1. 前置条件

在使用翻译功能前，需要确保已配置通义千问API Key：

1. 进入应用设置页面
2. 找到"LLM设置" -> "通义千问设置"
3. 输入有效的API Key

### 2. 访问翻译功能

1. 打开任意文档进行编辑
2. 在编辑器下方的PowerEdge面板中
3. 点击"智能翻译"标签页

### 3. 使用翻译

1. **输入原始内容**: 在"原始内容"文本框中输入需要翻译的文本
2. **选择目标语言**: 从下拉菜单中选择目标语言
3. **添加领域提示**（可选）: 输入领域信息以提高翻译质量，例如：
   - "技术文档"
   - "法律文件"
   - "医学文献"
   - "商务邮件"
4. **开始翻译**: 点击"开始翻译"按钮
5. **查看结果**: 翻译完成后，结果将显示在下方
   - **单个结果**: 显示为一个卡片，点击"复制"按钮复制整个结果
   - **多个选项**: 自动拆分为多个卡片，点击任意卡片复制该选项
6. **复制结果**:
   - 单个结果：点击"复制"按钮
   - 多个选项：直接点击任意选项卡片即可复制

## 支持的语言

| 语言     | 英文名称   |
| -------- | ---------- |
| 中文     | Chinese    |
| 英语     | English    |
| 日语     | Japanese   |
| 韩语     | Korean     |
| 法语     | French     |
| 德语     | German     |
| 西班牙语 | Spanish    |
| 阿拉伯语 | Arabic     |
| 俄语     | Russian    |
| 意大利语 | Italian    |
| 葡萄牙语 | Portuguese |
| 荷兰语   | Dutch      |
| 泰语     | Thai       |
| 越南语   | Vietnamese |
| 印尼语   | Indonesian |
| 土耳其语 | Turkish    |
| 波兰语   | Polish     |
| 捷克语   | Czech      |
| 匈牙利语 | Hungarian  |
| 瑞典语   | Swedish    |
| 芬兰语   | Finnish    |
| 希伯来语 | Hebrew     |
| 印地语   | Hindi      |
| 孟加拉语 | Bengali    |
| 马来语   | Malay      |
| 粤语     | Cantonese  |

## 多选项结果处理

当翻译API返回包含多个备选项的结果时（如"Knowledge\n\nPhilosophy"），组件会自动：

1. **智能拆分**: 按换行符(`\n`)和分号(`;`)拆分结果
2. **去除空白**: 自动去除每个选项的前后空白字符
3. **过滤空项**: 移除空的选项
4. **独立显示**: 每个选项显示为独立的可点击卡片
5. **悬停效果**: 鼠标悬停时显示高亮效果
6. **一键复制**: 点击任意选项直接复制到剪贴板

### 示例

输入结果：`"Knowledge\n\nPhilosophy"`
拆分后显示：

- Knowledge
- Philosophy

## 技术实现

### 结果拆分逻辑

```javascript
const translationOptions = computed(() => {
  if (!translationResult.value) return [];

  // 按换行符和分号拆分结果
  const options = translationResult.value
    .split(/[\n;]/)
    .map((option) => option.trim())
    .filter((option) => option.length > 0);

  return options;
});
```

### API调用

翻译功能使用阿里云通义千问的OpenAI兼容接口：

```javascript
const response = await fetch(`${baseUrl}/chat/completions`, {
  method: 'POST',
  headers: {
    Authorization: `Bearer ${apiKey}`,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model: 'qwen-mt-plus',
    messages: [
      {
        role: 'user',
        content: sourceText,
      },
    ],
    translation_options: {
      source_lang: 'auto',
      target_lang: targetLanguage,
      domains: domainHint, // 可选
    },
  }),
});
```

### 配置获取

API Key从uiStore中获取：

```javascript
const apiKey = uiStore.perferences?.llm?.qwen?.apiKey;
const baseUrl =
  uiStore.perferences?.llm?.qwen?.baseUrl || 'https://dashscope.aliyuncs.com/compatible-mode/v1';
```

## 错误处理

翻译功能包含完善的错误处理机制：

- **API Key未配置**: 提示用户配置API Key
- **网络错误**: 显示网络连接错误信息
- **API错误**: 显示具体的API错误信息
- **响应格式错误**: 处理异常的API响应

## 注意事项

1. **API费用**: 翻译功能会消耗通义千问API额度，请注意使用量
2. **网络连接**: 需要稳定的网络连接访问阿里云API
3. **文本长度**: 单次翻译建议不超过1024个token
4. **领域提示**: 领域提示目前仅支持英文描述

## 未来改进

- [ ] 支持批量翻译
- [ ] 添加翻译历史记录
- [ ] 支持术语干预功能
- [ ] 支持翻译记忆功能
- [ ] 添加翻译质量评估
