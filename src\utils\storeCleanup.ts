/**
 * Store 清理工具
 * 用于在应用退出或组件销毁时正确清理所有 store 的资源
 */

import { getUiStore, getLlmStore } from 'src/composeables/useStore';

/**
 * 清理所有 Store 的资源
 * 应该在应用退出时调用
 */
export function cleanupAllStores(): void {
  try {
    // 清理 UI Store
    const uiStore = getUiStore();
    if (typeof uiStore.$dispose === 'function') {
      uiStore.$dispose();
    }
    
    // 清理 LLM Store
    const llmStore = getLlmStore();
    if (typeof llmStore.cleanup === 'function') {
      llmStore.cleanup();
    }
    
    console.log('✅ [StoreCleanup] 所有 Store 资源清理完成');
  } catch (error) {
    console.error('❌ [StoreCleanup] 清理 Store 资源时发生错误:', error);
  }
}

/**
 * 安全获取 Store 实例
 * 确保在访问前 Store 已经正确初始化
 */
export function safeGetStore<T>(getter: () => T): T | null {
  try {
    return getter();
  } catch (error) {
    console.warn('⚠️ [StoreCleanup] 获取 Store 实例失败:', error);
    return null;
  }
}

/**
 * 检查 Store 是否有清理方法
 */
export function hasCleanupMethod(store: unknown): store is { cleanup(): void } {
  return typeof store === 'object' && 
         store !== null && 
         'cleanup' in store && 
         typeof (store as { cleanup: unknown }).cleanup === 'function';
}

/**
 * 检查 Store 是否有 $dispose 方法
 */
export function hasDisposeMethod(store: unknown): store is { $dispose(): void } {
  return typeof store === 'object' && 
         store !== null && 
         '$dispose' in store && 
         typeof (store as { $dispose: unknown }).$dispose === 'function';
}