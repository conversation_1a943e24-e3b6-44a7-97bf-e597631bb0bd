<template>
  <div
    v-if="isVisible"
    class="row no-wrap items-center justify-end dense-input"
    :data-search-panel="instanceKey"
    @keydown="handleKeydown"
    tabindex="-1"
  >
    <q-space />
    <div
      class="row no-wrap border-left border-bottom"
      :class="isDark ? 'bg-dark' : 'bg-white'"
      :style="`flex: 0 0 ${searchPanelWidth}px;`"
    >
      <div
        class="bg-primary drag-handle cursor-col-resize"
        style="flex: 0 0 2px"
        @mousedown="startDrag"
      ></div>
      <q-btn
        flat
        dense
        size="sm"
        square
        :icon="showReplace ? 'mdi-chevron-down' : 'mdi-chevron-right'"
        @click="showReplace = !showReplace"
      />
      <div class="q-space column no-wrap shadow-12 gap-xxs">
        <div class="row no-wrap items-center">
          <!-- 查找输入框 -->
          <div class="row no-wrap items-center q-space">
            <q-input
              v-model="searchTerm"
              dense
              square
              filled
              autofocus
              :placeholder="$t('src.components.tiptap.searchReplace.searchPlaceholder')"
              class="q-space"
              @keyup.enter.prevent="next"
            >
              <template v-slot:append>
                <!-- 选项 -->
                <div class="row no-wrap items-center gap-xs">
                  <q-btn
                    v-if="searchTerm"
                    dense
                    size="sm"
                    flat
                    color="grey-6"
                    icon="clear"
                    @click="clearAll"
                    :title="$t('src.components.tiptap.searchReplace.clearSearch')"
                  />
                  <q-btn
                    dense
                    size="sm"
                    flat
                    :color="caseSensitive ? 'primary' : 'grey-6'"
                    icon="mdi-format-size"
                    @click="toggleCase()"
                    :title="$t('src.components.tiptap.searchReplace.caseSensitive')"
                  />
                </div>
              </template>
            </q-input>
          </div>

          <!-- 固定宽度的右侧区域 -->
          <div class="row no-wrap items-center q-pl-md" style="flex: 0 0 120px">
            <!-- 搜索结果计数 -->
            <div class="text-xs text-grey-6 text-no-wrap" style="min-width: 60px">
              <template v-if="totalResults > 0"
                >{{ currentResultIndex + 1 }} / {{ totalResults }}</template
              >
              <template v-else>{{ $t('src.components.tiptap.searchReplace.noResults') }}</template>
            </div>
            <q-space />
            <q-btn
              flat
              dense
              size="sm"
              icon="keyboard_arrow_up"
              @click="previous"
              :disable="!searchTerm || totalResults === 0"
            />
            <q-btn
              flat
              dense
              size="sm"
              icon="keyboard_arrow_down"
              @click="next"
              :disable="!searchTerm || totalResults === 0"
            />
            <q-btn flat dense size="sm" icon="close" @click="closePanel" />
          </div>
        </div>

        <div v-if="showReplace" class="row no-wrap items-center">
          <!-- 替换输入框 -->
          <div class="row no-wrap items-center q-space">
            <q-input
              v-model="replaceTerm"
              square
              dense
              filled
              :placeholder="$t('src.components.tiptap.searchReplace.replacePlaceholder')"
              class="q-space"
              @keyup.enter="replace"
            />
          </div>

          <!-- 固定宽度的右侧区域，与搜索框保持一致 -->
          <div class="row no-wrap items-center q-pl-md" style="flex: 0 0 120px">
            <!-- 操作按钮 -->
            <q-btn
              dense
              size="sm"
              square
              flat
              icon="short_text"
              @click="replace"
              :disable="!searchTerm || totalResults === 0"
              :title="$t('src.components.tiptap.searchReplace.replace')"
            />
            <q-btn
              dense
              size="sm"
              square
              flat
              icon="format_list_numbered_rtl"
              @click="replaceAll"
              :disable="!searchTerm || totalResults === 0"
              :title="$t('src.components.tiptap.searchReplace.replaceAll')"
            />
            <!-- 占位空间，确保与搜索框右侧宽度一致 -->
            <div style="width: 32px"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import type { Editor } from '@tiptap/core';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';

const $q = useQuasar();
const { t: $t } = useI18n({ useScope: 'global' });

// 计算属性
const isDark = computed(() => $q.dark.isActive);

interface Props {
  editor: Editor | null;
  visible?: boolean;
  initialSearchTerm?: string;
  instanceKey?: string;
}

interface Emits {
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  initialSearchTerm: '',
  instanceKey: '',
});

const emit = defineEmits<Emits>();

// 响应式数据
const searchTerm = ref<string>('');
const replaceTerm = ref<string>('');
const caseSensitive = ref<boolean>(false);

const toggleCase = () => {
  caseSensitive.value = !caseSensitive.value;
  updateSearchReplace(true);
};

const updateSearchReplace = (clearIndex: boolean = false) => {
  if (!props.editor) return;

  if (clearIndex) props.editor.commands.resetIndex();

  props.editor.commands.setSearchTerm(searchTerm.value);
  props.editor.commands.setReplaceTerm(replaceTerm.value);
  props.editor.commands.setCaseSensitive(caseSensitive.value);
};

// 替换后更新搜索结果，避免重复替换已处理的内容
const updateSearchReplaceAfterReplace = (fromPosition: number) => {
  if (!props.editor) return;

  // 重新设置搜索词以触发重新搜索
  props.editor.commands.setSearchTerm(searchTerm.value);
  props.editor.commands.setReplaceTerm(replaceTerm.value);
  props.editor.commands.setCaseSensitive(caseSensitive.value);

  // 等待搜索结果更新后，找到下一个合适的匹配项
  setTimeout(() => {
    const { results, resultIndex } = props.editor.storage.searchAndReplace;

    if (results && results.length > 0) {
      // 找到第一个位置在替换位置之后的匹配项
      let nextIndex = 0;
      for (let i = 0; i < results.length; i++) {
        if (results[i].from >= fromPosition) {
          nextIndex = i;
          break;
        }
      }

      // 如果没有找到后续匹配项，回到第一个
      if (nextIndex === 0 && results[0].from < fromPosition) {
        // 所有匹配项都在替换位置之前，说明已经到了最后，回到开头
        nextIndex = 0;
      }

      // 使用导航命令来设置正确的索引，而不是直接修改storage
      const currentIndex = resultIndex || 0;
      if (nextIndex !== currentIndex) {
        // 计算需要移动的步数
        let steps = nextIndex - currentIndex;
        if (steps < 0) {
          steps = results.length + steps; // 处理回绕情况
        }

        // 使用nextSearchResult命令来导航到正确位置
        for (let i = 0; i < steps; i++) {
          props.editor.commands.nextSearchResult();
        }
      }
      goToSelection();
    }
  }, 50);
};

const goToSelection = () => {
  if (!props.editor) return;

  const { results, resultIndex } = props.editor.storage.searchAndReplace;
  const position: { from: number; to: number } = results[resultIndex];

  if (!position) return;

  props.editor.commands.setTextSelection({
    from: position.from,
    to: position.to,
  });

  const { node } = props.editor.view.domAtPos(props.editor.state.selection.anchor);
  if (node instanceof HTMLElement) {
    node.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

watch(
  () => searchTerm.value,
  (val, oldVal) => {
    if (val !== oldVal) {
      const trimmedVal = val.trim();
      if (trimmedVal) {
        // 有搜索词时保存到历史并触发搜索
        saveSearchHistory(trimmedVal);
        updateSearchReplace(true);
      } else {
        // 搜索词为空时清除搜索效果
        props.editor?.commands.setSearchTerm('');
        props.editor?.commands.resetIndex();
      }
    }
  },
);

watch(
  () => replaceTerm.value.trim(),
  (val, oldVal) => (val === oldVal ? null : updateSearchReplace()),
);

watch(
  () => caseSensitive.value,
  (val, oldVal) => (val === oldVal ? null : updateSearchReplace(true)),
);

const replace = () => {
  if (!props.editor) return;

  const { results, resultIndex } = props.editor.storage.searchAndReplace;
  const currentPosition = results[resultIndex];

  if (!currentPosition) return;

  // 执行替换
  const success = props.editor
    .chain()
    .focus()
    .setTextSelection({
      from: currentPosition.from,
      to: currentPosition.to,
    })
    .insertContent(replaceTerm.value)
    .run();

  if (success) {
    // 重新搜索，从替换后的位置开始
    updateSearchReplaceAfterReplace(currentPosition.from + replaceTerm.value.length);
  }
};

const next = () => {
  props.editor?.commands.nextSearchResult();
  goToSelection();
};

const previous = () => {
  props.editor?.commands.previousSearchResult();
  goToSelection();
};

const clearAll = () => {
  // 手动清空所有内容（用户主动操作）
  searchTerm.value = '';
  replaceTerm.value = '';

  // 清除搜索效果
  if (props.editor) {
    props.editor.commands.setSearchTerm('');
    props.editor.commands.resetIndex();
  }

  // 清空搜索历史
  if (props.instanceKey) {
    localStorage.removeItem(getSearchHistoryKey());
  }
};

const replaceAll = () => {
  if (!props.editor) return;

  const { results } = props.editor.storage.searchAndReplace;

  if (!results || results.length === 0) return;

  // 从后往前替换，避免位置偏移问题
  const sortedResults = [...results].sort((a, b) => b.from - a.from);

  let replacedCount = 0;

  for (const position of sortedResults) {
    const success = props.editor
      .chain()
      .focus()
      .setTextSelection({
        from: position.from,
        to: position.to,
      })
      .insertContent(replaceTerm.value)
      .run();

    if (success) {
      replacedCount++;
    }
  }

  // 替换完成后重新搜索
  if (replacedCount > 0) {
    setTimeout(() => {
      updateSearchReplace(true);
    }, 50);
  }
};

const currentResultIndex = computed<number>(
  () => props.editor?.storage?.searchAndReplace?.resultIndex,
);
const totalResults = computed<number>(
  () => props.editor?.storage?.searchAndReplace?.results.length,
);

// 监听编辑器搜索状态变化，同步到组件内部状态
watch(
  () => props.editor?.storage?.searchAndReplace?.searchTerm,
  (newSearchTerm) => {
    if (newSearchTerm !== undefined && newSearchTerm !== searchTerm.value) {
      // 编辑器的搜索词发生了变化（可能是外部调用命令导致的）
      searchTerm.value = newSearchTerm;
      if (newSearchTerm.trim()) {
        // 保存到搜索历史
        saveSearchHistory(newSearchTerm.trim());
      }
    }
  },
  { immediate: true },
);

// 监听编辑器替换词状态变化，同步到组件内部状态
watch(
  () => props.editor?.storage?.searchAndReplace?.replaceTerm,
  (newReplaceTerm) => {
    if (newReplaceTerm !== undefined && newReplaceTerm !== replaceTerm.value) {
      // 编辑器的替换词发生了变化
      replaceTerm.value = newReplaceTerm;
    }
  },
  { immediate: true },
);
const showReplace = ref<boolean>(false);
const searchPanelWidth = ref<number>(460);

// 拖拽相关状态
const isDragging = ref(false);
const dragStartX = ref(0);
const dragStartWidth = ref(0);

// 计算属性
const isVisible = computed(() => props.visible);

// 搜索历史管理
const getSearchHistoryKey = () => `searchHistory_${props.instanceKey}`;

const loadSearchHistory = () => {
  if (!props.instanceKey) return '';
  const historyKey = getSearchHistoryKey();
  return localStorage.getItem(historyKey) || '';
};

const saveSearchHistory = (term: string) => {
  if (!props.instanceKey || !term.trim()) return;
  const historyKey = getSearchHistoryKey();
  localStorage.setItem(historyKey, term);
};

// 监听面板可见性变化
watch(
  () => props.visible,
  (visible, wasVisible) => {
    if (visible && !wasVisible) {
      // 面板从不可见变为可见
      if (props.initialSearchTerm) {
        // 如果有初始搜索词，使用它
        searchTerm.value = props.initialSearchTerm;
        saveSearchHistory(props.initialSearchTerm);
        // 立即触发搜索
        if (props.editor) {
          updateSearchReplace(true);
        }
      } else {
        // 否则恢复上次的搜索历史
        const lastSearchTerm = loadSearchHistory();
        if (lastSearchTerm) {
          searchTerm.value = lastSearchTerm;
          // 恢复历史搜索词时也要触发搜索
          if (props.editor) {
            updateSearchReplace(true);
          }
        }
      }
    }
  },
);

const closePanel = () => {
  // 关闭面板时保存当前搜索词到历史，然后清空搜索效果
  if (searchTerm.value.trim()) {
    saveSearchHistory(searchTerm.value.trim());
  }

  // 清空搜索词和搜索效果
  searchTerm.value = '';
  replaceTerm.value = '';
  props.editor?.commands.resetIndex();

  emit('close');
};

// 拖拽处理函数
const startDrag = (event: MouseEvent) => {
  isDragging.value = true;
  dragStartX.value = event.clientX;
  dragStartWidth.value = searchPanelWidth.value;

  // 添加全局事件监听
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);

  // 添加拖拽样式类
  document.body.classList.add('dragging');

  // 防止文本选择
  event.preventDefault();
};

const onDrag = (event: MouseEvent) => {
  if (!isDragging.value) return;

  // 计算拖拽距离（向左拖拽为正值，向右拖拽为负值）
  const deltaX = dragStartX.value - event.clientX;
  const newWidth = dragStartWidth.value + deltaX;

  // 限制最小和最大宽度
  const minWidth = 300;
  const maxWidth = 1400;

  searchPanelWidth.value = Math.max(minWidth, Math.min(maxWidth, newWidth));
};

const stopDrag = () => {
  isDragging.value = false;

  // 移除全局事件监听
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);

  // 移除拖拽样式类
  document.body.classList.remove('dragging');

  // 保存到localStorage
  localStorage.setItem('searchPanelWidth', searchPanelWidth.value.toString());
};

// 键盘快捷键支持 - 只处理面板内的Enter键
const handleKeydown = (event: KeyboardEvent) => {
  if (!isVisible.value) return;

  // ESC键由编辑器处理，这里只处理Enter键
  if (event.key === 'Enter' && event.shiftKey) {
    event.preventDefault();
    previous();
  } else if (event.key === 'Enter') {
    event.preventDefault();
    next();
  }
};

// 组件挂载时添加键盘监听
import { onMounted, onUnmounted } from 'vue';

onMounted(() => {
  // 从localStorage加载保存的面板宽度
  const savedWidth = localStorage.getItem('searchPanelWidth');
  if (savedWidth) {
    const width = parseInt(savedWidth, 10);
    if (!isNaN(width) && width >= 300 && width <= 800) {
      searchPanelWidth.value = width;
    }
  }

  // 如果面板可见且有搜索词，立即触发搜索
  if (props.visible && props.editor && searchTerm.value) {
    updateSearchReplace(true);
  }
});

onUnmounted(() => {
  // 清理拖拽事件监听器
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);

  // 清理搜索效果
  if (props.editor) {
    props.editor.commands.setSearchTerm('');
    props.editor.commands.resetIndex();
  }
});
</script>

<style scoped>
.search-replace-panel {
  max-width: 320px;
  backdrop-filter: blur(8px);
}

.drag-handle {
  cursor: col-resize;
  transition: background-color 0.2s ease;
}

.drag-handle:hover {
  background-color: #1976d2 !important;
}

/* 拖拽时禁用文本选择 */
.dragging {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
</style>

<style>
/* 全局样式用于搜索结果高亮 - 优化性能版本 */
.search-result {
  background-color: #ffeb3b !important;
  color: #000 !important;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
  /* 移除transition以提升性能 */
}

.search-result-current {
  background-color: #ff5722 !important;
  color: #fff !important;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 600;
  /* 简化阴影效果 */
  box-shadow: 0 0 2px rgba(255, 87, 34, 0.8);
}

/* 暗色模式支持 */
.body--dark .search-result {
  background-color: #ffc107 !important;
  color: #000 !important;
}

.body--dark .search-result-current {
  background-color: #ff7043 !important;
  color: #000 !important;
  box-shadow: 0 0 2px rgba(255, 112, 67, 0.8);
}

/* 保持向后兼容 */
.bg-deep-orange {
  background-color: #ff5722 !important;
  color: white !important;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
}

/* 拖拽时禁用全局文本选择 */
body.dragging {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  cursor: col-resize !important;
}
</style>
