# InkCop Development Build Script
# Build Qt application with Release mode for better performance, but keep dev features

param(
    [string]$BuildType = "Release"  # 使用Release模式提升性能
)

Write-Host "======================================" -ForegroundColor Cyan
Write-Host "InkCop Development Build" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "Build Type: $BuildType (for better performance)" -ForegroundColor Blue
Write-Host "Frontend URL: localhost:9000" -ForegroundColor Blue
Write-Host "Console Mode: Enabled (terminal output visible)" -ForegroundColor Blue
Write-Host "Features: Hot reload + Performance optimized" -ForegroundColor Green
Write-Host ""

# Step 0: Setup development environment configuration
Write-Host "Step 0: Setting up development environment..." -ForegroundColor Yellow
if (Test-Path ".env.production") {
    Write-Host "Using development environment configuration (with API keys)..." -ForegroundColor Blue
    Copy-Item ".env.production" ".env" -Force
    Write-Host "Development .env configuration applied!" -ForegroundColor Green
} else {
    Write-Host "Warning: .env.production not found, using current .env" -ForegroundColor Yellow
}
Write-Host ""

# Step 1: Build frontend for better performance
Write-Host "Step 1: Building Quasar frontend..." -ForegroundColor Magenta
Write-Host "Running: bun run build" -ForegroundColor Blue

& bun run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Frontend build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Frontend build completed!" -ForegroundColor Green
Write-Host ""

# Setup Qt environment
Write-Host "Step 2: Setting up Qt 6.9.1 MSVC environment..." -ForegroundColor Magenta
$qtPath = "C:\Qt\6.9.1\msvc2022_64"
$qwkPath = "third-party/build-qwindowkit/install"

if (-not (Test-Path $qtPath)) {
    Write-Host "Error: Qt installation not found at: $qtPath" -ForegroundColor Red
    exit 1
}

# Include QWindowKit in CMAKE_PREFIX_PATH
$combinedPrefixPath = "$qwkPath;$qtPath"
$env:CMAKE_PREFIX_PATH = $combinedPrefixPath
$env:Qt6_DIR = "$qtPath\lib\cmake\Qt6"
$env:PATH = "$qtPath\bin;$env:PATH"

Write-Host "Qt environment configured!" -ForegroundColor Green

# Setup Visual Studio environment
Write-Host "Loading Visual Studio environment..." -ForegroundColor Yellow
$vsPath = "C:\Program Files\Microsoft Visual Studio\2022\Community"
if (Test-Path $vsPath) {
    & "$vsPath\VC\Auxiliary\Build\vcvars64.bat" >$null 2>&1
    Write-Host "Visual Studio environment loaded!" -ForegroundColor Green
} else {
    Write-Host "Warning: Visual Studio 2022 not found" -ForegroundColor Yellow
}

# Step 3: Build Qt application
Write-Host ""
Write-Host "Step 3: Building Qt application..." -ForegroundColor Magenta

# Create build directory
$buildDir = "build-dev"
if (Test-Path $buildDir) {
    Write-Host "Cleaning old build directory..." -ForegroundColor Yellow
    Remove-Item -Path $buildDir -Recurse -Force
}
New-Item -ItemType Directory -Path $buildDir -Force | Out-Null

# Enter build directory
Set-Location $buildDir

Write-Host "Configuring CMake..." -ForegroundColor Yellow
$cmakeArgs = @(
    "-G", "Visual Studio 17 2022",
    "-A", "x64",
    "-DCMAKE_BUILD_TYPE=$BuildType",
    "-DCMAKE_PREFIX_PATH=$env:CMAKE_PREFIX_PATH",
    "-DQt6_DIR=$env:Qt6_DIR",
    "-DDEV_MODE=ON",
    "-DCONSOLE_MODE=ON",
    "-DENABLE_LOCAL_GGUF=ON",
    "-DCMAKE_CXX_FLAGS=/Zm300 /bigobj",
    ".."
)

& cmake @cmakeArgs
if ($LASTEXITCODE -ne 0) {
    Write-Host "CMake configuration failed!" -ForegroundColor Red
    Set-Location ..
    exit 1
}

Write-Host "Building project..." -ForegroundColor Yellow
& cmake --build . --config $BuildType
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    Set-Location ..
    exit 1
}

Write-Host "Running windeployqt..." -ForegroundColor Yellow
$exePath = "bin\$BuildType\InkCop.exe"
if (Test-Path $exePath) {
    # 修复: 移除不支持的 --qmlsource 参数
    Write-Host "Running windeployqt with WebEngine support..." -ForegroundColor Blue
    & windeployqt $exePath --webenginewidgets --webenginecore --webchannel --force --verbose 2
    if ($LASTEXITCODE -ne 0) {
        Write-Host "windeployqt failed, trying basic deployment..." -ForegroundColor Yellow
        # 回退到基本部署
        & windeployqt $exePath --force
    }
    Write-Host "Deployment completed!" -ForegroundColor Green
} else {
    Write-Host "Warning: Executable not found, skipping deployment" -ForegroundColor Yellow
}

# Copy ObjectBox DLL
$objectboxDll = "..\third-party\objectbox-windows\lib\objectbox.dll"
$targetDir = "bin\$BuildType"
if (Test-Path $objectboxDll) {
    Copy-Item $objectboxDll $targetDir -Force
    Write-Host "ObjectBox DLL copied!" -ForegroundColor Green
}

# Copy llama.cpp DLLs if GGUF support is enabled
$llamaPath = "..\third-party\llama.cpp\build-cuda\bin\Release"
if (Test-Path $llamaPath) {
    Write-Host "Copying llama.cpp DLLs..." -ForegroundColor Yellow
    
    $llamaDlls = @(
        "ggml-base.dll",
        "ggml-cpu.dll", 
        "ggml-cuda.dll",
        "ggml.dll",
        "llama.dll"
    )
    
    $copiedCount = 0
    foreach ($dll in $llamaDlls) {
        $sourceDll = Join-Path $llamaPath $dll
        if (Test-Path $sourceDll) {
            Copy-Item $sourceDll $targetDir -Force
            Write-Host "  Copied $dll" -ForegroundColor Blue
            $copiedCount++
        } else {
            Write-Host "  Warning: $dll not found" -ForegroundColor Yellow
        }
    }
    
    if ($copiedCount -gt 0) {
        Write-Host "llama.cpp DLLs copied! ($copiedCount/$($llamaDlls.Count) files)" -ForegroundColor Green
    } else {
        Write-Host "Warning: No llama.cpp DLLs found in $llamaPath" -ForegroundColor Yellow
    }
} else {
    Write-Host "Warning: llama.cpp build directory not found: $llamaPath" -ForegroundColor Yellow
    Write-Host "  Make sure to build llama.cpp with CUDA support first" -ForegroundColor Yellow
}

# Copy QWindowKit DLLs
$qwkDllPath = "..\third-party\qwindowkit\build-qwk\out-amd64-Release\bin"
if (Test-Path $qwkDllPath) {
    Write-Host "Copying QWindowKit DLLs..." -ForegroundColor Yellow

    $qwkDlls = Get-ChildItem -Path $qwkDllPath -Filter "*.dll"
    $copiedCount = 0

    foreach ($dll in $qwkDlls) {
        try {
            Copy-Item $dll.FullName $targetDir -Force
            Write-Host "  Copied $($dll.Name)" -ForegroundColor Blue
            $copiedCount++
        } catch {
            Write-Host "  Warning: Failed to copy $($dll.Name)" -ForegroundColor Yellow
        }
    }

    if ($copiedCount -gt 0) {
        Write-Host "QWindowKit DLLs copied! ($copiedCount files)" -ForegroundColor Green
    } else {
        Write-Host "Warning: No QWindowKit DLLs found or copied" -ForegroundColor Yellow
    }
} else {
    Write-Host "Warning: QWindowKit DLL directory not found: $qwkDllPath" -ForegroundColor Yellow
    Write-Host "  Make sure to build QWindowKit first" -ForegroundColor Yellow
}

Set-Location ..

Write-Host ""
Write-Host "======================================" -ForegroundColor Green
Write-Host "Development Build Completed!" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green
Write-Host "Executable: $buildDir\bin\$BuildType\InkCop.exe" -ForegroundColor Blue
Write-Host "Frontend URL: localhost:9000" -ForegroundColor Blue
Write-Host "Console Mode: Enabled (terminal output visible)" -ForegroundColor Blue
Write-Host "Performance: Release mode optimized" -ForegroundColor Yellow
Write-Host "Features: Hot reload + Hardware acceleration" -ForegroundColor Green
Write-Host ""

# Launch application
Write-Host "Launching application..." -ForegroundColor Cyan
$exeFullPath = "$buildDir\bin\$BuildType\InkCop.exe"
if (Test-Path $exeFullPath) {
    Write-Host "Starting: $exeFullPath" -ForegroundColor Blue
    & $exeFullPath
} else {
    Write-Host "Error: Executable not found: $exeFullPath" -ForegroundColor Red
    exit 1
} 