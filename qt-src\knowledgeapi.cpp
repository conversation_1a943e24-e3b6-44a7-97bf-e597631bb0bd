#include "knowledgeapi.h"
#include "databaseapi.h"
#include "localggufembedding.h"
// TODO: 实现 ChunkingStrategyFactory
// #include "chunkingstrategyfactory.h"
#include <QUuid>
#include <QCoreApplication>
#include <QFileInfo>
#include <QFileDialog>
#include <QStringConverter>
#include <QLoggingCategory>
#include <QThread>
#include <QTimer>
#include <QScopeGuard>
#include <QJsonArray>
#include <QJsonObject>
#include <QJsonDocument>

#include <set>
#include <algorithm> // for std::replace on Windows

// 定义知识库日志类别
// Q_DECLARE_LOGGING_CATEGORY(knowledgeApi)
// Q_LOGGING_CATEGORY(knowledgeApi, "inkcop.knowledge")

// ObjectBox includes - 只在实现文件中包含
#define OBX_CPP_FILE
#include "objectbox.hpp"
#include "objectbox/objectbox-model.h"
#include "objectbox/knowledge.obx.hpp"

// Ensure ObjectBox C++ implementation is included
#ifdef OBX_CPP_FILE
// This should include the implementation when OBX_CPP_FILE is defined
#endif

// Use ObjectBox generated types
using namespace InkCop::Knowledge;

KnowledgeApi::KnowledgeApi(QObject *parent, DatabaseApi *databaseApi)
    : QObject(parent), m_networkManager(new QNetworkAccessManager(this)), m_databaseApi(databaseApi)
{
    logInfo("Initializing KnowledgeApi", "Constructor");

    // 简化初始化，移除复杂的批处理定时器
    // m_batchProcessTimer = new QTimer(this);
    // m_batchProcessTimer->setSingleShot(true);
    // m_batchProcessTimer->setInterval(BATCH_TIMEOUT);
    // connect(m_batchProcessTimer, &QTimer::timeout, this, &KnowledgeApi::processBatchQueue);

    initializeDatabase();
    initializeLocalGGUF();
}

KnowledgeApi::~KnowledgeApi()
{
    // ObjectBox自动管理资源释放
    m_queryBox.reset();
    m_chunkBox.reset();
    m_docBox.reset();
    m_kbBox.reset();
    m_store.reset();

    // 清理本地GGUF资源
    cleanupLocalGGUF();
}

bool KnowledgeApi::initializeDatabase()
{
    try
    {
        qDebug() << "🔧 [KnowledgeApi] Starting ObjectBox database initialization...";

        // Create database directory
        if (!createDirectories())
        {
            qDebug() << "❌ [KnowledgeApi] Failed to create ObjectBox database directory";
            return false;
        }
        qDebug() << "✅ [KnowledgeApi] Database directory created successfully";

        // Get database path
        QString dbPath = getKnowledgeDatabasePath();
        qDebug() << "📁 [KnowledgeApi] ObjectBox database path:" << dbPath;
        qDebug() << "📁 [KnowledgeApi] Using AppDataLocation to ensure unified storage with other user data";

        // Create ObjectBox model
        qDebug() << "🏗️ [KnowledgeApi] Creating ObjectBox model...";
        OBX_model *model = create_obx_model();
        if (!model)
        {
            qDebug() << "❌ [KnowledgeApi] Failed to create ObjectBox model - model is NULL";
            return false;
        }
        qDebug() << "✅ [KnowledgeApi] ObjectBox model created successfully";

        // Check model errors
        obx_err error_code = obx_model_error_code(model);
        if (error_code != OBX_SUCCESS)
        {
            const char *error_msg = obx_model_error_message(model);
            qDebug() << "❌ [KnowledgeApi] ObjectBox model error:" << error_code << ":" << (error_msg ? error_msg : "Unknown error");
            obx_model_free(model);
            return false;
        }
        qDebug() << "✅ [KnowledgeApi] ObjectBox model validation successful";

        // Create Store using C++ API
        qDebug() << "🏪 [KnowledgeApi] Creating ObjectBox Store...";
        obx::Options options;
        options.model(model);

        // Windows path handling: ensure ObjectBox can properly handle Windows paths
        std::string dbPathStd = dbPath.toStdString();

        // On Windows, ensure correct path format
#ifdef _WIN32
        // Convert backslashes to forward slashes, ObjectBox better supports forward slashes
        std::replace(dbPathStd.begin(), dbPathStd.end(), '\\', '/');
#endif

        options.directory(dbPathStd);
        qDebug() << "📦 [KnowledgeApi] Store configuration - directory:" << dbPathStd.c_str();

        m_store = std::make_unique<obx::Store>(options);
        qDebug() << "✅ [KnowledgeApi] ObjectBox Store created successfully";

        // Create Boxes
        qDebug() << "📋 [KnowledgeApi] Creating KnowledgeBase Box...";
        m_kbBox = std::make_unique<obx::Box<KnowledgeBase>>(*m_store);
        qDebug() << "✅ [KnowledgeApi] KnowledgeBase Box created successfully";

        qDebug() << "📄 [KnowledgeApi] Creating KnowledgeDocument Box...";
        m_docBox = std::make_unique<obx::Box<KnowledgeDocument>>(*m_store);
        qDebug() << "✅ [KnowledgeApi] KnowledgeDocument Box created successfully";

        qDebug() << "🧩 [KnowledgeApi] Creating KnowledgeChunk Box...";
        m_chunkBox = std::make_unique<obx::Box<KnowledgeChunk>>(*m_store);
        qDebug() << "✅ [KnowledgeApi] KnowledgeChunk Box created successfully";

        qDebug() << "🔍 [KnowledgeApi] Creating KnowledgeQuery Box...";
        m_queryBox = std::make_unique<obx::Box<KnowledgeQuery>>(*m_store);
        qDebug() << "✅ [KnowledgeApi] KnowledgeQuery Box created successfully";

        // Test basic operations
        qDebug() << "🧪 [KnowledgeApi] Testing basic database operations...";
        auto allKbs = m_kbBox->getAll();
        qDebug() << "📊 [KnowledgeApi] Current knowledge base count:" << allKbs.size();

        qDebug() << "🎉 [KnowledgeApi] ObjectBox database initialization completely successful!";

        // 移除数据库初始化时的自动向量化检查
        // 向量化检查现在只在模型加载完成后进行

        return true;
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] ObjectBox initialization exception:" << e.what();
        return false;
    }
    catch (...)
    {
        qDebug() << "❌ [KnowledgeApi] ObjectBox initialization unknown exception occurred";
        return false;
    }
}

QString KnowledgeApi::createKnowledgeBase(const QString &name, const QString &description, const QString &userId)
{
    qDebug() << "Creating knowledge base:" << name;

    try
    {
        if (!m_kbBox)
        {
            qDebug() << "Knowledge base Box not initialized";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // Create KnowledgeBase object
        KnowledgeBase kb;
        kb.id = 0; // ObjectBox will automatically assign ID
        kb.name = name.toStdString();
        kb.description = description.toStdString();
        kb.user_id = userId.toStdString();
        kb.created_at = QDateTime::currentMSecsSinceEpoch();
        kb.updated_at = kb.created_at;

        // Save to ObjectBox
        obx_id newId = m_kbBox->put(kb);
        if (newId == 0)
        {
            qDebug() << "Failed to save knowledge base";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Failed to save knowledge base";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // Get saved object
        auto savedKb = m_kbBox->get(newId);
        if (!savedKb)
        {
            qDebug() << "Failed to get saved knowledge base";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Failed to get saved knowledge base";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // Return success response
        QJsonObject response;
        response["success"] = true;
        response["message"] = "Knowledge base created successfully";
        response["data"] = kbToJsonObject(*savedKb);

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "Exception occurred while creating knowledge base:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Failed to create knowledge base: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::getAllKnowledgeBases()
{
    qDebug() << "Getting all knowledge bases";

    try
    {
        if (!m_kbBox)
        {
            qDebug() << "Knowledge base Box not initialized";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // Get all knowledge bases from ObjectBox
        auto allKbs = m_kbBox->getAll();

        QJsonArray kbArray;
        for (const auto &kb : allKbs)
        {
            QJsonObject kbObj = kbToJsonObject(*kb);

            // Get document count
            if (m_docBox)
            {
                auto allDocs = m_docBox->getAll();
                int count = 0;
                for (const auto &doc : allDocs)
                {
                    if (doc->kb_id == kb->id)
                    {
                        count++;
                    }
                }
                kbObj["document_count"] = count;
            }
            else
            {
                kbObj["document_count"] = 0;
            }

            kbArray.append(kbObj);
        }

        QJsonObject response;
        response["success"] = true;
        response["knowledge_bases"] = kbArray;
        response["message"] = "Successfully retrieved knowledge base list";

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "Exception occurred while getting knowledge base list:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Failed to get knowledge base list: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::getKnowledgeBase(const QString &kbId)
{
    qDebug() << "Getting knowledge base:" << kbId;

    try
    {
        if (!m_kbBox)
        {
            qDebug() << "Knowledge base Box not initialized";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id id = kbId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        auto kb = m_kbBox->get(id);
        if (!kb)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Knowledge base not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        QJsonObject kbObj = kbToJsonObject(*kb);

        // Get document count
        if (m_docBox)
        {
            auto allDocs = m_docBox->getAll();
            int count = 0;
            for (const auto &doc : allDocs)
            {
                if (doc->kb_id == id)
                {
                    count++;
                }
            }
            kbObj["document_count"] = count;
        }
        else
        {
            kbObj["document_count"] = 0;
        }

        QJsonObject response;
        response["success"] = true;
        response["data"] = kbObj;
        response["message"] = "Successfully retrieved knowledge base";

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "Exception occurred while getting knowledge base:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Failed to get knowledge base: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

bool KnowledgeApi::deleteKnowledgeBase(const QString &kbId)
{
    qDebug() << "🗑️ [KnowledgeApi] Starting to delete knowledge base:" << kbId;

    try
    {
        if (!m_kbBox || !m_docBox)
        {
            qDebug() << "❌ [KnowledgeApi] Database not initialized";
            return false;
        }

        bool ok;
        obx_id id = kbId.toULongLong(&ok);
        if (!ok)
        {
            qDebug() << "❌ [KnowledgeApi] Invalid knowledge base ID:" << kbId;
            return false;
        }

        // Verify knowledge base exists
        auto kb = m_kbBox->get(id);
        if (!kb)
        {
            qDebug() << "❌ [KnowledgeApi] Knowledge base does not exist:" << kbId;
            return false;
        }

        qDebug() << "📋 [KnowledgeApi] Starting cascade deletion of knowledge base:" << QString::fromStdString(kb->name);

        // 1. Get all documents and related data under this knowledge base
        auto allDocs = m_docBox->getAll();
        auto allChunks = m_chunkBox->getAll();
        auto allQueries = m_queryBox->getAll();

        int docCount = 0;
        int chunkCount = 0;
        int queryCount = 0;
        int deletedDocCount = 0;
        int deletedChunkCount = 0;
        int deletedQueryCount = 0;

        // Count data to be deleted
        QList<obx_id> docIdsToDelete;
        QList<obx_id> chunkIdsToDelete;
        QList<obx_id> queryIdsToDelete;

        // Collect document IDs to delete
        for (const auto &doc : allDocs)
        {
            if (doc->kb_id == id)
            {
                docCount++;
                docIdsToDelete.append(doc->id);
            }
        }

        // Collect chunk IDs to delete (belonging to these documents)
        for (const auto &chunk : allChunks)
        {
            for (obx_id docId : docIdsToDelete)
            {
                if (chunk->knowledge_document_id == docId)
                {
                    chunkCount++;
                    chunkIdsToDelete.append(chunk->id);
                    break;
                }
            }
        }

        // Collect query IDs to delete (belonging to this knowledge base)
        for (const auto &query : allQueries)
        {
            if (query->knowledge_base_id == id)
            {
                queryCount++;
                queryIdsToDelete.append(query->id);
            }
        }

        qDebug() << "📊 [KnowledgeApi] Data statistics - documents:" << docCount << ", vector chunks:" << chunkCount << ", queries:" << queryCount;

        // 2. Delete all chunks (including vector data)
        if (chunkCount > 0)
        {
            qDebug() << "🧩 [KnowledgeApi] Starting to delete vector chunks...";
            for (obx_id chunkId : chunkIdsToDelete)
            {
                bool chunkRemoved = m_chunkBox->remove(chunkId);
                if (chunkRemoved)
                {
                    deletedChunkCount++;
                }
                else
                {
                    qDebug() << "⚠️ [KnowledgeApi] Failed to delete chunk:" << chunkId;
                }
            }
            qDebug() << "✅ [KnowledgeApi] Vector chunks deletion completed:" << deletedChunkCount << "/" << chunkCount;
        }

        // 3. Delete all queries
        if (queryCount > 0)
        {
            qDebug() << "🔍 [KnowledgeApi] Starting to delete query records...";
            for (obx_id queryId : queryIdsToDelete)
            {
                bool queryRemoved = m_queryBox->remove(queryId);
                if (queryRemoved)
                {
                    deletedQueryCount++;
                }
                else
                {
                    qDebug() << "⚠️ [KnowledgeApi] Failed to delete query:" << queryId;
                }
            }
            qDebug() << "✅ [KnowledgeApi] Query records deletion completed:" << deletedQueryCount << "/" << queryCount;
        }

        // 4. Delete all knowledge base documents
        if (docCount > 0)
        {
            qDebug() << "📄 [KnowledgeApi] Starting to delete knowledge base documents...";
            for (obx_id docId : docIdsToDelete)
            {
                bool docRemoved = m_docBox->remove(docId);
                if (docRemoved)
                {
                    deletedDocCount++;
                }
                else
                {
                    qDebug() << "⚠️ [KnowledgeApi] Failed to delete document:" << docId;
                }
            }
            qDebug() << "✅ [KnowledgeApi] Knowledge base documents deletion completed:" << deletedDocCount << "/" << docCount;
        }

        // 3. Finally delete the knowledge base itself
        qDebug() << "🏗️ [KnowledgeApi] Deleting knowledge base itself...";
        bool removed = m_kbBox->remove(id);

        if (removed)
        {
            qDebug() << "🎉 [KnowledgeApi] Knowledge base completely deleted successfully:" << QString::fromStdString(kb->name);
            qDebug() << "📊 [KnowledgeApi] Deletion statistics - knowledge base:1, documents:" << deletedDocCount << "/" << docCount
                     << ", vector chunks:" << deletedChunkCount << "/" << chunkCount
                     << ", queries:" << deletedQueryCount << "/" << queryCount;
        }
        else
        {
            qDebug() << "❌ [KnowledgeApi] Failed to delete knowledge base itself:" << kbId;
        }

        return removed;
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] Exception occurred while deleting knowledge base:" << e.what();
        return false;
    }
}

QString KnowledgeApi::updateKnowledgeBase(const QString &kbId, const QString &name, const QString &description)
{
    qDebug() << "Updating knowledge base:" << kbId;

    try
    {
        if (!m_kbBox)
        {
            qDebug() << "Knowledge base Box not initialized";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id id = kbId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        auto kb = m_kbBox->get(id);
        if (!kb)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Knowledge base not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // Update knowledge base information
        kb->name = name.toStdString();
        kb->description = description.toStdString();
        kb->updated_at = QDateTime::currentMSecsSinceEpoch();

        // Save update
        m_kbBox->put(*kb);

        QJsonObject response;
        response["success"] = true;
        response["message"] = "Knowledge base updated successfully";
        response["data"] = kbToJsonObject(*kb);

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "Exception occurred while updating knowledge base:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Failed to update knowledge base: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::addDocumentToKnowledgeBase(const QString &kbId, const QString &title, const QString &content, const QString &documentType)
{
    logInfo(QString("Adding document to knowledge base: %1, title: %2, type: %3").arg(kbId, title, documentType), "addDocument");

    try
    {
        if (!m_kbBox || !m_docBox || !m_chunkBox)
        {
            logError("Database not initialized", "addDocument");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id kbIdNum = kbId.toULongLong(&ok);
        if (!ok)
        {
            logError(QString("Invalid knowledge base ID: %1").arg(kbId), "addDocument");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 验证知识库是否存在
        auto kb = m_kbBox->get(kbIdNum);
        if (!kb)
        {
            logError(QString("Knowledge base does not exist: %1").arg(kbId), "addDocument");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Knowledge base does not exist";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 创建知识库文档
        logDebug("Creating knowledge base document", "addDocument");
        KnowledgeDocument doc;
        doc.id = 0; // ObjectBox会自动分配ID
        doc.kb_id = kbIdNum;
        doc.title = title.toStdString();
        doc.content = content.toStdString();
        doc.document_type = documentType.toStdString();
        doc.metadata = "{}"; // 默认空metadata
        doc.created_at = QDateTime::currentMSecsSinceEpoch();
        doc.updated_at = doc.created_at;

        // 保存文档
        logDebug("Saving document to database", "addDocument");
        obx_id newDocId = m_docBox->put(doc);
        if (newDocId == 0)
        {
            logError("Document save failed", "addDocument");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Document save failed";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        logInfo(QString("Document save success, ID: %1, starting async vectorization").arg(newDocId), "addDocument");

        // 获取保存后的文档
        auto savedDoc = m_docBox->get(newDocId);
        if (!savedDoc)
        {
            logError("Failed to get saved document", "addDocument");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Failed to get saved document";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 立即返回文档创建结果，不等待向量化
        QJsonObject docData = docToJsonObject(*savedDoc);
        docData["chunk_count"] = 0;   // 初始为0，向量化完成后会更新
        docData["processing"] = true; // 标记正在处理

        QJsonObject response;
        response["success"] = true;
        response["message"] = "Document created successfully, vectorization processing in background";
        response["data"] = docData;

        // 异步启动向量化处理（使用线程池）
        processDocumentVectorizationInThread(newDocId, content, documentType);

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        logError(QString("Add document failed: %1").arg(e.what()), "addDocument");
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Add document failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

void KnowledgeApi::processDocumentVectorization(qint64 docId, const QString &content, const QString &documentType)
{
    logInfo(QString("Starting async vectorization for document ID: %1").arg(docId), "vectorization");
    qDebug() << "📄 [KnowledgeApi] Starting document vectorization for ID:" << docId << ", length:" << content.length();

    try
    {
#ifndef OBJECTBOX_DISABLED
        // 获取文档信息
        auto doc = m_docBox->get(docId);
        if (!doc)
        {
            logError(QString("Document not found, stopping vectorization: %1").arg(docId), "vectorization");
            qDebug() << "❌ [KnowledgeApi] Document not found in database, aborting vectorization";
            qDebug() << "📄 [KnowledgeApi] ======== processDocumentVectorization FAILED ========";
            return;
        }

        // 注意：切割任务现在由前端管理，后端只负责向量化
        // 这里应该接收前端已经切割好的chunks，而不是重新切割
        logWarning("processDocumentVectorization called but chunking is now handled by frontend", "vectorization");

        // 临时保留简单的切割逻辑作为后备方案
        QStringList chunks;
        int chunkSize = 800;
        int chunkOverlap = 200;

        // 简单的字符切割作为临时方案
        QString cleanContent = content.trimmed();
        int start = 0;
        while (start < cleanContent.length())
        {
            int end = qMin(start + chunkSize, cleanContent.length());
            QString chunk = cleanContent.mid(start, end - start).trimmed();
            if (!chunk.isEmpty())
            {
                chunks.append(chunk);
            }
            start = qMax(start + 1, end - chunkOverlap);
        }
        int createdChunkCount = 0;

        qDebug() << "📝 [KnowledgeApi] Split document into" << chunks.size() << "chunks for vectorization (simple character-based splitting)";

        if (chunks.isEmpty())
        {
            qDebug() << "⚠️ [KnowledgeApi] No chunks created, aborting vectorization";
            emit documentVectorized(docId, 0);
            return;
        }

        for (int i = 0; i < chunks.size(); ++i)
        {
            const QString &chunkContent = chunks[i];
            if (chunkContent.trimmed().isEmpty())
            {
                qDebug() << "⚠️ [KnowledgeApi] Skipping empty chunk" << (i + 1);
                continue; // 跳过空白chunks
            }

            qDebug() << "🔄 [KnowledgeApi] Processing chunk" << (i + 1) << "/" << chunks.size() << "length:" << chunkContent.length();

            KnowledgeChunk chunk;
            chunk.id = 0; // ObjectBox会自动分配ID
            chunk.knowledge_document_id = docId;
            chunk.chunk_index = static_cast<uint32_t>(i);
            chunk.content = chunkContent.toStdString();

            qDebug() << "🔤 [KnowledgeApi] Generating embedding for chunk" << (i + 1);

            // 添加超时机制
            QElapsedTimer chunkTimer;
            chunkTimer.start();

            std::vector<float> embedding;

            try
            {
                embedding = this->textToEmbedding(chunkContent); // 生成向量数据

                qint64 chunkTime = chunkTimer.elapsed();
                qDebug() << "⏱️ [KnowledgeApi] Chunk" << (i + 1) << "embedding took" << chunkTime << "ms";

                // 检查是否超时（超过30秒认为异常）
                if (chunkTime > 30000)
                {
                    qDebug() << "⚠️ [KnowledgeApi] Chunk" << (i + 1) << "took too long (" << chunkTime << "ms), may indicate a problem";
                }
            }
            catch (const std::exception &e)
            {
                qDebug() << "❌ [KnowledgeApi] Exception during embedding generation for chunk" << (i + 1) << ":" << e.what();
                embedding.clear(); // 确保为空
            }
            catch (...)
            {
                qDebug() << "❌ [KnowledgeApi] Unknown exception during embedding generation for chunk" << (i + 1);
                embedding.clear(); // 确保为空
            }

            if (embedding.empty())
            {
                qDebug() << "⚠️ [KnowledgeApi] Failed to generate embedding for chunk" << (i + 1) << "- continuing with empty embedding";
                // 继续处理，但使用空的embedding
            }
            else
            {
                qDebug() << "✅ [KnowledgeApi] Generated embedding for chunk" << (i + 1) << "dimension:" << embedding.size();
            }

            chunk.embedding = embedding;
            chunk.metadata = QString("{}").toStdString(); // 默认空metadata
            chunk.created_at = QDateTime::currentMSecsSinceEpoch();
            chunk.is_vectorized = !embedding.empty(); // 根据是否有向量数据设置状态

            qDebug() << "💾 [KnowledgeApi] Saving chunk" << (i + 1) << "to database";
            obx_id chunkId = m_chunkBox->put(chunk);
            if (chunkId > 0)
            {
                createdChunkCount++;
                qDebug() << "✅ [KnowledgeApi] Saved chunk" << (i + 1) << "with ID:" << chunkId;
            }
            else
            {
                qDebug() << "❌ [KnowledgeApi] Failed to save chunk" << (i + 1);
            }
        }

        qDebug() << "✅ [KnowledgeApi] Vectorization completed - created" << createdChunkCount << "/" << chunks.size() << "chunks";

        // 发送信号通知完成
        emit documentVectorized(docId, createdChunkCount);
#else
        qDebug() << "⚠️ [KnowledgeApi] ObjectBox disabled, skipping vectorization";
        emit documentVectorized(docId, -1);
#endif
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] Vectorization failed:" << e.what();
        emit documentVectorized(docId, -1); // -1 表示处理失败
    }
}

QString KnowledgeApi::getDocumentsByKnowledgeBase(const QString &kbId)
{
    qDebug() << "get knowledge base documents:" << kbId;

    try
    {
        if (!m_docBox || !m_chunkBox)
        {
            qDebug() << "document box not initialized";
            QJsonObject response;

            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id kbIdNum = kbId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 查询指定知识库的所有文档
        QJsonArray docArray;

        auto allDocs = m_docBox->getAll();
        auto allChunks = m_chunkBox->getAll();

        for (const auto &doc : allDocs)
        {
            if (doc->kb_id != kbIdNum)
                continue;

            QJsonObject docObj = docToJsonObject(*doc);

            // 计算该文档的chunk数量（分别统计已向量化和总数）
            int totalChunkCount = 0;
            int vectorizedChunkCount = 0;
            for (const auto &chunk : allChunks)
            {
                if (chunk->knowledge_document_id == doc->id)
                {
                    totalChunkCount++;
                    // 检查是否已向量化：embedding不为空
                    if (!chunk->embedding.empty())
                    {
                        vectorizedChunkCount++;
                    }
                }
            }

            // 为了与前端一致，将一些字段名转换为前端期望的格式
            if (!docObj.contains("file_type"))
            {
                docObj["file_type"] = docObj["document_type"];
            }
            if (!docObj.contains("source_type"))
            {
                docObj["source_type"] = "knowledge_base";
            }
            if (!docObj.contains("file_path"))
            {
                docObj["file_path"] = "";
            }
            docObj["chunk_count"] = vectorizedChunkCount;            // 已向量化的chunk数量（保持向后兼容）
            docObj["total_chunk_count"] = totalChunkCount;           // 总chunk数量
            docObj["vectorized_chunk_count"] = vectorizedChunkCount; // 已向量化的chunk数量

            docArray.append(docObj);
        }

        QJsonObject response;
        response["success"] = true;
        response["documents"] = docArray;
        response["message"] = "get documents list success";

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "get documents list failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("get documents list failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::getDocument(const QString &docId)
{
    qDebug() << "get document:" << docId;

    try
    {
        if (!m_docBox)
        {
            qDebug() << "document box not initialized";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id id = docId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid document ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        auto doc = m_docBox->get(id);
        if (!doc)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "document not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        QJsonObject response;
        response["success"] = true;
        response["data"] = docToJsonObject(*doc);
        response["message"] = "get document success";

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "get document failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("get document failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

bool KnowledgeApi::updateDocument(const QString &docId, const QString &title, const QString &content, const QString &documentType)
{
    qDebug() << "🔄 [KnowledgeApi] start update document:" << docId;

    try
    {
        if (!m_docBox)
        {
            qDebug() << "❌ [KnowledgeApi] database not initialized";
            return false;
        }

        bool ok;
        obx_id id = docId.toULongLong(&ok);
        if (!ok)
        {
            qDebug() << "❌ [KnowledgeApi] invalid document ID:" << docId;
            return false;
        }

        auto doc = m_docBox->get(id);
        if (!doc)
        {
            qDebug() << "❌ [KnowledgeApi] document not found:" << docId;
            return false;
        }

        qDebug() << "📋 [KnowledgeApi] update document:" << QString::fromStdString(doc->title);

        // 只更新文档信息，不进行向量化
        doc->title = title.toStdString();
        doc->content = content.toStdString();
        doc->updated_at = QDateTime::currentMSecsSinceEpoch();

        // 智能判断文档类型
        QString finalDocumentType;
        if (documentType != "text" && !documentType.isEmpty())
        {
            // 用户明确指定了类型，使用指定的类型
            finalDocumentType = documentType;
        }
        else if (QString::fromStdString(doc->document_type) != "text" && !QString::fromStdString(doc->document_type).isEmpty())
        {
            // 保留原有类型（如果不是默认text类型）
            finalDocumentType = QString::fromStdString(doc->document_type);
        }
        else
        {
            // 自动检测文档类型
            if (content.contains("```") || content.contains("# ") || content.contains("## "))
            {
                finalDocumentType = "markdown";
            }
            else if (content.contains("\\section") || content.contains("\\begin{"))
            {
                finalDocumentType = "latex";
            }
            else
            {
                finalDocumentType = "text";
            }
        }

        doc->document_type = finalDocumentType.toStdString();
        qDebug() << "🎯 [KnowledgeApi] document type set to:" << finalDocumentType;

        // 保存文档更新
        m_docBox->put(*doc);

        qDebug() << "🎉 [KnowledgeApi] document update completed (no vectorization)";

        return true;
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] update document failed:" << e.what();
        return false;
    }
}

bool KnowledgeApi::deleteDocument(const QString &docId)
{
    qDebug() << "🗑️ [KnowledgeApi] start delete document:" << docId;

    try
    {
        if (!m_docBox || !m_chunkBox)
        {
            qDebug() << "❌ [KnowledgeApi] database not initialized";
            return false;
        }

        bool ok;
        obx_id id = docId.toULongLong(&ok);
        if (!ok)
        {
            qDebug() << "❌ [KnowledgeApi] invalid document ID:" << docId;
            return false;
        }

        // 验证文档是否存在
        auto doc = m_docBox->get(id);
        if (!doc)
        {
            qDebug() << "❌ [KnowledgeApi] document not found:" << docId;
            return false;
        }

        qDebug() << "📋 [KnowledgeApi] start cascade delete document:" << QString::fromStdString(doc->title);

        // 1. 删除该文档的所有chunks（包含向量数据）
        auto allChunks = m_chunkBox->getAll();
        int chunkCount = 0;
        int deletedChunkCount = 0;

        // 先统计要删除的chunk数量
        for (const auto &chunk : allChunks)
        {
            if (chunk->knowledge_document_id == id)
            {
                chunkCount++;
            }
        }

        qDebug() << "🧩 [KnowledgeApi] find" << chunkCount << "chunks to delete";

        // 删除所有chunks
        if (chunkCount > 0)
        {
            qDebug() << "🧩 [KnowledgeApi] start delete document chunks...";

            // 为了避免在遍历时修改集合，先收集要删除的chunk ID
            QList<obx_id> chunkIdsToDelete;
            for (const auto &chunk : allChunks)
            {
                if (chunk->knowledge_document_id == id)
                {
                    chunkIdsToDelete.append(chunk->id);
                }
            }

            // 执行删除
            for (obx_id chunkId : chunkIdsToDelete)
            {
                bool chunkRemoved = m_chunkBox->remove(chunkId);
                if (chunkRemoved)
                {
                    deletedChunkCount++;
                    qDebug() << "✅ [KnowledgeApi] delete chunk:" << chunkId;
                }
                else
                {
                    qDebug() << "⚠️ [KnowledgeApi] delete chunk failed:" << chunkId;
                }
            }

            qDebug() << "📊 [KnowledgeApi] delete chunks completed:" << deletedChunkCount << "/" << chunkCount;
        }

        // 2. 删除文档本身
        qDebug() << "📄 [KnowledgeApi] delete document itself...";
        bool docRemoved = m_docBox->remove(id);

        if (docRemoved)
        {
            qDebug() << "🎉 [KnowledgeApi] document delete success:" << QString::fromStdString(doc->title);
            qDebug() << "📊 [KnowledgeApi] delete statistics - document:1, vector chunks:" << deletedChunkCount;
        }
        else
        {
            qDebug() << "❌ [KnowledgeApi] document delete failed:" << docId;
        }

        return docRemoved;
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] delete document failed:" << e.what();
        return false;
    }
}

QString KnowledgeApi::getKnowledgeBaseStats(const QString &kbId)
{
    qDebug() << "get knowledge base stats:" << kbId;

    try
    {
        if (!m_kbBox || !m_docBox)
        {
            qDebug() << "database not initialized";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id kbIdNum = kbId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 验证知识库是否存在
        auto kb = m_kbBox->get(kbIdNum);
        if (!kb)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "knowledge base not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 查询指定知识库的文档统计
        auto allDocs = m_docBox->getAll();
        auto allChunks = m_chunkBox->getAll();

        int documentCount = 0;
        int totalSize = 0;
        int totalChunks = 0;

        // 先收集属于该知识库的文档ID，提高效率
        std::set<obx_id> kbDocIds;

        // 计算文档数量和总字符数，同时收集文档ID
        for (const auto &doc : allDocs)
        {
            if (doc->kb_id == kbIdNum)
            {
                documentCount++;
                totalSize += static_cast<int>(doc->content.length());
                kbDocIds.insert(doc->id);
            }
        }

        // 分别计算已向量化和总chunks数量
        int vectorizedChunks = 0;
        int totalChunksCount = 0;
        for (const auto &chunk : allChunks)
        {
            if (kbDocIds.find(chunk->knowledge_document_id) != kbDocIds.end())
            {
                totalChunksCount++;
                // 检查是否已向量化：embedding不为空
                if (!chunk->embedding.empty())
                {
                    vectorizedChunks++;
                }
            }
        }
        totalChunks = vectorizedChunks; // 保持向后兼容

        QJsonObject stats;
        stats["knowledge_base_id"] = kbId;
        stats["document_count"] = documentCount;
        stats["total_characters"] = totalSize;
        stats["total_size"] = totalSize;                // 保持兼容性
        stats["total_chunks"] = totalChunks;            // 已向量化的chunks数量（保持向后兼容）
        stats["vectorized_chunks"] = vectorizedChunks;  // 已向量化的chunks数量
        stats["total_chunks_count"] = totalChunksCount; // 总chunks数量
        stats["average_chunks_per_doc"] = documentCount > 0 ? static_cast<double>(totalChunks) / documentCount : 0.0;

        QJsonObject response;
        response["success"] = true;
        response["data"] = stats;
        response["message"] = "get stats success";

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "get stats failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("get stats failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

int KnowledgeApi::getKnowledgeBaseCount()
{
    try
    {
        if (!m_kbBox)
        {
            qDebug() << "knowledge base box not initialized";
            return 0;
        }

        auto allKbs = m_kbBox->getAll();
        return static_cast<int>(allKbs.size());
    }
    catch (const std::exception &e)
    {
        qDebug() << "get knowledge base count failed:" << e.what();
        return 0;
    }
}

int KnowledgeApi::getDocumentCount(const QString &kbId)
{
    try
    {
        if (!m_docBox)
        {
            qDebug() << "document box not initialized";
            return 0;
        }

        if (kbId.isEmpty())
        {
            // 获取所有文档数量
            auto allDocs = m_docBox->getAll();
            return static_cast<int>(allDocs.size());
        }
        else
        {
            // 获取指定知识库的文档数量
            bool ok;
            obx_id kbIdNum = kbId.toULongLong(&ok);
            if (!ok)
            {
                qDebug() << "invalid knowledge base ID";
                return 0;
            }

            // 查询指定知识库的文档数量
            auto allDocs = m_docBox->getAll();
            int count = 0;
            for (const auto &doc : allDocs)
            {
                if (doc->kb_id == kbIdNum)
                {
                    count++;
                }
            }
            return count;
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "get document count failed:" << e.what();
        return 0;
    }
}

// Helper methods
QString KnowledgeApi::objectToJson(const KnowledgeBase &kb)
{
    QJsonObject obj = kbToJsonObject(kb);
    return QJsonDocument(obj).toJson(QJsonDocument::Compact);
}

QString KnowledgeApi::objectToJson(const KnowledgeDocument &doc)
{
    QJsonObject obj = docToJsonObject(doc);
    return QJsonDocument(obj).toJson(QJsonDocument::Compact);
}

QJsonObject KnowledgeApi::kbToJsonObject(const KnowledgeBase &kb)
{
    QJsonObject obj;
    // 保持ID为数字类型，与前端类型定义一致
    obj["id"] = static_cast<qint64>(kb.id);
    obj["name"] = QString::fromStdString(kb.name);
    obj["description"] = QString::fromStdString(kb.description);
    obj["user_id"] = QString::fromStdString(kb.user_id);
    obj["created_at"] = static_cast<qint64>(kb.created_at);
    obj["updated_at"] = static_cast<qint64>(kb.updated_at);
    return obj;
}

QJsonObject KnowledgeApi::docToJsonObject(const KnowledgeDocument &doc)
{
    QJsonObject obj;
    // 保持ID为数字类型，与前端类型定义一致
    obj["id"] = static_cast<qint64>(doc.id);
    obj["kb_id"] = static_cast<qint64>(doc.kb_id);
    obj["title"] = QString::fromStdString(doc.title);
    obj["content"] = QString::fromStdString(doc.content);
    obj["document_type"] = QString::fromStdString(doc.document_type);
    obj["metadata"] = QString::fromStdString(doc.metadata);
    obj["created_at"] = static_cast<qint64>(doc.created_at);
    obj["updated_at"] = static_cast<qint64>(doc.updated_at);
    return obj;
}

bool KnowledgeApi::createDirectories()
{
    QString dbPath = getKnowledgeDatabasePath();
    QDir dir = QFileInfo(dbPath).absoluteDir();
    if (!dir.exists())
    {
        if (!dir.mkpath("."))
        {
            qDebug() << "cannot create ObjectBox database directory:" << dir.absolutePath();
            return false;
        }
    }
    return true;
}

QString KnowledgeApi::getKnowledgeDatabasePath()
{
    // 使用与DatabaseApi相同的路径配置，确保所有用户数据统一存储在AppData\Roaming\inkCop\user下
    QString basePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);

    // Windows路径处理：确保使用正确的路径分隔符
    QString userPath = QDir(basePath).filePath("user");
    QString knowledgePath = QDir(userPath).filePath("knowledge");

    // 确保路径使用本地文件系统的分隔符
    return QDir::toNativeSeparators(knowledgePath);
}

QString KnowledgeApi::testConnection()
{
    QJsonObject response;

    try
    {
        if (!m_store)
        {
            response["success"] = false;
            response["message"] = "ObjectBox Store not initialized";
            response["details"] = "m_store is null";
        }
        else if (!m_kbBox)
        {
            response["success"] = false;
            response["message"] = "KnowledgeBase Box not initialized";
            response["details"] = "m_kbBox is null";
        }
        else if (!m_docBox)
        {
            response["success"] = false;
            response["message"] = "Document Box not initialized";
            response["details"] = "m_docBox is null";
        }
        else
        {
            // 尝试获取数据库统计信息
            auto allKbs = m_kbBox->getAll();
            response["success"] = true;
            response["message"] = "ObjectBox connection normal";
            response["details"] = QString("current knowledge base count: %1").arg(allKbs.size());
            response["kb_count"] = static_cast<int>(allKbs.size());
            response["database_path"] = getKnowledgeDatabasePath();
        }
    }
    catch (const std::exception &e)
    {
        response["success"] = false;
        response["message"] = "ObjectBox connection test failed";
        response["details"] = QString("exception: %1").arg(e.what());
    }

    return QJsonDocument(response).toJson(QJsonDocument::Compact);
}

QString KnowledgeApi::debugObjectBoxData(const QString &filter)
{
    qDebug() << "[KNOWLEDGE] Debugging ObjectBox data with filter:" << filter;

    QJsonObject debug;

    try
    {
        if (!m_kbBox || !m_docBox || !m_chunkBox || !m_queryBox)
        {
            debug["error"] = "Database not initialized";
            return QJsonDocument(debug).toJson(QJsonDocument::Compact);
        }

        debug["success"] = true;
        debug["database_path"] = getKnowledgeDatabasePath();
        debug["filter"] = filter;
        debug["debug_timestamp"] = QDateTime::currentMSecsSinceEpoch();

        // 获取所有知识库
        auto allKbs = m_kbBox->getAll();
        debug["total_knowledge_bases"] = static_cast<int>(allKbs.size());

        QJsonArray kbArray;
        for (const auto &kb : allKbs)
        {
            // 如果有过滤条件，检查是否匹配
            if (!filter.isEmpty())
            {
                QString kbName = QString::fromStdString(kb->name).toLower();
                QString kbDesc = QString::fromStdString(kb->description).toLower();
                QString filterLower = filter.toLower();

                if (!kbName.contains(filterLower) && !kbDesc.contains(filterLower))
                {
                    continue;
                }
            }

            QJsonObject kbInfo;
            kbInfo["id"] = QString::number(kb->id);
            kbInfo["name"] = QString::fromStdString(kb->name);
            kbInfo["description"] = QString::fromStdString(kb->description);
            kbInfo["user_id"] = QString::fromStdString(kb->user_id);
            kbInfo["created_at"] = static_cast<qint64>(kb->created_at);
            kbInfo["updated_at"] = static_cast<qint64>(kb->updated_at);

            // 添加创建时间的可读格式
            QDateTime createdTime = QDateTime::fromMSecsSinceEpoch(kb->created_at);
            QDateTime updatedTime = QDateTime::fromMSecsSinceEpoch(kb->updated_at);
            kbInfo["created_at_readable"] = createdTime.toString("yyyy-MM-dd hh:mm:ss");
            kbInfo["updated_at_readable"] = updatedTime.toString("yyyy-MM-dd hh:mm:ss");

            kbArray.append(kbInfo);
        }
        debug["knowledge_bases"] = kbArray;
        debug["filtered_knowledge_bases_count"] = kbArray.size();

        // 获取所有文档
        auto allDocs = m_docBox->getAll();
        debug["total_documents"] = static_cast<int>(allDocs.size());

        QJsonArray docArray;
        int filteredDocCount = 0;
        for (const auto &doc : allDocs)
        {
            // 如果有过滤条件，检查是否匹配
            if (!filter.isEmpty())
            {
                QString docTitle = QString::fromStdString(doc->title).toLower();
                QString docContent = QString::fromStdString(doc->content).left(200).toLower();
                QString filterLower = filter.toLower();

                if (!docTitle.contains(filterLower) && !docContent.contains(filterLower))
                {
                    continue;
                }
            }

            QJsonObject docInfo;
            docInfo["id"] = QString::number(doc->id);
            docInfo["kb_id"] = QString::number(doc->kb_id);
            docInfo["title"] = QString::fromStdString(doc->title);
            docInfo["document_type"] = QString::fromStdString(doc->document_type);
            docInfo["content_length"] = static_cast<int>(doc->content.length());
            // docInfo["content_preview"] = QString::fromStdString(doc->content).left(100);
            docInfo["content_preview"] = QString::fromStdString(doc->content);
            docInfo["metadata"] = QString::fromStdString(doc->metadata);
            docInfo["created_at"] = static_cast<qint64>(doc->created_at);
            docInfo["updated_at"] = static_cast<qint64>(doc->updated_at);

            // 添加时间的可读格式
            QDateTime createdTime = QDateTime::fromMSecsSinceEpoch(doc->created_at);
            QDateTime updatedTime = QDateTime::fromMSecsSinceEpoch(doc->updated_at);
            docInfo["created_at_readable"] = createdTime.toString("yyyy-MM-dd hh:mm:ss");
            docInfo["updated_at_readable"] = updatedTime.toString("yyyy-MM-dd hh:mm:ss");

            docArray.append(docInfo);
            filteredDocCount++;
        }
        debug["documents"] = docArray;
        debug["filtered_documents_count"] = filteredDocCount;

        // 获取所有chunks
        auto allChunks = m_chunkBox->getAll();
        debug["total_chunks"] = static_cast<int>(allChunks.size());

        QJsonArray chunkArray;
        int filteredChunkCount = 0;
        int chunksWithEmbedding = 0;
        for (const auto &chunk : allChunks)
        {
            // 如果有过滤条件，检查是否匹配
            if (!filter.isEmpty())
            {
                QString chunkContent = QString::fromStdString(chunk->content).toLower();
                QString filterLower = filter.toLower();

                if (!chunkContent.contains(filterLower))
                {
                    continue;
                }
            }

            QJsonObject chunkInfo;
            chunkInfo["id"] = QString::number(chunk->id);
            chunkInfo["document_id"] = QString::number(chunk->knowledge_document_id);
            chunkInfo["chunk_index"] = static_cast<int>(chunk->chunk_index);
            chunkInfo["content_length"] = static_cast<int>(chunk->content.length());
            chunkInfo["content_preview"] = QString::fromStdString(chunk->content);
            // chunkInfo["content_preview"] = QString::fromStdString(chunk->content).left(150);
            chunkInfo["has_embedding"] = !chunk->embedding.empty();
            chunkInfo["embedding_dimension"] = static_cast<int>(chunk->embedding.size());
            chunkInfo["is_vectorized"] = chunk->is_vectorized;
            chunkInfo["metadata"] = QString::fromStdString(chunk->metadata);
            chunkInfo["created_at"] = static_cast<qint64>(chunk->created_at);

            // 添加时间的可读格式
            QDateTime createdTime = QDateTime::fromMSecsSinceEpoch(chunk->created_at);
            chunkInfo["created_at_readable"] = createdTime.toString("yyyy-MM-dd hh:mm:ss");

            if (!chunk->embedding.empty())
            {
                chunksWithEmbedding++;
                // 提供embedding的前5个值作为预览
                QJsonArray embeddingPreview;
                int previewSize = std::min(5, static_cast<int>(chunk->embedding.size()));
                for (int i = 0; i < previewSize; ++i)
                {
                    embeddingPreview.append(static_cast<double>(chunk->embedding[i]));
                }
                chunkInfo["embedding_preview"] = embeddingPreview;
            }

            chunkArray.append(chunkInfo);
            filteredChunkCount++;
        }
        debug["chunks"] = chunkArray;
        debug["filtered_chunks_count"] = filteredChunkCount;
        debug["chunks_with_embedding"] = chunksWithEmbedding;
        debug["embedding_coverage_percentage"] = filteredChunkCount > 0 ? static_cast<double>(chunksWithEmbedding) / static_cast<double>(filteredChunkCount) * 100.0 : 0.0;

        // 获取所有查询记录
        auto allQueries = m_queryBox->getAll();
        debug["total_queries"] = static_cast<int>(allQueries.size());

        QJsonArray queryArray;
        int filteredQueryCount = 0;
        for (const auto &query : allQueries)
        {
            // 如果有过滤条件，检查是否匹配
            if (!filter.isEmpty())
            {
                QString queryText = QString::fromStdString(query->query_text).toLower();
                QString filterLower = filter.toLower();

                if (!queryText.contains(filterLower))
                {
                    continue;
                }
            }

            QJsonObject queryInfo;
            queryInfo["id"] = QString::number(query->id);
            queryInfo["knowledge_base_id"] = QString::number(query->knowledge_base_id);
            queryInfo["query_text"] = QString::fromStdString(query->query_text);
            queryInfo["results_preview"] = QString::fromStdString(query->results).left(100);
            queryInfo["created_at"] = static_cast<qint64>(query->created_at);

            // 添加时间的可读格式
            QDateTime createdTime = QDateTime::fromMSecsSinceEpoch(query->created_at);
            queryInfo["created_at_readable"] = createdTime.toString("yyyy-MM-dd hh:mm:ss");

            queryArray.append(queryInfo);
            filteredQueryCount++;
        }
        debug["queries"] = queryArray;
        debug["filtered_queries_count"] = filteredQueryCount;

        // 系统状态信息
        QJsonObject systemInfo;
        systemInfo["local_gguf_loaded"] = isLocalGGUFLoaded();
        systemInfo["api_configured"] = isApiConfigured();
        systemInfo["embedding_mode"] = "unknown";

        if (m_databaseApi)
        {
            QJsonObject settings = m_databaseApi->getAppSettings();
            if (!settings.isEmpty())
            {
                QJsonObject kbSettings = settings["knowledgeBase"].toObject();
                systemInfo["embedding_mode"] = kbSettings["embeddingMode"].toString("cloud");
            }
        }

        debug["system_info"] = systemInfo;

        // 性能统计 - 使用默认配置值
        QJsonObject performance;
        performance["vectorization_config"] = QJsonObject::fromVariantMap(QVariantMap{
            {"chunk_size", 800},
            {"chunk_overlap", 200},
            {"semantic_threshold", 0.7},
            {"search_limit", 10},
            {"enable_multi_semantic", true},
            {"enable_hierarchical", true}});
        debug["performance"] = performance;

        return QJsonDocument(debug).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        debug["error"] = QString("Exception during debugging: %1").arg(e.what());
        return QJsonDocument(debug).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::debugEmbeddingGeneration(const QString &text)
{
    qDebug() << "🔍 [KnowledgeApi] Debug embedding generation for text length:" << text.length();

    QJsonObject debug;
    debug["success"] = true;
    debug["debug_timestamp"] = QDateTime::currentMSecsSinceEpoch();
    debug["input_text"] = text;
    debug["input_length"] = text.length();

    try
    {
        QElapsedTimer totalTimer;
        totalTimer.start();

        // 1. 检查系统配置
        QJsonObject configInfo;
        configInfo["api_configured"] = isApiConfigured();
        configInfo["local_gguf_loaded"] = isLocalGGUFLoaded();

        if (m_databaseApi)
        {
            QJsonObject settings = m_databaseApi->getAppSettings();
            if (!settings.isEmpty())
            {
                QJsonObject kbSettings = settings["knowledgeBase"].toObject();
                configInfo["embedding_mode"] = kbSettings["embeddingMode"].toString("cloud");
                configInfo["base_url"] = kbSettings["baseUrl"].toString();
                configInfo["model"] = kbSettings["model"].toString();
                configInfo["api_key_configured"] = !kbSettings["apiKey"].toString().isEmpty();
                configInfo["local_model_path"] = kbSettings["localModelPath"].toString();
            }
        }

        // 使用默认配置值
        configInfo["chunk_size"] = 800;
        configInfo["chunk_overlap"] = 200;
        configInfo["semantic_threshold"] = 0.7;
        configInfo["enable_multi_semantic"] = true;
        configInfo["enable_hierarchical"] = true;

        debug["configuration"] = configInfo;

        // 2. 尝试不同的embedding方法并记录结果
        QJsonArray embeddingAttempts;

        // 2.1 本地GGUF模型尝试
        if (isLocalGGUFLoaded())
        {
            QJsonObject localAttempt;
            localAttempt["method"] = "local_gguf";
            localAttempt["available"] = true;

            QElapsedTimer localTimer;
            localTimer.start();

            try
            {
                std::vector<float> localEmbedding = generateLocalGGUFEmbedding(text);
                qint64 localTime = localTimer.elapsed();

                localAttempt["success"] = !localEmbedding.empty();
                localAttempt["inference_time_ms"] = localTime;
                localAttempt["dimension"] = static_cast<int>(localEmbedding.size());
                localAttempt["gpu_enabled"] = m_localGGUF->isGpuEnabled();

                if (!localEmbedding.empty())
                {
                    // 提供前10个值作为预览
                    QJsonArray preview;
                    int previewSize = qMin(10, static_cast<int>(localEmbedding.size()));
                    for (int i = 0; i < previewSize; ++i)
                    {
                        preview.append(static_cast<double>(localEmbedding[i]));
                    }
                    localAttempt["embedding_preview"] = preview;

                    // 计算向量统计
                    float sum = 0.0f, minVal = localEmbedding[0], maxVal = localEmbedding[0];
                    for (float val : localEmbedding)
                    {
                        sum += val;
                        minVal = qMin(minVal, val);
                        maxVal = qMax(maxVal, val);
                    }
                    localAttempt["embedding_stats"] = QJsonObject{
                        {"mean", static_cast<double>(sum / localEmbedding.size())},
                        {"min", static_cast<double>(minVal)},
                        {"max", static_cast<double>(maxVal)}};
                }
                else
                {
                    localAttempt["error"] = m_localGGUF->getLastError();
                }
            }
            catch (const std::exception &e)
            {
                localAttempt["success"] = false;
                localAttempt["error"] = QString("Exception: %1").arg(e.what());
                localAttempt["inference_time_ms"] = localTimer.elapsed();
            }

            embeddingAttempts.append(localAttempt);
        }
        else
        {
            QJsonObject localAttempt;
            localAttempt["method"] = "local_gguf";
            localAttempt["available"] = false;
            localAttempt["reason"] = "No local GGUF model loaded";
            embeddingAttempts.append(localAttempt);
        }

        // 2.2 云端API尝试
        if (isApiConfigured())
        {
            QJsonObject apiAttempt;
            apiAttempt["method"] = "cloud_api";
            apiAttempt["available"] = true;

            QElapsedTimer apiTimer;
            apiTimer.start();

            try
            {
                std::vector<float> apiEmbedding = generateApiEmbedding(text);
                qint64 apiTime = apiTimer.elapsed();

                apiAttempt["success"] = !apiEmbedding.empty();
                apiAttempt["inference_time_ms"] = apiTime;
                apiAttempt["dimension"] = static_cast<int>(apiEmbedding.size());

                if (!apiEmbedding.empty())
                {
                    // 提供前10个值作为预览
                    QJsonArray preview;
                    int previewSize = qMin(10, static_cast<int>(apiEmbedding.size()));
                    for (int i = 0; i < previewSize; ++i)
                    {
                        preview.append(static_cast<double>(apiEmbedding[i]));
                    }
                    apiAttempt["embedding_preview"] = preview;

                    // 计算向量统计
                    float sum = 0.0f, minVal = apiEmbedding[0], maxVal = apiEmbedding[0];
                    for (float val : apiEmbedding)
                    {
                        sum += val;
                        minVal = qMin(minVal, val);
                        maxVal = qMax(maxVal, val);
                    }
                    apiAttempt["embedding_stats"] = QJsonObject{
                        {"mean", static_cast<double>(sum / apiEmbedding.size())},
                        {"min", static_cast<double>(minVal)},
                        {"max", static_cast<double>(maxVal)}};
                }
                else
                {
                    apiAttempt["error"] = "API returned empty embedding";
                }
            }
            catch (const std::exception &e)
            {
                apiAttempt["success"] = false;
                apiAttempt["error"] = QString("Exception: %1").arg(e.what());
                apiAttempt["inference_time_ms"] = apiTimer.elapsed();
            }

            embeddingAttempts.append(apiAttempt);
        }
        else
        {
            QJsonObject apiAttempt;
            apiAttempt["method"] = "cloud_api";
            apiAttempt["available"] = false;
            apiAttempt["reason"] = "API not configured";
            embeddingAttempts.append(apiAttempt);
        }

        // 2.3 多重语义embedding尝试（如果文本够长）
        int chunkSize = 800;
        bool enableMultiSemantic = true;
        if (text.length() > chunkSize && enableMultiSemantic && isApiConfigured())
        {
            QJsonObject multiAttempt;
            multiAttempt["method"] = "multi_semantic";
            multiAttempt["available"] = true;

            QElapsedTimer multiTimer;
            multiTimer.start();

            try
            {
                std::vector<float> multiEmbedding = generateMultiSemanticEmbedding(text);
                qint64 multiTime = multiTimer.elapsed();

                multiAttempt["success"] = !multiEmbedding.empty();
                multiAttempt["inference_time_ms"] = multiTime;
                multiAttempt["dimension"] = static_cast<int>(multiEmbedding.size());

                // 计算分块信息 - 使用简单段落分割
                QStringList chunks = text.split(QRegularExpression("\n\\s*\n"), Qt::SkipEmptyParts);
                if (chunks.size() <= 1)
                {
                    // 如果没有段落分割，使用字符分割
                    chunks.clear();
                    QString cleanText = text.trimmed();
                    int start = 0;
                    while (start < cleanText.length())
                    {
                        int end = qMin(start + chunkSize, cleanText.length());
                        QString chunk = cleanText.mid(start, end - start).trimmed();
                        if (!chunk.isEmpty())
                        {
                            chunks.append(chunk);
                        }
                        start = end;
                    }
                }
                multiAttempt["chunk_count"] = chunks.size();
                multiAttempt["chunk_sizes"] = QJsonArray();
                for (const QString &chunk : chunks)
                {
                    QJsonArray chunkSizes = multiAttempt["chunk_sizes"].toArray();
                    chunkSizes.append(chunk.length());
                    multiAttempt["chunk_sizes"] = chunkSizes;
                }

                if (!multiEmbedding.empty())
                {
                    // 提供前10个值作为预览
                    QJsonArray preview;
                    int previewSize = qMin(10, static_cast<int>(multiEmbedding.size()));
                    for (int i = 0; i < previewSize; ++i)
                    {
                        preview.append(static_cast<double>(multiEmbedding[i]));
                    }
                    multiAttempt["embedding_preview"] = preview;

                    // 计算向量统计
                    float sum = 0.0f, minVal = multiEmbedding[0], maxVal = multiEmbedding[0];
                    for (float val : multiEmbedding)
                    {
                        sum += val;
                        minVal = qMin(minVal, val);
                        maxVal = qMax(maxVal, val);
                    }
                    multiAttempt["embedding_stats"] = QJsonObject{
                        {"mean", static_cast<double>(sum / multiEmbedding.size())},
                        {"min", static_cast<double>(minVal)},
                        {"max", static_cast<double>(maxVal)}};
                }
                else
                {
                    multiAttempt["error"] = "Multi-semantic embedding returned empty result";
                }
            }
            catch (const std::exception &e)
            {
                multiAttempt["success"] = false;
                multiAttempt["error"] = QString("Exception: %1").arg(e.what());
                multiAttempt["inference_time_ms"] = multiTimer.elapsed();
            }

            embeddingAttempts.append(multiAttempt);
        }
        else
        {
            QJsonObject multiAttempt;
            multiAttempt["method"] = "multi_semantic";
            multiAttempt["available"] = false;

            if (text.length() <= chunkSize)
            {
                multiAttempt["reason"] = QString("Text too short (%1 <= %2)").arg(text.length()).arg(chunkSize);
            }
            else if (!enableMultiSemantic)
            {
                multiAttempt["reason"] = "Multi-semantic disabled in config";
            }
            else
            {
                multiAttempt["reason"] = "API not configured";
            }

            embeddingAttempts.append(multiAttempt);
        }

        // 2.4 简单TF-IDF向量化尝试（总是可用）
        {
            QJsonObject simpleAttempt;
            simpleAttempt["method"] = "simple_tfidf";
            simpleAttempt["available"] = true;

            QElapsedTimer simpleTimer;
            simpleTimer.start();

            try
            {
                std::vector<float> simpleEmbedding = generateSimpleEmbedding(text);
                qint64 simpleTime = simpleTimer.elapsed();

                simpleAttempt["success"] = !simpleEmbedding.empty();
                simpleAttempt["inference_time_ms"] = simpleTime;
                simpleAttempt["dimension"] = static_cast<int>(simpleEmbedding.size());

                if (!simpleEmbedding.empty())
                {
                    // 提供前10个值作为预览
                    QJsonArray preview;
                    int previewSize = qMin(10, static_cast<int>(simpleEmbedding.size()));
                    for (int i = 0; i < previewSize; ++i)
                    {
                        preview.append(static_cast<double>(simpleEmbedding[i]));
                    }
                    simpleAttempt["embedding_preview"] = preview;

                    // 计算向量统计
                    float sum = 0.0f, minVal = simpleEmbedding[0], maxVal = simpleEmbedding[0];
                    for (float val : simpleEmbedding)
                    {
                        sum += val;
                        minVal = qMin(minVal, val);
                        maxVal = qMax(maxVal, val);
                    }
                    simpleAttempt["embedding_stats"] = QJsonObject{
                        {"mean", static_cast<double>(sum / simpleEmbedding.size())},
                        {"min", static_cast<double>(minVal)},
                        {"max", static_cast<double>(maxVal)}};

                    // TF-IDF特有的统计信息
                    QStringList words = text.toLower().split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
                    QHash<QString, int> wordFreq;
                    for (const QString &word : words)
                    {
                        if (word.length() > 1)
                        {
                            wordFreq[word]++;
                        }
                    }
                    simpleAttempt["word_analysis"] = QJsonObject{
                        {"total_words", words.size()},
                        {"unique_words", wordFreq.size()},
                        {"vocabulary_richness", words.size() > 0 ? static_cast<double>(wordFreq.size()) / static_cast<double>(words.size()) : 0.0}};
                }
                else
                {
                    simpleAttempt["error"] = "Simple embedding failed";
                }
            }
            catch (const std::exception &e)
            {
                simpleAttempt["success"] = false;
                simpleAttempt["error"] = QString("Exception: %1").arg(e.what());
                simpleAttempt["inference_time_ms"] = simpleTimer.elapsed();
            }

            embeddingAttempts.append(simpleAttempt);
        }

        debug["embedding_attempts"] = embeddingAttempts;

        // 3. 测试实际的textToEmbedding方法（这是系统实际使用的方法）
        QJsonObject actualTest;
        QElapsedTimer actualTimer;
        actualTimer.start();

        try
        {
            std::vector<float> actualEmbedding = textToEmbedding(text);
            qint64 actualTime = actualTimer.elapsed();

            actualTest["success"] = !actualEmbedding.empty();
            actualTest["inference_time_ms"] = actualTime;
            actualTest["dimension"] = static_cast<int>(actualEmbedding.size());

            if (!actualEmbedding.empty())
            {
                // 提供前10个值作为预览
                QJsonArray preview;
                int previewSize = qMin(10, static_cast<int>(actualEmbedding.size()));
                for (int i = 0; i < previewSize; ++i)
                {
                    preview.append(static_cast<double>(actualEmbedding[i]));
                }
                actualTest["embedding_preview"] = preview;

                // 计算向量统计
                float sum = 0.0f, minVal = actualEmbedding[0], maxVal = actualEmbedding[0];
                for (float val : actualEmbedding)
                {
                    sum += val;
                    minVal = qMin(minVal, val);
                    maxVal = qMax(maxVal, val);
                }
                actualTest["embedding_stats"] = QJsonObject{
                    {"mean", static_cast<double>(sum / actualEmbedding.size())},
                    {"min", static_cast<double>(minVal)},
                    {"max", static_cast<double>(maxVal)}};
            }
            else
            {
                actualTest["error"] = "textToEmbedding returned empty result";
            }
        }
        catch (const std::exception &e)
        {
            actualTest["success"] = false;
            actualTest["error"] = QString("Exception: %1").arg(e.what());
            actualTest["inference_time_ms"] = actualTimer.elapsed();
        }

        debug["actual_embedding_test"] = actualTest;

        // 4. 文本分析
        QJsonObject textAnalysis;
        textAnalysis["character_count"] = text.length();
        textAnalysis["word_count"] = text.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts).size();
        textAnalysis["line_count"] = text.split('\n').size();
        textAnalysis["paragraph_count"] = text.split(QRegularExpression("\n\\s*\n"), Qt::SkipEmptyParts).size();
        // 简单的文档类型检测
        QString lowerText = text.left(1000).toLower();
        QString docType = "text";
        if (lowerText.contains("```") || lowerText.contains("function") || lowerText.contains("class"))
        {
            docType = "code";
        }
        else if (lowerText.contains("#") || lowerText.contains("**"))
        {
            docType = "markdown";
        }
        textAnalysis["detected_document_type"] = docType;

        // 分块预览 - 使用简单切割逻辑
        int chunkOverlap = 200;
        QStringList chunks;

        // 简单的字符切割
        QString cleanText = text.trimmed();
        int start = 0;
        while (start < cleanText.length())
        {
            int end = qMin(start + chunkSize, cleanText.length());
            QString chunk = cleanText.mid(start, end - start).trimmed();
            if (!chunk.isEmpty())
            {
                chunks.append(chunk);
            }
            start = qMax(start + 1, end - chunkOverlap);
        }
        textAnalysis["chunk_count"] = chunks.size();
        QJsonArray chunkPreview;
        for (int i = 0; i < qMin(3, chunks.size()); ++i)
        {
            chunkPreview.append(QJsonObject{
                {"index", i},
                {"length", chunks[i].length()},
                {"preview", chunks[i].left(100)}});
        }
        textAnalysis["chunk_preview"] = chunkPreview;

        debug["text_analysis"] = textAnalysis;

        // 5. 总体性能
        qint64 totalTime = totalTimer.elapsed();
        debug["total_debug_time_ms"] = totalTime;
        debug["message"] = QString("Embedding debug completed in %1ms").arg(totalTime);

        return QJsonDocument(debug).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        debug["success"] = false;
        debug["error"] = QString("Debug failed: %1").arg(e.what());
        return QJsonDocument(debug).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::createKnowledgeDocumentOnly(const QString &kbId, const QString &title, const QString &content, const QString &documentType)
{
    qDebug() << "🚀 [KnowledgeApi] Create knowledge base document (only document, no vectorization):" << title;

    try
    {
        if (!m_kbBox || !m_docBox)
        {
            logError("Database not initialized", "createKnowledgeDocumentOnly");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 验证知识库ID
        bool ok;
        obx_id kbIdNum = kbId.toULongLong(&ok);
        if (!ok || kbIdNum == 0)
        {
            logError("Invalid knowledge base ID", "createKnowledgeDocumentOnly");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 检查知识库是否存在
        auto kb = m_kbBox->get(kbIdNum);
        if (!kb)
        {
            logError("Knowledge base not found", "createKnowledgeDocumentOnly");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Knowledge base not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 创建知识库文档
        logDebug("Creating knowledge base document (document only)", "createKnowledgeDocumentOnly");
        KnowledgeDocument doc;
        doc.id = 0; // ObjectBox会自动分配ID
        doc.kb_id = kbIdNum;
        doc.title = title.toStdString();
        doc.content = content.toStdString();
        doc.document_type = documentType.toStdString();
        doc.metadata = "{}"; // 默认空metadata
        doc.created_at = QDateTime::currentMSecsSinceEpoch();
        doc.updated_at = doc.created_at;

        // 保存文档
        logDebug("Saving document to database", "createKnowledgeDocumentOnly");
        obx_id newDocId = m_docBox->put(doc);
        if (newDocId == 0)
        {
            logError("Document save failed", "createKnowledgeDocumentOnly");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Document save failed";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 获取保存后的文档
        auto savedDoc = m_docBox->get(newDocId);
        if (!savedDoc)
        {
            logError("Failed to retrieve saved document", "createKnowledgeDocumentOnly");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Failed to retrieve saved document";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 立即返回文档创建结果，不进行向量化
        QJsonObject docData = docToJsonObject(*savedDoc);
        docData["chunk_count"] = 0;    // 初始为0，等待切割结果提交
        docData["processing"] = false; // 标记未开始处理

        QJsonObject response;
        response["success"] = true;
        response["message"] = "Document created successfully, waiting for chunking results";
        response["data"] = docData;

        logDebug(QString("Document created successfully with ID: %1").arg(newDocId), "createKnowledgeDocumentOnly");
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        logError(QString("Exception in createKnowledgeDocumentOnly: %1").arg(e.what()), "createKnowledgeDocumentOnly");
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Exception: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::submitChunkingResults(const QString &docId, const QJsonArray &chunks)
{
    qDebug() << "📤 [KnowledgeApi] Receiving chunking results, document ID:" << docId << ", chunk count:" << chunks.size();

    try
    {
        if (!m_docBox || !m_chunkBox)
        {
            logError("Database not initialized", "submitChunkingResults");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 验证文档ID
        bool ok;
        obx_id docIdNum = docId.toULongLong(&ok);
        if (!ok || docIdNum == 0)
        {
            logError("Invalid document ID", "submitChunkingResults");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Invalid document ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 检查文档是否存在
        auto doc = m_docBox->get(docIdNum);
        if (!doc)
        {
            logError("Document not found", "submitChunkingResults");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Document not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 删除该文档的现有chunks（如果有）
        auto query = m_chunkBox->query(KnowledgeChunk_::knowledge_document_id.equals(docIdNum)).build();
        auto existingChunks = query.find();
        if (!existingChunks.empty())
        {
            qDebug() << "🗑️ [KnowledgeApi] Delete existing chunks:" << existingChunks.size();
            for (const auto &chunk : existingChunks)
            {
                m_chunkBox->remove(chunk.id);
            }
        }

        // 存储新的chunks到数据库
        int savedChunkCount = 0;
        for (int i = 0; i < chunks.size(); ++i)
        {
            const QJsonValue &chunkValue = chunks[i];
            if (!chunkValue.isObject())
            {
                qWarning() << "⚠️ [KnowledgeApi] Skip invalid chunk data, index:" << i;
                continue;
            }

            QJsonObject chunkObj = chunkValue.toObject();
            QString chunkContent = chunkObj["pageContent"].toString();
            if (chunkContent.trimmed().isEmpty())
            {
                qWarning() << "⚠️ [KnowledgeApi] Skip blank chunk, index:" << i;
                continue;
            }

            // 创建chunk记录
            KnowledgeChunk chunk;
            chunk.id = 0; // ObjectBox会自动分配ID
            chunk.knowledge_document_id = docIdNum;
            chunk.chunk_index = static_cast<uint32_t>(i);
            chunk.content = chunkContent.toStdString();
            chunk.embedding = {}; // 暂时为空，等待向量化
            chunk.metadata = QJsonDocument(chunkObj["metadata"].toObject()).toJson(QJsonDocument::Compact).toStdString();
            chunk.created_at = QDateTime::currentMSecsSinceEpoch();
            chunk.is_vectorized = false; // 标记为未向量化

            obx_id chunkId = m_chunkBox->put(chunk);
            if (chunkId > 0)
            {
                savedChunkCount++;
                qDebug() << "💾 [KnowledgeApi] Save chunk" << i + 1 << "/" << chunks.size() << ", ID:" << chunkId;
            }
            else
            {
                qWarning() << "⚠️ [KnowledgeApi] Save chunk failed, index:" << i;
            }
        }

        qDebug() << "✅ [KnowledgeApi] Chunking results saved successfully, saved" << savedChunkCount << "chunks";

        // 返回保存结果
        QJsonObject response;
        response["success"] = true;
        response["message"] = "Chunking results saved successfully";
        response["document_id"] = docId;
        response["chunks_saved"] = savedChunkCount;
        response["chunks_total"] = chunks.size();

        // 异步启动向量化处理
        QTimer::singleShot(100, this, [this, docIdNum]()
                           { processDocumentVectorizationAsync(docIdNum); });

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        logError(QString("Exception in submitChunkingResults: %1").arg(e.what()), "submitChunkingResults");
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Exception: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

void KnowledgeApi::processDocumentVectorizationAsync(obx_id docId)
{
    qDebug() << "🔄 [KnowledgeApi] Start asynchronous vectorization processing, document ID:" << docId;

    try
    {
        if (!m_chunkBox || !m_docBox)
        {
            logError("Database not initialized", "processDocumentVectorizationAsync");
            return;
        }

        // 获取文档信息，包括知识库ID
        auto doc = m_docBox->get(docId);
        if (!doc)
        {
            logError(QString("Document not found: %1").arg(docId), "processDocumentVectorizationAsync");
            return;
        }

        obx_id kbId = doc->kb_id;
        qDebug() << "📚 [KnowledgeApi] Document knowledge base ID:" << kbId;

        // 查询该文档的所有未向量化的chunks
        auto query = m_chunkBox->query(KnowledgeChunk_::knowledge_document_id.equals(docId)).build();
        auto chunks = query.find();

        if (chunks.empty())
        {
            qDebug() << "📭 [KnowledgeApi] No pending vectorization chunks found, document ID:" << docId;
            return;
        }

        qDebug() << "📊 [KnowledgeApi] Found" << chunks.size() << "chunks pending vectorization";

        // 使用后台线程处理向量化
        processDocumentVectorizationInBackground(docId, chunks);
    }
    catch (const std::exception &e)
    {
        logError(QString("Exception in processDocumentVectorizationAsync: %1").arg(e.what()), "processDocumentVectorizationAsync");
    }
}

QString KnowledgeApi::getVectorizationProgress(const QString &docId)
{
    try
    {
        if (!m_chunkBox)
        {
            logError("Database not initialized", "getVectorizationProgress");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 验证文档ID
        bool ok;
        obx_id docIdNum = docId.toULongLong(&ok);
        if (!ok || docIdNum == 0)
        {
            logError("Invalid document ID", "getVectorizationProgress");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Invalid document ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 查询该文档的chunks数量
        auto query = m_chunkBox->query(KnowledgeChunk_::knowledge_document_id.equals(docIdNum)).build();
        auto chunks = query.find();

        QJsonObject response;
        response["success"] = true;
        response["document_id"] = docId;
        response["pending_chunks"] = static_cast<int>(chunks.size());
        response["is_processing"] = chunks.size() > 0;

        if (chunks.size() > 0)
        {
            response["message"] = QString("Vectorization in progress, remaining %1 chunks").arg(chunks.size());
        }
        else
        {
            response["message"] = "Vectorization completed";
        }

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        logError(QString("Exception in getVectorizationProgress: %1").arg(e.what()), "getVectorizationProgress");
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Exception: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::checkPendingVectorization()
{
    try
    {
        if (!m_chunkBox)
        {
            logError("Database not initialized", "checkPendingVectorization");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 查询所有待向量化的chunks
        auto query = m_chunkBox->query().build();
        auto allChunks = query.find();

        // 按文档ID分组统计
        QMap<obx_id, int> documentChunkCounts;
        for (const auto &chunk : allChunks)
        {
            documentChunkCounts[chunk.knowledge_document_id]++;
        }

        QJsonArray pendingDocuments;
        for (auto it = documentChunkCounts.begin(); it != documentChunkCounts.end(); ++it)
        {
            QJsonObject docInfo;
            docInfo["document_id"] = QString::number(it.key());
            docInfo["pending_chunks"] = it.value();
            pendingDocuments.append(docInfo);
        }

        QJsonObject response;
        response["success"] = true;
        response["total_pending_chunks"] = static_cast<int>(allChunks.size());
        response["pending_documents"] = pendingDocuments;
        response["has_pending"] = allChunks.size() > 0;

        if (allChunks.size() > 0)
        {
            response["message"] = QString("Found %1 documents with %2 chunks pending vectorization")
                                      .arg(documentChunkCounts.size())
                                      .arg(allChunks.size());
        }
        else
        {
            response["message"] = "No chunks pending vectorization";
        }

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        logError(QString("Exception in checkPendingVectorization: %1").arg(e.what()), "checkPendingVectorization");
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Exception: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

void KnowledgeApi::checkAndRecoverPendingVectorization()
{
    qDebug() << "🔍 [KnowledgeApi] Checking for pending vectorization chunks on application startup...";

    try
    {
        if (!m_chunkBox)
        {
            logError("Database not initialized", "checkAndRecoverPendingVectorization");
            return;
        }

        // 检查向量化模式和模型状态
        QString embeddingMode = "cloud"; // 默认云端
        if (m_databaseApi)
        {
            QJsonObject settings = m_databaseApi->getAppSettings();
            if (!settings.isEmpty())
            {
                QJsonObject kbSettings = settings["knowledgeBase"].toObject();
                embeddingMode = kbSettings["embeddingMode"].toString("cloud");
            }
        }

        // 如果是本地模式但模型未加载，跳过自动恢复
        if (embeddingMode == "local" && !isLocalGGUFLoaded())
        {
            logInfo("Local embedding mode but model not loaded, skipping auto-recovery", "checkAndRecoverPendingVectorization");
            return;
        }

        // 查询所有chunks，然后在代码中过滤未向量化的
        // 这样避免了ObjectBox schema问题
        auto allChunksQuery = m_chunkBox->query().build();
        auto allChunksTemp = allChunksQuery.find();

        // 过滤出未向量化的chunks（embedding为空或is_vectorized为false）
        std::vector<KnowledgeChunk> allChunks;
        for (const auto &chunk : allChunksTemp)
        {
            // 检查是否未向量化：embedding为空或者is_vectorized为false
            if (chunk.embedding.empty() || !chunk.is_vectorized)
            {
                allChunks.push_back(chunk);
            }
        }

        if (allChunks.empty())
        {
            qDebug() << "📭 [KnowledgeApi] No pending vectorization chunks found";
            return;
        }

        // 按文档ID分组
        QMap<obx_id, QList<KnowledgeChunk>> documentChunks;
        for (const auto &chunk : allChunks)
        {
            documentChunks[chunk.knowledge_document_id].append(chunk);
        }

        qDebug() << "📊 [KnowledgeApi] Found" << documentChunks.size() << "documents with" << allChunks.size() << "chunks pending vectorization";

        // 限制同时处理的文档数量，避免过载
        int maxConcurrentDocs = 3;
        int processedDocs = 0;

        // 逐个文档处理向量化恢复（静默模式）
        for (auto it = documentChunks.begin(); it != documentChunks.end() && processedDocs < maxConcurrentDocs; ++it, ++processedDocs)
        {
            obx_id docId = it.key();
            const QList<KnowledgeChunk> &chunks = it.value();

            qDebug() << "🔄 [KnowledgeApi] Silent recovery document" << docId << "vectorization, total" << chunks.size() << "chunks";

            // 异步启动向量化处理，使用较长延迟避免同时启动过多任务
            QTimer::singleShot(processedDocs * 500, this, [this, docId]()
                               { processDocumentVectorizationAsync(docId); });
        }

        if (documentChunks.size() > maxConcurrentDocs)
        {
            qDebug() << "⚠️ [KnowledgeApi] Limit concurrent processing, remaining" << (documentChunks.size() - maxConcurrentDocs) << "documents will be processed later";
        }

        qDebug() << "✅ [KnowledgeApi] Vectorization recovery task started (silent mode)";
    }
    catch (const std::exception &e)
    {
        logError(QString("Exception in checkAndRecoverPendingVectorization: %1").arg(e.what()), "checkAndRecoverPendingVectorization");
    }
}

QString KnowledgeApi::checkAndResumeVectorization()
{
    try
    {
        if (!m_chunkBox || !m_docBox)
        {
            logError("Database not initialized", "checkAndResumeVectorization");
            QJsonObject response;
            response["success"] = false;
            response["message"] = "Database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        qDebug() << "🔍 [KnowledgeApi] Checking for incomplete vectorization tasks...";

        // 查询所有未向量化的chunks（is_vectorized = false）
        auto query = m_chunkBox->query(KnowledgeChunk_::is_vectorized.equals(false)).build();
        auto allChunks = query.find();

        QMap<obx_id, QList<KnowledgeChunk>> unvectorizedByDoc;
        for (const auto &chunk : allChunks)
        {
            unvectorizedByDoc[chunk.knowledge_document_id].append(chunk);
        }

        int totalUnvectorizedDocs = unvectorizedByDoc.size();
        int totalUnvectorizedChunks = static_cast<int>(allChunks.size());

        qDebug() << "📊 [KnowledgeApi] Found unvectorized data:"
                 << "documents:" << totalUnvectorizedDocs
                 << "chunks:" << totalUnvectorizedChunks;

        if (totalUnvectorizedDocs == 0)
        {
            QJsonObject response;
            response["success"] = true;
            response["message"] = "No incomplete vectorization tasks found";
            response["resumed_documents"] = 0;
            response["resumed_chunks"] = 0;
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 为每个文档启动向量化任务
        int resumedDocs = 0;
        for (auto it = unvectorizedByDoc.begin(); it != unvectorizedByDoc.end(); ++it)
        {
            obx_id docId = it.key();
            const QList<KnowledgeChunk> &chunks = it.value();

            qDebug() << "🚀 [KnowledgeApi] Recovering document" << docId << "vectorization, total" << chunks.size() << "chunks";

            // 在后台线程中处理向量化
            QTimer::singleShot(100 * resumedDocs, this, [this, docId]()
                               { this->processDocumentVectorizationAsync(docId); });

            resumedDocs++;
        }

        QJsonObject response;
        response["success"] = true;
        response["message"] = QString("Recovered %1 documents vectorization tasks").arg(resumedDocs);
        response["resumed_documents"] = resumedDocs;
        response["resumed_chunks"] = totalUnvectorizedChunks;

        qDebug() << "✅ [KnowledgeApi] Vectorization recovery completed, started" << resumedDocs << "documents vectorization tasks";

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        logError(QString("Exception in checkAndResumeVectorization: %1").arg(e.what()), "checkAndResumeVectorization");
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Error resuming vectorization: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::regenerateDocumentChunks(const QString &docId)
{
    qDebug() << "[KNOWLEDGE] Starting to regenerate knowledge chunks for document:" << docId;

    try
    {
        if (!m_docBox || !m_chunkBox)
        {
            qDebug() << "❌ [KnowledgeApi] database not initialized";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id id = docId.toULongLong(&ok);
        if (!ok)
        {
            qDebug() << "❌ [KnowledgeApi] invalid document ID:" << docId;
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid document ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 获取文档信息
        auto doc = m_docBox->get(id);
        if (!doc)
        {
            qDebug() << "❌ [KnowledgeApi] document not found:" << docId;
            QJsonObject response;
            response["success"] = false;
            response["message"] = "document not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        QString content = QString::fromStdString(doc->content);
        qDebug() << "📋 [KnowledgeApi] Document content length:" << content.length();

        // 1. 删除现有的chunks
        auto allChunks = m_chunkBox->getAll();
        int oldChunkCount = 0;
        int deletedChunkCount = 0;

        // 收集要删除的chunk ID
        QList<obx_id> chunkIdsToDelete;
        for (const auto &chunk : allChunks)
        {
            if (chunk->knowledge_document_id == id)
            {
                oldChunkCount++;
                chunkIdsToDelete.append(chunk->id);
            }
        }

        qDebug() << "🗑️ [KnowledgeApi] Delete existing" << oldChunkCount << "chunks...";

        // 删除所有旧chunks
        for (obx_id chunkId : chunkIdsToDelete)
        {
            bool chunkRemoved = m_chunkBox->remove(chunkId);
            if (chunkRemoved)
            {
                deletedChunkCount++;
            }
            else
            {
                qDebug() << "⚠️ [KnowledgeApi] delete old chunk failed:" << chunkId;
            }
        }

        qDebug() << "✅ [KnowledgeApi] Delete old chunks completed:" << deletedChunkCount << "/" << oldChunkCount;

        // 2. 重新创建chunks - 使用简单切割逻辑
        int chunkSize = 800;
        int chunkOverlap = 200;
        QStringList chunks;

        // 简单的字符切割
        QString cleanContent = content.trimmed();
        int start = 0;
        while (start < cleanContent.length())
        {
            int end = qMin(start + chunkSize, cleanContent.length());
            QString chunk = cleanContent.mid(start, end - start).trimmed();
            if (!chunk.isEmpty())
            {
                chunks.append(chunk);
            }
            start = qMax(start + 1, end - chunkOverlap);
        }
        int createdChunkCount = 0;

        for (int i = 0; i < chunks.size(); ++i)
        {
            const QString &chunkContent = chunks[i];
            if (chunkContent.trimmed().isEmpty())
            {
                continue; // 跳过空白chunks
            }

            KnowledgeChunk chunk;
            chunk.id = 0; // ObjectBox会自动分配ID
            chunk.knowledge_document_id = id;
            chunk.chunk_index = static_cast<uint32_t>(i);
            chunk.content = chunkContent.toStdString();
            chunk.embedding = textToEmbedding(chunkContent); // 自动生成向量数据
            chunk.metadata = QString("{}").toStdString();    // 默认空metadata
            chunk.created_at = QDateTime::currentMSecsSinceEpoch();
            chunk.is_vectorized = !chunk.embedding.empty(); // 根据是否有向量数据设置状态

            obx_id chunkId = m_chunkBox->put(chunk);
            if (chunkId > 0)
            {
                createdChunkCount++;
                qDebug() << "✅ [KnowledgeApi] Create new knowledge chunk" << i + 1 << "/" << chunks.size() << ", ID:" << chunkId;
            }
            else
            {
                qDebug() << "⚠️ [KnowledgeApi] Create new knowledge chunk failed:" << i + 1;
            }
        }

        qDebug() << "🎉 [KnowledgeApi] document chunk regenerate completed - delete old chunks:" << deletedChunkCount << ", create new chunks:" << createdChunkCount;

        QJsonObject response;
        response["success"] = true;
        response["message"] = QString("regenerate %1 knowledge chunks").arg(createdChunkCount);
        response["old_chunks"] = deletedChunkCount;
        response["new_chunks"] = createdChunkCount;

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] regenerate document chunk failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("regenerate document chunk failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::deleteDocumentChunks(const QString &docId)
{
    qDebug() << "🗑️ [KnowledgeApi] Starting to delete all chunks for document:" << docId;

    try
    {
        if (!m_docBox || !m_chunkBox)
        {
            qDebug() << "❌ [KnowledgeApi] database not initialized";
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id id = docId.toULongLong(&ok);
        if (!ok)
        {
            qDebug() << "❌ [KnowledgeApi] invalid document ID:" << docId;
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid document ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 验证文档是否存在
        auto doc = m_docBox->get(id);
        if (!doc)
        {
            qDebug() << "❌ [KnowledgeApi] document not found:" << docId;
            QJsonObject response;
            response["success"] = false;
            response["message"] = "document not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 查找并删除所有属于该文档的chunks
        auto allChunks = m_chunkBox->getAll();
        int totalChunkCount = 0;
        int deletedChunkCount = 0;

        // 收集要删除的chunk ID
        QList<obx_id> chunkIdsToDelete;
        for (const auto &chunk : allChunks)
        {
            if (chunk->knowledge_document_id == id)
            {
                totalChunkCount++;
                chunkIdsToDelete.append(chunk->id);
            }
        }

        qDebug() << "📊 [KnowledgeApi] Found" << totalChunkCount << "chunks to delete for document:" << docId;

        // 删除所有找到的chunks
        for (obx_id chunkId : chunkIdsToDelete)
        {
            bool chunkRemoved = m_chunkBox->remove(chunkId);
            if (chunkRemoved)
            {
                deletedChunkCount++;
                qDebug() << "✅ [KnowledgeApi] Deleted chunk ID:" << chunkId;
            }
            else
            {
                qDebug() << "⚠️ [KnowledgeApi] Failed to delete chunk ID:" << chunkId;
            }
        }

        qDebug() << "🎉 [KnowledgeApi] Document chunks deletion completed - deleted:" << deletedChunkCount << "/" << totalChunkCount;

        QJsonObject response;
        response["success"] = true;
        response["message"] = QString("Successfully deleted %1 chunks for document %2").arg(deletedChunkCount).arg(docId);
        response["deleted_chunks"] = deletedChunkCount;
        response["total_chunks_found"] = totalChunkCount;

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] delete document chunks failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("Delete document chunks failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::searchKnowledgeBase(const QString &kbId, const QString &query, int limit, double minScore)
{
    qDebug() << "🔍 [KnowledgeApi] search knowledge base:" << kbId << "query:" << query.left(50) << "minScore:" << minScore;

    // 优先使用HNSW搜索，如果失败则降级到传统搜索
    try
    {
        QString hnswResult = searchKnowledgeBaseWithHNSW(kbId, query, limit, minScore);
        QJsonDocument doc = QJsonDocument::fromJson(hnswResult.toUtf8());
        QJsonObject obj = doc.object();

        // 如果HNSW搜索成功，直接返回结果
        if (obj["success"].toBool())
        {
            qDebug() << "✅ [KnowledgeApi] using HNSW search result";
            return hnswResult;
        }
        else
        {
            qDebug() << "⚠️ [KnowledgeApi] HNSW search failed, fallback to traditional search";
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "⚠️ [KnowledgeApi] HNSW search exception, fallback to traditional search:" << e.what();
    }

    // 降级到传统搜索实现
    qDebug() << "📝 [KnowledgeApi] using traditional search method";

    // 如果limit为默认值，使用配置中的搜索限制
    if (limit == 10)
    {
        QString configStr = getEmbeddingApiConfig();
        QJsonDocument configDoc = QJsonDocument::fromJson(configStr.toUtf8());
        QJsonObject config = configDoc.object();
        limit = config["search_limit"].toInt(10);
        qDebug() << "⚙️ [KnowledgeApi] use config search limit:" << limit;
    }

    try
    {
        if (!m_kbBox || !m_chunkBox)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id kbIdNum = kbId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 生成查询向量（优化：添加超时和降级机制）
        std::vector<float> queryEmbedding;
        bool useVectorSearch = false;

        // 检查embedding API是否可用
        if (isEmbeddingApiAvailable())
        {
            qDebug() << "🔤 [KnowledgeApi] try to generate query vector...";
            try
            {
                queryEmbedding = textToEmbedding(query);
                if (!queryEmbedding.empty())
                {
                    useVectorSearch = true;
                    qDebug() << "✅ [KnowledgeApi] query vector generation success, dimension:" << queryEmbedding.size();
                }
                else
                {
                    qDebug() << "⚠️ [KnowledgeApi] query vector generation failed, use text matching";
                }
            }
            catch (const std::exception &e)
            {
                qDebug() << "⚠️ [KnowledgeApi] query vector generation exception:" << e.what() << ", use text matching";
            }
        }
        else
        {
            qDebug() << "📴 [KnowledgeApi] Embedding API not available, use text matching";
        }

        // 获取该知识库下的所有chunks并计算相似度
        auto allChunks = m_chunkBox->getAll();
        auto allDocs = m_docBox->getAll();

        struct SearchResult
        {
            obx_id chunkId;
            obx_id docId;
            QString docTitle;
            QString content;
            double similarity;
        };

        std::vector<SearchResult> results;

        for (const auto &chunk : allChunks)
        {
            // 先找到对应的文档，检查是否属于目标知识库
            bool belongsToKB = false;
            QString docTitle;
            for (const auto &doc : allDocs)
            {
                if (doc->id == chunk->knowledge_document_id && doc->kb_id == kbIdNum)
                {
                    belongsToKB = true;
                    docTitle = QString::fromStdString(doc->title);
                    break;
                }
            }

            if (!belongsToKB)
                continue;

            // 计算相似度
            double similarity = 0.0;
            if (useVectorSearch && !chunk->embedding.empty())
            {
                // 使用向量相似度计算
                similarity = calculateCosineSimilarity(queryEmbedding, chunk->embedding);
            }
            else
            {
                // 使用文本匹配计算相似度
                QString chunkContent = QString::fromStdString(chunk->content);
                similarity = calculateTextSimilarity(query, chunkContent);
            }

            // 使用默认搜索阈值，支持用户自定义minScore参数
            double semanticThreshold = 0.7; // 默认语义阈值
            double threshold;
            if (minScore > 0.0)
            {
                // 用户指定了最小相似度阈值，直接使用
                threshold = minScore;
                qDebug() << "🎯 [KnowledgeApi] Using user-specified threshold:" << threshold;
            }
            else
            {
                // 使用默认阈值，根据搜索方式智能调整
                if (useVectorSearch)
                {
                    // 向量搜索：使用语义阈值的15%作为最小阈值
                    threshold = semanticThreshold * 0.15;
                }
                else
                {
                    // 文本匹配搜索：使用语义阈值的7%作为最小阈值
                    threshold = semanticThreshold * 0.07;
                }
                qDebug() << "🎯 [KnowledgeApi] Using config-based threshold:" << threshold
                         << "(vectorSearch:" << useVectorSearch
                         << ", configThreshold:" << semanticThreshold << ")";
            }

            if (similarity > threshold)
            {
                SearchResult result;
                result.chunkId = chunk->id;
                result.docId = chunk->knowledge_document_id;
                result.docTitle = docTitle;
                result.content = QString::fromStdString(chunk->content);
                result.similarity = similarity;
                results.push_back(result);
            }
        }

        // 按相似度排序
        std::sort(results.begin(), results.end(),
                  [](const SearchResult &a, const SearchResult &b)
                  {
                      return a.similarity > b.similarity;
                  });

        // 限制结果数量
        if (results.size() > static_cast<size_t>(limit))
        {
            results.resize(static_cast<size_t>(limit));
        }

        // 构建JSON响应
        QJsonArray resultsArray;
        for (const auto &result : results)
        {
            QJsonObject resultObj;
            resultObj["chunk_id"] = QString::number(result.chunkId);
            resultObj["document_id"] = QString::number(result.docId);
            resultObj["document_title"] = result.docTitle;
            resultObj["content"] = result.content;
            resultObj["similarity"] = result.similarity;
            resultsArray.append(resultObj);
        }

        QJsonObject response;
        response["success"] = true;
        response["results"] = resultsArray;
        response["total_results"] = static_cast<int>(results.size());
        response["message"] = QString("find %1 related results").arg(results.size());

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] search failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("search failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::searchAllKnowledgeBases(const QString &query, int limit, double minScore)
{
    qDebug() << "🔍 [KnowledgeApi] search all knowledge bases:" << query.left(50) << "minScore:" << minScore;

    // 优先使用HNSW搜索，如果失败则降级到传统搜索
    try
    {
        QString hnswResult = searchAllKnowledgeBasesWithHNSW(query, limit, minScore);
        QJsonDocument doc = QJsonDocument::fromJson(hnswResult.toUtf8());
        QJsonObject obj = doc.object();

        // 如果HNSW搜索成功，直接返回结果
        if (obj["success"].toBool())
        {
            qDebug() << "✅ [KnowledgeApi] using HNSW global search result";
            return hnswResult;
        }
        else
        {
            qDebug() << "⚠️ [KnowledgeApi] HNSW global search failed, fallback to traditional search";
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "⚠️ [KnowledgeApi] HNSW global search exception, fallback to traditional search:" << e.what();
    }

    // 降级到传统搜索实现
    qDebug() << "📝 [KnowledgeApi] using traditional global search method";

    try
    {
        if (!m_kbBox || !m_chunkBox)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 生成查询向量（优化：添加超时和降级机制）
        std::vector<float> queryEmbedding;
        bool useVectorSearch = false;

        // 检查embedding API是否可用
        if (isEmbeddingApiAvailable())
        {
            qDebug() << "🔤 [KnowledgeApi] try to generate query vector...";
            try
            {
                queryEmbedding = textToEmbedding(query);
                if (!queryEmbedding.empty())
                {
                    useVectorSearch = true;
                    qDebug() << "✅ [KnowledgeApi] query vector generation success, dimension:" << queryEmbedding.size();
                }
                else
                {
                    qDebug() << "⚠️ [KnowledgeApi] query vector generation failed, use text matching";
                }
            }
            catch (const std::exception &e)
            {
                qDebug() << "⚠️ [KnowledgeApi] query vector generation exception:" << e.what() << ", use text matching";
            }
        }
        else
        {
            qDebug() << "📴 [KnowledgeApi] Embedding API not available, use text matching";
        }

        // 获取所有chunks并计算相似度
        auto allChunks = m_chunkBox->getAll();
        auto allDocs = m_docBox->getAll();
        auto allKBs = m_kbBox->getAll();

        struct SearchResult
        {
            obx_id chunkId;
            obx_id docId;
            obx_id kbId;
            QString kbName;
            QString docTitle;
            QString content;
            double similarity;
        };

        std::vector<SearchResult> results;

        for (const auto &chunk : allChunks)
        {
            // 找到对应的文档和知识库信息
            QString docTitle, kbName;
            obx_id docId = 0, kbId = 0;

            for (const auto &doc : allDocs)
            {
                if (doc->id == chunk->knowledge_document_id)
                {
                    docId = doc->id;
                    kbId = doc->kb_id;
                    docTitle = QString::fromStdString(doc->title);

                    // 找到知识库名称
                    for (const auto &kb : allKBs)
                    {
                        if (kb->id == doc->kb_id)
                        {
                            kbName = QString::fromStdString(kb->name);
                            break;
                        }
                    }
                    break;
                }
            }

            if (docId == 0)
                continue;

            // 计算相似度
            double similarity = 0.0;
            if (useVectorSearch && !chunk->embedding.empty())
            {
                // 使用向量相似度计算
                similarity = calculateCosineSimilarity(queryEmbedding, chunk->embedding);
            }
            else
            {
                // 使用文本匹配计算相似度
                QString chunkContent = QString::fromStdString(chunk->content);
                similarity = calculateTextSimilarity(query, chunkContent);
            }

            // 使用默认搜索阈值，支持用户自定义minScore参数
            double semanticThreshold = 0.7; // 默认语义阈值
            double threshold;
            if (minScore > 0.0)
            {
                // 用户指定了最小相似度阈值，直接使用
                threshold = minScore;
                qDebug() << "🎯 [KnowledgeApi] Using user-specified threshold:" << threshold;
            }
            else
            {
                // 使用默认阈值，根据搜索方式智能调整
                if (useVectorSearch)
                {
                    // 向量搜索：使用语义阈值的15%作为最小阈值
                    threshold = semanticThreshold * 0.15;
                }
                else
                {
                    // 文本匹配搜索：使用语义阈值的7%作为最小阈值
                    threshold = semanticThreshold * 0.07;
                }
                qDebug() << "🎯 [KnowledgeApi] Using config-based threshold:" << threshold
                         << "(vectorSearch:" << useVectorSearch
                         << ", configThreshold:" << semanticThreshold << ")";
            }

            if (similarity > threshold)
            {
                SearchResult result;
                result.chunkId = chunk->id;
                result.docId = docId;
                result.kbId = kbId;
                result.kbName = kbName;
                result.docTitle = docTitle;
                result.content = QString::fromStdString(chunk->content);
                result.similarity = similarity;
                results.push_back(result);
            }
        }

        // 按相似度排序
        std::sort(results.begin(), results.end(),
                  [](const SearchResult &a, const SearchResult &b)
                  {
                      return a.similarity > b.similarity;
                  });

        // 限制结果数量
        if (results.size() > static_cast<size_t>(limit))
        {
            results.resize(static_cast<size_t>(limit));
        }

        // 构建JSON响应
        QJsonArray resultsArray;
        for (const auto &result : results)
        {
            QJsonObject resultObj;
            resultObj["chunk_id"] = QString::number(result.chunkId);
            resultObj["document_id"] = QString::number(result.docId);
            resultObj["knowledge_base_id"] = QString::number(result.kbId);
            resultObj["knowledge_base_name"] = result.kbName;
            resultObj["document_title"] = result.docTitle;
            resultObj["content"] = result.content;
            resultObj["similarity"] = result.similarity;
            resultsArray.append(resultObj);
        }

        QJsonObject response;
        response["success"] = true;
        response["results"] = resultsArray;
        response["total_results"] = static_cast<int>(results.size());
        response["message"] = QString("find %1 related results").arg(results.size());

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] global search failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("global search failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::generateEmbedding(const QString &text)
{
    qDebug() << "🔤 [KnowledgeApi] generate embedding, text length:" << text.length();

    try
    {
        QJsonObject response;
        response["success"] = true;
        response["input_text"] = text;
        response["input_length"] = text.length();

        // 检查API配置
        bool apiAvailable = isEmbeddingApiAvailable();
        response["api_available"] = apiAvailable;
        response["api_config"] = getEmbeddingApiConfig();

        // 确定处理策略
        QString strategy;
        if (!apiAvailable)
        {
            strategy = "simple_tfidf";
        }
        else if (text.length() < 200)
        {
            strategy = "direct_api";
        }
        else
        {
            strategy = "multi_semantic";
        }
        response["processing_strategy"] = strategy;

        // 生成向量
        std::vector<float> embedding = textToEmbedding(text);

        QJsonArray embeddingArray;
        for (float value : embedding)
        {
            embeddingArray.append(static_cast<double>(value));
        }

        response["embedding"] = embeddingArray;
        response["dimension"] = static_cast<int>(embedding.size());

        // 显示前10个向量值作为预览
        if (!embedding.empty())
        {
            QJsonArray preview;
            int previewSize = qMin(10, static_cast<int>(embedding.size()));
            for (int i = 0; i < previewSize; ++i)
            {
                preview.append(static_cast<double>(embedding[i]));
            }
            response["embedding_preview"] = preview;
        }

        // 如果是长文本，显示切割信息
        if (text.length() >= 200 && apiAvailable)
        {
            // 简单的段落分割
            QStringList chunks = text.split(QRegularExpression("\n\\s*\n"), Qt::SkipEmptyParts);
            if (chunks.size() <= 1)
            {
                // 如果没有段落分割，使用字符分割
                chunks.clear();
                QString cleanText = text.trimmed();
                int chunkSize = 300;
                int start = 0;
                while (start < cleanText.length())
                {
                    int end = qMin(start + chunkSize, cleanText.length());
                    QString chunk = cleanText.mid(start, end - start).trimmed();
                    if (!chunk.isEmpty())
                    {
                        chunks.append(chunk);
                    }
                    start = end;
                }
            }
            response["chunk_count"] = chunks.size();
            response["chunks_processed"] = qMin(chunks.size(), 5);
        }

        response["message"] = QString("embedding generation success (strategy: %1)").arg(strategy);

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] generate embedding failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("generate embedding failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::updateChunkEmbeddings(const QString &docId)
{
    qDebug() << "🔄 [KnowledgeApi] update document chunk embedding:" << docId;

    try
    {
        if (!m_chunkBox)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id id = docId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid document ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 获取该文档的所有chunks
        auto allChunks = m_chunkBox->getAll();
        int updatedCount = 0;

        for (auto &chunk : allChunks)
        {
            if (chunk->knowledge_document_id == id)
            {
                QString content = QString::fromStdString(chunk->content);
                chunk->embedding = textToEmbedding(content);

                if (m_chunkBox->put(*chunk) > 0)
                {
                    updatedCount++;
                }
            }
        }

        QJsonObject response;
        response["success"] = true;
        response["updated_chunks"] = updatedCount;
        response["message"] = QString("update %1 chunks embedding success").arg(updatedCount);

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] update chunk embedding failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("update chunk embedding failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::viewAllData()
{
    qDebug() << "📊 [KnowledgeApi] view all ObjectBox data";

    try
    {
        if (!m_kbBox || !m_docBox || !m_chunkBox)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        QJsonObject data;

        // 1. 获取所有知识库
        auto allKbs = m_kbBox->getAll();
        QJsonArray kbArray;
        for (const auto &kb : allKbs)
        {
            QJsonObject kbInfo = kbToJsonObject(*kb);
            kbArray.append(kbInfo);
        }
        data["knowledge_bases"] = kbArray;
        data["total_knowledge_bases"] = static_cast<int>(allKbs.size());

        // 2. 获取所有文档
        auto allDocs = m_docBox->getAll();
        QJsonArray docArray;
        for (const auto &doc : allDocs)
        {
            QJsonObject docInfo = docToJsonObject(*doc);

            // 计算该文档的chunk数量
            auto allChunks = m_chunkBox->getAll();
            int chunkCount = 0;
            for (const auto &chunk : allChunks)
            {
                if (chunk->knowledge_document_id == doc->id)
                {
                    chunkCount++;
                }
            }
            docInfo["chunk_count"] = chunkCount;

            docArray.append(docInfo);
        }
        data["documents"] = docArray;
        data["total_documents"] = static_cast<int>(allDocs.size());

        // 3. 获取所有chunks
        auto allChunks = m_chunkBox->getAll();
        QJsonArray chunkArray;
        for (const auto &chunk : allChunks)
        {
            QJsonObject chunkInfo;
            chunkInfo["id"] = QString::number(chunk->id);
            chunkInfo["document_id"] = QString::number(chunk->knowledge_document_id);
            chunkInfo["chunk_index"] = static_cast<int>(chunk->chunk_index);
            chunkInfo["content_length"] = static_cast<int>(chunk->content.length());
            chunkInfo["content_preview"] = QString::fromStdString(chunk->content).left(100);
            chunkInfo["has_embedding"] = !chunk->embedding.empty();
            chunkInfo["embedding_dimension"] = static_cast<int>(chunk->embedding.size());
            chunkInfo["created_at"] = static_cast<qint64>(chunk->created_at);
            chunkArray.append(chunkInfo);
        }
        data["chunks"] = chunkArray;
        data["total_chunks"] = static_cast<int>(allChunks.size());

        // 4. 统计信息
        QJsonObject stats;
        stats["database_path"] = getKnowledgeDatabasePath();
        stats["total_knowledge_bases"] = static_cast<int>(allKbs.size());
        stats["total_documents"] = static_cast<int>(allDocs.size());
        stats["total_chunks"] = static_cast<int>(allChunks.size());
        data["statistics"] = stats;

        QJsonObject response;
        response["success"] = true;
        response["data"] = data;
        response["message"] = QString("get all data success - KB:%1, Doc:%2, Chunk:%3")
                                  .arg(allKbs.size())
                                  .arg(allDocs.size())
                                  .arg(allChunks.size());

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] view data failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("view data failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::viewKnowledgeBaseData(const QString &kbId)
{
    qDebug() << "📋 [KnowledgeApi] view knowledge base detailed data:" << kbId;

    try
    {
        if (!m_kbBox || !m_docBox || !m_chunkBox)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id kbIdNum = kbId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 获取知识库信息
        auto kb = m_kbBox->get(kbIdNum);
        if (!kb)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "knowledge base not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        QJsonObject data;
        data["knowledge_base"] = kbToJsonObject(*kb);

        // 获取该知识库的所有文档
        auto allDocs = m_docBox->getAll();
        auto allChunks = m_chunkBox->getAll();

        QJsonArray docArray;
        int totalChunks = 0;
        int totalCharacters = 0;

        for (const auto &doc : allDocs)
        {
            if (doc->kb_id != kbIdNum)
                continue;

            QJsonObject docInfo = docToJsonObject(*doc);

            // 统计该文档的chunks
            QJsonArray chunkArray;
            int docChunkCount = 0;
            for (const auto &chunk : allChunks)
            {
                if (chunk->knowledge_document_id == doc->id)
                {
                    QJsonObject chunkInfo;
                    chunkInfo["id"] = QString::number(chunk->id);
                    chunkInfo["chunk_index"] = static_cast<int>(chunk->chunk_index);
                    chunkInfo["content_length"] = static_cast<int>(chunk->content.length());
                    chunkInfo["content_preview"] = QString::fromStdString(chunk->content).left(200);
                    chunkInfo["has_embedding"] = !chunk->embedding.empty();
                    chunkInfo["embedding_dimension"] = static_cast<int>(chunk->embedding.size());
                    chunkInfo["created_at"] = static_cast<qint64>(chunk->created_at);
                    chunkArray.append(chunkInfo);
                    docChunkCount++;
                    totalChunks++;
                }
            }

            docInfo["chunks"] = chunkArray;
            docInfo["chunk_count"] = docChunkCount;
            totalCharacters += static_cast<int>(doc->content.length());

            docArray.append(docInfo);
        }

        data["documents"] = docArray;

        // 统计信息
        QJsonObject stats;
        stats["knowledge_base_id"] = kbId;
        stats["knowledge_base_name"] = QString::fromStdString(kb->name);
        stats["document_count"] = docArray.size();
        stats["total_chunks"] = totalChunks;
        stats["total_characters"] = totalCharacters;
        stats["average_chunks_per_doc"] = docArray.size() > 0 ? static_cast<double>(totalChunks) / static_cast<double>(docArray.size()) : 0.0;
        data["statistics"] = stats;

        QJsonObject response;
        response["success"] = true;
        response["data"] = data;
        response["message"] = QString("view knowledge base data success - document:%1, chunk:%2")
                                  .arg(docArray.size())
                                  .arg(totalChunks);

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] view knowledge base data failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("view knowledge base data failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::viewDocumentChunks(const QString &docId)
{
    qDebug() << "🧩 [KnowledgeApi] view document chunks:" << docId;

    try
    {
        if (!m_docBox || !m_chunkBox)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id docIdNum = docId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid document ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 获取文档信息
        auto doc = m_docBox->get(docIdNum);
        if (!doc)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "document not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        QJsonObject data;
        data["document"] = docToJsonObject(*doc);

        // 获取该文档的所有chunks
        auto allChunks = m_chunkBox->getAll();
        QJsonArray chunkArray;

        for (const auto &chunk : allChunks)
        {
            if (chunk->knowledge_document_id != docIdNum)
                continue;

            QJsonObject chunkInfo;
            chunkInfo["id"] = QString::number(chunk->id);
            chunkInfo["chunk_index"] = static_cast<int>(chunk->chunk_index);
            chunkInfo["content"] = QString::fromStdString(chunk->content);
            chunkInfo["content_length"] = static_cast<int>(chunk->content.length());
            chunkInfo["has_embedding"] = !chunk->embedding.empty();
            chunkInfo["embedding_dimension"] = static_cast<int>(chunk->embedding.size());
            chunkInfo["metadata"] = QString::fromStdString(chunk->metadata);
            chunkInfo["created_at"] = static_cast<qint64>(chunk->created_at);

            // 如果有向量数据，显示前几个值作为预览
            if (!chunk->embedding.empty())
            {
                QJsonArray embeddingPreview;
                int previewSize = std::min(10, static_cast<int>(chunk->embedding.size()));
                for (int i = 0; i < previewSize; ++i)
                {
                    embeddingPreview.append(static_cast<double>(chunk->embedding[i]));
                }
                chunkInfo["embedding_preview"] = embeddingPreview;
            }

            chunkArray.append(chunkInfo);
        }

        data["chunks"] = chunkArray;

        // 统计信息
        QJsonObject stats;
        stats["document_id"] = docId;
        stats["document_title"] = QString::fromStdString(doc->title);
        stats["total_chunks"] = chunkArray.size();
        stats["total_content_length"] = static_cast<int>(doc->content.length());

        int totalChunkChars = 0;
        int chunksWithEmbedding = 0;
        for (const auto &chunk : allChunks)
        {
            if (chunk->knowledge_document_id == docIdNum)
            {
                totalChunkChars += static_cast<int>(chunk->content.length());
                if (!chunk->embedding.empty())
                {
                    chunksWithEmbedding++;
                }
            }
        }

        stats["total_chunk_characters"] = totalChunkChars;
        stats["chunks_with_embedding"] = chunksWithEmbedding;
        stats["embedding_coverage"] = chunkArray.size() > 0 ? static_cast<double>(chunksWithEmbedding) / static_cast<double>(chunkArray.size()) * 100.0 : 0.0;

        data["statistics"] = stats;

        QJsonObject response;
        response["success"] = true;
        response["data"] = data;
        response["message"] = QString("view document chunks success - total %1 chunks").arg(chunkArray.size());

        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] view document chunks failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("view document chunks failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::exportKnowledgeBaseData(const QString &kbId)
{
    qDebug() << "📤 [KnowledgeApi] export knowledge base data:" << kbId;

    try
    {
        if (!m_kbBox || !m_docBox || !m_chunkBox)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        bool ok;
        obx_id kbIdNum = kbId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 获取知识库信息
        auto kb = m_kbBox->get(kbIdNum);
        if (!kb)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "knowledge base not found";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        QJsonObject exportData;
        exportData["export_version"] = "1.0";
        exportData["export_timestamp"] = QDateTime::currentMSecsSinceEpoch();
        exportData["knowledge_base"] = kbToJsonObject(*kb);

        // 导出所有文档和chunks
        auto allDocs = m_docBox->getAll();
        auto allChunks = m_chunkBox->getAll();

        QJsonArray docArray;
        for (const auto &doc : allDocs)
        {
            if (doc->kb_id != kbIdNum)
                continue;

            QJsonObject docData = docToJsonObject(*doc);

            // 添加该文档的所有chunks
            QJsonArray chunkArray;
            for (const auto &chunk : allChunks)
            {
                if (chunk->knowledge_document_id == doc->id)
                {
                    QJsonObject chunkData;
                    chunkData["id"] = QString::number(chunk->id);
                    chunkData["chunk_index"] = static_cast<int>(chunk->chunk_index);
                    chunkData["content"] = QString::fromStdString(chunk->content);
                    chunkData["metadata"] = QString::fromStdString(chunk->metadata);
                    chunkData["created_at"] = static_cast<qint64>(chunk->created_at);

                    // 导出向量数据
                    if (!chunk->embedding.empty())
                    {
                        QJsonArray embeddingArray;
                        for (float value : chunk->embedding)
                        {
                            embeddingArray.append(static_cast<double>(value));
                        }
                        chunkData["embedding"] = embeddingArray;
                    }

                    chunkArray.append(chunkData);
                }
            }

            docData["chunks"] = chunkArray;
            docArray.append(docData);
        }

        exportData["documents"] = docArray;

        // 生成导出文件路径
        QString exportDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/InkCop_Exports";
        QDir().mkpath(exportDir);

        QString fileName = QString("KB_%1_%2.json")
                               .arg(kbId)
                               .arg(QDateTime::currentDateTime().toString("yyyyMMdd_HHmmss"));
        QString filePath = exportDir + "/" + fileName;

        // 写入文件
        QFile file(filePath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text))
        {
            QTextStream stream(&file);
            stream.setEncoding(QStringConverter::Utf8); // Qt6替代setCodec
            stream << QJsonDocument(exportData).toJson(QJsonDocument::Indented);
            file.close();

            QJsonObject response;
            response["success"] = true;
            response["file_path"] = filePath;
            response["file_name"] = fileName;
            response["export_size"] = file.size();
            response["document_count"] = docArray.size();
            response["message"] = QString("export data success, file: %1").arg(fileName);

            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }
        else
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "cannot create export file";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] export data failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("export data failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

// ===== 私有辅助方法实现 =====

std::vector<float> KnowledgeApi::textToEmbedding(const QString &text)
{
    // 检查配置中的嵌入模式偏好
    QString embeddingMode = "cloud"; // 默认云端
    if (m_databaseApi)
    {
        QJsonObject settings = m_databaseApi->getAppSettings();
        if (!settings.isEmpty())
        {
            QJsonObject kbSettings = settings["knowledgeBase"].toObject();
            embeddingMode = kbSettings["embeddingMode"].toString("cloud");
        }
    }

    // 添加详细的切割和模式调试日志
    logDebug(QString("textToEmbedding called - textLength: %1, embeddingMode: %2")
                 .arg(text.length())
                 .arg(embeddingMode),
             "embedding");

    // 1. 优先检查用户偏好：如果设置为本地模式且模型已加载，使用本地模型
    if (embeddingMode == "local" && isLocalGGUFLoaded())
    {
        qDebug() << "🏠 [KnowledgeApi] Using local GGUF model (user preference)";
        logDebug(QString("Sending to local GGUF - textLength: %1, will be processed with default chunkSize: 800")
                     .arg(text.length()),
                 "embedding");
        std::vector<float> localResult = generateLocalGGUFEmbedding(text);
        if (!localResult.empty())
        {
            qDebug() << "✅ [KnowledgeApi] Local GGUF embedding successful, dimension:" << localResult.size();
            return localResult;
        }
        else
        {
            qDebug() << "❌ [KnowledgeApi] Local GGUF embedding failed, falling back to cloud";
        }
    }

    // 2. 尝试云端API（如果配置了且可用）
    if (isEmbeddingApiAvailable())
    {
        qDebug() << "🌐 [KnowledgeApi] Using cloud embedding API for" << text.length() << "characters";
        std::vector<float> cloudResult = generateApiEmbedding(text);
        if (!cloudResult.empty())
        {
            qDebug() << "✅ [KnowledgeApi] Cloud embedding successful, dimension:" << cloudResult.size();
            return cloudResult;
        }
        else
        {
            qDebug() << "❌ [KnowledgeApi] Cloud embedding failed";
        }
    }
    else
    {
        qDebug() << "📴 [KnowledgeApi] Cloud API not available";
    }

    // 3. 尝试本地GGUF模型（如果云端失败或不可用）
    if (isLocalGGUFLoaded())
    {
        qDebug() << "🏠 [KnowledgeApi] Trying local GGUF model as fallback";
        std::vector<float> localResult = generateLocalGGUFEmbedding(text);
        if (!localResult.empty())
        {
            qDebug() << "✅ [KnowledgeApi] Local GGUF embedding successful, dimension:" << localResult.size();
            return localResult;
        }
        else
        {
            qDebug() << "❌ [KnowledgeApi] Local GGUF embedding failed";
        }
    }

    // 4. 最后回退到本地100维简单向量化
    qDebug() << "🔄 [KnowledgeApi] Falling back to local 100-dimension TF-IDF vectorization";
    std::vector<float> localResult = generateSimpleEmbedding(text);

    if (!localResult.empty())
    {
        qDebug() << "✅ [KnowledgeApi] Local TF-IDF embedding completed, dimension:" << localResult.size();
        return localResult;
    }
    else
    {
        qDebug() << "❌ [KnowledgeApi] All embedding methods failed";
        return std::vector<float>();
    }
}

double KnowledgeApi::calculateCosineSimilarity(const std::vector<float> &vec1, const std::vector<float> &vec2)
{
    if (vec1.size() != vec2.size() || vec1.empty())
    {
        return 0.0;
    }

    double dotProduct = 0.0;
    double norm1 = 0.0;
    double norm2 = 0.0;

    for (size_t i = 0; i < vec1.size(); ++i)
    {
        dotProduct += vec1[i] * vec2[i];
        norm1 += vec1[i] * vec1[i];
        norm2 += vec2[i] * vec2[i];
    }

    if (norm1 == 0.0 || norm2 == 0.0)
    {
        return 0.0;
    }

    return dotProduct / (std::sqrt(norm1) * std::sqrt(norm2));
}

QString KnowledgeApi::searchKnowledgeBaseWithHNSW(const QString &kbId, const QString &query, int limit, double minScore)
{
    qDebug() << "🚀 [KnowledgeApi] HNSW search knowledge base:" << kbId << "query:" << query.left(50) << "minScore:" << minScore;

    try
    {
#ifndef OBJECTBOX_DISABLED
        if (!m_kbBox || !m_chunkBox || !m_docBox)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 转换知识库ID
        bool ok;
        obx_id kbIdNum = kbId.toULongLong(&ok);
        if (!ok)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "invalid knowledge base ID";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 生成查询向量
        std::vector<float> queryEmbedding;
        bool useVectorSearch = false;

        if (isEmbeddingApiAvailable())
        {
            qDebug() << "🔤 [KnowledgeApi] generating query vector for HNSW search...";
            try
            {
                queryEmbedding = textToEmbedding(query);
                if (!queryEmbedding.empty())
                {
                    useVectorSearch = true;
                    qDebug() << "✅ [KnowledgeApi] query vector generated, dimension:" << queryEmbedding.size();
                }
                else
                {
                    qDebug() << "⚠️ [KnowledgeApi] query vector generation failed, fallback to text search";
                }
            }
            catch (const std::exception &e)
            {
                qDebug() << "⚠️ [KnowledgeApi] query vector generation exception:" << e.what();
            }
        }

        QJsonArray results;
        int totalResults = 0;

        if (useVectorSearch)
        {
            // 使用ObjectBox原生HNSW向量搜索
            qDebug() << "🎯 [KnowledgeApi] using native HNSW vector search";

            // 创建向量搜索查询
            auto query = m_chunkBox->query(
                                       InkCop::Knowledge::KnowledgeChunk_::embedding.nearestNeighbors(queryEmbedding, limit * 2) // 获取更多结果以便过滤
                                       )
                             .build();

            // 执行搜索并获取结果和分数
            auto searchResults = query.findWithScores();

            qDebug() << "📊 [KnowledgeApi] HNSW search found" << searchResults.size() << "candidates";

            // 获取所有相关文档信息
            auto allDocs = m_docBox->getAll();

            for (const auto &result : searchResults)
            {
                const auto &chunk = result.first;
                double score = 1.0 - result.second; // ObjectBox返回距离，转换为相似度分数

                // 检查分数阈值
                if (minScore >= 0.0 && score < minScore)
                {
                    continue;
                }

                // 找到对应的文档，检查是否属于目标知识库
                QString docTitle;
                bool belongsToKB = false;

                for (const auto &doc : allDocs)
                {
                    if (doc->id == chunk.knowledge_document_id && doc->kb_id == kbIdNum)
                    {
                        belongsToKB = true;
                        docTitle = QString::fromStdString(doc->title);
                        break;
                    }
                }

                if (!belongsToKB)
                {
                    continue;
                }

                // 构建结果对象
                QJsonObject resultObj;
                resultObj["chunk_id"] = static_cast<qint64>(chunk.id);
                resultObj["document_id"] = static_cast<qint64>(chunk.knowledge_document_id);
                resultObj["document_title"] = docTitle;
                resultObj["content"] = QString::fromStdString(chunk.content);
                resultObj["similarity"] = score;
                resultObj["chunk_index"] = static_cast<int>(chunk.chunk_index);
                resultObj["search_type"] = "vector_hnsw";

                results.append(resultObj);
                totalResults++;

                if (totalResults >= limit)
                {
                    break;
                }
            }
        }
        else
        {
            // 降级到文本搜索
            qDebug() << "📝 [KnowledgeApi] fallback to text search";
            return searchKnowledgeBase(kbId, query, limit, minScore);
        }

        qDebug() << "✅ [KnowledgeApi] HNSW search completed, found" << totalResults << "results";

        QJsonObject response;
        response["success"] = true;
        response["results"] = results;
        response["total_results"] = totalResults;
        response["search_method"] = "hnsw_vector";
        response["message"] = QString("Found %1 results using HNSW vector search").arg(totalResults);

        return QJsonDocument(response).toJson(QJsonDocument::Compact);

#else
        qDebug() << "⚠️ [KnowledgeApi] ObjectBox disabled, fallback to regular search";
        return searchKnowledgeBase(kbId, query, limit, minScore);
#endif
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] HNSW search failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("HNSW search failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

QString KnowledgeApi::searchAllKnowledgeBasesWithHNSW(const QString &query, int limit, double minScore)
{
    qDebug() << "🚀 [KnowledgeApi] HNSW search all knowledge bases:" << query.left(50) << "minScore:" << minScore;

    try
    {
#ifndef OBJECTBOX_DISABLED
        if (!m_kbBox || !m_chunkBox || !m_docBox)
        {
            QJsonObject response;
            response["success"] = false;
            response["message"] = "database not initialized";
            return QJsonDocument(response).toJson(QJsonDocument::Compact);
        }

        // 生成查询向量
        std::vector<float> queryEmbedding;
        bool useVectorSearch = false;

        if (isEmbeddingApiAvailable())
        {
            qDebug() << "🔤 [KnowledgeApi] generating query vector for global HNSW search...";
            try
            {
                queryEmbedding = textToEmbedding(query);
                if (!queryEmbedding.empty())
                {
                    useVectorSearch = true;
                    qDebug() << "✅ [KnowledgeApi] query vector generated, dimension:" << queryEmbedding.size();
                }
                else
                {
                    qDebug() << "⚠️ [KnowledgeApi] query vector generation failed, fallback to text search";
                }
            }
            catch (const std::exception &e)
            {
                qDebug() << "⚠️ [KnowledgeApi] query vector generation exception:" << e.what();
            }
        }

        QJsonArray results;
        int totalResults = 0;

        if (useVectorSearch)
        {
            // 使用ObjectBox原生HNSW向量搜索
            qDebug() << "🎯 [KnowledgeApi] using native HNSW vector search for all knowledge bases";

            // 创建向量搜索查询（搜索所有chunks）
            auto vectorQuery = m_chunkBox->query(
                                             InkCop::Knowledge::KnowledgeChunk_::embedding.nearestNeighbors(queryEmbedding, limit * 3) // 获取更多结果以便过滤
                                             )
                                   .build();

            // 执行搜索并获取结果和分数
            auto searchResults = vectorQuery.findWithScores();

            qDebug() << "📊 [KnowledgeApi] HNSW global search found" << searchResults.size() << "candidates";

            // 获取所有相关数据
            auto allDocs = m_docBox->getAll();
            auto allKBs = m_kbBox->getAll();

            for (const auto &result : searchResults)
            {
                const auto &chunk = result.first;
                double score = 1.0 - result.second; // ObjectBox返回距离，转换为相似度分数

                // 检查分数阈值
                if (minScore >= 0.0 && score < minScore)
                {
                    continue;
                }

                // 找到对应的文档和知识库信息
                QString docTitle, kbName;
                obx_id docId = 0, kbId = 0;

                for (const auto &doc : allDocs)
                {
                    if (doc->id == chunk.knowledge_document_id)
                    {
                        docId = doc->id;
                        kbId = doc->kb_id;
                        docTitle = QString::fromStdString(doc->title);

                        // 找到知识库名称
                        for (const auto &kb : allKBs)
                        {
                            if (kb->id == doc->kb_id)
                            {
                                kbName = QString::fromStdString(kb->name);
                                break;
                            }
                        }
                        break;
                    }
                }

                if (docId == 0)
                {
                    continue;
                }

                // 构建结果对象
                QJsonObject resultObj;
                resultObj["chunk_id"] = static_cast<qint64>(chunk.id);
                resultObj["document_id"] = static_cast<qint64>(docId);
                resultObj["document_title"] = docTitle;
                resultObj["knowledge_base_id"] = static_cast<qint64>(kbId);
                resultObj["knowledge_base_name"] = kbName;
                resultObj["content"] = QString::fromStdString(chunk.content);
                resultObj["similarity"] = score;
                resultObj["chunk_index"] = static_cast<int>(chunk.chunk_index);
                resultObj["search_type"] = "vector_hnsw";

                results.append(resultObj);
                totalResults++;

                if (totalResults >= limit)
                {
                    break;
                }
            }
        }
        else
        {
            // 降级到文本搜索
            qDebug() << "📝 [KnowledgeApi] fallback to text search";
            return searchAllKnowledgeBases(query, limit, minScore);
        }

        qDebug() << "✅ [KnowledgeApi] HNSW global search completed, found" << totalResults << "results";

        QJsonObject response;
        response["success"] = true;
        response["results"] = results;
        response["total_results"] = totalResults;
        response["search_method"] = "hnsw_vector";
        response["message"] = QString("Found %1 results using HNSW vector search").arg(totalResults);

        return QJsonDocument(response).toJson(QJsonDocument::Compact);

#else
        qDebug() << "⚠️ [KnowledgeApi] ObjectBox disabled, fallback to regular search";
        return searchAllKnowledgeBases(query, limit, minScore);
#endif
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] HNSW global search failed:" << e.what();
        QJsonObject response;
        response["success"] = false;
        response["message"] = QString("HNSW global search failed: %1").arg(e.what());
        return QJsonDocument(response).toJson(QJsonDocument::Compact);
    }
}

double KnowledgeApi::calculateTextSimilarity(const QString &query, const QString &text)
{
    // 简单的文本相似度计算：统计共同关键词
    QString cleanQuery = query.toLower();
    QString cleanText = text.toLower();

    // 分词（简单的空格分割）
    QStringList queryWords = cleanQuery.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
    QStringList textWords = cleanText.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);

    if (queryWords.isEmpty() || textWords.isEmpty())
    {
        return 0.0;
    }

    // 计算交集
    QSet<QString> querySet = QSet<QString>(queryWords.begin(), queryWords.end());
    QSet<QString> textSet = QSet<QString>(textWords.begin(), textWords.end());
    QSet<QString> intersection = querySet & textSet;

    // 计算Jaccard相似度
    QSet<QString> unionSet = querySet | textSet;
    if (unionSet.isEmpty())
    {
        return 0.0;
    }

    double jaccard = static_cast<double>(intersection.size()) / static_cast<double>(unionSet.size());

    // 加权：如果query中的重要词在text中出现，给予额外权重
    double bonus = 0.0;
    for (const QString &word : queryWords)
    {
        if (word.length() > 2 && cleanText.contains(word))
        {
            bonus += 0.1;
        }
    }

    return std::min(1.0, jaccard + bonus);
}

int KnowledgeApi::detectEmbeddingDimension()
{
    qDebug() << "🔍 [KnowledgeApi] Detecting embedding dimension...";

    // 使用简单的测试文本来检测维度
    QString testText = "测试向量维度检测";

    try
    {
        std::vector<float> testEmbedding = textToEmbedding(testText);
        if (!testEmbedding.empty())
        {
            int dimension = static_cast<int>(testEmbedding.size());
            qDebug() << "✅ [KnowledgeApi] Detected embedding dimension:" << dimension;
            return dimension;
        }
        else
        {
            qDebug() << "❌ [KnowledgeApi] Failed to generate test embedding";
            return -1;
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] Exception during dimension detection:" << e.what();
        return -1;
    }
}

QString KnowledgeApi::validateHNSWConfiguration()
{
    qDebug() << "🔧 [KnowledgeApi] Validating HNSW configuration...";

    QJsonObject result;
    result["success"] = false;

    try
    {
        // 检测当前embedding维度
        int actualDimension = detectEmbeddingDimension();
        if (actualDimension <= 0)
        {
            result["message"] = "Failed to detect embedding dimension";
            return QJsonDocument(result).toJson(QJsonDocument::Compact);
        }

        // HNSW索引配置的维度（应该与model.h中的配置一致）
        const int HNSW_CONFIGURED_DIMENSION = 1536;

        // 从知识库配置中获取保存的维度
        int configuredDimension = getEmbeddingDimensionFromKnowledgeBaseConfig();

        result["actual_dimension"] = actualDimension;
        result["hnsw_configured_dimension"] = HNSW_CONFIGURED_DIMENSION;
        result["saved_dimension"] = configuredDimension;
        result["dimension_match"] = (actualDimension == HNSW_CONFIGURED_DIMENSION);
        result["config_hnsw_match"] = (configuredDimension == HNSW_CONFIGURED_DIMENSION);

        // 检查各种维度匹配情况
        bool actualMatchesHnsw = (actualDimension == HNSW_CONFIGURED_DIMENSION);
        bool configMatchesHnsw = (configuredDimension == HNSW_CONFIGURED_DIMENSION);
        bool actualMatchesConfig = (configuredDimension > 0 && actualDimension == configuredDimension);

        if (actualMatchesHnsw && (configuredDimension <= 0 || actualMatchesConfig))
        {
            result["success"] = true;
            result["message"] = QString("✅ 配置完美！实际维度(%1)与HNSW索引维度匹配").arg(actualDimension);
            result["recommendation"] = "配置已优化，无需调整";
        }
        else if (configuredDimension > 0 && !actualMatchesConfig)
        {
            result["success"] = false;
            result["message"] = QString("⚠️ 实际维度(%1)与保存的配置维度(%2)不匹配")
                                    .arg(actualDimension)
                                    .arg(configuredDimension);
            result["recommendation"] = "请重新测试embedding配置以更新维度信息";
            result["impact"] = "可能使用了错误的embedding模型或配置";
        }
        else if (!actualMatchesHnsw)
        {
            if (actualDimension < HNSW_CONFIGURED_DIMENSION)
            {
                result["success"] = false;
                result["message"] = QString("⚠️ 实际维度(%1)小于HNSW配置维度(%2)")
                                        .arg(actualDimension)
                                        .arg(HNSW_CONFIGURED_DIMENSION);
                result["recommendation"] = QString("建议在model.h中将HNSW维度更新为%1以获得最佳性能").arg(actualDimension);
                result["impact"] = "向量会被索引，但高维度部分会被忽略";
            }
            else
            {
                result["success"] = false;
                result["message"] = QString("❌ 实际维度(%1)大于HNSW配置维度(%2)")
                                        .arg(actualDimension)
                                        .arg(HNSW_CONFIGURED_DIMENSION);
                result["recommendation"] = QString("必须在model.h中将HNSW维度更新为%1").arg(actualDimension);
                result["impact"] = "高维度向量将被完全忽略，索引失效";
            }
        }
        else
        {
            result["success"] = true;
            result["message"] = QString("✅ 实际维度(%1)与HNSW索引维度匹配").arg(actualDimension);
            result["recommendation"] = "配置正确，建议保存当前维度到配置中";
        }

        // 添加embedding模式信息
        QString embeddingMode = "cloud";
        if (m_databaseApi)
        {
            QJsonObject settings = m_databaseApi->getAppSettings();
            if (!settings.isEmpty())
            {
                QJsonObject kbSettings = settings["knowledgeBase"].toObject();
                embeddingMode = kbSettings["embeddingMode"].toString("cloud");
            }
        }
        result["embedding_mode"] = embeddingMode;

        // 添加模型信息
        if (embeddingMode == "cloud")
        {
            QJsonObject config = QJsonDocument::fromJson(getEmbeddingApiConfig().toUtf8()).object();
            result["embedding_model"] = config["model"].toString();
            result["embedding_base_url"] = config["base_url"].toString();
        }
        else if (embeddingMode == "local")
        {
            result["embedding_model"] = "Local GGUF Model";
            result["local_model_loaded"] = isLocalGGUFLoaded();
        }

        qDebug() << "📊 [KnowledgeApi] HNSW validation result:" << result;
    }
    catch (const std::exception &e)
    {
        result["message"] = QString("Validation failed: %1").arg(e.what());
        qDebug() << "❌ [KnowledgeApi] HNSW validation exception:" << e.what();
    }

    return QJsonDocument(result).toJson(QJsonDocument::Compact);
}

int KnowledgeApi::getEmbeddingDimensionFromKnowledgeBaseConfig()
{
    try
    {
        if (!m_databaseApi)
        {
            qDebug() << "⚠️ [KnowledgeApi] DatabaseApi not available for config reading";
            return -1;
        }

        // 获取应用设置
        QJsonObject appSettings = m_databaseApi->getAppSettings();
        if (appSettings.isEmpty())
        {
            qDebug() << "⚠️ [KnowledgeApi] No app settings found";
            return -1;
        }

        // 获取知识库设置
        QJsonObject kbSettings = appSettings["knowledgeBase"].toObject();
        if (kbSettings.isEmpty())
        {
            qDebug() << "⚠️ [KnowledgeApi] No knowledge base settings found";
            return -1;
        }

        // 获取保存的embedding维度
        if (kbSettings.contains("embeddingDimension"))
        {
            int dimension = kbSettings["embeddingDimension"].toInt();
            if (dimension > 0)
            {
                qDebug() << "✅ [KnowledgeApi] Found saved embedding dimension:" << dimension;
                return dimension;
            }
        }

        qDebug() << "ℹ️ [KnowledgeApi] No valid embedding dimension found in config";
        return -1;
    }
    catch (const std::exception &e)
    {
        qDebug() << "❌ [KnowledgeApi] Error reading embedding dimension from config:" << e.what();
        return -1;
    }
}

std::vector<float> KnowledgeApi::generateSimpleEmbedding(const QString &text)
{
    // 简单的TF-IDF向量化实现
    const int EMBEDDING_DIM = 100; // 向量维度
    std::vector<float> embedding(EMBEDDING_DIM, 0.0f);

    if (text.isEmpty())
    {
        return embedding;
    }

    // 文本预处理
    QString cleanText = text.toLower();
    // 使用简单的字符替换来避免正则表达式问题
    QString processedText;
    for (const QChar &ch : cleanText)
    {
        if (ch.isLetterOrNumber() || ch.isSpace() || (ch.unicode() >= 0x4e00 && ch.unicode() <= 0x9fff))
        {
            processedText.append(ch);
        }
        else
        {
            processedText.append(' ');
        }
    }
    cleanText = processedText;

    // 分词
    QStringList words = cleanText.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);

    if (words.isEmpty())
    {
        return embedding;
    }

    // 计算词频
    QHash<QString, int> wordFreq;
    for (const QString &word : words)
    {
        if (word.length() > 1)
        { // 过滤单字符
            wordFreq[word]++;
        }
    }

    // 生成简单的hash-based向量
    for (auto it = wordFreq.constBegin(); it != wordFreq.constEnd(); ++it)
    {
        const QString &word = it.key();
        int freq = it.value();

        // 使用简单的hash函数将词映射到向量维度
        uint hash = qHash(word);
        for (int i = 0; i < 3; ++i)
        { // 每个词影响3个维度
            int dim = (hash + i) % EMBEDDING_DIM;
            float weight = static_cast<float>(freq) / static_cast<float>(words.size());
            embedding[dim] += weight * (1.0f + static_cast<float>(word.length()) * 0.1f);
        }
    }

    // 归一化向量
    float norm = 0.0f;
    for (float val : embedding)
    {
        norm += val * val;
    }

    if (norm > 0.0f)
    {
        norm = std::sqrt(norm);
        for (float &val : embedding)
        {
            val /= norm;
        }
    }

    return embedding;
}

QJsonObject KnowledgeApi::callEmbeddingApi(const QString &text, int retryCount)
{
    QJsonObject result;
    result["success"] = false;

    if (!isEmbeddingApiAvailable())
    {
        result["error"] = "API not available";
        return result;
    }

    QString configStr = getEmbeddingApiConfig();
    QJsonDocument configDoc = QJsonDocument::fromJson(configStr.toUtf8());
    QJsonObject config = configDoc.object();

    QString baseUrl = config["base_url"].toString();
    QString apiKey = config["api_key"].toString();
    QString model = config["model"].toString();

    logDebug(QString("Original baseUrl: %1").arg(baseUrl), "EmbeddingAPI");

    // 智能修正Ollama的baseUrl
    QString apiUrl;
    if (baseUrl.contains("localhost:11434") || baseUrl.contains("127.0.0.1:11434"))
    {
        // 检测Ollama服务，使用正确的embeddings端点
        if (baseUrl.contains("/v1/chat/completions"))
        {
            // 错误的聊天完成端点，修正为embeddings端点
            apiUrl = baseUrl.replace("/v1/chat/completions", "/v1/embeddings");
            logInfo(QString("Auto-corrected Ollama URL from chat to embeddings: %1").arg(apiUrl), "EmbeddingAPI");
        }
        else if (baseUrl.contains("/api/generate") || baseUrl.contains("/api/chat"))
        {
            // 其他Ollama原生端点，修正为embeddings端点
            QString correctedBase = baseUrl;
            correctedBase.replace(QRegularExpression("/api/(generate|chat).*$"), "");
            apiUrl = correctedBase + "/v1/embeddings";
            logInfo(QString("Auto-corrected Ollama URL to embeddings: %1").arg(apiUrl), "EmbeddingAPI");
        }
        else if (baseUrl.endsWith("/v1") || baseUrl.endsWith("/v1/"))
        {
            // OpenAI兼容格式
            apiUrl = baseUrl.trimmed();
            if (!apiUrl.endsWith("/"))
                apiUrl += "/";
            apiUrl += "embeddings";
        }
        else if (baseUrl.endsWith("/api") || baseUrl.endsWith("/api/"))
        {
            // Ollama原生API格式
            apiUrl = baseUrl.trimmed();
            if (!apiUrl.endsWith("/"))
                apiUrl += "/";
            apiUrl += "embeddings";
        }
        else
        {
            // 基础URL，默认使用OpenAI兼容格式
            apiUrl = baseUrl.trimmed();
            if (apiUrl.endsWith("/"))
                apiUrl.chop(1);
            apiUrl += "/v1/embeddings";
        }
    }
    else
    {
        // 非Ollama服务，使用标准格式
        apiUrl = baseUrl.trimmed();
        if (apiUrl.endsWith("/"))
            apiUrl.chop(1);
        apiUrl += "/embeddings";
    }

    logDebug(QString("Final API URL: %1").arg(apiUrl), "EmbeddingAPI");

    // 构建请求体
    QJsonObject requestBody;
    requestBody["model"] = model;
    requestBody["input"] = text;
    requestBody["encoding_format"] = "float";

    QJsonObject parameters;
    parameters["text_type"] = "document";
    requestBody["parameters"] = parameters;

    QJsonDocument requestDoc(requestBody);
    QByteArray requestData = requestDoc.toJson(QJsonDocument::Compact);

    // 重试逻辑：最多5次，每次等待时间递增10秒
    const int maxRetries = 5;
    for (int attempt = 0; attempt < maxRetries; ++attempt)
    {
        // 如果不是第一次尝试，等待递增的时间
        if (attempt > 0)
        {
            int waitTime = attempt * 10; // 10秒、20秒、30秒、40秒
            qDebug() << "🔄 [KnowledgeApi] Retry attempt" << (attempt + 1) << "after" << waitTime << "seconds";
            QThread::msleep(waitTime * 1000);
        }

        // 创建网络请求
        QUrl url(apiUrl);
        QNetworkRequest request(url);
        request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
        request.setRawHeader("Authorization", QString("Bearer %1").arg(apiKey).toUtf8());
        request.setRawHeader("User-Agent", "InkCop/1.0");
        request.setRawHeader("Accept", "application/json");

        // 发送请求
        QNetworkReply *reply = m_networkManager->post(request, requestData);

        // 等待响应（增加超时时间）
        QEventLoop loop;
        QTimer timeoutTimer;
        timeoutTimer.setSingleShot(true);

        connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
        connect(&timeoutTimer, &QTimer::timeout, [&loop, reply]()
                {
            reply->abort();
            loop.quit(); });

        int timeoutMs = 30000 + (attempt * 10000); // 30秒、40秒、50秒、60秒、70秒
        timeoutTimer.start(timeoutMs);
        loop.exec();

        if (reply->error() == QNetworkReply::NoError)
        {
            QByteArray responseData = reply->readAll();
            QJsonDocument responseDoc = QJsonDocument::fromJson(responseData);

            if (!responseDoc.isNull())
            {
                QJsonObject responseObj = responseDoc.object();
                if (responseObj.contains("data") && responseObj["data"].isArray())
                {
                    qDebug() << "✅ [KnowledgeApi] API call succeeded on attempt" << (attempt + 1);
                    result["success"] = true;
                    result["data"] = responseObj["data"];
                    reply->deleteLater();
                    return result;
                }
                else if (responseObj.contains("error"))
                {
                    QString errorMsg = responseObj["error"].toString();
                    qDebug() << "❌ [KnowledgeApi] API error on attempt" << (attempt + 1) << ":" << errorMsg;
                    result["error"] = errorMsg;
                    // API错误通常不需要重试
                    reply->deleteLater();
                    return result;
                }
            }
        }
        else
        {
            QString errorMsg = reply->errorString();
            qDebug() << "🔗 [KnowledgeApi] Network error on attempt" << (attempt + 1) << ":" << errorMsg;
            result["error"] = errorMsg;
        }

        reply->deleteLater();

        // 如果是最后一次尝试，不再重试
        if (attempt == maxRetries - 1)
        {
            qDebug() << "❌ [KnowledgeApi] All" << maxRetries << "attempts failed";
            break;
        }
    }

    return result;
}

std::vector<float> KnowledgeApi::generateApiEmbedding(const QString &text)
{
    if (text.trimmed().isEmpty())
    {
        return std::vector<float>();
    }

    // 直接调用API，不使用多重语义切割
    QJsonObject apiResult = callEmbeddingApi(text);

    if (!apiResult["success"].toBool())
    {
        return std::vector<float>();
    }

    QJsonArray dataArray = apiResult["data"].toArray();
    if (dataArray.isEmpty())
    {
        return std::vector<float>();
    }

    QJsonObject firstItem = dataArray[0].toObject();
    if (!firstItem.contains("embedding"))
    {
        return std::vector<float>();
    }

    QJsonArray embeddingArray = firstItem["embedding"].toArray();
    if (embeddingArray.isEmpty())
    {
        return std::vector<float>();
    }

    // 转换为float向量
    std::vector<float> embedding;
    embedding.reserve(embeddingArray.size());

    for (int i = 0; i < embeddingArray.size(); ++i)
    {
        const QJsonValue &value = embeddingArray[i];
        if (value.isDouble())
        {
            embedding.push_back(static_cast<float>(value.toDouble()));
        }
    }

    return embedding;
}

std::vector<float> KnowledgeApi::generateMultiSemanticEmbedding(const QString &text)
{
    // 简化的多语义处理 - 现在切割由前端管理
    int chunkSize = 800;

    // 对于短文本，直接调用API
    if (text.length() <= chunkSize)
    {
        qDebug() << "🔄 [KnowledgeApi] Text length" << text.length() << "<= chunk size" << chunkSize << ", using direct API";
        return generateApiEmbedding(text);
    }

    qDebug() << "🧩 [KnowledgeApi] Starting multi-semantic embedding for" << text.length() << "characters";

    // 简单的段落分割
    QStringList chunks = text.split(QRegularExpression("\n\\s*\n"), Qt::SkipEmptyParts);

    // 如果没有段落分割，使用字符分割
    if (chunks.size() <= 1)
    {
        chunks.clear();
        QString cleanText = text.trimmed();
        int start = 0;
        while (start < cleanText.length())
        {
            int end = qMin(start + chunkSize, cleanText.length());
            QString chunk = cleanText.mid(start, end - start).trimmed();
            if (!chunk.isEmpty())
            {
                chunks.append(chunk);
            }
            start = end;
        }
    }
    if (chunks.isEmpty())
    {
        qDebug() << "⚠️ [KnowledgeApi] No chunks created, falling back to direct API";
        return generateApiEmbedding(text);
    }

    qDebug() << "📋 [KnowledgeApi] Split into" << chunks.size() << "chunks for multi-semantic processing";

    std::vector<std::vector<float>> chunkEmbeddings;
    std::vector<double> chunkWeights;
    double totalWeight = 0.0;

    // 为每个块生成向量
    for (int i = 0; i < chunks.size(); ++i)
    {
        const QString &chunk = chunks[i];
        if (chunk.trimmed().isEmpty())
            continue;

        qDebug() << "🔤 [KnowledgeApi] Processing chunk" << (i + 1) << "/" << chunks.size() << ", length:" << chunk.length();

        std::vector<float> chunkEmbedding = generateApiEmbedding(chunk);
        if (!chunkEmbedding.empty())
        {
            chunkEmbeddings.push_back(chunkEmbedding);

            // 计算权重：基于文本长度和位置
            double lengthWeight = static_cast<double>(chunk.length()) / static_cast<double>(text.length());
            double positionWeight = 1.0; // 可以根据位置调整权重
            double weight = lengthWeight * positionWeight;

            chunkWeights.push_back(weight);
            totalWeight += weight;

            qDebug() << "✅ [KnowledgeApi] Chunk" << (i + 1) << "embedding generated, dimension:" << chunkEmbedding.size() << ", weight:" << weight;
        }
        else
        {
            qDebug() << "❌ [KnowledgeApi] Failed to generate embedding for chunk" << (i + 1);
        }
    }

    if (chunkEmbeddings.empty())
    {
        qDebug() << "❌ [KnowledgeApi] No valid chunk embeddings, falling back to direct API";
        return generateApiEmbedding(text);
    }

    // 检查所有向量维度是否一致
    size_t expectedDim = chunkEmbeddings[0].size();
    for (const auto &embedding : chunkEmbeddings)
    {
        if (embedding.size() != expectedDim)
        {
            qDebug() << "❌ [KnowledgeApi] Inconsistent embedding dimensions, falling back to direct API";
            return generateApiEmbedding(text);
        }
    }

    // 加权平均融合向量
    std::vector<float> fusedEmbedding(expectedDim, 0.0f);

    for (size_t i = 0; i < chunkEmbeddings.size(); ++i)
    {
        double normalizedWeight = chunkWeights[i] / totalWeight;
        for (size_t j = 0; j < expectedDim; ++j)
        {
            fusedEmbedding[j] += chunkEmbeddings[i][j] * static_cast<float>(normalizedWeight);
        }
    }

    // 归一化最终向量
    float norm = 0.0f;
    for (float val : fusedEmbedding)
    {
        norm += val * val;
    }

    if (norm > 0.0f)
    {
        norm = std::sqrt(norm);
        for (float &val : fusedEmbedding)
        {
            val /= norm;
        }
    }

    qDebug() << "🎯 [KnowledgeApi] Multi-semantic embedding completed - fused" << chunkEmbeddings.size() << "chunks into" << expectedDim << "dimensions";

    return fusedEmbedding;
}

// ===== 外部API Embedding方法实现 =====

QString KnowledgeApi::getEmbeddingApiConfig()
{
    QJsonObject config = getKnowledgeConfig();
    return QJsonDocument(config).toJson(QJsonDocument::Compact);
}

bool KnowledgeApi::isEmbeddingApiAvailable()
{
    return isApiConfigured();
}

// ===== 统一日志方法实现 =====

void KnowledgeApi::logInfo(const QString &message, const QString &context) const
{
    QString logMessage = context.isEmpty() ? QString("ℹ️ [KnowledgeApi] %1").arg(message) : QString("ℹ️ [KnowledgeApi:%1] %2").arg(context, message);
    qInfo() << logMessage;
}

void KnowledgeApi::logDebug(const QString &message, const QString &context) const
{
    QString logMessage = context.isEmpty() ? QString("🔍 [KnowledgeApi] %1").arg(message) : QString("🔍 [KnowledgeApi:%1] %2").arg(context, message);
    qDebug() << logMessage;
}

void KnowledgeApi::logWarning(const QString &message, const QString &context) const
{
    QString logMessage = context.isEmpty() ? QString("⚠️ [KnowledgeApi] %1").arg(message) : QString("⚠️ [KnowledgeApi:%1] %2").arg(context, message);
    qWarning() << logMessage;
}

void KnowledgeApi::logError(const QString &message, const QString &context) const
{
    QString logMessage = context.isEmpty() ? QString("❌ [KnowledgeApi] %1").arg(message) : QString("❌ [KnowledgeApi:%1] %2").arg(context, message);
    qCritical() << logMessage;
}

// ===== 统一配置管理实现 =====

QJsonObject KnowledgeApi::getKnowledgeConfig()
{
    QJsonObject config;

    // 首先尝试从前端配置读取
    if (m_databaseApi)
    {
        QJsonObject settings = m_databaseApi->getAppSettings();
        if (!settings.isEmpty())
        {
            QJsonObject kbSettings = settings["knowledgeBase"].toObject();

            QString baseUrl = kbSettings["baseUrl"].toString();
            QString apiKey = kbSettings["apiKey"].toString();
            QString model = kbSettings["model"].toString();

            if (!baseUrl.isEmpty() && !apiKey.isEmpty() && !model.isEmpty())
            {
                config["base_url"] = baseUrl;
                config["api_key"] = apiKey;
                config["model"] = model;
                config["chunk_size"] = kbSettings["chunkSize"].toInt(800);
                config["chunk_overlap"] = kbSettings["chunkOverlap"].toInt(200);
                config["semantic_threshold"] = kbSettings["semanticThreshold"].toDouble(0.7);
                config["search_limit"] = kbSettings["searchLimit"].toInt(10);
                config["enable_multi_semantic"] = kbSettings["enableMultiSemanticSplit"].toBool(true);
                config["enable_hierarchical"] = kbSettings["enableHierarchicalSplit"].toBool(true);
                config["source"] = "frontend";
                return config;
            }
        }
    }

    // 回退到环境变量配置
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    QString baseUrl = env.value("QWEN_BASEURL", "https://dashscope.aliyuncs.com/compatible-mode/v1");
    QString apiKey = env.value("QWEN_KEY", "");
    QString model = env.value("EMBEDDING_MODEL", "text-embedding-v4");

    if (!apiKey.isEmpty())
    {
        config["base_url"] = baseUrl;
        config["api_key"] = apiKey;
        config["model"] = model;
        config["chunk_size"] = 800;
        config["chunk_overlap"] = 200;
        config["semantic_threshold"] = 0.7;
        config["search_limit"] = 10;
        config["enable_multi_semantic"] = true;
        config["enable_hierarchical"] = true;
        config["max_safe_tokens"] = 5;
        config["source"] = "environment";
    }

    return config;
}

bool KnowledgeApi::isApiConfigured()
{
    QJsonObject config = getKnowledgeConfig();
    return !config["api_key"].toString().isEmpty() && !config["base_url"].toString().isEmpty();
}

// ===== 本地GGUF嵌入式模型方法实现 =====

bool KnowledgeApi::loadLocalGGUFModel(const QString &modelPath, int gpuLayers, int contextSize)
{
    // 调用新的带GPU选择的方法，使用默认GPU设备(-1)
    return loadLocalGGUFModelWithGpu(modelPath, gpuLayers, contextSize, -1);
}

bool KnowledgeApi::loadLocalGGUFModelWithGpu(const QString &modelPath, int gpuLayers, int contextSize, int selectedGpuDevice)
{
    logInfo(QString("Loading local GGUF model: %1 with GPU device: %2").arg(modelPath).arg(selectedGpuDevice), "LocalGGUF");

    if (!m_localGGUF)
    {
        logError("Local GGUF engine not initialized", "LocalGGUF");
        return false;
    }

    // 配置模型参数
    LocalGGUFEmbedding::ModelConfig config;
    config.modelPath = modelPath;

    // 设置合理的GPU层数，避免内存问题
    const int MAX_SAFE_GPU_LAYERS = 40; // 设置安全阈值为40层
    if (gpuLayers > MAX_SAFE_GPU_LAYERS && selectedGpuDevice >= 0)
    {
        qDebug() << "⚠️ [KnowledgeApi] User set GPU layers" << gpuLayers << "exceeds safe threshold" << MAX_SAFE_GPU_LAYERS << ", automatically adjusted to safe value";
        config.gpuLayers = MAX_SAFE_GPU_LAYERS;
    }
    else
    {
        config.gpuLayers = gpuLayers;
    }

    config.contextSize = contextSize;
    config.useGpu = (selectedGpuDevice >= 0); // 根据选择的GPU设备决定是否启用GPU
    config.selectedGpuDevice = selectedGpuDevice;
    config.threads = QThread::idealThreadCount();

    // 记录最终配置
    qDebug() << "🔧 [KnowledgeApi] Model config - GPU layers:" << config.gpuLayers
             << ", use GPU:" << config.useGpu
             << ", GPU device:" << config.selectedGpuDevice
             << ", context size:" << config.contextSize;

    // 使用默认切割参数
    config.chunkSize = 800;
    config.chunkOverlap = 200;

    logInfo(QString("Model config - chunkSize: %1, chunkOverlap: %2, contextSize: %3")
                .arg(config.chunkSize)
                .arg(config.chunkOverlap)
                .arg(config.contextSize),
            "LocalGGUF");

    bool result = m_localGGUF->loadModel(config);
    if (result)
    {
        logInfo(QString("Local GGUF model loaded successfully: %1").arg(modelPath), "LocalGGUF");
    }
    else
    {
        logError(QString("Failed to load local GGUF model: %1").arg(m_localGGUF->getLastError()), "LocalGGUF");
    }

    return result;
}

void KnowledgeApi::loadLocalGGUFModelAsync(const QString &modelPath, int gpuLayers, int contextSize)
{
    logInfo(QString("Scheduling async model loading: %1").arg(modelPath), "AsyncLoad");

    // 立即返回，不等待加载完成
    // 使用 QTimer::singleShot 确保在下一个事件循环中执行，避免阻塞当前调用
    QTimer::singleShot(0, this, [this, modelPath, gpuLayers, contextSize]()
                       {
        // 在后台线程中执行模型加载，避免阻塞UI
        QThreadPool::globalInstance()->start([this, modelPath, gpuLayers, contextSize]() {
            try
            {
                logInfo(QString("Starting async model loading in background thread: %1").arg(modelPath), "AsyncLoad");

                // 调用同步的加载方法
                bool result = loadLocalGGUFModel(modelPath, gpuLayers, contextSize);

                QString message;
                if (result)
                {
                    message = QString("模型加载成功！路径: %1").arg(modelPath);
                    logInfo(QString("Async model loading completed successfully: %1").arg(modelPath), "AsyncLoad");
                }
                else
                {
                    message = QString("模型加载失败！路径: %1").arg(modelPath);
                    logError(QString("Async model loading failed: %1").arg(modelPath), "AsyncLoad");
                }

                // 发送完成信号
                emit localModelLoadCompleted(result, message);
            }
            catch (const std::exception &e)
            {
                QString errorMsg = QString("模型加载异常: %1").arg(e.what());
                logError(QString("Exception during async model loading: %1").arg(e.what()), "AsyncLoad");
                emit localModelLoadCompleted(false, errorMsg);
            }
            catch (...)
            {
                QString errorMsg = "模型加载发生未知异常";
                logError("Unknown exception during async model loading", "AsyncLoad");
                emit localModelLoadCompleted(false, errorMsg);
            }
        }); });
}

void KnowledgeApi::loadLocalGGUFModelAsyncWithGpu(const QString &modelPath, int gpuLayers, int contextSize, int selectedGpuDevice)
{
    logInfo(QString("Scheduling async model loading with GPU device %1: %2").arg(selectedGpuDevice).arg(modelPath), "AsyncLoad");

    // 立即返回，不等待加载完成
    // 使用 QTimer::singleShot 确保在下一个事件循环中执行，避免阻塞当前调用
    QTimer::singleShot(0, this, [this, modelPath, gpuLayers, contextSize, selectedGpuDevice]()
                       {
        // 在后台线程中执行模型加载，避免阻塞UI
        QThreadPool::globalInstance()->start([this, modelPath, gpuLayers, contextSize, selectedGpuDevice]() {
            try
            {
                logInfo(QString("Starting async model loading in background thread with GPU device %1: %2")
                            .arg(selectedGpuDevice).arg(modelPath), "AsyncLoad");

                // 调用新的带GPU选择的同步加载方法
                bool result = loadLocalGGUFModelWithGpu(modelPath, gpuLayers, contextSize, selectedGpuDevice);

                QString message;
                if (result)
                {
                    message = QString("模型加载成功！路径: %1, GPU设备: %2").arg(modelPath).arg(selectedGpuDevice);
                    logInfo(QString("Async model loading with GPU completed successfully: %1 on device %2")
                                .arg(modelPath).arg(selectedGpuDevice), "AsyncLoad");
                }
                else
                {
                    message = QString("模型加载失败！路径: %1, GPU设备: %2").arg(modelPath).arg(selectedGpuDevice);
                    logError(QString("Async model loading with GPU failed: %1 on device %2")
                                 .arg(modelPath).arg(selectedGpuDevice), "AsyncLoad");
                }

                // 发送完成信号
                emit localModelLoadCompleted(result, message);
            }
            catch (const std::exception &e)
            {
                QString errorMsg = QString("模型加载异常: %1 (GPU设备: %2)").arg(e.what()).arg(selectedGpuDevice);
                logError(QString("Exception during async model loading with GPU: %1").arg(e.what()), "AsyncLoad");
                emit localModelLoadCompleted(false, errorMsg);
            }
            catch (...)
            {
                QString errorMsg = QString("模型加载发生未知异常 (GPU设备: %1)").arg(selectedGpuDevice);
                logError("Unknown exception during async model loading with GPU", "AsyncLoad");
                emit localModelLoadCompleted(false, errorMsg);
            }
        }); });
}

void KnowledgeApi::unloadLocalGGUFModel()
{
    logInfo("Unloading local GGUF model", "LocalGGUF");

    if (m_localGGUF)
    {
        m_localGGUF->unloadModel();
        logInfo("Local GGUF model unloaded", "LocalGGUF");
    }
}

bool KnowledgeApi::isLocalGGUFLoaded() const
{
    return m_localGGUF && m_localGGUF->isModelLoaded();
}

QString KnowledgeApi::getLocalGGUFInfo() const
{
    if (!m_localGGUF)
    {
        return "Local GGUF engine not initialized";
    }

    if (!m_localGGUF->isModelLoaded())
    {
        return "No local GGUF model loaded";
    }

    return m_localGGUF->getModelInfo();
}

QString KnowledgeApi::getLocalGGUFStatus() const
{
    QJsonObject status;

    if (!m_localGGUF)
    {
        status["available"] = false;
        status["error"] = "GGUF engine not initialized";
    }
    else
    {
        status["available"] = true;
        status["loaded"] = m_localGGUF->isModelLoaded();
        status["gpu_enabled"] = m_localGGUF->isGpuEnabled();

        if (m_localGGUF->isModelLoaded())
        {
            status["model_info"] = m_localGGUF->getModelInfo();
        }

        QString lastError = m_localGGUF->getLastError();
        if (!lastError.isEmpty())
        {
            status["last_error"] = lastError;
        }
    }

    return QJsonDocument(status).toJson(QJsonDocument::Compact);
}

QString KnowledgeApi::detectGpuCapabilities() const
{
    QJsonObject result;

    LocalGGUFEmbedding::GpuInfo gpuInfo = LocalGGUFEmbedding::detectGpuCapabilities();

    result["available"] = gpuInfo.available;
    result["device_name"] = gpuInfo.deviceName;
    result["backend"] = gpuInfo.backend;
    result["max_layers"] = gpuInfo.maxLayers;
    result["recommended_layers"] = 20; // 推荐的GPU层数

    return QJsonDocument(result).toJson(QJsonDocument::Compact);
}

QString KnowledgeApi::detectAllGpuDevices() const
{
    QJsonObject result;
    QJsonArray devicesArray;

    std::vector<LocalGGUFEmbedding::GpuDeviceInfo> devices = LocalGGUFEmbedding::detectAllGpuDevices();

    for (const auto &device : devices)
    {
        QJsonObject deviceObj;
        deviceObj["deviceId"] = device.deviceId;
        deviceObj["deviceName"] = device.deviceName;
        deviceObj["backend"] = device.backend;
        deviceObj["available"] = device.available;
        deviceObj["maxLayers"] = device.maxLayers;
        deviceObj["recommendedLayers"] = device.recommendedLayers;
        deviceObj["memorySize"] = static_cast<qint64>(device.memorySize);
        deviceObj["availableMemory"] = static_cast<qint64>(device.availableMemory);

        devicesArray.append(deviceObj);
    }

    result["devices"] = devicesArray;
    result["count"] = devicesArray.size();

    return QJsonDocument(result).toJson(QJsonDocument::Compact);
}

QString KnowledgeApi::getGpuDeviceInfo(int deviceId) const
{
    QJsonObject result;

    LocalGGUFEmbedding::GpuDeviceInfo device = LocalGGUFEmbedding::getGpuDeviceInfo(deviceId);

    result["deviceId"] = device.deviceId;
    result["deviceName"] = device.deviceName;
    result["backend"] = device.backend;
    result["available"] = device.available;
    result["maxLayers"] = device.maxLayers;
    result["recommendedLayers"] = device.recommendedLayers;
    result["memorySize"] = static_cast<qint64>(device.memorySize);
    result["availableMemory"] = static_cast<qint64>(device.availableMemory);

    return QJsonDocument(result).toJson(QJsonDocument::Compact);
}

QString KnowledgeApi::testLocalGGUFEmbedding(const QString &text) const
{
    QJsonObject result;

    if (!m_localGGUF)
    {
        result["success"] = false;
        result["error"] = "Local GGUF engine not initialized";
        return QJsonDocument(result).toJson(QJsonDocument::Compact);
    }

    if (!m_localGGUF->isModelLoaded())
    {
        result["success"] = false;
        result["error"] = "No local GGUF model loaded";
        return QJsonDocument(result).toJson(QJsonDocument::Compact);
    }

    QElapsedTimer timer;
    timer.start();

    std::vector<float> embedding = m_localGGUF->generateEmbedding(text);

    qint64 inferenceTime = timer.elapsed();

    if (!embedding.empty())
    {
        result["success"] = true;
        result["dimension"] = static_cast<int>(embedding.size());
        result["inference_time_ms"] = inferenceTime;
        result["text_length"] = text.length();
        result["gpu_enabled"] = m_localGGUF->isGpuEnabled();

        // 提供前10个向量值作为预览
        QJsonArray preview;
        int previewSize = qMin(10, static_cast<int>(embedding.size()));
        for (int i = 0; i < previewSize; ++i)
        {
            preview.append(static_cast<double>(embedding[i]));
        }
        result["embedding_preview"] = preview;

        QString message = QString("Local GGUF embedding test successful - %1D vector in %2ms")
                              .arg(embedding.size())
                              .arg(inferenceTime);

        result["message"] = message;
    }
    else
    {
        result["success"] = false;
        result["error"] = m_localGGUF->getLastError();
        result["inference_time_ms"] = inferenceTime;
    }

    return QJsonDocument(result).toJson(QJsonDocument::Compact);
}

void KnowledgeApi::triggerAutoLoadLocalModel()
{
    logInfo("Manual trigger for auto-loading local model and vectorization check", "AutoLoad");
    autoLoadLocalModelIfNeeded();
}

QString KnowledgeApi::getGpuDiagnostics() const
{
    QJsonObject diagnostics;

    if (!m_localGGUF)
    {
        diagnostics["error"] = "Local GGUF engine not initialized";
        return QJsonDocument(diagnostics).toJson(QJsonDocument::Compact);
    }

    // 基本GPU信息
    auto gpuInfo = LocalGGUFEmbedding::detectGpuCapabilities();
    diagnostics["gpu_available"] = gpuInfo.available;
    diagnostics["gpu_device_name"] = gpuInfo.deviceName;
    diagnostics["gpu_backend"] = gpuInfo.backend;
    diagnostics["gpu_max_layers"] = gpuInfo.maxLayers;

    // 当前配置
    if (m_localGGUF->isModelLoaded())
    {
        auto config = m_localGGUF->getCurrentConfig();
        diagnostics["current_gpu_enabled"] = config.useGpu;
        diagnostics["current_gpu_layers"] = config.gpuLayers;
        diagnostics["current_gpu_device"] = config.selectedGpuDevice;
        diagnostics["model_loaded"] = true;
    }
    else
    {
        diagnostics["model_loaded"] = false;
    }

    // 推理状态
    diagnostics["inference_in_progress"] = m_localGGUF->isInferenceInProgress();
    diagnostics["last_error"] = m_localGGUF->getLastError();

    return QJsonDocument(diagnostics).toJson(QJsonDocument::Compact);
}

bool KnowledgeApi::forceResetLocalGGUF()
{
    logWarning("Force resetting Local GGUF engine", "LocalGGUF");

    if (m_localGGUF)
    {
        // 卸载当前模型
        m_localGGUF->unloadModel();

        // 重置unique_ptr（自动释放内存）
        m_localGGUF.reset();

        // 重新创建
        if (initializeLocalGGUF())
        {
            logInfo("Local GGUF engine reset successfully", "LocalGGUF");
            return true;
        }
        else
        {
            logError("Failed to reinitialize Local GGUF engine after reset", "LocalGGUF");
            return false;
        }
    }

    return false;
}

std::vector<float> KnowledgeApi::generateLocalGGUFEmbedding(const QString &text)
{
    if (!m_localGGUF || !m_localGGUF->isModelLoaded())
    {
        logError("Local GGUF model not available", "LocalGGUF");
        return std::vector<float>();
    }

    return m_localGGUF->generateEmbedding(text);
}

// ===== 自动加载本地模型和向量化检查方法实现 =====

void KnowledgeApi::autoLoadLocalModelIfNeeded()
{
    logInfo("Scheduling auto-load and vectorization check in background thread", "AutoLoad");

    // 使用线程池在真正的后台线程中执行，完全避免阻塞UI
    QThreadPool::globalInstance()->start([this]()
                                         {
        try
        {
            logInfo("Checking embedding mode and vectorization status", "AutoLoad");

            // 1. 检查数据库配置
            if (!m_databaseApi)
            {
                logInfo("Database API not available, skipping auto-load", "AutoLoad");
                return;
            }

            QJsonObject settings = m_databaseApi->getAppSettings();
            if (settings.isEmpty())
            {
                logInfo("No app settings found, skipping auto-load", "AutoLoad");
                return;
            }

            QJsonObject kbSettings = settings["knowledgeBase"].toObject();

            // 2. 检查嵌入模式
            QString embeddingMode = kbSettings["embeddingMode"].toString("cloud");
            logInfo(QString("Current embedding mode: %1").arg(embeddingMode), "AutoLoad");

            if (embeddingMode == "local")
            {
                // 本地模式处理逻辑
                logInfo("Processing local embedding mode", "AutoLoad");

                // 3. 检查是否已经加载了模型
                if (isLocalGGUFLoaded())
                {
                    logInfo("Local GGUF model already loaded, checking for pending vectorization", "AutoLoad");
                    // 即使模型已加载，也要检查是否有待向量化的chunks
                    QMetaObject::invokeMethod(this, [this]() {
                        QTimer::singleShot(500, this, [this]() {
                            checkAndRecoverPendingVectorization();
                        });
                    }, Qt::QueuedConnection);
                    return;
                }

                // 4. 获取模型配置
                QString modelPath = kbSettings["localModelPath"].toString();
                int gpuLayers = kbSettings["localGpuLayers"].toInt(20);
                int contextSize = kbSettings["localContextSize"].toInt(2048);

                if (modelPath.isEmpty())
                {
                    logInfo("Local model path not configured, skipping auto-load", "AutoLoad");
                    return;
                }

                // 5. 检查模型文件是否存在
                QFileInfo fileInfo(modelPath);
                if (!fileInfo.exists() || !fileInfo.isReadable())
                {
                    logInfo(QString("Local model file not found or not readable: %1, skipping auto-load").arg(modelPath), "AutoLoad");
                    return;
                }

                // 6. 静默加载模型（使用异步方法以便发出完成信号）
                logInfo(QString("Auto-loading local GGUF model: %1").arg(modelPath), "AutoLoad");

                // 使用异步加载方法，这样会发出 localModelLoadCompleted 信号
                // 在主线程中调用异步加载
                QMetaObject::invokeMethod(this, [this, modelPath, gpuLayers, contextSize]() {
                    loadLocalGGUFModelAsync(modelPath, gpuLayers, contextSize);
                }, Qt::QueuedConnection);
            }
            else if (embeddingMode == "cloud")
            {
                // 云端模式处理逻辑
                logInfo("Processing cloud embedding mode", "AutoLoad");

                // 检查云端API配置是否可用
                if (isApiConfigured())
                {
                    logInfo("Cloud API is configured, checking for pending vectorization", "AutoLoad");

                    // 延迟一点时间确保应用完全启动
                    QMetaObject::invokeMethod(this, [this]() {
                        QTimer::singleShot(1000, this, [this]() {
                            checkAndRecoverPendingVectorization();
                        });
                    }, Qt::QueuedConnection);
                }
                else
                {
                    logInfo("Cloud API not configured, skipping vectorization check", "AutoLoad");
                }
            }
            else
            {
                logInfo(QString("Unknown embedding mode: %1, skipping auto-load").arg(embeddingMode), "AutoLoad");
            }
        }
        catch (const std::exception &e)
        {
            logError(QString("Exception during auto-load: %1").arg(e.what()), "AutoLoad");
        }
        catch (...)
        {
            logError("Unknown exception during auto-load", "AutoLoad");
        } });
}

bool KnowledgeApi::initializeLocalGGUF()
{
    try
    {
        logInfo("Initializing local GGUF embedding engine", "LocalGGUF");
        m_localGGUF = std::make_unique<LocalGGUFEmbedding>(this);

        // 连接信号
        connect(m_localGGUF.get(), &LocalGGUFEmbedding::modelLoaded,
                this, [this](const QString &modelPath)
                { logInfo(QString("Local GGUF model loaded signal: %1").arg(modelPath), "LocalGGUF"); });

        connect(m_localGGUF.get(), &LocalGGUFEmbedding::modelUnloaded,
                this, [this]()
                { logInfo("Local GGUF model unloaded signal", "LocalGGUF"); });

        connect(m_localGGUF.get(), &LocalGGUFEmbedding::errorOccurred,
                this, [this](const QString &error)
                { logError(QString("Local GGUF error: %1").arg(error), "LocalGGUF"); });

        // 连接模型加载完成信号，在模型加载成功后自动检查待向量化的chunks
        connect(this, &KnowledgeApi::localModelLoadCompleted,
                this, [this](bool success, const QString &message)
                {
                    if (success) {
                        logInfo("Model loaded successfully, checking for pending vectorization tasks", "AutoLoad");
                        // 延迟一点时间确保模型完全就绪
                        QTimer::singleShot(500, this, [this]() {
                            checkAndRecoverPendingVectorization();
                        });
                    } else {
                        logInfo(QString("Model loading failed: %1, skipping vectorization check").arg(message), "AutoLoad");
                    } });

        logInfo("Local GGUF embedding engine initialized successfully", "LocalGGUF");
        return true;
    }
    catch (const std::exception &e)
    {
        logError(QString("Failed to initialize local GGUF engine: %1").arg(e.what()), "LocalGGUF");
        return false;
    }
}

void KnowledgeApi::cleanupLocalGGUF()
{
    if (m_localGGUF)
    {
        logInfo("Cleaning up local GGUF embedding engine", "LocalGGUF");
        m_localGGUF.reset();
    }
}

QString KnowledgeApi::selectFile(const QString &extension, const QString &caption, const QString &filter) const
{
    logInfo("Opening file selection dialog", "FileSelection");

    QString dialogCaption = caption.isEmpty() ? "选择文件" : caption;
    QString dialogFilter = filter;

    // 如果没有提供过滤器但提供了扩展名，构造默认过滤器
    if (dialogFilter.isEmpty() && !extension.isEmpty())
    {
        QString ext = extension.startsWith('.') ? extension.mid(1) : extension;
        dialogFilter = QString("%1 文件 (*.%2)").arg(ext.toUpper()).arg(ext);
    }

    // 如果仍然没有过滤器，使用通用过滤器
    if (dialogFilter.isEmpty())
    {
        dialogFilter = "所有文件 (*.*)";
    }

    QString selectedFile = QFileDialog::getOpenFileName(
        nullptr,
        dialogCaption,
        QString(), // 默认目录
        dialogFilter);

    if (!selectedFile.isEmpty())
    {
        logInfo(QString("File selected: %1").arg(selectedFile), "FileSelection");
    }
    else
    {
        logInfo("File selection cancelled", "FileSelection");
    }

    return selectedFile;
}

// ===== 私有批处理方法实现 =====

void KnowledgeApi::processSmallBatch(qint64 docId, const QList<QPair<qint64, QString>> &chunks)
{
    logInfo(QString("Processing small batch: %1 chunks").arg(chunks.size()), "vectorization");

    for (const auto &chunkPair : chunks)
    {
        // 检查取消标志
        if (m_vectorizationCancelled.load())
        {
            logInfo("Vectorization cancelled during small batch processing", "vectorization");
            emit documentVectorized(docId, 0);
            return;
        }

        qint64 chunkId = chunkPair.first;
        QString content = chunkPair.second;

        std::vector<float> embedding = textToEmbedding(content);
        if (!embedding.empty())
        {
#ifndef OBJECTBOX_DISABLED
            auto chunk = m_chunkBox->get(chunkId);
            if (chunk)
            {
                chunk->embedding.assign(embedding.begin(), embedding.end());
                chunk->is_vectorized = true;
                m_chunkBox->put(*chunk);

                // 发送进度信号
                emit chunkVectorized(QString::number(docId), QString::number(docId), chunk->chunk_index, chunks.size());
            }
#endif
        }

        // 短暂休眠，避免阻塞UI
        QThread::msleep(1);
    }

    // 更新文档状态
    updateDocumentCompletionStatus(docId, chunks.size());
}

void KnowledgeApi::processLargeBatch(qint64 docId, const QList<QPair<qint64, QString>> &chunks)
{
    logInfo(QString("Processing large batch: %1 chunks, using %2 chunk batches").arg(chunks.size()).arg(BATCH_SIZE), "vectorization");

    // 分批处理
    QList<QList<QPair<qint64, QString>>> batches;
    for (int i = 0; i < chunks.size(); i += BATCH_SIZE)
    {
        QList<QPair<qint64, QString>> batch;
        int end = qMin(i + BATCH_SIZE, chunks.size());
        for (int j = i; j < end; ++j)
        {
            batch.append(chunks[j]);
        }
        batches.append(batch);
    }

    logInfo(QString("Created %1 batches for processing").arg(batches.size()), "vectorization");

    // 使用批处理工作线程
    for (const auto &batch : batches)
    {
        if (m_vectorizationCancelled.load())
        {
            logInfo("Vectorization cancelled during large batch processing", "vectorization");
            emit documentVectorized(docId, 0);
            return;
        }

        BatchVectorizationWorker *worker = new BatchVectorizationWorker(this, batch, &m_vectorizationCancelled);
        worker->setAutoDelete(true);
        QThreadPool::globalInstance()->start(worker);

        // 批次间延迟，避免系统过载
        QThread::msleep(BATCH_DELAY_MS * 5);
    }

    updateDocumentCompletionStatus(docId, chunks.size());
}

void KnowledgeApi::updateDocumentCompletionStatus(qint64 docId, int totalChunks)
{
#ifndef OBJECTBOX_DISABLED
    try
    {
        auto doc = m_docBox->get(docId);
        if (doc)
        {
            // 文档对象可能没有这些字段，跳过状态更新
            // doc 结构基于生成的 ObjectBox 模型，字段名可能不同
            m_docBox->put(*doc);

            logInfo(QString("Document %1 vectorization completed with %2 chunks").arg(docId).arg(totalChunks), "vectorization");
        }
    }
    catch (const std::exception &e)
    {
        logError(QString("Failed to update document completion status: %1").arg(e.what()), "vectorization");
    }
#endif

    m_activeVectorizationCount.fetch_sub(1, std::memory_order_relaxed);
    emit documentVectorized(docId, totalChunks);
}

// ===== 向量化工作线程实现 =====

KnowledgeApi::VectorizationWorker::VectorizationWorker(KnowledgeApi *api, qint64 docId, const QString &content, const QString &documentType, std::atomic_bool *cancelled)
    : m_api(api), m_docId(docId), m_content(content), m_documentType(documentType), m_cancelled(cancelled)
{
    setAutoDelete(true); // 自动删除任务
}

void KnowledgeApi::VectorizationWorker::run()
{
    if (m_api)
    {
        // 检查是否已取消
        if (m_cancelled && m_cancelled->load())
        {
            qDebug() << "⚠️ [VectorizationWorker] Vectorization cancelled for doc ID:" << m_docId;
            return;
        }

        m_api->processDocumentVectorization(m_docId, m_content, m_documentType);
    }
}

// ===== 批处理向量化工作线程实现 =====

KnowledgeApi::BatchVectorizationWorker::BatchVectorizationWorker(KnowledgeApi *api, const QList<QPair<qint64, QString>> &chunks, std::atomic_bool *cancelled)
    : m_api(api), m_chunks(chunks), m_cancelled(cancelled)
{
    setAutoDelete(true);
}

void KnowledgeApi::BatchVectorizationWorker::run()
{
    if (!m_api || m_chunks.isEmpty())
    {
        return;
    }

    int processed = 0;
    int total = m_chunks.size();

    // 处理批次中的每个chunk
    for (const auto &chunkPair : m_chunks)
    {
        // 检查是否已取消
        if (m_cancelled && m_cancelled->load())
        {
            qDebug() << "⚠️ [BatchVectorizationWorker] Vectorization cancelled, processed:" << processed << "of" << total;
            break;
        }

        qint64 chunkId = chunkPair.first;
        QString content = chunkPair.second;

        // 生成向量并更新数据库
        try
        {
            std::vector<float> embedding = m_api->textToEmbedding(content);
            if (!embedding.empty())
            {
// 更新chunk的向量
#ifndef OBJECTBOX_DISABLED
                auto chunk = m_api->m_chunkBox->get(chunkId);
                if (chunk)
                {
                    chunk->embedding.assign(embedding.begin(), embedding.end());
                    chunk->is_vectorized = true;
                    m_api->m_chunkBox->put(*chunk);
                }
#endif
            }

            processed++;

            // 每处理完一个chunk后短暂休眠，避免CPU过度占用
            if (processed % 10 == 0)
            {
                QThread::msleep(BATCH_DELAY_MS);
            }
        }
        catch (const std::exception &e)
        {
            qDebug() << "❌ [BatchVectorizationWorker] Error processing chunk" << chunkId << ":" << e.what();
        }
    }
}

void KnowledgeApi::processDocumentVectorizationInThread(qint64 docId, const QString &content, const QString &documentType)
{
    logInfo(QString("Starting batched vectorization for document ID: %1").arg(docId), "vectorization");

    // 重置取消标志
    m_vectorizationCancelled.store(false);
    m_activeVectorizationCount.fetch_add(1, std::memory_order_relaxed);

    // 创建异步工作线程
    VectorizationWorker *worker = new VectorizationWorker(this, docId, content, documentType, &m_vectorizationCancelled);
    worker->setAutoDelete(true);
    QThreadPool::globalInstance()->start(worker);

    logInfo(QString("Vectorization task queued, active threads: %1, max threads: %2")
                .arg(QThreadPool::globalInstance()->activeThreadCount())
                .arg(QThreadPool::globalInstance()->maxThreadCount()),
            "vectorization");
}

// ===== 批量处理控制方法 =====

void KnowledgeApi::cancelCurrentVectorization()
{
    QMutexLocker locker(&m_vectorizationMutex);
    m_vectorizationCancelled.store(true);
    logInfo("Vectorization cancellation requested", "vectorization");
}

bool KnowledgeApi::isVectorizationInProgress() const
{
    return m_activeVectorizationCount.load() > 0;
}

int KnowledgeApi::getCurrentBatchSize() const
{
    return m_currentBatchSize.load();
}

QString KnowledgeApi::getVectorizationStatus() const
{
    QJsonObject status;
    status["isProcessing"] = isVectorizationInProgress();
    status["activeTasks"] = m_activeVectorizationCount.load();
    status["currentBatchSize"] = m_currentBatchSize.load();
    status["maxThreadCount"] = QThreadPool::globalInstance()->maxThreadCount();
    status["activeThreadCount"] = QThreadPool::globalInstance()->activeThreadCount();

    return QJsonDocument(status).toJson(QJsonDocument::Compact);
}

// ===== 新的后台向量化工作线程 =====

// BackgroundVectorizationWorker implementation

KnowledgeApi::BackgroundVectorizationWorker::BackgroundVectorizationWorker(KnowledgeApi *api,
                                                                           const QList<QPair<qint64, QString>> &chunks,
                                                                           ProgressTracker *tracker,
                                                                           std::atomic_bool *cancelled)
    : m_api(api), m_chunks(chunks), m_tracker(tracker), m_cancelled(cancelled)
{
    setAutoDelete(true);
}

void KnowledgeApi::BackgroundVectorizationWorker::run()
{
    if (!m_api || m_chunks.isEmpty())
    {
        if (m_tracker)
            m_tracker->completed();
        return;
    }

    int processed = 0;
    int total = m_chunks.size();

    qDebug() << "🔄 [BackgroundVectorizationWorker] Start background vectorization, total chunks:" << total;

    // 确保在退出时清理tracker，避免内存泄漏
    auto guard = qScopeGuard([this]()
                             {
        if (m_tracker)
        {
            // 使用异步方式删除tracker，避免在信号处理过程中删除对象
            QMetaObject::invokeMethod(m_api, [tracker = m_tracker]() {
                delete tracker;
            }, Qt::QueuedConnection);
        } });

    for (const auto &chunkPair : m_chunks)
    {
        // 检查是否已取消
        if (m_cancelled && m_cancelled->load())
        {
            qDebug() << "⚠️ [BackgroundVectorizationWorker] Vectorization cancelled, processed:" << processed << "of" << total;
            break;
        }

        qint64 chunkId = chunkPair.first;
        QString content = chunkPair.second;

        try
        {
            // 生成向量 - 这是耗时的操作，在后台线程中执行
            std::vector<float> embedding = m_api->textToEmbedding(content);

            if (!embedding.empty())
            {
// 更新chunk的向量数据 - 线程安全操作
#ifndef OBJECTBOX_DISABLED
                try
                {
                    // 确保数据库连接在线程中是有效的
                    auto chunk = m_api->m_chunkBox->get(chunkId);
                    if (chunk)
                    {
                        chunk->embedding.assign(embedding.begin(), embedding.end());
                        chunk->is_vectorized = true;
                        m_api->m_chunkBox->put(*chunk);

                        processed++;

                        // 发送进度更新到主线程
                        if (m_tracker)
                        {
                            m_tracker->updateProgress(processed);
                            m_tracker->chunkCompleted(chunk->chunk_index);
                        }

                        qDebug() << "✅ [BackgroundVectorizationWorker] Vectorization completed" << processed << "/" << total
                                 << ", chunk ID:" << chunkId << ", embedding dimension:" << embedding.size();
                    }
                }
                catch (const std::exception &e)
                {
                    qDebug() << "❌ [BackgroundVectorizationWorker] Database error processing chunk, ID:" << chunkId << ":" << e.what();
                }
#endif
            }
            else
            {
                qWarning() << "⚠️ [BackgroundVectorizationWorker] Vectorization failed, chunk ID:" << chunkId;
            }
        }
        catch (const std::exception &e)
        {
            qDebug() << "❌ [BackgroundVectorizationWorker] Error processing chunk, ID:" << chunkId << ":" << e.what();
        }

        // 添加小延迟避免过度占用CPU，特别是对本地模型
        QThread::msleep(5);
    }

    qDebug() << "🎉 [BackgroundVectorizationWorker] Background vectorization completed, successfully processed:" << processed << "/" << total;

    // 通知完成 - 使用guard确保tracker被正确清理
    if (m_tracker)
    {
        m_tracker->completed();
    }
}

void KnowledgeApi::BackgroundVectorizationWorker::ProgressTracker::updateProgress(int completed)
{
    if (api)
    {
        QMetaObject::invokeMethod(api, [this, completed]()
                                  { emit api->vectorizationProgress(QString::number(kbId), QString::number(docId), completed, totalChunks); }, Qt::QueuedConnection);
    }
}

void KnowledgeApi::BackgroundVectorizationWorker::ProgressTracker::chunkCompleted(int chunkIndex)
{
    if (api)
    {
        QMetaObject::invokeMethod(api, [this, chunkIndex]()
                                  { emit api->chunkVectorized(QString::number(kbId), QString::number(docId), chunkIndex, totalChunks); }, Qt::QueuedConnection);
    }
}

void KnowledgeApi::BackgroundVectorizationWorker::ProgressTracker::completed()
{
    if (api)
    {
        QMetaObject::invokeMethod(api, [this]()
                                  {
            emit api->documentVectorized(docId, totalChunks);
            api->m_activeVectorizationCount.fetch_sub(1, std::memory_order_relaxed); }, Qt::QueuedConnection);
    }
}

void KnowledgeApi::processDocumentVectorizationInBackground(obx_id docId, const std::vector<InkCop::Knowledge::KnowledgeChunk> &chunks)
{
    qDebug() << "🔄 [KnowledgeApi] Start background vectorization, document ID:" << docId << "chunks size:" << chunks.size();

    // 重置取消标志
    m_vectorizationCancelled.store(false);
    m_activeVectorizationCount.fetch_add(1, std::memory_order_relaxed);

    // 准备数据供后台线程使用
    QList<QPair<qint64, QString>> chunkList;
    auto doc = m_docBox->get(docId);
    if (!doc)
    {
        logError("Failed to get document for background vectorization", "processDocumentVectorizationInBackground");
        m_activeVectorizationCount.fetch_sub(1, std::memory_order_relaxed);
        return;
    }

    obx_id kbId = doc->kb_id;

    for (const auto &chunk : chunks)
    {
        chunkList.append(qMakePair(static_cast<qint64>(chunk.id), QString::fromStdString(chunk.content)));
    }

    // 创建进度追踪器
    auto *tracker = new BackgroundVectorizationWorker::ProgressTracker{this, kbId, docId, static_cast<int>(chunks.size())};

    // 创建后台工作线程
    auto *worker = new BackgroundVectorizationWorker(this, chunkList, tracker, &m_vectorizationCancelled);

    // 使用全局线程池启动后台处理
    QThreadPool::globalInstance()->start(worker);

    qDebug() << "✅ [KnowledgeApi] Background vectorization task started, active threads:"
             << QThreadPool::globalInstance()->activeThreadCount()
             << "/" << QThreadPool::globalInstance()->maxThreadCount();
}
