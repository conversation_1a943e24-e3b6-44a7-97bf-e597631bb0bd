// Performance Optimization Integration Example for SplitterEditor.vue
// This file demonstrates how to integrate all performance optimization systems

import { onMounted, onUnmounted } from 'vue';
import type { Editor } from '@tiptap/vue-3';
import { usePerformanceOptimization } from './PerformanceOptimizationLayer';
import { useMemoryManager } from './MemoryManager';
import { usePerformanceMonitor } from './PerformanceMonitor';
// import { useEditorStateManager } from './EditorStateManager';
// import { useEditorInstancePool } from './EditorInstancePool';
import { useVirtualDragSystem } from './VirtualDragSystem';
import { useCommandManager, CommandCategory } from './CommandManager';

export function useOptimizedSplitterEditor() {
  // Get all optimization systems
  const performanceLayer = usePerformanceOptimization();
  const memoryManager = useMemoryManager();
  const performanceMonitor = usePerformanceMonitor();
  // const stateManager = useEditorStateManager();
  // const instancePool = useEditorInstancePool();
  const dragSystem = useVirtualDragSystem();
  const commandManager = useCommandManager();

  // Initialize performance monitor
  performanceMonitor.initialize();
  performanceMonitor.start();

  // Initialize performance optimization layer with custom config
  const initializeOptimizations = () => {
    // 使用 PERFORMANCE_CONFIG 中的默认值，只覆盖需要的部分
    performanceLayer.initialize({
      // 大部分配置已经在 performance.config.ts 中定义
      // 这里只需要覆盖特定于此用例的配置
      maxEditorInstances: 20, // 覆盖默认的实例数
      instancePrewarmCount: 3,
      instanceRecycleDelay: 10000, // 10 seconds

      // 其他配置将使用 performance.config.ts 中的默认值
    });
  };

  // Register an editor instance
  const registerEditor = (paneId: string, editor: Editor) => {
    performanceMonitor.startTiming(`register-editor-${paneId}`);

    // Register with performance layer
    performanceLayer.registerEditor(paneId, editor);

    // Setup command shortcuts
    setupEditorCommands(editor);

    // Setup performance tracking
    setupPerformanceTracking(editor);

    performanceMonitor.endTiming(`register-editor-${paneId}`);
  };

  // Unregister an editor instance
  const unregisterEditor = (paneId: string) => {
    performanceLayer.unregisterEditor(paneId);
  };

  // Setup editor commands
  const setupEditorCommands = (editor: Editor) => {
    // Set editor for command manager
    commandManager.setEditor(editor);

    // Register custom commands
    commandManager.registerCommand({
      id: 'editor.splitPane',
      name: 'Split Pane',
      description: 'Split the current pane',
      icon: 'splitscreen',
      shortcut: 'Ctrl+\\',
      category: CommandCategory.EDITOR,
      execute: () => {
        // Split pane logic
        console.log('Splitting pane...');
      },
      priority: 95,
    });

    commandManager.registerCommand({
      id: 'editor.closePane',
      name: 'Close Pane',
      description: 'Close the current pane',
      icon: 'close',
      shortcut: 'Ctrl+W',
      category: CommandCategory.EDITOR,
      execute: () => {
        // Close pane logic
        console.log('Closing pane...');
      },
      priority: 94,
    });

    commandManager.registerCommand({
      id: 'editor.focusNext',
      name: 'Focus Next Pane',
      description: 'Focus the next editor pane',
      icon: 'arrow_forward',
      shortcut: 'Ctrl+Tab',
      category: CommandCategory.EDITOR,
      execute: () => {
        // Focus next pane logic
        console.log('Focusing next pane...');
      },
      priority: 93,
    });
  };

  // Setup performance tracking
  const setupPerformanceTracking = (editor: Editor) => {
    // Track editor transactions
    editor.on('transaction', ({ transaction }) => {
      if (transaction.docChanged) {
        performanceMonitor.recordMetric('editor-transaction', transaction.steps.length);
      }
    });

    // Track selection changes
    editor.on('selectionUpdate', () => {
      performanceMonitor.recordMetric('selection-update', 1);
    });

    // Monitor memory on large operations
    editor.on('update', ({ transaction }) => {
      if (transaction.steps.length > 10) {
        const memUsage = memoryManager.manager.getMemoryUsage();
        if (memUsage.percentage > 80) {
          console.warn('High memory usage during large operation');
        }
      }
    });
  };

  // Optimize for large documents
  const optimizeForLargeDocument = (docSize: number) => {
    if (docSize > 1000000) {
      // 1MB
      console.log('Optimizing for large document...');

      // Adjust memory thresholds
      performanceLayer.updateConfig({
        memoryWarningThreshold: 200 * 1024 * 1024, // 200MB
        memoryCriticalThreshold: 400 * 1024 * 1024, // 400MB
      });

      // Enable aggressive optimizations
      performanceLayer.setOptimizationEnabled('virtualRendering', true);
      performanceLayer.setOptimizationEnabled('autoCleanup', true);

      // Run optimization pass
      performanceLayer.optimize();
    }
  };

  // Handle drag operations
  const setupDragHandlers = (handleElement: HTMLElement) => {
    const cleanup = dragSystem.registerHandle(handleElement, {
      onDragStart: (e) => {
        performanceMonitor.startTiming('pane-resize');
        console.log('Drag started:', e);
      },
      onDragMove: (e) => {
        // Update pane sizes
        console.log('Dragging:', e);
      },
      onDragEnd: (e) => {
        performanceMonitor.endTiming('pane-resize');
        console.log('Drag ended:', e);
      },
    });

    return cleanup;
  };

  // Monitor and report performance
  const startPerformanceMonitoring = () => {
    // Get metrics every 10 seconds
    const interval = setInterval(() => {
      const metrics = performanceLayer.getMetrics();

      // Log if memory is high
      if (metrics.memory.heapUsed > 100 * 1024 * 1024) {
        console.warn('High memory usage detected:', metrics);
      }

      // Log if FPS is low
      if (metrics.performance.fps < 30) {
        console.warn('Low FPS detected:', metrics);
      }

      // Store metrics for analytics
      performanceMonitor.recordMetric('memory-usage', metrics.memory.heapUsed);
      performanceMonitor.recordMetric('fps', metrics.performance.fps);
      performanceMonitor.recordMetric('active-editors', metrics.editors.active);
    }, 10000);

    return () => clearInterval(interval);
  };

  // Lifecycle management
  onMounted(() => {
    // Initialize all systems
    initializeOptimizations();

    // Start monitoring
    const stopMonitoring = startPerformanceMonitoring();

    // Cleanup on unmount
    onUnmounted(() => {
      stopMonitoring();
      performanceLayer.destroy();
    });
  });

  // Return API
  return {
    // Editor management
    registerEditor,
    unregisterEditor,

    // Optimization controls
    optimizeForLargeDocument,
    runOptimization: () => performanceLayer.optimize(),

    // Drag system
    setupDragHandlers,

    // Metrics
    getMetrics: () => performanceLayer.getMetrics(),
    getConfig: () => performanceLayer.getConfig(),

    // Command palette
    openCommandPalette: () => commandManager.openCommandPalette(),

    // Memory management
    forceCleanup: () => {
      memoryManager.manager.performCleanup();
    },

    // Performance tracking
    startTimer: (name: string) => performanceMonitor.startTiming(name),
    endTimer: (name: string) => performanceMonitor.endTiming(name),
  };
}
