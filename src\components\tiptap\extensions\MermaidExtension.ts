import { Node, mergeAttributes, nodeInputRule } from '@tiptap/core';
import { VueNodeViewRenderer } from '@tiptap/vue-3';
import MermaidComponent from '../MermaidComponent.vue';

export interface MermaidOptions {
  HTMLAttributes: Record<string, unknown>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    mermaid: {
      /**
       * Insert a mermaid diagram
       */
      insertMermaid: (options?: { code?: string }) => ReturnType;
    };
  }
}

export const MermaidExtension = Node.create<MermaidOptions>({
  name: 'mermaid',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  addAttributes() {
    return {
      code: {
        default:
          'graph TD\n    A[开始] --> B{是否完成?}\n    B -->|是| C[结束]\n    B -->|否| D[继续工作]\n    D --> B',
        parseHTML: (element) => element.getAttribute('data-code'),
        renderHTML: (attributes) => {
          if (!attributes.code) {
            return {};
          }
          return {
            'data-code': attributes.code,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="mermaid"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        'data-type': 'mermaid',
      }),
    ];
  },

  addCommands() {
    return {
      insertMermaid:
        (options = {}) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              code:
                options.code ||
                'graph TD\n    A[开始] --> B{是否完成?}\n    B -->|是| C[结束]\n    B -->|否| D[继续工作]\n    D --> B',
            },
          });
        },
    };
  },

  addInputRules() {
    return [
      nodeInputRule({
        find: /^```mermaid$/,
        type: this.type,
        getAttributes: () => ({
          code: 'graph TD\n    A[开始] --> B{是否完成?}\n    B -->|是| C[结束]\n    B -->|否| D[继续工作]\n    D --> B',
        }),
      }),
    ];
  },

  addNodeView() {
    return VueNodeViewRenderer(MermaidComponent);
  },
});
