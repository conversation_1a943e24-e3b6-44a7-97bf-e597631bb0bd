/**
 * @description 文档的数据结构
 */
export interface Document {
  id: number;
  title?: string;
  created_at?: string;
  updated_at?: string;
  sort_order?: number;
  folder_id?: number; // -1 表示不在任何文件夹中
  success?: boolean;
  error?: string;
  content?: string; // 需要解析为 JSONContent
  metadata?: string; // 需要解析为 DocumentMetadata
  snapshot?: DocumentSnapshot[]; // 修改为数组类型
  conversations?: Array<{
    id: number;
    document_id: number; // -1 表示独立对话
    title?: string;
    messages?: string;
    prompt?: string;
    created_at?: string;
    updated_at?: string;
  }>;
  documents?: Array<{
    id: number;
  }>;
  knowledge_document_id?: number;
}

/**
 * @description 文档-知识库关联的数据结构
 */
export interface DocumentKnowledgeAssociation {
  id: number;
  document_id: number;
  knowledge_document_id: number;
  knowledge_base_id: number;
  created_at: string;
  updated_at: string;
}

// 定义快照类型
export interface DocumentSnapshot {
  name: string;
  content: string;
  created_at: string;
}

/**
 * @description 文件夹的数据结构
 */
export interface Folder {
  success?: boolean;
  error?: string;
  id: number;
  name?: string;
  parent_id: number; // -1 表示根文件夹
  created_at?: string;
  updated_at?: string;
  sort_order?: number;
  children?: Folder[];
  documents?: Document[];
}
