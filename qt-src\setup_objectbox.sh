#!/bin/bash

# ObjectBox集成设置脚本
# 这个脚本帮助设置ObjectBox开发环境

set -e

echo "=== InkCop ObjectBox 集成设置 ==="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "项目根目录: $PROJECT_ROOT"

# 创建third-party目录
THIRD_PARTY_DIR="$PROJECT_ROOT/third-party"
OBJECTBOX_DIR="$THIRD_PARTY_DIR/objectbox"

echo "创建third-party目录结构..."
mkdir -p "$OBJECTBOX_DIR"/{include,lib}

# 检查ObjectBox库是否已存在
if [ ! -f "$OBJECTBOX_DIR/lib/libobjectbox.so" ]; then
    echo "⚠️  ObjectBox库未找到"
    echo "请按照以下步骤手动安装ObjectBox:"
    echo ""
    echo "1. 从 https://github.com/objectbox/objectbox-c 下载ObjectBox C++ SDK"
    echo "2. 将头文件复制到: $OBJECTBOX_DIR/include/"
    echo "3. 将库文件复制到: $OBJECTBOX_DIR/lib/"
    echo ""
    echo "或者运行:"
    echo "  # 下载ObjectBox预编译库"
    echo "  cd $THIRD_PARTY_DIR"
    echo "  wget -O objectbox-c.tar.gz 'https://github.com/objectbox/objectbox-c/releases/latest/download/objectbox-c-linux-x64.tar.gz'"
    echo "  tar -xzf objectbox-c.tar.gz"
    echo "  mv objectbox-c/* objectbox/"
    echo "  rm -rf objectbox-c objectbox-c.tar.gz"
    echo ""
fi

# 检查FlatBuffers
echo "检查FlatBuffers支持..."
if ! command -v flatc &> /dev/null; then
    echo "⚠️  FlatBuffers编译器 (flatc) 未找到"
    echo "请安装FlatBuffers:"
    echo "  # Ubuntu/Debian:"
    echo "  sudo apt-get install flatbuffers-compiler libflatbuffers-dev"
    echo "  # 或者从源码编译: https://github.com/google/flatbuffers"
else
    echo "✅ FlatBuffers编译器已安装: $(flatc --version)"
fi

# 生成FlatBuffers C++代码
OBJECTBOX_SRC_DIR="$PROJECT_ROOT/qt-src/objectbox"
if [ -f "$OBJECTBOX_SRC_DIR/knowledge.fbs" ]; then
    echo "生成FlatBuffers C++绑定代码..."
    cd "$OBJECTBOX_SRC_DIR"
    
    if command -v flatc &> /dev/null; then
        flatc --cpp --gen-object-api knowledge.fbs
        echo "✅ FlatBuffers代码生成完成"
    else
        echo "⚠️  跳过FlatBuffers代码生成 (flatc未安装)"
    fi
fi

# 检查CMake配置
echo "检查CMake配置..."
CMAKE_FILE="$PROJECT_ROOT/CMakeLists.txt"
if grep -q "OBJECTBOX_LIBRARY" "$CMAKE_FILE"; then
    echo "✅ CMake已配置ObjectBox支持"
else
    echo "⚠️  CMake配置可能不完整"
fi

# 总结
echo ""
echo "=== 集成状态检查 ==="

if [ -f "$OBJECTBOX_DIR/lib/libobjectbox.so" ]; then
    echo "✅ ObjectBox库: 已安装"
else
    echo "❌ ObjectBox库: 未安装"
fi

if [ -f "$OBJECTBOX_SRC_DIR/knowledge_generated.h" ]; then
    echo "✅ FlatBuffers代码: 已生成"
else
    echo "❌ FlatBuffers代码: 未生成"
fi

if [ -f "$OBJECTBOX_SRC_DIR/model.h" ]; then
    echo "✅ ObjectBox模型: 已创建"
else
    echo "❌ ObjectBox模型: 未创建"
fi

echo ""
echo "=== 下一步 ==="
echo "1. 确保ObjectBox库已正确安装到 $OBJECTBOX_DIR/"
echo "2. 运行 'mkdir build && cd build && cmake .. && make' 编译项目"
echo "3. 如果遇到链接错误，检查ObjectBox库路径配置"
echo ""
echo "如需帮助，请查看 docs/OBJECTBOX_MIGRATION_PLAN.md" 