import type { KnowledgeSearchResult } from 'src/env';
import { $t } from 'src/composables/useTrans';

/**
 * 知识库工具集合
 *
 * 包含所有与知识库操作相关的AI工具：
 * - 搜索知识库内容
 * - 获取知识库信息
 * - 添加文档到知识库 (未来)
 * - 删除知识库内容 (未来)
 * - 获取知识库统计信息 (未来)
 */

// ==================== 搜索相关工具 ====================

interface KnowledgeSearchOptions {
  query: string;
  knowledge_base_id?: number;
  limit?: number;
  min_score?: number;
}

/**
 * 搜索知识库内容
 */
async function searchKnowledge(options: KnowledgeSearchOptions): Promise<{
  success: boolean;
  message: string;
  tool_name: string;
  results?: KnowledgeSearchResult[];
  knowledge_base_name?: string;
  query?: string;
  total_results?: number;
}> {
  try {
    console.log('🔍 [Knowledge Search] 开始搜索知识库:', options);

    // 导入知识库相关模块
    const { useKnowledge } = await import('src/composeables/useKnowledge');
    const { knowledgeService } = await import('src/services/knowledgeService');

    const knowledge = useKnowledge();

    // 获取当前选中的知识库
    const selectedKnowledgeBase = knowledgeService.getSelectedKnowledgeBase();

    // 确定要搜索的知识库ID
    let targetKnowledgeBaseId: number;
    let knowledgeBaseName: string;

    if (options.knowledge_base_id) {
      // 如果指定了知识库ID，使用指定的
      targetKnowledgeBaseId = options.knowledge_base_id;
      // 需要获取知识库名称
      try {
        const { useLlmStore } = await import('src/stores/llm');
        const llmStore = useLlmStore();
        await llmStore.loadKnowledgeBasesList();
        const targetKb = llmStore.knowledgeBases?.find((kb) => kb.id === targetKnowledgeBaseId);
        knowledgeBaseName =
          targetKb?.name ||
          $t('src.llm.tools.knowledgeBase.knowledgeBaseName', { id: targetKnowledgeBaseId });
      } catch (error) {
        console.warn('获取知识库名称失败:', error);
        knowledgeBaseName = $t('src.llm.tools.knowledgeBase.knowledgeBaseName', {
          id: targetKnowledgeBaseId,
        });
      }
    } else if (selectedKnowledgeBase) {
      // 如果没有指定，使用当前选中的知识库
      targetKnowledgeBaseId = selectedKnowledgeBase.id;
      knowledgeBaseName = selectedKnowledgeBase.name;
    } else {
      return {
        success: false,
        message: $t('src.llm.tools.knowledgeBase.noKnowledgeBaseSelected'),
        tool_name: 'search_knowledge',
      };
    }

    // 执行搜索
    const results = await knowledge.searchKnowledge(
      options.query,
      targetKnowledgeBaseId,
      options.limit || 10,
      options.min_score || 0.01,
    );

    console.log(`✅ [Knowledge Search] 搜索完成，找到 ${results.length} 个结果`);

    // 格式化结果为自然语言
    let message = $t('src.llm.tools.knowledgeBase.searchResult', {
      knowledgeBaseName,
      query: options.query,
      resultCount: results.length,
    });

    if (results.length === 0) {
      message = $t('src.llm.tools.knowledgeBase.noResult', {
        knowledgeBaseName,
        query: options.query,
      });
    } else {
      results.forEach((result, index) => {
        message += $t('src.llm.tools.knowledgeBase.resultItem', {
          index: index + 1,
          title: result.document_title || $t('src.llm.tools.knowledgeBase.relatedContent'),
          score: (result.score * 100).toFixed(1),
          summary: result.memory.substring(0, 200),
          ellipsis: result.memory.length > 200 ? '...' : '',
        });
      });

      message += $t('src.llm.tools.knowledgeBase.resultFooter');
    }

    return {
      success: true,
      message,
      results,
      knowledge_base_name: knowledgeBaseName,
      query: options.query,
      total_results: results.length,
      tool_name: 'search_knowledge',
    };
  } catch (error) {
    console.error('❌ [Knowledge Search] 搜索失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.knowledgeBase.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
      tool_name: 'search_knowledge',
    };
  }
}

// ==================== 未来工具扩展区域 ====================

// TODO: 添加更多知识库工具
// - getKnowledgeBaseInfo: 获取知识库详细信息
// - addDocumentToKnowledgeBase: 添加文档到知识库
// - removeDocumentFromKnowledgeBase: 从知识库删除文档
// - getKnowledgeBaseStats: 获取知识库统计信息
// - listKnowledgeBases: 列出所有可用知识库

// ==================== 工具导出 ====================

// 导出工具函数映射
export const knowledgeBaseTools = {
  search_knowledge: searchKnowledge,
  // 未来在这里添加更多工具
};

// 导出工具映射表
export const knowledgeBaseToolMappings = {
  search_knowledge: searchKnowledge,
  // 未来在这里添加更多工具映射
} as const;

// 导出工具schema
export const tools = [
  {
    type: 'function' as const,
    function: {
      name: 'search_knowledge',
      description:
        '在知识库中搜索相关内容。当需要查找特定信息、回答基于知识库的问题时使用此工具。会自动使用用户当前选择的知识库进行搜索。',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: '搜索查询字符串，应该是具体的问题或关键词，而不是用户的原始输入',
          },
          knowledge_base_id: {
            type: 'number',
            description: '可选：指定要搜索的知识库ID。如果不指定，将使用用户当前选择的知识库',
          },
          limit: {
            type: 'number',
            description: '返回结果的最大数量，默认为5',
            minimum: 1,
            maximum: 20,
          },
          min_score: {
            type: 'number',
            description: '最小相似度分数，0-1之间，默认为0.01',
            minimum: 0,
            maximum: 1,
          },
        },
        required: ['query'],
      },
    },
  },
  // 未来在这里添加更多工具的schema定义
];
