import { ref, type Ref } from 'vue';

export interface DialogState {
  show: boolean;
  title: string;
  content: string;
}

export type DialogRef = Ref<
  DialogState & {
    parentId?: number | null;
    id?: number | null;
  }
>;

export type DialogExtra = {
  parentId?: number | null;
  id?: number | null;
  [key: string]: unknown;
};

export function useDialog() {
  const createFolderDialog = ref<DialogState & { parentId: number | null }>({
    show: false,
    title: '',
    content: '',
    parentId: null,
  });

  const renameFolderDialog = ref<DialogState & { id: number | null }>({
    show: false,
    title: '',
    content: '',
    id: null,
  });

  const createDocumentDialog = ref<DialogState>({
    show: false,
    title: '',
    content: '',
  });

  const renameDocumentDialog = ref<DialogState & { id: number | null }>({
    show: false,
    title: '',
    content: '',
    id: null,
  });

  const showDialog = (
    dialog: DialogRef,
    title: string,
    content: string = '',
    extra: DialogExtra = {},
  ) => {
    dialog.value = {
      show: true,
      title,
      content,
      ...extra,
    };
  };

  const hideDialog = (dialog: DialogRef) => {
    dialog.value.show = false;
  };

  return {
    createFolderDialog,
    renameFolderDialog,
    createDocumentDialog,
    renameDocumentDialog,
    showDialog,
    hideDialog,
  };
}
