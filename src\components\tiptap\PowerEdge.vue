<template>
  <div class="column no-wrap relative-position" :style="`flex: 0 0 ${currentHeight}px`">
    <!-- 拖拽手柄 -->
    <div
      class="resize-handle"
      :class="{ 'bg-primary': isResizing }"
      @mousedown="startResize"
      @touchstart="startResize"
    />

    <!-- 内容区域 -->
    <div class="column no-wrap q-space">
      <div class="row no-wrap items-center">
        <q-tabs v-model="tab" dense shrink keep-alive>
          <q-tab
            v-for="config in tabConfigs"
            :key="config.name"
            :name="config.name"
            :label="config.label"
            no-caps
            @click.stop="autoToggle"
          />
        </q-tabs>
        <q-space class="full-height" @dblclick="toggle" />
        <q-btn
          dense
          size="sm"
          flat
          :icon="currentHeight >= 38 ? 'mdi-chevron-down' : 'mdi-chevron-up'"
          @click="toggle"
        />
        <slot name="right" />
      </div>

      <!-- Tab 内容区域 -->
      <q-tab-panels v-model="tab" keep-alive class="q-space no-padding">
        <q-tab-panel
          v-for="config in tabConfigs"
          :key="config.name"
          :name="config.name"
          class="q-pa-none"
        >
          <q-scroll-area class="absolute-full">
            <!-- DocumentMetadata组件不需要props，直接从docStore获取数据 -->
            <component v-if="config.name === 'metadata'" :is="config.component" />
            <!-- 其他组件需要传递props -->
            <component
              v-else
              :is="config.component"
              :doc-id="props.docId"
              :active-pane-index="props.activePaneIndex"
              :word-count="props.wordCount"
            />
          </q-scroll-area>
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, watch, type Component } from 'vue';
import { useMouse } from '@vueuse/core';
import { useI18n } from 'vue-i18n';
import DocumentMetadata from './DocumentMetadata.vue';
import TranslationTool from './TranslationTool.vue';
import KnowledgeSearch from '../KnowledgeSearch.vue';
import PexelsIntegration from '../PexelsIntegration.vue';

const { y } = useMouse();
const { t: $t } = useI18n({ useScope: 'global' });

// Props 定义
interface Props {
  docId?: number | null;
  activePaneIndex?: number;
  wordCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  docId: null,
  activePaneIndex: 0,
  wordCount: 0,
});

// Tab配置 - 定义可用的组件
interface TabConfig {
  name: string;
  label: string;
  component: Component;
  icon?: string;
}

const tabConfigs: TabConfig[] = [
  {
    name: 'metadata',
    label: $t('src.components.tiptap.documentMetadata.title'),
    component: DocumentMetadata,
    icon: 'info',
  },
  {
    name: 'translation',
    label: $t('src.components.tiptap.translationTool.title'),
    component: TranslationTool,
    icon: 'translate',
  },
  {
    name: 'KnowledgeSearch',
    label: $t('src.components.tiptap.knowledgeSearch'),
    component: KnowledgeSearch,
    icon: 'translate',
  },
  {
    name: 'PexelsIntegration',
    label: $t('src.components.tiptap.pexelsIntegration'),
    component: PexelsIntegration,
    icon: 'translate',
  },
];

// 从localStorage获取保存的tab，默认使用第一个
const getDefaultTab = () => {
  const saved = localStorage.getItem('powerEdgeActiveTab');
  if (saved && tabConfigs.some((config) => config.name === saved)) {
    return saved;
  }
  return tabConfigs.length > 0 ? tabConfigs[0].name : '';
};

const tab = ref(getDefaultTab());

// 监听tab变化，保存到localStorage
watch(tab, (newTab) => {
  localStorage.setItem('powerEdgeActiveTab', newTab);
});

// 监听当前编辑文档变化，更新PowerEdge内容
watch(
  () => props.docId,
  (newDocId) => {
    if (newDocId) {
      // 这里可以根据文档ID加载相关的附加信息
      console.log('PowerEdge: 当前编辑文档变更为', newDocId);
    }
  },
  { immediate: true },
);

// 内部高度管理 - 从localStorage加载保存的高度，默认37px
const currentHeight = ref(Number(localStorage.getItem('powerEdgeHeight')) || 37);
const heightBeforeToggle = ref(300);
const toggle = () => {
  if (currentHeight.value !== 37) {
    heightBeforeToggle.value = currentHeight.value;
  }
  currentHeight.value = currentHeight.value === 37 ? heightBeforeToggle.value : 37;
};
const autoToggle = () => {
  if (currentHeight.value === 37) {
    currentHeight.value = heightBeforeToggle.value;
  }
};

// 拖拽状态
const isResizing = ref(false);
const startY = ref(0);
const startHeight = ref(0);

// 最小高度限制
const MIN_HEIGHT = 37;

// 监听鼠标y位置变化，实时更新高度
watch(y, (newY) => {
  if (isResizing.value) {
    const deltaY = startY.value - newY; // 向上拖拽为正值
    const newHeight = Math.max(MIN_HEIGHT, startHeight.value + deltaY);
    currentHeight.value = newHeight;
  }
});

// 监听currentHeight变化，保存到localStorage
watch(
  currentHeight,
  (newHeight) => {
    localStorage.setItem('powerEdgeHeight', newHeight.toString());
  },
  { immediate: false },
);

// 开始拖拽调整大小
const startResize = (event: MouseEvent | TouchEvent) => {
  isResizing.value = true;

  // 记录初始y位置和高度
  startY.value = y.value;
  startHeight.value = currentHeight.value;

  // 添加body类来优化拖拽性能 - 使用不同的类名避免与IndexPage冲突
  document.body.classList.add('resizing-vertical');

  document.addEventListener('mouseup', stopResize);
  document.addEventListener('touchend', stopResize);
  event.preventDefault();
};

// 停止拖拽
const stopResize = () => {
  // 移除body类
  document.body.classList.remove('resizing-vertical');

  isResizing.value = false;
  document.removeEventListener('mouseup', stopResize);
  document.removeEventListener('touchend', stopResize);
};

// 组件销毁时清理
onBeforeUnmount(() => {
  if (isResizing.value) {
    stopResize();
  }
});
</script>

<style scoped>
.resize-handle {
  position: absolute;
  top: -4px;
  left: 0;
  right: 0;
  height: 2px;
  cursor: ns-resize;
  z-index: 10;
  background-color: transparent;
  transition: background-color 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.flex-1 {
  flex: 1;
}

/* 拖拽时禁用页面选择，提高性能 */
:global(body.resizing-vertical) {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  cursor: ns-resize !important;
}

:global(body.resizing-vertical *) {
  cursor: ns-resize !important;
}
</style>
