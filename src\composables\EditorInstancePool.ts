import type { Editor } from '@tiptap/vue-3';
import { ref, reactive, computed, nextTick, readonly } from 'vue';
import { useResourceCleanup } from './useResourceCleanup';
import { MemoryManager } from './MemoryManager';
import { EditorStateManager } from './EditorStateManager';

/**
 * 编辑器实例池
 * 第三阶段性能优化：高级编辑器实例管理和复用
 *
 * 核心功能：
 * 1. 实例池管理 - 预创建和复用编辑器实例
 * 2. 智能分配 - 根据性能和使用情况分配实例
 * 3. 生命周期管理 - 自动管理实例的创建、激活、休眠、销毁
 * 4. 内存优化 - 智能回收和内存管理
 * 5. 性能监控 - 监控实例池性能和使用情况
 */

// 编辑器实例状态
export type EditorInstanceStatus = 'idle' | 'active' | 'busy' | 'sleeping' | 'error' | 'destroying';

// 编辑器实例接口
export interface EditorInstance {
  id: string;
  editor: Editor | null;
  status: EditorInstanceStatus;
  createdAt: number;
  lastUsedAt: number;
  useCount: number;
  memoryUsage: number;
  renderTime: number;
  documentId?: number;
  isPrewarmed: boolean;
  priority: 'high' | 'medium' | 'low';
  metadata: {
    extensions: string[];
    features: string[];
    configuration: Record<string, unknown>;
  };
}

// 实例池配置
export interface EditorPoolConfig {
  minInstances: number; // 最小实例数
  maxInstances: number; // 最大实例数
  prewarmCount: number; // 预热实例数
  maxIdleTime: number; // 最大空闲时间 (ms)
  maxMemoryPerInstance: number; // 每个实例最大内存 (MB)
  enableAutoCleanup: boolean; // 是否启用自动清理
  cleanupInterval: number; // 清理间隔 (ms)
  enablePrewarming: boolean; // 是否启用预热
  reuseThreshold: number; // 复用阈值 (使用次数)
  performanceThreshold: number; // 性能阈值 (ms)
}

// 实例池统计
export interface PoolStats {
  totalInstances: number;
  activeInstances: number;
  idleInstances: number;
  sleepingInstances: number;
  errorInstances: number;
  averageMemoryUsage: number;
  averageRenderTime: number;
  poolUtilization: number;
  hitRate: number; // 实例复用命中率
  creationRate: number; // 实例创建速率
  destroyRate: number; // 实例销毁速率
}

// 实例创建配置
export interface InstanceCreateConfig {
  documentId?: number;
  extensions?: string[];
  features?: string[];
  priority?: 'high' | 'medium' | 'low';
  prewarm?: boolean;
  customConfig?: Record<string, unknown>;
}

// 等待队列项类型
type WaitingQueueItem = {
  resolve: (instance: EditorInstance) => void;
  reject: (error: Error) => void;
  config: InstanceCreateConfig;
};

// 默认配置
const DEFAULT_CONFIG: EditorPoolConfig = {
  minInstances: 2,
  maxInstances: 10,
  prewarmCount: 3,
  maxIdleTime: 300000, // 5分钟
  maxMemoryPerInstance: 50, // 50MB
  enableAutoCleanup: true,
  cleanupInterval: 60000, // 1分钟
  enablePrewarming: true,
  reuseThreshold: 100,
  performanceThreshold: 100, // 100ms
};

/**
 * 编辑器实例池类
 */
export class EditorInstancePool {
  private config: EditorPoolConfig;
  private instances = reactive<Map<string, EditorInstance>>(new Map());
  private waitingQueue = ref<WaitingQueueItem[]>([]);
  private isInitialized = ref(false);

  // 依赖服务
  private memoryManager: MemoryManager;
  private stateManager: EditorStateManager;
  private resourceCleanup: ReturnType<typeof useResourceCleanup>;

  // 定时器和监控
  private cleanupTimer: NodeJS.Timeout | null = null;
  private prewarmTimer: NodeJS.Timeout | null = null;
  private statsTimer: NodeJS.Timeout | null = null;

  // 统计数据
  private stats = reactive<PoolStats>({
    totalInstances: 0,
    activeInstances: 0,
    idleInstances: 0,
    sleepingInstances: 0,
    errorInstances: 0,
    averageMemoryUsage: 0,
    averageRenderTime: 0,
    poolUtilization: 0,
    hitRate: 0,
    creationRate: 0,
    destroyRate: 0,
  });

  // 性能计数器
  private counters = reactive({
    totalRequests: 0,
    cacheHits: 0,
    instancesCreated: 0,
    instancesDestroyed: 0,
    lastResetTime: Date.now(),
  });

  constructor(config: Partial<EditorPoolConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };

    // 初始化依赖服务
    this.memoryManager = new MemoryManager({
      // 移除不存在的配置项
    });

    this.stateManager = new EditorStateManager({
      enablePerformanceTracking: true,
      cacheTimeout: 60000,
    });

    this.resourceCleanup = useResourceCleanup();

    void this.initialize();
  }

  /**
   * 初始化实例池
   */
  private async initialize(): Promise<void> {
    try {
      // 注册资源清理
      this.resourceCleanup.addResource('editorInstancePool', {
        cleanup: () => this.destroy(),
        priority: 'high',
        type: 'pool',
      });

      // 启动定时器
      this.startTimers();

      // 预热实例
      if (this.config.enablePrewarming) {
        await this.prewarmInstances();
      }

      this.isInitialized.value = true;
      console.log('editor instance pool initialized');
    } catch (error) {
      console.error('editor instance pool initialization failed:', error);
      throw error;
    }
  }

  /**
   * 启动定时器
   */
  private startTimers(): void {
    // 自动清理定时器
    if (this.config.enableAutoCleanup) {
      this.cleanupTimer = setInterval(() => {
        this.performCleanup();
      }, this.config.cleanupInterval);
    }

    // 统计更新定时器
    this.statsTimer = setInterval(() => {
      this.updateStats();
    }, 5000); // 每5秒更新统计

    // 预热补充定时器
    if (this.config.enablePrewarming) {
      this.prewarmTimer = setInterval(() => {
        void this.maintainPrewarmPool();
      }, 30000); // 每30秒检查预热池
    }
  }

  /**
   * 预热实例
   */
  private async prewarmInstances(): Promise<void> {
    const prewarmPromises: Promise<void>[] = [];

    for (let i = 0; i < this.config.prewarmCount; i++) {
      prewarmPromises.push(this.createPrewarmInstance());
    }

    await Promise.all(prewarmPromises);
    console.log(`prewarmed ${this.config.prewarmCount} editor instances`);
  }

  /**
   * 创建预热实例
   */
  private async createPrewarmInstance(): Promise<void> {
    try {
      const instance = await this.createNewInstance({
        prewarm: true,
        priority: 'low',
      });

      instance.status = 'idle';
      instance.isPrewarmed = true;
    } catch (error) {
      console.error('create prewarm instance failed:', error);
    }
  }

  /**
   * 维护预热池
   */
  private async maintainPrewarmPool(): Promise<void> {
    const idleInstances = Array.from(this.instances.values()).filter(
      (instance) => instance.status === 'idle' && instance.isPrewarmed,
    );

    const needed = this.config.prewarmCount - idleInstances.length;
    if (needed > 0 && this.instances.size < this.config.maxInstances) {
      for (let i = 0; i < Math.min(needed, this.config.maxInstances - this.instances.size); i++) {
        await this.createPrewarmInstance();
      }
    }
  }

  /**
   * 获取编辑器实例
   */
  async acquireInstance(config: InstanceCreateConfig = {}): Promise<EditorInstance> {
    this.counters.totalRequests++;

    // 尝试复用现有实例
    const reusableInstance = this.findReusableInstance(config);
    if (reusableInstance) {
      return this.activateInstance(reusableInstance, config);
    }

    // 检查是否可以创建新实例
    if (this.instances.size >= this.config.maxInstances) {
      // 等待实例可用
      return new Promise((resolve, reject) => {
        this.waitingQueue.value.push({ resolve, reject, config });

        // 设置超时
        setTimeout(() => {
          const index = this.waitingQueue.value.findIndex((item) => item.resolve === resolve);
          if (index !== -1) {
            this.waitingQueue.value.splice(index, 1);
            reject(new Error('get editor instance timeout'));
          }
        }, 10000); // 10秒超时
      });
    }

    // 创建新实例
    return this.createNewInstance(config);
  }

  /**
   * 查找可复用的实例
   */
  private findReusableInstance(config: InstanceCreateConfig): EditorInstance | null {
    const candidates = Array.from(this.instances.values())
      .filter((instance) => {
        // 基本条件
        if (instance.status !== 'idle' && instance.status !== 'sleeping') return false;
        if (instance.useCount >= this.config.reuseThreshold) return false;
        if (instance.memoryUsage > this.config.maxMemoryPerInstance) return false;

        // 检查兼容性
        if (config.documentId && instance.documentId && instance.documentId !== config.documentId)
          return false;
        if (
          config.extensions &&
          !this.isExtensionsCompatible(instance.metadata.extensions, config.extensions)
        )
          return false;

        return true;
      })
      .sort((a, b) => {
        // 优先级排序：优先选择最近使用、性能好、内存少的实例
        const priorityWeight = a.priority === 'high' ? 3 : a.priority === 'medium' ? 2 : 1;
        const bPriorityWeight = b.priority === 'high' ? 3 : b.priority === 'medium' ? 2 : 1;

        const scoreA = priorityWeight * 1000 - a.lastUsedAt + a.renderTime + a.memoryUsage * 10;
        const scoreB = bPriorityWeight * 1000 - b.lastUsedAt + b.renderTime + b.memoryUsage * 10;

        return scoreA - scoreB;
      }) as EditorInstance[];

    return candidates[0] || null;
  }

  /**
   * 检查扩展兼容性
   */
  private isExtensionsCompatible(
    instanceExtensions: string[],
    requiredExtensions: string[],
  ): boolean {
    return requiredExtensions.every((ext) => instanceExtensions.includes(ext));
  }

  /**
   * 激活实例
   */
  private async activateInstance(
    instance: EditorInstance,
    config: InstanceCreateConfig,
  ): Promise<EditorInstance> {
    // 如果实例在休眠状态，先唤醒
    if (instance.status === 'sleeping') {
      await this.wakeUpInstance(instance);
    }

    instance.status = 'busy';
    instance.lastUsedAt = Date.now();
    instance.useCount++;
    instance.documentId = config.documentId;

    // 更新实例配置
    if (config.customConfig) {
      instance.metadata.configuration = {
        ...instance.metadata.configuration,
        ...config.customConfig,
      };
    }

    this.counters.cacheHits++;

    // 设置为活跃状态
    instance.status = 'active';

    return instance;
  }

  /**
   * 唤醒休眠实例
   */
  private async wakeUpInstance(instance: EditorInstance): Promise<void> {
    if (!instance.editor) {
      throw new Error('cannot wake up instance: editor not found');
    }

    // 重新激活编辑器
    try {
      await nextTick();
      instance.editor.setEditable(true);
      instance.status = 'idle';

      console.log(`instance ${instance.id} is awakened`);
    } catch (error) {
      console.error(`wake up instance ${instance.id} failed:`, error);
      instance.status = 'error';
      throw error;
    }
  }

  /**
   * 创建新实例
   */
  private async createNewInstance(config: InstanceCreateConfig): Promise<EditorInstance> {
    const startTime = performance.now();

    try {
      const id = `editor_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 创建编辑器实例的工厂函数 - 这里需要根据实际的编辑器创建逻辑来实现
      const editor = await this.createEditor(config);

      const instance: EditorInstance = {
        id,
        editor,
        status: 'idle',
        createdAt: Date.now(),
        lastUsedAt: Date.now(),
        useCount: 0,
        memoryUsage: 0,
        renderTime: performance.now() - startTime,
        documentId: config.documentId,
        isPrewarmed: config.prewarm || false,
        priority: config.priority || 'medium',
        metadata: {
          extensions: config.extensions || [],
          features: config.features || [],
          configuration: config.customConfig || {},
        },
      };

      this.instances.set(id, instance);
      this.counters.instancesCreated++;

      // 注册到状态管理器
      if (editor) {
        this.stateManager.registerEditor(id, editor);
      }

      console.log(`create new editor instance: ${id}`);

      // 处理等待队列
      this.processWaitingQueue();

      return instance;
    } catch (error) {
      console.error('create editor instance failed:', error);
      throw error;
    }
  }

  /**
   * 创建编辑器 - 这是一个抽象方法，需要根据实际情况实现
   */
  private createEditor(config: InstanceCreateConfig): Promise<Editor | null> {
    // 这里应该包含实际的编辑器创建逻辑
    // 由于这是一个通用的实例池，这里返回null，在实际使用时需要重写这个方法
    console.warn('createEditor method needs to be implemented in subclass', config);
    return Promise.resolve(null);
  }

  /**
   * 处理等待队列
   */
  private processWaitingQueue(): void {
    while (this.waitingQueue.value.length > 0 && this.instances.size < this.config.maxInstances) {
      const waiting = this.waitingQueue.value.shift();
      if (waiting) {
        void this.createNewInstance(waiting.config)
          .then((instance) => waiting.resolve(instance))
          .catch((error) => waiting.reject(error));
      }
    }
  }

  /**
   * 释放实例
   */
  releaseInstance(instanceId: string, force = false): void {
    const instance = this.instances.get(instanceId);
    if (!instance) return;

    if (force || instance.status === 'error') {
      this.destroyInstance(instanceId);
      return;
    }

    // 检查是否应该休眠或保持活跃
    const shouldSleep = this.shouldSleepInstance(instance as EditorInstance);

    if (shouldSleep) {
      this.sleepInstance(instance as EditorInstance);
    } else {
      instance.status = 'idle';
      instance.lastUsedAt = Date.now();
    }
  }

  /**
   * 判断是否应该休眠实例
   */
  private shouldSleepInstance(instance: EditorInstance): boolean {
    // 如果实例使用次数过多，休眠以减少内存占用
    if (instance.useCount > this.config.reuseThreshold * 0.8) return true;

    // 如果内存使用过高，休眠
    if (instance.memoryUsage > this.config.maxMemoryPerInstance * 0.8) return true;

    // 如果性能较差，休眠
    if (instance.renderTime > this.config.performanceThreshold) return true;

    return false;
  }

  /**
   * 休眠实例
   */
  private sleepInstance(instance: EditorInstance): void {
    try {
      if (instance.editor) {
        instance.editor.setEditable(false);
        // 可以添加更多休眠逻辑，比如暂停某些监听器
      }

      instance.status = 'sleeping';
      console.log(`instance ${instance.id} is sleeping`);
    } catch (error) {
      console.error(`sleep instance ${instance.id} failed:`, error);
    }
  }

  /**
   * 销毁实例
   */
  private destroyInstance(instanceId: string): void {
    const instance = this.instances.get(instanceId);
    if (!instance) return;

    instance.status = 'destroying';

    try {
      // 从状态管理器注销
      this.stateManager.unregisterEditor(instanceId);

      // 销毁编辑器
      if (instance.editor) {
        instance.editor.destroy();
      }

      // 从实例池移除
      this.instances.delete(instanceId);
      this.counters.instancesDestroyed++;

      console.log(`destroy editor instance: ${instanceId}`);
    } catch (error) {
      console.error(`destroy instance ${instanceId} failed:`, error);
    }
  }

  /**
   * 执行清理
   */
  private performCleanup(): void {
    const now = Date.now();
    const instancesToCleanup: string[] = [];

    for (const [id, instance] of this.instances.entries()) {
      // 清理长时间未使用的实例
      if (now - instance.lastUsedAt > this.config.maxIdleTime) {
        instancesToCleanup.push(id);
        continue;
      }

      // 清理内存使用过高的实例
      if (instance.memoryUsage > this.config.maxMemoryPerInstance) {
        instancesToCleanup.push(id);
        continue;
      }

      // 清理错误状态的实例
      if (instance.status === 'error') {
        instancesToCleanup.push(id);
        continue;
      }

      // 清理使用次数过多的实例
      if (instance.useCount > this.config.reuseThreshold) {
        instancesToCleanup.push(id);
        continue;
      }
    }

    // 执行清理，但保持最小实例数
    const canCleanup = Math.max(0, this.instances.size - this.config.minInstances);
    const toCleanup = instancesToCleanup.slice(0, canCleanup);

    toCleanup.forEach((id) => this.destroyInstance(id));

    if (toCleanup.length > 0) {
      console.log(`cleaned up ${toCleanup.length} editor instances`);
    }

    // 触发内存管理器清理
    this.memoryManager.performCleanup();
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    const instances = Array.from(this.instances.values());

    this.stats.totalInstances = instances.length;
    this.stats.activeInstances = instances.filter((i) => i.status === 'active').length;
    this.stats.idleInstances = instances.filter((i) => i.status === 'idle').length;
    this.stats.sleepingInstances = instances.filter((i) => i.status === 'sleeping').length;
    this.stats.errorInstances = instances.filter((i) => i.status === 'error').length;

    this.stats.averageMemoryUsage =
      instances.reduce((sum, i) => sum + i.memoryUsage, 0) / instances.length || 0;
    this.stats.averageRenderTime =
      instances.reduce((sum, i) => sum + i.renderTime, 0) / instances.length || 0;

    this.stats.poolUtilization = (this.stats.activeInstances / this.config.maxInstances) * 100;
    this.stats.hitRate =
      this.counters.totalRequests > 0
        ? (this.counters.cacheHits / this.counters.totalRequests) * 100
        : 0;

    // 计算速率 (每分钟)
    const timeDiff = (Date.now() - this.counters.lastResetTime) / 60000;
    if (timeDiff > 0) {
      this.stats.creationRate = this.counters.instancesCreated / timeDiff;
      this.stats.destroyRate = this.counters.instancesDestroyed / timeDiff;
    }
  }

  /**
   * 获取实例池统计
   */
  getStats(): PoolStats {
    return { ...this.stats };
  }

  /**
   * 设置最大实例数
   */
  setMaxInstances(max: number): void {
    this.updateConfig({ maxInstances: max });
  }

  /**
   * 预热单个实例
   */
  async prewarmInstance(): Promise<void> {
    const config: InstanceCreateConfig = {
      prewarm: true,
      priority: 'low',
    };
    await this.createNewInstance(config);
  }

  /**
   * 回收非活跃实例
   */
  recycleInactiveInstances(): number {
    let recycled = 0;
    const now = Date.now();
    const instances = Array.from(this.instances.values());

    for (const instance of instances) {
      if (instance.status === 'idle' && now - instance.lastUsedAt > this.config.maxIdleTime) {
        this.destroyInstance(instance.id);
        recycled++;
      }
    }

    return recycled;
  }

  /**
   * 获取所有实例信息
   */
  getAllInstances(): EditorInstance[] {
    return Array.from(this.instances.values()) as EditorInstance[];
  }

  /**
   * 获取特定状态的实例
   */
  getInstancesByStatus(status: EditorInstanceStatus): EditorInstance[] {
    return Array.from(this.instances.values()).filter(
      (instance) => instance.status === status,
    ) as EditorInstance[];
  }

  /**
   * 强制清理所有实例
   */
  async forceCleanupAll(): Promise<void> {
    const allIds = Array.from(this.instances.keys());
    allIds.forEach((id) => this.destroyInstance(id));

    // 重新预热
    if (this.config.enablePrewarming) {
      await this.prewarmInstances();
    }

    console.log('force cleaned up all editor instances');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<EditorPoolConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // 处理配置变化
    if (newConfig.maxInstances !== undefined && newConfig.maxInstances < oldConfig.maxInstances) {
      // 减少实例数量
      const excess = this.instances.size - newConfig.maxInstances;
      if (excess > 0) {
        const idleInstances = this.getInstancesByStatus('idle');
        const toRemove = idleInstances.slice(0, excess);
        toRemove.forEach((instance) => this.destroyInstance(instance.id));
      }
    }

    console.log('editor instance pool config updated', newConfig);
  }

  /**
   * 重置统计计数器
   */
  resetCounters(): void {
    this.counters.totalRequests = 0;
    this.counters.cacheHits = 0;
    this.counters.instancesCreated = 0;
    this.counters.instancesDestroyed = 0;
    this.counters.lastResetTime = Date.now();

    console.log('stats counters reset');
  }

  /**
   * 销毁实例池
   */
  destroy(): void {
    // 停止所有定时器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    if (this.prewarmTimer) {
      clearInterval(this.prewarmTimer);
      this.prewarmTimer = null;
    }

    if (this.statsTimer) {
      clearInterval(this.statsTimer);
      this.statsTimer = null;
    }

    // 销毁所有实例
    const allIds = Array.from(this.instances.keys());
    allIds.forEach((id) => this.destroyInstance(id));

    // 清理等待队列
    this.waitingQueue.value.forEach((item) => {
      item.reject(new Error('editor instance pool destroyed'));
    });
    this.waitingQueue.value = [];

    // 销毁依赖服务
    this.memoryManager.destroy();
    this.stateManager.destroy();

    this.isInitialized.value = false;

    console.log('editor instance pool destroyed');
  }
}

/**
 * 创建编辑器实例池
 */
export const createEditorInstancePool = (config?: Partial<EditorPoolConfig>) => {
  return new EditorInstancePool(config);
};

/**
 * 编辑器实例池组合式API
 */
export const useEditorInstancePool = (config?: Partial<EditorPoolConfig>) => {
  const pool = ref<EditorInstancePool | null>(null);

  const initialize = () => {
    if (!pool.value) {
      pool.value = createEditorInstancePool(config);
    }
    return pool.value;
  };

  const acquireInstance = async (config?: InstanceCreateConfig) => {
    const editorPool = initialize();
    return editorPool.acquireInstance(config);
  };

  const releaseInstance = (instanceId: string, force = false) => {
    pool.value?.releaseInstance(instanceId, force);
  };

  const getStats = () => {
    return pool.value?.getStats() || null;
  };

  const cleanup = () => {
    pool.value?.destroy();
    pool.value = null;
  };

  // 计算属性
  const totalInstances = computed(() => pool.value?.getStats().totalInstances || 0);
  const activeInstances = computed(() => pool.value?.getStats().activeInstances || 0);
  const poolUtilization = computed(() => pool.value?.getStats().poolUtilization || 0);
  const hitRate = computed(() => pool.value?.getStats().hitRate || 0);

  return {
    pool: readonly(pool),
    initialize,
    acquireInstance,
    releaseInstance,
    getStats,
    cleanup,
    totalInstances,
    activeInstances,
    poolUtilization,
    hitRate,
  };
};

export default {
  EditorInstancePool,
  createEditorInstancePool,
  useEditorInstancePool,
};
