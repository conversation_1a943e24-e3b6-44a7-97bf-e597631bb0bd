<template>
  <div class="pexels-media-browser">
    <!-- 搜索栏 -->
    <div class="search-section q-pa-md">
      <q-input
        v-model="searchQuery"
        outlined
        dense
        :placeholder="$t('src.components.PexelsMediaBrowser.search_placeholder')"
        @keyup.enter="handleSearch"
        class="search-input input-pr-xs"
      >
        <template #prepend>
          <q-icon name="mdi-magnify" />
        </template>
        <template #append>
          <q-btn
            color="primary"
            padding="xs md"
            dense
            :label="$t('src.components.PexelsMediaBrowser.search')"
            @click="handleSearch"
            :loading="loading"
          />
        </template>
      </q-input>
    </div>
    <!-- 内容区域 -->
    <div class="content-area q-pa-md">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <q-spinner-dots size="50px" color="primary" />
        <div class="q-mt-md text-center">
          {{ $t('src.components.PexelsMediaBrowser.searching') }}
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container text-center q-pa-lg">
        <q-icon name="mdi-alert-circle" size="48px" color="negative" />
        <div class="q-mt-md text-h6">
          {{ $t('src.components.PexelsMediaBrowser.search_failed') }}
        </div>
        <div class="q-mt-sm text-body2 text-grey-6">{{ error }}</div>
        <q-btn
          flat
          color="primary"
          :label="$t('src.components.PexelsMediaBrowser.retry')"
          @click="handleSearch"
          class="q-mt-md"
        />
      </div>

      <!-- 空状态 -->
      <div v-else-if="!hasResults && searchQuery" class="empty-container text-center q-pa-lg">
        <q-icon name="mdi-image-off" size="48px" color="grey-5" />
        <div class="q-mt-md text-h6">
          {{ $t('src.components.PexelsMediaBrowser.no_results') }}
        </div>
        <div class="q-mt-sm text-body2 text-grey-6">
          {{ $t('src.components.PexelsMediaBrowser.try_different_keywords') }}
        </div>
      </div>

      <!-- 媒体网格 -->
      <div v-else-if="hasResults" class="media-grid">
        <!-- 图片网格 -->
        <div v-if="mediaType === 'photos'" class="photos-grid">
          <div
            v-for="photo in photos"
            :key="photo.id"
            class="photo-item"
            draggable="true"
            @dragstart="handleDragStart($event, photo)"
            @dragend="handleDragEnd"
          >
            <img :src="photo.src.medium" :alt="photo.alt" class="photo-image" loading="lazy" />
            <div class="photo-overlay">
              <div class="photo-info">
                <div class="photographer">{{ photo.photographer }}</div>
              </div>
              <div class="photo-actions">
                <q-btn
                  flat
                  dense
                  round
                  icon="mdi-eye"
                  color="white"
                  @click.stop="previewPhoto(photo)"
                />
                <q-btn
                  flat
                  dense
                  round
                  icon="mdi-plus"
                  color="white"
                  @click.stop="selectPhoto(photo)"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 分页控制 -->
        <div class="pagination-section q-mt-lg">
          <q-pagination
            v-model="currentPage"
            :max="totalPages"
            :max-pages="6"
            direction-links
            boundary-links
            @update:model-value="handlePageChange"
            color="primary"
            class="justify-center"
          />
          <div class="pagination-info text-center q-mt-sm text-body2 text-grey-6">
            {{
              $t('src.components.PexelsMediaBrowser.pagination_info', {
                start: (currentPage - 1) * perPage + 1,
                end: Math.min(currentPage * perPage, totalResults),
                total: totalResults,
              })
            }}
          </div>
        </div>
      </div>

      <!-- 初始状态 -->
      <div v-else class="initial-state text-center q-pa-lg">
        <q-icon name="mdi-image-search" size="64px" color="grey-5" />
        <div class="q-mt-md text-h6">
          {{ $t('src.components.PexelsMediaBrowser.search_high_quality_media') }}
        </div>
        <div class="q-mt-sm text-body2 text-grey-6">
          {{ $t('src.components.PexelsMediaBrowser.input_keywords_search_pexels') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useUiStore } from 'src/stores/ui';
import { Notify } from 'quasar';
import type { PexelsVideo } from 'src/env.d';
import type { PexelsPhoto } from 'src/llm/tools/pexels';
import { previewImages } from 'hevue-img-preview/v3';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

// 事件定义
const emit = defineEmits<{
  'media-selected': [media: PexelsPhoto | PexelsVideo, type: 'photo' | 'video'];
}>();

// 响应式数据
const uiStore = useUiStore();
const searchQuery = ref('');
const mediaType = ref<'photos' | 'videos'>('photos');
const loading = ref(false);
const error = ref('');

// 媒体数据
const photos = ref<PexelsPhoto[]>([]);
const videos = ref<PexelsVideo[]>([]);

// 分页数据
const currentPage = ref(1);
const perPage = 12;
const totalResults = ref(0);
const totalPages = computed(() => Math.ceil(totalResults.value / perPage));

// 计算属性
const hasResults = computed(() => {
  return mediaType.value === 'photos' ? photos.value.length > 0 : videos.value.length > 0;
});

// API 调用函数
const searchPhotos = async (query: string, page: number = 1) => {
  const defaultProvider = uiStore.defaultResourceProvider;
  if (!defaultProvider || defaultProvider.key !== 'pexels') {
    throw new Error(
      $t('src.components.PexelsMediaBrowser.pexels_resource_provider_not_configured'),
    );
  }

  const config = defaultProvider.config as { apiKey: string; baseUrl: string };
  const { apiKey, baseUrl } = config;

  if (!apiKey || !baseUrl) {
    throw new Error($t('src.components.PexelsMediaBrowser.pexels_api_config_incomplete'));
  }

  const response = await fetch(
    `${baseUrl}/v1/search?query=${encodeURIComponent(query)}&per_page=${perPage}&page=${page}`,
    {
      headers: {
        Authorization: apiKey,
      },
    },
  );

  if (!response.ok) {
    throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

const searchVideos = async (query: string, page: number = 1) => {
  const defaultProvider = uiStore.defaultResourceProvider;
  if (!defaultProvider || defaultProvider.key !== 'pexels') {
    throw new Error(
      $t('src.components.PexelsMediaBrowser.pexels_resource_provider_not_configured'),
    );
  }

  const config = defaultProvider.config as { apiKey: string; baseUrl: string };
  const { apiKey, baseUrl } = config;

  if (!apiKey || !baseUrl) {
    throw new Error($t('src.components.PexelsMediaBrowser.pexels_api_config_incomplete'));
  }

  const response = await fetch(
    `${baseUrl}/videos/search?query=${encodeURIComponent(query)}&per_page=${perPage}&page=${page}`,
    {
      headers: {
        Authorization: apiKey,
      },
    },
  );

  if (!response.ok) {
    throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

// 搜索处理
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    Notify.create({
      type: 'warning',
      message: $t('src.components.PexelsMediaBrowser.please_input_search_keywords'),
    });
    return;
  }

  loading.value = true;
  error.value = '';
  currentPage.value = 1;

  try {
    if (mediaType.value === 'photos') {
      const data = await searchPhotos(searchQuery.value, currentPage.value);
      photos.value = data.photos || [];
      totalResults.value = data.total_results || 0;
    } else {
      const data = await searchVideos(searchQuery.value, currentPage.value);
      videos.value = data.videos || [];
      totalResults.value = data.total_results || 0;
    }
  } catch (err) {
    error.value =
      err instanceof Error ? err.message : $t('src.components.PexelsMediaBrowser.search_failed');
    console.error('Pexels search error:', err);
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handlePageChange = async (page: number) => {
  if (!searchQuery.value.trim()) return;

  loading.value = true;
  error.value = '';

  try {
    if (mediaType.value === 'photos') {
      const data = await searchPhotos(searchQuery.value, page);
      photos.value = data.photos || [];
    } else {
      const data = await searchVideos(searchQuery.value, page);
      videos.value = data.videos || [];
    }
  } catch (err) {
    error.value =
      err instanceof Error ? err.message : $t('src.components.PexelsMediaBrowser.load_failed');
    console.error('Pexels pagination error:', err);
  } finally {
    loading.value = false;
  }
};

// 拖拽功能
const handleDragStart = (event: DragEvent, photo: PexelsPhoto) => {
  if (event.dataTransfer) {
    // 设置拖拽数据，使用 landscape 尺寸的图片URL
    event.dataTransfer.setData('text/uri-list', photo.src.landscape);
    event.dataTransfer.setData('text/plain', photo.src.landscape);

    // 设置拖拽效果
    event.dataTransfer.effectAllowed = 'copy';

    // 可选：设置拖拽时的预览图片
    const dragImage = new Image();
    dragImage.src = photo.src.small;
    dragImage.onload = () => {
      event.dataTransfer?.setDragImage(dragImage, 50, 50);
    };

    // console.log('🖼️ [Drag Start] 开始拖拽图片:', photo.src.landscape);
  }
};

const handleDragEnd = () => {
  // console.log('🖼️ [Drag End] 拖拽结束');
  // 可以在这里添加拖拽结束后的清理逻辑
};

// 选择功能
const selectPhoto = (photo: PexelsPhoto) => {
  emit('media-selected', photo, 'photo');
};

// 预览功能
const previewPhoto = (photo: PexelsPhoto) => {
  // const images = photos.value.map((p) => ({
  //   src: p.src.large,
  //   title: `Photo by ${p.photographer}`,
  // }));

  // const currentIndex = photos.value.findIndex((p) => p.id === photo.id);
  // console.log(photo);

  previewImages(photo.src.landscape);
};

// 生命周期
onMounted(() => {
  // 组件挂载时的初始化逻辑
});
</script>

<style scoped>
.pexels-media-browser {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-section {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.search-input {
  max-width: 600px;
  margin: 0 auto;
}

.media-type-tabs {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.content-area {
  flex: 1;
  overflow-y: auto;
}

.loading-container,
.error-container,
.empty-container,
.initial-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

/* 媒体网格样式 */
.photos-grid,
.videos-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

@media (max-width: 1400px) {
  .photos-grid,
  .videos-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1024px) {
  .photos-grid,
  .videos-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .photos-grid,
  .videos-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .photos-grid,
  .videos-grid {
    grid-template-columns: 1fr;
  }
}

/* 图片项样式 */
.photo-item,
.video-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

/* 拖拽样式 */
.photo-item[draggable='true'] {
  cursor: grab;
}

.photo-item[draggable='true']:active {
  cursor: grabbing;
}

.photo-item:hover,
.video-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.photo-item:hover {
  cursor: grab;
}

.photo-image,
.video-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.photo-item:hover .photo-image,
.video-item:hover .video-image {
  transform: scale(1.05);
}

/* 覆盖层样式 */
.photo-overlay,
.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.photo-item:hover .photo-overlay,
.video-item:hover .video-overlay {
  opacity: 1;
}

.photo-info,
.video-info {
  color: white;
}

.photographer,
.video-author {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.video-duration {
  font-size: 11px;
  opacity: 0.8;
}

.photo-actions,
.video-actions {
  display: flex;
  justify-content: flex-end;
}

/* 视频播放按钮 */
.video-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.video-item:hover .video-play-overlay {
  opacity: 1;
}

/* 视频预览对话框 */
.video-preview-card {
  width: 100%;
  height: 100%;
}

.video-preview-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.video-player-container {
  width: 100%;
  max-width: 1200px;
  aspect-ratio: 16/9;
}

/* 分页样式 */
.pagination-section {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  padding-top: 24px;
}

.pagination-info {
  margin-top: 8px;
}

/* 深色主题适配 */
.body--dark .search-section,
.body--dark .media-type-tabs,
.body--dark .pagination-section {
  border-color: rgba(255, 255, 255, 0.12);
}
</style>
