/**
 * 窗口拖拽工具函数
 * 提供选择性窗口拖拽功能，只有带有特定类名的元素才能拖拽窗口
 */

/**
 * 检查元素是否具有拖拽指示器类或者是拖拽指示器的子元素
 * @param element 要检查的元素
 * @param dragClass 拖拽指示器类名，默认为 'drag-indicator'
 * @returns 是否可以拖拽
 */
export function isDragIndicator(element: Element | null, dragClass = 'drag-indicator'): boolean {
  if (!element) return false;
  
  // 检查当前元素
  if (element.classList.contains(dragClass)) {
    return true;
  }
  
  // 检查父元素（向上遍历DOM树）
  let parent = element.parentElement;
  while (parent) {
    if (parent.classList.contains(dragClass)) {
      return true;
    }
    parent = parent.parentElement;
  }
  
  return false;
}

/**
 * 处理选择性窗口拖拽的鼠标按下事件
 * @param event 鼠标事件
 * @param dragClass 拖拽指示器类名，默认为 'drag-indicator'
 * @returns 是否处理了拖拽
 */
export function handleSelectiveWindowDrag(event: MouseEvent, dragClass = 'drag-indicator'): boolean {
  // 只在左键按下时处理
  if (event.button !== 0) return false;

  // 检查点击的元素是否是拖拽指示器
  const target = event.target as Element;
  if (!isDragIndicator(target, dragClass)) {
    // 如果不是拖拽指示器，不启动窗口拖拽
    return false;
  }

  // 阻止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();

  // 调用 Qt 的原生窗口拖拽功能
  if (window.qtWindow) {
    window.qtWindow.startWindowDrag(event.clientX, event.clientY);
    return true;
  }

  return false;
}

/**
 * 处理选择性窗口双击事件（最大化/还原）
 * @param event 鼠标事件
 * @param dragClass 拖拽指示器类名，默认为 'drag-indicator'
 * @returns 是否处理了双击
 */
export function handleSelectiveWindowDoubleClick(event: MouseEvent, dragClass = 'drag-indicator'): boolean {
  // 检查双击的元素是否是拖拽指示器
  const target = event.target as Element;
  if (!isDragIndicator(target, dragClass)) {
    // 如果不是拖拽指示器，不处理双击
    return false;
  }

  // 双击最大化/还原窗口
  if (window.qtWindow) {
    window.qtWindow.toggleMaximizeWindow();
    return true;
  }

  return false;
}

/**
 * 为元素添加选择性窗口拖拽功能
 * @param element 要添加拖拽功能的元素
 * @param dragClass 拖拽指示器类名，默认为 'drag-indicator'
 * @returns 清理函数
 */
export function addSelectiveWindowDrag(element: HTMLElement, dragClass = 'drag-indicator'): () => void {
  const handleMouseDown = (event: MouseEvent) => {
    handleSelectiveWindowDrag(event, dragClass);
  };

  const handleDoubleClick = (event: MouseEvent) => {
    handleSelectiveWindowDoubleClick(event, dragClass);
  };

  element.addEventListener('mousedown', handleMouseDown);
  element.addEventListener('dblclick', handleDoubleClick);

  // 返回清理函数
  return () => {
    element.removeEventListener('mousedown', handleMouseDown);
    element.removeEventListener('dblclick', handleDoubleClick);
  };
}

/**
 * Vue 组合式函数：为组件添加选择性窗口拖拽功能
 * @param dragClass 拖拽指示器类名，默认为 'drag-indicator'
 */
export function useSelectiveWindowDrag(dragClass = 'drag-indicator') {
  const handleMouseDown = (event: MouseEvent) => {
    return handleSelectiveWindowDrag(event, dragClass);
  };

  const handleDoubleClick = (event: MouseEvent) => {
    return handleSelectiveWindowDoubleClick(event, dragClass);
  };

  return {
    handleMouseDown,
    handleDoubleClick,
    isDragIndicator: (element: Element | null) => isDragIndicator(element, dragClass),
  };
}
