<template>
  <div class="q-pa-md">
    <h4>ChunkingStrategySelector 测试</h4>
    
    <div class="q-mb-md">
      <ChunkingStrategySelector
        v-model="chunkingConfig"
        @validation-change="onValidationChange"
      />
    </div>
    
    <div class="q-mt-md">
      <h5>当前配置：</h5>
      <pre>{{ JSON.stringify(chunkingConfig, null, 2) }}</pre>
    </div>
    
    <div class="q-mt-md">
      <h5>验证状态：</h5>
      <p>{{ validationStatus ? '配置有效' : '配置无效' }}</p>
    </div>
    
    <div class="q-mt-md">
      <q-btn 
        color="primary" 
        label="测试切割策略" 
        @click="testStrategy"
        :disable="!validationStatus"
      />
    </div>
    
    <div v-if="testResult" class="q-mt-md">
      <h5>测试结果：</h5>
      <pre>{{ testResult }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ChunkingStrategySelector from './ChunkingStrategySelector.vue';
import { splitWithStrategy } from '../utils/knowledgeBase';

// 响应式数据
const chunkingConfig = ref({
  method: 'markdown' as const,
  config: {
    chunkSize: 800,
    chunkOverlap: 200,
  },
});

const validationStatus = ref(true);
const testResult = ref<string | null>(null);

// 方法
const onValidationChange = (isValid: boolean) => {
  validationStatus.value = isValid;
  console.log('验证状态变化:', isValid);
};

const testStrategy = async () => {
  try {
    const testContent = `# 测试文档

这是一个用于测试切割策略的示例文档。

## 第一章节

这里是第一章节的内容。包含了一些基本的文本内容，用于测试切割器的工作效果。

### 子章节 1.1

这是一个子章节，包含更详细的内容。我们需要确保切割器能够正确处理不同层级的标题。

## 第二章节

这里是第二章节的内容。

### 代码示例

\`\`\`typescript
function example() {
  console.log('这是一个代码示例');
  return 'success';
}
\`\`\`

## 结论

这是文档的结论部分。切割器应该能够正确处理这些内容，并在合适的位置进行分割。`;

    console.log('开始测试切割策略:', chunkingConfig.value);
    
    const result = await splitWithStrategy(
      chunkingConfig.value.method,
      testContent,
      chunkingConfig.value.config,
      true // 启用日志
    );
    
    testResult.value = JSON.stringify({
      策略: chunkingConfig.value.method,
      配置: chunkingConfig.value.config,
      原始长度: result.originalLength,
      切割块数: result.chunkCount,
      统计信息: result.summary,
      切割结果: result.chunks.map((chunk, index) => ({
        索引: index + 1,
        长度: chunk.pageContent.length,
        内容预览: chunk.pageContent.substring(0, 100) + '...',
      })),
    }, null, 2);
    
  } catch (error) {
    console.error('测试失败:', error);
    testResult.value = `测试失败: ${error instanceof Error ? error.message : '未知错误'}`;
  }
};
</script>
