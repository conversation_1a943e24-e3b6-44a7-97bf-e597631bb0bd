<template>
  <q-list class="column gap-xs q-pa-sm">
    <q-item
      v-for="key in Object.keys(items)"
      :key="key"
      clickable
      v-ripple
      class="radius-xs"
      :active="uiStore.settingfor === key"
      active-class="bg-primary text-white"
      @click="handleSettingClick(key)"
    >
      <q-item-section avatar>
        <q-icon
          :color="uiStore.settingfor === key ? 'white' : $q.dark.isActive ? 'grey-4' : 'grey-9'"
          :name="getIcon(key)"
        />
      </q-item-section>
      <q-item-section>{{ $t(`src.components.settings.SettingPannel.${key}`) }}</q-item-section>
    </q-item>
  </q-list>
</template>
<script setup lang="ts">
import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useUiStore } from 'src/stores/ui';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';

const { t: $t } = useI18n();
const uiStore = useUiStore();
const $q = useQuasar();

const route = useRoute();
const settingfor = computed(() => route.query?.settingfor);
const items = computed(() => {
  const _perferences = { ...uiStore.perferences };
  delete _perferences.provider;
  return _perferences;
});

const handleSettingClick = (key: string) => {
  console.log('Setting clicked:', key);
  uiStore.settingfor = key;
};

watch(settingfor, () => {
  if (settingfor.value) {
    uiStore.settingfor = Array.isArray(settingfor.value) ? settingfor.value[0] : settingfor.value;
  }
});

const getIcon = (key: string) => {
  if (key === 'llm') {
    return 'mdi-robot';
  } else if (key === 'autoComplete') {
    return 'mdi-auto-fix';
  } else if (key === 'editor') {
    return 'history_edu';
  } else if (key === 'base') {
    return 'settings';
  } else if (key === 'searchEngine') {
    return 'mdi-magnify';
  } else if (key === 'resourceProvider') {
    return 'mdi-image-multiple';
  } else if (key === 'knowledgeBase') {
    return 'mdi-database';
  } else if (key === 'floatAgent') {
    return 'mdi-robot-outline';
  }
  return 'mdi-pencil';
};
</script>
