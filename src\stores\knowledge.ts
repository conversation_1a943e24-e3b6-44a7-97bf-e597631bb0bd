import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { Notify } from 'quasar';
import type { KnowledgeBase, KnowledgeSearchResult, KnowledgeDocument } from '../env';

interface RawKnowledgeBase {
  id: string | number;
  name: string;
  description: string;
  user_id: string;
  settings?: string;
  document_count?: number;
  created_at: number | string;
  updated_at: number | string;
}

interface CreateKBResult {
  id: string | number;
  name: string;
  description: string;
  user_id?: string;
  settings?: string;
  created_at?: number | string;
  updated_at?: number | string;
}

// 知识库上下文接口
interface KnowledgeContext {
  knowledgeBase: KnowledgeBase | null;
  searchResults: KnowledgeSearchResult[];
  lastQuery: string;
}

// Helper function to call Qt KnowledgeApi
const callKnowledgeApi = <T>(method: string, ...args: unknown[]): Promise<T> => {
  return new Promise((resolve, reject) => {
    if (!window.knowledgeApi || typeof window.knowledgeApi[method] !== 'function') {
      reject(new Error(`KnowledgeApi method ${method} not available`));
      return;
    }

    try {
      const result = window.knowledgeApi[method](...args);

      // Qt WebChannel 方法可能返回 Promise，也可能直接返回值
      const handleResult = (data: unknown) => {
        if (typeof data === 'string') {
          try {
            const parsed = JSON.parse(data);
            if (parsed.success) {
              // 对于getAllKnowledgeBases，返回完整的parsed对象
              // 对于其他方法，如果有data字段则返回data，否则返回整个对象
              if (method === 'getAllKnowledgeBases') {
                resolve(parsed);
              } else {
                resolve(parsed.data ? parsed.data : parsed);
              }
            } else {
              reject(new Error(parsed.message || `${method} failed`));
            }
          } catch (parseError) {
            console.error(
              `❌ [callKnowledgeApi] ${method} JSON解析失败:`,
              parseError,
              'result:',
              data,
            );
            reject(new Error(`JSON parse error: ${parseError}`));
          }
        } else {
          resolve(data as T);
        }
      };

      // 检查是否是 Promise
      if (result && typeof result.then === 'function') {
        result
          .then((data: unknown) => {
            handleResult(data);
          })
          .catch((error: Error) => {
            console.error(`❌ [callKnowledgeApi] ${method} Promise异常:`, error);
            reject(error);
          });
      } else {
        // 直接返回值
        handleResult(result);
      }
    } catch (error) {
      reject(error instanceof Error ? error : new Error(String(error)));
    }
  });
};

// 数据转换函数
const transformKnowledgeBase = (raw: RawKnowledgeBase): KnowledgeBase => {
  try {
    // Qt端返回的ID是字符串，需要转换为数字
    let id: number;
    if (typeof raw.id === 'string') {
      id = parseInt(raw.id, 10);
      if (isNaN(id)) {
        throw new Error(`无法将ID "${raw.id}" 转换为数字`);
      }
    } else if (typeof raw.id === 'number') {
      id = raw.id;
    } else {
      throw new Error(`ID字段类型无效: ${typeof raw.id}, 值: ${String(raw.id)}`);
    }

    // Qt端返回的时间戳是数字(毫秒)，需要转换为ISO字符串
    let created_at: string;
    if (typeof raw.created_at === 'number') {
      created_at = new Date(raw.created_at).toISOString();
    } else if (typeof raw.created_at === 'string') {
      // 如果已经是字符串，尝试解析并重新格式化
      const timestamp = parseInt(raw.created_at, 10);
      if (!isNaN(timestamp)) {
        created_at = new Date(timestamp).toISOString();
      } else {
        created_at = raw.created_at;
      }
    } else {
      console.warn('created_at字段类型异常:', typeof raw.created_at, raw.created_at);
      created_at = new Date().toISOString();
    }

    let updated_at: string;
    if (typeof raw.updated_at === 'number') {
      updated_at = new Date(raw.updated_at).toISOString();
    } else if (typeof raw.updated_at === 'string') {
      // 如果已经是字符串，尝试解析并重新格式化
      const timestamp = parseInt(raw.updated_at, 10);
      if (!isNaN(timestamp)) {
        updated_at = new Date(timestamp).toISOString();
      } else {
        updated_at = raw.updated_at;
      }
    } else {
      console.warn('updated_at字段类型异常:', typeof raw.updated_at, raw.updated_at);
      updated_at = created_at;
    }

    // 构建转换后的知识库对象
    const transformed: KnowledgeBase = {
      id,
      name: String(raw.name || ''),
      description: String(raw.description || ''),
      user_id: String(raw.user_id || 'default_user'),
      // Qt端当前不返回settings字段，使用默认值
      settings: String(raw.settings || '{}'),
      // Qt端返回document_count，如果没有则默认为0
      document_count: Number(raw.document_count) || 0,
      created_at,
      updated_at,
    };

    return transformed;
  } catch (transformError) {
    console.error('❌ [transformKnowledgeBase] 转换失败:', transformError, '原始数据:', raw);
    throw transformError;
  }
};

export const useKnowledgeStore = defineStore('knowledge', () => {
  // 状态
  const knowledgeBases = ref<KnowledgeBase[]>([]);
  const currentKnowledgeBase = ref<KnowledgeBase | null>(null);
  const knowledgeDocuments = ref<KnowledgeDocument[]>([]);
  const searchResults = ref<KnowledgeSearchResult[]>([]);
  const isLoading = ref(false);
  const isProcessing = ref(false);
  const error = ref<Error | null>(null);

  // 标志位：记录需要刷新文档列表的知识库ID（用于keep-alive缓存场景）
  const knowledgeBasesNeedRefresh = ref<Set<number>>(new Set());

  // 知识库上下文状态
  const knowledgeContext = ref<KnowledgeContext>({
    knowledgeBase: null,
    searchResults: [],
    lastQuery: '',
  });

  // 计算属性
  const hasKnowledgeBases = computed(() => knowledgeBases.value.length > 0);
  const hasError = computed(() => !!error.value);
  const totalDocuments = computed(() => {
    return knowledgeBases.value.reduce((total, kb) => total + kb.document_count, 0);
  });

  // 基础操作方法
  const loadKnowledgeBases = async (): Promise<KnowledgeBase[]> => {
    isLoading.value = true;
    error.value = null;

    try {
      // 直接调用Qt API，callKnowledgeApi已经处理了JSON解析
      const result = await callKnowledgeApi<{
        success: boolean;
        knowledge_bases: RawKnowledgeBase[];
        message: string;
      }>('getAllKnowledgeBases');
      let rawBases: RawKnowledgeBase[] = [];

      if (result.knowledge_bases && Array.isArray(result.knowledge_bases)) {
        rawBases = result.knowledge_bases;
      }

      // 转换数据格式
      const bases: KnowledgeBase[] = [];
      for (let i = 0; i < rawBases.length; i++) {
        try {
          const transformed = transformKnowledgeBase(rawBases[i]);
          bases.push(transformed);
        } catch (transformError) {
          console.error(
            `❌ [KnowledgeStore] 第${i + 1}个知识库转换失败:`,
            transformError,
            '原始数据:',
            rawBases[i],
          );
        }
      }

      // 更新状态
      knowledgeBases.value = bases;
      return bases;
    } catch (err) {
      console.error('❌ [KnowledgeStore] 加载知识库失败:', err);
      error.value = err instanceof Error ? err : new Error('加载知识库失败');
      return [];
    } finally {
      isLoading.value = false;
    }
  };

  const createKnowledgeBase = async (
    name: string,
    description: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _settings: Record<string, unknown> = {},
  ): Promise<number> => {
    isProcessing.value = true;
    error.value = null;

    try {
      const result = await callKnowledgeApi<CreateKBResult>(
        'createKnowledgeBase',
        name,
        description,
        'default_user',
      );
      const id = typeof result.id === 'string' ? parseInt(result.id, 10) : result.id;

      // 重新加载列表以获取最新数据
      await loadKnowledgeBases();

      Notify.create({
        type: 'positive',
        message: `知识库 "${name}" 创建成功`,
        timeout: 2000,
      });

      return id;
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error('创建知识库失败');
      error.value = errorObj;
      console.error('❌ [KnowledgeStore] 创建知识库失败:', errorObj);
      throw errorObj;
    } finally {
      isProcessing.value = false;
    }
  };

  const updateKnowledgeBase = async (
    id: number,
    name: string,
    description: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _settings: Record<string, unknown> = {},
  ): Promise<void> => {
    console.log('🔄 [KnowledgeStore] 开始更新知识库:', { id, name, description });
    isProcessing.value = true;
    error.value = null;

    try {
      await callKnowledgeApi('updateKnowledgeBase', id.toString(), name, description);

      // 重新加载列表
      await loadKnowledgeBases();

      Notify.create({
        type: 'positive',
        message: `知识库 "${name}" 更新成功`,
        timeout: 2000,
      });

      console.log('✅ [KnowledgeStore] 知识库更新完成');
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error('更新知识库失败');
      error.value = errorObj;
      console.error('❌ [KnowledgeStore] 更新知识库失败:', errorObj);
      throw errorObj;
    } finally {
      isProcessing.value = false;
    }
  };

  const deleteKnowledgeBase = async (id: number): Promise<void> => {
    console.log('🔄 [KnowledgeStore] 开始删除知识库:', id);
    isProcessing.value = true;
    error.value = null;

    try {
      // 第一步：删除知识库（ObjectBox）
      await callKnowledgeApi('deleteKnowledgeBase', id.toString());
      console.log('✅ [KnowledgeStore] 知识库删除成功');

      // 第二步：清除所有相关的文档关联关系
      try {
        const { useSqlite } = await import('../composeables/useSqlite');
        const sqlite = useSqlite();

        // 调用后端API清除知识库的所有文档关联
        const clearResult = await sqlite.clearKnowledgeBaseAssociations(id);

        if (clearResult.success) {
          console.log('✅ [KnowledgeStore] 知识库文档关联清除成功:', {
            clearedCount: clearResult.cleared_count,
            message: clearResult.message,
          });
        } else {
          console.error('❌ [KnowledgeStore] 清除知识库文档关联失败:', clearResult.message);
        }
      } catch (clearError) {
        console.error('❌ [KnowledgeStore] 清除知识库关联失败:', clearError);
        // 不阻断主流程，只记录错误
      }

      // 从本地状态移除
      knowledgeBases.value = knowledgeBases.value.filter((kb) => kb.id !== id);

      // 如果删除的是当前知识库，清空当前选择
      if (currentKnowledgeBase.value?.id === id) {
        currentKnowledgeBase.value = null;
      }

      Notify.create({
        type: 'positive',
        message: '知识库删除成功',
        timeout: 2000,
      });

      console.log('✅ [KnowledgeStore] 知识库删除完成');
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error('删除知识库失败');
      error.value = errorObj;
      console.error('❌ [KnowledgeStore] 删除知识库失败:', errorObj);
      throw errorObj;
    } finally {
      isProcessing.value = false;
    }
  };

  const setCurrentKnowledgeBase = async (id: number | null): Promise<KnowledgeBase | null> => {
    if (!id) {
      currentKnowledgeBase.value = null;
      knowledgeDocuments.value = []; // 清空文档列表
      return null;
    }

    console.log('🔄 [KnowledgeStore] 设置当前知识库:', id);
    isLoading.value = true;
    error.value = null;

    try {
      // 先清空之前的文档列表
      knowledgeDocuments.value = [];

      interface RawKnowledgeBaseDetail {
        id: string | number;
        name: string;
        description: string;
        user_id: string;
        settings?: string;
        document_count?: number;
        created_at: number | string;
        updated_at: number | string;
      }

      const result = await callKnowledgeApi<RawKnowledgeBaseDetail>(
        'getKnowledgeBase',
        id.toString(),
      );

      const kb = transformKnowledgeBase(result);
      currentKnowledgeBase.value = kb;

      // 设置当前知识库后，自动加载对应的文档
      await loadKnowledgeDocuments(id);

      console.log('✅ [KnowledgeStore] 当前知识库设置完成:', kb);
      return kb;
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error('加载知识库详情失败');
      error.value = errorObj;
      console.error('❌ [KnowledgeStore] 设置当前知识库失败:', errorObj);
      throw errorObj;
    } finally {
      isLoading.value = false;
    }
  };

  // 文档管理方法
  const loadKnowledgeDocuments = async (knowledgeBaseId: number): Promise<KnowledgeDocument[]> => {
    console.log('🔄 [KnowledgeStore] 开始加载知识库文档:', knowledgeBaseId);
    isLoading.value = true;
    error.value = null;

    try {
      // 先清空现有的文档列表，避免不同知识库间数据混淆
      knowledgeDocuments.value = [];

      const result = await callKnowledgeApi<
        { documents?: KnowledgeDocument[] } | KnowledgeDocument[]
      >('getDocumentsByKnowledgeBase', knowledgeBaseId.toString());

      // 处理不同的返回格式
      let docs: KnowledgeDocument[] = [];
      if (Array.isArray(result)) {
        docs = result;
      } else if (result && typeof result === 'object' && 'documents' in result) {
        docs = result.documents || [];
      }

      knowledgeDocuments.value = docs;
      console.log('📄 [KnowledgeStore] 加载知识库文档完成:', docs.length, '个');
      return docs;
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error('加载知识库文档失败');
      error.value = errorObj;
      console.error('❌ [KnowledgeStore] 加载知识库文档失败:', errorObj);
      // 发生错误时也清空文档列表
      knowledgeDocuments.value = [];
      throw errorObj;
    } finally {
      isLoading.value = false;
    }
  };

  // 知识库上下文管理
  const setKnowledgeContext = (
    knowledgeBase: KnowledgeBase | null,
    searchResults: KnowledgeSearchResult[] = [],
    query: string = '',
  ) => {
    knowledgeContext.value = {
      knowledgeBase,
      searchResults,
      lastQuery: query,
    };
  };

  const getKnowledgeContext = () => {
    return knowledgeContext.value;
  };

  const clearKnowledgeContext = () => {
    knowledgeContext.value = {
      knowledgeBase: null,
      searchResults: [],
      lastQuery: '',
    };
  };

  const testConnection = async (): Promise<void> => {
    console.log('🔍 [KnowledgeStore] 测试连接...');

    try {
      const result = await callKnowledgeApi<{
        success: boolean;
        details?: string;
        message?: string;
      }>('testConnection');

      if (result && result.success) {
        Notify.create({
          type: 'positive',
          message: `连接成功: ${result.details || 'ObjectBox正常工作'}`,
          timeout: 3000,
        });

        // 连接成功后自动加载知识库列表
        await loadKnowledgeBases();
      } else {
        throw new Error(result?.message || '连接失败');
      }
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error('测试连接失败');
      error.value = errorObj;

      Notify.create({
        type: 'negative',
        message: errorObj.message,
        timeout: 5000,
      });

      console.error('❌ [KnowledgeStore] 测试连接失败:', errorObj);
    }
  };

  const clearError = () => {
    error.value = null;
  };

  const refreshKnowledgeBases = async () => {
    console.log('🔄 [KnowledgeStore] 手动刷新知识库列表');
    await loadKnowledgeBases();
  };

  // 刷新标志位管理
  const markKnowledgeBaseNeedsRefresh = (knowledgeBaseId: number) => {
    console.log('🔄 [KnowledgeStore] 标记知识库需要刷新文档列表:', knowledgeBaseId);
    knowledgeBasesNeedRefresh.value.add(knowledgeBaseId);
  };

  const isKnowledgeBaseNeedsRefresh = (knowledgeBaseId: number) => {
    return knowledgeBasesNeedRefresh.value.has(knowledgeBaseId);
  };

  const clearKnowledgeBaseRefreshFlag = (knowledgeBaseId: number) => {
    console.log('🔄 [KnowledgeStore] 清除知识库刷新标志:', knowledgeBaseId);
    knowledgeBasesNeedRefresh.value.delete(knowledgeBaseId);
  };

  const getAllKnowledgeBasesNeedRefresh = () => {
    return Array.from(knowledgeBasesNeedRefresh.value);
  };

  return {
    // 状态
    knowledgeBases,
    currentKnowledgeBase,
    knowledgeDocuments,
    searchResults,
    isLoading,
    isProcessing,
    error,
    knowledgeContext,
    knowledgeBasesNeedRefresh,

    // 计算属性
    hasKnowledgeBases,
    hasError,
    totalDocuments,

    // 知识库操作方法
    loadKnowledgeBases,
    createKnowledgeBase,
    updateKnowledgeBase,
    deleteKnowledgeBase,
    setCurrentKnowledgeBase,
    testConnection,
    clearError,
    refreshKnowledgeBases,

    // 文档管理方法
    loadKnowledgeDocuments,

    // 知识库上下文管理
    setKnowledgeContext,
    getKnowledgeContext,
    clearKnowledgeContext,

    // 刷新标志位管理
    markKnowledgeBaseNeedsRefresh,
    isKnowledgeBaseNeedsRefresh,
    clearKnowledgeBaseRefreshFlag,
    getAllKnowledgeBasesNeedRefresh,
  };
});
