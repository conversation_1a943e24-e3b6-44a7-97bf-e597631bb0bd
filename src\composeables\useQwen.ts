import type { Message, AssistantMessage as AssistantMessageType, ToolCall } from 'src/types/qwen';
import { computed, type Ref } from 'vue';
import { useSqlite } from 'src/composeables/useSqlite';
import { useUiStore } from 'src/stores/ui';
import { DEFAULT_QWEN_SETTINGS } from 'src/config/defaultSettings';
import { executeToolCall } from 'src/llm/tools/index';
import { PromptService, type ConversationRole } from 'src/services/promptService';
import type { SimplifiedLlmParams } from 'src/services/messagePreprocessor';
import { $t } from 'src/composables/useTrans';
import { llmStore } from './useStore';

export const useQwen = () => {
  // 在函数内部获取组合式函数
  const { updateConversation } = useSqlite();
  const uiStore = useUiStore();

  // 使用 computed 来创建响应式的 qwenSettings
  const qwenSettings = computed(() => {
    return uiStore.perferences?.llm?.qwen || DEFAULT_QWEN_SETTINGS;
  });
  // readSettings 方法现在不再需要，因为设置是响应式的
  // 保留此方法以保持向后兼容性，但实际上不执行任何操作
  const readSettings = () => {
    // 设置现在通过 computed 自动同步，无需手动读取
    console.log('[useQwen] 设置已自动同步:', qwenSettings.value);
  };

  const sendMessage = async (params: SimplifiedLlmParams, loading: Ref<boolean>): Promise<void> => {
    const { messages, messagesRef, conversation, tools, enableTools, abortController } = params;

    if (!qwenSettings.value) return;

    // console.log('[useQwen] 开始发送消息');
    // console.log('[useQwen] 消息数量:', messages.length);
    // console.log('[useQwen] 工具数量:', tools.length);
    // console.log('[useQwen] 启用工具:', enableTools);

    loading.value = true;

    try {
      // 构建请求体
      const requestBody: {
        model: string;
        messages: Message[];
        temperature?: number;
        max_tokens?: number;
        stream: boolean;
        top_p?: number;
        enable_thinking: boolean;
        tools?: Array<{
          type: string;
          function: {
            name: string;
            description: string;
            parameters: Record<string, unknown>;
          };
        }>;
        parallel_tool_calls?: boolean;
        tool_choice?: string;
      } = {
        model: qwenSettings.value.model,
        messages: [...messages],
        max_tokens: qwenSettings.value.maxTokens,
        stream: true,
        enable_thinking: true,
      };

      // 只添加有效的随机性控制参数（不是NaN的值）
      if (qwenSettings.value.temperature !== undefined && !isNaN(qwenSettings.value.temperature)) {
        requestBody.temperature = qwenSettings.value.temperature;
      }
      if (qwenSettings.value.topP !== undefined && !isNaN(qwenSettings.value.topP)) {
        requestBody.top_p = qwenSettings.value.topP;
      }

      // 判断是否为 Qwen 模型，决定是否传递 parallel_tool_calls 参数
      const isQwenModel = qwenSettings.value.model.toLowerCase().includes('qwen');
      if (isQwenModel) {
        requestBody.parallel_tool_calls = true;
        // console.log(`🔧 [useQwen] 检测到 Qwen 模型，启用 parallel_tool_calls`);
      }

      // 如果有启用的工具，添加工具定义
      if (enableTools && tools.length > 0) {
        // console.log(`🔧 [useQwen] 启用工具数: ${tools.length}`);
        // console.log(`🔧 [useQwen] 工具列表: ${tools.map((t) => t.function.name).join(', ')}`);

        requestBody.tools = tools;
        requestBody.tool_choice = 'auto';
      }

      const response = await fetch(`${qwenSettings.value.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${qwenSettings.value.apiKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('ReadableStream not supported');
      }
      llmStore.waittingLlm = false;

      // 直接操作响应式消息数组，实现流式更新
      messagesRef.value.push({
        role: 'assistant',
        content: '',
        reasoning_content: '',
      });
      const lastMessage = messagesRef.value[messagesRef.value.length - 1];

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      const toolCallsBuffer: Record<string, ToolCall> = {};

      while (true) {
        // 检查是否被中断
        if (abortController?.signal.aborted) {
          void reader.cancel();
          throw new DOMException('Operation was aborted', 'AbortError');
        }

        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices[0]?.delta;

              if (!delta) continue;

              const content = delta.content || '';
              const reasoning_content = delta.reasoning_content || '';

              // 处理思考内容
              if (parsed.choices[0]?.index === 0) {
                (lastMessage as AssistantMessageType).reasoning_content += '';
              }

              if (reasoning_content) {
                (lastMessage as AssistantMessageType).reasoning_content += reasoning_content;
              }

              // 处理普通内容
              if (content) {
                (lastMessage as AssistantMessageType).content += content;
              }

              // 处理工具调用
              if (delta.tool_calls) {
                for (const toolCall of delta.tool_calls) {
                  const toolCallId = toolCall.id;
                  const toolCallIndex = toolCall.index;

                  // 使用 index 作为主要标识符，因为后续片段的 id 可能为空
                  const bufferKey = `tool_${toolCallIndex}`;

                  if (!toolCallsBuffer[bufferKey]) {
                    // 初始化工具调用，使用第一次出现的完整ID
                    toolCallsBuffer[bufferKey] = {
                      id: toolCallId || bufferKey,
                      type: 'function',
                      index: toolCallIndex || 0,
                      function: {
                        name: toolCall.function?.name || '',
                        arguments: toolCall.function?.arguments || '',
                      },
                    };
                  } else {
                    // 如果当前片段有ID而缓存中没有，更新ID
                    if (toolCallId && !toolCallsBuffer[bufferKey].id.startsWith('call_')) {
                      toolCallsBuffer[bufferKey].id = toolCallId;
                    }

                    // 累积工具函数名称（如果分段传输）
                    if (toolCall.function?.name) {
                      toolCallsBuffer[bufferKey].function.name += toolCall.function.name;
                    }
                    // 累积函数参数
                    if (toolCall.function?.arguments) {
                      toolCallsBuffer[bufferKey].function.arguments += toolCall.function.arguments;
                    }
                  }
                }
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }

      // 处理完整的工具调用
      const toolCalls = Object.values(toolCallsBuffer);
      if (toolCalls.length > 0 && enableTools) {
        console.log('完整的工具调用列表:', toolCalls);

        // 验证工具调用的完整性
        toolCalls.forEach((toolCall, index) => {
          console.log(`工具调用 ${index + 1}:`, {
            id: toolCall.id,
            functionName: toolCall.function.name,
            argumentsLength: toolCall.function.arguments.length,
            arguments: toolCall.function.arguments,
          });
        });

        // 将工具调用添加到助手消息中
        (lastMessage as AssistantMessageType).tool_calls = toolCalls;

        // 执行工具调用并添加结果消息
        for (const toolCall of toolCalls) {
          try {
            // 验证工具调用完整性
            if (!toolCall.function.name) {
              throw new Error('工具函数名称为空');
            }

            // 解析工具参数
            let parameters = {};
            if (toolCall.function.arguments) {
              try {
                parameters = JSON.parse(toolCall.function.arguments);
                console.log(`解析工具参数成功 - ${toolCall.function.name}:`, parameters);
              } catch (parseError) {
                console.error(`工具参数解析失败 - ${toolCall.function.name}:`, {
                  rawArguments: toolCall.function.arguments,
                  error: parseError,
                });
                throw new Error(`工具参数格式错误: ${parseError.message}`);
              }
            }

            // 执行工具
            console.log(`开始执行工具: ${toolCall.function.name}`, parameters);
            const toolResult = await executeToolCall(toolCall.function.name, parameters);

            // 添加工具结果消息
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify(toolResult),
              tool_call_id: toolCall.id,
            });

            console.log(`工具执行成功 ${toolCall.function.name}:`, {
              parameters,
              result: toolResult,
            });
          } catch (error) {
            console.error(`工具执行失败 ${toolCall.function.name}:`, {
              error: error.message,
              functionName: toolCall.function.name,
              rawArguments: toolCall.function.arguments,
            });

            // 添加错误结果
            messagesRef.value.push({
              role: 'tool',
              content: JSON.stringify({
                success: false,
                message: $t('src.composeables.useQwen.toolExecutionFailed', {
                  error: error.message,
                }),
                toolName: toolCall.function.name,
              }),
              tool_call_id: toolCall.id,
            });
          }
        }

        // 如果有工具调用，递归调用获取AI对工具结果的响应
        console.log('工具执行完成，开始递归调用获取AI响应...');
        console.log('当前消息数量:', messages.length);
        console.log(
          '最后几条消息:',
          messages.slice(-3).map((m) => ({
            role: m.role,
            content:
              typeof m.content === 'string'
                ? m.content.substring(0, 100)
                : JSON.stringify(m.content).substring(0, 100),
          })),
        );

        // 递归调用以获取最终响应
        console.log('🔄 [useQwen] 递归调用获取最终响应');
        const recursiveParams: SimplifiedLlmParams = {
          ...params,
          messages: messagesRef.value,
        };
        await sendMessage(recursiveParams, loading);
        return;
      }
      console.log('[useQwen] 消息发送完成');
    } catch (error) {
      console.error('[useQwen] Error sending message:', error);
      messagesRef.value.push({
        role: 'assistant',
        content: $t('src.composeables.useQwen.errorOccurred'),
      });
    } finally {
      loading.value = false;

      await updateConversation(
        conversation.id,
        conversation.title,
        JSON.stringify(messagesRef.value),
        conversation.prompt,
      );
    }
  };

  return {
    readSettings,
    qwenSettings,
    sendMessage,
    // 导出prompt服务的方法
    getAvailableRoles: () => PromptService.getAvailableRoles(),
    getRoleSuggestions: (role: ConversationRole) => PromptService.getRoleSuggestions(role),
    // 移除知识库服务方法，统一由 LLM Store 管理
  };
};
