<template>
  <div class="column no-wrap q-pa-md">
    <div class="row no-wrap items-center q-px-sm q-mb-sm gap-sm">
      <span @click="showHistory = !showHistory" class="cursor-pointer">{{
        $t('src.components.ChatConversationList.history')
      }}</span>
      <q-icon
        :name="showHistory ? 'mdi-chevron-down' : 'mdi-chevron-right'"
        size="18px"
        @click="showHistory = !showHistory"
        class="cursor-pointer"
      />
      <q-space />
      <span
        v-if="showHistory && chatConversations?.length > 3"
        class="text-caption cursor-pointer"
        @click="toggleShowLess"
      >
        {{
          showLess
            ? $t('src.components.ChatConversationList.showAll')
            : $t('src.components.ChatConversationList.showLess')
        }}
      </span>
    </div>
    <q-list v-if="showHistory" dense class="scroll-y" style="max-height: 420px">
      <q-item
        v-for="conversation in showConversations"
        :key="conversation.id"
        clickable
        class="q-py-sm hover-item radius-xs"
        style="padding: 2px 8px"
        :class="selectedConversationId === conversation.id ? 'bg-primary text-white' : ''"
        @click="selectConversation(conversation)"
      >
        <q-item-section>
          <q-item-label class="row no-wrap items-center">
            <span class="q-space">{{ conversation.title }}</span>
            <q-btn
              dense
              round
              flat
              size="sm"
              icon="mdi-dots-vertical"
              class="hover-show-item"
              @click.stop
              :color="selectedConversationId === conversation.id ? 'white' : ''"
            >
              <q-menu>
                <q-list dense>
                  <q-item clickable v-close-popup @click="startRename(conversation)">
                    <q-item-section side>
                      <q-icon name="mdi-pencil" />
                    </q-item-section>
                    <q-item-section class="text-no-wrap">{{
                      $t('src.components.ChatConversationList.rename')
                    }}</q-item-section>
                  </q-item>
                  <q-separator class="op-5" />
                  <q-item clickable v-close-popup @click="deleteConversation(conversation.id)">
                    <q-item-section side>
                      <q-icon name="mdi-delete" color="negative" />
                    </q-item-section>
                    <q-item-section class="text-no-wrap">{{
                      $t('src.components.ChatConversationList.delete')
                    }}</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </q-item-label>
        </q-item-section>
      </q-item>
    </q-list>

    <!-- 重命名对话框 -->
    <q-dialog v-model="showRenameDialog">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ $t('src.components.ChatConversationList.renameDialog') }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            v-model="renameTitle"
            :label="$t('src.components.ChatConversationList.conversationTitle')"
            dense
            outlined
            autofocus
            @keyup.enter="confirmRename"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('src.components.ChatConversationList.cancel')" v-close-popup />
          <q-btn
            flat
            :label="$t('src.components.ChatConversationList.confirm')"
            color="primary"
            @click="confirmRename"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useSqlite } from 'src/composeables/useSqlite';
import { useLlmStore } from 'src/stores/llm';
import type { Conversation } from 'src/types/qwen';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

const emit = defineEmits<{
  selectConversation: [conversation: Conversation];
}>();

const $q = useQuasar();
const llmStore = useLlmStore();
const { deleteConversation: deleteChatConversation, updateConversation } = useSqlite();

// 状态
const loading = ref(false);
const selectedConversationId = ref<number | null>(null);

// 使用 store 中的对话数据
const chatConversations = computed(() => llmStore.chatConversations);

// 重命名对话框相关
const showRenameDialog = ref(false);
const renameTitle = ref('');
const renamingConversation = ref<Conversation | null>(null);

/**
 * 重新加载对话列表
 */
const loadChatConversations = async () => {
  loading.value = true;
  try {
    await llmStore.reloadChatConversations();
  } catch (error) {
    console.error('❌ [对话列表] 重新加载失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.ChatConversationList.loadFailed'),
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
};

const showHistory = ref(true);
const showLess = ref(true);
const toggleShowLess = () => {
  showLess.value = !showLess.value;
};
const showConversations = computed(() => {
  if (showLess.value) {
    return chatConversations.value.slice(0, 3);
  }
  return chatConversations.value;
});

/**
 * 创建新的聊天对话
 */
const createNewChatConversation = async (title: string = '新对话') => {
  loading.value = true;
  try {
    // 通过 store 创建新对话，会自动更新列表和选中对话
    const newConversation = await llmStore.createNewChatConversation(title);
    if (newConversation) {
      selectConversation(newConversation);
    }
  } catch (error) {
    console.error('❌ [对话列表] 创建对话失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.ChatConversationList.createFailed'),
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
};

/**
 * 选择对话
 */
const selectConversation = (conversation: Conversation) => {
  selectedConversationId.value = conversation.id;
  emit('selectConversation', conversation);
};

/**
 * 开始重命名对话
 */
const startRename = (conversation: Conversation) => {
  renamingConversation.value = conversation;
  renameTitle.value = conversation.title;
  showRenameDialog.value = true;
};

/**
 * 确认重命名
 */
const confirmRename = async () => {
  if (!renamingConversation.value || !renameTitle.value.trim()) {
    return;
  }

  try {
    await updateConversation(
      renamingConversation.value.id,
      renameTitle.value.trim(),
      renamingConversation.value.messages || '',
      renamingConversation.value.prompt || '',
    );

    // 更新 store 中的数据
    const conversation = llmStore.chatConversations.find(
      (c) => c.id === renamingConversation.value?.id,
    );
    if (conversation) {
      conversation.title = renameTitle.value.trim();
    }

    showRenameDialog.value = false;
    renamingConversation.value = null;
    renameTitle.value = '';

    $q.notify({
      type: 'positive',
      message: $t('src.components.ChatConversationList.renameSuccess'),
      position: 'top',
    });
  } catch (error) {
    console.error('❌ [对话列表] 重命名失败:', error);
    $q.notify({
      type: 'negative',
      message: $t('src.components.ChatConversationList.renameFailed'),
      position: 'top',
    });
  }
};

/**
 * 删除对话
 */
const deleteConversation = async (conversationId: number) => {
  const conversation = chatConversations.value.find((c) => c.id === conversationId);
  if (!conversation) return;

  try {
    await new Promise<void>((resolve, reject) => {
      $q.dialog({
        title: $t('src.components.ChatConversationList.confirmDelete'),
        message: $t('src.components.ChatConversationList.confirmDeleteMessage', {
          title: conversation.title,
        }),
        cancel: true,
        persistent: true,
      })
        .onOk(() => {
          resolve();
        })
        .onCancel(() => {
          reject(new Error($t('src.components.ChatConversationList.cancelDelete')));
        });
    });

    await deleteChatConversation(conversationId);

    // 从 store 中移除
    const index = llmStore.chatConversations.findIndex((c) => c.id === conversationId);
    if (index !== -1) {
      llmStore.chatConversations.splice(index, 1);
    }

    // 如果删除的是当前选中的对话，清除选中状态
    if (selectedConversationId.value === conversationId) {
      selectedConversationId.value = null;
    }

    $q.notify({
      type: 'positive',
      message: $t('src.components.ChatConversationList.deleteSuccess'),
      position: 'top',
    });
  } catch (error) {
    if (error.message !== '用户取消') {
      console.error('❌ [对话列表] 删除失败:', error);
      $q.notify({
        type: 'negative',
        message: $t('src.components.ChatConversationList.deleteFailed'),
        position: 'top',
      });
    }
  }
};

// 对外暴露方法
defineExpose({
  loadChatConversations,
  createNewChatConversation,
  selectedConversationId: computed(() => selectedConversationId.value),
});

// 监听 store 中的加载状态，如果未加载则触发加载
watch(
  () => llmStore.chatConversationsLoaded,
  (loaded) => {
    if (!loaded && !llmStore.isLoadingChatConversations) {
      void llmStore.loadChatConversations();
    }
  },
  { immediate: true },
);
</script>

<style scoped>
.q-item.bg-primary {
  border-radius: 8px;
  margin: 0 8px;
}
</style>
